[Log]
Level = "info"
EnableAccessLog = false

[API]
Addr = "0.0.0.0:8080"
BasePath = ""

[Admin]
Addr = "0.0.0.0:10000"
BasePath = ""

[NewRelic]
Enabled = true

[Spanner]
Project = "aftership-pro"
Instance = "p-connectors-usce1"
Database = "products-p-listings"

[ConnectorsJobAPI]
Url = "http://pltf-cn-jobs.as-in.com/v1"

[JobGroups]
publish_product_listings = "17163d65befc492fb4d4abf976cd2296"
publish_single_product_listings = "e01aa92fbe064dfe9846b9efd00da882"
batch_edit_product_listings_product_attributes = "0fc3ab462726472d8d732612e2e7def3"
create_product_listings_by_products_center_products = "d1a36db5eb604942a9299d1b3d32948d"
create_product_listings_by_category_mapping = "3a9aa4bf0de048c99f0900da01c482c7"
publish_inventories = "314e3f168e894c39b6e14fd0346341c1"
publish_prices = "e0d17af2fb3247d0b58dc652c63a6a75"
batch_auto_link = "cfc0e039dbe14d958213b0225a856da2"
delete_product_listings = "7880ee17090c44a68ee9a826856506be"
recover_product_listings = "f44b5a97fa514f768e370ea15e62c633"
deactivate_product_listings = "27c92dbdf3c946c39059a8eeb531330f"
activate_product_listings = "b6b5d45d31d24d6089e13b5a0a23e4f8"
remove_product_listings="59968a2b65304efb8ee60064fe1390d3"
batch_invoke_product_listing_jobs = "da2db2c54f894dce91bf0d07a9122bba"
batch_publish_prices = "56c364b5d0074e6e8be21d262bf15552"
batch_publish_inventories = "0c161408eb97448ba890c25b1b9b6340"
create_open_collaboration = "dca0bfa538dd41a4b0719cc8b3466e69"
create_combined_listings = "18a4645c3c9443eb84c534c02110ee7b"
bulk_edit_combined_listings = "9b993fc84d62444f8729bdad56e7c6b0"

[ConnectorsAPI]
Url = "http://pltf-connectors.as-in.com/connectors/v2"

[ConnectorsCommerceProxyAPI]
Url = "http://pltf-connectors-commerce-proxy.as-in.com"

[ProductsCenterAPI]
Url = "http://pltf-pd-products-center.as-in.com/api/v1"

[FeedV1API]
Url = "http://prod-feed.as-in.com/feed/v1"

[FeedV2API]
Url = "http://prod-feed.as-in.com/feed/v2"

[GCP]
ProjectID = "aftership-pro"

[PubSubTopics]
SearchableProductModifySalesChannel = "pd-prod-upsert-products-relations-v1"

[BusinessMonitoringExporter]
FlushInterval = 1
FlushCount = 100