[Log]
Level = "info"
EnableAccessLog = true

[API]
Addr = "0.0.0.0:8080"
BasePath = ""

[Admin]
Addr = "0.0.0.0:10000"
BasePath = ""

[NewRelic]
Enabled = false

[Spanner]
Project = "aftership-test"
Instance = "aftership-test-2-asea1"
Database = "products-t-listings"

[DynamicConfigs.Redis]
host = "127.0.0.1:26379"
master_name = "mymaster"
db_number = 5

[ConnectorsJobAPI]
Url = "http://testing-incy-pltf-cn-jobs.as-in.io/v1"

[JobGroups]
publish_product_listings = "30ffbb5fff2e4a02bbdcd522645bbcc0"
publish_single_product_listings = "569caddd98bd432e885c5f98530c4ec4"
batch_edit_product_listings_product_attributes = "f1e754fc81524be09ad10a2b7468118a"
create_product_listings_by_products_center_products = "fa45fadd08414ccfbef9d84816db1c00"
create_product_listings_by_category_mapping = "d0d12ce03c2a478db0301f04c7cd32bc"
publish_inventories = "a06e229abb6b4b9abc91df722068a545"
publish_prices = "2d293bc72ab249358b1b6a3d8f7ccd89"
batch_auto_link = "433d4d5d3fac4bd4bb0b9601ac7a2b71"
delete_product_listings = "79d5a24bf6114180a81f535da3be56bb"
recover_product_listings = "846b95a0aa71483697d7624a47b70d63"
deactivate_product_listings="966f643cc17e4a97a2cc00fc258ca7bb"
activate_product_listings="d023e1530ec6406e9dc328fd0e035e35"
remove_product_listings="ef9e7f2436cf43dcb0b6a45c6d410fc7"
batch_invoke_product_listing_jobs = "da07908e735d4214b2634e8cdfa09949"
batch_publish_prices = "4695785cb363400d8821bda41e38e442"
batch_publish_inventories = "0fa79d22cf11445ba08cb298eb16bc24"
create_open_collaboration = "913a00a99bf442c7b46bc4865a692b26"
create_combined_listings = "9c8311088de448ecb1f22723ba616fcd"
bulk_edit_combined_listings = "e258dcac34ae4ba9b4406d97fa1b197b"

[ConnectorsAPI]
Url = "http://testing-incy-pltf-connectors.as-in.io/connectors/v2"

[ConnectorsCommerceProxyAPI]
Url = "http://testing-incy-pltf-connectors-commerce-proxy.as-in.io"

[ProductsCenterAPI]
Url = "http://testing-incy-pltf-pd-products-center.as-in.io/api/v1"

[FeedV1API]
Url = "http://testing-incy-prod-feed.as-in.io/feed/v1"

[FeedV2API]
Url = "http://testing-incy-prod-feed.as-in.io/feed/v2"

[GCP]
ProjectID = "aftership-test"

[PubSubTopics]
SearchableProductModifySalesChannel = "pd-test-upsert-products-relations-v1"

[BusinessMonitoringExporter]
FlushInterval = 1
FlushCount = 100