[Log]
Level = "info"
EnableAccessLog = true

[API]
Addr = "0.0.0.0:8080"
BasePath = ""

[Admin]
Addr = "0.0.0.0:10000"
BasePath = ""

[NewRelic]
Enabled = true

[Spanner]
Project = "aftership-pro"
Instance = "p-connectors-usce1"
Database = "products-p-listings"

[ConnectorsJobAPI]
Url = "http://pltf-cn-jobs.as-in.com/v1"

[JobGroups]
publish_product_listings = "f11825fbbbd244b2a41980f1cfa9251e"
publish_single_product_listings = "b7165d2865ec4153b92a38ba08d85cc1"
batch_edit_product_listings_product_attributes = "1072e0e774b34c2fa76a06928e98f673"
create_product_listings_by_products_center_products = "a87bca21af294cbc89a017af2122ef15"
create_product_listings_by_category_mapping = "71328b564a6e43caa79b4a8f6c1d04ff"
publish_inventories = "95cbe1d2ef0d46bbacda6a935c522e7b"
publish_prices = "5a5f3190ab674b4396db942bfd46c1fd"
batch_auto_link = "a1e93b0a36cd481c8c838940477d0b1f"
delete_product_listings = "383e39cf458447f3b4bdd11368db3700"
recover_product_listings = "c6733f942f674fd8a6eb2e64b7422b9a"
deactivate_product_listings = "61dc057abee2417a851ab0c5c9a5126d"
activate_product_listings = "82835810b307421681d69b81a5faa757"
remove_product_listings="f2ba8d6f4d8a47e5b8130e3718cd4ecd"
batch_invoke_product_listing_jobs = "21a5e11a865d4d07a42df74a46a7bce6"
batch_publish_prices = "67c61c0601c643439a10ce165b56f824"
batch_publish_inventories = "a90741e45dc047cc9587cd32df4e4a59"
create_open_collaboration = "260659cf15fd4abc9155e9d9af69b999"
create_combined_listings = "e8c42bcefd7b4cc286c6a1e8c946e850"
bulk_edit_combined_listings = "130c94e5eb4e4c86880cfcd343611336"

[ConnectorsAPI]
Url = "http://pltf-connectors.as-in.com/connectors/v2"

[ConnectorsCommerceProxyAPI]
Url = "http://pltf-connectors-commerce-proxy.as-in.com"

[ProductsCenterAPI]
Url = "http://staging-pltf-pd-products-center.as-in.com/api/v1"

[FeedV1API]
Url = "http://staging-prod-feed.as-in.com/feed/v1"

[FeedV2API]
Url = "http://staging-prod-feed.as-in.com/feed/v2"

[GCP]
ProjectID = "aftership-pro"

[PubSubTopics]
SearchableProductModifySalesChannel = "pd-prod-upsert-products-relations-v1"

[BusinessMonitoringExporter]
FlushInterval = 1
FlushCount = 100