package main

import (
	"os"
	"os/signal"
	"syscall"

	"go.uber.org/automaxprocs/maxprocs"
	"go.uber.org/zap"

	"github.com/AfterShip/pltf-pd-product-listings/internal/config"
	"github.com/AfterShip/pltf-pd-product-listings/internal/logger"
	"github.com/AfterShip/pltf-pd-product-listings/internal/web"
)

func init() {
	_, _ = maxprocs.Set(maxprocs.Logger(func(msg string, opts ...interface{}) {
		logger.Get().Info(msg, zap.Any("opts", opts))
	}))
}

func registerSignal(shutdown chan struct{}) {
	c := make(chan os.Signal, 1)
	signal.Notify(c, []os.Signal{syscall.SIGHUP, syscall.SIGINT, syscall.SIGTERM, syscall.SIGUSR1}...)
	go func() {
		for sig := range c {
			if handleSignals(sig) {
				close(shutdown)
				return
			}
		}
	}()
}

func handleSignals(sig os.Signal) (exitNow bool) {
	switch sig {
	case syscall.SIGHUP, syscall.SIGINT, syscall.SIGTERM:
		return true
	case syscall.SIGUSR1:
		return false
	}
	return false
}

func main() {
	shutdownCh := make(chan struct{})
	registerSignal(shutdownCh)

	conf, err := config.Load()
	if err != nil {
		panic("Failed to load the config, err: " + err.Error())
	}

	server, err := web.NewServer(conf)
	if err != nil {
		panic("Failed to create the server, err: " + err.Error())
	}

	if err := server.Init(); err != nil {
		panic("Failed to init the server, err: " + err.Error())
	}

	if err := server.Run(); err != nil {
		panic("Failed to run the server, err: " + err.Error())
	}

	// wait for the term signal
	<-shutdownCh
	server.Shutdown()
}
