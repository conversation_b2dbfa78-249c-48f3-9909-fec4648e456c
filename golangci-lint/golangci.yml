version: "2"
run:
  concurrency: 4
  tests: false
  timeout: 1h
output:
  path-prefix: ""
linters:
  enable:
    - errorlint
    - funlen
    - gocritic
    - gosec
    - misspell
    - nestif
    - promlinter
    - revive
    - rowserrcheck
    - testpackage
  disable:
    - contextcheck
    - errcheck
    - errchk<PERSON>son
    - unused
  settings:
    errcheck:
      check-type-assertions: true
      check-blank: false
    errorlint:
      errorf: true
      asserts: true
      comparison: true
    funlen:
      lines: 80
      statements: 40
    gocognit:
      min-complexity: 100
    gocritic:
      enabled-checks:
        - rangeExprCopy
        - rangeValCopy
      disabled-checks:
        - commentFormatting
        - ifElseChain
      settings:
        captLocal:
          paramsOnly: true
        rangeExprCopy:
          sizeThreshold: 10240
          skipTestFuncs: true
        rangeValCopy:
          sizeThreshold: 10240
          skipTestFuncs: true
    gosec:
      excludes:
        - G103
        - G104
        - G106
        - G112
        - G114
        - G301
        - G302
        - G304
        - G306
        - G307
        - G402
        - G501
        - G502
        - G503
        - G504
        - G505
        - G601
      config:
        G101:
          custom:
            - ^asat_[a-fA-F0-9]{32}$
            - ^([a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12})$
            - ^[a-zA-Z0-9+=_.-]{20}$
          ignore_case: true
          pattern: (?i)As-Api-Key|As_Api_Key|Am-Api-Key|Am_Api_Key|Api-Key|Api_Key|aftership-api-key|mock-aftership-api-key|aftership_api_key|mock-am-api-key|automizely-api-key|AWS_SECRET_ACCESS_KEY|AWS-SECRET-ACCESS-KEY
        global:
          nosec: true
    govet:
      enable:
        - appends
        - assign
        - atomic
        - bools
        - cgocall
        - composites
        - copylocks
        - deepequalerrors
        - defers
        - errorsas
        - findcall
        - framepointer
        - httpresponse
        - ifaceassert
        - loopclosure
        - lostcancel
        - nilfunc
        - nilness
        - reflectvaluecompare
        - shift
        - sigchanyzer
        - slog
        - sortslice
        - stringintconv
        - testinggoroutine
        - unreachable
        - unsafeptr
        - unusedresult
    misspell:
      locale: US
      ignore-rules:
        - automizely
    nakedret:
      max-func-lines: 50
    nestif:
      min-complexity: 50
    nilnil:
      checked-types:
        - ptr
        - func
        - iface
        - map
        - chan
    nolintlint:
      require-explanation: false
      require-specific: false
      allow-unused: true
    prealloc:
      simple: false
      range-loops: true
      for-loops: false
    predeclared:
      qualified-name: false
    promlinter:
      strict: false
    revive:
      severity: warning
      rules:
        - name: indent-error-flow
          severity: warning
    rowserrcheck:
      packages:
        - github.com/jmoiron/sqlx
    staticcheck:
      checks: [ "all", "-ST1003", "-ST1000", "-ST1016", "-ST1020", "-ST1021", "-ST1022" ]
      initialisms:
        - ACL
        - API
        - ASCII
        - CPU
        - CSS
        - DNS
        - EOF
        - GUID
        - HTML
        - HTTP
        - HTTPS
        - ID
        - IP
        - JSON
        - QPS
        - RAM
        - RPC
        - SLA
        - SMTP
        - SQL
        - SSH
        - TCP
        - TLS
        - TTL
        - UDP
        - UI
        - GID
        - UID
        - UUID
        - URI
        - URL
        - UTF8
        - VM
        - XML
        - XMPP
        - XSRF
        - XSS
    testpackage:
      skip-regexp: (export|internal_api)_test\.go
  exclusions:
    generated: lax
    presets:
      - comments
      - common-false-positives
      - legacy
      - std-error-handling
    rules:
      - linters:
          - dupl
          - errcheck
          - gocyclo
          - gosec
        path: _test\.go
      - linters:
          - gosec
        path: internal_api/hmac/
        text: weak cryptographic primitive
      - linters:
          - staticcheck
        text: 'SA9003:'
      - linters:
          - lll
        source: '^//go:generate '
    paths:
      - mock_.*\.go
      - .*_test\.go
      - internal/server/services/common_service/data_clean
      - third_party$
      - builtin$
      - examples$
issues:
  max-issues-per-linter: 0
  max-same-issues: 0
  new-from-rev: ""
  new: true
  fix: true
severity:
  default: error
  rules:
    - linters:
        - dupl
      severity: info
formatters:
  settings:
    gofmt:
      simplify: true
    goimports:
      local-prefixes:
        - github.com/AfterShip
  exclusions:
    generated: lax
    paths:
      - third_party$
      - builtin$
      - examples$