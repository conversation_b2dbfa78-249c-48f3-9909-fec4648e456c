FROM asia-east1-docker.pkg.dev/aftership-admin/ci-artifacts/golang-onbuild:golang-1.24.1 AS builder
WORKDIR ${WORK_DIR}
RUN make

FROM asia-east1-docker.pkg.dev/aftership-admin/ci-artifacts/other:ubuntu-22.04-v0.0.4
ARG APP_NAME
ENV WORK_DIR /deploy/${APP_NAME}
WORKDIR ${WORK_DIR}

COPY --from=builder /deploy/${APP_NAME}/cmd/apiserver/conf conf
COPY --from=builder /deploy/${APP_NAME}/cmd/apiserver/pltf-pd-product-listings /usr/local/bin/

ENTRYPOINT ["pltf-pd-product-listings"]