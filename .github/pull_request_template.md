## 目的或是相关联的 Jira tickets
如果有 Jira ticket, 直接在这里写上 Jira ticket 的链接即可

## 相关联的技术方案文档或是你的代码改动方式
如果有技术方案文档, 直接在这里贴上文档的链接即可

## 代码提交同学的自查事项(Take Easy, 5分钟时间自查一下,需要 ✅)
- [ ] 安全风险 - 数据库密码、API Key 等敏感信息严禁随代码提交
- [ ] 安全风险 - 不能在 error message 中拼接敏感信息
- [ ] 安全风险 - 不能在日志记录泄露敏感信息
- [ ] 安全风险 - 不使用字符串拼接参数生成 SQL 的方式
- [ ] 安全风险 - 操作资源的时候需要校验当前用户的 organization id 是否有权限
- [ ] 安全风险 - 校验用户权限需要使用 header 里边的 organization id, 禁止直接使用 FE 带过来的 organization id
- [ ] 上线不会产生大量日志的输出，同时避免 Debug 日志发布到线上，避免使用 zap.Any()
- [ ] 发送 邮件、SMS 的场景，需要 Dry run 确保发送对象、频率这两方面没有异常，不可以直接上线
- [ ] SQL检查，明确所有查询都使用正确的索引
- [ ] channel 对象需要注意读数据或是 close，timer、ticker 对象需要注意 stop，避免内存泄露