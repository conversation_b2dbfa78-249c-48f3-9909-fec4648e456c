name: CI Actions
on:
  push:
    branches:
      - master
      - testing
      - feat*
      - staging*
      - canary
  pull_request:
    branches:
      - master
      - testing
      - feat*
      - staging*
      - canary
  workflow_dispatch:

env:
  go-version: 1.21.x

jobs:
  Lint:
    runs-on: ubuntu-latest

    steps:
      - name: Configure git for private modules
        env:
          TOKEN: ${{ secrets.RESTRICTED_REPO_TOKEN}}
        run: git config --global url."https://${TOKEN}:<EMAIL>/".insteadOf "https://github.com/"

      - uses: actions/checkout@v3
        with:
          fetch-depth: '0'

      - name: Install Go
        uses: actions/setup-go@v4
        with:
          go-version: ${{env.go-version}}

      - name: Fetch base branch
        run: git fetch origin ${{ github.base_ref }}:${{ github.base_ref }}

      - name: golangci-lint
        uses: golangci/golangci-lint-action@v7
        with:
          args: --config=golangci-lint/golangci.yml
          only-new-issues: true
          github-token: ${{ secrets.GITHUB_TOKEN}}

      - name: Download PR Diff
        if: ${{ github.event_name == 'pull_request' }}
        id: pr_diff_downloader
        uses: mingyuans/github-diff@v1.0.0
        with:
          logger-level: info
          token: ${{ secrets.GITHUB_TOKEN }}
          file-name: 'pr.diff'

      - name: NilAway on Pull Request
        if: ${{ github.event_name == 'pull_request' }}
        run: |
          echo "NilAway scanning..."
         
          SCAN_DIRS="internal/domains/... internal/web/..."
          GOLANGCI_BIN="./golangci-lint/golangci-lint-custom_linux_amd64"
          GOLANGCI_CONFIG="golangci-lint/golangci_nilaway.yaml"
          SCAN_FLAGS="--new-from-patch=pr.diff -v"

          chmod +x $GOLANGCI_BIN

          # 执行扫描
          for dir in $SCAN_DIRS; do
            echo "Scanning $dir"
            $GOLANGCI_BIN run -c $GOLANGCI_CONFIG ./$dir $SCAN_FLAGS
          done

  Test:
    runs-on: ubuntu-latest

    steps:
      - name: Configure git for private modules
        env:
          TOKEN: ${{ secrets.RESTRICTED_REPO_TOKEN }}
        run: git config --global url."https://${TOKEN}:<EMAIL>/".insteadOf "https://github.com/"

      - name: Install Go
        uses: actions/setup-go@v4
        with:
          go-version: ${{env.go-version}}

      #  java is required to start the PubSub simulator
      - uses: actions/setup-java@v2
        with:
          distribution: 'adopt'
          java-version: '8'

      - name: Checkout Code Base
        uses: actions/checkout@v2

      - name: Restore Go Module Cache
        uses: actions/cache@v3
        with:
          path: |
            ~/.cache/go-build
            ~/go/pkg/mod
            ~/go/internal_api/mod
          key: ${{ runner.os }}-go-${{ hashFiles('**/go.sum') }}
          restore-keys: |
            ${{ runner.os }}-go-

      - name: Test
        run: make test

      - name: SonarQube Scan On PR
        if: ${{ github.event_name == 'pull_request' }}
        uses: sonarsource/sonarqube-scan-action@v2
        with:
          projectBaseDir: .
          args: >
            -Dsonar.go.coverage.reportPaths=coverage.out
            -Dsonar.exclusions=**/*_test.go,**/*.gen.go,**/*.py,**/*mock.go
            -Dsonar.projectKey=${{ github.event.repository.name }}
            -Dsonar.pullrequest.provider=github
            -Dsonar.pullrequest.github.repository=${{ github.repository }}
            -Dsonar.pullrequest.key=${{ github.event.number }}
            -Dsonar.pullrequest.branch=PR
            -Dsonar.pullrequest.base=${{ github.base_ref }}
            -Dsonar.qualitygate.wait=false
        env:
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
          SONAR_HOST_URL: ${{ secrets.SONAR_HOST_URL }}

      - name: Get branch name (merge)
        if: ${{ github.event_name != 'pull_request' }}
        shell: bash
        run: echo "BRANCH_NAME=$(echo ${GITHUB_REF#refs/heads/})" >> $GITHUB_ENV

      - name: SonarQube Scan On Push
        if: ${{ github.event_name != 'pull_request' }}
        uses: sonarsource/sonarqube-scan-action@v2
        with:
          projectBaseDir: .
          args: >
            -Dsonar.go.coverage.reportPaths=coverage.out
            -Dsonar.exclusions=**/*_test.go,**/*.gen.go,**/*.py
            -Dsonar.projectKey=${{ github.event.repository.name }}
            -Dsonar.branch.name=${{ env.BRANCH_NAME }}
            -Dsonar.projectVersion="master"
            -Dsonar.qualitygate.wait=false
        env:
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
          SONAR_HOST_URL: ${{ secrets.SONAR_HOST_URL }}

      - name: Coverage Threshold
        if: ${{ github.event_name == 'pull_request' }}
        id: coverage_threshold
        uses: mingyuans/gocov-threshold@latest
        with:
          path: .
          coverprofile: coverage.out
          module: github.com/AfterShip/pltf-pd-product-listings
          token: ${{ secrets.GITHUB_TOKEN }}
          conf: gocov-conf.yaml
          print-uncovered-lines: true
          threshold: 60
          logger-level: 'debug'