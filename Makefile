PROGRAM=pltf-pd-product-listings
PKG_FILES=`go list ./... | sed -e 's=github.com/AfterShip/pltf-pd-product-listings/=./='`

CCCOLOR="\033[37;1m"
MAKECOLOR="\033[32;1m"
ENDCOLOR="\033[0m"

all: $(PROGRAM)

.PHONY: all

run: $(PROGRAM)
	@SPANNER_EMULATOR_HOST=localhost:9010 
	@PUBSUB_EMULATOR_HOST=127.0.0.1:8085 
	@PUBSUB_PROJECT_ID=test-project
	./cmd/apiserver/pltf-pd-product-listings

$(PROGRAM):
	@go mod tidy
	@bash scripts/build.sh
	@echo ""
	@printf $(MAKECOLOR)"Hint: It's a good idea to run 'make test' ;)"$(ENDCOLOR)
	@echo ""

setup:
	@cd scripts && bash setup.sh && cd ..

teardown:
	@cd scripts && bash teardown.sh && cd ..

test:
	@cd scripts && bash setup.sh && cd ..
	@bash scripts/test.sh
	@cd scripts && bash teardown.sh && cd ..

data_race:
	@cd scripts && bash setup.sh && cd ..
	@SPANNER_EMULATOR_HOST=localhost:9010 bash scripts/data_race.sh
	@cd scripts && bash teardown.sh && cd ..

lint:
	@printf $(CCCOLOR)"GolangCI Lint...\n"$(ENDCOLOR)
	@go mod tidy
	@golangci-lint run --config golangci-lint/.golangci.yml

nilaway:
	@# 这个是 MacBook 本地执行的命令；如果是在 linux 环境需要执行 xxx
	@printf $(CCCOLOR)"NilAway scanning...\n"$(ENDCOLOR)
	@#export GOLANGCI_LINT_CACHE=$(shell pwd)/.golangci-lint/nilaway_cache
	@SCAN_DIRS="internal/domains/... internal/web/..."; \
	GOLANGCI_BIN="./golangci-lint/golangci-lint-custom"; \
	GOLANGCI_CONFIG="golangci-lint/golangci_nilaway.yaml"; \
	SCAN_FLAGS="--issues-exit-code=0 --fix --new-from-merge-base=origin/testing -v"; \
	echo "$(GOLANGCI_LINT_CACHE)"; \
	for dir in $$SCAN_DIRS; do \
		printf $(CCCOLOR)"Scanning $$dir\n"$(ENDCOLOR); \
		$$GOLANGCI_BIN run -c $$GOLANGCI_CONFIG ./$$dir $$SCAN_FLAGS; \
	done