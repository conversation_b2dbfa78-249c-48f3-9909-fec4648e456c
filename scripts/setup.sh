#!/usr/bin/env bash

if [[ -n $(docker ps -q -f "name=pltf_pd_product_listings") ]];then
	cd docker && docker compose -p pltf_pd_product_listings down -v --remove-orphans && cd ..
fi

cd docker && docker compose -p pltf_pd_product_listings up -d  --remove-orphans && cd ..

ROOT_DIR=$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd -P)

PROJECT=test-project
INSTANCE=test-instance
DATABASE=test-db

SCHEMA_DIR="${ROOT_DIR}/scripts/sql" # Change to your sql scripts path
SCHEMA_FILES=(
  schemas.sql
)
export SPANNER_EMULATOR_HOST="${SPANNER_EMULATOR_HOST:-127.0.0.1:9010}"

ENSURE_ARGS=("${DATABASE}")
for file in "${SCHEMA_FILES[@]}"
do
ENSURE_ARGS+=("${SCHEMA_DIR}/${file}")
done

if ! which go-gcloud-test-helper > /dev/null
then
env GOPRIVATE=github.com/AfterShip/* \
        go install github.com/AfterShip/go-gcloud-test-helper@latest #下载
fi

until nc -z 127.0.0.1 9010; do
  echo "Spanner is not ready"
  sleep 1
done

go-gcloud-test-helper spanner ensure-db --project ${PROJECT} --instance ${INSTANCE} "${ENSURE_ARGS[@]}"

until nc -z 127.0.0.1 9200; do
  echo "ES is not ready"
  sleep 1
done

until nc -z 127.0.0.1 26379; do
  echo "redis is not ready"
  sleep 1
done

sleep 2 # Wait 2s for make sure ES to be ready
