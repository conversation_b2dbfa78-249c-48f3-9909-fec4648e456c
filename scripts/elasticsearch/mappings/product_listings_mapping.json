{"settings": {"index": {"refresh_interval": "100ms", "analysis": {"normalizer": {"lowercase": {"filter": ["lowercase"], "type": "custom"}}, "analyzer": {"folding": {"filter": ["lowercase", "asciifolding"], "tokenizer": "whitespace"}, "ngram": {"filter": ["lowercase", "asciifolding"], "tokenizer": "ngram_tokenizer"}}, "tokenizer": {"ngram_tokenizer": {"token_chars": ["letter", "digit"], "min_gram": "2", "type": "ngram", "max_gram": "3"}}}}}, "mappings": {"dynamic": "false", "dynamic_templates": [], "properties": {"id": {"index": false, "type": "keyword"}, "organization_id": {"type": "keyword"}, "sales_channel_store_key": {"type": "keyword"}, "sales_channel_platform": {"type": "keyword"}, "sales_channel_product_id": {"type": "keyword"}, "sales_channel_connector_product_id": {"type": "keyword"}, "sales_channel_product_state": {"type": "keyword"}, "matched_products_center_product_id": {"type": "keyword"}, "state": {"type": "keyword"}, "link_status": {"type": "keyword"}, "sync_status": {"type": "keyword"}, "ready_status": {"type": "keyword"}, "publish_state": {"fields": {"is_null": {"type": "keyword"}}, "type": "keyword"}, "audit_state": {"type": "keyword"}, "deleted": {"type": "boolean"}, "product_title": {"analyzer": "ngram", "type": "text"}, "product_sales_channel_category_ids": {"type": "keyword"}, "product_tags": {"type": "keyword"}, "created_at": {"type": "date"}, "updated_at": {"type": "date"}, "variants_allow_backorder": {"type": "boolean"}, "variants_inventory_quantity": {"type": "double"}, "options": {"type": "nested", "properties": {"name": {"type": "keyword"}, "value_details_sales_channel_id": {"type": "keyword"}, "value_details_state": {"type": "keyword"}, "value_details_sync_status": {"type": "keyword"}}}, "product_number": {"type": "text", "analyzer": "ngram"}, "product_variants": {"type": "nested", "properties": {"id": {"type": "keyword"}, "sku": {"analyzer": "ngram", "type": "text"}, "title": {"analyzer": "ngram", "type": "text"}, "inventory_quantity": {"type": "double"}, "allow_backorder": {"type": "boolean"}, "price_currency": {"type": "keyword"}, "price_amount": {"type": "float"}, "compare_at_price_currency": {"type": "keyword"}, "compare_at_price_amount": {"type": "float"}}}}}}