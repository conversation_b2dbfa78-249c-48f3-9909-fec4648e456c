#!/usr/bin/env bash

export SPANNER_EMULATOR_HOST=localhost:9010
export PUBSUB_EMULATOR_HOST=127.0.0.1:8085
export PUBSUB_PROJECT_ID=test-project
export AM_API_KEY=xxxx
# 现有单测代码里边，依赖如下环境变量定位到 config-local.toml 的配置文件
export NODE_ENV=""
export APP_ENV="local"

set -a
# 排除 third_party 目录的单测用例；该目录属于集成测试，不是我们单测的范围。
go test $(go list ./... | grep -v -E "/third_party|/integration") -covermode=count -coverprofile=coverage_all.out &&
# exclude the mock files
cat coverage_all.out | grep -v 'mock.*' > coverage.out &&
go tool cover -func=coverage.out