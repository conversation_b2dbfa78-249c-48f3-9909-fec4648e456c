version: '2'

services:
  spanner:
    image: gcr.io/cloud-spanner-emulator/emulator:latest
    ports:
      - "9010:9010"
      - "9020:9020"
    # 如果使用到 NULL FILTER 的索引，默认 spanner emulator 会报错要求检查并在 SQL 中指定；这里直接关闭.
    command: [ "./gateway_main","--hostname", "0.0.0.0","--disable_query_null_filtered_index_check=true" ]
  elasticsearch:
    image: elasticsearch:7.16.3
    mem_limit: 512MB
    environment:
      - 'discovery.type=single-node'
      - 'ES_JAVA_OPTS=-Xms128m -Xmx128m'
    ports:
      - '9200:9200'
  redis:
    image: redis:4
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes
  redis-sentinel:
    image: bitnami/redis-sentinel:latest
    environment:
      - REDIS_MASTER_HOST=127.0.0.1
    ports:
      - "26379:26379"
    depends_on:
      - redis
