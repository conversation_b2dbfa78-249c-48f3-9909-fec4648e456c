CREATE TABLE products (
    id STRING(32) NOT NULL,
    name STRING(256) NOT NULL,
    description STRING(1024) NOT NULL,
) PRIMARY KEY (id);

CREATE TABLE product_listings
(
    product_listing_id STRING(32) NOT NULL,
    organization_id STRING(32) NOT NULL,
    sales_channel_platform STRING(256),
    sales_channel_store_key STRING(256),
    sales_channel_country_region STRING(256),
    sales_channel_product_id STRING(64),
    sales_channel_connector_product_id STRING(32),
    sales_channel_state STRING(64),
    sales_channel_metrics_created_at TIMESTAMP,
    sales_channel_metrics_updated_at TIMESTAMP,
    products_center_product_id STRING(32),
    products_center_connector_product_id STRING(32),
    products_center_state STRING(64),
    products_center_source_store_key STRING(256),
    products_center_source_platform STRING(256),
    products_center_source_product_id STRING(64),
    state STRING(64),
    link_status STRING(64),
    sync_status STRING(64),
    ready_status STRING(64),
    ready_failed_reasons STRING(MAX),
    ready_last_failed_at TIMESTAMP,
    audit_state STRING(64),
    audit_failed_reasons STRING(MAX),
    audit_last_failed_at TIMESTAMP,
    publish_last_reference_id STRING(32),
    publish_state STRING(64),
    publish_error_code STRING(64),
    publish_error_msg STRING(MAX),
    publish_last_failed_at TIMESTAMP,
    product STRING(MAX),
    settings STRING(MAX),
    version INT64,
    pending_deleted_at TIMESTAMP OPTIONS (allow_commit_timestamp = true),
    deleted_at TIMESTAMP OPTIONS (allow_commit_timestamp = true),
    created_at TIMESTAMP OPTIONS (allow_commit_timestamp = true),
    updated_at TIMESTAMP OPTIONS (allow_commit_timestamp = true),
    feed_category_template_id STRING(32),
) PRIMARY KEY(product_listing_id);


-- #### this line only supports for spanner emulator, not for spanner production ####
CREATE UNIQUE INDEX product_listings_by_organization_id_a_sales_channel_platform_a_sales_channel_store_key_a_sales_channel_product_id_a_u ON product_listings(organization_id ASC, sales_channel_platform ASC, sales_channel_store_key ASC, sales_channel_product_id ASC);
-- CREATE UNIQUE NULL_FILTERED INDEX product_listings_by_organization_id_a_sales_channel_platform_a_sales_channel_store_key_a_sales_channel_product_id_a_u ON product_listings(organization_id ASC, sales_channel_platform ASC, sales_channel_store_key ASC, sales_channel_product_id ASC);
CREATE INDEX product_listings_by_organization_id_a_products_center_product_id_a_sales_channel_platform_a_sales_channel_store_key_a_u ON product_listings(organization_id ASC, products_center_product_id ASC, sales_channel_platform ASC, sales_channel_store_key ASC);

CREATE TABLE product_listing_relations
(
    product_listing_relation_id STRING(32) NOT NULL,
    product_listing_id STRING(32) NOT NULL,
    product_listing_variant_id STRING(32) NOT NULL,
    organization_id STRING(32) NOT NULL,
    sales_channel_platform STRING(256),
    sales_channel_store_key STRING(256),
    sales_channel_variant_id STRING(256),
    sales_channel_connector_product_id STRING(256),
    sales_channel_product_id STRING(64),
    sales_channel_sku STRING(64),
    products_center_variant_id STRING(32),
    products_center_product_id STRING(32),
    products_center_connector_product_id STRING(32),
    products_center_source_store_key STRING(256),
    products_center_source_platform STRING(256),
    products_center_source_variant_id STRING(64),
    products_center_source_product_id STRING(64),
    products_center_source_sku STRING(256),
    sync_status STRING(64),
    link_status STRING(64),
    allow_sync STRING(64),
    last_synced_at TIMESTAMP OPTIONS (allow_commit_timestamp = true),
    last_linked_at TIMESTAMP OPTIONS (allow_commit_timestamp = true),
    deleted_at TIMESTAMP OPTIONS (allow_commit_timestamp = true),
    created_at TIMESTAMP OPTIONS (allow_commit_timestamp = true),
    updated_at TIMESTAMP OPTIONS (allow_commit_timestamp = true),
) PRIMARY KEY(product_listing_relation_id);

CREATE UNIQUE INDEX product_listing_relations_by_product_listing_id_a_product_listing_variant_id_a_deleted_at_a_u ON product_listing_relations(product_listing_id ASC, product_listing_variant_id ASC, deleted_at ASC);
CREATE INDEX product_listing_relations_by_organization_id_a_platform_a_store_key_a_sales_channel_product_id_a_sales_channel_variant_id_a ON product_listing_relations(organization_id ASC, sales_channel_platform ASC, sales_channel_store_key ASC, sales_channel_product_id ASC, sales_channel_variant_id ASC);
CREATE INDEX product_listing_relations_by_organization_id_a_pc_product_id_a_pc_variant_id_a_platform_a_store_key_a ON product_listing_relations(organization_id ASC, products_center_product_id ASC, products_center_variant_id ASC, sales_channel_platform ASC, sales_channel_store_key ASC);
CREATE INDEX product_listing_relations_by_organization_id_a_products_center_connector_product_id_a ON product_listing_relations(organization_id ASC, products_center_connector_product_id ASC);

CREATE TABLE product_listing_audit_versions
(
    product_listing_audit_version_id STRING(32) NOT NULL,
    product_listing_id STRING(32) NOT NULL,
    audit_data STRING(MAX),
    created_at TIMESTAMP OPTIONS (allow_commit_timestamp = true),
) PRIMARY KEY(product_listing_audit_version_id);

CREATE INDEX product_listing_audit_versions_by_product_listing_id_a_created_at_d ON product_listing_audit_versions(product_listing_id, created_at);

CREATE TABLE settings (
      setting_id STRING(32) NOT NULL,
      organization_id STRING(32) NOT NULL,
      source_app_platform STRING(256) NOT NULL,
      source_app_key STRING(256) NOT NULL,
      sales_channel_platform STRING(256) NOT NULL,
      sales_channel_store_key STRING(256) NOT NULL,
      default_brand STRING(MAX),
      default_compliance STRING(MAX),
      price_sync STRING(MAX),
      inventory_sync STRING(MAX),
      product_sync STRING(MAX),
      auto_link STRING(MAX),
      dimensions_mapping STRING(MAX),
      markets_currency_mapping STRING(MAX),
      product_ai STRING(MAX),
      created_at TIMESTAMP OPTIONS (allow_commit_timestamp = true),
      updated_at TIMESTAMP OPTIONS (allow_commit_timestamp = true),
) PRIMARY KEY(setting_id);

CREATE UNIQUE INDEX settings_by_organization_id_a_source_app_platform_a_source_app_key_a_sales_channel_platform_a_sales_channel_store_key_a_u ON settings(organization_id, source_app_platform,source_app_key,sales_channel_platform, sales_channel_store_key);

CREATE TABLE organization_settings (
      organization_setting_id STRING(32) NOT NULL,
      organization_id STRING(32) NOT NULL,
      currency_convertors STRING(MAX),
      created_at TIMESTAMP OPTIONS (allow_commit_timestamp = true),
      updated_at TIMESTAMP OPTIONS (allow_commit_timestamp = true),
) PRIMARY KEY(organization_setting_id);

CREATE UNIQUE INDEX organization_settings_by_organization_id_a_u ON organization_settings(organization_id);