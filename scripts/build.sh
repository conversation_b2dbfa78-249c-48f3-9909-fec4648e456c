#!/usr/bin/env bash

SERVICE_NAME="pltf-pd-product-listings"
VERSION=`grep -i "^VERSION" Changelog | head -1 | cut -d " " -f2`
BUILD_DATE=`date '+%Y-%m-%dT%H:%M:%S%z'`
GIT_REVISION=`git rev-parse --short HEAD`
GIT_BRANCH=`git name-rev --name-only HEAD`
BUILD_NUMBER="UNKNOWN"
if [[ -f "build.json" ]];then
BUILD_NUMBER="`cat build.json`"
fi

GOPKG="github.com/AfterShip/gopkg"

export GOPRIVATE=github.com/AfterShip/*
go mod tidy

go build -v -ldflags \
    "-X $GOPKG/whoami.version=$VERSION \
    -X $GOPKG/whoami.name=$SERVICE_NAME \
    -X $GOPKG/whoami.buildNumber=$BUILD_NUMBER \
    -X $GOPKG/whoami.commitHash=$GIT_REVISION \
    -X $GOPKG/whoami.commitBranch=$GIT_BRANCH \
    -X $GOPKG/whoami.buildAt=$BUILD_DATE" \
	-o ./cmd/apiserver/$SERVICE_NAME ./cmd/apiserver
