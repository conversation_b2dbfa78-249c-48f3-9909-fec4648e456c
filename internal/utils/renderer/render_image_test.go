package renderer

import (
	"context"
	"errors"
	"strings"
	"testing"

	"github.com/stretchr/testify/require"
	"golang.org/x/net/html"
)

func Test_ReplaceImageSrc(t *testing.T) {
	tests := []struct {
		name        string
		input       string
		imageOutput string
		want        string
	}{
		{
			name:        "single image",
			input:       `<img src="https://example.com/image1.jpg"/>`,
			imageOutput: "https://newexample.com/newimage1.jpg",
			want:        `<img src="https://newexample.com/newimage1.jpg"/>`,
		},
		{
			name:        "multiple images",
			input:       `<img src="https://example.com/image1.jpg"/><img src="https://example.com/image2.jpg"/>`,
			imageOutput: "https://newexample.com/newimage.jpg",
			want:        `<img src="https://newexample.com/newimage.jpg"/><img src="https://newexample.com/newimage.jpg"/>`,
		},
		{
			name:        "nested images",
			input:       `<div><p><img src="https://example.com/image1.jpg"></p><span><img src="https://example.com/image2.jpg"></span></div>`,
			imageOutput: "https://newexample.com/newimage.jpg",
			want:        `<div><p><img src="https://newexample.com/newimage.jpg"/></p><span><img src="https://newexample.com/newimage.jpg"/></span></div>`,
		},
		{
			name:        "no images",
			input:       `<p>No images here!</p>`,
			imageOutput: "",
			want:        `<p>No images here!</p>`,
		},
		{
			name:        "image output error",
			input:       `<div><p><img src="https://example.com/image1.jpg"></p><span><img src="https://example.com/image2.jpg"></span></div>`,
			imageOutput: "",
			want:        `<div><p></p><span></span></div>`,
		},
		{
			name:        "image src empty",
			input:       `<div><p><img src=""></p><span><img src="https://example.com/image2.jpg"></span></div>`,
			imageOutput: "https://example.com/update-image2.jpg",
			want:        `<div><p></p><span><img src="https://example.com/update-image2.jpg"/></span></div>`,
		},
		{
			name:        "image output error 2",
			input:       `<div><p><img src="https://example.com/image1.jpg"></p><span><img src=""></span></div>`,
			imageOutput: "",
			want:        `<div><p></p><span></span></div>`,
		},
		{
			name:        "img with style attribute",
			input:       `<img src="https://example.com/image1.jpg" style="width:100px"/>`,
			imageOutput: "https://newexample.com/newimage1.jpg",
			want:        `<img src="https://newexample.com/newimage1.jpg"/>`,
		},
		{
			name:        "div with style, img with style",
			input:       `<div style="color:red"><img src="https://example.com/image1.jpg" style="width:100px"></div>`,
			imageOutput: "https://newexample.com/newimage1.jpg",
			want:        `<div><img src="https://newexample.com/newimage1.jpg"/></div>`,
		},
		{
			name:        "img with multiple attributes",
			input:       `<img src="https://example.com/image1.jpg" alt="pic" style="width:100px"/>`,
			imageOutput: "https://newexample.com/newimage1.jpg",
			want:        `<img src="https://newexample.com/newimage1.jpg" alt="pic"/>`,
		},
		{
			name:        "img without src",
			input:       `<img alt="no src"/>`,
			imageOutput: "should-not-be-used",
			want:        ``,
		},
		{
			name:        "empty string",
			input:       ``,
			imageOutput: "should-not-be-used",
			want:        ``,
		},
		{
			name:        "img outside body",
			input:       `<img src="https://example.com/image1.jpg"/>`,
			imageOutput: "https://newexample.com/newimage1.jpg",
			want:        `<img src="https://newexample.com/newimage1.jpg"/>`,
		},
		{
			name:        "only style tag",
			input:       `<p style="color: red;">This is a paragraph.</p>`,
			imageOutput: "",
			want:        `<p>This is a paragraph.</p>`,
		},
		{
			name:        "remove svg tag",
			input:       `<div><svg><circle cx="50" cy="50" r="40"/></svg><span>abc</span></div>`,
			imageOutput: "",
			want:        `<div><span>abc</span></div>`,
		},
		{
			name:        "remove class attributes",
			input:       `<div class="test"><span class="nested">abc</span><p>def</p></div>`,
			imageOutput: "",
			want:        `<div><span>abc</span><p>def</p></div>`,
		},
		{
			name:        "remove class and style attributes",
			input:       `<div class="test" style="color:red"><span class="nested" style="font-size:12px">abc</span><p>def</p></div>`,
			imageOutput: "",
			want:        `<div><span>abc</span><p>def</p></div>`,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := ReplaceImageSrc(context.TODO(), tt.input, func(inputUrl string) (outputUrl string, err error) {
				return tt.imageOutput, nil
			}, 10)
			if err != nil {
				t.Error(err)
			}
			require.Equal(t, tt.want, result)
		})
	}
}

func Test_traverseImg(t *testing.T) {
	tests := []struct {
		name           string
		input          string
		imageOutput    string
		imageOutputErr error
		want           string
	}{
		{
			name:           "single image",
			input:          `<img src="https://example.com/image1.jpg"/>`,
			imageOutput:    "https://newexample.com/newimage1.jpg",
			imageOutputErr: nil,
			want:           `<img src="https://newexample.com/newimage1.jpg"/>`,
		},
		{
			name:           "multiple images",
			input:          `<img src="https://example.com/image1.jpg"/><img src="https://example.com/image2.jpg"/>`,
			imageOutput:    "https://newexample.com/newimage.jpg",
			imageOutputErr: nil,
			want:           `<img src="https://newexample.com/newimage.jpg"/><img src="https://newexample.com/newimage.jpg"/>`,
		},
		{
			name:           "nested images",
			input:          `<div><p><img src="https://example.com/image1.jpg"></p><span><img src="https://example.com/image2.jpg"></span></div>`,
			imageOutput:    "https://newexample.com/newimage.jpg",
			imageOutputErr: nil,
			want:           `<div><p><img src="https://newexample.com/newimage.jpg"/></p><span><img src="https://newexample.com/newimage.jpg"/></span></div>`,
		},
		{
			name:           "no images",
			input:          `<p>No images here!</p>`,
			imageOutput:    "",
			imageOutputErr: nil,
			want:           `<p>No images here!</p>`,
		},
		{
			name:           "convert image error",
			input:          `<div><p><img src="https://example.com/image1.jpg"></p><span><img src="https://example.com/image2.jpg"></span></div>`,
			imageOutput:    "",
			imageOutputErr: errors.New("error"),
			want:           `<div><p></p><span></span></div>`,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			doc, err := html.Parse(strings.NewReader(tt.input))
			if err != nil {
				t.Fatal(err)
			}
			processor := &imageProcessor{
				ctx: context.TODO(),
				f: func(inputUrl string) (outputUrl string, err error) {
					return tt.imageOutput, tt.imageOutputErr
				},
				maxImages: 10,
				count:     0,
			}

			processor.traverseAndProcessImages(doc)

			var buf strings.Builder
			if err := html.Render(&buf, doc); err != nil {
				t.Fatal(err)
			}

			require.Contains(t, buf.String(), tt.want)
		})
	}
}

func Test_getBodyContext(t *testing.T) {
	tests := []struct {
		name  string
		input string
		want  string
	}{
		{
			name:  "body with content",
			input: `<html><head><title>Test</title></head><body><p>Some text</p><img src="https://example.com/image.jpg"></body></html>`,
			want:  `<p>Some text</p><img src="https://example.com/image.jpg"/>`,
		},
		{
			name:  "empty body",
			input: `<html><head><title>Test</title></head><body></body></html>`,
			want:  ``,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			doc, err := html.Parse(strings.NewReader(tt.input))
			if err != nil {
				t.Fatal(err)
			}

			result, err := extractBodyContent(doc)
			require.NoError(t, err)
			require.Equal(t, tt.want, result)
		})
	}
}

func Test_preprocessHTMLNode(t *testing.T) {
	tests := []struct {
		name  string
		input string
		want  string
	}{
		{
			name:  "remove style from div",
			input: `<div style="color:red">text</div>`,
			want:  `<html><head></head><body><div>text</div></body></html>`,
		},
		{
			name:  "remove style from nested nodes",
			input: `<div style="color:red"><span style="font-size:12px">abc</span><p>def</p></div>`,
			want:  `<html><head></head><body><div><span>abc</span><p>def</p></div></body></html>`,
		},
		{
			name:  "remove svg tag",
			input: `<div><svg><circle cx="50" cy="50" r="40"/></svg><span>abc</span></div>`,
			want:  `<html><head></head><body><div><span>abc</span></div></body></html>`,
		},
		{
			name:  "remove style and svg",
			input: `<div style="color:red"><svg style="display:none"></svg><p style="font-size:12px">text</p></div>`,
			want:  `<html><head></head><body><div><p>text</p></div></body></html>`,
		},
		{
			name:  "no style or svg",
			input: `<div><span>abc</span></div>`,
			want:  `<html><head></head><body><div><span>abc</span></div></body></html>`,
		},
		{
			name:  "svg as root",
			input: `<svg><circle cx="50" cy="50" r="40"/></svg>`,
			want:  `<html><head></head><body></body></html>`,
		},
		{
			name:  "class and style attributes",
			input: `<div class="test" style="color:red"><span class="nested" style="font-size:12px">abc</span><p>def</p></div>`,
			want:  `<html><head></head><body><div><span>abc</span><p>def</p></div></body></html>`,
		},
		{
			name:  "class attributes",
			input: `<div class="test"><span class="nested">abc</span><p>def</p></div>`,
			want:  `<html><head></head><body><div><span>abc</span><p>def</p></div></body></html>`,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			nodes, err := html.ParseFragment(strings.NewReader(tt.input), nil)
			require.NoError(t, err)
			require.NotEmpty(t, nodes)
			preprocessHTMLNode(nodes[0])

			var buf strings.Builder
			err = html.Render(&buf, nodes[0])
			require.NoError(t, err)
			require.Equal(t, tt.want, buf.String())
		})
	}
}
