package renderer

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"golang.org/x/net/html"
)

func Test_TTSRender(t *testing.T) {
	tests := []struct {
		name string
		desc string
		want string
	}{
		{
			name: "root text",
			desc: `my product nice`,
			want: `<p>my product nice</p>`,
		},
		{
			name: "h1",
			desc: `<h1>my title</h1>`,
			want: `<p><strong>my title</strong></p>`,
		},
		{
			name: "root img",
			desc: `<img src="https://cdn.shopify.com/s/files/1/0559/1328/3637/products/chopsticks3_480x480.png?v=1662732036-5" alt="" data-mce-fragment="1">`,
			want: `<img src="https://cdn.shopify.com/s/files/1/0559/1328/3637/products/chopsticks3_480x480.png?v=1662732036-5"></img>`,
		},
		{
			name: "p tag 1",
			desc: `<p>my product</p>`,
			want: `<p>my product</p>`,
		},
		{
			name: "p tag with embedded tag",
			desc: `<p>my <i>nice</i> <b>product</b> <strong>is</strong> <u>made</u> <em>in</em> China</p>`,
			want: `<p>my <i>nice</i> <b>product</b> <strong>is</strong> <u>made</u> <em>in</em> China</p>`,
		},
		{
			name: "root li with embedded tag",
			desc: `<li> <em>Emphasis in list</em> custom <br>list <li>`,
			want: `<li> <em>Emphasis in list</em> custom <br/>list </li>`,
		},
		{
			name: "root li with embedded img tag",
			desc: `<li> custom list with img <p><img src="https://cdn.shopify.com/s/files/1/0559/1328/3637/products/chopsticks3_480x480.png?v=1662732036-1" alt="" data-mce-fragment="1"></img></p></li>`,
			want: `<li> custom list with img </li><img src="https://cdn.shopify.com/s/files/1/0559/1328/3637/products/chopsticks3_480x480.png?v=1662732036-1"></img>`,
		},
		{
			name: "ol all",
			desc: `<ul><li><p>Made from 100% natural ingredients.<img src="https://cdn.shopify.com/s/files/1/0559/1328/3637/products/chopsticks3_480x480.png?v=1662732036-2" alt="" ></p></li><li><p>Hard <strong>and</strong> crunchy to <br>help wear down teeth.</p></li><li><p>6 separate cookies to help with portion control.</p><li><p>embedded li tag</p></li></li><li><p>Grain-free with no added sugar.</p></li></ul>`,
			want: `<ul><li>Made from 100% natural ingredients.</li><li>Hard <strong>and</strong> crunchy to <br/>help wear down teeth.</li><li>6 separate cookies to help with portion control.</li><li>embedded li tag</li><li>Grain-free with no added sugar.</li></ul><img src="https://cdn.shopify.com/s/files/1/0559/1328/3637/products/chopsticks3_480x480.png?v=1662732036-2"></img>`,
		},
		{
			name: "div has strong tag or b tag or i tag or u tag",
			desc: `<style type="text/css"><!--td {border: 1px solid #cccccc;}br {mso-data-placement:same-cell;}--></style><div style="text-align: center;"><strong>Baby Powder | Rose | Vanilla </strong></div><div style="text-align: center;"><b>Baby Powder | Rose | Vanilla </b></div><div style="text-align: center;"><i>Baby Powder | Rose | Vanilla </i></div><div style="text-align: center;"><u>Baby Powder | Rose | Vanilla </u></div><!---->`,
			want: `<p><strong>Baby Powder | Rose | Vanilla </strong></p><p><b>Baby Powder | Rose | Vanilla </b></p><p><i>Baby Powder | Rose | Vanilla </i></p><p><u>Baby Powder | Rose | Vanilla </u></p>`,
		},
		{
			name: "div has plain text",
			desc: `<style type="text/css"><!--td {border: 1px solid #cccccc;}br {mso-data-placement:same-cell;}--></style><div style="text-align: center;">123</div><!---->`,
			want: `<p>123</p>`,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			input := tt.desc
			result, err := TTSRender(context.TODO(), input, func(inputUrl string) (outputUrl string, err error) {
				return inputUrl, nil
			})
			if err != nil {
				t.Error(err)
			}
			// fmt.Println(tt.name, "result")
			// fmt.Println(result)
			assert.Equal(t, tt.want, result)
		})
	}
}

func Test_getAttribute(t *testing.T) {
	tests := []struct {
		name    string
		node    *html.Node
		attrKey string
		want    string
		wantErr bool
	}{
		{
			name: "Test with existing attribute",
			node: &html.Node{
				Attr: []html.Attribute{
					{Key: "class", Val: "test-class"},
					{Key: "id", Val: "test-id"},
				},
			},
			attrKey: "class",
			want:    "test-class",
			wantErr: false,
		},
		{
			name: "Test with non-existing attribute",
			node: &html.Node{
				Attr: []html.Attribute{
					{Key: "class", Val: "test-class"},
					{Key: "id", Val: "test-id"},
				},
			},
			attrKey: "style",
			want:    "",
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := getAttribute(tt.node, tt.attrKey)
			if (err != nil) != tt.wantErr {
				t.Errorf("getAttribute() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("getAttribute() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_getNodeHTMLStr(t *testing.T) {
	tests := []struct {
		name    string
		node    *html.Node
		want    string
		wantErr bool
	}{
		{
			name: "Test with valid node",
			node: &html.Node{
				Type: html.ElementNode,
				Data: "p",
				FirstChild: &html.Node{
					Type: html.TextNode,
					Data: "Hello, World!",
				},
			},
			want:    "<p>Hello, World!</p>",
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := getNodeHTMLStr(tt.node)
			if (err != nil) != tt.wantErr {
				t.Errorf("getNodeHTMLStr() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("getNodeHTMLStr() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_ttsRenderer_replaceImgSrc(t *testing.T) {
	tests := []struct {
		name     string
		src      string
		visited  map[string]bool
		imgCount int
		want     string
	}{
		{
			name:     "Test with unvisited source and image count less than 9",
			src:      "https://example.com/image1.png",
			visited:  make(map[string]bool),
			imgCount: 8,
			want:     "https://example.com/image1.png",
		},
		{
			name: "Test with visited source",
			src:  "https://example.com/image1.png",
			visited: map[string]bool{
				"https://example.com/image1.png": true,
			},
			imgCount: 8,
			want:     "",
		},
		{
			name:     "Test with image count equal to 9",
			src:      "https://example.com/image1.png",
			visited:  make(map[string]bool),
			imgCount: 9,
			want:     "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			r := &ttsRenderer{
				srcVisited: tt.visited,
				imageCnt:   tt.imgCount,
				f: func(inputUrl string) (string, error) {
					return inputUrl, nil
				},
			}
			if got := r.replaceImgSrc(tt.src); got != tt.want {
				t.Errorf("ttsRenderer.replaceImgSrc() = %v, want %v", got, tt.want)
			}
		})
	}
}
