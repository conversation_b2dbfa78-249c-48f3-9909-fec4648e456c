package renderer

import (
	"bytes"
	"context"
	"errors"
	"strings"

	"go.uber.org/zap"
	"golang.org/x/net/html"

	"github.com/AfterShip/gopkg/log"
)

const (
	maxDepth      = 20
	imgTag        = "img"
	bodyTag       = "body"
	srcAttr       = "src"
	emptySrcError = "empty src attribute"
)

// ReplaceImageSrc replaces image sources in HTML content using the provided function
// maxImages specifies the maximum number of images to process (0 means no limit)
func ReplaceImageSrc(ctx context.Context, htmlStr string, f OnImageFunc, maxImages int) (string, error) {
	doc, err := html.Parse(strings.NewReader(htmlStr))
	if err != nil {
		return "", err
	}

	preprocessHTMLNode(doc)

	processor := &imageProcessor{
		ctx:       ctx,
		f:         f,
		maxImages: maxImages,
		count:     0,
	}
	processor.traverseAndProcessImages(doc)

	return extractBodyContent(doc)
}

// imageProcessor holds the state for image processing
type imageProcessor struct {
	ctx       context.Context
	f         OnImageFunc
	maxImages int
	count     int
}

// extractBodyContent extracts and renders the content inside the <body> tag
func extractBodyContent(doc *html.Node) (string, error) {
	body := findNode(doc, bodyTag, 0)
	if body == nil {
		return "", errors.New("body not found")
	}

	var buf bytes.Buffer
	for c := body.FirstChild; c != nil; c = c.NextSibling {
		if err := html.Render(&buf, c); err != nil {
			return "", err
		}
	}

	return buf.String(), nil
}

// findNode recursively searches for a node with the specified tag name
func findNode(n *html.Node, tagName string, depth int) *html.Node {
	if n.Type == html.ElementNode && n.Data == tagName {
		return n
	}

	if depth >= maxDepth {
		return nil
	}

	for c := n.FirstChild; c != nil; c = c.NextSibling {
		if found := findNode(c, tagName, depth+1); found != nil {
			return found
		}
	}

	return nil
}

// traverseAndProcessImages traverses the HTML tree and processes images
func (p *imageProcessor) traverseAndProcessImages(n *html.Node) {
	if n.Type == html.ElementNode && n.Data == imgTag {
		p.processImageNode(n)
		return // Images can't have child nodes in valid HTML
	}

	// Process child nodes
	for c := n.FirstChild; c != nil; {
		next := c.NextSibling // Save next sibling before processing (node might be removed)
		p.traverseAndProcessImages(c)
		c = next
	}
}

// processImageNode handles src replacement for a single image node
func (p *imageProcessor) processImageNode(imgNode *html.Node) {
	// If we've reached max images, remove this image
	if p.maxImages > 0 && p.count >= p.maxImages {
		removeNode(imgNode)
		return
	}

	src, err := getAttribute(imgNode, srcAttr)
	if err != nil {
		log.GlobalLogger().InfoCtx(p.ctx, "getAttribute failed", zap.Error(err))
	}

	if src == "" {
		removeNode(imgNode)
		return
	}

	for i, attr := range imgNode.Attr {
		if attr.Key != srcAttr {
			continue
		}

		replacedSrc, err := p.f(src)
		if err != nil {
			p.logImageReplacementError(src, err)
		}

		if replacedSrc == "" {
			removeNode(imgNode)
			return
		}

		p.logImageReplacement(src, replacedSrc)
		imgNode.Attr[i].Val = replacedSrc
		p.count++ // Only increment count for successfully processed images
		break     // Only one src attribute should exist
	}
}

// removeNode safely removes a node from its parent
func removeNode(n *html.Node) {
	if n.Parent != nil {
		n.Parent.RemoveChild(n)
	}
}

// logImageReplacement logs successful image source replacement
func (p *imageProcessor) logImageReplacement(original, replaced string) {
	log.GlobalLogger().InfoCtx(p.ctx,
		"replace description image src",
		zap.String("origin", original),
		zap.String("after", replaced))
}

// logImageReplacementError logs image replacement errors with appropriate level
func (p *imageProcessor) logImageReplacementError(src string, err error) {
	if strings.Contains(err.Error(), "not less than 300 x 300") {
		log.GlobalLogger().InfoCtx(p.ctx, "replace description image src failed",
			zap.String("src", src), zap.Error(err))
	} else {
		log.GlobalLogger().WarnCtx(p.ctx, "replace description image src failed",
			zap.String("src", src), zap.Error(err))
	}
}

// preprocessHTMLNode
// 递归移除所有节点的 style 属性
// 递归移除所有 svg 标签
func preprocessHTMLNode(n *html.Node) {
	for c := n.FirstChild; c != nil; {
		next := c.NextSibling
		if c.Type == html.ElementNode && c.Data == "svg" {
			removeNode(c)
		} else {
			preprocessHTMLNode(c)
		}
		c = next
	}
	if n.Type == html.ElementNode {
		newAttrs := make([]html.Attribute, 0, len(n.Attr))
		for _, attr := range n.Attr {
			if attr.Key != "style" && attr.Key != "class" {
				newAttrs = append(newAttrs, attr)
			}
		}
		n.Attr = newAttrs
	}
}
