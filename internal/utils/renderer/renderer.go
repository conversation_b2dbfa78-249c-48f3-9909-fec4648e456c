package renderer

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	"strings"

	md "github.com/<PERSON>/html-to-markdown"
	"github.com/<PERSON><PERSON><PERSON><PERSON>/html-to-markdown/plugin"
	"github.com/microcosm-cc/bluemonday"
	"github.com/samber/lo"
	"go.uber.org/zap"
	"golang.org/x/net/html"

	"github.com/AfterShip/gopkg/log"
)

const (
	imgConst = "img"
)

var (
	emptyElementSanitizePolicy = bluemonday.NewPolicy()
	listElementSanitizePolicy  = bluemonday.NewPolicy()
	liElementSanitizePolicy    = bluemonday.NewPolicy()
	pElementSanitizePolicy     = bluemonday.NewPolicy()
)

func init() {
	listElementSanitizePolicy.AllowElements("li", "strong", "em", "u", "b", "i", "br")
	liElementSanitizePolicy.AllowElements("strong", "em", "u", "b", "i", "br")
	pElementSanitizePolicy.AllowElements("strong", "em", "u", "b", "i", "br")
}

func TTSRender(ctx context.Context, str string, f OnImageFunc) (string, error) {
	sanitizedStr := sanitize(str)

	converter := md.NewConverter("", true, nil)
	converter.Use(plugin.Table())
	r := &ttsRenderer{
		ctx:         ctx,
		buffer:      &bytes.Buffer{},
		imageCnt:    0,
		f:           f,
		mdConverter: converter,
		srcVisited:  make(map[string]bool),
	}

	doc, err := html.Parse(strings.NewReader(sanitizedStr))
	if err != nil {
		return "", err
	}
	r.traverseV2(doc)

	return r.buffer.String(), nil
}

type ttsRenderer struct {
	ctx         context.Context
	buffer      *bytes.Buffer
	imageCnt    int
	f           OnImageFunc
	mdConverter *md.Converter
	srcVisited  map[string]bool
}

// TTS 描述规则
// https://partner.tiktokshop.com/docv2/page/6502fc8da57708028b42b18a?external_id=6502fc8da57708028b42b18a
// Prerequisites:
//
// - Must conform to html syntax
// - Currently, it only supports html tags <p> <img> <ul> <ol> <li> <br> <strong> <b> <i> <em> <u>,
// other HTML tags will be filtered out and will not take effect.
// - Tags can not be nested
// - This field character limit needs to be within 10000 characters.
// - It is recommended to avoid using Chinese because the copy will be displayed to local users.
// - The img tag needs to include the src, width, and height attributes, and the image dimensions can not exceed 4000
// - Only Tiktok Shop image URLs are allowed, no external URLs.
// - <strong> <b> <i> <em> <u> <br> should be used within <p> and <li>
// 目前可以支持的标签
// 独立的 <li>
// [<ul> <ol>] + <li> 的列表
// <li> 可以包含 <strong> <b> <i> <em> <u> <br>
// <p>  可以包含 <strong> <b> <i> <em> <u> <br>
// <h1> - <h6> 元素会默认转成 <p><strong> 元素
// 目前已知的处理不好的情况：ol 和 ul 里面的 li 如果有嵌套 li, 则嵌套 li 会被转成非嵌套 li
func (r *ttsRenderer) traverseV2(n *html.Node) {
	if n.Type == html.ElementNode && !textEmpty(n.Data) {
		r.traverseBr(n)
		r.traverseH(n)
		r.traverseP(n)
		r.traverseList(n)
		r.traverseLi(n)
		r.traverseImg(n)
		r.traverseOther(n)
	}
	r.traverseText(n)
	for child := n.FirstChild; child != nil; child = child.NextSibling {
		r.traverseV2(child)
	}
}

func (r *ttsRenderer) traverseBr(n *html.Node) {
	if !lo.Contains([]string{"br"}, n.Data) {
		return
	}
	r.buffer.WriteString("<br>")
	// r.buffer.WriteString("\n")
	n.FirstChild = nil
}

func (r *ttsRenderer) traverseH(n *html.Node) {
	if !lo.Contains([]string{"h1", "h2", "h3", "h4", "h5", "h6"}, n.Data) {
		return
	}
	nodeStr, err := getNodeHTMLStr(n)
	if err != nil {
		log.GlobalLogger().Info("getNodeHTMLStr failed", zap.Error(err))
	}
	filtered := emptyElementSanitizePolicy.Sanitize(nodeStr)
	r.buffer.WriteString(fmt.Sprintf("<p><strong>%v</strong></p>", filtered))
	// r.buffer.WriteString("\n")
	n.FirstChild = nil
}

func (r *ttsRenderer) traverseP(n *html.Node) {
	if n.Data != "p" {
		return
	}
	nodeStr, err := getNodeHTMLStr(n)
	if err != nil {
		log.GlobalLogger().Info("getNodeHTMLStr failed", zap.Error(err))
	}
	filtered := pElementSanitizePolicy.Sanitize(nodeStr)
	if !textEmpty(filtered) {
		str := fmt.Sprintf("<p>%v</p>", filtered)
		r.buffer.WriteString(str)
		// r.buffer.WriteString("\n")
	}
	r.traverseInnerImg(n)
	n.FirstChild = nil
}

func (r *ttsRenderer) traverseList(n *html.Node) {
	if !lo.Contains([]string{"ol", "ul"}, n.Data) {
		return
	}
	cleanEmptyLi(n)
	nodeStr, err := getNodeHTMLStr(n)
	if err != nil {
		log.GlobalLogger().Info("getNodeHTMLStr failed", zap.Error(err))
	}
	filtered := listElementSanitizePolicy.Sanitize(nodeStr)
	if !textEmpty(filtered) {
		str := fmt.Sprintf("<%v>%v</%v>", n.Data, filtered, n.Data)
		r.buffer.WriteString(str)
		// r.buffer.WriteString("\n")
	}
	r.traverseInnerImg(n)
	n.FirstChild = nil
}

func (r *ttsRenderer) traverseLi(n *html.Node) {
	if n.Data != "li" {
		return
	}
	nodeStr, err := getNodeHTMLStr(n)
	if err != nil {
		log.GlobalLogger().Info("getNodeHTMLStr failed", zap.Error(err))
	}

	filtered := liElementSanitizePolicy.Sanitize(nodeStr)
	if !textEmpty(filtered) {
		str := fmt.Sprintf("<li>%v</li>", filtered)
		r.buffer.WriteString(str)
		// r.buffer.WriteString("\n")
	}
	r.traverseInnerImg(n)
	n.FirstChild = nil
}

func (r *ttsRenderer) traverseImg(n *html.Node) {
	if n.Data != imgConst {
		return
	}
	imgUrl, err := getAttribute(n, "src")
	if err != nil {
		log.GlobalLogger().Info("getAttribute failed", zap.Error(err))
	} else {
		replacedStr := r.replaceImgSrc(imgUrl)
		if replacedStr != "" {
			r.buffer.WriteString(fmt.Sprintf("<img src=\"%v\"></img>", replacedStr))
			// r.buffer.WriteString("\n")
		}
	}
	n.FirstChild = nil
}

func (r *ttsRenderer) traverseInnerImg(n *html.Node) {
	imgUrls := r.findImages(n)
	for _, imgUrl := range imgUrls {
		replacedStr := r.replaceImgSrc(imgUrl)
		if replacedStr != "" {
			r.buffer.WriteString(fmt.Sprintf("<img src=\"%v\"></img>", replacedStr))
			// r.buffer.WriteString("\n")
		}
	}
}

func (r *ttsRenderer) traverseText(n *html.Node) {
	if n.Type == html.TextNode && n.Parent != nil && n.Parent.Data == "body" && !textEmpty(n.Data) {
		str := fmt.Sprintf("<p>%v</p>", n.Data)
		r.buffer.WriteString(str)
		// r.buffer.WriteString("\n")
	}
}

func (r *ttsRenderer) traverseOther(n *html.Node) {
	if !lo.Contains([]string{"strong", "b", "i", "em", "u"}, n.Data) {
		return
	}

	nodeStr, err := getNodeHTMLStr(n)
	if err != nil {
		log.GlobalLogger().Info("getNodeHTMLStr failed", zap.Error(err))
	}
	filtered := pElementSanitizePolicy.Sanitize(nodeStr)
	if !textEmpty(filtered) {
		str := fmt.Sprintf("<p>%v</p>", filtered)
		r.buffer.WriteString(str)
		// r.buffer.WriteString("\n")
	}

	n.FirstChild = nil
}

func (r *ttsRenderer) replaceImgSrc(src string) string {
	if r.srcVisited[src] {
		return ""
	}
	r.srcVisited[src] = true
	if r.imageCnt < 9 && r.f != nil {
		replacedSrc, err := r.f(src)
		if err != nil {
			if strings.Contains(err.Error(), "not less than 300 x 300") {
				log.GlobalLogger().InfoCtx(r.ctx, "replace description image src failed", zap.String("src", src), zap.Error(err))
			} else {
				log.GlobalLogger().WarnCtx(r.ctx, "replace description image src failed", zap.String("src", src), zap.Error(err))
			}
			return ""
		}
		log.GlobalLogger().InfoCtx(r.ctx,
			"replace description image src",
			zap.String("origin", src),
			zap.String("after", replacedSrc))
		r.imageCnt++
		return replacedSrc
	}

	return ""
}

// 只跟踪一个元素下面的图片
func (r *ttsRenderer) findImages(n *html.Node) []string {
	imgUrls := make([]string, 0)
	current := n.FirstChild
	for {
		if current == nil {
			break
		}
		if current.Type == html.ElementNode && current.Data == imgConst {
			src, err := getAttribute(current, "src")
			if err != nil {
				log.GlobalLogger().InfoCtx(r.ctx, "getAttribute failed", zap.Error(err))
			}
			if src != "" {
				imgUrls = append(imgUrls, src)
			}
		}
		childImgUrls := r.findImages(current)
		if len(childImgUrls) > 0 {
			imgUrls = append(imgUrls, childImgUrls...)
		}
		if current == n.LastChild {
			break
		}
		current = current.NextSibling
	}
	return imgUrls
}

// 清理只包含 br, 换行符 空格 的 li 标签
func cleanEmptyLi(n *html.Node) {
	current := n.FirstChild
	toDeleteNodes := make([]*html.Node, 0)

	for current != nil {
		if current.Type == html.ElementNode && current.Data == "li" {
			nodeStr, err := getNodeHTMLStr(current)
			if err != nil {
				log.GlobalLogger().Info("getNodeHTMLStr failed", zap.Error(err))
			}
			filtered := emptyElementSanitizePolicy.Sanitize(nodeStr)
			if textEmpty(filtered) {
				toDeleteNodes = append(toDeleteNodes, current)
			}
		}
		if current == n.LastChild {
			break
		}
		current = current.NextSibling
	}

	for _, node := range toDeleteNodes {
		n.RemoveChild(node)
	}
}

func textEmpty(str string) bool {
	str = strings.ReplaceAll(str, "\n", "<br/>")
	return strings.TrimSpace(str) == ""
}

func getNodeHTMLStr(n *html.Node) (string, error) {
	buf := &bytes.Buffer{}
	err := html.Render(buf, n)
	if err != nil {
		return "", err
	}
	return buf.String(), nil
}

// 需要把电商平台的图片换成 tts 的图片
type OnImageFunc func(inputUrl string) (outputUrl string, err error)

func sanitize(str string) string {
	p := bluemonday.NewPolicy()
	p.AllowElements("h1", "h2", "h3", "h4", "h5", "h6")
	p.AllowElements("p", "strong", "em", "b", "u", "i")
	p.AllowElements("li", "ol", "ul")
	p.AllowElements("br")
	p.AllowElements(imgConst)
	p.AllowAttrs("src").OnElements(imgConst)
	sanitizedStr := p.Sanitize(str)

	return sanitizedStr
}

func getAttribute(n *html.Node, key string) (string, error) {
	for _, attr := range n.Attr {
		if attr.Key == key {
			return attr.Val, nil
		}
	}
	return "", errors.New(key + " not exist in attribute!")
}
