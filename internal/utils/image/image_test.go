package image

import "testing"

func Test_FormatOriginURL(t *testing.T) {
	type args struct {
		originURL string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "Success",
			args: args{
				originURL: "https://example.com",
			},
			want: "https://example.com",
		},
		{
			name: "Success",
			args: args{
				originURL: "//example.com",
			},
			want: "https://example.com",
		},
		{
			name: "Success",
			args: args{
				originURL: "http://example.com",
			},
			want: "https://example.com",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := FormatOriginURL(tt.args.originURL); got != tt.want {
				t.Errorf("FormatOriginURL() = %v, want %v", got, tt.want)
			}
		})
	}

}

func TestGetExtensionByRawURL(t *testing.T) {
	tests := []struct {
		name string
		url  string
		want string
	}{
		{
			name: "Test with .png extension",
			url:  "https://example.com/image.png",
			want: "png",
		},
		{
			name: "Test with .jpg extension",
			url:  "https://example.com/image.jpg",
			want: "jpg",
		},
		{
			name: "Test with no extension",
			url:  "https://example.com/image",
			want: "",
		},
		{
			name: "Test with empty string",
			url:  "",
			want: "",
		},
		{
			name: "Test with invalid URL",
			url:  "invalid",
			want: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := GetExtensionByRawURL(tt.url); got != tt.want {
				t.Errorf("GetExtensionByRawURL() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestIsSalesChannelURL(t *testing.T) {
	type args struct {
		url string
	}
	tests := []struct {
		name  string
		args  args
		want  string
		want1 bool
	}{
		// TODO: Add test cases.
		{
			name: "Case 1: Domain tiktokcdn ",
			args: args{
				url: "https://p16-oec-ttp.tiktokcdn-us.com/tos-useast5-i-omjb5zjo8w-tx/fa52d3c8e4aa414dbbc3c8ba91908614~tplv-omjb5zjo8w-origin-jpeg.jpeg?from=520841845",
			},
			want:  "tos-useast5-i-omjb5zjo8w-tx/fa52d3c8e4aa414dbbc3c8ba91908614",
			want1: true,
		},
		{
			name: "Case 2: Domain ibyteimg",
			args: args{
				url: "https://p19-oec-eu-common-useast2a.ibyteimg.com/tos-useast2a-i-tulkllf4y5-euttp/dd313b3db2734b64a4e2fb545f24dd9b~tplv-tulkllf4y5-origin-jpeg.jpeg?from=520841845",
			},
			want:  "tos-useast2a-i-tulkllf4y5-euttp/dd313b3db2734b64a4e2fb545f24dd9b",
			want1: true,
		},
		{
			name: "Case 3: Invalid URL",
			args: args{
				url: "https://cdn.shopify.com/s/files/1/0553/5186/9572/files/HORSESCHRISTMASLIGHTSEXPIC_adde8814-5f6e-452c-9f07-bd8660585300.png?v=1699413197",
			},
			want:  "",
			want1: false,
		},
		{
			name: "Case 4: Domain tiktokc",
			args: args{
				url: "https://p19-oec-useast8.tiktokcdn-us.com/tos-useast8-i-rt0ujvrtvp-tx2/d3092bf427d1494abb9bb7f0891bbb21~tplv-rt0ujvrtvp-origin-jpeg.jpeg?from=1432613627",
			},
			want:  "tos-useast8-i-rt0ujvrtvp-tx2/d3092bf427d1494abb9bb7f0891bbb21",
			want1: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, got1 := IsSalesChannelURL(tt.args.url, nil)
			if got != tt.want {
				t.Errorf("IsSalesChannelURL() got = %v, want %v", got, tt.want)
			}
			if got1 != tt.want1 {
				t.Errorf("IsSalesChannelURL() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}

func TestIsVideoURL(t *testing.T) {
	type args struct {
		url string
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{
			name: "Case 1: Video URL",
			args: args{
				url: "https://example.com/video.mp4",
			},
			want: true,
		},
		{
			name: "Case 2: Image URL",
			args: args{
				url: "https://cdn.shopify.com/s/files/1/0633/0102/5928/files/<EMAIL>?v=1719459793",
			},
			want: false,
		},
		{
			name: "Case 3: Invalid URL",
			args: args{
				url: "https://cdn.shopify.com/s/files/1/0633/0102/5928/files",
			},
			want: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := IsVideoURL(tt.args.url); got != tt.want {
				t.Errorf("IsVideoURL() = %v, want %v", got, tt.want)
			}
		})
	}
}
