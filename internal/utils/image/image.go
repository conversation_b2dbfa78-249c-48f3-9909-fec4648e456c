package image

import (
	"bytes"
	"image"
	"net/url"
	"path"
	"regexp"
	"strings"

	"github.com/gabriel-vasile/mimetype"
	"github.com/pkg/errors"
	"github.com/rubenfonseca/fastimage"
)

var (
	defaultCDNDomainRegExps = []*regexp.Regexp{
		regexp.MustCompile(`https://.*\.tiktokcdn.*\.com/(.*)[~]+.*`),
		regexp.MustCompile(`https://.*ibyteimg\.com/(.*)[~]+.*`),
	}
)

//nolint:unused
func DetectFileMime(fileBytes []byte) (string, error) {
	if len(fileBytes) == 0 {
		return "", nil
	}
	// 请注意，任意字符串甚至空字符串，都会被探测为 txt 类型
	m := mimetype.Detect(fileBytes)
	if m == nil {
		return "", errors.New("detect mime error, return nil")
	}

	// .jpeg
	extensionType := m.Extension()
	// 去掉 .
	var fileType string
	if strings.Contains(extensionType, ".") &&
		strings.LastIndex(extensionType, ".") == 0 {
		length := len(extensionType)
		fileType = extensionType[1:length]
	}
	return strings.ToLower(fileType), nil
}

//nolint:unused
func CheckMinSize(imageBytes []byte) (bool, error) {
	// go-image 原生包仅支持解码 jpeg、png、gif 这几种格式的图片，其他类型的图片会报错
	imageConfig, _, err := image.DecodeConfig(bytes.NewReader(imageBytes))
	if err != nil {
		return false, err
	}
	return imageConfig.Height >= 300 && imageConfig.Width >= 300, nil
}

//nolint:unused
func CheckMinSizeByURL(url string) (bool, error) {
	_, dimensions, err := fastimage.DetectImageType(url)
	if err != nil {
		return false, err
	}
	return dimensions.Height >= 300 && dimensions.Width >= 300, nil
}

func GetExtensionByRawURL(rawURL string) string {
	if len(rawURL) == 0 {
		return ""
	}
	u, err := url.Parse(rawURL)
	if err != nil {
		return "false"
	}
	pos := strings.LastIndex(u.Path, ".")
	if pos == -1 || len(u.Path) < 5 {
		// file extension not exist
		return ""
	}
	return strings.ToLower(u.Path[pos+1 : len(u.Path)])
}

func FormatOriginURL(originURL string) string {
	if strings.HasPrefix(originURL, "//") {
		originURL = "https:" + originURL
	}
	if strings.HasPrefix(originURL, "http:") {
		originURL = "https:" + originURL[5:]
	}
	return originURL
}

func IsSalesChannelURL(url string, domainRegExps []*regexp.Regexp) (string, bool) {
	if len(domainRegExps) == 0 {
		domainRegExps = defaultCDNDomainRegExps
	}

	for _, reg := range domainRegExps {
		matches := reg.FindStringSubmatch(url)
		if len(matches) > 1 {
			return matches[1], true
		}
	}

	return "", false
}

func IsVideoURL(url string) bool {
	fileName := path.Base(url)
	extension := path.Ext(fileName)
	prefix := strings.TrimPrefix(extension, ".")
	return prefix == "mp4" || prefix == "mov" || prefix == "avi" || prefix == "flv" || prefix == "wmv" || prefix == "3gp"
}
