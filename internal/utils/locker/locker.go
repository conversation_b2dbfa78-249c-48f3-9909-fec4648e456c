package locker

import (
	"context"
	"fmt"
	"time"

	redis "github.com/go-redis/redis/v8"

	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
)

const deleteScript = `
	if redis.call("GET", KEYS[1]) == ARGV[1] then
		return redis.call("DEL", KEYS[1])
	else
		return 0
	end
`

const lockScript = `
    -- Get sync lock first
	local syncLock = tonumber(redis.call("GET", KEYS[1]))
	-- If the sync lock exist, return 0, caller may retry
	if syncLock ~= nil then
	    return 0
	end
	-- If sync lock not exist, check version lock
	local version = tonumber(ARGV[1])
	local origin_version = tonumber(redis.call("GET", KEYS[2]))
	if origin_version == nil then
	        -- sync lock
			redis.call("SET", KEYS[1], ARGV[1], "EX", ARGV[2])
			-- version lock
			redis.call("SET", KEYS[2], ARGV[1], "EX", ARGV[3])
			return 1
	elseif origin_version <= version then
		    -- sync lock
    		redis.call("SET", KEYS[1], ARGV[1], "EX", ARGV[2])
			-- update version lock
			redis.call("SET", KEYS[2], ARGV[1], "EX", ARGV[3])
			return 2
	else
	        -- If version is old, should abandon
            redis.call("EXPIRE", KEYS[2], ARGV[3])
			return -1
	end
`

type VersionLocker struct {
	ctx           context.Context
	redisClient   *redis.Client
	key           string
	value         int64
	expiry        time.Duration
	versionExpiry time.Duration
}

func NewVersionLocker(ctx context.Context, redisClient *redis.Client, key string, version int64, expiry, versionExpiry time.Duration) *VersionLocker {
	return &VersionLocker{
		ctx:           ctx,
		redisClient:   redisClient,
		key:           key,
		value:         version,
		expiry:        expiry,
		versionExpiry: versionExpiry,
	}

}

func (m *VersionLocker) Lock() error {
	v, err := m.redisClient.Eval(m.ctx, lockScript, []string{m.key, fmt.Sprintf("%s-version", m.key)}, m.value, m.expiry.Seconds(), m.versionExpiry.Seconds()).Int()
	if err != nil {
		return models.ErrRedisLockFailed
	}
	switch v {
	case 0:
		return models.ErrAcquireLockConflict
	case 1, 2:
		return nil
	case -1:
		return models.ErrVersionConflict
	default:
		return models.ErrInternal
	}
}

func (m *VersionLocker) Unlock() error {
	return m.redisClient.Eval(m.ctx, deleteScript, []string{m.key}, m.value).Err()
}
