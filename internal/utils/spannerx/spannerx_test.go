package spannerx

import (
	"testing"

	"cloud.google.com/go/spanner"
	"github.com/stretchr/testify/require"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

func TestIsAlreadyExists(t *testing.T) {
	tests := []struct {
		name     string
		err      error
		expected bool
	}{
		{
			name:     "AlreadyExists error",
			err:      spanner.ToSpannerError(status.Error(codes.AlreadyExists, "record exists")),
			expected: true,
		},
		{
			name:     "Other error",
			err:      spanner.ToSpannerError(status.Error(codes.FailedPrecondition, "other error	")),
			expected: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			require.Equal(t, tt.expected, IsAlreadyExists(tt.err))
		})
	}
}

func TestParseColumns(t *testing.T) {
	tests := []struct {
		name      string
		model     any
		expected  []string
		expectErr bool
	}{
		{
			name: "Spanner tag model",
			model: struct {
				Name string `spanner:"name"`
			}{},
			expected: []string{"name"},
		},
		{
			name:     "Empty model",
			model:    struct{ Name string }{},
			expected: []string{"Name"},
		},
		{
			name:      "Invalid model",
			model:     "invalid",
			expectErr: true,
		},
		{
			name:      "Nil model",
			model:     nil,
			expectErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			columns, err := ParseColumns(tt.model)
			if tt.expectErr {
				require.Error(t, err)
			} else {
				require.NoError(t, err)
			}
			require.Equal(t, tt.expected, columns)
		})
	}
}
