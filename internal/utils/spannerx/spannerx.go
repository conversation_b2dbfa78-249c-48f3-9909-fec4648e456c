package spannerx

import (
	"errors"
	"reflect"

	"cloud.google.com/go/spanner"
	"google.golang.org/grpc/codes"
)

var (
	ErrInvalidModel = errors.New("model cannot be nil, it only accepts a struct or a pointer to a struct")
)

func IsAlreadyExists(err error) bool {
	return spanner.ErrCode(err) == codes.AlreadyExists
}

func IsNotFoundErr(err error) bool {
	return spanner.ErrCode(err) == codes.NotFound
}

func ParseColumns(model any) ([]string, error) {
	if model == nil {
		return nil, ErrInvalidModel
	}

	modelType := reflect.TypeOf(model)
	switch modelType.Kind() {
	case reflect.Ptr:
		modelType = modelType.Elem()
	case reflect.Struct:
	default:
		return nil, ErrInvalidModel
	}

	fieldsNum := modelType.NumField()
	columns := make([]string, 0, fieldsNum)
	for i := 0; i < fieldsNum; i++ {
		field := modelType.Field(i)

		// If the column tag is empty, then uses the field name instead
		column := field.Tag.Get("spanner")
		if column == "" {
			column = field.Name
		}
		columns = append(columns, column)
	}

	return columns, nil
}
