package download_client

import (
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func TestIsURLSafe(t *testing.T) {
	type args struct {
		URL string
	}
	tests := []struct {
		name   string
		args   args
		hasErr bool
	}{
		// TODO: Add test cases.

		{
			name:   "1",
			args:   args{URL: "https://baidu.com"},
			hasErr: false,
		},
		{
			name:   "2",
			args:   args{URL: "https://169.254.169.254"},
			hasErr: true,
		},
		{
			name:   "4",
			args:   args{URL: "https://fe80::8080/who"},
			hasErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := IsURLSafe(tt.args.URL)
			fmt.Println("err", err)
			hasErr := err != nil
			assert.Equal(t, hasErr, tt.hasErr)
		})
	}
}

func TestNewDownloadClient(t *testing.T) {
	client := NewDownloadClient()

	assert.NotNil(t, client)

	// Check if the client has the expected timeout
	assert.Equal(t, 25*time.Second, client.GetClient().Timeout)

	// Check if the client has the expected redirect policy
	assert.NotNil(t, client.GetClient().CheckRedirect)
}

func TestIsURL(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected bool
	}{
		{
			name:     "Valid URL with https",
			input:    "https://example.com",
			expected: true,
		},
		{
			name:     "Valid URL with http",
			input:    "http://example.com",
			expected: true,
		},
		{
			name:     "Valid URL without protocol",
			input:    "example.com",
			expected: true,
		},
		{
			name:     "Invalid URL with spaces",
			input:    "http://example .com",
			expected: false,
		},
		{
			name:     "Empty string",
			input:    "",
			expected: false,
		},
		{
			name:     "invalid URL",
			input:    "?width=790&amp;height=1870&amp;hash=2660",
			expected: false,
		},
		{
			name:     "valid URL",
			input:    "https://p19-oec-ttp.tiktokcdn-us.com/tos-useast5-i-omjb5zjo8w-tx/c50d80f83d424edd9444868e045c2320~tplv-omjb5zjo8w-origin-jpeg.jpeg?from=1432613627",
			expected: true,
		},
		{
			name:     "valid URL",
			input:    "https://cdn.shopify.com/s/files/1/0655/0922/8702/files/d58b927c-aaf2-4b1d-9ab2-6cb8eb73951c_430x.jpg?v=1724144963#",
			expected: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := IsURL(tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}
}
