package download_client

import (
	"net"
	"net/http"
	"net/url"
	"regexp"
	"strings"
	"time"

	"github.com/go-resty/resty/v2"
	"github.com/pkg/errors"
	"go.uber.org/zap"

	standard_error "github.com/AfterShip/connectors-errors-sdk-go"
	gopkg_client "github.com/AfterShip/gopkg/api/client"
	"github.com/AfterShip/gopkg/log"
)

// IsURLSafe
// 内网地址不安全
// 只接收 https

func IsURLSafe(targetURL string) (err error) {
	parsed, err := url.Parse(targetURL)
	if err != nil {
		return
	}

	ips, _ := net.LookupIP(parsed.Host)
	for _, ip := range ips {
		if ip.IsPrivate() {
			// err = errors.WithStack(errors.New("private ip not allowed"))
			err = standard_error.WFPTTSDownloadClientCheckURLPrivateIP_603422733
			return
		}

		//  *************** gcp meta 地址
		if ip.IsLinkLocalUnicast() {
			// err = errors.WithStack(errors.New("local cast url not allowed"))
			err = standard_error.WFPTTSDownloadClientCheckURLLocalCastUrl_603422734
			return
		}
	}

	ipAddress := net.ParseIP(parsed.Host)

	if ipAddress.IsPrivate() {
		// err = errors.WithStack(errors.New("private ip not allowed"))
		err = standard_error.WFPTTSDownloadClientCheckURLPrivateIP_603422733
		return
	}

	//  *************** gcp meta 地址
	if ipAddress.IsLinkLocalUnicast() {
		// err = errors.WithStack(errors.New("local cast url not allowed"))
		err = standard_error.WFPTTSDownloadClientCheckURLLocalCastUrl_603422734
		return
	}

	if parsed.Scheme != "https" {
		err = errors.WithStack(ErrorOnlyAllowHttps)
		return
	}

	return
}

// NewDownloadClient 初始化一个 http client, 需要预防 SSRF 攻击
func NewDownloadClient() *gopkg_client.Client {
	clientCfg := gopkg_client.NewConfig(gopkg_client.WithCheckRedirect(func(req *http.Request, via []*http.Request) error {
		if len(via) >= 10 {
			return errors.New("stopped after 10 redirects")
		}
		if err := IsURLSafe(req.URL.String()); err != nil {
			return http.ErrUseLastResponse
		}
		return nil
	}))
	clientCfg.EnabledMetric = false
	clientCfg.EnabledTrace = false
	httpClient := gopkg_client.New(clientCfg)
	httpClient.SetTimeout(25 * time.Second)

	httpClient.SetRetryCount(3)
	httpClient.SetRetryWaitTime(2 * time.Second)
	httpClient.OnBeforeRequest(func(c *resty.Client, request *resty.Request) error {
		return IsURLSafe(request.URL)
	})
	httpClient.AddRetryCondition(
		func(r *resty.Response, err error) bool {
			if r == nil {
				return false
			}

			if err != nil && !errors.Is(err, ErrorOnlyAllowHttps) && !strings.Contains(err.Error(), "timeout") {
				log.GlobalLogger().Warn("error from image platform service",
					zap.String("url", gopkg_client.RedactURL(r.Request.URL)),
					zap.Int("attempt", r.Request.Attempt),
					zap.Error(err))
				return true
			}

			if r.StatusCode() != 200 {
				log.GlobalLogger().Info("image download retry",
					zap.String("url", gopkg_client.RedactURL(r.Request.URL)),
					zap.Int("attempt", r.Request.Attempt),
					zap.Int("code", r.StatusCode()),
					zap.Error(err),
				)
			}

			if r.StatusCode() >= http.StatusInternalServerError {
				return true
			}
			return r.StatusCode() == http.StatusTooManyRequests
		},
	)
	return httpClient
}

func IsURL(str string) bool {
	urlRegex := regexp.MustCompile(`^(http:\/\/|https:\/\/)?[^\s$.?#].[^\s]*$`)
	return urlRegex.MatchString(str)
}
