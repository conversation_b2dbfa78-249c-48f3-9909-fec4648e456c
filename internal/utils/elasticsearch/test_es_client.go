package elasticsearch

import (
	"context"
	"net/http"
	"os"
	"path/filepath"
	"runtime"

	"github.com/olivere/elastic/v7"
	"github.com/pkg/errors"
	"github.com/spf13/viper"

	"github.com/AfterShip/gopkg/cfg"

	"github.com/AfterShip/pltf-pd-product-listings/internal/config"
	"github.com/AfterShip/pltf-pd-product-listings/internal/datastore"
)

// called by unit test only
func CreateTestIndexWithAlias(cli *elastic.Client, indexName, aliasName, mappingFile string) error {
	ctx := context.Background()

	// 保持测试数据干净，先删除再创建
	exists, err := cli.IndexExists(indexName).Do(ctx)
	if err != nil {
		return err
	}
	if exists {
		if err := DeleteTestIndex(cli, indexName); err != nil {
			return err
		}
	}

	// 读取 mapping
	mappingBytes, err := loadMappingFile(mappingFile)
	if err != nil {
		return err
	}

	// 创建索引
	createdIndex, err := cli.CreateIndex(indexName).Body(string(mappingBytes)).Do(ctx)
	if err != nil {
		return err
	}
	if !createdIndex.Acknowledged {
		return errors.New("create index not acknowledged")
	}

	if aliasName != "" {
		aliasResult, err := cli.Alias().Add(indexName, aliasName).Do(ctx)
		if err != nil {
			return err
		}
		if !aliasResult.Acknowledged {
			return errors.New("create alias not acknowledged")
		}
	}

	return nil
}

func loadMappingFile(mappingFile string) ([]byte, error) {
	var mappingBytes []byte
	_, file, _, ok := runtime.Caller(0)
	if !ok {
		return mappingBytes, errors.New("can not to recover the information")
	}
	curPath := filepath.Dir(file)
	mappingDir := curPath + "/../../../scripts/elasticsearch/mappings/"
	mappingBytes, err := os.ReadFile(mappingDir + mappingFile)
	if err != nil {
		return mappingBytes, err
	}
	return mappingBytes, nil
}

func DeleteTestIndex(cli *elastic.Client, indexName string) error {
	deleteIndex, err := cli.DeleteIndex(indexName).Do(context.Background())
	if err != nil {
		return err
	}
	if !deleteIndex.Acknowledged {
		return errors.New("delete index not acknowledged")
	}
	return nil
}

func BuildTestESClient() (*elastic.Client, error) {
	configs := new(config.Config)
	_, err := cfg.LoadViperConfig(configs, func(v *viper.Viper) { v.AddConfigPath("../../../cmd/apiserver/conf") })
	if err != nil {
		return nil, err
	}

	configs.DynamicConfigs.ElasticsearchAuth = &config.ElasticsearchAuthConfig{
		Host: "http://localhost:9200",
	}

	err = datastore.Init(configs)
	if err != nil {
		return nil, err
	}

	esCli := datastore.Get().ESClient
	res, num, err := esCli.Ping("http://localhost:9200").Do(context.Background())
	if err != nil {
		return nil, err
	}

	if num != http.StatusOK {
		return nil, errors.New("ping elasticsearch failed")
	}

	if res == nil {
		return nil, errors.New("ping elasticsearch response is nil")
	}

	return esCli, nil
}
