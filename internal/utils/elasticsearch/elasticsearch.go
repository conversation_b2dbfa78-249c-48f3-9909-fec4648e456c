package elasticsearch

import (
	"encoding/base64"
	"encoding/json"

	"github.com/olivere/elastic/v7"

	"github.com/AfterShip/pltf-pd-product-listings/internal/consts"
)

func ParseCursor(cursor string) ([]interface{}, error) {
	// begin search after query
	// https://www.notion.so/automizely/PAC-03-Request-1-beginning-c66cc21fb8cf4404a8cb95b7a35d8927
	if cursor == consts.BeginningCursor || cursor == "" {
		return []interface{}{}, nil
	}

	// 0 and other inputs will return error
	decodedCursor, err := base64.StdEncoding.DecodeString(cursor)
	if err != nil {
		return []interface{}{}, err
	}

	var parsedCursor []interface{}
	err = json.Unmarshal([]byte(decodedCursor), &parsedCursor)
	if err != nil {
		return []interface{}{}, err
	}
	return parsedCursor, nil
}

func BuildNextCursor(esSortResult []interface{}) (string, error) {
	cursor, err := json.<PERSON>(esSortResult)
	if err != nil {
		return "", err
	}
	return base64.StdEncoding.EncodeToString(cursor), nil
}

func BuildIsNullQuery(query *elastic.BoolQuery, field string) {
	if query == nil || field == "" {
		return
	}
	query.Filter(elastic.NewBoolQuery().MustNot(elastic.NewExistsQuery(field)))
}

func BuildTermQuery(query *elastic.BoolQuery, field, value string) {
	if query == nil || field == "" || value == "" {
		return
	}
	query.Filter(elastic.NewTermQuery(field, value))
}

func BuildTermsQuery(query *elastic.BoolQuery, field string, values []string) {
	if query == nil || field == "" || len(values) == 0 {
		return
	}

	termsValues := make([]interface{}, len(values))
	for i, v := range values {
		termsValues[i] = v
	}

	query.Filter(elastic.NewTermsQuery(field, termsValues...))
}
