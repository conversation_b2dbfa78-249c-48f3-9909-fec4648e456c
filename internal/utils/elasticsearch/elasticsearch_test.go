package elasticsearch

import (
	"encoding/base64"
	"math"
	"reflect"
	"testing"

	"github.com/olivere/elastic/v7"
	"github.com/stretchr/testify/require"

	"github.com/AfterShip/pltf-pd-product-listings/internal/consts"
)

func Test_ParseCursor(t *testing.T) {
	tests := []struct {
		name        string
		cursor      string
		resultCheck func([]interface{}, error)
	}{
		{
			name:   "Beginning cursor",
			cursor: consts.BeginningCursor,
			resultCheck: func(i []interface{}, e error) {
				require.Nil(t, e)
				require.Empty(t, i)
			},
		},
		{
			name:   "Empty cursor",
			cursor: "",
			resultCheck: func(i []interface{}, e error) {
				require.Nil(t, e)
				require.Empty(t, i)
			},
		},
		{
			name:   "Invalid cursor",
			cursor: "invalid",
			resultCheck: func(i []interface{}, e error) {
				require.NotNil(t, e)
				require.ErrorIs(t, e, base64.CorruptInputError(4))
				require.Empty(t, i)
			},
		},
		{
			name:   "Invalid parsedCursor",
			cursor: "MQ==",
			resultCheck: func(i []interface{}, e error) {
				require.NotNil(t, e)
			},
		},
		{
			name:   "Valid cursor",
			cursor: "WzEsMiwzXQ==", // [1, 2, 3]
			resultCheck: func(i []interface{}, e error) {
				require.Nil(t, e)
				require.Equal(t, 3, len(i))
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			i, e := ParseCursor(tt.cursor)
			tt.resultCheck(i, e)
		})
	}
}

func Test_BuildNextCursor(t *testing.T) {
	tests := []struct {
		name         string
		esSortResult []interface{}
		resultCheck  func(string, error)
	}{
		{
			name:         "Empty esSortResult",
			esSortResult: []interface{}{},
			resultCheck: func(s string, e error) {
				require.Nil(t, e)
				require.NotEmpty(t, s)
				require.Equal(t, "W10=", s)
			},
		},
		{
			name:         "Nil esSortResult",
			esSortResult: nil,
			resultCheck: func(s string, e error) {
				require.Nil(t, e)
				require.NotEmpty(t, s)
			},
		},
		{
			name:         "Error json.Marshal",
			esSortResult: []interface{}{math.Inf(1)},
			resultCheck: func(s string, e error) {
				require.NotNil(t, e)
			},
		},
		{
			name:         "Error json.Marshal",
			esSortResult: []interface{}{make(chan int)},
			resultCheck: func(s string, e error) {
				require.NotNil(t, e)
			},
		},
		{
			name:         "Valid esSortResult",
			esSortResult: []interface{}{1, 2, 3},
			resultCheck: func(s string, e error) {
				require.Nil(t, e)
				require.NotEmpty(t, s)
				require.Equal(t, "WzEsMiwzXQ==", s)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s, e := BuildNextCursor(tt.esSortResult)
			tt.resultCheck(s, e)
		})
	}
}

func Test_BuildIsNullQuery(t *testing.T) {
	tests := []struct {
		name        string
		query       *elastic.BoolQuery
		fieldName   string
		resultCheck func(*elastic.BoolQuery)
	}{
		{
			name:      "Nil query",
			query:     nil,
			fieldName: "test",
			resultCheck: func(q *elastic.BoolQuery) {
				require.Nil(t, q)
			},
		},
		{
			name:      "Empty field name",
			query:     elastic.NewBoolQuery(),
			fieldName: "",
			resultCheck: func(q *elastic.BoolQuery) {
				qValue := reflect.ValueOf(*q)
				require.Equal(t, 0, qValue.FieldByName("filterClauses").Len())
			},
		},
		{
			name:      "Exist field name",
			query:     elastic.NewBoolQuery(),
			fieldName: "filedName",
			resultCheck: func(q *elastic.BoolQuery) {
				qValue := reflect.ValueOf(*q)
				require.Equal(t, 1, qValue.FieldByName("filterClauses").Len())
			},
		},
		{
			name:      "Same field name",
			query:     elastic.NewBoolQuery().Filter(elastic.NewBoolQuery().MustNot(elastic.NewExistsQuery("filedName"))),
			fieldName: "filedName",
			resultCheck: func(q *elastic.BoolQuery) {
				qValue := reflect.ValueOf(*q)
				require.Equal(t, 2, qValue.FieldByName("filterClauses").Len())
			},
		},
		{
			name:      "Empty field name with exist query",
			query:     elastic.NewBoolQuery().Filter(elastic.NewBoolQuery().MustNot(elastic.NewExistsQuery("filedName"))),
			fieldName: "",
			resultCheck: func(q *elastic.BoolQuery) {
				qValue := reflect.ValueOf(*q)
				require.Equal(t, 1, qValue.FieldByName("filterClauses").Len())
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			BuildIsNullQuery(tt.query, tt.fieldName)
			tt.resultCheck(tt.query)
		})
	}
}

func Test_BuildTermQuery(t *testing.T) {
	tests := []struct {
		name        string
		query       *elastic.BoolQuery
		fieldName   string
		value       string
		resultCheck func(*elastic.BoolQuery)
	}{
		{
			name:      "Nil query",
			query:     nil,
			fieldName: "test",
			value:     "test",
			resultCheck: func(q *elastic.BoolQuery) {
				require.Nil(t, q)
			},
		},
		{
			name:      "Empty field name",
			query:     elastic.NewBoolQuery(),
			fieldName: "",
			value:     "test",
			resultCheck: func(q *elastic.BoolQuery) {
				qValue := reflect.ValueOf(*q)
				require.Equal(t, 0, qValue.FieldByName("filterClauses").Len())
			},
		},
		{
			name:      "Empty value",
			query:     elastic.NewBoolQuery(),
			fieldName: "filedName",
			value:     "",
			resultCheck: func(q *elastic.BoolQuery) {
				qValue := reflect.ValueOf(*q)
				require.Equal(t, 0, qValue.FieldByName("filterClauses").Len())
			},
		},
		{
			name:      "Exist field name and value",
			query:     elastic.NewBoolQuery(),
			fieldName: "filedName",
			value:     "test",
			resultCheck: func(q *elastic.BoolQuery) {
				qValue := reflect.ValueOf(*q)
				require.Equal(t, 1, qValue.FieldByName("filterClauses").Len())
			},
		},
		{
			name:      "Same field name and value",
			query:     elastic.NewBoolQuery().Filter(elastic.NewTermQuery("filedName", "value")),
			fieldName: "filedName",
			value:     "value",
			resultCheck: func(q *elastic.BoolQuery) {
				qValue := reflect.ValueOf(*q)
				require.Equal(t, 2, qValue.FieldByName("filterClauses").Len())
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			BuildTermQuery(tt.query, tt.fieldName, tt.value)
			tt.resultCheck(tt.query)
		})
	}
}

func Test_BuildTermsQuery(t *testing.T) {
	tests := []struct {
		name        string
		query       *elastic.BoolQuery
		fieldName   string
		values      []string
		resultCheck func(*elastic.BoolQuery)
	}{
		{
			name:      "Nil query",
			query:     nil,
			fieldName: "test",
			values:    []string{"test"},
			resultCheck: func(q *elastic.BoolQuery) {
				require.Nil(t, q)
			},
		},
		{
			name:      "Empty field name",
			query:     elastic.NewBoolQuery(),
			fieldName: "",
			values:    []string{"test"},
			resultCheck: func(q *elastic.BoolQuery) {
				qValue := reflect.ValueOf(*q)
				require.Equal(t, 0, qValue.FieldByName("filterClauses").Len())
			},
		},
		{
			name:      "Empty values",
			query:     elastic.NewBoolQuery(),
			fieldName: "filedName",
			values:    []string{},
			resultCheck: func(q *elastic.BoolQuery) {
				qValue := reflect.ValueOf(*q)
				require.Equal(t, 0, qValue.FieldByName("filterClauses").Len())
			},
		},
		{
			name:      "Exist field name and values",
			query:     elastic.NewBoolQuery(),
			fieldName: "filedName",
			values:    []string{"test"},
			resultCheck: func(q *elastic.BoolQuery) {
				qValue := reflect.ValueOf(*q)
				require.Equal(t, 1, qValue.FieldByName("filterClauses").Len())
			},
		},
		{
			name:      "Same field name and values",
			query:     elastic.NewBoolQuery().Filter(elastic.NewTermsQuery("filedName", "value")),
			fieldName: "filedName",
			values:    []string{"value"},
			resultCheck: func(q *elastic.BoolQuery) {
				qValue := reflect.ValueOf(*q)
				require.Equal(t, 2, qValue.FieldByName("filterClauses").Len())
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			BuildTermsQuery(tt.query, tt.fieldName, tt.values)
			tt.resultCheck(tt.query)
		})
	}
}
