package toolbox

import (
	"testing"

	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
)

func Test_CalculateAvailableQuantity(t *testing.T) {
	tests := []struct {
		name             string
		quantity         float64
		inventorySync    *models.InventorySync
		expectedQuantity float64
		expectedBool     bool
	}{
		{
			name:     "not hit threshold",
			quantity: 10.0,
			inventorySync: &models.InventorySync{
				AutoSync:                 "enabled",
				AvailableQuantityPercent: 0.5,
				LowQuantityThreshold: models.LowQuantityThreshold{
					State: "enabled",
					Value: 5.0,
				},
			},
			expectedQuantity: 5.0,   // Replace with expected value
			expectedBool:     false, // Replace with expected value
		},
		{
			name:     "hit threshold",
			quantity: 2.0,
			inventorySync: &models.InventorySync{
				AutoSync:                 "enabled",
				AvailableQuantityPercent: 0.5,
				LowQuantityThreshold: models.LowQuantityThreshold{
					State: "enabled",
					Value: 5.0,
				},
			},
			expectedQuantity: 0,
			expectedBool:     true,
		},
		{
			name:     "not hit threshold,use percent",
			quantity: 10.0,
			inventorySync: &models.InventorySync{
				AutoSync:                 "enabled",
				AvailableQuantityPercent: 0.5,
				LowQuantityThreshold: models.LowQuantityThreshold{
					State: "enabled",
					Value: 5.0,
				},
			},
			expectedQuantity: 10 * 0.5,
			expectedBool:     false,
		},
		{
			name:     "hit threshold,use percent",
			quantity: 2.0,
			inventorySync: &models.InventorySync{
				AutoSync:                 "enabled",
				AvailableQuantityPercent: 0.5,
				LowQuantityThreshold: models.LowQuantityThreshold{
					State: "enabled",
					Value: 5.0,
				},
			},
			expectedQuantity: 0,
			expectedBool:     true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			resultQuantity, resultBool := CalculateAvailableQuantity(tt.quantity, tt.inventorySync)
			if resultQuantity != tt.expectedQuantity || resultBool != tt.expectedBool {
				t.Errorf("CalculateAvailableQuantity(%v, %v) = %v, %v; want %v, %v", tt.quantity, tt.inventorySync, resultQuantity, resultBool, tt.expectedQuantity, tt.expectedBool)
			}
		})
	}
}
