package toolbox

import (
	"math"

	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
)

func CalculateAvailableQuantity(quantity float64, inventorySync *models.InventorySync) (float64, bool) {
	// 仅提供计算，不关注是否开启，业务费自己决定
	if inventorySync == nil {
		return quantity, false
	}
	// 低库存控制
	if inventorySync.LowQuantityThreshold.Enabled() {
		if quantity <= inventorySync.LowQuantityThreshold.Value {
			return 0, true
		}
	}

	// 四舍五入
	tmpQuantity := quantity * inventorySync.AvailableQuantityPercent
	return math.Round(tmpQuantity), false
}
