package toolbox

import "testing"

func TestBarcodeType(t *testing.T) {
	tests := []struct {
		name     string
		barcode  string
		expected string
	}{
		{
			name:     "Valid EAN-8",
			barcode:  "12345678",
			expected: EAN,
		},
		{
			name:     "Valid UPC",
			barcode:  "123456789012",
			expected: UPC,
		},
		{
			name:     "Valid EAN-13",
			barcode:  "1234567890123",
			expected: EAN,
		},
		{
			name:     "Valid GTIN",
			barcode:  "12345678901234",
			expected: GTIN,
		},
		{
			name:     "Invalid non-numeric",
			barcode:  "abc123",
			expected: "",
		},
		{
			name:     "Invalid length",
			barcode:  "12345",
			expected: "",
		},
		{
			name:     "Empty barcode",
			barcode:  "",
			expected: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := BarcodeType(tt.barcode)
			if result != tt.expected {
				t.<PERSON><PERSON>("BarcodeType(%v) = %v, want %v", tt.barcode, result, tt.expected)
			}
		})
	}
}
