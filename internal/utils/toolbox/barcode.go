package toolbox

import "strconv"

const (
	GTIN = "gtin"
	EAN  = "ean"
	UPC  = "upc"
	ISBN = "isbn"
)

// BarcodeToProductIdentifier Identifier code logic：
//  1. Must be a numeric type.
//  2. The number of characters needs to meet the requirements (GTIN: 14 digits, EAN: 8/13/14 digits, UPC: 12 digits, ISBN: 13 digits)
//  3. Different SKUs are not allowed to use the same gtin code.
func BarcodeType(barcode string) string {
	if barcode == "" {
		return ""
	}

	if _, err := strconv.Atoi(barcode); err != nil {
		return ""
	}
	switch len(barcode) {
	case 8:
		return EAN
	case 12:
		return UPC
	case 13:
		// Indistinguishable EAN,ISBN(both 13 digits)
		return EAN
	case 14:
		// Indistinguishable GTIN,EAN(both 14 digits)
		return GTIN
	default:
		return ""
	}
}
