package toolbox

import (
	"strings"

	"github.com/PuerkitoBio/goquery"
	"go.uber.org/zap"
	"jaytaylor.com/html2text"

	"github.com/AfterShip/pltf-pd-product-listings/internal/logger"
)

func HtmlToRichText(inputHtml string) (string, error) {
	text, err := html2text.FromString(inputHtml, html2text.Options{PrettyTables: true, OmitLinks: true})
	if err != nil {
		logger.Get().Info("failed to convert html to rich text", zap.String("input", inputHtml), zap.Error(err))
		if text, err = htmlToSimpleText(inputHtml); err != nil {
			logger.Get().Warn("failed to convert html to simple text", zap.String("input", inputHtml), zap.Error(err))
			return "", err
		}
	}
	return text, nil
}

func htmlToSimpleText(inputHtml string) (string, error) {
	doc, err := goquery.NewDocumentFromReader(strings.NewReader(inputHtml))
	if err != nil {
		return "", err
	}
	text := doc.Text()
	return text, nil
}
