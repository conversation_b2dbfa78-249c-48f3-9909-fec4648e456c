package toolbox

import (
	"strconv"

	"github.com/pkg/errors"
	"github.com/shopspring/decimal"

	"github.com/AfterShip/pltf-pd-product-listings/internal/consts"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
)

// nolint:funlen
func CalculateSalesPrice(sourceSalePrice, sourceCompareAtPrice decimal.Decimal,
	priceSync *models.PriceSync) (decimal.Decimal, decimal.Decimal, error) {
	/**
	1.先判断使用哪个价格
	2.最后进行价格浮动调整
	*/

	// 无 priceSync，默认使用 sale price
	if priceSync == nil {
		return sourceSalePrice, sourceCompareAtPrice, nil
	}

	// 根据 price rule 决定使用哪个价格
	updatingSalePrice := lookUpUsePrice(sourceSalePrice, sourceCompareAtPrice, priceSync)
	// 默认 compareAtPrice 为 0
	updatingCompareAtPrice := decimal.NewFromInt(0)
	// 开启了划线价同步，就要记录原始划线价
	if priceSync.SyncCompareAtPrice == consts.StateEnabled {
		updatingCompareAtPrice = sourceCompareAtPrice
	}

	if len(priceSync.Rules) == 0 {
		return updatingSalePrice, updatingCompareAtPrice, nil
	}

	rule := priceSync.Rules[0]
	var err error
	var priceAfterDiscount decimal.Decimal
	switch rule.ValueType {
	case consts.PriceSyncRulesUseFixedAmount:
		priceAfterDiscount, err = buildPriceWithFixedAmount(updatingSalePrice, rule)

	case consts.PriceSyncUsePercentage:
		priceAfterDiscount, err = buildPriceWithPercentage(updatingSalePrice, rule)
	default:
		err = errors.New("unsupported price rule")
	}
	if err != nil {
		return updatingSalePrice, sourceCompareAtPrice, err
	}

	return priceAfterDiscount, updatingCompareAtPrice, nil
}

func lookUpUsePrice(sourceSalePrice, sourceCompareAtPrice decimal.Decimal,
	priceSync *models.PriceSync) decimal.Decimal {
	var usePrice decimal.Decimal
	if priceSync.SourceField == consts.PriceSyncSourceFieldCompareAtPrice {
		usePrice = sourceCompareAtPrice
	} else {
		usePrice = sourceSalePrice
	}
	return usePrice
}

func buildPriceWithFixedAmount(inputPrice decimal.Decimal, rule models.PriceRules) (decimal.Decimal, error) {
	floatValue, err := strconv.ParseFloat(rule.Value, 64)
	if err != nil {
		return decimal.Decimal{}, errors.WithStack(err)
	}
	if floatValue < 0 {
		return decimal.Decimal{}, errors.WithStack(errors.New("decreasing a fixed amount is not supported"))
	}
	floatValueDecimal := decimal.NewFromFloat(floatValue)
	return inputPrice.Add(floatValueDecimal), nil
}

func buildPriceWithPercentage(inputPrice decimal.Decimal, rule models.PriceRules) (decimal.Decimal, error) {
	percentage, err := strconv.ParseFloat(rule.Value, 64)
	if err != nil {
		return decimal.Decimal{}, errors.WithStack(err)
	}
	discount := false
	if percentage < 0 {
		discount = true
		// 纠正成正数
		percentage = -percentage
	}
	// 产品价格要比原价高 x%? 产品价格要比原价低 x%?
	if percentage == 0 {
		return inputPrice, nil
	}
	// 99% 阈值折扣阈值
	if discount && percentage > 99 {
		// 不支持 100% 折扣，折扣最高 99%
		return decimal.Decimal{}, errors.WithStack(errors.New("discount percentage exceeds threshold"))
	}
	percentageDecimal := decimal.NewFromFloat(percentage * 0.01)
	// 得到了需要增加或者减少的金额
	discountPrice := inputPrice.Mul(percentageDecimal)
	var outputPrice decimal.Decimal
	if discount {
		// 折扣，需要减少金额
		outputPrice = inputPrice.Sub(discountPrice)
	} else {
		// 增加，需要加上金额
		outputPrice = inputPrice.Add(discountPrice)
	}
	return outputPrice, nil
}
