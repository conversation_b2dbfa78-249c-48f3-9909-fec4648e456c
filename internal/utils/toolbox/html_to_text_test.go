package toolbox

import (
	"log"
	"os"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestHtmlToRichText(t *testing.T) {
	tests := []struct {
		name        string
		inputHtml   string
		expected    string
		expectError bool
	}{
		{
			name:        "Valid HTML",
			inputHtml:   "<p>Hello, World!</p>",
			expected:    "Hello, World!",
			expectError: false,
		},
		{
			name:        "Empty HTML",
			inputHtml:   "",
			expected:    "",
			expectError: false,
		},
		{
			name:        "Invalid HTML",
			inputHtml:   "<p>Hello, World!",
			expected:    "Hello, World!",
			expectError: false,
		},
		{
			name:        "HTML with links",
			inputHtml:   `<a href="https://example.com">Example</a>`,
			expected:    "Example",
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := HtmlToRichText(tt.inputHtml)
			if tt.expectError {
				assert.Error(t, err)
			} else {
				assert.Equal(t, tt.expected, result)
			}
		})
	}

	inputHtml, err := os.ReadFile("./fixtures/html_input.txt")
	if err != nil {
		log.Fatal(err)
	}
	result, err := HtmlToRichText(string(inputHtml))
	if err != nil {
		log.Fatal(err)
	}
	wantResultTxt, err := os.ReadFile("./fixtures/html_to_text_result.txt")
	if err != nil {
		log.Fatal(err)
	}
	assert.Equal(t, string(wantResultTxt), result)
}
