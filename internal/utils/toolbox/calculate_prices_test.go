package toolbox

import (
	"testing"

	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/require"

	"github.com/AfterShip/pltf-pd-product-listings/internal/consts"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
)

func Test_CalculatePrice(t *testing.T) {
	tests := []struct {
		name                                  string
		sourceSalePrice, sourceCompareAtPrice decimal.Decimal
		priceSync                             *models.PriceSync
		wantPrice                             float64
		wantError                             bool
	}{
		{
			name:                 "above price using percentage",
			sourceSalePrice:      decimal.NewFromFloat(18.1),
			sourceCompareAtPrice: decimal.NewFromFloat(12.4),
			priceSync: &models.PriceSync{
				SourceField: consts.PriceSyncSourceFieldSalePrice,
				Rules: []models.PriceRules{
					{
						Value:     "90", // above 90%
						ValueType: consts.PriceSyncUsePercentage,
					},
				},
			},
			wantPrice: 18.1 * (1 + 0.9),
			wantError: false,
		},
		{
			name:                 "below price using percentage",
			sourceSalePrice:      decimal.NewFromFloat(18.1),
			sourceCompareAtPrice: decimal.NewFromFloat(12.4),
			priceSync: &models.PriceSync{
				SourceField: consts.PriceSyncSourceFieldSalePrice,
				Rules: []models.PriceRules{
					{
						Value:     "-10", // discount 10%
						ValueType: consts.PriceSyncUsePercentage,
					},
				},
			},
			wantPrice: 18.1 * (1 - 0.1),
			wantError: false,
		},
		{
			name:                 "above price using amount",
			sourceSalePrice:      decimal.NewFromFloat(18.1),
			sourceCompareAtPrice: decimal.NewFromFloat(12.4),
			priceSync: &models.PriceSync{
				SourceField: consts.PriceSyncSourceFieldSalePrice,
				Rules: []models.PriceRules{
					{
						Value:     "90", // above 90
						ValueType: consts.PriceSyncRulesUseFixedAmount,
					},
				},
			},
			wantPrice: 18.1 + 90,
			wantError: false,
		},
		{
			name:                 "below price using amount",
			sourceSalePrice:      decimal.NewFromFloat(18.1),
			sourceCompareAtPrice: decimal.NewFromFloat(12.4),
			priceSync: &models.PriceSync{
				SourceField: consts.PriceSyncSourceFieldSalePrice,
				Rules: []models.PriceRules{
					{
						Value:     "-10", // discount 10
						ValueType: consts.PriceSyncRulesUseFixedAmount,
					},
				},
			},
			wantPrice: 18.1 - 10,
			wantError: true, // 固定价格模式不可以允许减少
		},
		{
			name:                 "use compare at price,above price using percentage",
			sourceSalePrice:      decimal.NewFromFloat(18.1),
			sourceCompareAtPrice: decimal.NewFromFloat(12.4),
			priceSync: &models.PriceSync{
				SourceField: consts.PriceSyncSourceFieldCompareAtPrice,
				Rules: []models.PriceRules{
					{
						Value:     "90", // above 90%
						ValueType: consts.PriceSyncUsePercentage,
					},
				},
			},
			wantPrice: 12.4 * (1 + 0.9),
			wantError: false,
		},
		{
			name:                 "unsupported price rule",
			sourceSalePrice:      decimal.NewFromFloat(18.1),
			sourceCompareAtPrice: decimal.NewFromFloat(12.4),
			priceSync: &models.PriceSync{
				SourceField: consts.PriceSyncSourceFieldCompareAtPrice,
				Rules: []models.PriceRules{
					{
						Value:     "90", // above 90%
						ValueType: "unsupported",
					},
				},
			},
			wantPrice: 12.4 * (1 + 0.9),
			wantError: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			calculatedPrice, _, err := CalculateSalesPrice(tt.sourceSalePrice, tt.sourceCompareAtPrice, tt.priceSync)
			if tt.wantError {
				require.Error(t, err)
			} else {
				require.NoError(t, err)
				toFloat, _ := calculatedPrice.Float64()
				require.Equal(t, tt.wantPrice, toFloat)
			}
		})
	}
}
