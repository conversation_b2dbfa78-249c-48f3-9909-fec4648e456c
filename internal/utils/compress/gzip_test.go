package compress

import "testing"

func Test_GzipAndBase64Data(t *testing.T) {
	data := []byte("hello world")
	encoded, err := GzipAndBase64Data(data)
	if err != nil {
		t.Error(err)
	}
	if encoded != "H4sIAAAAAAAA/8pIzcnJVyjPL8pJAQQAAP//hRFKDQsAAAA=" {
		t.Error("encoded is empty")
	}
}

func Test_Base64GzipDecode(t *testing.T) {
	encoded := "H4sIAAAAAAAA/8pIzcnJVyjPL8pJAQQAAP//hRFKDQsAAAA="
	decoded, err := Base64GzipDecode([]byte(encoded))
	if err != nil {
		t.Error(err)
	}
	if string(decoded) != "hello world" {
		t.Error("decoded is empty")
	}
}
