package compress

import (
	"bytes"
	"compress/gzip"
	"encoding/base64"
	"io/ioutil"
)

func GzipAndBase64Data(data []byte) (string, error) {
	var buf bytes.Buffer
	zw := gzip.NewWriter(&buf)

	_, err := zw.Write(data)
	if err != nil {
		return "", err
	}
	if err := zw.Close(); err != nil {
		return "", err
	}
	//使用base64编码
	encoded := base64.StdEncoding.EncodeToString(buf.Bytes())
	return encoded, nil
}

func unGzip(data []byte) ([]byte, error) {
	buf := bytes.NewBuffer(data)
	r, err := gzip.NewReader(buf)
	if err != nil {
		return nil, err
	}

	result, err := ioutil.ReadAll(r)
	if err != nil {
		return nil, err
	}

	if err := r.Close(); err != nil {
		return nil, err
	}

	return result, nil
}

func Base64GzipDecode(data []byte) ([]byte, error) {
	gzipData, err := base64.StdEncoding.DecodeString(string(data))
	if err != nil {
		return nil, err
	}
	originData, err := unGzip(gzipData)
	if err != nil {
		return nil, err
	}

	return originData, nil
}
