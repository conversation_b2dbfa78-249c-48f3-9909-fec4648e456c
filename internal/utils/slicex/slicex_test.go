package slicex

import (
	"reflect"
	"testing"

	"github.com/AfterShip/gopkg/facility/types"
)

func Test_SplitSlice(t *testing.T) {
	tests := []struct {
		name      string
		data      []string
		chunkSize int
		expected  [][]string
	}{
		{
			name:      "Empty slice",
			data:      []string{},
			chunkSize: 2,
			expected:  [][]string{},
		},
		{
			name:      "Chunk size larger than slice",
			data:      []string{"a", "b"},
			chunkSize: 3,
			expected:  [][]string{{"a", "b"}},
		},
		{
			name:      "Chunk size smaller than slice",
			data:      []string{"a", "b", "c", "d", "e"},
			chunkSize: 2,
			expected:  [][]string{{"a", "b"}, {"c", "d"}, {"e"}},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := SplitSlice(tt.data, tt.chunkSize)
			if len(result) != len(tt.expected) {
				t.<PERSON><PERSON><PERSON>("Expected %v, got %v", tt.expected, result)
			}
			for i, chunk := range result {
				if len(chunk) != len(tt.expected[i]) {
					t.<PERSON><PERSON>("Expected %v, got %v", tt.expected[i], chunk)
				}
				for j, item := range chunk {
					if item != tt.expected[i][j] {
						t.Errorf("Expected %v, got %v", tt.expected[i][j], item)
					}
				}
			}
		})
	}
}

func Test_UniqueSlice(t *testing.T) {
	tests := []struct {
		name     string
		data     []string
		expected []string
	}{
		{
			name:     "No duplicates",
			data:     []string{"a", "b", "c"},
			expected: []string{"a", "b", "c"},
		},
		{
			name:     "With duplicates",
			data:     []string{"a", "b", "a", "c", "b"},
			expected: []string{"a", "b", "c"},
		},
		{
			name:     "All duplicates",
			data:     []string{"a", "a", "a", "a"},
			expected: []string{"a"},
		},
		{
			name:     "Empty slice",
			data:     []string{},
			expected: []string{},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := UniqueSlice(tt.data)
			if !reflect.DeepEqual(result, tt.expected) {
				t.Errorf("Expected %v, got %v", tt.expected, result)
			}
		})
	}
}

func TestRemoveEmptyStrings(t *testing.T) {
	type args struct {
		data []string
	}
	tests := []struct {
		name string
		args args
		want []string
	}{
		// TODO: Add test cases.
		{
			name: "No empty strings",
			args: args{data: []string{"a", "b", "c"}},
			want: []string{"a", "b", "c"},
		},
		{
			name: "Empty strings",
			args: args{data: []string{"a", "", "b", "", "c"}},
			want: []string{"a", "b", "c"},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := RemoveEmptyStrings(tt.args.data); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("RemoveEmptyStrings() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestTypeStringToString(t *testing.T) {
	tests := []struct {
		name     string
		input    []types.String
		expected []string
	}{
		{
			name:     "Empty slice",
			input:    []types.String{},
			expected: []string{},
		},
		{
			name:     "Single element",
			input:    []types.String{types.MakeString("a")},
			expected: []string{"a"},
		},
		{
			name:     "Multiple elements",
			input:    []types.String{types.MakeString("a"), types.MakeString("b"), types.MakeString("c")},
			expected: []string{"a", "b", "c"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := TypeStringToString(tt.input)
			if !reflect.DeepEqual(result, tt.expected) {
				t.Errorf("TypeStringToString() = %v, want %v", result, tt.expected)
			}
		})
	}
}
