package slicex

import (
	"github.com/AfterShip/gopkg/facility/types"
)

func SplitSlice(data []string, chunkSize int) [][]string {
	var chunks [][]string
	for i := 0; i < len(data); i += chunkSize {
		end := i + chunkSize
		if end > len(data) {
			end = len(data)
		}
		chunks = append(chunks, data[i:end])
	}
	return chunks
}

func UniqueSlice(data []string) []string {
	dMap := make(map[string]struct{})
	newData := make([]string, 0)
	for _, v := range data {
		if _, ok := dMap[v]; ok {
			continue
		}
		dMap[v] = struct{}{}
		newData = append(newData, v)
	}
	return newData
}

func ContainsString(data []string, target string) bool {
	for _, v := range data {
		if v == target {
			return true
		}
	}
	return false
}

func RemoveEmptyStrings(data []string) []string {
	result := make([]string, 0)

	for _, str := range data {
		if len(str) > 0 {
			result = append(result, str)
		}
	}

	return result
}

func TypeStringToString(input []types.String) []string {
	result := make([]string, 0, len(input))

	for _, str := range input {
		result = append(result, str.String())
	}

	return result
}
