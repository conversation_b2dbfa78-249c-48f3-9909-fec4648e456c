package datastore

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"strings"
	"time"

	"cloud.google.com/go/pubsub"
	validator "github.com/go-playground/validator/v10"
	"github.com/go-redis/redis/v8"
	"github.com/go-redsync/redsync/v4"
	"github.com/go-redsync/redsync/v4/redis/goredis/v8"
	"github.com/go-resty/resty/v2"
	"github.com/olivere/elastic/v7"
	"golang.org/x/time/rate"
	"google.golang.org/api/option"

	exporter "github.com/AfterShip/business-monitoring-events-exporter-sdk-go"
	sender_pubsub "github.com/AfterShip/business-monitoring-events-exporter-sdk-go/sender/pubsub"
	"github.com/AfterShip/connectors-library/httpx"
	"github.com/AfterShip/connectors-library/sdks/jobs"
	"github.com/AfterShip/connectors-library/sdks/products_center"
	platform_api_v2 "github.com/AfterShip/connectors-sdk-go/v2"
	feed_base_cli "github.com/AfterShip/feed-sdk-go/client"
	"github.com/AfterShip/gopkg/api/client"
	pkgcfg "github.com/AfterShip/gopkg/cfg"
	"github.com/AfterShip/gopkg/pubsubx"
	"github.com/AfterShip/gopkg/storage/elasticx"
	"github.com/AfterShip/gopkg/storage/spannerx"

	"github.com/AfterShip/pltf-pd-product-listings/internal/config"
	redisLogger "github.com/AfterShip/pltf-pd-product-listings/internal/logger/redis"
	restyLogger "github.com/AfterShip/pltf-pd-product-listings/internal/logger/resty"
	"github.com/AfterShip/pltf-pd-product-listings/internal/third_party/bme"
	"github.com/AfterShip/pltf-pd-product-listings/internal/third_party/feed"
)

var _dataStore *DataStore

type DataStore struct {
	SpannerCli   *spannerx.Client
	ESClient     *elastic.Client
	RedisClient  *redis.Client
	RedisLocker  *redsync.Redsync
	PubSubClient *pubsubx.Client
	ClientStore  *ClientStore
}

type ClientStore struct {
	CNTJobClient               *jobs.Client
	CNTClient                  *platform_api_v2.PlatformV2Client
	ProductsCenterClient       *products_center.Client
	BusinessMonitoringExporter *bme.Client
	FeedV1Client               *feed.ClientV1
	FeedV2Client               *feed.ClientV2
}

func Init(cfg *config.Config) error {
	ctx := context.Background()

	spannerCli, err := buildSpannerClient(ctx, cfg)
	if err != nil {
		return fmt.Errorf("build spanner client: %w", err)
	}

	esCli, err := buildESClient(cfg)
	if err != nil {
		return fmt.Errorf("build es client: %w", err)
	}

	redisClient, err := buildRedisClient(cfg)
	if err != nil {
		return fmt.Errorf("build redis client: %w", err)
	}

	pubsubClient, err := buildPubSubClient(cfg)
	if err != nil {
		return fmt.Errorf("create pubsub client: %w", err)
	}

	clientStore := buildClientStore(cfg)

	_dataStore = &DataStore{
		SpannerCli:   spannerCli,
		ESClient:     esCli,
		ClientStore:  clientStore,
		RedisClient:  redisClient,
		RedisLocker:  redsync.New(goredis.NewPool(redisClient)),
		PubSubClient: pubsubClient,
	}
	return nil
}

func Get() *DataStore {
	return _dataStore
}

func buildSpannerClient(ctx context.Context, cfg *config.Config) (cli *spannerx.Client, err error) {
	database := fmt.Sprintf("projects/%s/instances/%s/databases/%s",
		cfg.Spanner.Project, cfg.Spanner.Instance, cfg.Spanner.Database)

	if len(cfg.DynamicConfigs.GCPServiceAccount) > 0 {
		opt := option.WithCredentialsJSON([]byte(cfg.DynamicConfigs.GCPServiceAccount))
		cli, err = spannerx.NewClient(ctx, database, opt)
	} else {
		cli, err = spannerx.NewClient(ctx, database)
	}
	return
}

func buildESClient(cfg *config.Config) (*elastic.Client, error) {
	esConfig := new(elasticx.ClientConfig)

	esConfig.URLs = strings.Split(cfg.DynamicConfigs.ElasticsearchAuth.Host, ",")
	// use proxy, must set sniffer_enabled to false
	esConfig.SnifferEnabled = false
	esConfig.ClientTLS = &pkgcfg.ClientTLSConfig{
		CAFileBytes:   []byte(cfg.DynamicConfigs.ElasticsearchSSLCA),
		CertFileBytes: []byte(cfg.DynamicConfigs.ElasticsearchSSLCrt),
		KeyFileBytes:  []byte(cfg.DynamicConfigs.ElasticsearchSSLKey),
	}
	esConfig.HttpClient = client.DefaultConfig()
	esConfig.HttpClient.Timeout = 10 * time.Second
	esConfig.HttpClient.MaxConnsPerHost = 100

	esConfig.Username = cfg.DynamicConfigs.ElasticsearchAuth.User
	esConfig.Password = cfg.DynamicConfigs.ElasticsearchAuth.Password
	esConfig.HealthCheckInterval = 3 * time.Second

	return elasticx.NewClientFromConfig(esConfig)
}

func buildClientStore(cfg *config.Config) *ClientStore {
	cli := client.NewWithConfigOptions(client.WithMaxIdleConnsPerHost(100))
	token := os.Getenv("AM_API_KEY")
	options := []jobs.ClientOptions{
		jobs.WithJobsApiToken(token),
	}
	return &ClientStore{
		CNTJobClient:               jobs.NewClient(cli, options...),
		CNTClient:                  buildCNTClient(cfg),
		ProductsCenterClient:       products_center.NewClient(httpx.NewClient(cfg.ProductsCenterAPI.Url, token)),
		BusinessMonitoringExporter: initBusinessMonitoringExporter(cfg),
		FeedV1Client:               initFeedV1Client(cfg.FeedV1API.Url, token),
		FeedV2Client:               initFeedV2Client(cfg.FeedV2API.Url, token),
	}
}

func initFeedV1Client(url, token string) *feed.ClientV1 {
	restyClient := initRestyClient("Feed", token, 10, client.WithMaxIdleConnsPerHost(100))
	limiter := rate.NewLimiter(rate.Every(time.Millisecond), 1e5)
	validate := validator.New()
	return feed.NewClientV1(feed_base_cli.NewFeedClient(restyClient, url, limiter, validate))
}

func initFeedV2Client(url, token string) *feed.ClientV2 {
	restyClient := initRestyClient("Feed", token, 10, client.WithMaxIdleConnsPerHost(100))
	limiter := rate.NewLimiter(rate.Every(time.Millisecond), 1e5)
	validate := validator.New()
	return feed.NewClientV2(feed_base_cli.NewFeedClient(restyClient, url, limiter, validate))
}

func initRestyClient(clientName, token string, retryCount int, opts ...client.Option) *client.Client {
	clientConf := client.DefaultConfig()
	clientConf.Timeout = 30 * time.Second
	clientConf.IdleConnTimeout = 90 * time.Second
	for _, opt := range opts {
		clientConf.ApplyOptions(opt)
	}
	restyClient := client.New(clientConf)
	restyClient.SetLogger(restyLogger.NewLogger(fmt.Sprintf("%s RESTY", clientName)))
	restyClient.SetHeader("am-api-key", token)

	// retry
	restyClient.SetRetryCount(retryCount)
	restyClient.SetRetryWaitTime(time.Second)
	restyClient.AddRetryCondition(
		func(r *resty.Response, err error) bool {
			if r == nil {
				return false
			}
			return r.StatusCode() == http.StatusTooManyRequests
		},
	)
	return restyClient
}
func buildCNTClient(cfg *config.Config) *platform_api_v2.PlatformV2Client {
	restyClient := initRestyClient("Connectors", os.Getenv("AM_API_KEY"), 10,
		client.WithMaxIdleConnsPerHost(100))
	validate := validator.New()
	limiter := rate.NewLimiter(rate.Every(time.Millisecond), 1e5)

	return platform_api_v2.NewPlatformV2Client(restyClient, cfg.ConnectorsAPI.Url, limiter, validate)
}

func buildRedisClient(cfg *config.Config) (*redis.Client, error) {
	redis.SetLogger(redisLogger.NewLogger("redis"))
	cli := redis.NewFailoverClient(&redis.FailoverOptions{
		MasterName:    cfg.DynamicConfigs.Redis.MasterName,
		SentinelAddrs: strings.Split(cfg.DynamicConfigs.Redis.Host, ","),
		Password:      cfg.DynamicConfigs.Redis.Password,
		DB:            cfg.DynamicConfigs.Redis.DBNumber,
		PoolSize:      200,
		MaxConnAge:    2 * time.Hour,
	})
	pong := cli.Ping(context.Background())
	if pong.Err() != nil {
		return nil, pong.Err()
	}
	return cli, nil
}

func buildPubSubClient(config *config.Config) (*pubsubx.Client, error) {
	var opts []option.ClientOption
	if config.DynamicConfigs.GCPServiceAccount != "" {
		opts = append(opts, option.WithCredentialsJSON([]byte(config.DynamicConfigs.GCPServiceAccount)))
	}
	receiveSettings := pubsub.DefaultReceiveSettings
	receiveSettings.MaxOutstandingMessages = 32
	client, err := pubsubx.NewWithConfig(context.Background(), &pubsubx.Config{
		ProjectID:         config.GCP.ProjectID,
		EnableOtelTracing: true,
		ReceiveSettings:   &receiveSettings,
	}, opts...)
	if err != nil {
		return nil, err
	}

	return client, nil
}

func initBusinessMonitoringExporter(config *config.Config) *bme.Client {
	var pubsubOpts []option.ClientOption
	if config.DynamicConfigs != nil && config.DynamicConfigs.GCPServiceAccount != "" {
		pubsubOpts = append(pubsubOpts, option.WithCredentialsJSON([]byte(config.DynamicConfigs.GCPServiceAccount)))
	}

	sender, err := sender_pubsub.New(context.TODO(),
		sender_pubsub.WithPubSubClientOptions(pubsubOpts...))
	if err != nil {
		panic(err)
	}
	e, err := exporter.New(sender, exporter.WithFlushInterval(time.Duration(config.BusinessMonitoringExporter.FlushInterval)*time.Second),
		exporter.WithFlushCount(config.BusinessMonitoringExporter.FlushCount))
	if err != nil {
		panic(err)
	}

	e.Start()
	return bme.NewBusinessMonitoringExporter(e)
}
