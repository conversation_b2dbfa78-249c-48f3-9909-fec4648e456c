package products_center

import (
	"context"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/AfterShip/connectors-library/sdks/products_center"
	"github.com/AfterShip/connectors-sdk-go/v2/metafields"
	"github.com/AfterShip/gopkg/log"
	"github.com/AfterShip/pltf-pd-product-listings/internal/config"
	"github.com/AfterShip/pltf-pd-product-listings/internal/consts"
	"github.com/AfterShip/pltf-pd-product-listings/internal/third_party/connectors"
)

type productsService struct {
	logger            *log.Logger
	configs           *config.Config
	connectorsCli     connectors.Service
	productsCenterCli *products_center.Client
}

func (s *productsService) List(ctx context.Context, args *products_center.GetProductsArgs) ([]*products_center.Product, error) {
	return s.productsCenterCli.Product.List(ctx, args)
}

func (s *productsService) GetByID(ctx context.Context, id string) (*products_center.Product, error) {
	product, err := s.productsCenterCli.Product.GetByID(ctx, id)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	return product, nil
}

func (s *productsService) CoverProductVariantsPriceWithSpecialRules(ctx context.Context, product *products_center.Product) (*products_center.Product, error) {
	// 处理特殊规则的价格覆盖逻辑
	if product.Source.App.Platform == consts.Shopify {
		return s.coverProductVariantsPriceWithPriceFromMetaField(ctx, product)
	}

	return product, nil
}

func (s *productsService) coverProductVariantsPriceWithPriceFromMetaField(ctx context.Context, product *products_center.Product) (*products_center.Product, error) {
	if s.configs == nil || s.configs.DynamicConfigs == nil || s.configs.DynamicConfigs.ShopifyProductPriceSourceConfig == nil {
		return product, nil // 如果没有配置，则直接返回原始产品
	}

	resource, key, ok := s.configs.DynamicConfigs.ShopifyProductPriceSourceConfig.IsHit(product.Organization.ID, product.Source.App.Key)
	if !ok { // 没有命中白名单
		return product, nil
	}

	price, ok, err := s.getPriceFromMetaFields(ctx, product, resource, key)
	if err != nil { // 部分白名单客户的特殊逻辑，出错不影响主链路，只打印日志；最坏情况就是白名单内客户没有按预期同步价格，但是其他客户不影响
		s.logger.ErrorCtx(ctx, "failed to get price from meta fields", zap.Error(err))
	}

	// 没有匹配到指定 key
	if !ok || price == "" {
		return product, nil
	}

	// 用 Meta field 的价格覆盖，货币保持原样
	for i := range product.Variants {
		product.Variants[i].Price.Amount = price
	}

	return product, nil
}

// getPriceFromMetaFields 支持直接传入 meta fields 列表
func (s *productsService) getPriceFromMetaFields(ctx context.Context, product *products_center.Product, resource, key string) (string, bool, error) {
	ctx = log.AppendFieldsToContext(ctx, zap.String("organization_id", product.Organization.ID),
		zap.String("app_key", product.Source.App.Key), zap.String("expect_resource", resource), zap.String("expect_key", key))

	metaFields, err := s.connectorsCli.GetMetaFields(ctx, metafields.GetMetafieldsParams{
		OrganizationID:      product.Organization.ID,
		AppPlatform:         product.Source.App.Platform,
		AppKey:              product.Source.App.Key,
		ExternalResourceIDs: product.Source.ID,
		Keys:                key,
		Resource:            resource,
		Page:                "1",
		Limit:               "1",
	})
	if err != nil {
		return "", false, errors.WithStack(err)
	}

	if len(metaFields) > 0 {
		for _, mf := range metaFields {
			if mf.Resource.String() == resource && mf.Key.String() == key {
				if mf.Value.String() != "" {
					// 直接返回第一个匹配的值
					return mf.Value.String(), true, nil
				}
				s.logger.WarnCtx(ctx, " matched meta field by key, but value is empty")
			}
		}
	}

	s.logger.WarnCtx(ctx, "no matching meta field found")
	return "", false, nil
}
