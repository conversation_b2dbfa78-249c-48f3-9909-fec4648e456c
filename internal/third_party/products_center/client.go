package products_center

import (
	"context"

	"github.com/AfterShip/connectors-library/sdks/products_center"
	"github.com/AfterShip/gopkg/log"
	"github.com/AfterShip/pltf-pd-product-listings/internal/config"
	"github.com/AfterShip/pltf-pd-product-listings/internal/third_party/connectors"
)

type Client struct {
	Product productAPICollection
}

func NewClient(configs *config.Config, logger *log.Logger,
	connectorsCli connectors.Service, productsCenterCli *products_center.Client) *Client {
	return &Client{
		Product: &productsService{
			logger:            logger,
			configs:           configs,
			connectorsCli:     connectorsCli,
			productsCenterCli: productsCenterCli,
		},
	}
}

type productAPICollection interface {
	List(ctx context.Context, args *products_center.GetProductsArgs) ([]*products_center.Product, error)
	GetByID(ctx context.Context, id string) (*products_center.Product, error)
	CoverProductVariantsPriceWithSpecialRules(ctx context.Context, product *products_center.Product) (*products_center.Product, error)
}
