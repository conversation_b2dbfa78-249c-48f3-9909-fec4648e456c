package tiktokapi

import (
	"strings"

	"github.com/pkg/errors"

	tiktok_rest "github.com/AfterShip/connectors-ecommerce-sdk-go/tiktok/rest"
)

var (
	// Format data errors
	errPtrNotPointer = errors.New("target ptr is not a pointer")

	// Common errors
	ErrRequestFailed   = errors.New("request failed")
	ErrInvalidResponse = errors.New("invalid response")

	ErrCategoryVersionNotMatch      = errors.New("category version not match")
	ErrSellerInactive               = errors.New("seller is inactive")
	ErrCategoryInviteOnly           = errors.New("category is invite only")
	ErrInvalidEUCountry             = errors.New("invalid EU country")
	ErrCategoryUnavailable          = errors.New("category unavailable")
	ErrAccessTokenInvalid           = errors.New("access token is invalid")
	ErrAuthorizationExpired         = errors.New("authorization expired")
	ErrCategoryNotAvailableInRegion = errors.New("category not available in this region")
)

func convertErr(err error) error {
	tikTokError := &tiktok_rest.TikTokError{}
	if !errors.As(err, &tikTokError) {
		return err
	}

	if tikTokError.Code.Int() == 12052230 || tikTokError.Code.Int() == 12052217 {
		return errors.Wrap(ErrCategoryVersionNotMatch, err.Error())
	}
	if tikTokError.Code.Int() == 12052700 && strings.Contains(tikTokError.Message.String(), "seller is inactive") {
		return errors.Wrap(ErrSellerInactive, err.Error())
	}
	if tikTokError.Code.Int() == 12052223 {
		return errors.Wrap(ErrCategoryInviteOnly, err.Error())
	}
	if tikTokError.Code.Int() == 38023026 {
		return errors.Wrap(ErrInvalidEUCountry, err.Error())
	}
	if tikTokError.Code.Int() == 12052446 {
		return errors.Wrap(ErrCategoryUnavailable, err.Error())
	}
	if tikTokError.Code.Int() == 105001 {
		return errors.Wrap(ErrAccessTokenInvalid, err.Error())
	}
	if tikTokError.Code.Int() == 105002 {
		return errors.Wrap(ErrAuthorizationExpired, err.Error())
	}
	if tikTokError.Code.Int() == 12052446 {
		return errors.Wrap(ErrCategoryNotAvailableInRegion, err.Error())
	}

	return errors.Wrap(tikTokError, err.Error())
}
