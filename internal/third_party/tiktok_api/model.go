package tiktokapi

import (
	"encoding/json"
	"net/http"
	"reflect"

	tiktokrest_v202309 "github.com/AfterShip/connectors-ecommerce-sdk-go/tiktok/rest/version202309"
	commerceproxy "github.com/AfterShip/connectors-library/sdks/commerce_proxy"
	"github.com/AfterShip/gopkg/facility/types"
)

type baseResponse struct {
	Code      int    `json:"code"`
	Message   string `json:"message"`
	RequestID string `json:"request_id"`
}

type response struct {
	baseResponse
	Data json.RawMessage `json:"data"`
}

func (r *response) formatData(ptr any) error {
	if ptr == nil || reflect.TypeOf(ptr).Kind() != reflect.Ptr {
		return errPtrNotPointer
	}
	return json.Unmarshal(r.Data, ptr)
}

type tiktokParams interface {
	toRequestMeta() *commerceproxy.RequestMeta
	toRESTfulRequest() *commerceproxy.RestfulRequest
}

type CommonParams struct {
	OrganizationID string
	AppName        string
	AppKey         string
	ContentType    commerceproxy.ContentType
}

func (p *CommonParams) toRequestMeta() *commerceproxy.RequestMeta {
	return &commerceproxy.RequestMeta{
		OrganizationID: p.OrganizationID,
		AppName:        p.AppName,
		AppKey:         p.AppKey,
		ContentType:    p.ContentType,
	}
}

type GetCategoriesParams struct {
	CommonParams
	CategoryVersion string
	RefreshCache    bool
}

func (p *GetCategoriesParams) toRESTfulRequest() *commerceproxy.RestfulRequest {
	return &commerceproxy.RestfulRequest{
		Method: http.MethodGet,
		Path:   "/product/202309/categories",
		Queries: map[string]string{
			"category_version": p.CategoryVersion,
		},
	}
}

type GetCategoryRulesParams struct {
	CommonParams
	CategoryID      string
	CategoryVersion string
}

func (p *GetCategoryRulesParams) toRESTfulRequest() *commerceproxy.RestfulRequest {
	return &commerceproxy.RestfulRequest{
		Method: http.MethodGet,
		Path:   "/product/202309/categories/" + p.CategoryID + "/rules",
		Queries: map[string]string{
			"category_version": p.CategoryVersion,
		},
	}
}

type GetCategoryAttributesParams struct {
	CommonParams
	CategoryID      string
	CategoryVersion string
}

func (p *GetCategoryAttributesParams) toRESTfulRequest() *commerceproxy.RestfulRequest {
	return &commerceproxy.RestfulRequest{
		Method: http.MethodGet,
		Path:   "/product/202309/categories/" + p.CategoryID + "/attributes",
		Queries: map[string]string{
			"category_version": p.CategoryVersion,
		},
	}
}

type UploadProductImageParams struct {
	CommonParams
	UseCase    string
	ImageName  string
	ImageType  string
	ImageBytes []byte
}

func (p *UploadProductImageParams) toRESTfulRequest() *commerceproxy.RestfulRequest {
	return &commerceproxy.RestfulRequest{
		Method: http.MethodPost,
		Path:   "/product/202309/images/upload",
		MultipartFields: []*commerceproxy.MultipartField{
			{
				FileName:  p.ImageName,
				FileType:  p.ImageType,
				FieldName: "data",
				FileBytes: p.ImageBytes,
			},
		},
	}
}

type UploadProductFileParams struct {
	CommonParams
	FileName  string
	FileType  string
	FileBytes []byte
}

func (p *UploadProductFileParams) toRESTfulRequest() *commerceproxy.RestfulRequest {
	return &commerceproxy.RestfulRequest{
		Method: http.MethodPost,
		Path:   "/product/202309/files/upload",
		Body:   nil,
		MultipartFields: []*commerceproxy.MultipartField{
			{
				FileName:  p.FileName,
				FileType:  p.FileType,
				FieldName: "data",
				FileBytes: p.FileBytes,
			},
			{
				FieldName: "name",
				FileBytes: []byte(p.FileName),
				FileType:  "",
				FileName:  "",
			},
		},
	}
}

type PromotionCreateActivityParams struct {
	CommonParams
	Body tiktokrest_v202309.PromotionCreateActivityReq
}

func (p *PromotionCreateActivityParams) toRESTfulRequest() *commerceproxy.RestfulRequest {
	return &commerceproxy.RestfulRequest{
		Method: http.MethodPost,
		Path:   "/promotion/202309/activities",
		Body:   p.Body,
	}
}

type PromotionUpdateActivityParams struct {
	CommonParams
	ActivityID string
	Body       tiktokrest_v202309.PromotionUpdateActivityReq
}

func (p *PromotionUpdateActivityParams) toRESTfulRequest() *commerceproxy.RestfulRequest {
	return &commerceproxy.RestfulRequest{
		Method: http.MethodPut,
		Path:   "/promotion/202309/activities/" + p.ActivityID,
		Body:   p.Body,
	}
}

type PromotionDeactivateActivityParams struct {
	CommonParams
	ActivityID string
}

func (p *PromotionDeactivateActivityParams) toRESTfulRequest() *commerceproxy.RestfulRequest {
	return &commerceproxy.RestfulRequest{
		Method: http.MethodPost,
		Path:   "/promotion/202309/activities/" + p.ActivityID + "/deactivate",
	}
}

type PromotionGetActivityByIDParams struct {
	CommonParams
	ActivityID string
}

func (p *PromotionGetActivityByIDParams) toRESTfulRequest() *commerceproxy.RestfulRequest {
	return &commerceproxy.RestfulRequest{
		Method: http.MethodGet,
		Path:   "/promotion/202309/activities/" + p.ActivityID,
	}
}

type PromotionListActivityParams struct {
	CommonParams
	Body tiktokrest_v202309.PromotionListActivityReq
}

func (p *PromotionListActivityParams) toRESTfulRequest() *commerceproxy.RestfulRequest {
	return &commerceproxy.RestfulRequest{
		Method: http.MethodPost,
		Path:   "/promotion/202309/activities/search",
		Body:   p.Body,
	}
}

type PromotionUpdateActivityProductParams struct {
	CommonParams
	ActivityID string
	Body       tiktokrest_v202309.PromotionUpdateActivityProductReq
}

func (p *PromotionUpdateActivityProductParams) toRESTfulRequest() *commerceproxy.RestfulRequest {
	return &commerceproxy.RestfulRequest{
		Method: http.MethodPut,
		Path:   "/promotion/202309/activities/" + p.ActivityID + "/products",
		Body:   p.Body,
	}
}

type PromotionRemoveActivityProductParams struct {
	CommonParams
	ActivityID string
	Body       tiktokrest_v202309.PromotionRemoveActivityProductReq
}

func (p *PromotionRemoveActivityProductParams) toRESTfulRequest() *commerceproxy.RestfulRequest {
	return &commerceproxy.RestfulRequest{
		Method: http.MethodDelete,
		Path:   "/promotion/202309/activities/" + p.ActivityID + "/products",
		Body:   p.Body,
	}
}

type SearchResponsiblePersonsReq struct {
	CommonParams
	Query           string
	SalesChannelIDs []string
}

func (p *SearchResponsiblePersonsReq) toRESTfulRequest() *commerceproxy.RestfulRequest {
	return &commerceproxy.RestfulRequest{
		Method: http.MethodPost,
		Path:   "/product/202409/compliance/responsible_persons/search",
		Queries: map[string]string{
			"page_size": "100",
		},
		Body: tiktokrest_v202309.ListResponsiblePersonsReq{
			ResponsiblePersonIds: p.SalesChannelIDs,
			Keyword:              types.MakeString(p.Query),
		},
	}
}

type CreateResponsiblePersonReq struct {
	CommonParams
	Name             string
	Email            string
	PhoneCountryCode string
	PhoneNumber      string
	AddressLine1     string
	AddressLine2     string
	District         string
	City             string
	PostalCode       string
	Province         string
	Country          string
}

func (p *CreateResponsiblePersonReq) toRESTfulRequest() *commerceproxy.RestfulRequest {
	return &commerceproxy.RestfulRequest{
		Method: http.MethodPost,
		Path:   "/product/202409/compliance/responsible_persons",
		Body: tiktokrest_v202309.CreateResponsiblePersonReq{
			Address: &tiktokrest_v202309.Address{
				City:               types.MakeString(p.City),
				Country:            types.MakeString(p.Country),
				District:           types.MakeString(p.District),
				PostalCode:         types.MakeString(p.PostalCode),
				Province:           types.MakeString(p.Province),
				StreetAddressLine1: types.MakeString(p.AddressLine1),
				StreetAddressLine2: types.MakeString(p.AddressLine2),
			},
			Email: types.MakeString(p.Email),
			Name:  types.MakeString(p.Name),
			PhoneNumber: &tiktokrest_v202309.PhoneNumber{
				CountryCode: types.MakeString(p.PhoneCountryCode),
				LocalNumber: types.MakeString(p.PhoneNumber),
			},
		},
	}
}

type SearchManufacturersReq struct {
	CommonParams
	Query           string
	ManufacturerIDs []string
}

func (p *SearchManufacturersReq) toRESTfulRequest() *commerceproxy.RestfulRequest {
	return &commerceproxy.RestfulRequest{
		Method: http.MethodPost,
		Path:   "/product/202409/compliance/manufacturers/search",
		Queries: map[string]string{
			"page_size": "100",
		},
		Body: tiktokrest_v202309.ListManufacturersReq{
			ManufacturerIds: p.ManufacturerIDs,
			Keyword:         types.MakeString(p.Query),
		},
	}
}

type CreateManufacturerReq struct {
	CommonParams
	Name                string
	RegisteredTradeName string
	Email               string
	PhoneCountryCode    string
	PhoneNumber         string
	AddressLine1        string
}

func (p *CreateManufacturerReq) toRESTfulRequest() *commerceproxy.RestfulRequest {
	return &commerceproxy.RestfulRequest{
		Method: http.MethodPost,
		Path:   "/product/202409/compliance/manufacturers",
		Body: tiktokrest_v202309.CreateManufacturerReq{
			Name:                types.MakeString(p.Name),
			RegisteredTradeName: types.MakeString(p.RegisteredTradeName),
			Address:             types.MakeString(p.AddressLine1),
			Email:               types.MakeString(p.Email),
			PhoneNumber: &tiktokrest_v202309.PhoneNumber{
				CountryCode: types.MakeString(p.PhoneCountryCode),
				LocalNumber: types.MakeString(p.PhoneNumber),
			},
		},
	}
}
