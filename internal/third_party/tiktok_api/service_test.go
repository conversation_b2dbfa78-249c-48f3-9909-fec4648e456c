package tiktokapi

import (
	"context"
	"errors"
	"net/http"
	"testing"

	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"

	tiktokres_v202309 "github.com/AfterShip/connectors-ecommerce-sdk-go/tiktok/rest/version202309"
	"github.com/AfterShip/gopkg/facility/types"

	tiktokres "github.com/AfterShip/connectors-ecommerce-sdk-go/tiktok/rest"
	commerce "github.com/AfterShip/connectors-library/sdks/commerce_proxy"
)

func TestServiceImpl_request(t *testing.T) {
	type testModel struct {
		Foo string `json:"foo"`
	}

	testCases := []struct {
		name     string
		mockData func() (*commerce.ProxyResponse, error)
		expErr   error
	}{
		{
			name: "Success case",
			mockData: func() (*commerce.ProxyResponse, error) {
				return &commerce.ProxyResponse{
					Status: http.StatusOK,
					Body: response{
						baseResponse: baseResponse{
							Code:      codeSuccess,
							Message:   "Success",
							RequestID: "test-request-id",
						},
						Data: []byte(`{"foo":"bar"}`),
					},
				}, nil
			},
		},
		{
			name: "Request failed case",
			mockData: func() (*commerce.ProxyResponse, error) {
				return nil, errors.New("test-error")
			},
			expErr: ErrRequestFailed,
		},
		{
			name: "Invalid Proxy Response case",
			mockData: func() (*commerce.ProxyResponse, error) {
				return &commerce.ProxyResponse{Status: http.StatusOK, Body: "INVALID_TIKTOK_RESPONSE"}, nil
			},
			expErr: ErrInvalidResponse,
		},
		{
			name: "Invalid TikTok API response case",
			mockData: func() (*commerce.ProxyResponse, error) {
				return &commerce.ProxyResponse{
					Status: http.StatusOK,
					Body: response{
						baseResponse: baseResponse{
							Code:      codeSuccess,
							Message:   "Success",
							RequestID: "test-request-id",
						},
						Data: []byte("INVALID_TIKTOK_RESPONSE"),
					},
				}, nil
			},
			expErr: ErrInvalidResponse,
		},
	}
	for _, tt := range testCases {
		t.Run(tt.name, func(t *testing.T) {
			mockProxy := new(commerce.MockRestfulProxy)
			if tt.mockData != nil {
				resp, err := tt.mockData()
				mockProxy.On("TikTokShop", mock.Anything, mock.Anything).Return(resp, err).Once()
			}
			params := &GetCategoriesParams{
				CommonParams: CommonParams{
					OrganizationID: "test-org-id",
					AppName:        "test-app-name",
					AppKey:         "test-app-key",
				},
			}

			err := NewService(mockProxy).request(context.Background(), params, new(testModel))
			require.ErrorIs(t, err, tt.expErr)
		})
	}

	t.Run("Some cornor cases", func(t *testing.T) {
		mockProxy := new(commerce.MockRestfulProxy)

		params := &GetCategoriesParams{
			CommonParams: CommonParams{
				OrganizationID: "test-org-id",
				AppName:        "test-app-name",
				AppKey:         "test-app-key",
			},
		}

		// Resonse with TikTok error
		mockProxy.On("TikTokShop", mock.Anything, mock.Anything).Return(&commerce.ProxyResponse{
			Status: http.StatusOK,
			Body: response{
				baseResponse: baseResponse{
					Code:      12019002,
					Message:   "category is invalid",
					RequestID: "test-request-id",
				},
			},
		}, nil).Once()
		err := NewService(mockProxy).request(context.Background(), params, new(testModel))
		var ttErr *tiktokres.TikTokError
		require.ErrorAs(t, err, &ttErr)

		// Format data with nil pointer case
		mockProxy.On("TikTokShop", mock.Anything, mock.Anything).Return(&commerce.ProxyResponse{
			Status: http.StatusOK,
			Body: response{
				baseResponse: baseResponse{
					Code:      codeSuccess,
					Message:   "Success",
					RequestID: "test-request-id",
				},
				Data: []byte(`{"foo":"bar"}`),
			},
		}, nil).Once()
		err = NewService(mockProxy).request(context.Background(), params, nil)
		require.ErrorIs(t, err, ErrInvalidResponse)
	})
}

func TestServiceImpl_GetCategoryRules(t *testing.T) {
	testCases := []struct {
		name      string
		mockData  func() (*commerce.ProxyResponse, error)
		expResult tiktokres_v202309.CategoryRule
		expErr    error
	}{
		{
			name: "Success case",
			mockData: func() (*commerce.ProxyResponse, error) {
				return &commerce.ProxyResponse{
					Status: http.StatusOK,
					Body: response{
						baseResponse: baseResponse{
							Code:      codeSuccess,
							Message:   "Success",
							RequestID: "test-request-id",
						},
						Data: []byte(`{
							"external_category_id": "939272",
							"product_certifications": [],
							"size_chart": {
								"is_required": false,
								"is_supported": false
							},
							"cod": {
								"is_supported": false
							},
							"package_dimension": {
								"is_required": true
							}
						  }`),
					},
				}, nil
			},
			expResult: tiktokres_v202309.CategoryRule{
				ProductCertifications: []tiktokres_v202309.CategoryProductCertification{},
				SizeChart: &tiktokres_v202309.CategorySizeChart{
					IsSupported: types.MakeBool(false),
					IsRequired:  types.MakeBool(false),
				},
				Cod: &tiktokres_v202309.CategoryCod{
					IsSupported: types.MakeBool(false),
				},
				PackageDimension: &tiktokres_v202309.CategoryPackageDimension{
					IsRequired: types.MakeBool(true),
				},
			},
		},
		{
			name: "Invalid TikTok response case",
			mockData: func() (*commerce.ProxyResponse, error) {
				return &commerce.ProxyResponse{Status: http.StatusOK, Body: "INVALID_TIKTOK_RESPONSE"}, nil
			},
			expErr: ErrInvalidResponse,
		},
	}
	for _, tt := range testCases {
		t.Run(tt.name, func(t *testing.T) {
			mockProxy := new(commerce.MockRestfulProxy)
			if tt.mockData != nil {
				resp, err := tt.mockData()
				mockProxy.On("TikTokShop", mock.Anything, mock.Anything).Return(resp, err).Once()
			}
			params := &GetCategoryRulesParams{
				CommonParams: CommonParams{
					OrganizationID: "test-org-id",
					AppName:        "test-app-name",
					AppKey:         "test-app-key",
				},
				CategoryID: "test-category-id",
			}
			rules, err := NewService(mockProxy).GetCategoryRules(context.Background(), params)
			require.ErrorIs(t, err, tt.expErr)
			require.Equal(t, tt.expResult, rules)
		})
	}
}

func TestServiceImpl_GetAttributes(t *testing.T) {
	testCases := []struct {
		name      string
		mockData  func() (*commerce.ProxyResponse, error)
		expResult []tiktokres_v202309.Attributes
		expErr    error
	}{
		{
			name: "Success case",
			mockData: func() (*commerce.ProxyResponse, error) {
				return &commerce.ProxyResponse{
					Status: http.StatusOK,
					Body: response{
						baseResponse: baseResponse{
							Code:      codeSuccess,
							Message:   "Success",
							RequestID: "test-request-id",
						},
						Data: []byte(`{
							"attributes": [
							  {
									"id": "101400",
									"name": "CA Prop 65: Carcinogens",
									"type": "PRODUCT_PROPERTY",
									"is_requried": false,
									"values": [
										{
											"id": "1000058",
											"name": "Yes"
										},
										{
											"id": "1000059",
											"name": "No"
										}
									],
									"is_multiple_selected": false,
									"is_customizable": false
								}
							]
						  }`),
					},
				}, nil
			},
			expResult: []tiktokres_v202309.Attributes{
				{
					Id:         types.MakeString("101400"),
					Name:       types.MakeString("CA Prop 65: Carcinogens"),
					Type:       types.MakeString("PRODUCT_PROPERTY"),
					IsRequired: types.MakeBool(false),
					Values: []tiktokres_v202309.AttributeValue{
						{
							Id:   types.MakeString("1000058"),
							Name: types.MakeString("Yes"),
						},
						{
							Id:   types.MakeString("1000059"),
							Name: types.MakeString("No"),
						},
					},
					IsMultipleSelected: types.MakeBool(false),
					IsCustomizable:     types.MakeBool(false),
				},
			},
		},
		{
			name: "Invalid TikTok response case",
			mockData: func() (*commerce.ProxyResponse, error) {
				return &commerce.ProxyResponse{Status: http.StatusOK, Body: "INVALID_TIKTOK_RESPONSE"}, nil
			},
			expErr: ErrInvalidResponse,
		},
	}
	for _, tt := range testCases {
		t.Run(tt.name, func(t *testing.T) {
			mockProxy := new(commerce.MockRestfulProxy)
			if tt.mockData != nil {
				resp, err := tt.mockData()
				mockProxy.On("TikTokShop", mock.Anything, mock.Anything).Return(resp, err).Once()
			}
			params := &GetCategoryAttributesParams{
				CommonParams: CommonParams{
					OrganizationID: "test-org-id",
					AppName:        "test-app-name",
					AppKey:         "test-app-key",
				},
				CategoryID: "test-category-id",
			}
			rules, err := NewService(mockProxy).GetCategoryAttributes(context.Background(), params)
			require.ErrorIs(t, err, tt.expErr)
			require.Equal(t, tt.expResult, rules)
		})
	}
}
