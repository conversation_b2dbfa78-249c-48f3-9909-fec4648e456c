package tiktokapi

import (
	"context"

	"github.com/stretchr/testify/mock"

	tiktokres_v202309 "github.com/AfterShip/connectors-ecommerce-sdk-go/tiktok/rest/version202309"
)

var _ Service = (*MockService)(nil)

type MockService struct {
	mock.Mock
}

func (m *MockService) GetCategories(_ context.Context, params *GetCategoriesParams) ([]tiktokres_v202309.Category, error) {
	rest := m.Called(params)
	return rest.Get(0).([]tiktokres_v202309.Category), rest.Error(1)
}
func (m *MockService) GetCategoryRules(_ context.Context, params *GetCategoryRulesParams) (tiktokres_v202309.CategoryRule, error) {
	rest := m.Called(params)
	return rest.Get(0).(tiktokres_v202309.CategoryRule), rest.Error(1)
}
func (m *MockService) GetCategoryAttributes(_ context.Context, params *GetCategoryAttributesParams) ([]tiktokres_v202309.Attributes, error) {
	rest := m.Called(params)
	return rest.Get(0).([]tiktokres_v202309.Attributes), rest.Error(1)
}

func (m *MockService) UploadProductImage(_ context.Context, params *UploadProductImageParams) (tiktokres_v202309.ProductImage, error) {
	rest := m.Called(params)
	return rest.Get(0).(tiktokres_v202309.ProductImage), rest.Error(1)
}

func (m *MockService) UploadProductFile(_ context.Context, params *UploadProductFileParams) (tiktokres_v202309.ProductFile, error) {
	rest := m.Called(params)
	return rest.Get(0).(tiktokres_v202309.ProductFile), rest.Error(1)
}

func (m *MockService) PromotionCreateActivity(ctx context.Context, params *PromotionCreateActivityParams) (tiktokres_v202309.PromotionCreateActivityResp, error) {
	rest := m.Called(params)
	return rest.Get(0).(tiktokres_v202309.PromotionCreateActivityResp), rest.Error(1)
}

func (m *MockService) PromotionUpdateActivity(ctx context.Context, params *PromotionUpdateActivityParams) (tiktokres_v202309.PromotionUpdateActivityResp, error) {
	rest := m.Called(params)
	return rest.Get(0).(tiktokres_v202309.PromotionUpdateActivityResp), rest.Error(1)
}

func (m *MockService) PromotionDeactivateActivity(ctx context.Context, params *PromotionDeactivateActivityParams) (tiktokres_v202309.PromotionDeactivateActivityResp, error) {
	rest := m.Called(params)
	return rest.Get(0).(tiktokres_v202309.PromotionDeactivateActivityResp), rest.Error(1)
}

func (m *MockService) PromotionUpdateActivityProduct(ctx context.Context, params *PromotionUpdateActivityProductParams) (tiktokres_v202309.PromotionUpdateActivityProductResp, error) {
	rest := m.Called(params)
	return rest.Get(0).(tiktokres_v202309.PromotionUpdateActivityProductResp), rest.Error(1)
}

func (m *MockService) PromotionListActivity(ctx context.Context, params *PromotionListActivityParams) (tiktokres_v202309.PromotionListActivityResp, error) {
	rest := m.Called(params)
	return rest.Get(0).(tiktokres_v202309.PromotionListActivityResp), rest.Error(1)
}

func (m *MockService) PromotionGetActivityByID(ctx context.Context, params *PromotionGetActivityByIDParams) (tiktokres_v202309.PromotionGetActivityDetailResp, error) {
	rest := m.Called(params)
	return rest.Get(0).(tiktokres_v202309.PromotionGetActivityDetailResp), rest.Error(1)
}

func (m *MockService) PromotionRemoveActivityProduct(ctx context.Context, params *PromotionRemoveActivityProductParams) (tiktokres_v202309.PromotionRemoveActivityProductResp, error) {
	rest := m.Called(params)
	return rest.Get(0).(tiktokres_v202309.PromotionRemoveActivityProductResp), rest.Error(1)
}

func (m *MockService) SearchResponsiblePersons(_ context.Context, params *SearchResponsiblePersonsReq) ([]tiktokres_v202309.ResponsiblePersons, error) {
	rest := m.Called(params)
	return rest.Get(0).([]tiktokres_v202309.ResponsiblePersons), rest.Error(1)
}

func (m *MockService) CreateResponsiblePerson(_ context.Context, params *CreateResponsiblePersonReq) (string, error) {
	rest := m.Called(params)
	return rest.Get(0).(string), rest.Error(1)
}

func (m *MockService) SearchManufacturers(_ context.Context, params *SearchManufacturersReq) ([]tiktokres_v202309.Manufacturers, error) {
	rest := m.Called(params)
	return rest.Get(0).([]tiktokres_v202309.Manufacturers), rest.Error(1)
}

func (m *MockService) CreateManufacturer(_ context.Context, params *CreateManufacturerReq) (string, error) {
	rest := m.Called(params)
	return rest.Get(0).(string), rest.Error(1)
}
