package tiktokapi

import (
	"context"
	"fmt"

	"github.com/AfterShip/gopkg/facility/types"

	tiktokres "github.com/AfterShip/connectors-ecommerce-sdk-go/tiktok/rest"
	tiktokres_v202309 "github.com/AfterShip/connectors-ecommerce-sdk-go/tiktok/rest/version202309"
	commerce "github.com/AfterShip/connectors-library/sdks/commerce_proxy"
)

const codeSuccess = 0

type Service interface {
	GetCategories(ctx context.Context, params *GetCategoriesParams) ([]tiktokres_v202309.Category, error)
	GetCategoryRules(ctx context.Context, params *GetCategoryRulesParams) (tiktokres_v202309.CategoryRule, error)
	GetCategoryAttributes(ctx context.Context, params *GetCategoryAttributesParams) ([]tiktokres_v202309.Attributes, error)
	UploadProductImage(ctx context.Context, params *UploadProductImageParams) (tiktokres_v202309.ProductImage, error)
	UploadProductFile(ctx context.Context, params *UploadProductFileParams) (tiktokres_v202309.ProductFile, error)
	PromotionDeactivateActivity(ctx context.Context, params *PromotionDeactivateActivityParams) (tiktokres_v202309.PromotionDeactivateActivityResp, error)
	PromotionGetActivityByID(ctx context.Context, params *PromotionGetActivityByIDParams) (tiktokres_v202309.PromotionGetActivityDetailResp, error)
	SearchResponsiblePersons(ctx context.Context, req *SearchResponsiblePersonsReq) ([]tiktokres_v202309.ResponsiblePersons, error)
	CreateResponsiblePerson(ctx context.Context, req *CreateResponsiblePersonReq) (string, error)
	SearchManufacturers(ctx context.Context, req *SearchManufacturersReq) ([]tiktokres_v202309.Manufacturers, error)
	CreateManufacturer(ctx context.Context, req *CreateManufacturerReq) (string, error)
}

type serviceImpl struct {
	proxy commerce.RestfulProxy
}

func NewService(proxy commerce.RestfulProxy) *serviceImpl {
	return &serviceImpl{
		proxy: proxy,
	}
}

// GetCategories returns the categories which obtain from TikTok.
func (impl *serviceImpl) GetCategories(ctx context.Context, params *GetCategoriesParams) ([]tiktokres_v202309.Category, error) {
	var result tiktokres_v202309.ListCategoriesData
	err := impl.request(ctx, params, &result)
	if err != nil {
		return nil, convertErr(err)
	}

	return result.Categories, nil
}

// GetCategoryRules returns the category rules which obtain from TikTok.
// TODO: There still not cache the result, since we don't know how to make the cache perform more efficient(high hit rate).
func (impl *serviceImpl) GetCategoryRules(ctx context.Context, params *GetCategoryRulesParams) (tiktokres_v202309.CategoryRule, error) {
	var result tiktokres_v202309.CategoryRule
	err := impl.request(ctx, params, &result)
	if err != nil {
		return tiktokres_v202309.CategoryRule{}, convertErr(err)
	}

	return result, nil
}

func (impl *serviceImpl) GetCategoryAttributes(ctx context.Context, params *GetCategoryAttributesParams) ([]tiktokres_v202309.Attributes, error) {
	var result tiktokres_v202309.ListCategoryAttributesData
	err := impl.request(ctx, params, &result)
	if err != nil {
		return nil, convertErr(err)
	}

	return result.Attributes, nil
}

func (impl *serviceImpl) UploadProductImage(ctx context.Context, params *UploadProductImageParams) (tiktokres_v202309.ProductImage, error) {
	var result tiktokres_v202309.ProductImage
	err := impl.request(ctx, params, &result)
	if err != nil {
		return tiktokres_v202309.ProductImage{}, convertErr(err)
	}

	return result, nil
}

func (impl *serviceImpl) UploadProductFile(ctx context.Context, params *UploadProductFileParams) (tiktokres_v202309.ProductFile, error) {
	var result tiktokres_v202309.ProductFile
	return result, impl.request(ctx, params, &result)
}

func (impl *serviceImpl) PromotionCreateActivity(ctx context.Context, params *PromotionCreateActivityParams) (tiktokres_v202309.PromotionCreateActivityResp, error) {
	var result tiktokres_v202309.PromotionCreateActivityResp
	return result, impl.request(ctx, params, &result)
}

func (impl *serviceImpl) PromotionUpdateActivity(ctx context.Context, params *PromotionUpdateActivityParams) (tiktokres_v202309.PromotionUpdateActivityResp, error) {
	var result tiktokres_v202309.PromotionUpdateActivityResp
	return result, impl.request(ctx, params, &result)
}

func (impl *serviceImpl) PromotionDeactivateActivity(ctx context.Context, params *PromotionDeactivateActivityParams) (tiktokres_v202309.PromotionDeactivateActivityResp, error) {
	var result tiktokres_v202309.PromotionDeactivateActivityResp
	return result, impl.request(ctx, params, &result)
}

func (impl *serviceImpl) PromotionUpdateActivityProduct(ctx context.Context, params *PromotionUpdateActivityProductParams) (tiktokres_v202309.PromotionUpdateActivityProductResp, error) {
	var result tiktokres_v202309.PromotionUpdateActivityProductResp
	return result, impl.request(ctx, params, &result)
}

func (impl *serviceImpl) PromotionListActivity(ctx context.Context, params *PromotionListActivityParams) (tiktokres_v202309.PromotionListActivityResp, error) {
	var result tiktokres_v202309.PromotionListActivityResp
	return result, impl.request(ctx, params, &result)
}

func (impl *serviceImpl) PromotionGetActivityByID(ctx context.Context, params *PromotionGetActivityByIDParams) (tiktokres_v202309.PromotionGetActivityDetailResp, error) {
	var result tiktokres_v202309.PromotionGetActivityDetailResp
	return result, impl.request(ctx, params, &result)
}

func (impl *serviceImpl) PromotionRemoveActivityProduct(ctx context.Context, params *PromotionRemoveActivityProductParams) (tiktokres_v202309.PromotionRemoveActivityProductResp, error) {
	var result tiktokres_v202309.PromotionRemoveActivityProductResp
	return result, impl.request(ctx, params, &result)
}

func (impl *serviceImpl) SearchResponsiblePersons(ctx context.Context, req *SearchResponsiblePersonsReq) ([]tiktokres_v202309.ResponsiblePersons, error) {
	type SearchResponsiblePersonsRsp struct {
		ResponsiblePersons []tiktokres_v202309.ResponsiblePersons `json:"responsible_persons"`
	}
	var rsp SearchResponsiblePersonsRsp
	return rsp.ResponsiblePersons, impl.request(ctx, req, &rsp)
}

func (impl *serviceImpl) CreateResponsiblePerson(ctx context.Context, req *CreateResponsiblePersonReq) (string, error) {
	type CreateResponsiblePersonRsp struct {
		ResponsiblePersonID string `json:"responsible_person_id"`
	}
	var rsp CreateResponsiblePersonRsp
	if err := impl.request(ctx, req, &rsp); err != nil {
		return "", convertErr(err)
	}
	return rsp.ResponsiblePersonID, nil
}

func (impl *serviceImpl) SearchManufacturers(ctx context.Context, req *SearchManufacturersReq) ([]tiktokres_v202309.Manufacturers, error) {
	type SearchManufacturersRsp struct {
		Manufacturers []tiktokres_v202309.Manufacturers `json:"manufacturers"`
	}
	var rsp SearchManufacturersRsp
	return rsp.Manufacturers, impl.request(ctx, req, &rsp)
}

func (impl *serviceImpl) CreateManufacturer(ctx context.Context, req *CreateManufacturerReq) (string, error) {
	type CreateManufacturerRsp struct {
		ManufacturerID string `json:"manufacturer_id"`
	}
	var rsp CreateManufacturerRsp
	if err := impl.request(ctx, req, &rsp); err != nil {
		return "", convertErr(err)
	}
	return rsp.ManufacturerID, nil
}

func (impl *serviceImpl) request(ctx context.Context, params tiktokParams, targetPtr any) error {
	req := params.toRESTfulRequest()
	proxyResp, err := impl.proxy.TikTokShop(ctx, params.toRequestMeta(), req)
	if err != nil {
		return fmt.Errorf("%w: %s", ErrRequestFailed, err.Error())
	}

	var ttResp response
	if err := proxyResp.FormatBody(&ttResp); err != nil {
		return fmt.Errorf("%w(%d): %s", ErrInvalidResponse, proxyResp.Status, err.Error())
	}

	if ttResp.Code != codeSuccess {
		ttErr := &tiktokres.TikTokError{
			Code:      types.MakeInt(ttResp.Code),
			Message:   types.MakeString(ttResp.Message),
			RequestID: types.MakeString(ttResp.RequestID),
		}
		return tiktokres.MapTTSErrorToStandardError(proxyResp.Status, ttResp.Code, req.Method, req.Path, ttErr)
	}

	if err := ttResp.formatData(targetPtr); err != nil {
		return fmt.Errorf("%w: %s", ErrInvalidResponse, err.Error())
	}

	return nil
}
