package bme

import (
	"time"

	"go.uber.org/zap"

	exporter "github.com/AfterShip/business-monitoring-events-exporter-sdk-go"
	exporter_event "github.com/AfterShip/business-monitoring-events-exporter-sdk-go/event"
	product_listings_api_v1 "github.com/AfterShip/connectors-library/sdks/product_listings"
	"github.com/AfterShip/feed-sdk-go/business_events_collector"

	"github.com/AfterShip/pltf-pd-product-listings/internal/consts"
	"github.com/AfterShip/pltf-pd-product-listings/internal/logger"
)

type Client struct {
	RecordService              Service
	businessMonitoringExporter *exporter.Exporter
}

func NewBusinessMonitoringExporter(businessMonitoringExporter *exporter.Exporter) *Client {
	return &Client{
		RecordService: infraBme{
			businessMonitoringExporter: businessMonitoringExporter,
			businessEventsCollector:    business_events_collector.NewCollector(businessMonitoringExporter),
		},
		businessMonitoringExporter: businessMonitoringExporter,
	}
}

type Logs struct {
	Organization product_listings_api_v1.Organization
	SalesChannel product_listings_api_v1.SalesChannel
	Source       product_listings_api_v1.Source
	Biz          Biz
}

type Biz struct {
	SourceID  string
	Event     consts.BizEvent
	StageName consts.BizStageName
	Status    consts.BizStatus
	ErrorCode consts.BizErrorCode
	Message   consts.BizMessage
	CreatedAt time.Time
	UpdatedAt time.Time
}

type infraBme struct {
	businessMonitoringExporter *exporter.Exporter
	businessEventsCollector    *business_events_collector.FeedEventsCollector
}

type Service interface {
	Recording(arg Logs)
	CollectBusinessEvents(input *business_events_collector.BizEventInput) error
}

func (b infraBme) Recording(arg Logs) {
	bzEvent := exporter_event.NewEvent(consts.BMEEventName).
		WithOrgID(arg.Organization.ID).
		WithProperties(exporter_event.String("biz_resource_id", arg.Biz.SourceID),
			exporter_event.String("platform", arg.Source.App.Platform),
			exporter_event.String("store", arg.Source.App.Key),
			exporter_event.String("sales_channel", arg.SalesChannel.Platform),
			exporter_event.String("feed_channel_store", arg.SalesChannel.StoreKey),
			exporter_event.DateTime("biz_updated_at", arg.Biz.UpdatedAt),
			exporter_event.DateTime("biz_created_at", arg.Biz.CreatedAt),
			exporter_event.String("biz_event", arg.Biz.Event.String()),
			// listing 默认都是商品域
			exporter_event.String("biz_resource_type", consts.Products.String()),
			exporter_event.String("biz_resource_status", arg.Biz.Status.String()),
			exporter_event.String("biz_step_name", arg.Biz.StageName.String()),
			exporter_event.String("biz_message", arg.Biz.Message.String()),
			exporter_event.Int64("biz_step_result_status", arg.Biz.ErrorCode.Int64()),
		)
	// always be successful
	_ = b.businessMonitoringExporter.Send(bzEvent)
}

func (b infraBme) CollectBusinessEvents(input *business_events_collector.BizEventInput) error {
	return b.businessEventsCollector.Handle(input)
}

func (b Client) Shutdown() {
	err := b.businessMonitoringExporter.Close()
	if err != nil {
		logger.Get().Error("Failed to close business monitoring exporter", zap.Error(err))
	}
	logger.Get().Info("shutdown business monitoring exporter")
}
