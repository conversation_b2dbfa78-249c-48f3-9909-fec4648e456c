package connectors

import (
	"context"

	"github.com/pkg/errors"

	"github.com/AfterShip/connectors-sdk-go/v2/stores"
)

func (s *service) GetStore(ctx context.Context, params stores.GetStoresParams) (*stores.ModelsResponseStore, error) {
	if params.OrganizationID == "" || params.AppKey == "" || params.AppPlatform == "" {
		return nil, ErrorMissingRequiredFields
	}

	resp, err := s.stores.GetStores(ctx, params)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	if resp == nil || resp.Data == nil || len(resp.Data.Stores) == 0 {
		return nil, ErrStoreNotFound
	}

	return &resp.Data.Stores[0], nil
}
