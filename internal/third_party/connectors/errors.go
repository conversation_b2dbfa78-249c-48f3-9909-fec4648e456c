package connectors

import (
	"errors"

	platform_api_v2 "github.com/AfterShip/connectors-sdk-go/v2"
)

var (
	ErrStoreNotFound                   = errors.New("store not found")
	ErrorMissingRequiredFields         = errors.New("params missing required fields")
	ErrorInventoryLevelEmpty           = errors.New("inventory_level empty")
	ErrorExternalInventoryItemIDsEmpty = errors.New("external_inventory_item_ids is empty")
	ErrProductCurrencyPricesEmpty      = errors.New("product_currency_prices empty")
)

func isCurrencyNotFoundInCurrencyPricesAPI(err error) bool {
	apiErr := &platform_api_v2.ConnectorAPIError{}
	if errors.As(err, &apiErr) {
		return apiErr.MetaCode == 40443
	}

	return false
}
