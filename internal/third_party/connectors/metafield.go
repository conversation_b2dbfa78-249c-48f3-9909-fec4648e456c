package connectors

import (
	"context"

	"github.com/pkg/errors"

	"github.com/AfterShip/connectors-sdk-go/v2/metafields"
)

func (s *service) GetMetaFields(ctx context.Context, params metafields.GetMetafieldsParams) ([]metafields.ModelsResponseMetafield, error) {
	resp, err := s.metafields.GetMetafields(ctx, params)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if resp.Data == nil {
		return nil, errors.New("meta fields resp empty")
	}
	return resp.Data.Metafields, nil
}
