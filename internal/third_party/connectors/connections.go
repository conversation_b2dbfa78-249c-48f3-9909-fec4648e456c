package connectors

import (
	"context"

	jsoniter "github.com/json-iterator/go"
	"github.com/pkg/errors"
	"github.com/tidwall/gjson"
	"go.uber.org/zap"

	"github.com/AfterShip/connectors-sdk-go/v2/connections"

	"github.com/AfterShip/pltf-pd-product-listings/internal/consts"
	"github.com/AfterShip/pltf-pd-product-listings/internal/logger"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
)

func (s *service) GetBothConnections(ctx context.Context, organizationID string) (BothConnections, error) {
	result := BothConnections{}
	resp, err := s.connections.GetConnections(ctx, connections.GetConnectionsParams{
		OrganizationID:              organizationID,
		AppName:                     consts.AppNameFeed,
		Status:                      "connected",
		Page:                        1,
		Limit:                       100, // 100 enough
		DisableAssociatedConnection: "true",
		Types:                       consts.ConnectionTypeStore,
	})
	if err != nil {
		return result, err
	}

	for _, connection := range resp.Data.Connections {
		result.OrganizationID = connection.Organization.ID.String()
		if isChannelPlatform(connection.App.Platform.String()) {
			result.Channels = append(result.Channels, models.App{
				Key:      connection.App.Key.String(),
				Platform: connection.App.Platform.String(),
			})
		} else {
			if result.App.Key != "" && result.App.Platform != "" {
				logger.Get().WarnCtx(ctx, "multiple source ecommerce connections found, only one connection will be used",
					zap.String("organization_id", organizationID))
			}
			// old connection cover new connection
			result.App = models.App{
				Key:      connection.App.Key.String(),
				Platform: connection.App.Platform.String(),
			}
		}
	}

	return result, nil
}

func (s *service) GetConnections(ctx context.Context, params connections.GetConnectionsParams) ([]connections.ModelsResponseConnection, error) {
	params.AppName = "feed"

	resp, err := s.connections.GetConnections(ctx, params)
	if err != nil {
		return nil, err
	}

	if resp.Data == nil {
		return nil, errors.New("response data is nil")
	}

	return resp.Data.Connections, nil
}

func (s *service) GetConnectionRegions(ctx context.Context, params GetConnectionRegionParam) (string, error) {
	connectionList, err := s.GetConnections(ctx, connections.GetConnectionsParams{
		OrganizationID:              params.OrganizationID,
		AppPlatform:                 params.AppPlatform,
		AppKey:                      params.AppKey,
		Page:                        1,
		Limit:                       1,
		DisableAssociatedConnection: "true",
		Types:                       consts.ConnectionTypeStore,
	})
	if err != nil {
		return "", err
	}
	if len(connectionList) == 0 {
		return "", ErrConnectionNotFound
	}
	optionByte, _ := jsoniter.Marshal(connectionList[0].App.Options)
	region := gjson.GetBytes(optionByte, "region").String()
	if region == "" {
		return "", errors.New("region is empty")
	}
	return region, nil
}
