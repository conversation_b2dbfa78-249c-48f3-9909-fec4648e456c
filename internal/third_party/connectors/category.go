package connectors

import (
	"context"
	"errors"

	"github.com/AfterShip/connectors-sdk-go/v2/categories"
)

func (s *service) GetCategories(ctx context.Context, params categories.GetCategoriesParams) ([]categories.ModelsResponseCategory, error) {
	resp, err := s.categories.GetCategories(ctx, params)
	if err != nil {
		return nil, err
	}

	if resp.Data == nil {
		return nil, errors.New("response data is nil")
	}

	return resp.Data.Categories, nil
}
