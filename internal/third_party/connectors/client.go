package connectors

import (
	"context"

	platform_api_v2 "github.com/AfterShip/connectors-sdk-go/v2"
	"github.com/AfterShip/connectors-sdk-go/v2/categories"
	"github.com/AfterShip/connectors-sdk-go/v2/connections"
	"github.com/AfterShip/connectors-sdk-go/v2/inventory_levels"
	"github.com/AfterShip/connectors-sdk-go/v2/metafields"
	product_currency_prices "github.com/AfterShip/connectors-sdk-go/v2/product-currency-prices"
	"github.com/AfterShip/connectors-sdk-go/v2/products"
	"github.com/AfterShip/connectors-sdk-go/v2/stores"
	"github.com/AfterShip/gopkg/log"
)

type service struct {
	logger                *log.Logger
	products              products.ProductsSvc
	inventoryLevels       inventory_levels.InventoryLevelsSvc
	stores                stores.StoresSvc
	categories            categories.CategoriesSvc
	connections           connections.ConnectionsSvc
	productCurrencyPrices product_currency_prices.ProductCurrencyPricesSvc
	metafields            metafields.MetafieldsSvc
}

type Service interface {
	GetProductByID(ctx context.Context, id, expand string) (*products.ModelsResponseProduct, error)
	GetProducts(ctx context.Context, params *products.GetProductsParams) ([]products.ModelsResponseProduct, error)
	ListInventoryLevelsV2(ctx context.Context, params *ListInventoryLevelsArgs) ([]inventory_levels.ModelsResponseInventoryLevel2, error)
	GetStore(ctx context.Context, params stores.GetStoresParams) (*stores.ModelsResponseStore, error)
	GetCategories(ctx context.Context, params categories.GetCategoriesParams) ([]categories.ModelsResponseCategory, error)
	GetConnections(ctx context.Context, params connections.GetConnectionsParams) ([]connections.ModelsResponseConnection, error)
	GetBothConnections(ctx context.Context, organizationID string) (BothConnections, error)
	GetConnectionRegions(ctx context.Context, params GetConnectionRegionParam) (string, error)
	GetProductCurrencyPrices(
		ctx context.Context, params GetProductCurrencyPricesParams,
	) ([]product_currency_prices.ModelsProductCurrencyPrices, error)
	GetMetaFields(ctx context.Context, params metafields.GetMetafieldsParams) ([]metafields.ModelsResponseMetafield, error)
}

var _ Service = (*service)(nil)

func NewService(logger *log.Logger, cli *platform_api_v2.PlatformV2Client) Service {
	return &service{
		logger:                logger,
		products:              products.NewProductsSvc(cli),
		inventoryLevels:       inventory_levels.NewInventoryLevelsSvc(cli),
		stores:                stores.NewStoresSvc(cli),
		categories:            categories.NewCategoriesSvc(cli),
		connections:           connections.NewConnectionsSvc(cli),
		productCurrencyPrices: product_currency_prices.NewProductCurrencyPricesSvc(cli),
		metafields:            metafields.NewMetafieldsSvc(cli),
	}
}
