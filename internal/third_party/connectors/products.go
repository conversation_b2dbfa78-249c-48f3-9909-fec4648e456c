package connectors

import (
	"context"

	"github.com/AfterShip/connectors-sdk-go/v2/products"
)

func (s *service) GetProductByID(ctx context.Context, id, expand string) (*products.ModelsResponseProduct, error) {
	resp, err := s.products.GetProductsByID(ctx, id, products.GetProductsByIDParams{
		Expand: expand,
	})
	if err != nil {
		return nil, err
	}

	return resp.Data, nil
}

func (s *service) GetProducts(ctx context.Context, params *products.GetProductsParams) ([]products.ModelsResponseProduct, error) {
	resp, err := s.products.GetProducts(ctx, *params)
	if err != nil {
		return nil, err
	}

	if resp == nil || resp.Data == nil {
		return nil, nil
	}

	return resp.Data.Products, nil
}
