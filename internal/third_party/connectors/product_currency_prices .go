package connectors

import (
	"context"

	"github.com/pkg/errors"

	pcp "github.com/AfterShip/connectors-sdk-go/v2/product-currency-prices"
)

func (s *service) GetProductCurrencyPrices(
	ctx context.Context, params GetProductCurrencyPricesParams,
) ([]pcp.ModelsProductCurrencyPrices, error) {
	sdkParams := params.toConnectorsSDKGetParams()

	resp, err := s.productCurrencyPrices.GetProductCurrencyPrices(ctx, sdkParams)
	if err != nil {
		if isCurrencyNotFoundInCurrencyPricesAPI(err) {
			return nil, ErrProductCurrencyPricesEmpty
		}
		return nil, errors.WithStack(err)
	}

	if resp == nil || resp.Data == nil {
		return nil, ErrProductCurrencyPricesEmpty
	}

	return resp.Data.ProductCurrencyPrices, nil
}
