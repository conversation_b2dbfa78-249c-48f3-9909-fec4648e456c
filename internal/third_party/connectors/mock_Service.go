// Code generated by mockery v2.40.3. DO NOT EDIT.

package connectors

import (
	context "context"

	"github.com/AfterShip/connectors-sdk-go/v2/categories"
	"github.com/AfterShip/connectors-sdk-go/v2/connections"
	inventory_levels "github.com/AfterShip/connectors-sdk-go/v2/inventory_levels"
	"github.com/AfterShip/connectors-sdk-go/v2/metafields"
	product_currency_prices "github.com/AfterShip/connectors-sdk-go/v2/product-currency-prices"

	mock "github.com/stretchr/testify/mock"

	products "github.com/AfterShip/connectors-sdk-go/v2/products"

	stores "github.com/AfterShip/connectors-sdk-go/v2/stores"
)

// MockService is an autogenerated mock type for the Service type
type MockService struct {
	mock.Mock
}

// GetBothConnections provides a mock function with given fields: ctx, organizationID
func (_m *MockService) GetBothConnections(ctx context.Context, organizationID string) (BothConnections, error) {
	ret := _m.Called(ctx, organizationID)

	if len(ret) == 0 {
		panic("no return value specified for GetBothConnections")
	}

	var r0 BothConnections
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (BothConnections, error)); ok {
		return rf(ctx, organizationID)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) BothConnections); ok {
		r0 = rf(ctx, organizationID)
	} else {
		r0 = ret.Get(0).(BothConnections)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, organizationID)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetProductByID provides a mock function with given fields: ctx, id, expand
func (_m *MockService) GetProductByID(ctx context.Context, id string, expand string) (*products.ModelsResponseProduct, error) {
	ret := _m.Called(ctx, id, expand)

	if len(ret) == 0 {
		panic("no return value specified for GetProductByID")
	}

	var r0 *products.ModelsResponseProduct
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string) (*products.ModelsResponseProduct, error)); ok {
		return rf(ctx, id, expand)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string) *products.ModelsResponseProduct); ok {
		r0 = rf(ctx, id, expand)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*products.ModelsResponseProduct)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string) error); ok {
		r1 = rf(ctx, id, expand)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetProducts provides a mock function with given fields: ctx, params
func (_m *MockService) GetProducts(ctx context.Context, params *products.GetProductsParams) ([]products.ModelsResponseProduct, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for GetProducts")
	}

	var r0 []products.ModelsResponseProduct
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *products.GetProductsParams) ([]products.ModelsResponseProduct, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *products.GetProductsParams) []products.ModelsResponseProduct); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]products.ModelsResponseProduct)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *products.GetProductsParams) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetStore provides a mock function with given fields: ctx, params
func (_m *MockService) GetStore(ctx context.Context, params stores.GetStoresParams) (*stores.ModelsResponseStore, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for GetStore")
	}

	var r0 *stores.ModelsResponseStore
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, stores.GetStoresParams) (*stores.ModelsResponseStore, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, stores.GetStoresParams) *stores.ModelsResponseStore); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*stores.ModelsResponseStore)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, stores.GetStoresParams) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

func (_m *MockService) ListInventoryLevelsV2(ctx context.Context, params *ListInventoryLevelsArgs) ([]inventory_levels.ModelsResponseInventoryLevel2, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for ListInventoryLevelsV2")
	}

	var r0 []inventory_levels.ModelsResponseInventoryLevel2
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *ListInventoryLevelsArgs) ([]inventory_levels.ModelsResponseInventoryLevel2, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *ListInventoryLevelsArgs) []inventory_levels.ModelsResponseInventoryLevel2); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]inventory_levels.ModelsResponseInventoryLevel2)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *ListInventoryLevelsArgs) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

func (_m *MockService) GetCategories(ctx context.Context, params categories.GetCategoriesParams) ([]categories.ModelsResponseCategory, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for GetCategories")
	}

	var r0 []categories.ModelsResponseCategory
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, categories.GetCategoriesParams) ([]categories.ModelsResponseCategory, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, categories.GetCategoriesParams) []categories.ModelsResponseCategory); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]categories.ModelsResponseCategory)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, categories.GetCategoriesParams) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

func (_m *MockService) GetConnections(ctx context.Context, params connections.GetConnectionsParams) ([]connections.ModelsResponseConnection, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for GetConnections")
	}

	var r0 []connections.ModelsResponseConnection
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, connections.GetConnectionsParams) ([]connections.ModelsResponseConnection, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, connections.GetConnectionsParams) []connections.ModelsResponseConnection); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]connections.ModelsResponseConnection)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, connections.GetConnectionsParams) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

func (_m *MockService) GetConnectionRegions(ctx context.Context, params GetConnectionRegionParam) (string, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for GetConnectionRegions")
	}

	var r0 string
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, GetConnectionRegionParam) (string, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, GetConnectionRegionParam) string); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(string)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, GetConnectionRegionParam) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetStore provides a mock function with given fields: ctx, params
func (_m *MockService) GetProductCurrencyPrices(
	ctx context.Context, params GetProductCurrencyPricesParams,
) ([]product_currency_prices.ModelsProductCurrencyPrices, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for GetStore")
	}

	var r0 []product_currency_prices.ModelsProductCurrencyPrices
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, GetProductCurrencyPricesParams) ([]product_currency_prices.ModelsProductCurrencyPrices, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, GetProductCurrencyPricesParams) []product_currency_prices.ModelsProductCurrencyPrices); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]product_currency_prices.ModelsProductCurrencyPrices)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, GetProductCurrencyPricesParams) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

func (_m *MockService) GetMetaFields(ctx context.Context, params metafields.GetMetafieldsParams) ([]metafields.ModelsResponseMetafield, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for GetMetaFields")
	}

	var r0 []metafields.ModelsResponseMetafield
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, metafields.GetMetafieldsParams) ([]metafields.ModelsResponseMetafield, error)); ok {
		return rf(ctx, params)
	}

	if rf, ok := ret.Get(0).(func(context.Context, metafields.GetMetafieldsParams) []metafields.ModelsResponseMetafield); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]metafields.ModelsResponseMetafield)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, metafields.GetMetafieldsParams) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// NewMockService creates a new instance of MockService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockService(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockService {
	mock := &MockService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
