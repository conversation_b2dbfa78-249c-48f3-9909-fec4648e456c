package connectors

import (
	"context"
	"strconv"
	"strings"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	connector_lib_utils "github.com/AfterShip/connectors-library/utils"
	"github.com/AfterShip/connectors-library/utils/sets"
	"github.com/AfterShip/connectors-sdk-go/v2/inventory_levels"
	"github.com/AfterShip/pltf-pd-product-listings/internal/consts"
	"github.com/AfterShip/pltf-pd-product-listings/internal/utils/slicex"
)

type ListInventoryLevelsArgs struct {
	OrganizationID           string   `json:"organization_id,omitempty" validate:"required"`
	AppPlatform              string   `json:"app_platform,omitempty" validate:"required"`
	AppKey                   string   `json:"app_key,omitempty" validate:"required"`
	ExternalInventoryItemIDs []string `json:"external_inventory_item_ids" validate:"required"`
	Page                     int      `json:"page" validate:"required"`
	Limit                    int      `json:"limit" validate:"required"`
}

func (s *service) ListInventoryLevelsV2(ctx context.Context, params *ListInventoryLevelsArgs) (
	[]inventory_levels.ModelsResponseInventoryLevel2, error) {
	if len(params.ExternalInventoryItemIDs) == 0 {
		return nil, ErrorExternalInventoryItemIDsEmpty
	}

	result := make([]inventory_levels.ModelsResponseInventoryLevel2, 0)
	inventoryItemIDs := slicex.SplitSlice(params.ExternalInventoryItemIDs, 10)

	arg := &ListInventoryLevelsArgs{
		OrganizationID: params.OrganizationID,
		AppPlatform:    params.AppPlatform,
		AppKey:         params.AppKey,
		Page:           params.Page,
		Limit:          params.Limit,
	}

	for _, itemIDs := range inventoryItemIDs {
		inventoryItemIDSet := sets.NewStringSet(itemIDs...)
		existInventoryItemIDs := sets.NewStringSet()

		arg.ExternalInventoryItemIDs = itemIDs
		inventoryLevels, err := s.listInventoryLevels(ctx, arg)
		if err != nil && !errors.Is(err, ErrorInventoryLevelEmpty) {
			return nil, err
		}

		for _, level := range inventoryLevels {
			existInventoryItemIDs.Add(level.ExternalInventoryItemID.String())
			result = append(result, level)
		}

		notFoundInventoryItemIDsSet := inventoryItemIDSet.Diff(existInventoryItemIDs)
		notFoundInventoryItemIDs := notFoundInventoryItemIDsSet.ToList()
		if len(notFoundInventoryItemIDs) > 0 {
			s.logger.InfoCtx(ctx, "inventory levels not found, go to search by proxy",
				zap.String("organization_id", params.OrganizationID),
				zap.String("app_platform", params.AppPlatform),
				zap.String("app_key", params.AppKey),
				zap.Strings("not_found_inventory_item_ids", notFoundInventoryItemIDs),
				zap.Int("cnt_exist_inventory_level_length", len(inventoryLevels)),
				zap.String("search_arg", connector_lib_utils.GetJsonIndent(arg)),
			)
			arg.ExternalInventoryItemIDs = notFoundInventoryItemIDs
			levels, err := s.listInventoryLevelsByProxy(ctx, arg)
			if err != nil && !errors.Is(err, ErrorInventoryLevelEmpty) {
				return nil, err
			}
			if len(levels) > 0 {
				result = append(result, levels...)
			}
		}
	}

	return result, nil
}

func (s *service) listInventoryLevels(ctx context.Context, params *ListInventoryLevelsArgs) (
	[]inventory_levels.ModelsResponseInventoryLevel2, error) {

	result := make([]inventory_levels.ModelsResponseInventoryLevel2, 0)
	for page := params.Page; page < 10; page++ {
		resp, err := s.inventoryLevels.GetInventoryLevels(ctx, inventory_levels.GetInventoryLevelsParams{
			OrganizationID:           params.OrganizationID,
			AppPlatform:              params.AppPlatform,
			AppKey:                   params.AppKey,
			ExternalInventoryItemIDs: strings.Join(params.ExternalInventoryItemIDs, ","),
			Page:                     strconv.Itoa(page),
			Limit:                    strconv.Itoa(params.Limit),
		})
		if err != nil {
			return nil, err
		}
		if resp.Data == nil { // 不预期出现
			s.logger.ErrorCtx(ctx, "inventory levels is empty",
				zap.String("organization_id", params.OrganizationID),
				zap.String("app_platform", params.AppPlatform),
				zap.String("app_key", params.AppKey),
				zap.Int("page", page),
				zap.Int("limit", params.Limit),
				zap.Strings("external_inventory_item_ids", params.ExternalInventoryItemIDs),
			)
			return nil, ErrorInventoryLevelEmpty
		}
		result = append(result, resp.Data.InventoryLevels...)
		if len(resp.Data.InventoryLevels) == 0 || len(resp.Data.InventoryLevels) < params.Limit {
			break
		}
		s.logger.InfoCtx(ctx, "inventory levels search next page",
			zap.String("organization_id", params.OrganizationID),
			zap.String("params", connector_lib_utils.GetJsonIndent(params)),
			zap.Int("page", page),
			zap.Int("result_length", len(result)),
		)
	}

	if len(result) >= params.Limit {
		s.logger.InfoCtx(ctx, "more inventory levels are available",
			zap.String("organization_id", params.OrganizationID),
			zap.String("app_platform", params.AppPlatform),
			zap.String("app_key", params.AppKey),
			zap.Strings("external_inventory_item_ids", params.ExternalInventoryItemIDs),
			zap.Int("length", len(result)),
		)
	}

	return result, nil
}

func (s *service) listInventoryLevelsByProxy(ctx context.Context, params *ListInventoryLevelsArgs) (
	[]inventory_levels.ModelsResponseInventoryLevel2, error) {
	resp, err := s.inventoryLevels.GetAppsInventoryLevelsByAppPlatformAppKeyAppName(ctx,
		params.AppPlatform, params.AppKey, consts.AppNameFeed,
		inventory_levels.GetAppsInventoryLevelsByAppPlatformAppKeyAppNameParams{
			OrganizationID:           params.OrganizationID,
			AppPlatform:              params.AppPlatform,
			AppKey:                   params.AppKey,
			AppName:                  consts.AppNameFeed,
			ExternalInventoryItemIDs: strings.Join(params.ExternalInventoryItemIDs, ","),
			Limit:                    consts.CNTAppsInventoryLevelSearchLimit,
		})
	if err != nil {
		return nil, err
	}

	if resp.Data == nil {
		return nil, ErrorInventoryLevelEmpty
	}

	result := make([]inventory_levels.ModelsResponseInventoryLevel2, 0)
	for _, item := range resp.Data.InventoryLevels {
		result = append(result, inventory_levels.ModelsResponseInventoryLevel2{
			App:                     item.App,
			Organization:            item.Organization,
			AvailableQuantity:       item.AvailableQuantity,
			ExternalInventoryItemID: item.ExternalInventoryItemID,
			ExternalWarehouseID:     item.ExternalWarehouseID,
		})
	}

	return result, nil
}
