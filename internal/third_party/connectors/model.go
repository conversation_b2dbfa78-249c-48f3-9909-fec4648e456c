package connectors

import (
	pcp "github.com/AfterShip/connectors-sdk-go/v2/product-currency-prices"
	"github.com/AfterShip/connectors-sdk-go/v2/products"
	"github.com/AfterShip/pltf-pd-product-listings/internal/consts"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
)

type BothConnections struct {
	OrganizationID string `json:"organization"`
	App            models.App
	Channels       []models.App
}

func (b *BothConnections) IsBothConnections() bool {
	if b == nil {
		return false
	}
	return b.App.Key != "" && b.App.Platform != "" && len(b.Channels) > 0
}

func (b *BothConnections) IsBothConnectionsWithSaleChannel(saleChannel models.SalesChannel) bool {
	if b == nil {
		return false
	}

	if !b.IsBothConnections() {
		return false
	}

	for _, channel := range b.Channels {
		if channel.Key == saleChannel.StoreKey &&
			channel.Platform == saleChannel.Platform {
			return true
		}
	}

	return false
}

type GetConnectionRegionParam struct {
	OrganizationID string `json:"organization_id"`
	AppPlatform    string `json:"app_platform"`
	AppKey         string `json:"app_key"`
}

type GetProductCurrencyPricesParams struct {
	ConnectorsProduct *products.ModelsResponseProduct
	CountryRegion     string `validate:"required"`
	Currency          string `validate:"required"`
}

func (p GetProductCurrencyPricesParams) toConnectorsSDKGetParams() pcp.GetProductCurrencyPricesParams {
	externalVariantIDs := make([]string, 0)
	for _, variant := range p.ConnectorsProduct.Variants {
		externalVariantIDs = append(externalVariantIDs, variant.ExternalID.String())
	}

	return pcp.GetProductCurrencyPricesParams{
		OrganizationID:     p.ConnectorsProduct.Organization.ID.String(),
		AppKey:             p.ConnectorsProduct.App.Key.String(),
		AppName:            consts.AppFeed,
		AppPlatform:        p.ConnectorsProduct.App.Platform.String(),
		CountryRegion:      p.CountryRegion,
		Currency:           p.Currency,
		ExternalProductID:  p.ConnectorsProduct.ExternalID.String(),
		ExternalVariantIDs: externalVariantIDs,
	}
}

func isChannelPlatform(platform string) bool {
	if platform == consts.TikTokShop || platform == consts.Shein {
		return true
	}
	return false
}
