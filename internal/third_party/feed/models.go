package feed

import (
	"strings"
)

type SupportFeatureStatus string

func (s SupportFeatureStatus) String() string {
	return string(s)
}

const (
	FeatureEnabled  SupportFeatureStatus = "enabled"
	FeatureDisabled SupportFeatureStatus = "disabled"
)

type FeatureCode string

func (f FeatureCode) String() string {
	return string(f)
}

const (
	FeatureCodeAutoLinkV2             FeatureCode = "auto_link_v2"
	FeatureCodeShopifyMarketsMapping  FeatureCode = "shopify_markets_mapping"
	FeatureCodeVariantStatusInventory FeatureCode = "variant_status_inventory"
)

// 集合所有 listing 需要使用的 feature code，一次性查询
func unionListingWorkerUsedFeatureCodes() string {
	allFeatureCodes := []string{
		FeatureCodeAutoLinkV2.String(),
		FeatureCodeShopifyMarketsMapping.String(),
		FeatureCodeVariantStatusInventory.String(),
	}
	return strings.Join(allFeatureCodes, ",")
}
