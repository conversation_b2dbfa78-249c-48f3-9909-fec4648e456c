package feed

import (
	"context"

	"github.com/AfterShip/feed-sdk-go/v1/events"
	events_sdk "github.com/AfterShip/feed-sdk-go/v1/events"
)

type eventsService struct {
	eventsService events_sdk.EventsSvc
}

func (s eventsService) GetEvents(ctx context.Context, arg events.GetInternalEventsParams) ([]events.GetInternalEventsDataEvents, error) {
	eventsResp, err := s.eventsService.GetInternalEvents(ctx, arg)
	if err != nil {
		return nil, err
	}

	if eventsResp == nil || eventsResp.Data == nil {
		return nil, nil
	}

	return eventsResp.Data.Events, nil
}
