package feed

import (
	"context"

	"github.com/stretchr/testify/mock"

	"github.com/AfterShip/feed-sdk-go/v2/support_features"
)

type MockSupportFeature struct {
	mock.Mock
}

func (m *MockSupportFeature) GetSupportFeatures(ctx context.Context, organizationID string) ([]support_features.GetInternalSupportFeaturesDataSupportFeatures, error) {
	args := m.Called(ctx, organizationID)
	return args.Get(0).([]support_features.GetInternalSupportFeaturesDataSupportFeatures), args.Error(1)
}
