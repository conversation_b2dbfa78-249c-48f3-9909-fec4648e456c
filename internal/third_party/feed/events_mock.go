package feed

import (
	"context"

	"github.com/stretchr/testify/mock"

	"github.com/AfterShip/feed-sdk-go/v1/events"
)

type MockEventsService struct {
	mock.Mock
}

func (m *MockEventsService) GetEvents(ctx context.Context, arg events.GetInternalEventsParams) ([]events.GetInternalEventsDataEvents, error) {
	args := m.Called(ctx, arg)
	return args.Get(0).([]events.GetInternalEventsDataEvents), args.Error(1)
}
