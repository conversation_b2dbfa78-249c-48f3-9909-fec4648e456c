package feed

import (
	"testing"

	"github.com/AfterShip/feed-sdk-go/v2/support_features"
	"github.com/AfterShip/gopkg/facility/types"
)

func TestSupportFeatureService_RunInGrayModel(t *testing.T) {
	tests := []struct {
		name                        string
		organizationSupportFeatures []support_features.GetInternalSupportFeaturesDataSupportFeatures
		featureCode                 FeatureCode
		want                        bool
	}{
		{
			name: "Feature enabled",
			organizationSupportFeatures: []support_features.GetInternalSupportFeaturesDataSupportFeatures{
				{
					Name:   types.MakeString("sync_inventory"),
					Status: types.MakeString("enabled"),
				},
			},
			featureCode: "sync_inventory",
			want:        true,
		},
		{
			name: "Feature disabled",
			organizationSupportFeatures: []support_features.GetInternalSupportFeaturesDataSupportFeatures{
				{
					Name:   types.MakeString("sync_inventory"),
					Status: types.MakeString("disabled"),
				},
			},
			featureCode: "sync_inventory",
			want:        false,
		},
		{
			name: "feed support code not found",
			organizationSupportFeatures: []support_features.GetInternalSupportFeaturesDataSupportFeatures{
				{
					Name:   types.MakeString("xxxx"),
					Status: types.MakeString("enabled"),
				},
			},
			featureCode: "sync_inventory",
			want:        false,
		},
		{
			name:                        "Empty organizationSupportFeatures",
			organizationSupportFeatures: nil,
			featureCode:                 "feature",
			want:                        false,
		},
		{
			name: "Empty featureCode",
			organizationSupportFeatures: []support_features.GetInternalSupportFeaturesDataSupportFeatures{
				{
					Name:   types.MakeString("sync_inventory"),
					Status: types.MakeString("disabled"),
				},
			},
			featureCode: "",
			want:        false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := RunInGrayModel(tt.organizationSupportFeatures, tt.featureCode)
			if got != tt.want {
				t.Errorf("isInGrayModel() = %v, want %v", got, tt.want)
			}
		})
	}
}
