package feed

import (
	"context"

	feed_base_cli "github.com/AfterShip/feed-sdk-go/client"
	"github.com/AfterShip/feed-sdk-go/v2/support_features"
)

type ClientV2 struct {
	SupportFeature supportFeatureCollection
}

func NewClientV2(feedBaseCli *feed_base_cli.FeedClient) *ClientV2 {
	return &ClientV2{
		SupportFeature: supportFeatureService{
			feedSupportFeaturesSvc: support_features.NewSupportFeaturesSvc(feedBaseCli),
		},
	}
}

type supportFeatureCollection interface {
	GetSupportFeatures(ctx context.Context, organizationID string) ([]support_features.GetInternalSupportFeaturesDataSupportFeatures, error)
}
