package feed

import (
	"context"

	feed_base_cli "github.com/AfterShip/feed-sdk-go/client"
	"github.com/AfterShip/feed-sdk-go/v1/events"
)

type ClientV1 struct {
	EventsService eventsCollection
}

func NewClientV1(feedBaseCli *feed_base_cli.FeedClient) *ClientV1 {
	return &ClientV1{
		EventsService: eventsService{
			eventsService: events.NewEventsSvc(feedBaseCli),
		},
	}
}

type eventsCollection interface {
	GetEvents(ctx context.Context, arg events.GetInternalEventsParams) ([]events.GetInternalEventsDataEvents, error)
}
