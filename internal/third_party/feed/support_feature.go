package feed

import (
	"context"

	"github.com/AfterShip/feed-sdk-go/v2/support_features"
)

type supportFeatureService struct {
	// cliV2    *feed_api_v2.FeedV2Client
	feedSupportFeaturesSvc support_features.SupportFeaturesSvc
}

func (s supportFeatureService) GetSupportFeatures(ctx context.Context, organizationID string) ([]support_features.GetInternalSupportFeaturesDataSupportFeatures, error) {
	supportFeatureResp, err := s.feedSupportFeaturesSvc.GetInternalSupportFeatures(ctx, support_features.GetInternalSupportFeaturesParams{
		OrganizationID: organizationID,
		FeatureCodes:   unionListingWorkerUsedFeatureCodes(),
	})
	if err != nil {
		return nil, err
	}

	if supportFeatureResp == nil || supportFeatureResp.Data == nil {
		return nil, nil
	}

	return supportFeatureResp.Data.SupportFeatures, nil
}

func RunInGrayModel(organizationSupportFeatures []support_features.GetInternalSupportFeaturesDataSupportFeatures, featureCode FeatureCode) bool {
	if len(organizationSupportFeatures) == 0 {
		return false
	}

	for _, featureItem := range organizationSupportFeatures {
		if featureItem.Name.String() == string(featureCode) {
			return featureItem.Status.String() == string(FeatureEnabled)
		}
	}
	return false
}
