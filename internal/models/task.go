package models

import (
	"context"
	"time"

	"github.com/AfterShip/pltf-pd-product-listings/internal/consts"
)

type BaseTask interface {
	BuildTaskArgs(ctx context.Context, input TaskInput) (TaskArgs, error)
	ParseOutput(ctx context.Context, task *Task) TaskOutput
}

type TaskOutput interface{}

type TaskInput interface{}

type TaskArgs struct {
	GroupName        consts.TaskGroupName
	Type             string `oneof:"batch single"`
	OrganizationID   string
	StoreKey         string
	Platform         string
	ResourceID       string
	Description      string
	ConcurrencyKey   string
	ConcurrencyLimit int64
	Inputs           TaskInput
}

type Task struct {
	ID             string       `json:"id"`
	Organization   Organization `json:"organization"`
	StoreKey       string       `json:"store_key"`
	Platform       string       `json:"platform"`
	Metadata       TaskMetadata `json:"metadata"`
	Inputs         string       `json:"inputs"`
	Outputs        TaskOutputs  `json:"outputs"`
	Description    string       `json:"description"`
	ParentTaskID   string       `json:"parent_task_id"`
	ChildTaskIDs   []string     `json:"child_task_ids"`
	ChildTasks     []Task       `json:"child_tasks"`
	PreviousTaskID string       `json:"previous_task_id"`
	NextTaskID     string       `json:"next_task_id"`
	Status         TaskStatus   `json:"status"`
	CreatedAt      time.Time    `json:"created_at"`
	UpdatedAt      time.Time    `json:"updated_at"`
}

type TaskMetadata struct {
	GroupName   consts.TaskGroupName    `json:"group_name"`
	Type        string                  `json:"type"`
	Reference   TaskMetadataReference   `json:"reference"`
	Description string                  `json:"description"`
	Concurrency TaskMetadataConcurrency `json:"concurrency"`
	Controller  TaskMetadataController  `json:"controller"`
}

type TaskMetadataReference struct {
	Module     string `json:"module"`
	Submodule  string `json:"submodule"`
	ResourceID string `json:"resource_id"`
}

type TaskMetadataConcurrency struct {
	Key string `json:"key"`
}

type TaskMetadataController struct {
	RetryAttempts  int    `json:"retry_attempts"`
	TimeoutSeconds int    `json:"timeout_seconds"`
	GuaranteeJobID string `json:"guarantee_job_id"`
}

type TaskOutputs struct {
	Code    string `json:"code"`
	Message string `json:"message"`
	Data    string `json:"data"`
}

type TaskStatus struct {
	State        string    `json:"state"`
	PendingAt    time.Time `json:"pending_at"`
	RunningAt    time.Time `json:"running_at"`
	SucceededAt  time.Time `json:"succeeded_at"`
	CancelledAt  time.Time `json:"cancelled_at"`
	LastFailedAt time.Time `json:"last_failed_at"`
	TerminatedAt time.Time `json:"terminated_at"`
}
