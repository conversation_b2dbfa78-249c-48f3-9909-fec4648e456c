## Project common models
* `models` - models for handlers and domains
* common models as Pagination

## How to use
* import models from `internal/models`
* use models in handlers and domains
* Don't add func to model unless necessary.
* Models used within domains repo should not be defined at this level.

## 项目中共有模型
* `models` - 用于处理程序和域的模型
* 通用模型如 Pagination

## 如何使用
* 从 `internal/models` 导入模型
* 在处理程序和域中使用模型
* 除非必要，否则不要将 func 添加到模型中。
* repo 域内使用的模型不应在此级别定义。