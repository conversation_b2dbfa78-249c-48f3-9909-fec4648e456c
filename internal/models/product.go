package models

import (
	"encoding/json"
	"regexp"
	"strings"
	"time"

	"github.com/AfterShip/gopkg/facility/set"
	"github.com/AfterShip/pltf-pd-product-listings/internal/consts"
	"github.com/AfterShip/pltf-pd-product-listings/internal/utils/image"
)

type SalesChannelFile struct {
	SalesChannelID string `json:"sales_channel_id"`
	URL            string `json:"url"`
}

type SalesChannelResource struct {
	SalesChannelID string `json:"sales_channel_id"`
	Name           string `json:"name"`
	Value          string `json:"value"`
}

type ProductSizeChart struct {
	Attributes []ProductSizeChartAttribute `json:"attributes"`
	Images     []SalesChannelFile          `json:"images"`
}

type ProductSizeChartAttribute struct {
	SalesChannelID            string `json:"sales_channel_id"`
	SalesChannelValueID       string `json:"sales_channel_value_id"`
	RelateSalesChannelID      string `json:"relate_sales_channel_id"`
	RelateSalesChannelValueID string `json:"relate_sales_channel_value_id"`
	Value                     string `json:"value"`
}

type ProductCertification struct {
	SalesChannelID string             `json:"sales_channel_id"`
	Files          []SalesChannelFile `json:"files"`
	Images         []SalesChannelFile `json:"images"`
}

type ProductAttribute struct {
	SalesChannelID string                 `json:"sales_channel_id"`
	Name           string                 `json:"name"`
	Values         []SalesChannelResource `json:"values"`
}

type ProductVariantBarcode struct {
	Value string `json:"value"`
	Type  string `json:"type"`
}

// nolint:gocritic
func (b ProductVariantBarcode) IsDiff(compareBarcode ProductVariantBarcode) bool {
	// 如果两个 barcode 都为空，则认为相同
	if b.Value == "" && compareBarcode.Value == "" {
		return false
	}

	if !strings.EqualFold(b.Value, compareBarcode.Value) {
		return true
	}

	// 如果两个 barcode 都不为空，且值不相等，则认为不同
	if b.Type != "" && compareBarcode.Type != "" {
		if !strings.EqualFold(b.Type, compareBarcode.Type) {
			return true
		}
	}

	return false
}

type ProductVariantPrice struct {
	Currency string `json:"currency"`
	Amount   string `json:"amount"`
}

type ProductVariantShippingSetting struct {
	Unit  string  `json:"unit"`
	Value float64 `json:"value"`
}

func (s ProductVariantShippingSetting) IsDiff(compareSetting ProductVariantShippingSetting) bool {
	return s.Unit != compareSetting.Unit || s.Value != compareSetting.Value
}

type ProductVariantOption struct {
	Name                 string `json:"name"`
	Value                string `json:"value"`
	SalesChannelOptionID string `json:"sales_channel_option_id"`
	SalesChannelValueID  string `json:"sales_channel_value_id"`
}

type VariantCompareSetting struct {
	IgnoreBarcode bool
	IgnorePackage bool
	IgnoreImage   bool
}

type ProductVariant struct {
	ID                 string                        `json:"id"`
	Position           int                           `json:"position"`
	InventoryQuantity  float64                       `json:"inventory_quantity"`
	Sku                string                        `json:"sku"`
	Barcode            ProductVariantBarcode         `json:"barcode"`
	Title              string                        `json:"title"`
	Price              ProductVariantPrice           `json:"price"`
	Cost               ProductVariantPrice           `json:"cost"`
	ImageURL           string                        `json:"image_url"`
	CompareAtPrice     ProductVariantPrice           `json:"compare_at_price"`
	Length             ProductVariantShippingSetting `json:"length"`
	Width              ProductVariantShippingSetting `json:"width"`
	Height             ProductVariantShippingSetting `json:"height"`
	Weight             ProductVariantShippingSetting `json:"weight"`
	AllowBackorder     bool                          `json:"allow_backorder"`
	Options            []*ProductVariantOption       `json:"options"`
	FulfillmentService string                        `json:"fulfillment_service"`
	RequiresShipping   bool                          `json:"requires_shipping"`
	Available          string                        `json:"available"` // "", "available" or "unavailable"
}

// 当前 variant 是否属于此SKC 下
func (v *ProductVariant) IsBelongToOption(salesChannelOptionID, salesChannelValueID string) bool {
	for _, option := range v.Options {
		if option.SalesChannelOptionID == salesChannelOptionID && option.SalesChannelValueID == salesChannelValueID {
			return true
		}
	}

	return false
}

// nolint:gocyclo
func (v *ProductVariant) IsDiff(variant *ProductVariant, setting VariantCompareSetting, ttsCDNDomainRegExps []*regexp.Regexp) bool {
	// 比较 sku, image_url, package
	if variant.Sku != v.Sku {
		return true
	}

	if variant.Available != v.Available {
		return true
	}

	if !setting.IgnorePackage {
		if variant.Width.IsDiff(v.Width) {
			return true
		}
		if variant.Height.IsDiff(v.Height) {
			return true
		}
		if variant.Length.IsDiff(v.Length) {
			return true
		}
		if variant.Weight.IsDiff(v.Weight) {
			return true
		}
	}

	if !setting.IgnoreBarcode {
		if variant.Barcode.IsDiff(v.Barcode) {
			return true
		}
	}

	isNewVariantSingle := v.IsSingleVariant()
	isOldVariantSingle := variant.IsSingleVariant()

	// 都不是单 variant 的情况下，比较 options 和 image
	if !isNewVariantSingle || !isOldVariantSingle { //nolint:nestif
		// 比较 options 数量
		if len(variant.Options) != len(v.Options) {
			return true
		}

		newOptionSet := set.NewStringSet()
		oldOptionSet := set.NewStringSet()

		// 比较 options 的值
		for i := range v.Options {
			newOptionSet.Add(v.Options[i].Name + ":" + v.Options[i].Value)
			oldOptionSet.Add(variant.Options[i].Name + ":" + variant.Options[i].Value)
		}

		if !newOptionSet.Equal(oldOptionSet) {
			return true
		}

		if !setting.IgnoreImage {
			if v.isImageDiff(variant, ttsCDNDomainRegExps) {
				return true
			}
		}
	}

	return false
}

func (v *ProductVariant) isImageDiff(variant *ProductVariant, ttsCDNDomainRegExps []*regexp.Regexp) bool {
	// image compare
	lastUri, lastOk := image.IsSalesChannelURL(v.ImageURL, ttsCDNDomainRegExps)
	newUri, newOK := image.IsSalesChannelURL(variant.ImageURL, ttsCDNDomainRegExps)
	if lastOk && newOK {
		if lastUri != newUri {
			return true
		}
	} else {
		if variant.ImageURL != v.ImageURL {
			return true
		}
	}

	return false
}

func (v *ProductVariant) IsSingleVariant() bool {
	for i := range v.Options {
		if v.Options[i].Name != "" && v.Options[i].Value != "" {
			return false
		}
	}

	return true
}

func (v *ProductVariant) BarcodeFilled() bool {
	if v.Barcode.Value == "" || v.Barcode.Type == "" {
		return false
	}
	return true
}

func (v *ProductVariant) LengthFilled() bool {
	if v.Length.Unit == "" || v.Length.Value == 0 {
		return false
	}
	return true
}

func (v *ProductVariant) WidthFilled() bool {
	if v.Width.Unit == "" || v.Width.Value == 0 {
		return false
	}
	return true
}

func (v *ProductVariant) HeightFilled() bool {
	if v.Height.Unit == "" || v.Height.Value == 0 {
		return false
	}
	return true
}

func (v *ProductVariant) WeightFilled() bool {
	if v.Weight.Unit == "" || v.Weight.Value == 0 {
		return false
	}
	return true
}

func (v *ProductVariant) LengthExceedTTSMaxValue() bool {
	if v.Length.Unit == "cm" &&
		v.Length.Value >= 100 {
		return true
	}

	return false
}

type ProductOption struct {
	Name                 string                     `json:"name"`
	Position             int                        `json:"position"`
	Values               []string                   `json:"values"`
	SalesChannelOptionID string                     `json:"sales_channel_option_id"`
	ValueDetails         []ProductOptionValueDetail `json:"value_details"`
}

type ProductOptionValueDetail struct {
	Value                    string                         `json:"value"`
	SalesChannelValueID      string                         `json:"sales_channel_value_id"`
	SalesChannelMediaGroupID string                         `json:"sales_channel_media_group_id"`
	Media                    []ProductMedia                 `json:"media"`
	SalesChannelID           string                         `json:"sales_channel_id"`
	Audit                    ProductOptionValueDetailAudit  `json:"audit"`
	State                    consts.ProductOptionValueState `json:"state"`
	SyncStatus               consts.SyncStatus              `json:"sync_status"`
}

type ProductOptionValueDetailAudit struct {
	State         consts.ProductOptionValueAuditState         `json:"state"`
	FailedReasons []ProductOptionValueDetailAuditFailedReason `json:"failed_reasons"`
	LastFailedAt  *time.Time                                  `json:"last_failed_at"`
}

type ProductOptionValueDetailAuditFailedReason struct {
	Position    string   `json:"position"`
	Reasons     []string `json:"reasons"`
	Suggestions []string `json:"suggestions"`
}

type ProductMedia struct {
	SalesChannelID    string                `json:"sales_channel_id"`
	Type              string                `json:"type"`
	Position          int                   `json:"position"`
	Thumbnail         ProductMediaThumbnail `json:"thumbnail"`
	URL               string                `json:"url"`
	MimeType          string                `json:"mime_type"`
	ExternalVideoHost string                `json:"external_video_host"`
	ExternalImageType string                `json:"external_image_type"`
}

type ProductMediaThumbnail struct {
	URL string `json:"url"`
}

type ProductCompliance struct {
	Manufacturers      []ProductComplianceManufacturer      `json:"manufacturers"`
	ResponsiblePersons []ProductComplianceResponsiblePerson `json:"responsible_persons"`
}

type ProductComplianceManufacturer struct {
	SalesChannelID string `json:"sales_channel_id"`
}

type ProductComplianceResponsiblePerson struct {
	SalesChannelID string `json:"sales_channel_id"`
}

type Product struct {
	Title            string                  `json:"title"`
	ShortDescription string                  `json:"short_description"`
	Categories       []*SalesChannelResource `json:"categories"`
	Tags             []string                `json:"tags"`
	Brand            SalesChannelResource    `json:"brand"`
	Vendor           string                  `json:"vendor"`
	Description      string                  `json:"description"`
	SizeChart        ProductSizeChart        `json:"size_chart"`
	Certifications   []*ProductCertification `json:"certifications"`
	Attributes       []*ProductAttribute     `json:"attributes"`
	Variants         []*ProductVariant       `json:"variants"`
	Options          []*ProductOption        `json:"options"`
	Media            []*ProductMedia         `json:"media"`
	Compliance       ProductCompliance       `json:"compliance"`
	ProductNumber    string                  `json:"product_number"`
	ProductTypes     []string                `json:"product_types"`
}

// nolint:gocritic
func (p Product) EncodeSpanner() (interface{}, error) {
	bytes, err := json.Marshal(p)
	if err != nil {
		return nil, err
	}

	return string(bytes), nil
}

func (p *Product) DecodeSpanner(val interface{}) (err error) {
	strVal, _ := val.(string)
	return json.Unmarshal([]byte(strVal), p)
}

func (p *Product) VariantsBarcodeFilled() bool {
	for _, variant := range p.Variants {
		if !variant.BarcodeFilled() {
			return false
		}
	}
	return true
}

func (p *Product) SizeChartFilled() bool {
	return len(p.SizeChart.Images) > 0
}

func (p *Product) CertificationsFilled() bool {
	for _, certification := range p.Certifications {
		if len(certification.Files) == 0 && len(certification.Images) == 0 {
			return false
		}
	}
	return true
}

func (p *Product) GetLength() ProductVariantShippingSetting {
	if len(p.Variants) == 0 {
		return ProductVariantShippingSetting{}
	}

	return p.Variants[0].Length
}

func (p *Product) GetWidth() ProductVariantShippingSetting {
	if len(p.Variants) == 0 {
		return ProductVariantShippingSetting{}
	}

	return p.Variants[0].Width
}

func (p *Product) GetHeight() ProductVariantShippingSetting {
	if len(p.Variants) == 0 {
		return ProductVariantShippingSetting{}
	}

	return p.Variants[0].Height
}

func (p *Product) GetWeight() ProductVariantShippingSetting {
	if len(p.Variants) == 0 {
		return ProductVariantShippingSetting{}
	}

	return p.Variants[0].Weight
}

func (p *Product) GetMainOption() (*ProductOption, bool) {
	if len(p.Options) == 0 {
		return nil, false
	}

	mainOption := p.Options[0]
	position := mainOption.Position

	for _, option := range p.Options {
		if option.Position < position {
			mainOption = option
			position = option.Position
		}
	}

	return mainOption, true
}

func (p *Product) GetVariantsByOption(optionID, optionValueID string) []*ProductVariant {
	variants := make([]*ProductVariant, 0)
	for i := range p.Variants {
		for j := range p.Variants[i].Options {
			if p.Variants[i].Options[j].SalesChannelOptionID == optionID &&
				p.Variants[i].Options[j].SalesChannelValueID == optionValueID {
				variants = append(variants, p.Variants[i])
			}
		}
	}

	return variants
}

func (p *Product) OverwriteOptions(optionNameMapping map[string]string) {
	if len(p.Options) == 0 {
		return
	}

	// 处理Product的Options
	if len(p.Options) > 0 {
		p.Options = ConvertOptionsWithNameMapping(p.Options, optionNameMapping)
	}

	// 处理所有Variants的Options
	for i := range p.Variants {
		if len(p.Variants[i].Options) > 0 {
			p.Variants[i].Options = ConvertVariantOptionsWithNameMapping(p.Variants[i].Options, optionNameMapping)
		}
	}
}

// ConvertOptionsWithNameMapping option name 长度如果超过 20 个字符，需要从 optionNameMapping 中取映射后的 name
func ConvertOptionsWithNameMapping(options []*ProductOption, optionNameMapping map[string]string) []*ProductOption {
	if len(options) == 0 {
		return []*ProductOption{}
	}

	if optionNameMapping == nil {
		return options
	}

	result := make([]*ProductOption, 0, len(options))
	for i := range options {
		option := &ProductOption{
			Name:                 options[i].Name,
			Position:             options[i].Position,
			Values:               options[i].Values,
			SalesChannelOptionID: options[i].SalesChannelOptionID,
			ValueDetails:         options[i].ValueDetails,
		}

		// 从配置中心的映射中取 Option Name 的映射，并赋值
		if mappedName, exists := optionNameMapping[option.Name]; exists {
			option.Name = mappedName
		}

		result = append(result, option)
	}

	return result
}

// ConvertVariantOptionsWithNameMapping variant option name 长度如果超过 20 个字符，需要从 optionNameMapping 中取映射后的 name
func ConvertVariantOptionsWithNameMapping(options []*ProductVariantOption, optionNameMapping map[string]string) []*ProductVariantOption {
	if len(options) == 0 {
		return []*ProductVariantOption{}
	}

	if optionNameMapping == nil {
		return options
	}

	result := make([]*ProductVariantOption, 0, len(options))
	for i := range options {
		option := &ProductVariantOption{
			Name:                 options[i].Name,
			Value:                options[i].Value,
			SalesChannelOptionID: options[i].SalesChannelOptionID,
			SalesChannelValueID:  options[i].SalesChannelValueID,
		}

		// 从配置中心的映射中取 Option Name 的映射，并赋值
		if mappedName, exists := optionNameMapping[option.Name]; exists {
			option.Name = mappedName
		}

		result = append(result, option)
	}

	return result
}
