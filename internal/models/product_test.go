package models

import (
	"reflect"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestProductVariant_IsBelongToOption(t *testing.T) {
	variant := &ProductVariant{
		Options: []*ProductVariantOption{
			{
				SalesChannelOptionID: "opt1",
				SalesChannelValueID:  "val1",
			},
			{
				SalesChannelOptionID: "opt2",
				SalesChannelValueID:  "val2",
			},
		},
	}
	assert.True(t, variant.IsBelongToOption("opt1", "val1"))
	assert.True(t, variant.IsBelongToOption("opt2", "val2"))
	assert.False(t, variant.IsBelongToOption("opt3", "val3"))
}

func TestProductVariant_LengthExceedTTSMaxValue(t *testing.T) {
	v := &ProductVariant{
		Length: ProductVariantShippingSetting{Unit: "cm", Value: 100},
	}
	assert.True(t, v.LengthExceedTTSMaxValue())
	v.Length.Value = 99.9
	assert.False(t, v.LengthExceedTTSMaxValue())
	v.Length.Unit = "mm"
	v.Length.Value = 200
	assert.False(t, v.LengthExceedTTSMaxValue())
}

func TestProduct_EncodeDecodeSpanner(t *testing.T) {
	p := Product{Title: "test", Variants: []*ProductVariant{{Sku: "sku1"}}}
	val, err := p.EncodeSpanner()
	assert.NoError(t, err)
	var p2 Product
	assert.NoError(t, p2.DecodeSpanner(val))
	assert.Equal(t, p.Title, p2.Title)
	assert.Equal(t, p.Variants[0].Sku, p2.Variants[0].Sku)
}

func TestProduct_GetVariantsByOption(t *testing.T) {
	v1 := &ProductVariant{ID: "1", Options: []*ProductVariantOption{{SalesChannelOptionID: "opt1", SalesChannelValueID: "val1"}}}
	v2 := &ProductVariant{ID: "2", Options: []*ProductVariantOption{{SalesChannelOptionID: "opt2", SalesChannelValueID: "val2"}}}
	p := &Product{Variants: []*ProductVariant{v1, v2}}
	res := p.GetVariantsByOption("opt1", "val1")
	assert.Len(t, res, 1)
	assert.Equal(t, "1", res[0].ID)
	res2 := p.GetVariantsByOption("opt2", "val2")
	assert.Len(t, res2, 1)
	assert.Equal(t, "2", res2[0].ID)
	res3 := p.GetVariantsByOption("opt3", "val3")
	assert.Len(t, res3, 0)
}

func TestProduct_CertificationsFilled(t *testing.T) {
	tests := []struct {
		name     string
		product  Product
		expected bool
	}{
		{
			name: "Certifications are filled",
			product: Product{
				Certifications: []*ProductCertification{
					{
						SalesChannelID: "test",
						Files: []SalesChannelFile{
							{
								SalesChannelID: "test",
								URL:            "test",
							},
						},
						Images: []SalesChannelFile{
							{
								SalesChannelID: "test",
								URL:            "test",
							},
						},
					},
				},
			},
			expected: true,
		},
		{
			name: "Certifications are filled",
			product: Product{
				Certifications: []*ProductCertification{
					{
						SalesChannelID: "test",
						Files: []SalesChannelFile{
							{
								SalesChannelID: "test",
								URL:            "test",
							},
						},
					},
				},
			},
			expected: true,
		},
		{
			name: "Certifications are filled",
			product: Product{
				Certifications: []*ProductCertification{
					{
						SalesChannelID: "test",
						Images: []SalesChannelFile{
							{
								SalesChannelID: "test",
								URL:            "test",
							},
						},
					},
				},
			},
			expected: true,
		},
		{
			name: "Certifications are not filled",
			product: Product{
				Certifications: []*ProductCertification{
					{
						SalesChannelID: "test",
						Files:          []SalesChannelFile{},
						Images:         []SalesChannelFile{},
					},
				},
			},
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tt.product.CertificationsFilled()
			require.Equal(t, tt.expected, result)
		})
	}
}

func TestProduct_SizeChartFilled(t *testing.T) {
	tests := []struct {
		name     string
		product  Product
		expected bool
	}{
		{
			name: "Size chart is filled",
			product: Product{
				SizeChart: ProductSizeChart{
					Images: []SalesChannelFile{
						{
							SalesChannelID: "test",
							URL:            "test",
						},
					},
				},
			},
			expected: true,
		},
		{
			name: "Size chart is not filled",
			product: Product{
				SizeChart: ProductSizeChart{
					Images: []SalesChannelFile{},
				},
			},
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tt.product.SizeChartFilled()
			require.Equal(t, tt.expected, result)
		})
	}
}

func TestProduct_VariantsBarcodeFilled(t *testing.T) {
	tests := []struct {
		name     string
		product  Product
		expected bool
	}{
		{
			name: "All variants' barcodes are filled",
			product: Product{
				Variants: []*ProductVariant{
					{
						Barcode: ProductVariantBarcode{
							Value: "test",
							Type:  "test",
						},
					},
					{
						Barcode: ProductVariantBarcode{
							Value: "test",
							Type:  "test",
						},
					},
				},
			},
			expected: true,
		},
		{
			name: "At least one variant's barcode is not filled",
			product: Product{
				Variants: []*ProductVariant{
					{
						Barcode: ProductVariantBarcode{
							Value: "test",
							Type:  "test",
						},
					},
					{
						Barcode: ProductVariantBarcode{
							Value: "",
							Type:  "test",
						},
					},
				},
			},
			expected: false,
		},
		{
			name: "At least one variant's barcode is not filled",
			product: Product{
				Variants: []*ProductVariant{
					{
						Barcode: ProductVariantBarcode{
							Value: "test",
							Type:  "test",
						},
					},
					{
						Barcode: ProductVariantBarcode{
							Value: "test",
							Type:  "",
						},
					},
				},
			},
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tt.product.VariantsBarcodeFilled()
			require.Equal(t, tt.expected, result)
		})
	}
}

func TestProductVariant_BarcodeFilled(t *testing.T) {
	tests := []struct {
		name     string
		variant  ProductVariant
		expected bool
	}{
		{
			name: "Barcode is filled",
			variant: ProductVariant{
				Barcode: ProductVariantBarcode{
					Value: "test",
					Type:  "test",
				},
			},
			expected: true,
		},
		{
			name: "Barcode is not filled",
			variant: ProductVariant{
				Barcode: ProductVariantBarcode{
					Value: "",
					Type:  "test",
				},
			},
			expected: false,
		},
		{
			name: "Barcode is not filled",
			variant: ProductVariant{
				Barcode: ProductVariantBarcode{
					Value: "test",
					Type:  "",
				},
			},
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tt.variant.BarcodeFilled()
			require.Equal(t, tt.expected, result)
		})
	}
}

func TestProductVariant_LengthFilled(t *testing.T) {
	tests := []struct {
		name     string
		variant  ProductVariant
		expected bool
	}{
		{
			name: "Length is filled",
			variant: ProductVariant{
				Length: ProductVariantShippingSetting{
					Unit:  "cm",
					Value: 10,
				},
			},
			expected: true,
		},
		{
			name: "Length Unit is not filled",
			variant: ProductVariant{
				Length: ProductVariantShippingSetting{
					Unit:  "",
					Value: 0,
				},
			},
			expected: false,
		},
		{
			name: "Length Value is invalid",
			variant: ProductVariant{
				Length: ProductVariantShippingSetting{
					Unit:  "cm",
					Value: 0,
				},
			},
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tt.variant.LengthFilled()
			require.Equal(t, tt.expected, result)
		})
	}
}

func TestProductVariant_WidthFilled(t *testing.T) {
	tests := []struct {
		name     string
		variant  ProductVariant
		expected bool
	}{
		{
			name: "Width is filled",
			variant: ProductVariant{
				Width: ProductVariantShippingSetting{
					Unit:  "cm",
					Value: 10,
				},
			},
			expected: true,
		},
		{
			name: "Width Unit is not filled",
			variant: ProductVariant{
				Width: ProductVariantShippingSetting{
					Unit:  "",
					Value: 0,
				},
			},
			expected: false,
		},
		{
			name: "Width is value is invalid",
			variant: ProductVariant{
				Width: ProductVariantShippingSetting{
					Unit:  "cm",
					Value: 0,
				},
			},
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tt.variant.WidthFilled()
			require.Equal(t, tt.expected, result)
		})
	}
}

func TestProductVariant_HeightFilled(t *testing.T) {
	tests := []struct {
		name     string
		variant  ProductVariant
		expected bool
	}{
		{
			name: "Height is filled",
			variant: ProductVariant{
				Height: ProductVariantShippingSetting{
					Unit:  "cm",
					Value: 10,
				},
			},
			expected: true,
		},
		{
			name: "Height Unit is not filled",
			variant: ProductVariant{
				Height: ProductVariantShippingSetting{
					Unit:  "",
					Value: 0,
				},
			},
			expected: false,
		},
		{
			name: "Height Value is invalid",
			variant: ProductVariant{
				Height: ProductVariantShippingSetting{
					Unit:  "cm",
					Value: 0,
				},
			},
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tt.variant.HeightFilled()
			require.Equal(t, tt.expected, result)
		})
	}
}

func TestProductVariant_WeightFilled(t *testing.T) {
	tests := []struct {
		name     string
		variant  ProductVariant
		expected bool
	}{
		{
			name: "Weight is filled",
			variant: ProductVariant{
				Weight: ProductVariantShippingSetting{
					Unit:  "kg",
					Value: 10,
				},
			},
			expected: true,
		},
		{
			name: "Weight Unit is not filled",
			variant: ProductVariant{
				Weight: ProductVariantShippingSetting{
					Unit:  "",
					Value: 0,
				},
			},
			expected: false,
		},
		{
			name: "Weight Value is invalid",
			variant: ProductVariant{
				Weight: ProductVariantShippingSetting{
					Unit:  "kg",
					Value: 0,
				},
			},
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tt.variant.WeightFilled()
			require.Equal(t, tt.expected, result)
		})
	}
}

func TestProduct_GetLength(t *testing.T) {
	type fields struct {
		Variants []*ProductVariant
	}
	tests := []struct {
		name   string
		fields fields
		want   ProductVariantShippingSetting
	}{
		// TODO: Add test cases.
		{
			name: "Get length",
			fields: fields{
				Variants: []*ProductVariant{
					{
						Length: ProductVariantShippingSetting{
							Unit:  "cm",
							Value: 10,
						},
					},
				},
			},
			want: ProductVariantShippingSetting{
				Unit:  "cm",
				Value: 10,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := &Product{
				Variants: tt.fields.Variants,
			}
			if got := p.GetLength(); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetLength() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestProduct_GetWidth(t *testing.T) {
	type fields struct {
		Variants []*ProductVariant
	}
	tests := []struct {
		name   string
		fields fields
		want   ProductVariantShippingSetting
	}{
		// TODO: Add test cases.
		{
			name: "Get width",
			fields: fields{
				Variants: []*ProductVariant{
					{
						Width: ProductVariantShippingSetting{
							Unit:  "cm",
							Value: 10,
						},
					},
				},
			},
			want: ProductVariantShippingSetting{
				Unit:  "cm",
				Value: 10,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := &Product{
				Variants: tt.fields.Variants,
			}
			if got := p.GetWidth(); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetWidth() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestProduct_GetHeight(t *testing.T) {
	type fields struct {
		Variants []*ProductVariant
	}
	tests := []struct {
		name   string
		fields fields
		want   ProductVariantShippingSetting
	}{
		{
			name: "Get height",
			fields: fields{
				Variants: []*ProductVariant{
					{
						Height: ProductVariantShippingSetting{
							Unit:  "cm",
							Value: 10,
						},
					},
				},
			},
			want: ProductVariantShippingSetting{
				Unit:  "cm",
				Value: 10,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := &Product{
				Variants: tt.fields.Variants,
			}
			if got := p.GetHeight(); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetHeight() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestProduct_GetWeight(t *testing.T) {
	type fields struct {
		Variants []*ProductVariant
	}
	tests := []struct {
		name   string
		fields fields
		want   ProductVariantShippingSetting
	}{
		{
			name: "Get weight",
			fields: fields{
				Variants: []*ProductVariant{
					{
						Weight: ProductVariantShippingSetting{
							Unit:  "kg",
							Value: 10,
						},
					},
				},
			},
			want: ProductVariantShippingSetting{
				Unit:  "kg",
				Value: 10,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := &Product{
				Variants: tt.fields.Variants,
			}
			if got := p.GetWeight(); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetWeight() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestProductVariant_isSingleVariant(t *testing.T) {
	tests := []struct {
		name     string
		variant  ProductVariant
		expected bool
	}{
		{
			name: "No options",
			variant: ProductVariant{
				Options: []*ProductVariantOption{},
			},
			expected: true,
		},
		{
			name: "One option with empty name",
			variant: ProductVariant{
				Options: []*ProductVariantOption{
					{Name: "", Value: "test"},
				},
			},
			expected: true,
		},
		{
			name: "One option with empty value",
			variant: ProductVariant{
				Options: []*ProductVariantOption{
					{Name: "test", Value: ""},
				},
			},
			expected: true,
		},
		{
			name: "One option with empty name and value",
			variant: ProductVariant{
				Options: []*ProductVariantOption{
					{Name: "", Value: ""},
				},
			},
			expected: true,
		},
		{
			name: "One option with non-empty name and value",
			variant: ProductVariant{
				Options: []*ProductVariantOption{
					{Name: "Color", Value: "Red"},
				},
			},
			expected: false,
		},
		{
			name: "Multiple options",
			variant: ProductVariant{
				Options: []*ProductVariantOption{
					{Name: "Color", Value: "Red"},
					{Name: "Size", Value: "M"},
				},
			},
			expected: false,
		},
		{
			name: "Multiple options with empty values",
			variant: ProductVariant{
				Options: []*ProductVariantOption{
					{Name: "Color", Value: ""},
					{Name: "Size", Value: ""},
				},
			},
			expected: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tt.variant.IsSingleVariant()
			require.Equal(t, tt.expected, result)
		})
	}
}

func TestProductVariant_IsDiff(t *testing.T) {
	tests := []struct {
		name     string
		variant1 ProductVariant
		variant2 ProductVariant
		setting  VariantCompareSetting
		expected bool
	}{
		{
			name:     "Different SKUs",
			variant1: ProductVariant{Sku: "sku1"},
			variant2: ProductVariant{Sku: "sku2"},
			setting:  VariantCompareSetting{},
			expected: true,
		},
		{
			name: "Same SKUs, different barcodes",
			variant1: ProductVariant{
				Sku:     "sku1",
				Barcode: ProductVariantBarcode{Value: "123", Type: "EAN"},
			},
			variant2: ProductVariant{
				Sku:     "sku1",
				Barcode: ProductVariantBarcode{Value: "456", Type: "EAN"},
			},
			setting:  VariantCompareSetting{},
			expected: true,
		},
		{
			name: "Same SKUs and barcodes, different dimensions",
			variant1: ProductVariant{
				Sku:    "sku1",
				Width:  ProductVariantShippingSetting{Unit: "cm", Value: 10},
				Height: ProductVariantShippingSetting{Unit: "cm", Value: 20},
			},
			variant2: ProductVariant{
				Sku:    "sku1",
				Width:  ProductVariantShippingSetting{Unit: "cm", Value: 15},
				Height: ProductVariantShippingSetting{Unit: "cm", Value: 25},
			},
			setting:  VariantCompareSetting{},
			expected: true,
		},
		{
			name: "Same SKUs, barcodes, and dimensions",
			variant1: ProductVariant{
				Sku:    "sku1",
				Width:  ProductVariantShippingSetting{Unit: "cm", Value: 10},
				Height: ProductVariantShippingSetting{Unit: "cm", Value: 20},
			},
			variant2: ProductVariant{
				Sku:    "sku1",
				Width:  ProductVariantShippingSetting{Unit: "cm", Value: 10},
				Height: ProductVariantShippingSetting{Unit: "cm", Value: 20},
			},
			setting:  VariantCompareSetting{},
			expected: false,
		},
		{
			name: "Ignore barcode comparison",
			variant1: ProductVariant{
				Sku:     "sku1",
				Barcode: ProductVariantBarcode{Value: "123", Type: "EAN"},
			},
			variant2: ProductVariant{
				Sku:     "sku1",
				Barcode: ProductVariantBarcode{Value: "456", Type: "EAN"},
			},
			setting:  VariantCompareSetting{IgnoreBarcode: true},
			expected: false,
		},
		{
			name: "Different options values",
			variant1: ProductVariant{
				Sku:     "sku1",
				Options: []*ProductVariantOption{{Name: "Color", Value: "Red"}},
			},
			variant2: ProductVariant{
				Sku:     "sku1",
				Options: []*ProductVariantOption{{Name: "Color", Value: "Blue"}},
			},
			setting:  VariantCompareSetting{},
			expected: true,
		},
		{
			name: "Same options",
			variant1: ProductVariant{
				Sku:     "sku1",
				Options: []*ProductVariantOption{{Name: "Color", Value: "Red"}},
			},
			variant2: ProductVariant{
				Sku:     "sku1",
				Options: []*ProductVariantOption{{Name: "Color", Value: "Red"}},
			},
			setting:  VariantCompareSetting{},
			expected: false,
		},
		{
			name: "available diff",
			variant1: ProductVariant{
				Sku:       "sku1",
				Options:   []*ProductVariantOption{{Name: "Color", Value: "Red"}},
				Available: "available",
			},
			variant2: ProductVariant{
				Sku:       "sku1",
				Options:   []*ProductVariantOption{{Name: "Color", Value: "Red"}},
				Available: "unavailable",
			},
			setting:  VariantCompareSetting{},
			expected: true,
		},
		{
			name: "options order",
			variant1: ProductVariant{
				Sku:     "sku1",
				Options: []*ProductVariantOption{{Name: "Color", Value: "Red"}, {Name: "Size", Value: "X"}},
			},
			variant2: ProductVariant{
				Sku:     "sku1",
				Options: []*ProductVariantOption{{Name: "Size", Value: "X"}, {Name: "Color", Value: "Red"}},
			},
			setting:  VariantCompareSetting{},
			expected: false,
		},
		{
			name: "options order",
			variant1: ProductVariant{
				Sku:     "sku1",
				Options: []*ProductVariantOption{{Name: "Color", Value: "Red"}, {Name: "Size", Value: "XL"}},
			},
			variant2: ProductVariant{
				Sku:     "sku1",
				Options: []*ProductVariantOption{{Name: "Size", Value: "X"}, {Name: "Color", Value: "Red"}},
			},
			setting:  VariantCompareSetting{},
			expected: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tt.variant1.IsDiff(&tt.variant2, tt.setting, nil)
			require.Equal(t, tt.expected, result)
		})
	}
}

func TestProductVariantBarcode_IsDiff(t *testing.T) {
	tests := []struct {
		name     string
		barcode1 ProductVariantBarcode
		barcode2 ProductVariantBarcode
		expected bool
	}{
		{
			name: "Both barcodes empty",
			barcode1: ProductVariantBarcode{
				Value: "",
				Type:  "",
			},
			barcode2: ProductVariantBarcode{
				Value: "",
				Type:  "",
			},
			expected: false,
		},
		{
			name: "One barcode empty",
			barcode1: ProductVariantBarcode{
				Value: "123",
				Type:  "EAN",
			},
			barcode2: ProductVariantBarcode{
				Value: "",
				Type:  "",
			},
			expected: true,
		},
		{
			name: "Different values, same type",
			barcode1: ProductVariantBarcode{
				Value: "123",
				Type:  "EAN",
			},
			barcode2: ProductVariantBarcode{
				Value: "456",
				Type:  "EAN",
			},
			expected: true,
		},
		{
			name: "Same values, different types",
			barcode1: ProductVariantBarcode{
				Value: "123",
				Type:  "EAN",
			},
			barcode2: ProductVariantBarcode{
				Value: "123",
				Type:  "UPC",
			},
			expected: true,
		},
		{
			name: "Same values and types",
			barcode1: ProductVariantBarcode{
				Value: "123",
				Type:  "EAN",
			},
			barcode2: ProductVariantBarcode{
				Value: "123",
				Type:  "EAN",
			},
			expected: false,
		},
		{
			name: "Both barcodes empty",
			barcode1: ProductVariantBarcode{
				Value: "",
				Type:  "EAN",
			},
			barcode2: ProductVariantBarcode{
				Value: "",
				Type:  "",
			},
			expected: false,
		},
		{
			name: "Both barcodes empty",
			barcode1: ProductVariantBarcode{
				Value: "",
				Type:  "EAN",
			},
			barcode2: ProductVariantBarcode{
				Value: "",
				Type:  "UPS",
			},
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tt.barcode1.IsDiff(tt.barcode2)
			require.Equal(t, tt.expected, result)
		})
	}
}

func Test_GetMainProductOption(t *testing.T) {
	tests := []struct {
		name     string
		product  *Product
		expected *ProductOption
		found    bool
	}{
		{
			name: "no options",
			product: &Product{
				Options: []*ProductOption{},
			},
			expected: nil,
			found:    false,
		},
		{
			name: "single option",
			product: &Product{
				Options: []*ProductOption{
					{Name: "Option1", Position: 1},
				},
			},
			expected: &ProductOption{Name: "Option1", Position: 1},
			found:    true,
		},
		{
			name: "multiple options with different positions",
			product: &Product{
				Options: []*ProductOption{
					{Name: "Option1", Position: 2},
					{Name: "Option2", Position: 1},
				},
			},
			expected: &ProductOption{Name: "Option2", Position: 1},
			found:    true,
		},
		{
			name: "multiple options with same positions",
			product: &Product{
				Options: []*ProductOption{
					{Name: "Option1", Position: 1},
					{Name: "Option2", Position: 1},
				},
			},
			expected: &ProductOption{Name: "Option1", Position: 1},
			found:    true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, found := tt.product.GetMainOption()
			if !reflect.DeepEqual(result, tt.expected) || found != tt.found {
				t.Errorf("GetMainProductOption() = %v, %v, expected %v, %v", result, found, tt.expected, tt.found)
			}
		})
	}
}

func TestConvertOptionsWithNameMapping(t *testing.T) {
	tests := []struct {
		name              string
		options           []*ProductOption
		optionNameMapping map[string]string
		expected          []*ProductOption
	}{
		{
			name:              "Empty options",
			options:           []*ProductOption{},
			optionNameMapping: map[string]string{"color": "颜色"},
			expected:          []*ProductOption{},
		},
		{
			name: "Nil optionNameMapping",
			options: []*ProductOption{
				{
					Name:                 "color",
					Position:             1,
					Values:               []string{"red", "blue"},
					SalesChannelOptionID: "option1",
					ValueDetails: []ProductOptionValueDetail{
						{Value: "red", SalesChannelValueID: "value1"},
					},
				},
			},
			optionNameMapping: nil,
			expected: []*ProductOption{
				{
					Name:                 "color",
					Position:             1,
					Values:               []string{"red", "blue"},
					SalesChannelOptionID: "option1",
					ValueDetails: []ProductOptionValueDetail{
						{Value: "red", SalesChannelValueID: "value1"},
					},
				},
			},
		},
		{
			name: "With name mapping",
			options: []*ProductOption{
				{
					Name:                 "color",
					Position:             1,
					Values:               []string{"red", "blue"},
					SalesChannelOptionID: "option1",
					ValueDetails: []ProductOptionValueDetail{
						{Value: "red", SalesChannelValueID: "value1"},
					},
				},
				{
					Name:                 "size",
					Position:             2,
					Values:               []string{"L", "M"},
					SalesChannelOptionID: "option2",
					ValueDetails: []ProductOptionValueDetail{
						{Value: "L", SalesChannelValueID: "value2"},
					},
				},
			},
			optionNameMapping: map[string]string{
				"color": "颜色",
				"size":  "尺寸",
			},
			expected: []*ProductOption{
				{
					Name:                 "颜色",
					Position:             1,
					Values:               []string{"red", "blue"},
					SalesChannelOptionID: "option1",
					ValueDetails: []ProductOptionValueDetail{
						{Value: "red", SalesChannelValueID: "value1"},
					},
				},
				{
					Name:                 "尺寸",
					Position:             2,
					Values:               []string{"L", "M"},
					SalesChannelOptionID: "option2",
					ValueDetails: []ProductOptionValueDetail{
						{Value: "L", SalesChannelValueID: "value2"},
					},
				},
			},
		},
		{
			name: "Partial name mapping",
			options: []*ProductOption{
				{
					Name:                 "color",
					Position:             1,
					Values:               []string{"red"},
					SalesChannelOptionID: "option1",
				},
				{
					Name:                 "material",
					Position:             2,
					Values:               []string{"cotton"},
					SalesChannelOptionID: "option2",
				},
			},
			optionNameMapping: map[string]string{
				"color": "颜色",
			},
			expected: []*ProductOption{
				{
					Name:                 "颜色",
					Position:             1,
					Values:               []string{"red"},
					SalesChannelOptionID: "option1",
				},
				{
					Name:                 "material",
					Position:             2,
					Values:               []string{"cotton"},
					SalesChannelOptionID: "option2",
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := ConvertOptionsWithNameMapping(tt.options, tt.optionNameMapping)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestConvertVariantOptionsWithNameMapping(t *testing.T) {
	tests := []struct {
		name              string
		options           []*ProductVariantOption
		optionNameMapping map[string]string
		expected          []*ProductVariantOption
	}{
		{
			name:              "Empty options",
			options:           []*ProductVariantOption{},
			optionNameMapping: map[string]string{"color": "颜色"},
			expected:          []*ProductVariantOption{},
		},
		{
			name: "Nil optionNameMapping",
			options: []*ProductVariantOption{
				{
					Name:                 "color",
					Value:                "red",
					SalesChannelOptionID: "option1",
					SalesChannelValueID:  "value1",
				},
			},
			optionNameMapping: nil,
			expected: []*ProductVariantOption{
				{
					Name:                 "color",
					Value:                "red",
					SalesChannelOptionID: "option1",
					SalesChannelValueID:  "value1",
				},
			},
		},
		{
			name: "With name mapping",
			options: []*ProductVariantOption{
				{
					Name:                 "color",
					Value:                "red",
					SalesChannelOptionID: "option1",
					SalesChannelValueID:  "value1",
				},
				{
					Name:                 "size",
					Value:                "L",
					SalesChannelOptionID: "option2",
					SalesChannelValueID:  "value2",
				},
			},
			optionNameMapping: map[string]string{
				"color": "颜色",
				"size":  "尺寸",
			},
			expected: []*ProductVariantOption{
				{
					Name:                 "颜色",
					Value:                "red",
					SalesChannelOptionID: "option1",
					SalesChannelValueID:  "value1",
				},
				{
					Name:                 "尺寸",
					Value:                "L",
					SalesChannelOptionID: "option2",
					SalesChannelValueID:  "value2",
				},
			},
		},
		{
			name: "Partial name mapping",
			options: []*ProductVariantOption{
				{
					Name:                 "color",
					Value:                "red",
					SalesChannelOptionID: "option1",
					SalesChannelValueID:  "value1",
				},
				{
					Name:                 "material",
					Value:                "cotton",
					SalesChannelOptionID: "option2",
					SalesChannelValueID:  "value2",
				},
			},
			optionNameMapping: map[string]string{
				"color": "颜色",
			},
			expected: []*ProductVariantOption{
				{
					Name:                 "颜色",
					Value:                "red",
					SalesChannelOptionID: "option1",
					SalesChannelValueID:  "value1",
				},
				{
					Name:                 "material",
					Value:                "cotton",
					SalesChannelOptionID: "option2",
					SalesChannelValueID:  "value2",
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := ConvertVariantOptionsWithNameMapping(tt.options, tt.optionNameMapping)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestProduct_OverwriteOptions(t *testing.T) {
	tests := []struct {
		name              string
		product           Product
		optionNameMapping map[string]string
		expectedProduct   Product
	}{
		{
			name: "Empty options - should return early",
			product: Product{
				Title:   "Test Product",
				Options: []*ProductOption{},
				Variants: []*ProductVariant{
					{
						ID:      "variant1",
						Options: []*ProductVariantOption{},
					},
				},
			},
			optionNameMapping: map[string]string{"color": "颜色"},
			expectedProduct: Product{
				Title:   "Test Product",
				Options: []*ProductOption{},
				Variants: []*ProductVariant{
					{
						ID:      "variant1",
						Options: []*ProductVariantOption{},
					},
				},
			},
		},
		{
			name: "Nil optionNameMapping",
			product: Product{
				Title: "Test Product",
				Options: []*ProductOption{
					{
						Name:                 "color",
						Position:             1,
						Values:               []string{"red", "blue"},
						SalesChannelOptionID: "option1",
						ValueDetails: []ProductOptionValueDetail{
							{Value: "red", SalesChannelValueID: "value1"},
						},
					},
				},
				Variants: []*ProductVariant{
					{
						ID: "variant1",
						Options: []*ProductVariantOption{
							{
								Name:                 "color",
								Value:                "red",
								SalesChannelOptionID: "option1",
								SalesChannelValueID:  "value1",
							},
						},
					},
				},
			},
			optionNameMapping: nil,
			expectedProduct: Product{
				Title: "Test Product",
				Options: []*ProductOption{
					{
						Name:                 "color",
						Position:             1,
						Values:               []string{"red", "blue"},
						SalesChannelOptionID: "option1",
						ValueDetails: []ProductOptionValueDetail{
							{Value: "red", SalesChannelValueID: "value1"},
						},
					},
				},
				Variants: []*ProductVariant{
					{
						ID: "variant1",
						Options: []*ProductVariantOption{
							{
								Name:                 "color",
								Value:                "red",
								SalesChannelOptionID: "option1",
								SalesChannelValueID:  "value1",
							},
						},
					},
				},
			},
		},
		{
			name: "With name mapping for both Product Options and Variant Options",
			product: Product{
				Title: "Test Product",
				Options: []*ProductOption{
					{
						Name:                 "color",
						Position:             1,
						Values:               []string{"red", "blue"},
						SalesChannelOptionID: "option1",
						ValueDetails: []ProductOptionValueDetail{
							{Value: "red", SalesChannelValueID: "value1"},
							{Value: "blue", SalesChannelValueID: "value2"},
						},
					},
					{
						Name:                 "size",
						Position:             2,
						Values:               []string{"L", "M"},
						SalesChannelOptionID: "option2",
						ValueDetails: []ProductOptionValueDetail{
							{Value: "L", SalesChannelValueID: "value3"},
						},
					},
				},
				Variants: []*ProductVariant{
					{
						ID: "variant1",
						Options: []*ProductVariantOption{
							{
								Name:                 "color",
								Value:                "red",
								SalesChannelOptionID: "option1",
								SalesChannelValueID:  "value1",
							},
							{
								Name:                 "size",
								Value:                "L",
								SalesChannelOptionID: "option2",
								SalesChannelValueID:  "value3",
							},
						},
					},
					{
						ID: "variant2",
						Options: []*ProductVariantOption{
							{
								Name:                 "color",
								Value:                "blue",
								SalesChannelOptionID: "option1",
								SalesChannelValueID:  "value2",
							},
						},
					},
				},
			},
			optionNameMapping: map[string]string{
				"color": "颜色",
				"size":  "尺寸",
			},
			expectedProduct: Product{
				Title: "Test Product",
				Options: []*ProductOption{
					{
						Name:                 "颜色",
						Position:             1,
						Values:               []string{"red", "blue"},
						SalesChannelOptionID: "option1",
						ValueDetails: []ProductOptionValueDetail{
							{Value: "red", SalesChannelValueID: "value1"},
							{Value: "blue", SalesChannelValueID: "value2"},
						},
					},
					{
						Name:                 "尺寸",
						Position:             2,
						Values:               []string{"L", "M"},
						SalesChannelOptionID: "option2",
						ValueDetails: []ProductOptionValueDetail{
							{Value: "L", SalesChannelValueID: "value3"},
						},
					},
				},
				Variants: []*ProductVariant{
					{
						ID: "variant1",
						Options: []*ProductVariantOption{
							{
								Name:                 "颜色",
								Value:                "red",
								SalesChannelOptionID: "option1",
								SalesChannelValueID:  "value1",
							},
							{
								Name:                 "尺寸",
								Value:                "L",
								SalesChannelOptionID: "option2",
								SalesChannelValueID:  "value3",
							},
						},
					},
					{
						ID: "variant2",
						Options: []*ProductVariantOption{
							{
								Name:                 "颜色",
								Value:                "blue",
								SalesChannelOptionID: "option1",
								SalesChannelValueID:  "value2",
							},
						},
					},
				},
			},
		},
		{
			name: "Partial name mapping - only some options mapped",
			product: Product{
				Title: "Test Product",
				Options: []*ProductOption{
					{
						Name:                 "color",
						Position:             1,
						SalesChannelOptionID: "option1",
					},
					{
						Name:                 "material",
						Position:             2,
						SalesChannelOptionID: "option2",
					},
				},
				Variants: []*ProductVariant{
					{
						ID: "variant1",
						Options: []*ProductVariantOption{
							{
								Name:                 "color",
								Value:                "red",
								SalesChannelOptionID: "option1",
								SalesChannelValueID:  "value1",
							},
							{
								Name:                 "material",
								Value:                "cotton",
								SalesChannelOptionID: "option2",
								SalesChannelValueID:  "value2",
							},
						},
					},
				},
			},
			optionNameMapping: map[string]string{
				"color": "颜色",
				// material is not mapped
			},
			expectedProduct: Product{
				Title: "Test Product",
				Options: []*ProductOption{
					{
						Name:                 "颜色",
						Position:             1,
						SalesChannelOptionID: "option1",
					},
					{
						Name:                 "material", // unchanged
						Position:             2,
						SalesChannelOptionID: "option2",
					},
				},
				Variants: []*ProductVariant{
					{
						ID: "variant1",
						Options: []*ProductVariantOption{
							{
								Name:                 "颜色",
								Value:                "red",
								SalesChannelOptionID: "option1",
								SalesChannelValueID:  "value1",
							},
							{
								Name:                 "material", // unchanged
								Value:                "cotton",
								SalesChannelOptionID: "option2",
								SalesChannelValueID:  "value2",
							},
						},
					},
				},
			},
		},
		{
			name: "Variant without options - should skip",
			product: Product{
				Title: "Test Product",
				Options: []*ProductOption{
					{
						Name:                 "color",
						Position:             1,
						SalesChannelOptionID: "option1",
					},
				},
				Variants: []*ProductVariant{
					{
						ID:      "variant1",
						Options: []*ProductVariantOption{}, // empty options
					},
					{
						ID: "variant2",
						Options: []*ProductVariantOption{
							{
								Name:                 "color",
								Value:                "red",
								SalesChannelOptionID: "option1",
								SalesChannelValueID:  "value1",
							},
						},
					},
				},
			},
			optionNameMapping: map[string]string{
				"color": "颜色",
			},
			expectedProduct: Product{
				Title: "Test Product",
				Options: []*ProductOption{
					{
						Name:                 "颜色",
						Position:             1,
						SalesChannelOptionID: "option1",
					},
				},
				Variants: []*ProductVariant{
					{
						ID:      "variant1",
						Options: []*ProductVariantOption{}, // unchanged
					},
					{
						ID: "variant2",
						Options: []*ProductVariantOption{
							{
								Name:                 "颜色",
								Value:                "red",
								SalesChannelOptionID: "option1",
								SalesChannelValueID:  "value1",
							},
						},
					},
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Make a copy to avoid modifying the original test data
			productCopy := tt.product

			// Call the method (which modifies in-place)
			productCopy.OverwriteOptions(tt.optionNameMapping)

			// Compare the result
			assert.Equal(t, tt.expectedProduct, productCopy)
		})
	}
}

func TestProduct_OverwriteOptions_InPlaceModification(t *testing.T) {
	t.Run("Should modify the product in-place", func(t *testing.T) {
		product := &Product{
			Title: "Test Product",
			Options: []*ProductOption{
				{
					Name:                 "color",
					Position:             1,
					SalesChannelOptionID: "option1",
				},
			},
			Variants: []*ProductVariant{
				{
					ID: "variant1",
					Options: []*ProductVariantOption{
						{
							Name:                 "color",
							Value:                "red",
							SalesChannelOptionID: "option1",
							SalesChannelValueID:  "value1",
						},
					},
				},
			},
		}

		optionNameMapping := map[string]string{
			"color": "颜色",
		}

		// Store original pointer to verify in-place modification
		originalOptionsPtr := product.Options[0]
		originalVariantOptionsPtr := product.Variants[0].Options[0]

		product.OverwriteOptions(optionNameMapping)

		// Verify the product was modified in-place
		assert.Equal(t, "颜色", product.Options[0].Name)
		assert.Equal(t, "颜色", product.Variants[0].Options[0].Name)

		// Verify that new option objects were created (not in-place modification of the option objects themselves)
		assert.NotSame(t, originalOptionsPtr, product.Options[0])
		assert.NotSame(t, originalVariantOptionsPtr, product.Variants[0].Options[0])
	})
}
