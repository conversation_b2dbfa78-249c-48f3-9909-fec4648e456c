package models

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func TestProductAI_Spanner(t *testing.T) {
	ai := ProductAI{OptimizationInfo: OptimizationInfo{State: "enabled"}}
	val, err := ai.EncodeSpanner()
	assert.NoError(t, err)
	var ai2 ProductAI
	assert.NoError(t, ai2.DecodeSpanner(val))
	assert.Equal(t, ai, ai2)
}

func TestCompliance_Spanner(t *testing.T) {
	c := Compliance{
		Manufacturers:      []ComplianceManufacturer{{SalesChannelID: "1"}},
		ResponsiblePersons: []ComplianceResponsiblePerson{{SalesChannelID: "2"}},
	}
	val, err := c.EncodeSpanner()
	assert.NoError(t, err)
	var c2 Compliance
	assert.NoError(t, c2.DecodeSpanner(val))
	assert.Equal(t, c, c2)
}

func TestBrand_Spanner(t *testing.T) {
	b := Brand{ID: "brand1"}
	val, err := b.EncodeSpanner()
	assert.NoError(t, err)
	var b2 Brand
	assert.NoError(t, b2.DecodeSpanner(val))
	assert.Equal(t, b, b2)
}

func TestInventorySync_Enabled(t *testing.T) {
	inv := InventorySync{AutoSync: "enabled"}
	assert.True(t, inv.EnabledAutoSync())
	inv.AutoSync = "disabled"
	assert.False(t, inv.EnabledAutoSync())
}

func TestLowQuantityThreshold_Enabled(t *testing.T) {
	l := LowQuantityThreshold{State: "enabled"}
	assert.True(t, l.Enabled())
	l.State = "disabled"
	assert.False(t, l.Enabled())
}

func TestActiveWarehouse_Enabled(t *testing.T) {
	w := ActiveWarehouse{State: "enabled"}
	assert.True(t, w.Enabled())
	w.State = "disabled"
	assert.False(t, w.Enabled())
}

func TestInventorySync_EnabledMultiWarehouse(t *testing.T) {
	inv := InventorySync{
		ActiveWarehouses: []ActiveWarehouse{
			{State: "disabled"},
			{State: "enabled"},
		},
	}
	assert.True(t, inv.EnabledMultiWarehouse())
	inv.ActiveWarehouses[1].State = "disabled"
	assert.False(t, inv.EnabledMultiWarehouse())
}

func TestInventorySync_GatherEnableMultiWarehouseIDs(t *testing.T) {
	inv := InventorySync{
		ActiveWarehouses: []ActiveWarehouse{
			{State: "enabled", SourceWarehouseId: "w1"},
			{State: "disabled", SourceWarehouseId: "w2"},
		},
	}
	set := inv.GatherEnableMultiWarehouseIDs()
	assert.True(t, set.Contains("w1"))
	assert.False(t, set.Contains("w2"))
}

func TestInventorySync_FollowSourceAllowBackorderEnabled(t *testing.T) {
	inv := InventorySync{FollowSourceAllowBackorder: "enabled"}
	assert.True(t, inv.FollowSourceAllowBackorderEnabled())
	inv.FollowSourceAllowBackorder = "disabled"
	assert.False(t, inv.FollowSourceAllowBackorderEnabled())
}

func TestInventorySync_Spanner(t *testing.T) {
	inv := InventorySync{AutoSync: "enabled", AvailableQuantityPercent: 0.5}
	val, err := inv.EncodeSpanner()
	assert.NoError(t, err)
	var inv2 InventorySync
	assert.NoError(t, inv2.DecodeSpanner(val))
	assert.Equal(t, inv.AutoSync, inv2.AutoSync)
}

func TestPriceSync_Spanner(t *testing.T) {
	ps := PriceSync{SourceField: "price", AutoSync: "enabled", Rules: []PriceRules{{ValueType: "fixed", Value: "10"}}}
	val, err := ps.EncodeSpanner()
	assert.NoError(t, err)
	var ps2 PriceSync
	assert.NoError(t, ps2.DecodeSpanner(val))
	assert.Equal(t, ps, ps2)
}

func TestProductSync_Spanner(t *testing.T) {
	ps := ProductSync{UpdateDetail: UpdateDetail{Fields: []string{"title"}, AutoSync: "enabled"}, UpdateVariants: UpdateVariants{AutoSync: "enabled"}, FulfillmentService: "default"}
	val, err := ps.EncodeSpanner()
	assert.NoError(t, err)
	var ps2 ProductSync
	assert.NoError(t, ps2.DecodeSpanner(val))
	assert.Equal(t, ps, ps2)
}

func TestAutoLink_SpannerAndEnabled(t *testing.T) {
	a := AutoLink{State: "enabled"}
	val, err := a.EncodeSpanner()
	assert.NoError(t, err)
	var a2 AutoLink
	assert.NoError(t, a2.DecodeSpanner(val))
	assert.Equal(t, a, a2)
	assert.True(t, a.Enabled())
	a.State = "disabled"
	assert.False(t, a.Enabled())
}

func TestCurrencyConvertor_GenerateCacheKey(t *testing.T) {
	c := CurrencyConvertor{SourceCurrency: "USD", SalesChannelCurrency: "CNY"}
	assert.Equal(t, "USD-CNY", c.GenerateCacheKey())
}

func TestCurrencyConvertors_Spanner(t *testing.T) {
	cc := CurrencyConvertors{{SourceCurrency: "USD", SalesChannelCurrency: "CNY", CustomExchangeRate: 7.0, LastEffectAt: time.Now()}}
	val, err := cc.EncodeSpanner()
	assert.NoError(t, err)
	var cc2 CurrencyConvertors
	assert.NoError(t, cc2.DecodeSpanner(val))
	assert.Equal(t, cc[0].SourceCurrency, cc2[0].SourceCurrency)
}

func TestDimensionsMapping_Spanner(t *testing.T) {
	d := DimensionsMapping{Length: SourceMetafield{Name: "l", Unit: "cm"}, Width: SourceMetafield{Name: "w", Unit: "cm"}, Height: SourceMetafield{Name: "h", Unit: "cm"}}
	val, err := d.EncodeSpanner()
	assert.NoError(t, err)
	var d2 DimensionsMapping
	assert.NoError(t, d2.DecodeSpanner(val))
	assert.Equal(t, d, d2)
}

func TestMarketsCurrencyMapping_Spanner(t *testing.T) {
	m := MarketsCurrencyMapping{Currency: "USD", CountryRegion: "US"}
	val, err := m.EncodeSpanner()
	assert.NoError(t, err)
	var m2 MarketsCurrencyMapping
	assert.NoError(t, m2.DecodeSpanner(val))
	assert.Equal(t, m, m2)
	// 空字符串应返回 nil
	var m3 MarketsCurrencyMapping
	assert.NoError(t, m3.DecodeSpanner(""))
}

func TestDecodeSpanner_InvalidJSON(t *testing.T) {
	var b Brand
	assert.Error(t, b.DecodeSpanner("{"))
	var c Compliance
	assert.Error(t, c.DecodeSpanner("{"))
	var ai ProductAI
	assert.Error(t, ai.DecodeSpanner("{"))
	var inv InventorySync
	assert.Error(t, inv.DecodeSpanner("{"))
	var ps PriceSync
	assert.Error(t, ps.DecodeSpanner("{"))
	var p ProductSync
	assert.Error(t, p.DecodeSpanner("{"))
	var a AutoLink
	assert.Error(t, a.DecodeSpanner("{"))
	var cc CurrencyConvertors
	assert.Error(t, cc.DecodeSpanner("{"))
	var d DimensionsMapping
	assert.Error(t, d.DecodeSpanner("{"))
	var m MarketsCurrencyMapping
	assert.Error(t, m.DecodeSpanner("{"))
}
