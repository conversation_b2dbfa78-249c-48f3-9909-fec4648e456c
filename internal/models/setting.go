package models

import (
	"encoding/json"
	"fmt"
	"time"

	"github.com/AfterShip/connectors-library/utils/sets"
	"github.com/AfterShip/pltf-pd-product-listings/internal/consts"
)

type ProductAI struct {
	OptimizationInfo OptimizationInfo `json:"optimization_info"`
}

type OptimizationInfo struct {
	State string `json:"state" validate:"omitempty,oneof='enabled' 'disabled'"`
}

// nolint:gocritic
func (productAI ProductAI) EncodeSpanner() (interface{}, error) {
	bytes, err := json.Marshal(productAI)
	if err != nil {
		return nil, err
	}
	return string(bytes), nil
}

func (productAI *ProductAI) DecodeSpanner(val interface{}) (err error) {
	strVal, _ := val.(string)
	if strVal == "" {
		return nil
	}
	err = json.Unmarshal([]byte(strVal), productAI)
	if err != nil {
		return err
	}
	return nil
}

type Compliance struct {
	Manufacturers      []ComplianceManufacturer      `json:"manufacturers"`
	ResponsiblePersons []ComplianceResponsiblePerson `json:"responsible_persons"`
}

type ComplianceManufacturer struct {
	SalesChannelID string `json:"sales_channel_id"`
}

type ComplianceResponsiblePerson struct {
	SalesChannelID string `json:"sales_channel_id"`
}

// nolint:gocritic
func (compliance Compliance) EncodeSpanner() (interface{}, error) {
	bytes, err := json.Marshal(compliance)
	if err != nil {
		return nil, err
	}
	return string(bytes), nil
}

func (compliance *Compliance) DecodeSpanner(val interface{}) (err error) {
	strVal, _ := val.(string)
	if strVal == "" {
		return nil
	}
	err = json.Unmarshal([]byte(strVal), compliance)
	if err != nil {
		return err
	}
	return nil
}

type Brand struct {
	ID string `json:"id"`
}

// nolint:gocritic
func (brand Brand) EncodeSpanner() (interface{}, error) {
	bytes, err := json.Marshal(brand)
	if err != nil {
		return nil, err
	}
	return string(bytes), nil
}

func (brand *Brand) DecodeSpanner(val interface{}) (err error) {
	strVal, _ := val.(string)
	err = json.Unmarshal([]byte(strVal), brand)
	if err != nil {
		return err
	}
	return nil
}

type InventorySync struct {
	AutoSync                   string               `json:"auto_sync" validate:"omitempty,oneof='enabled' 'disabled'"`
	AvailableQuantityPercent   float64              `json:"available_quantity_percent"`
	LowQuantityThreshold       LowQuantityThreshold `json:"low_quantity_threshold"`
	ActiveWarehouses           []ActiveWarehouse    `json:"active_warehouses"`
	PreferredChannelWarehouse  string               `json:"preferred_channel_warehouse"`
	LastEffectAt               time.Time            `json:"last_effect_at"`
	FollowSourceAllowBackorder string               `json:"follow_source_allow_backorder"`
}

// nolint:gocritic
func (inventorySync InventorySync) EnabledAutoSync() bool {
	return inventorySync.AutoSync == string(consts.AllowSyncEnabled)
}

// nolint:gocritic
func (inventorySync InventorySync) EnabledMultiWarehouse() bool {
	for i := range inventorySync.ActiveWarehouses {
		if inventorySync.ActiveWarehouses[i].Enabled() {
			return true
		}
	}
	return false
}

// nolint:gocritic
func (lowQuantityThreshold LowQuantityThreshold) Enabled() bool {
	return lowQuantityThreshold.State == string(consts.AllowSyncEnabled)
}

// nolint:gocritic
func (warehouse ActiveWarehouse) Enabled() bool {
	return warehouse.State == string(consts.AllowSyncEnabled)
}

// nolint:gocritic
func (inventorySync InventorySync) GatherEnableMultiWarehouseIDs() *sets.StringSet {
	warehouseIDsSet := sets.NewStringSet()
	for _, warehouse := range inventorySync.ActiveWarehouses {
		if warehouse.Enabled() {
			warehouseIDsSet.Add(warehouse.SourceWarehouseId)
		}
	}
	return warehouseIDsSet
}

// nolint:gocritic
func (inventorySync InventorySync) FollowSourceAllowBackorderEnabled() bool {
	return inventorySync.FollowSourceAllowBackorder == string(consts.AllowSyncEnabled)
}

// nolint:gocritic
func (inventorySync InventorySync) EncodeSpanner() (interface{}, error) {
	bytes, err := json.Marshal(inventorySync)
	if err != nil {
		return nil, err
	}
	return string(bytes), nil
}

func (inventorySync *InventorySync) DecodeSpanner(val interface{}) (err error) {
	strVal, _ := val.(string)
	err = json.Unmarshal([]byte(strVal), inventorySync)
	if err != nil {
		return err
	}
	return nil
}

type LowQuantityThreshold struct {
	State string  `json:"state" validate:"omitempty,oneof='enabled' 'disabled'"`
	Value float64 `json:"value"`
}

type ActiveWarehouse struct {
	State             string `json:"state" validate:"omitempty,oneof='enabled' 'disabled'"`
	SourceWarehouseId string `json:"source_warehouse_id"`
}

type PriceSync struct {
	SourceField        string       `json:"source_field"`
	SyncCompareAtPrice string       `json:"sync_compare_at_price"`
	AutoSync           string       `json:"auto_sync" validate:"omitempty,oneof='enabled' 'disabled'"`
	Rules              []PriceRules `json:"rules"`
}

// nolint:gocritic
func (priceSync PriceSync) EncodeSpanner() (interface{}, error) {
	bytes, err := json.Marshal(priceSync)
	if err != nil {
		return nil, err
	}
	return string(bytes), nil
}

func (priceSync *PriceSync) DecodeSpanner(val interface{}) (err error) {
	strVal, _ := val.(string)
	err = json.Unmarshal([]byte(strVal), priceSync)
	if err != nil {
		return err
	}
	return nil
}

type PriceRules struct {
	ValueType string `json:"value_type"`
	Value     string `json:"value"`
}

type ProductSync struct {
	UpdateDetail       UpdateDetail   `json:"update_detail"`
	UpdateVariants     UpdateVariants `json:"update_variants"`
	FulfillmentService string         `json:"fulfillment_service" validate:"omitempty,oneof='default' 'seller_fulfilled' 'platform_fulfilled'"`
	LastEffectAt       time.Time      `json:"last_effect_at"`
}

// nolint:gocritic
func (productSync ProductSync) EncodeSpanner() (interface{}, error) {
	bytes, err := json.Marshal(productSync)
	if err != nil {
		return nil, err
	}
	return string(bytes), nil
}

func (productSync *ProductSync) DecodeSpanner(val interface{}) (err error) {
	strVal, _ := val.(string)
	err = json.Unmarshal([]byte(strVal), productSync)
	if err != nil {
		return err
	}
	return nil
}

type UpdateDetail struct {
	Fields   []string `json:"fields"` // 'title' 'media' 'description'
	AutoSync string   `json:"auto_sync" validate:"omitempty,oneof='enabled' 'disabled'"`
}

type UpdateVariants struct {
	AutoSync string `json:"auto_sync" validate:"omitempty,oneof='enabled' 'disabled'"`
}

type AutoLink struct {
	State string `json:"state" validate:"omitempty,oneof='enabled' 'disabled'"`
}

// nolint:gocritic
func (autoLink AutoLink) EncodeSpanner() (interface{}, error) {
	bytes, err := json.Marshal(autoLink)
	if err != nil {
		return nil, err
	}
	return string(bytes), nil
}

func (autoLink *AutoLink) DecodeSpanner(val interface{}) (err error) {
	strVal, _ := val.(string)
	err = json.Unmarshal([]byte(strVal), autoLink)
	if err != nil {
		return err
	}
	return nil
}

func (autoLink AutoLink) Enabled() bool {
	return autoLink.State == string(consts.AllowSyncEnabled)
}

type CurrencyConvertors []CurrencyConvertor

type CurrencyConvertor struct {
	CustomExchangeRate   float64   `json:"custom_exchange_rate"`
	SourceCurrency       string    `json:"source_currency" validate:"omitempty,len=3"`
	SalesChannelCurrency string    `json:"sales_channel_currency" validate:"omitempty,len=3"`
	LastEffectAt         time.Time `json:"last_effect_at"`
}

func (convertor CurrencyConvertor) GenerateCacheKey() string {
	sourceCurrency := convertor.SourceCurrency
	salesChannelCurrency := convertor.SalesChannelCurrency
	return fmt.Sprintf("%s-%s", sourceCurrency, salesChannelCurrency)
}

// nolint:gocritic
func (currencyConvertors CurrencyConvertors) EncodeSpanner() (interface{}, error) {
	bytes, err := json.Marshal(currencyConvertors)
	if err != nil {
		return nil, err
	}
	return string(bytes), nil
}

func (currencyConvertors *CurrencyConvertors) DecodeSpanner(val interface{}) (err error) {
	strVal, _ := val.(string)
	err = json.Unmarshal([]byte(strVal), currencyConvertors)
	if err != nil {
		return err
	}
	return nil
}

type DimensionsMapping struct {
	Length SourceMetafield `json:"length"`
	Width  SourceMetafield `json:"width"`
	Height SourceMetafield `json:"height"`
}

// nolint:gocritic
func (dimensionsMapping DimensionsMapping) EncodeSpanner() (interface{}, error) {
	bytes, err := json.Marshal(dimensionsMapping)
	if err != nil {
		return nil, err
	}
	return string(bytes), nil
}

func (dimensionsMapping *DimensionsMapping) DecodeSpanner(val interface{}) (err error) {
	strVal, _ := val.(string)
	err = json.Unmarshal([]byte(strVal), dimensionsMapping)
	if err != nil {
		return err
	}
	return nil
}

type SourceMetafield struct {
	Name string `json:"source_metafield_name"`
	Unit string `json:"unit"`
}

type MarketsCurrencyMapping struct {
	Currency      string `json:"currency"`
	CountryRegion string `json:"country_region"`
}

func (mcm MarketsCurrencyMapping) EncodeSpanner() (interface{}, error) {
	bytes, err := json.Marshal(mcm)
	if err != nil {
		return nil, err
	}
	return string(bytes), nil
}

func (mcm *MarketsCurrencyMapping) DecodeSpanner(val interface{}) (err error) {
	strVal, _ := val.(string)

	if strVal == "" {
		return nil
	}

	err = json.Unmarshal([]byte(strVal), mcm)
	if err != nil {
		return err
	}
	return nil
}
