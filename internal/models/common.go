package models

import (
	"time"

	"github.com/shopspring/decimal"
)

type Paginator struct {
	Page        int64 `json:"page"`
	Limit       int64 `json:"limit"`
	HasNextPage bool  `json:"has_next_page"`
}

type Pagination struct {
	Page           int64  `json:"page,omitempty"`
	Limit          int64  `json:"limit"`
	NextCursor     string `json:"next_cursor,omitempty"`
	PreviousCursor string `json:"previous_cursor,omitempty"`
	Total          int64  `json:"total"`
	HasNextPage    bool   `json:"has_next_page"`
}

type SalesChannel struct {
	StoreKey      string `json:"store_key"`
	Platform      string `json:"platform"`
	CountryRegion string `json:"country_region,omitempty"`
}

type Organization struct {
	ID string `json:"id"`
}

type Source struct {
	Type string `json:"type"`
	App  App    `json:"app"`
	ID   string `json:"id"`
}

type App struct {
	Key      string `json:"key"`
	Platform string `json:"platform"`
}

type Price struct {
	Amount   decimal.Decimal `json:"amount"`
	Currency string          `json:"currency"`
}

type CompliancePhone struct {
	CountryCode string `json:"country_code" validate:"required,max=5"`
	Number      string `json:"number" validate:"required,min=7,max=10"`
}

type ResponsiblePersonAddress struct {
	AddressLine1 string `json:"address_line_1" validate:"required,lte=500"`
	AddressLine2 string `json:"address_line_2" validate:"lte=500"`
	District     string `json:"district" validate:"lte=50"`
	City         string `json:"city" validate:"required,lte=50"`
	PostalCode   string `json:"postal_code" validate:"required,lte=50"`
	Province     string `json:"province" validate:"required,lte=50"`
	Country      string `json:"country" validate:"required,lte=50"`
}

type ManufacturerAddress struct {
	AddressLine1 string `json:"address_line_1" validate:"required,lte=500"`
}

type Metrics struct {
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

type Phone struct {
	CountryCode string `json:"country_code,omitempty"`
	Number      string `json:"number,omitempty"`
}
