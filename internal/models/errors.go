package models

import (
	"github.com/pkg/errors"

	"github.com/AfterShip/connectors-library/gin/responder"
)

var (
	ErrResourceNotFound       = errors.New("resource not found")
	ErrVersionConflict        = errors.New("version conflict")
	ErrMissRequiredPathParam  = errors.New("missing required path param")
	ErrMissRequiredQueryParam = errors.New("missing required query param")
	ErrMissRequiredBodyParam  = errors.New("missing required body param")
	ErrMissingRequiredField   = errors.New("missing required field")
	ErrRedisLockFailed        = errors.New("failed to acquire redis lock")
	ErrInternal               = errors.New("internal error")
	ErrAcquireLockConflict    = errors.New("acquire lock conflict")
)

const (
	CodeResourceNotFound       responder.MetaCode = 40400
	CodeVersionConflict        responder.MetaCode = 40900
	CodeMissRequiredPathParam  responder.MetaCode = 42201
	CodeMissRequiredQueryParam responder.MetaCode = 42202
	CodeMissRequiredBodyParam  responder.MetaCode = 42203
	CodeMissingRequiredField   responder.MetaCode = 42205
	CodeUnprocessableEntity    responder.MetaCode = 42206
	CodeTooManyRequests        responder.MetaCode = 42900
)

var CommonErrorsMapping = map[error]responder.Meta{
	ErrResourceNotFound: {
		Code: CodeResourceNotFound,
		Description: responder.Description{
			Type:    "ResourceNotFound",
			Message: "The request source not found.",
		},
	},
	ErrVersionConflict: {
		Code: CodeVersionConflict,
		Description: responder.Description{
			Type:    "VersionConflict",
			Message: "The request conflicts with another request (perhaps due to using the same idempotent key).",
		},
	},
	ErrMissRequiredPathParam: {
		Code: CodeMissRequiredPathParam,
		Description: responder.Description{
			Type:    "MissRequiredPathParam",
			Message: "missing required path parameters.",
		},
	},
	ErrMissRequiredQueryParam: {
		Code: CodeMissRequiredQueryParam,
		Description: responder.Description{
			Type:    "MissRequiredQueryParam",
			Message: "missing required query parameters.",
		},
	},
	ErrMissRequiredBodyParam: {
		Code: CodeMissRequiredBodyParam,
		Description: responder.Description{
			Type: "UnprocessableEntity",
			Message: "The request body was well-formed but contains semantical errors. " +
				"The response body will provide more details in the errors or error parameters.",
		},
	},
	ErrMissingRequiredField: {
		Code: CodeMissingRequiredField,
		Description: responder.Description{
			Type:    "UnprocessableEntity",
			Message: "Missing required filed in handler process.",
		},
	},
}
