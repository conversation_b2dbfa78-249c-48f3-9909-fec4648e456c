package metrics

import (
	"github.com/prometheus/client_golang/prometheus"

	"github.com/AfterShip/gopkg/prome"
)

const (
	_namespace = "pltf-pd-product-listings" // #nosec G101
	_subsystem = "api"
)

var (
	_metrics *metrics
)

type metrics struct {
	Latencies *prometheus.HistogramVec
}

// Get the metrics instance and creates if not exists
func Get() *metrics {
	return _metrics
}

func setup() {
	buckets := prometheus.ExponentialBuckets(2, 2, 16)
	newHistogram := func(name string, labels ...string) *prometheus.HistogramVec {
		return prome.NewHistogramHelper(_namespace, _subsystem, name, buckets, labels...)
	}
	labels := []string{}
	_metrics = &metrics{
		Latencies: newHistogram("latencies", labels...),
	}
}

func init() {
	setup()
}
