package product_compliance

import (
	"context"

	validator "github.com/go-playground/validator/v10"

	"github.com/AfterShip/gopkg/log"
	tiktokapi "github.com/AfterShip/pltf-pd-product-listings/internal/third_party/tiktok_api"
)

type Service interface {
	GetResponsiblePersons(ctx context.Context, arg *GetResponsiblePersonsArg) (GetResponsiblePersonsOutput, error)
	CreateResponsiblePerson(ctx context.Context, arg *CreateResponsiblePersonArg) (CreateResponsiblePersonOutput, error)
	GetManufacturers(ctx context.Context, arg *GetManufacturersArg) (GetManufacturersOutput, error)
	CreateManufacturer(ctx context.Context, arg *CreateManufacturerArg) (CreateManufacturerOutput, error)
}

type serviceImpl struct {
	logger           *log.Logger
	tiktokAPIService tiktokapi.Service
	validate         *validator.Validate
}

func NewService(logger *log.Logger, tiktokAPIService tiktokapi.Service) Service {
	return &serviceImpl{
		logger:           logger,
		tiktokAPIService: tiktokAPIService,
		validate:         validator.New(),
	}
}
