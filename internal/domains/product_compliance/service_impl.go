package product_compliance

import (
	"context"
	"strings"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	connector_lib_utils "github.com/AfterShip/connectors-library/utils"
	"github.com/AfterShip/pltf-pd-product-listings/internal/consts"
	tiktokapi "github.com/AfterShip/pltf-pd-product-listings/internal/third_party/tiktok_api"
)

func (s *serviceImpl) GetResponsiblePersons(ctx context.Context, arg *GetResponsiblePersonsArg) (GetResponsiblePersonsOutput, error) {

	if arg.SalesChannel.Platform != consts.TikTokShop {
		return GetResponsiblePersonsOutput{}, errors.New("only support tiktok-shop")
	}

	if err := s.validate.Struct(arg); err != nil {
		return GetResponsiblePersonsOutput{}, err
	}

	responsiblePersons := make([]ResponsiblePerson, 0)

	ttsResponsiblePersons, err := s.tiktokAPIService.SearchResponsiblePersons(ctx, &tiktokapi.SearchResponsiblePersonsReq{
		CommonParams: tiktokapi.CommonParams{
			OrganizationID: arg.Organization.ID,
			AppName:        "feed",
			AppKey:         arg.SalesChannel.StoreKey,
		},
		Query:           arg.Query,
		SalesChannelIDs: arg.SalesChannelIDs,
	})
	if err != nil {
		return GetResponsiblePersonsOutput{}, err
	}

	for index := range ttsResponsiblePersons {
		responsiblePersons = append(responsiblePersons, convertTTSResponsiblePerson(ttsResponsiblePersons[index]))
	}

	return GetResponsiblePersonsOutput{
		Organization:       arg.Organization,
		SalesChannel:       arg.SalesChannel,
		ResponsiblePersons: responsiblePersons,
	}, nil
}

func (s *serviceImpl) CreateResponsiblePerson(ctx context.Context, arg *CreateResponsiblePersonArg) (CreateResponsiblePersonOutput, error) {

	if arg.SalesChannel.Platform != consts.TikTokShop {
		return CreateResponsiblePersonOutput{}, errors.New("only support tiktok-shop")
	}

	if err := s.validate.Struct(arg); err != nil {
		return CreateResponsiblePersonOutput{}, err
	}

	// TTS Phone.CountryCode is a string, so we need to add "+" to it
	if !strings.HasPrefix(arg.Phone.CountryCode, "+") {
		arg.Phone.CountryCode = "+" + arg.Phone.CountryCode
	}

	ttsResponsiblePersonID, err := s.tiktokAPIService.CreateResponsiblePerson(ctx, &tiktokapi.CreateResponsiblePersonReq{
		CommonParams: tiktokapi.CommonParams{
			OrganizationID: arg.Organization.ID,
			AppName:        "feed",
			AppKey:         arg.SalesChannel.StoreKey,
		},
		Name:             arg.Name,
		Email:            arg.Email,
		PhoneCountryCode: arg.Phone.CountryCode,
		PhoneNumber:      arg.Phone.Number,
		AddressLine1:     arg.Address.AddressLine1,
		AddressLine2:     arg.Address.AddressLine2,
		District:         arg.Address.District,
		City:             arg.Address.City,
		PostalCode:       arg.Address.PostalCode,
		Province:         arg.Address.Province,
		Country:          arg.Address.Country,
	})
	if err != nil {
		s.logger.WarnCtx(ctx, "failed to create responsible person",
			zap.String("arg", connector_lib_utils.GetJsonIndent(arg)), zap.Error(err))
		return CreateResponsiblePersonOutput{}, err
	}

	responsiblePersonsOutput, err := s.GetResponsiblePersons(ctx, &GetResponsiblePersonsArg{
		Organization:    arg.Organization,
		SalesChannel:    arg.SalesChannel,
		SalesChannelIDs: []string{ttsResponsiblePersonID},
	})
	if err != nil {
		return CreateResponsiblePersonOutput{}, err
	}
	if len(responsiblePersonsOutput.ResponsiblePersons) == 0 {
		return CreateResponsiblePersonOutput{}, errors.New("failed to get created responsible person")
	}

	ttsResponsiblePerson := responsiblePersonsOutput.ResponsiblePersons[0]

	return CreateResponsiblePersonOutput{
		Organization:      arg.Organization,
		SalesChannel:      arg.SalesChannel,
		ResponsiblePerson: ttsResponsiblePerson,
	}, nil

}

func (s *serviceImpl) GetManufacturers(ctx context.Context, arg *GetManufacturersArg) (GetManufacturersOutput, error) {

	if arg.SalesChannel.Platform != consts.TikTokShop {
		return GetManufacturersOutput{}, errors.New("only support tiktok-shop")
	}

	if err := s.validate.Struct(arg); err != nil {
		return GetManufacturersOutput{}, err
	}

	manufacturers := make([]Manufacturer, 0)

	ttsManufacturers, err := s.tiktokAPIService.SearchManufacturers(ctx, &tiktokapi.SearchManufacturersReq{
		CommonParams: tiktokapi.CommonParams{
			OrganizationID: arg.Organization.ID,
			AppName:        "feed",
			AppKey:         arg.SalesChannel.StoreKey,
		},
		Query:           arg.Query,
		ManufacturerIDs: arg.SalesChannelIDs,
	})
	if err != nil {
		return GetManufacturersOutput{}, err
	}

	for index := range ttsManufacturers {
		manufacturers = append(manufacturers, convertTTSManufacturer(ttsManufacturers[index]))
	}

	return GetManufacturersOutput{
		Organization:  arg.Organization,
		SalesChannel:  arg.SalesChannel,
		Manufacturers: manufacturers,
	}, nil
}

func (s *serviceImpl) CreateManufacturer(ctx context.Context, arg *CreateManufacturerArg) (CreateManufacturerOutput, error) {

	if arg.SalesChannel.Platform != consts.TikTokShop {
		return CreateManufacturerOutput{}, errors.New("only support tiktok-shop")
	}

	if err := s.validate.Struct(arg); err != nil {
		return CreateManufacturerOutput{}, err
	}

	if !strings.HasPrefix(arg.Phone.CountryCode, "+") {
		// TTS Phone.CountryCode is a string, so we need to add "+" to it
		arg.Phone.CountryCode = "+" + arg.Phone.CountryCode
	}

	ttsManufacturerID, err := s.tiktokAPIService.CreateManufacturer(ctx, &tiktokapi.CreateManufacturerReq{
		CommonParams: tiktokapi.CommonParams{
			OrganizationID: arg.Organization.ID,
			AppName:        "feed",
			AppKey:         arg.SalesChannel.StoreKey,
		},
		Name:                arg.Name,
		RegisteredTradeName: arg.RegisteredTradeName,
		Email:               arg.Email,
		PhoneCountryCode:    arg.Phone.CountryCode,
		PhoneNumber:         arg.Phone.Number,
		AddressLine1:        arg.Address.AddressLine1,
	})
	if err != nil {
		s.logger.WarnCtx(ctx, "failed to create manufacturer",
			zap.String("arg", connector_lib_utils.GetJsonIndent(arg)), zap.Error(err))
		return CreateManufacturerOutput{}, err
	}

	manufacturersOutput, err := s.GetManufacturers(ctx, &GetManufacturersArg{
		Organization:    arg.Organization,
		SalesChannel:    arg.SalesChannel,
		SalesChannelIDs: []string{ttsManufacturerID},
	})
	if err != nil {
		return CreateManufacturerOutput{}, err
	}

	if len(manufacturersOutput.Manufacturers) == 0 {
		return CreateManufacturerOutput{}, errors.New("failed to get created manufacturer")
	}

	ttsManufacturer := manufacturersOutput.Manufacturers[0]

	return CreateManufacturerOutput{
		Organization: arg.Organization,
		SalesChannel: arg.SalesChannel,
		Manufacturer: ttsManufacturer,
	}, nil
}
