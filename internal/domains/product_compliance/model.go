package product_compliance

import (
	tiktokres_v202309 "github.com/AfterShip/connectors-ecommerce-sdk-go/tiktok/rest/version202309"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
)

type GetResponsiblePersonsArg struct {
	Organization    models.Organization `json:"organization" validate:"required"`
	SalesChannel    models.SalesChannel `json:"sales_channel" validate:"required"`
	SalesChannelIDs []string            `json:"sales_channel_ids"`
	Query           string              `json:"query"` // name, local_number, email 模糊搜索
}

type GetResponsiblePersonsOutput struct {
	Organization       models.Organization `json:"organization"`
	SalesChannel       models.SalesChannel `json:"sales_channel"`
	ResponsiblePersons []ResponsiblePerson `json:"responsible_persons"`
}

type ResponsiblePerson struct {
	SalesChannelID string                          `json:"sales_channel_id"`
	Name           string                          `json:"name"`
	Email          string                          `json:"email"`
	Phone          models.CompliancePhone          `json:"phone"`
	Address        models.ResponsiblePersonAddress `json:"address"`
}

type CreateResponsiblePersonArg struct {
	Organization models.Organization             `json:"organization" validate:"required"`
	SalesChannel models.SalesChannel             `json:"sales_channel" validate:"required"`
	Name         string                          `json:"name" validate:"required,lte=200"`
	Email        string                          `json:"email" validate:"required,lte=200"`
	Phone        models.CompliancePhone          `json:"phone"`
	Address      models.ResponsiblePersonAddress `json:"address"`
}

type CreateResponsiblePersonOutput struct {
	Organization      models.Organization `json:"organization"`
	SalesChannel      models.SalesChannel `json:"sales_channel"`
	ResponsiblePerson ResponsiblePerson   `json:"responsible_person"`
}

type GetManufacturersArg struct {
	Organization    models.Organization `json:"organization"`
	SalesChannel    models.SalesChannel `json:"sales_channel"`
	SalesChannelIDs []string            `json:"sales_channel_ids"`
	Query           string              `json:"query"` // name, local_number, email 模糊搜索
}

type GetManufacturersOutput struct {
	Organization  models.Organization `json:"organization"`
	SalesChannel  models.SalesChannel `json:"sales_channel"`
	Manufacturers []Manufacturer      `json:"manufacturers"`
}

type Manufacturer struct {
	SalesChannelID      string                     `json:"sales_channel_id"`
	Name                string                     `json:"name"`
	RegisteredTradeName string                     `json:"registered_trade_name"`
	Email               string                     `json:"email"`
	Phone               models.CompliancePhone     `json:"phone"`
	Address             models.ManufacturerAddress `json:"address"`
}

type CreateManufacturerArg struct {
	Organization        models.Organization        `json:"organization"`
	SalesChannel        models.SalesChannel        `json:"sales_channel"`
	Name                string                     `json:"name" validate:"required,lte=255"`
	RegisteredTradeName string                     `json:"registered_trade_name" validate:"lte=200"`
	Email               string                     `json:"email" validate:"required,lte=200"`
	Phone               models.CompliancePhone     `json:"phone"`
	Address             models.ManufacturerAddress `json:"address"`
}

type CreateManufacturerOutput struct {
	Organization models.Organization `json:"organization"`
	SalesChannel models.SalesChannel `json:"sales_channel"`
	Manufacturer Manufacturer        `json:"manufacturer"`
}

func convertTTSResponsiblePerson(ttsResponsiblePersons tiktokres_v202309.ResponsiblePersons) ResponsiblePerson {
	responsiblePerson := ResponsiblePerson{
		SalesChannelID: ttsResponsiblePersons.ID.String(),
		Name:           ttsResponsiblePersons.Name.String(),
		Email:          ttsResponsiblePersons.Email.String(),
	}
	if ttsResponsiblePersons.PhoneNumber != nil {
		responsiblePerson.Phone = models.CompliancePhone{
			CountryCode: ttsResponsiblePersons.PhoneNumber.CountryCode.String(),
			Number:      ttsResponsiblePersons.PhoneNumber.LocalNumber.String(),
		}
	}
	if ttsResponsiblePersons.Address != nil {
		responsiblePerson.Address = models.ResponsiblePersonAddress{
			AddressLine1: ttsResponsiblePersons.Address.StreetAddressLine1.String(),
			AddressLine2: ttsResponsiblePersons.Address.StreetAddressLine2.String(),
			District:     ttsResponsiblePersons.Address.District.String(),
			City:         ttsResponsiblePersons.Address.City.String(),
			PostalCode:   ttsResponsiblePersons.Address.PostalCode.String(),
			Province:     ttsResponsiblePersons.Address.Province.String(),
			Country:      ttsResponsiblePersons.Address.Country.String(),
		}
	}
	return responsiblePerson
}

func convertTTSManufacturer(ttsManufacturer tiktokres_v202309.Manufacturers) Manufacturer {
	manufacturer := Manufacturer{
		SalesChannelID:      ttsManufacturer.ID.String(),
		Name:                ttsManufacturer.Name.String(),
		RegisteredTradeName: ttsManufacturer.RegisteredTradeName.String(),
		Email:               ttsManufacturer.Email.String(),
		Address: models.ManufacturerAddress{
			AddressLine1: ttsManufacturer.Address.String(),
		},
	}
	if ttsManufacturer.PhoneNumber != nil {
		manufacturer.Phone = models.CompliancePhone{
			CountryCode: ttsManufacturer.PhoneNumber.CountryCode.String(),
			Number:      ttsManufacturer.PhoneNumber.LocalNumber.String(),
		}
	}
	return manufacturer
}
