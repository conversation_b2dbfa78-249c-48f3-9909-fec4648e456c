package product_compliance

import (
	"context"

	"github.com/stretchr/testify/mock"
)

type MockProductComplianceService struct {
	mock.Mock
}

func (m *MockProductComplianceService) GetResponsiblePersons(ctx context.Context, arg *GetResponsiblePersonsArg) (GetResponsiblePersonsOutput, error) {
	rest := m.Called(arg)
	return rest.Get(0).(GetResponsiblePersonsOutput), rest.Error(1)
}

func (m *MockProductComplianceService) CreateResponsiblePerson(ctx context.Context, arg *CreateResponsiblePersonArg) (CreateResponsiblePersonOutput, error) {
	rest := m.Called(arg)
	return rest.Get(0).(CreateResponsiblePersonOutput), rest.Error(1)
}

func (m *MockProductComplianceService) GetManufacturers(ctx context.Context, arg *GetManufacturersArg) (GetManufacturersOutput, error) {
	rest := m.Called(arg)
	return rest.Get(0).(GetManufacturersOutput), rest.Error(1)
}

func (m *MockProductComplianceService) CreateManufacturer(ctx context.Context, arg *CreateManufacturerArg) (CreateManufacturerOutput, error) {
	rest := m.Called(arg)
	return rest.Get(0).(CreateManufacturerOutput), rest.Error(1)
}
