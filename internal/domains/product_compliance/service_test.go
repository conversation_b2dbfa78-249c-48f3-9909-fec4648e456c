package product_compliance

import (
	"context"
	"testing"

	validator "github.com/go-playground/validator/v10"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"

	tiktokres_v202309 "github.com/AfterShip/connectors-ecommerce-sdk-go/tiktok/rest/version202309"
	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/pltf-pd-product-listings/internal/consts"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
	tiktokapi "github.com/AfterShip/pltf-pd-product-listings/internal/third_party/tiktok_api"
)

func Test_serviceImpl_getResponsiblePersons(t *testing.T) {

	tiktokAPIService := &tiktokapi.MockService{}

	type fields struct {
		tiktokAPIService tiktokapi.Service
	}
	type args struct {
		ctx context.Context
		arg *GetResponsiblePersonsArg
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		mock   func()
		check  func(out GetResponsiblePersonsOutput, err error)
	}{
		{
			name: "Case 1: success",
			fields: fields{
				tiktokAPIService: tiktokAPIService,
			},
			args: args{
				ctx: context.Background(),
				arg: &GetResponsiblePersonsArg{
					Organization: models.Organization{},
					SalesChannel: models.SalesChannel{
						Platform: consts.TikTokShop,
					},
					SalesChannelIDs: []string{"id_1"},
				},
			},
			mock: func() {
				tiktokAPIService.On("SearchResponsiblePersons", mock.Anything, mock.Anything).Return([]tiktokres_v202309.ResponsiblePersons{
					{
						ID: types.MakeString("id_1"),
					},
				}, nil)
			},
			check: func(out GetResponsiblePersonsOutput, err error) {
				require.NoError(t, err)
				require.Equal(t, out.ResponsiblePersons[0].SalesChannelID, "id_1")
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &serviceImpl{
				tiktokAPIService: tt.fields.tiktokAPIService,
				validate:         validator.New(),
			}

			tt.mock()
			got, err := s.GetResponsiblePersons(tt.args.ctx, tt.args.arg)
			tt.check(got, err)
		})
	}

}

func Test_serviceImpl_createResponsiblePersons(t *testing.T) {

	tiktokAPIService := &tiktokapi.MockService{}

	type fields struct {
		tiktokAPIService tiktokapi.Service
	}
	type args struct {
		ctx context.Context
		arg *CreateResponsiblePersonArg
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		mock   func()
		check  func(out CreateResponsiblePersonOutput, err error)
	}{
		{
			name: "Case 1: success",
			fields: fields{
				tiktokAPIService: tiktokAPIService,
			},
			args: args{
				ctx: context.Background(),
				arg: &CreateResponsiblePersonArg{
					Organization: models.Organization{},
					SalesChannel: models.SalesChannel{
						Platform: consts.TikTokShop,
					},
					Name:  "test_name",
					Email: "<EMAIL>",
					Phone: models.CompliancePhone{
						CountryCode: "353",
						Number:      "1234567",
					},
					Address: models.ResponsiblePersonAddress{
						AddressLine1: "test_address_line1",
						AddressLine2: "test_address_line2",
						District:     "test_district",
						City:         "test_city",
						PostalCode:   "test_postal_code",
						Province:     "test_province",
						Country:      "test_country",
					},
				},
			},
			mock: func() {
				tiktokAPIService.On("CreateResponsiblePerson", mock.Anything, mock.Anything).Return("id_1", nil)
				tiktokAPIService.On("SearchResponsiblePersons", mock.Anything, mock.Anything).Return([]tiktokres_v202309.ResponsiblePersons{
					{
						ID:   types.MakeString("id_1"),
						Name: types.MakeString("test_name"),
					},
				}, nil)
			},
			check: func(out CreateResponsiblePersonOutput, err error) {
				require.NoError(t, err)
				require.Equal(t, out.ResponsiblePerson.SalesChannelID, "id_1")
				require.Equal(t, out.ResponsiblePerson.Name, "test_name")
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &serviceImpl{
				tiktokAPIService: tt.fields.tiktokAPIService,
				validate:         validator.New(),
			}

			tt.mock()
			got, err := s.CreateResponsiblePerson(tt.args.ctx, tt.args.arg)
			tt.check(got, err)
		})
	}

}

func Test_serviceImpl_getManufacturers(t *testing.T) {

	tiktokAPIService := &tiktokapi.MockService{}

	type fields struct {
		tiktokAPIService tiktokapi.Service
	}
	type args struct {
		ctx context.Context
		arg *GetManufacturersArg
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		mock   func()
		check  func(out GetManufacturersOutput, err error)
	}{
		{
			name: "Case 1: success",
			fields: fields{
				tiktokAPIService: tiktokAPIService,
			},
			args: args{
				ctx: context.Background(),
				arg: &GetManufacturersArg{
					Organization: models.Organization{},
					SalesChannel: models.SalesChannel{
						Platform: consts.TikTokShop,
					},
					SalesChannelIDs: []string{"id_1"},
				},
			},
			mock: func() {
				tiktokAPIService.On("SearchManufacturers", mock.Anything, mock.Anything).Return([]tiktokres_v202309.Manufacturers{
					{
						ID: types.MakeString("id_1"),
					},
				}, nil)
			},
			check: func(out GetManufacturersOutput, err error) {
				require.NoError(t, err)
				require.Equal(t, out.Manufacturers[0].SalesChannelID, "id_1")
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &serviceImpl{
				tiktokAPIService: tt.fields.tiktokAPIService,
				validate:         validator.New(),
			}

			tt.mock()
			got, err := s.GetManufacturers(tt.args.ctx, tt.args.arg)
			tt.check(got, err)
		})
	}

}

func Test_serviceImpl_createManufacturers(t *testing.T) {

	tiktokAPIService := &tiktokapi.MockService{}

	type fields struct {
		tiktokAPIService tiktokapi.Service
	}
	type args struct {
		ctx context.Context
		arg *CreateManufacturerArg
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		mock   func()
		check  func(out CreateManufacturerOutput, err error)
	}{
		{
			name: "Case 1: success",
			fields: fields{
				tiktokAPIService: tiktokAPIService,
			},
			args: args{
				ctx: context.Background(),
				arg: &CreateManufacturerArg{
					Organization: models.Organization{},
					SalesChannel: models.SalesChannel{
						Platform: consts.TikTokShop,
					},
					Name:                "test_name",
					RegisteredTradeName: "test_registered_trade_name",
					Email:               "<EMAIL>",
					Phone: models.CompliancePhone{
						CountryCode: "353",
						Number:      "1234567",
					},
					Address: models.ManufacturerAddress{
						AddressLine1: "test_address_line1",
					},
				},
			},
			mock: func() {
				tiktokAPIService.On("CreateManufacturer", mock.Anything, mock.Anything).Return("id_1", nil)
				tiktokAPIService.On("SearchManufacturers", mock.Anything, mock.Anything).Return([]tiktokres_v202309.Manufacturers{
					{
						ID:   types.MakeString("id_1"),
						Name: types.MakeString("test_name"),
					},
				}, nil)
			},
			check: func(out CreateManufacturerOutput, err error) {
				require.NoError(t, err)
				require.Equal(t, out.Manufacturer.SalesChannelID, "id_1")
				require.Equal(t, out.Manufacturer.Name, "test_name")
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &serviceImpl{
				tiktokAPIService: tt.fields.tiktokAPIService,
				validate:         validator.New(),
			}

			tt.mock()
			got, err := s.CreateManufacturer(tt.args.ctx, tt.args.arg)
			tt.check(got, err)
		})
	}

}
