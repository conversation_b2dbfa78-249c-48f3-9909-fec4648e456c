package brand

import (
	"testing"

	shein_rest "github.com/AfterShip/connectors-ecommerce-sdk-go/shein/rest"
	"github.com/AfterShip/connectors-library/sdks/shein_proxy"
	"github.com/AfterShip/pltf-pd-product-listings/internal/consts"
)

func TestListArg_toGetBrandsParams(t *testing.T) {
	tests := []struct {
		name     string
		arg      ListArg
		language string
		want     *shein_proxy.GetBrandsParams
	}{
		{
			name: "基本参数转换测试",
			arg: ListArg{
				OrganizationID:       "org123",
				SalesChannelStoreKey: "store456",
				SalesChannelPlatform: "shein",
				RefreshCache:         false,
			},
			language: "zh_CN",
			want: &shein_proxy.GetBrandsParams{
				CommonParams: shein_proxy.CommonParams{
					OrganizationID: "org123",
					AppName:        consts.AppFeed,
					AppKey:         "store456",
				},
				Language: "zh_CN",
			},
		},
		{
			name: "空语言参数测试",
			arg: ListArg{
				OrganizationID:       "org789",
				SalesChannelStoreKey: "store101",
				SalesChannelPlatform: "shein",
				RefreshCache:         true,
			},
			language: "",
			want: &shein_proxy.GetBrandsParams{
				CommonParams: shein_proxy.CommonParams{
					OrganizationID: "org789",
					AppName:        consts.AppFeed,
					AppKey:         "store101",
				},
				Language: "",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := tt.arg.toGetBrandsParams(tt.language)

			if got.CommonParams.OrganizationID != tt.want.CommonParams.OrganizationID {
				t.Errorf("OrganizationID = %v, 期望 %v", got.CommonParams.OrganizationID, tt.want.CommonParams.OrganizationID)
			}

			if got.CommonParams.AppName != tt.want.CommonParams.AppName {
				t.Errorf("AppName = %v, 期望 %v", got.CommonParams.AppName, tt.want.CommonParams.AppName)
			}

			if got.CommonParams.AppKey != tt.want.CommonParams.AppKey {
				t.Errorf("AppKey = %v, 期望 %v", got.CommonParams.AppKey, tt.want.CommonParams.AppKey)
			}

			if got.Language != tt.want.Language {
				t.Errorf("Language = %v, 期望 %v", got.Language, tt.want.Language)
			}
		})
	}
}

func TestConvertSheinBrands(t *testing.T) {
	tests := []struct {
		name        string
		sheinBrands []shein_rest.Brand
		want        []Brand
	}{
		{
			name: "正常品牌数据转换",
			sheinBrands: []shein_rest.Brand{
				{
					BrandName: "Nike",
					BrandCode: "B001",
				},
				{
					BrandName: "Adidas",
					BrandCode: "B002",
				},
			},
			want: []Brand{
				{
					Name:           "Nike",
					SalesChannelID: "B001",
				},
				{
					Name:           "Adidas",
					SalesChannelID: "B002",
				},
			},
		},
		{
			name:        "空数组测试",
			sheinBrands: []shein_rest.Brand{},
			want:        []Brand{},
		},
		{
			name: "单个品牌数据转换",
			sheinBrands: []shein_rest.Brand{
				{
					BrandName: "Puma",
					BrandCode: "B003",
				},
			},
			want: []Brand{
				{
					Name:           "Puma",
					SalesChannelID: "B003",
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := convertSheinBrands(tt.sheinBrands)

			if len(got) != len(tt.want) {
				t.Errorf("长度不匹配: 得到 %d, 期望 %d", len(got), len(tt.want))
				return
			}

			for i := 0; i < len(got); i++ {
				if got[i].Name != tt.want[i].Name {
					t.Errorf("第 %d 个元素的 Name 不匹配: 得到 %s, 期望 %s", i, got[i].Name, tt.want[i].Name)
				}
				if got[i].SalesChannelID != tt.want[i].SalesChannelID {
					t.Errorf("第 %d 个元素的 SalesChannelID 不匹配: 得到 %s, 期望 %s", i, got[i].SalesChannelID, tt.want[i].SalesChannelID)
				}
			}
		})
	}
}
