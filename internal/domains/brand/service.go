package brand

import (
	"context"

	redis "github.com/go-redis/redis/v8"

	"github.com/AfterShip/connectors-library/sdks/shein_proxy"
)

type Service interface {
	List(ctx context.Context, arg ListArg) (BrandsOutput, error)
}

type serviceImpl struct {
	redisClient     *redis.Client
	sheinAPIService shein_proxy.Service
}

func NewService(sheinAPIService shein_proxy.Service, redisClient *redis.Client) Service {
	return &serviceImpl{
		sheinAPIService: sheinAPIService,
		redisClient:     redisClient,
	}
}
