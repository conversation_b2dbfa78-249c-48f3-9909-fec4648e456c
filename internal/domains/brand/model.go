package brand

import (
	shein_rest "github.com/AfterShip/connectors-ecommerce-sdk-go/shein/rest"
	"github.com/AfterShip/connectors-library/sdks/shein_proxy"
	"github.com/AfterShip/pltf-pd-product-listings/internal/consts"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
)

type BrandsOutput struct {
	Organization models.Organization `json:"organization"`
	SalesChannel models.SalesChannel `json:"sales_channel"`
	Brands       []Brand             `json:"brands"`
}

type Brand struct {
	SalesChannelID string `json:"sales_channel_id"`
	Name           string `json:"name"`
}

type ListArg struct {
	OrganizationID       string
	SalesChannelStoreKey string
	SalesChannelPlatform string
	RefreshCache         bool
}

func (arg ListArg) toGetBrandsParams(language string) *shein_proxy.GetBrandsParams {
	return &shein_proxy.GetBrandsParams{
		CommonParams: shein_proxy.CommonParams{
			OrganizationID: arg.OrganizationID,
			AppName:        consts.AppFeed,
			AppKey:         arg.SalesChannelStoreKey,
		},
		Language: language,
	}
}

func convertSheinBrands(sheinBrands []shein_rest.Brand) []Brand {
	var brands []Brand
	for _, b := range sheinBrands {
		brands = append(brands, Brand{
			Name:           b.BrandName,
			SalesChannelID: b.BrandCode,
		})
	}
	return brands
}
