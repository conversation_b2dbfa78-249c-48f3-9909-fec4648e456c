package brand

import (
	"context"
	"fmt"

	redis "github.com/go-redis/redis/v8"
	jsoniter "github.com/json-iterator/go"
	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/AfterShip/connectors-library/sdks/shein_proxy"
	"github.com/AfterShip/gopkg/log"
	"github.com/AfterShip/pltf-pd-product-listings/internal/consts"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/common"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
)

func (s *serviceImpl) List(ctx context.Context, arg ListArg) (BrandsOutput, error) {
	switch arg.SalesChannelPlatform {
	case consts.Shein:
		return s.listSheinBrands(ctx, arg)
	default:
		return BrandsOutput{}, common.ErrSalesChannelNotSupported
	}
}

func (s *serviceImpl) listSheinBrands(ctx context.Context, arg ListArg) (BrandsOutput, error) {
	brands := make([]Brand, 0)
	language, err := s.getSheinDefaultLanguage(ctx, arg.OrganizationID, arg.SalesChannelStoreKey)
	if err != nil {
		return BrandsOutput{}, errors.WithStack(err)
	}

	redisKey := fmt.Sprintf("%s:%s:%s:%s", consts.SHEINBrandsCacheKeyPrefix, arg.OrganizationID, arg.SalesChannelStoreKey, language)

	if arg.RefreshCache {
		_, err := s.redisClient.Del(ctx, redisKey).Result()
		if err != nil {
			return BrandsOutput{}, errors.WithStack(err)
		}
	}

	redisValue, err := s.redisClient.Get(ctx, redisKey).Result()
	if err != nil {
		if !errors.Is(err, redis.Nil) {
			return BrandsOutput{}, errors.WithStack(err)
		}
		resp, err := s.sheinAPIService.GetBrands(ctx, arg.toGetBrandsParams(language))
		if err != nil {
			return BrandsOutput{}, errors.WithStack(err)
		}

		brands = convertSheinBrands(resp.Data)

		if redisValue, err = jsoniter.MarshalToString(brands); err == nil {
			_ = s.redisClient.Set(ctx, redisKey, redisValue, consts.SHEINBrandsCacheDuration).Err()
		}
	} else {
		err = jsoniter.Unmarshal([]byte(redisValue), &brands)
		if err != nil {
			return BrandsOutput{}, errors.WithStack(err)
		}
	}

	return BrandsOutput{
		Brands: brands,
		Organization: models.Organization{
			ID: arg.OrganizationID,
		},
		SalesChannel: models.SalesChannel{
			Platform: arg.SalesChannelPlatform,
			StoreKey: arg.SalesChannelStoreKey,
		},
	}, nil
}

func (s *serviceImpl) getSheinDefaultLanguage(ctx context.Context, orgID, storeKey string) (string, error) {
	language := ""
	redisKey := fmt.Sprintf("%s:%s:%s", consts.SHEINLanguageCacheKeyPrefix, orgID, storeKey)
	redisValue, err := s.redisClient.Get(ctx, redisKey).Result()
	if err != nil {
		if !errors.Is(err, redis.Nil) {
			return "", errors.WithStack(err)
		}

		fillStandard, err := s.sheinAPIService.GetProductPublishFillStandard(ctx,
			&shein_proxy.GetProductPublishFillStandardParams{
				CommonParams: shein_proxy.CommonParams{
					OrganizationID: orgID,
					AppName:        consts.AppFeed,
					AppKey:         storeKey,
				},
			})
		if err != nil {
			return "", err
		}

		language = fillStandard.DefaultLanguage.String()
		if err := s.redisClient.Set(ctx, redisKey, language, consts.SHEINLanguageCacheDuration).Err(); err != nil {
			log.GlobalLogger().WarnCtx(ctx, "failed to cache shein store language",
				zap.String("orgID", orgID),
				zap.String("storeKey", storeKey),
				zap.Error(err))
		}
	} else {
		language = redisValue
	}

	return language, nil
}
