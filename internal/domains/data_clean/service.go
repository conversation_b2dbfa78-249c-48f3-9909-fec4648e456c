package data_clean

import (
	"context"

	redis "github.com/go-redis/redis/v8"
	elastic "github.com/olivere/elastic/v7"

	"github.com/AfterShip/connectors-library/sdks/products_center"
	"github.com/AfterShip/gopkg/log"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/data_clean/searchable_product_fields"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/data_clean/util"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/searchable_products"
	"github.com/AfterShip/pltf-pd-product-listings/internal/third_party/connectors"
)

type Service interface {
	Abort(ctx context.Context, scriptName string) error
	CleanSearchableProductFields(ctx context.Context, arg searchable_product_fields.SearchableProductFieldsInputArgs) (string, error)
}

type serviceImpl struct {
	logger                   *log.Logger
	connectorService         connectors.Service
	searchableProductService searchable_products.Service
	productsCenterClient     *products_center.Client
	esCli                    *elastic.Client
	redisClient              *redis.Client
}

func NewService(logger *log.Logger, connectorService connectors.Service,
	searchableProductService searchable_products.Service,
	productsCenterClient *products_center.Client, esCli *elastic.Client, redisClient *redis.Client) Service {
	return &serviceImpl{
		logger:                   logger,
		connectorService:         connectorService,
		searchableProductService: searchableProductService,
		productsCenterClient:     productsCenterClient,
		esCli:                    esCli,
		redisClient:              redisClient,
	}
}

func (s *serviceImpl) Abort(ctx context.Context, scriptName string) error {
	return util.SetScriptAbort(ctx, s.redisClient, scriptName)
}

func (s *serviceImpl) CleanSearchableProductFields(ctx context.Context, arg searchable_product_fields.SearchableProductFieldsInputArgs) (string, error) {

	service := searchable_product_fields.NewSearchableProductFieldsService(arg, s.logger, s.connectorService,
		s.searchableProductService, s.productsCenterClient, s.esCli, s.redisClient)

	scriptName, err := newDataCleanHandle(ctx, service).Do()
	if err != nil {
		return "", err
	}

	return scriptName, nil
}
