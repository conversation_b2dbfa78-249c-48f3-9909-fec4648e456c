package data_clean

import (
	"context"

	"github.com/AfterShip/gopkg/log"
	"github.com/AfterShip/gopkg/routine"
)

type dataCleanService interface {
	GetScriptName() string

	ReadyCheck(ctx context.Context) error
	Run(ctx context.Context)
}

type DataCleanHandle func() (string, error)

func newDataCleanHandle(ctx context.Context, service dataCleanService) DataCleanHandle {
	return func() (string, error) {
		newCtx := log.CloneLogContext(ctx)

		if err := service.ReadyCheck(newCtx); err != nil {
			return "", err
		}

		routine.WithRecover(log.GlobalLogger(), func() {
			service.Run(newCtx)
		})

		return service.GetScriptName(), nil
	}
}

func (h DataCleanHandle) Do() (string, error) {
	return h()
}
