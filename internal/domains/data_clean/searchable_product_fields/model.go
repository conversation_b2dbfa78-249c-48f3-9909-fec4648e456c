package searchable_product_fields

import "time"

type SearchableProductFieldsInputArgs struct {
	OrganizationIDs []string
	BatchSize       int
	ProducerCount   int
	ConsumerCount   int
	ProcessingQPS   int
}

type searchResult struct {
	ID        string    `json:"id"`
	CreatedAt time.Time `json:"created_at"`
}

type searchArgs struct {
	OrganizationID string `json:"organization_id"`
	SourcePlatform string `json:"source_platform"`
	SourceStoreKey string `json:"source_store_key"`
	Cursor         string `json:"cursor"`
	Limit          int    `json:"limit"`
}
