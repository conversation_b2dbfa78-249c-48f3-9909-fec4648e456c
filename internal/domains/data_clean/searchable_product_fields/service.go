package searchable_product_fields

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"sync"
	"time"

	redis "github.com/go-redis/redis/v8"
	"github.com/google/uuid"
	elastic "github.com/olivere/elastic/v7"
	"github.com/pkg/errors"
	"go.uber.org/zap"
	"golang.org/x/time/rate"

	"github.com/AfterShip/connectors-library/sdks/products_center"
	"github.com/AfterShip/gopkg/log"
	"github.com/AfterShip/gopkg/routine"
	"github.com/AfterShip/pltf-pd-product-listings/internal/consts"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/data_clean/util"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/searchable_products"
	"github.com/AfterShip/pltf-pd-product-listings/internal/third_party/connectors"
	"github.com/AfterShip/pltf-pd-product-listings/internal/utils/elasticsearch"
)

const scriptName = "searchable_product_fields"

type SearchableProductFieldsService struct {
	instanceValue string

	arg     SearchableProductFieldsInputArgs
	limiter *rate.Limiter

	logger                   *log.Logger
	connectorService         connectors.Service
	searchableProductService searchable_products.Service
	productsCenterClient     *products_center.Client
	esCli                    *elastic.Client
	redisClient              *redis.Client
}

func NewSearchableProductFieldsService(arg SearchableProductFieldsInputArgs, logger *log.Logger,
	connectorService connectors.Service, searchableProductService searchable_products.Service,
	productsCenterClient *products_center.Client, esCli *elastic.Client, redisClient *redis.Client,
) *SearchableProductFieldsService {
	return &SearchableProductFieldsService{
		instanceValue:            uuid.New().String(),
		limiter:                  rate.NewLimiter(rate.Every(time.Second/time.Duration(arg.ProcessingQPS)), arg.ProcessingQPS),
		arg:                      arg,
		logger:                   logger,
		connectorService:         connectorService,
		searchableProductService: searchableProductService,
		productsCenterClient:     productsCenterClient,
		esCli:                    esCli,
		redisClient:              redisClient,
	}
}

func (s *SearchableProductFieldsService) Run(ctx context.Context) {

	ctx = log.AppendFieldsToContext(ctx,
		zap.String("script_name", scriptName),
		zap.String("instance_value", s.instanceValue),
	)

	dataChannel := make(chan []searchResult, s.arg.ProducerCount)
	mainWaitChan := make(chan struct{})

	// 生产者关闭 channel
	go s.doProduce(ctx, dataChannel)

	// 消费者处理 channel 数据
	go func() {
		s.doConsume(ctx, dataChannel)
		mainWaitChan <- struct{}{}
	}()

	<-mainWaitChan
	s.logger.InfoCtx(ctx, "[main] all done")

	_ = util.SetScriptAbort(ctx, s.redisClient, scriptName)

}

func (s *SearchableProductFieldsService) doProduce(ctx context.Context, dataChannel chan []searchResult) {
	organizationIDs := s.arg.OrganizationIDs
	producerLimit := make(chan struct{}, s.arg.ProducerCount)
	producerWaitGroup := sync.WaitGroup{}
	defer close(dataChannel)

	for _, organizationID := range organizationIDs {
		producerLimit <- struct{}{}
		producerWaitGroup.Add(1)
		inputOrgID := organizationID
		routine.WithRecover(s.logger, func() {
			defer producerWaitGroup.Done()
			err := s.produceIDs(ctx, inputOrgID, dataChannel)
			if err != nil {
				s.logger.ErrorCtx(ctx, "[produce] produceByOrganization error",
					zap.String("organization_id", inputOrgID),
					zap.Error(err),
				)
			}
			<-producerLimit
		})
	}
	producerWaitGroup.Wait() // 生产者都结束后会关闭 dataChannel
}

func (s *SearchableProductFieldsService) produceIDs(ctx context.Context, organizationID string, dataChannel chan []searchResult) error {

	ctx = log.AppendFieldsToContext(ctx, zap.String("organization_id", organizationID))

	bothConnections, err := s.connectorService.GetBothConnections(ctx, organizationID)
	if err != nil {
		return errors.WithStack(err)
	}

	if bothConnections.App.Platform != consts.Shopify {
		return nil
	}

	appPlatform := bothConnections.App.Platform
	appKey := bothConnections.App.Key

	args := &searchArgs{
		OrganizationID: organizationID,
		SourcePlatform: appPlatform,
		SourceStoreKey: appKey,
		Cursor:         consts.BeginningCursor,
		Limit:          s.arg.BatchSize,
	}

	count := 0

	for {

		running, _ := util.CheckSelfScriptRunning(ctx, s.redisClient, scriptName, s.instanceValue)
		if !running {
			s.logger.InfoCtx(ctx, "[produce] script is not running, stop produce")
			return nil
		}

		result, nextCursor, err := s.searchIDsAndCreatedAt(ctx, args)
		if err != nil {
			return errors.WithStack(err)
		}
		if len(result) > 0 {
			dataChannel <- result
		}

		count += len(result)
		s.logger.InfoCtx(ctx, "[produce] get searchable product ids",
			zap.Int("count", count),
			zap.Int("cur_count", len(result)),
		)

		if len(result) == 0 || nextCursor == "" {
			s.logger.InfoCtx(ctx, "[produce] all product are produced", zap.Int("count", count))
			break
		}
		args.Cursor = nextCursor
	}

	return nil
}

func (s *SearchableProductFieldsService) searchIDsAndCreatedAt(ctx context.Context, args *searchArgs) ([]searchResult, string, error) {

	query := elastic.NewBoolQuery()
	query = query.Must(elastic.NewTermQuery("organization.id", args.OrganizationID))
	query = query.Must(elastic.NewTermQuery("source.app.platform", args.SourcePlatform))
	query = query.Must(elastic.NewTermQuery("source.app.key", args.SourceStoreKey))

	searchService := s.esCli.Search().
		Index(searchable_products.IndexAliasV2).
		Query(query).
		Sort("updated_at", false).
		FetchSourceContext(elastic.NewFetchSourceContext(true).Include("id", "created_at"))

	cursor, err := elasticsearch.ParseCursor(args.Cursor)
	if err != nil {
		return nil, "", errors.WithMessage(elasticsearch.ErrorInvalidCursor, err.Error())
	}
	searchService.Size(args.Limit).SearchAfter(cursor...)

	result, err := searchService.Do(ctx)
	if err != nil {
		return nil, "", err
	}

	var searchResults []searchResult
	for _, hit := range result.Hits.Hits {
		sr := searchResult{}
		if err := json.Unmarshal(hit.Source, &sr); err != nil {
			return nil, "", err
		}
		sr.ID = hit.Id
		searchResults = append(searchResults, sr)
	}

	var nextCursor string
	if result.Hits != nil && len(result.Hits.Hits) > 0 {
		lastSort := result.Hits.Hits[len(result.Hits.Hits)-1].Sort
		nextCursor, err = elasticsearch.BuildNextCursor(lastSort)
		if err != nil {
			return nil, "", errors.WithStack(err)
		}
	}

	return searchResults, nextCursor, nil
}

func (s *SearchableProductFieldsService) doConsume(ctx context.Context, dataChannel chan []searchResult) {
	consumeWaitGroup := sync.WaitGroup{}
	for i := 0; i < s.arg.ConsumerCount; i++ {
		index := i
		consumeWaitGroup.Add(1)
		routine.WithRecover(s.logger, func() {
			defer consumeWaitGroup.Done()
			s.consumeIDs(ctx, dataChannel)
			s.logger.InfoCtx(ctx, fmt.Sprintf("[consume] consume goroutine end %d", index))
		})
	}
	consumeWaitGroup.Wait()
}

// consumeIDs 根据 IDs 查询 源 products 修改 ES models
// nolint:funlen
func (s *SearchableProductFieldsService) consumeIDs(ctx context.Context, dataChannel chan []searchResult) {

	for data := range dataChannel {

		running, _ := util.CheckSelfScriptRunning(ctx, s.redisClient, scriptName, s.instanceValue)
		if !running {
			s.logger.InfoCtx(ctx, "[consume] script is not running, stop consume")
			return
		}

		// 控制住消费速度
		if err := s.limiter.Wait(ctx); err != nil { // 延时等待
			s.logger.ErrorCtx(ctx, "[consume] rate limiter wait error", zap.Error(err))
			continue
		}

		ids := make([]string, 0, len(data))
		idCreatedAtMap := make(map[string]time.Time, len(data))
		for _, sr := range data {
			ids = append(ids, sr.ID)
			idCreatedAtMap[sr.ID] = sr.CreatedAt
		}

		/**
		1. 查询 products 源数据
		*/
		pcProducts, err := s.productsCenterClient.Product.List(ctx, &products_center.GetProductsArgs{
			IDs:   strings.Join(ids, ","),
			Limit: len(ids),
		})
		if err != nil {
			s.logger.ErrorCtx(ctx, "[consume] get products error",
				zap.String("ids", strings.Join(ids, ",")),
				zap.Error(err))
			continue
		}

		/**
		2. 定义需要更新的 ES model 字段
		*/
		type updateStruct struct {
			Index               string
			Id                  string
			SourceSalesChannels []map[string]interface{}
		}

		docsToUpdate := make([]updateStruct, 0)

		for _, product := range pcProducts {
			if len(product.SourceSalesChannels) == 0 {
				continue
			}

			createdAt, ok := idCreatedAtMap[product.ID]
			if !ok {
				s.logger.ErrorCtx(ctx, "[consume] get created_at error", zap.String("product_id", product.ID))
				continue
			}

			index, err := s.getIndex(createdAt)
			if err != nil {
				s.logger.ErrorCtx(ctx, "[consume] get index error",
					zap.String("product_id", product.ID),
					zap.Error(err))
				continue
			}

			sourceSalesChannels := make([]map[string]interface{}, 0)

			for _, sourceSalesChannel := range product.SourceSalesChannels {
				sourceSalesChannels = append(sourceSalesChannels, map[string]interface{}{
					"id":   sourceSalesChannel.ID,
					"name": sourceSalesChannel.Name,
				})
			}

			/**
			3. 构建目标字段 update req
			*/
			docsToUpdate = append(docsToUpdate, updateStruct{
				Index:               index,
				Id:                  product.ID,
				SourceSalesChannels: sourceSalesChannels,
			})
		}

		if len(docsToUpdate) == 0 {
			continue
		}

		bulkRequest := s.esCli.Bulk()

		/**
		4. 仅更新需要的字段
		*/
		for _, doc := range docsToUpdate {
			updateRequest := elastic.NewBulkUpdateRequest().
				Index(doc.Index).
				Id(doc.Id).
				Doc(map[string]interface{}{
					"source_sales_channels": doc.SourceSalesChannels,
				})

			bulkRequest = bulkRequest.Add(updateRequest)
		}

		// 执行批量请求
		bulkResponse, err := bulkRequest.Do(ctx)
		if err != nil {
			s.logger.ErrorCtx(ctx, "[consume] bulk all update error",
				zap.String("ids", strings.Join(ids, ",")),
				zap.Error(err))
			continue
		}

		// 检查批量响应中的错误
		if bulkResponse.Errors {
			// 处理部分失败的情况
			for _, item := range bulkResponse.Items {
				for _, result := range item {
					if result.Error != nil {
						s.logger.ErrorCtx(ctx, "[consume] bulk item update error",
							zap.String("result_id", result.Id),
							zap.Any("error", result.Error))
					}
				}
			}
		}

	}

	return
}

func (s *SearchableProductFieldsService) getIndex(createdAt time.Time) (string, error) {
	return fmt.Sprintf(searchable_products.IndexNameTemplateV2, strconv.Itoa(createdAt.Year())), nil
}

// ReadyCheck 当前脚本是单例任务
func (s *SearchableProductFieldsService) ReadyCheck(ctx context.Context) error {
	anyRunning, err := util.CheckAnyScriptRunning(ctx, s.redisClient, scriptName)
	if err != nil {
		return errors.WithStack(err)
	}
	if anyRunning {
		return errors.New("other script is running")
	}
	err = util.SetScriptRunning(ctx, s.redisClient, scriptName, s.instanceValue)
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func (s *SearchableProductFieldsService) GetScriptName() string {
	return scriptName
}
