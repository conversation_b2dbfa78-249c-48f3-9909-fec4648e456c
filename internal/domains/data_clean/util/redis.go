package util

import (
	"context"
	"fmt"
	"time"

	redis "github.com/go-redis/redis/v8"
	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/AfterShip/pltf-pd-product-listings/internal/logger"
)

const (
	RedisDataCleanPreKey        = "redis:listing:data:clean:flag:%s"
	RedisDataCleanKeyExpireTime = 24 * time.Hour
)

func SetScriptRunning(ctx context.Context, redisClient *redis.Client, scriptName string, tagValue string) error {
	result := redisClient.Set(ctx, fmt.Sprintf(RedisDataCleanPreKey, scriptName), tagValue, RedisDataCleanKeyExpireTime)
	if result.Err() != nil {
		logger.Get().WarnCtx(ctx, "set redis running tag error", zap.String("key", scriptName), zap.Error(result.Err()))
		return errors.WithStack(result.Err())
	}
	return nil
}

func SetScriptAbort(ctx context.Context, redisClient *redis.Client, scriptName string) error {
	result := redisClient.Del(ctx, fmt.Sprintf(RedisDataCleanPreKey, scriptName))
	if result.Err() != nil {
		logger.Get().WarnCtx(ctx, "remove redis running tag error", zap.String("key", scriptName), zap.Error(result.Err()))
		return errors.WithStack(result.Err())
	}
	return nil
}

func CheckAnyScriptRunning(ctx context.Context, redisClient *redis.Client, scriptName string) (bool, error) {
	result := redisClient.Get(ctx, fmt.Sprintf(RedisDataCleanPreKey, scriptName))
	if result.Err() != nil && errors.Is(result.Err(), redis.Nil) {
		return false, nil // nil error 表示没有任何任务在运行
	}
	return true, nil
}

func CheckSelfScriptRunning(ctx context.Context, redisClient *redis.Client, scriptName string, tagValue string) (bool, error) {
	result := redisClient.Get(ctx, fmt.Sprintf(RedisDataCleanPreKey, scriptName))
	if result.Err() != nil {
		if errors.Is(result.Err(), redis.Nil) {
			return false, nil // nil error 表示没有任何任务在运行
		}
		return false, errors.WithStack(result.Err())
	}
	if result.Val() == tagValue {
		return true, nil // value 和 tagValue 相等表示自身实例在运行
	}
	return false, nil // value 和 tagValue 不相等表示其他任务实例在运行
}
