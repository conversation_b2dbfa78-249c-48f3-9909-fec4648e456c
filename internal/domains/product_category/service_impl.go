package product_category

import (
	"context"
	"strconv"
	"strings"
	"sync"

	"go.uber.org/zap"

	"github.com/AfterShip/gopkg/log"
	"github.com/AfterShip/gopkg/routine"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/category"
	"github.com/AfterShip/pltf-pd-product-listings/internal/logger"
)

func (s *serviceImpl) GetProductCategoryAttributes(ctx context.Context, args *GetProductCategoryAttributesArgs) ([]category.Attribute, error) {
	// Validate
	if err := s.validate.Struct(args); err != nil {
		return nil, err
	}

	// Get category IDs from product listings
	salesChannelCategoryIDs, err := s.productListingService.AggregateCategoryIDs(ctx, args.ProductListingsQuery)
	if err != nil {
		return nil, err
	}

	ctx = log.AppendFieldsToContext(ctx, zap.String("sales_channel_category_ids", strings.Join(salesChannelCategoryIDs, ",")))

	// Get attributes by category IDs
	attributes, err := s.getProductCategoryAttributesByCategoryIDs(ctx, args, salesChannelCategoryIDs)
	if err != nil {
		return nil, err
	}

	// Filter attributes
	boolValue, _ := strconv.ParseBool(args.IsRequired)
	result := category.FilterProductCategoryAttributes(attributes, category.FilterAttributesArg{
		IsRequired: boolValue,
		Types:      args.Types,
	})

	return result, nil
}

func (s *serviceImpl) getProductCategoryAttributesByCategoryIDs(ctx context.Context, args *GetProductCategoryAttributesArgs,
	salesChannelCategoryIDs []string) ([]category.Attribute, error) {
	result := make([]category.Attribute, 0)

	// For removing duplicate attributes
	attributeIDs := make(map[string]bool)
	isRequired, _ := strconv.ParseBool(args.IsRequired)

	group := routine.Group{}
	lock := sync.Mutex{}
	limit := make(chan struct{}, 10)

	for _, externalCategoryID := range salesChannelCategoryIDs {
		id := externalCategoryID // Create a new copy of the loop variable
		limit <- struct{}{}
		group.GoWithRecover(logger.Get(), func() error {
			defer func() {
				<-limit
			}()
			data, err := s.categoryService.GetCategoryAttributes(ctx, &category.GetAttributesArg{
				OrganizationID:       args.OrganizationID,
				SalesChannelStoreKey: args.SalesChannelStoreKey,
				SalesChannelPlatform: args.SalesChannelPlatform,
				ExternalCategoryID:   id,
				IsRequired:           isRequired,
			})
			if err != nil {
				return err
			}

			for _, attribute := range data.Attributes {
				lock.Lock()
				if _, ok := attributeIDs[attribute.ID]; !ok {
					attributeIDs[attribute.ID] = true
					result = append(result, attribute)
				}
				lock.Unlock()
			}

			return nil
		})
	}

	if err := group.Wait(); err != nil {
		return nil, err
	}

	return result, nil
}

func (s *serviceImpl) GetProductCategoryRules(ctx context.Context, args *GetProductCategoryRulesArgs) ([]category.Rule, error) {
	// Validate
	if err := s.validate.Struct(args); err != nil {
		return nil, err
	}

	salesChannelCategoryIDs, err := s.productListingService.AggregateCategoryIDs(ctx, args.ProductListingsQuery)
	if err != nil {
		return nil, err
	}

	rules, err := s.getProductCategoryRulesByCategoryIDs(ctx, args, salesChannelCategoryIDs)
	if err != nil {
		return nil, err
	}

	return rules, nil
}

func (s *serviceImpl) getProductCategoryRulesByCategoryIDs(ctx context.Context, args *GetProductCategoryRulesArgs,
	salesChannelCategoryIDs []string) ([]category.Rule, error) {
	result := make([]category.Rule, 0)

	isRequired := false
	if strings.EqualFold(args.IsRequired, "true") {
		isRequired = true
	}
	group := routine.Group{}
	lock := sync.Mutex{}
	limit := make(chan struct{}, 10)

	for _, externalCategoryID := range salesChannelCategoryIDs {
		id := externalCategoryID // Create a new copy of the loop variable
		limit <- struct{}{}
		group.GoWithRecover(logger.Get(), func() error {
			defer func() {
				<-limit
			}()
			data, err := s.categoryService.GetCategoryRules(ctx, &category.GetRulesArg{
				OrganizationID:       args.OrganizationID,
				SalesChannelStoreKey: args.SalesChannelStoreKey,
				SalesChannelPlatform: args.SalesChannelPlatform,
				ExternalCategoryID:   id,
				IsRequired:           isRequired,
			})
			if err != nil {
				return err
			}

			lock.Lock()
			data.Rule.ExternalCategoryID = id
			result = append(result, data.Rule)
			lock.Unlock()

			return nil
		})
	}

	if err := group.Wait(); err != nil {
		return nil, err
	}

	return result, nil
}
