package product_category

import (
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/product_listing"
)

type GetProductCategoryAttributesArgs struct {
	OrganizationID       string                                    `validate:"required"`
	SalesChannelStoreKey string                                    `validate:"required"`
	SalesChannelPlatform string                                    `validate:"required"`
	ProductListingsQuery *product_listing.SearchProductListingArgs `validate:"required"`
	IsRequired           string                                    `validate:"omitempty,oneof=true false"`
	Types                []string
}

type GetProductCategoryRulesArgs struct {
	OrganizationID       string                                    `validate:"required"`
	SalesChannelStoreKey string                                    `validate:"required"`
	SalesChannelPlatform string                                    `validate:"required"`
	ProductListingsQuery *product_listing.SearchProductListingArgs `validate:"required"`
	IsRequired           string                                    `validate:"omitempty,oneof=true false"`
}
