package product_category

import (
	"context"
	"errors"
	"sort"
	"testing"

	"github.com/go-playground/validator/v10"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"

	"github.com/AfterShip/pltf-pd-product-listings/internal/consts"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/category"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/product_listing"
)

func Test_serviceImpl_getProductAttributesByCategoryIDs(t *testing.T) {

	categoryService := &category.MockCategoryService{}

	type fields struct {
		categoryService category.Service
	}
	type args struct {
		ctx                     context.Context
		args                    *GetProductCategoryAttributesArgs
		salesChannelCategoryIDs []string
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		mock   func()
		check  func(result []category.Attribute, err error)
	}{
		// TODO: Add test cases.
		{
			name: "Case 1: success",
			fields: fields{
				categoryService: categoryService,
			},
			args: args{
				ctx: context.Background(),
				args: &GetProductCategoryAttributesArgs{
					OrganizationID:       "3ae8df847ca34499b22653ee1581d2a3",
					SalesChannelStoreKey: "7955198705854",
					SalesChannelPlatform: "tiktok-shop",
				},
				salesChannelCategoryIDs: []string{"60002", "53000"},
			},
			mock: func() {
				categoryService.On("GetCategoryAttributes", mock.Anything, &category.GetAttributesArg{
					OrganizationID:       "3ae8df847ca34499b22653ee1581d2a3",
					SalesChannelStoreKey: "7955198705854",
					SalesChannelPlatform: "tiktok-shop",
					ExternalCategoryID:   "60002",
				}).Return(category.AttributesOutput{
					Attributes: []category.Attribute{
						{
							ID:         "1",
							IsRequired: false,
							Type:       consts.AttributeTypeProductProperty,
						},
						{
							ID:         "2",
							IsRequired: true,
							Type:       consts.AttributeTypeSalesProperty,
						},
					},
				}, nil).Once()

				categoryService.On("GetCategoryAttributes", mock.Anything, &category.GetAttributesArg{
					OrganizationID:       "3ae8df847ca34499b22653ee1581d2a3",
					SalesChannelStoreKey: "7955198705854",
					SalesChannelPlatform: "tiktok-shop",
					ExternalCategoryID:   "53000",
				}).Return(category.AttributesOutput{
					Attributes: []category.Attribute{
						{
							ID:         "1",
							IsRequired: false,
							Type:       consts.AttributeTypeProductProperty,
						},
						{
							ID:         "3",
							IsRequired: true,
							Type:       consts.AttributeTypeSalesProperty,
						},
					},
				}, nil).Once()
			},
			check: func(result []category.Attribute, err error) {
				require.NoError(t, err)
				expected := []category.Attribute{
					{
						ID:         "1",
						IsRequired: false,
						Type:       consts.AttributeTypeProductProperty,
					},
					{
						ID:         "2",
						IsRequired: true,
						Type:       consts.AttributeTypeSalesProperty,
					},
					{
						ID:         "3",
						IsRequired: true,
						Type:       consts.AttributeTypeSalesProperty,
					},
				}

				sort.Slice(expected, func(i, j int) bool {
					return expected[i].ID < expected[j].ID
				})
				sort.Slice(result, func(i, j int) bool {
					return result[i].ID < result[j].ID
				})
				require.Equal(t, expected, result)
			},
		},
		{
			name: "Case 2: error",
			fields: fields{
				categoryService: categoryService,
			},
			args: args{
				ctx: context.Background(),
				args: &GetProductCategoryAttributesArgs{
					OrganizationID:       "3ae8df847ca34499b22653ee1581d2a3",
					SalesChannelStoreKey: "7955198705854",
					SalesChannelPlatform: "tiktok-shop",
				},
				salesChannelCategoryIDs: []string{"60002", "53000"},
			},
			mock: func() {
				categoryService.On("GetCategoryAttributes", mock.Anything, &category.GetAttributesArg{
					OrganizationID:       "3ae8df847ca34499b22653ee1581d2a3",
					SalesChannelStoreKey: "7955198705854",
					SalesChannelPlatform: "tiktok-shop",
					ExternalCategoryID:   "60002",
				}).Return(category.AttributesOutput{
					Attributes: []category.Attribute{
						{
							ID:         "1",
							IsRequired: false,
							Type:       consts.AttributeTypeProductProperty,
						},
						{
							ID:         "2",
							IsRequired: true,
							Type:       consts.AttributeTypeSalesProperty,
						},
					},
				}, nil).Once()

				categoryService.On("GetCategoryAttributes", mock.Anything, &category.GetAttributesArg{
					OrganizationID:       "3ae8df847ca34499b22653ee1581d2a3",
					SalesChannelStoreKey: "7955198705854",
					SalesChannelPlatform: "tiktok-shop",
					ExternalCategoryID:   "53000",
				}).Return(category.AttributesOutput{}, errors.New("not found")).Once()
			},
			check: func(result []category.Attribute, err error) {
				require.Error(t, err)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &serviceImpl{
				categoryService: tt.fields.categoryService,
			}

			tt.mock()
			got, err := s.getProductCategoryAttributesByCategoryIDs(tt.args.ctx, tt.args.args, tt.args.salesChannelCategoryIDs)
			tt.check(got, err)
		})
	}
}

func Test_serviceImpl_GetProductAttributes(t *testing.T) {
	productListingService := &product_listing.MockProductListingService{}
	categoryService := &category.MockCategoryService{}
	type fields struct {
		productListingService product_listing.Service
		categoryService       category.Service
		validate              *validator.Validate
	}
	type args struct {
		ctx  context.Context
		args *GetProductCategoryAttributesArgs
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		mock   func()
		check  func(result []category.Attribute, err error)
	}{
		// TODO: Add test cases.
		{
			name: "Case 1: success",
			fields: fields{
				productListingService: productListingService,
				categoryService:       categoryService,
				validate:              validator.New(),
			},
			args: args{
				ctx: context.Background(),
				args: &GetProductCategoryAttributesArgs{
					OrganizationID:       "3ae8df847ca34499b22653ee1581d2a3",
					SalesChannelStoreKey: "7955198705854",
					SalesChannelPlatform: "tiktok-shop",
					ProductListingsQuery: &product_listing.SearchProductListingArgs{
						OrganizationID: "3ae8df847ca34499b22653ee1581d2a3",
						States:         []string{string(consts.ProductListingProductStatePending)},
						Limit:          100,
					},
					IsRequired: "true",
				},
			},
			mock: func() {
				productListingService.On("AggregateCategoryIDs", mock.Anything, mock.Anything).Return([]string{"60002", "53000"}, nil).Once()
				categoryService.On("GetCategoryAttributes", mock.Anything, &category.GetAttributesArg{
					OrganizationID:       "3ae8df847ca34499b22653ee1581d2a3",
					SalesChannelStoreKey: "7955198705854",
					SalesChannelPlatform: "tiktok-shop",
					ExternalCategoryID:   "60002",
					IsRequired:           true,
				}).Return(category.AttributesOutput{
					Attributes: []category.Attribute{
						{
							ID:         "1",
							IsRequired: false,
							Type:       consts.AttributeTypeProductProperty,
						},
						{
							ID:         "2",
							IsRequired: true,
							Type:       consts.AttributeTypeSalesProperty,
						},
					},
				}, nil).Once()

				categoryService.On("GetCategoryAttributes", mock.Anything, &category.GetAttributesArg{
					OrganizationID:       "3ae8df847ca34499b22653ee1581d2a3",
					SalesChannelStoreKey: "7955198705854",
					SalesChannelPlatform: "tiktok-shop",
					ExternalCategoryID:   "53000",
					IsRequired:           true,
				}).Return(category.AttributesOutput{
					Attributes: []category.Attribute{
						{
							ID:         "1",
							IsRequired: false,
							Type:       consts.AttributeTypeProductProperty,
						},
						{
							ID:         "3",
							IsRequired: true,
							Type:       consts.AttributeTypeProductProperty,
						},
					},
				}, nil).Once()
			},
			check: func(result []category.Attribute, err error) {
				require.NoError(t, err)
				expected := []category.Attribute{
					{
						ID:         "3",
						IsRequired: true,
						Type:       consts.AttributeTypeProductProperty,
					},
				}
				require.Equal(t, expected, result)
			},
		},
		{
			name: "Case 2: error",
			fields: fields{
				productListingService: productListingService,
				categoryService:       categoryService,
				validate:              validator.New(),
			},
			args: args{
				ctx: context.Background(),
				args: &GetProductCategoryAttributesArgs{
					OrganizationID:       "3ae8df847ca34499b22653ee1581d2a3",
					SalesChannelStoreKey: "7955198705854",
					SalesChannelPlatform: "tiktok-shop",
					ProductListingsQuery: &product_listing.SearchProductListingArgs{
						OrganizationID: "3ae8df847ca34499b22653ee1581d2a3",
						States:         []string{string(consts.ProductListingProductStatePending)},
						Limit:          100,
					},
					IsRequired: "enabled",
				},
			},
			mock: func() {},
			check: func(result []category.Attribute, err error) {
				require.Error(t, err)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &serviceImpl{
				productListingService: tt.fields.productListingService,
				categoryService:       tt.fields.categoryService,
				validate:              tt.fields.validate,
			}

			tt.mock()

			got, err := s.GetProductCategoryAttributes(tt.args.ctx, tt.args.args)
			tt.check(got, err)

		})
	}
}
