// Code generated by mockery v2.40.1. DO NOT EDIT.

package product_category

import (
	context "context"

	category "github.com/AfterShip/pltf-pd-product-listings/internal/domains/category"

	mock "github.com/stretchr/testify/mock"
)

// MockService is an autogenerated mock type for the Service type
type MockService struct {
	mock.Mock
}

// GetProductAttributes provides a mock function with given fields: ctx, args
func (_m *MockService) GetProductCategoryAttributes(ctx context.Context, args *GetProductCategoryAttributesArgs) ([]category.Attribute, error) {
	ret := _m.Called(ctx, args)

	if len(ret) == 0 {
		panic("no return value specified for GetProductAttributes")
	}

	var r0 []category.Attribute
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *GetProductCategoryAttributesArgs) ([]category.Attribute, error)); ok {
		return rf(ctx, args)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *GetProductCategoryAttributesArgs) []category.Attribute); ok {
		r0 = rf(ctx, args)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]category.Attribute)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *GetProductCategoryAttributesArgs) error); ok {
		r1 = rf(ctx, args)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

func (_m MockService) GetProductCategoryRules(ctx context.Context, args *GetProductCategoryRulesArgs)([]category.Rule, error){
	resp := _m.Called(ctx, args)
	return resp.Get(0).([]category.Rule), resp.Error(1)
}

// NewMockService creates a new instance of MockService. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockService(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockService {
	mock := &MockService{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
