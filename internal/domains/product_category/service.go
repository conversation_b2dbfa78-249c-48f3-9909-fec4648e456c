package product_category

import (
	"context"

	"github.com/go-playground/validator/v10"

	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/category"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/product_listing"
)

type Service interface {
	GetProductCategoryAttributes(ctx context.Context, args *GetProductCategoryAttributesArgs) ([]category.Attribute, error)
	GetProductCategoryRules(ctx context.Context, args *GetProductCategoryRulesArgs) ([]category.Rule, error)
}

type serviceImpl struct {
	productListingService product_listing.Service
	categoryService       category.Service
	validate              *validator.Validate
}

var _ Service = &serviceImpl{}

func NewService(categoryService category.Service, productListingService product_listing.Service) Service {
	return &serviceImpl{
		productListingService: productListingService,
		categoryService:       categoryService,
		validate:              validator.New(),
	}
}
