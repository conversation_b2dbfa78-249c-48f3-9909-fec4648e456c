package databus

import (
	"context"

	"cloud.google.com/go/pubsub"
	jsoniter "github.com/json-iterator/go"
	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/AfterShip/connectors-library/utils"
	"github.com/AfterShip/gopkg/pubsubx"
	"github.com/AfterShip/pltf-pd-product-listings/internal/logger"
)

type Service interface {
	SendToPubSub(ctx context.Context, topicName string, data []byte, meta PubSubMeta) error
}

func NewService(pubSubClient *pubsubx.Client) *serviceImpl {
	return &serviceImpl{
		PubSubClient: pubSubClient,
	}
}

type serviceImpl struct {
	PubSubClient *pubsubx.Client
}

func (impl *serviceImpl) SendToPubSub(ctx context.Context, topicName string, data []byte, meta PubSubMeta) error {
	msg := PubSubMessage{Data: data}
	msgByte, _ := jsoniter.Marshal(msg)
	attributes := meta.ToAttributes(ctx)
	data, err := utils.Gzip(msgByte)
	if err != nil {
		logger.Get().Error("gzip error",
			zap.Error(err),
			zap.Any("data", string(msgByte)),
			zap.String("message_id", attributes["id"]),
			zap.String("org_id", meta.OrgID),
			zap.String("x_app_name", attributes["x_app_name"]),
			zap.String("type", meta.Type),
			zap.String("event", meta.Event),
			zap.String("event_ts", attributes["event_ts"]),
			zap.String("trace_id", attributes["am_trace_id"]),
		)
		return errors.WithStack(err)
	}
	publishMsg := pubsub.Message{
		Data:       data,
		Attributes: attributes,
	}

	_, err = impl.PubSubClient.Publish(ctx, topicName, &publishMsg)
	if err != nil {
		logger.Get().Error("send to pubsub error",
			zap.Error(err),
			zap.Any("data", string(msgByte)),
			zap.String("message_id", attributes["id"]),
			zap.String("org_id", meta.OrgID),
			zap.String("x_app_name", attributes["x_app_name"]),
			zap.String("type", meta.Type),
			zap.String("event", meta.Event),
			zap.String("event_ts", attributes["event_ts"]),
			zap.String("trace_id", attributes["am_trace_id"]),
		)
		return errors.WithStack(err)
	}
	return nil
}
