package databus

import (
	"context"
	"encoding/json"
	"strconv"
	"time"

	"github.com/AfterShip/gopkg/uuid"
)

const (
	ContextKeyTraceID = "automizelyTraceID"
	AppName           = "product_listings"
)

type PubSubMessage struct {
	Data json.RawMessage `json:"data"`
}

type PubSubMeta struct {
	Type  string
	Event string
	OrgID string
	// 如果有自定义的属性，可以放在这里，但是必须是 x_ 前缀开头
	CustomerAttributes map[string]string
}

func (meta PubSubMeta) ToAttributes(ctx context.Context) map[string]string {
	// generate attributes
	attributes := make(map[string]string)
	attributes["x_app_name"] = AppName
	if len(meta.Type) != 0 {
		attributes["type"] = meta.Type
	}
	if len(meta.Event) != 0 {
		attributes["event"] = meta.Event
	}
	if len(meta.OrgID) != 0 {
		attributes["org_id"] = meta.OrgID
	}
	attributes["id"] = uuid.GenerateUUIDV4()
	attributes["event_ts"] = strconv.FormatInt(time.Now().UnixNano()/1e6, 10)
	// default gzip
	attributes["compression"] = "gzip"
	// add trace id
	traceID, _ := ctx.Value(ContextKeyTraceID).(string)
	attributes["am_trace_id"] = traceID

	for k, v := range meta.CustomerAttributes {
		attributes[k] = v
	}

	return attributes
}
