package calculators

import (
	"encoding/json"
	"os"
	"reflect"
	"sort"
	"testing"

	"github.com/pkg/errors"
	"github.com/stretchr/testify/require"

	connector_sdk_v2_inventory_level "github.com/AfterShip/connectors-sdk-go/v2/inventory_levels"
	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/pltf-pd-product-listings/internal/consts"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
)

func TestCalculateAvailableQuantitiesOutput_GetAvailableQuantity(t *testing.T) {
	type fields struct {
		InventorySync         *models.InventorySync
		ProductsCenterProduct *ProductsCenterProductOut
	}
	type args struct {
		productsCenterVariantID string
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   float64
		want1  error
	}{
		{
			name: "Test Case 1: Variant exists",
			fields: fields{
				ProductsCenterProduct: &ProductsCenterProductOut{
					Variants: []CalculateAvailableQuantitiesVariantOutput{
						{
							ID:                          "variant1",
							Status:                      consts.CalculateSuccess,
							CalculatedAvailableQuantity: 100,
						},
					},
				},
			},
			args: args{
				productsCenterVariantID: "variant1",
			},
			want:  100,
			want1: nil,
		},
		{
			name: "Test Case 2: Variant does not exist",
			fields: fields{
				ProductsCenterProduct: &ProductsCenterProductOut{
					Variants: []CalculateAvailableQuantitiesVariantOutput{
						{
							ID:                          "variant1",
							Status:                      consts.CalculateSuccess,
							CalculatedAvailableQuantity: 100,
						},
					},
				},
			},
			args: args{
				productsCenterVariantID: "variant2",
			},
			want:  0,
			want1: ErrInventoryNotFound,
		},
		{
			name: "Test Case 3: Calculation failed",
			fields: fields{
				ProductsCenterProduct: &ProductsCenterProductOut{
					Variants: []CalculateAvailableQuantitiesVariantOutput{
						{
							ID:                          "variant1",
							Status:                      consts.CalculateFailed,
							CalculatedAvailableQuantity: 100,
						},
					},
				},
			},
			args: args{
				productsCenterVariantID: "variant1",
			},
			want:  0,
			want1: ErrCalculateInventoryFailed,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			output := &CalculateAvailableQuantitiesOutput{
				InventorySync:         tt.fields.InventorySync,
				ProductsCenterProduct: tt.fields.ProductsCenterProduct,
			}
			got, got1 := output.GetAvailableQuantity(tt.args.productsCenterVariantID)
			if got != tt.want {
				t.Errorf("GetAvailableQuantity() got = %v, want %v", got, tt.want)
			}
			if !errors.Is(got1, tt.want1) {
				t.Errorf("GetAvailableQuantity() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}

func Test_wrapQuantityOutputWithTikTokSpecialCases(t *testing.T) {
	bytes, err := os.ReadFile("./testdata/calculate_quantity_output.json")
	require.NoError(t, err)
	output := new(CalculateAvailableQuantitiesOutput)
	json.Unmarshal(bytes, &output)
	tests := []struct {
		name         string
		input        *CalculateAvailableQuantitiesInput
		output       *CalculateAvailableQuantitiesOutput
		wantQuantity float64
	}{
		{
			name: "test follow source back order,ecommerce continue sell when out of stock",
			input: &CalculateAvailableQuantitiesInput{
				InventorySync: &models.InventorySync{
					FollowSourceAllowBackorder: string(consts.AllowSyncEnabled),
				},
				ProductsCenterProduct: &CalculateAvailableQuantitiesProductsCenterProductInput{
					Variants: []CalculateAvailableQuantitiesVariantInput{
						{
							ID:                      "7716c664528344c9baae8603836de36c",
							ExternalID:              "44608203620609",
							AllowBackorder:          true,
							AvailableQuantity:       10,
							ExternalInventoryItemID: "46709721956609",
						},
					},
				},
			},
			output: &CalculateAvailableQuantitiesOutput{
				InventorySync: &models.InventorySync{
					FollowSourceAllowBackorder: string(consts.AllowSyncEnabled),
				},
				ProductsCenterProduct: &ProductsCenterProductOut{
					Variants: []CalculateAvailableQuantitiesVariantOutput{
						{
							ExternalID:                  "44608203620609",
							CalculatedAvailableQuantity: 10,
							HitThreshold:                false,
						},
					},
				},
			},
			wantQuantity: float64(consts.TikTokMaxInventory),
		},
		{
			name: "test not follow source back order,ecommerce continue sell when out of stock",
			input: &CalculateAvailableQuantitiesInput{
				InventorySync: &models.InventorySync{
					FollowSourceAllowBackorder: string(consts.AllowSyncDisabled),
				},
				ProductsCenterProduct: &CalculateAvailableQuantitiesProductsCenterProductInput{
					Variants: []CalculateAvailableQuantitiesVariantInput{
						{
							ID:                      "7716c664528344c9baae8603836de36c",
							ExternalID:              "44608203620609",
							AllowBackorder:          true,
							AvailableQuantity:       10,
							ExternalInventoryItemID: "46709721956609",
						},
					},
				},
			},
			output: &CalculateAvailableQuantitiesOutput{
				InventorySync: &models.InventorySync{
					FollowSourceAllowBackorder: string(consts.AllowSyncEnabled),
				},
				ProductsCenterProduct: &ProductsCenterProductOut{
					Variants: []CalculateAvailableQuantitiesVariantOutput{
						{
							ExternalID:                  "44608203620609",
							CalculatedAvailableQuantity: 10,
							HitThreshold:                false,
						},
					},
				},
			},
			wantQuantity: 10,
		},
		{
			name: "not continue sell when out of stock,but hit threshold",
			input: &CalculateAvailableQuantitiesInput{
				InventorySync: &models.InventorySync{
					FollowSourceAllowBackorder: string(consts.AllowSyncDisabled),
				},
				ProductsCenterProduct: &CalculateAvailableQuantitiesProductsCenterProductInput{
					Variants: []CalculateAvailableQuantitiesVariantInput{
						{
							ID:                      "7716c664528344c9baae8603836de36c",
							ExternalID:              "44608203620609",
							AllowBackorder:          false,
							AvailableQuantity:       10,
							ExternalInventoryItemID: "46709721956609",
						},
					},
				},
			},
			output: &CalculateAvailableQuantitiesOutput{
				InventorySync: &models.InventorySync{
					FollowSourceAllowBackorder: string(consts.AllowSyncEnabled),
				},
				ProductsCenterProduct: &ProductsCenterProductOut{
					Variants: []CalculateAvailableQuantitiesVariantOutput{
						{
							ExternalID:                  "44608203620609",
							CalculatedAvailableQuantity: 10,
							HitThreshold:                true,
						},
					},
				},
			},
			wantQuantity: 0,
		},
		{
			name: "test NegativeQuantity",
			input: &CalculateAvailableQuantitiesInput{
				InventorySync: &models.InventorySync{
					FollowSourceAllowBackorder: string(consts.AllowSyncDisabled),
				},
				ProductsCenterProduct: &CalculateAvailableQuantitiesProductsCenterProductInput{
					Variants: []CalculateAvailableQuantitiesVariantInput{
						{
							ID:                      "7716c664528344c9baae8603836de36c",
							ExternalID:              "44608203620609",
							AllowBackorder:          false,
							AvailableQuantity:       10,
							ExternalInventoryItemID: "46709721956609",
						},
					},
				},
			},
			output: &CalculateAvailableQuantitiesOutput{
				InventorySync: &models.InventorySync{
					FollowSourceAllowBackorder: string(consts.AllowSyncEnabled),
				},
				ProductsCenterProduct: &ProductsCenterProductOut{
					Variants: []CalculateAvailableQuantitiesVariantOutput{
						{
							ExternalID:                  "44608203620609",
							CalculatedAvailableQuantity: -1,
							HitThreshold:                false,
						},
					},
				},
			},
			wantQuantity: 0,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.input.wrapOutputWithTikTokSpecialCases(tt.output, false)
			require.Equal(t, tt.wantQuantity, tt.output.ProductsCenterProduct.Variants[0].CalculatedAvailableQuantity)
		})
	}

}

func TestCalculateAvailableQuantitiesInput_gatherExternalInventoryItemIDs(t *testing.T) {
	tests := []struct {
		name  string
		input *CalculateAvailableQuantitiesInput
		want  []string
	}{
		{
			name: "正常收集多个库存项ID",
			input: &CalculateAvailableQuantitiesInput{
				ProductsCenterProduct: &CalculateAvailableQuantitiesProductsCenterProductInput{
					Variants: []CalculateAvailableQuantitiesVariantInput{
						{
							ID:                      "var1",
							ExternalID:              "ext1",
							ExternalInventoryItemID: "inv1",
						},
						{
							ID:                      "var2",
							ExternalID:              "ext2",
							ExternalInventoryItemID: "inv2",
						},
						{
							ID:                      "var3",
							ExternalID:              "ext3",
							ExternalInventoryItemID: "inv3",
						},
					},
				},
			},
			want: []string{"inv1", "inv2", "inv3"},
		},
		{
			name: "有重复的库存项ID",
			input: &CalculateAvailableQuantitiesInput{
				ProductsCenterProduct: &CalculateAvailableQuantitiesProductsCenterProductInput{
					Variants: []CalculateAvailableQuantitiesVariantInput{
						{
							ID:                      "var1",
							ExternalID:              "ext1",
							ExternalInventoryItemID: "inv1",
						},
						{
							ID:                      "var2",
							ExternalID:              "ext2",
							ExternalInventoryItemID: "inv1", // 重复的ID
						},
						{
							ID:                      "var3",
							ExternalID:              "ext3",
							ExternalInventoryItemID: "inv2",
						},
					},
				},
			},
			want: []string{"inv1", "inv2"}, // 应该去重
		},
		{
			name: "部分变体没有库存项ID",
			input: &CalculateAvailableQuantitiesInput{
				ProductsCenterProduct: &CalculateAvailableQuantitiesProductsCenterProductInput{
					Variants: []CalculateAvailableQuantitiesVariantInput{
						{
							ID:                      "var1",
							ExternalID:              "ext1",
							ExternalInventoryItemID: "inv1",
						},
						{
							ID:                      "var2",
							ExternalID:              "ext2",
							ExternalInventoryItemID: "", // 空ID
						},
						{
							ID:                      "var3",
							ExternalID:              "ext3",
							ExternalInventoryItemID: "inv2",
						},
					},
				},
			},
			want: []string{"inv1", "inv2"}, // 应该跳过空ID
		},
		{
			name: "没有变体",
			input: &CalculateAvailableQuantitiesInput{
				ProductsCenterProduct: &CalculateAvailableQuantitiesProductsCenterProductInput{
					Variants: []CalculateAvailableQuantitiesVariantInput{},
				},
			},
			want: []string{}, // 返回空切片
		},
		{
			name: "所有变体都没有库存项ID",
			input: &CalculateAvailableQuantitiesInput{
				ProductsCenterProduct: &CalculateAvailableQuantitiesProductsCenterProductInput{
					Variants: []CalculateAvailableQuantitiesVariantInput{
						{
							ID:                      "var1",
							ExternalID:              "ext1",
							ExternalInventoryItemID: "",
						},
						{
							ID:                      "var2",
							ExternalID:              "ext2",
							ExternalInventoryItemID: "",
						},
					},
				},
			},
			want: []string{}, // 返回空切片
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := tt.input.gatherExternalInventoryItemIDs()

			// 由于函数内部使用了slicex.UniqueSlice，可能会改变元素顺序
			// 所以我们需要排序后再比较
			sort.Strings(got)
			sort.Strings(tt.want)

			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("gatherExternalInventoryItemIDs() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestCalculateAvailableQuantitiesInput_buildVariantsQuantityPairs(t *testing.T) {
	tests := []struct {
		name                string
		input               *CalculateAvailableQuantitiesInput
		isCareVariantStatus bool
		expected            map[string]quantityPair
	}{
		{
			name: "基本测试-不关心变体状态",
			input: &CalculateAvailableQuantitiesInput{
				ProductsCenterProduct: &CalculateAvailableQuantitiesProductsCenterProductInput{
					Variants: []CalculateAvailableQuantitiesVariantInput{
						{
							ID:                "var1",
							ExternalID:        "ext1",
							AvailableQuantity: 10,
							AllowBackorder:    true,
							Status:            consts.ProductsCenterVariantStatusActive,
						},
						{
							ID:                "var2",
							ExternalID:        "ext2",
							AvailableQuantity: 20,
							AllowBackorder:    false,
							Status:            consts.ProductsCenterVariantStatusActive,
						},
					},
				},
			},
			isCareVariantStatus: false,
			expected: map[string]quantityPair{
				"ext1": {
					AvailableQuantity: 10,
					AllowBackorder:    true,
					QuantityFrom:      consts.QuantityFromInputVariant,
				},
				"ext2": {
					AvailableQuantity: 20,
					AllowBackorder:    false,
					QuantityFrom:      consts.QuantityFromInputVariant,
				},
			},
		},
		{
			name: "关心变体状态-全部变体均为激活状态",
			input: &CalculateAvailableQuantitiesInput{
				ProductsCenterProduct: &CalculateAvailableQuantitiesProductsCenterProductInput{
					Variants: []CalculateAvailableQuantitiesVariantInput{
						{
							ID:                "var1",
							ExternalID:        "ext1",
							AvailableQuantity: 10,
							AllowBackorder:    true,
							Status:            consts.ProductsCenterVariantStatusActive,
						},
						{
							ID:                "var2",
							ExternalID:        "ext2",
							AvailableQuantity: 20,
							AllowBackorder:    false,
							Status:            consts.ProductsCenterVariantStatusActive,
						},
					},
				},
			},
			isCareVariantStatus: true,
			expected: map[string]quantityPair{
				"ext1": {
					AvailableQuantity: 10,
					AllowBackorder:    true,
					QuantityFrom:      consts.QuantityFromInputVariant,
				},
				"ext2": {
					AvailableQuantity: 20,
					AllowBackorder:    false,
					QuantityFrom:      consts.QuantityFromInputVariant,
				},
			},
		},
		{
			name: "关心变体状态-包含下架变体",
			input: &CalculateAvailableQuantitiesInput{
				ProductsCenterProduct: &CalculateAvailableQuantitiesProductsCenterProductInput{
					Variants: []CalculateAvailableQuantitiesVariantInput{
						{
							ID:                "var1",
							ExternalID:        "ext1",
							AvailableQuantity: 10,
							AllowBackorder:    true,
							Status:            consts.ProductsCenterVariantStatusActive,
						},
						{
							ID:                "var2",
							ExternalID:        "ext2",
							AvailableQuantity: 20,
							AllowBackorder:    false,
							Status:            consts.ProductsCenterVariantStatusInActive,
						},
					},
				},
			},
			isCareVariantStatus: true,
			expected: map[string]quantityPair{
				"ext1": {
					AvailableQuantity: 10,
					AllowBackorder:    true,
					QuantityFrom:      consts.QuantityFromInputVariant,
				},
				"ext2": {
					AvailableQuantity: 0,
					AllowBackorder:    false,
					QuantityFrom:      consts.QuantityFromInactiveVariant,
				},
			},
		},
		{
			name: "混合状态-关心变体状态",
			input: &CalculateAvailableQuantitiesInput{
				ProductsCenterProduct: &CalculateAvailableQuantitiesProductsCenterProductInput{
					Variants: []CalculateAvailableQuantitiesVariantInput{
						{
							ID:                "var1",
							ExternalID:        "ext1",
							AvailableQuantity: 10,
							AllowBackorder:    true,
							Status:            consts.ProductsCenterVariantStatusActive,
						},
						{
							ID:                "var2",
							ExternalID:        "ext2",
							AvailableQuantity: 20,
							AllowBackorder:    true,
							Status:            consts.ProductsCenterVariantStatusInActive,
						},
						{
							ID:                "var3",
							ExternalID:        "ext3",
							AvailableQuantity: 30,
							AllowBackorder:    false,
							Status:            consts.ProductsCenterVariantStatusActive,
						},
					},
				},
			},
			isCareVariantStatus: true,
			expected: map[string]quantityPair{
				"ext1": {
					AvailableQuantity: 10,
					AllowBackorder:    true,
					QuantityFrom:      consts.QuantityFromInputVariant,
				},
				"ext2": {
					AvailableQuantity: 0,
					AllowBackorder:    false,
					QuantityFrom:      consts.QuantityFromInactiveVariant,
				},
				"ext3": {
					AvailableQuantity: 30,
					AllowBackorder:    false,
					QuantityFrom:      consts.QuantityFromInputVariant,
				},
			},
		},
		{
			name: "混合状态-不关心变体状态",
			input: &CalculateAvailableQuantitiesInput{
				ProductsCenterProduct: &CalculateAvailableQuantitiesProductsCenterProductInput{
					Variants: []CalculateAvailableQuantitiesVariantInput{
						{
							ID:                "var1",
							ExternalID:        "ext1",
							AvailableQuantity: 10,
							AllowBackorder:    true,
							Status:            consts.ProductsCenterVariantStatusActive,
						},
						{
							ID:                "var2",
							ExternalID:        "ext2",
							AvailableQuantity: 20,
							AllowBackorder:    true,
							Status:            consts.ProductsCenterVariantStatusInActive,
						},
					},
				},
			},
			isCareVariantStatus: false,
			expected: map[string]quantityPair{
				"ext1": {
					AvailableQuantity: 10,
					AllowBackorder:    true,
					QuantityFrom:      consts.QuantityFromInputVariant,
				},
				"ext2": {
					AvailableQuantity: 20,
					AllowBackorder:    true,
					QuantityFrom:      consts.QuantityFromInputVariant,
				},
			},
		},
		{
			name: "没有变体",
			input: &CalculateAvailableQuantitiesInput{
				ProductsCenterProduct: &CalculateAvailableQuantitiesProductsCenterProductInput{
					Variants: []CalculateAvailableQuantitiesVariantInput{},
				},
			},
			isCareVariantStatus: true,
			expected:            map[string]quantityPair{},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			actual := tt.input.buildVariantsQuantityPairs(tt.isCareVariantStatus)

			if len(actual) != len(tt.expected) {
				t.Errorf("结果长度不匹配: got %d, want %d", len(actual), len(tt.expected))
			}

			for externalID, expectedPair := range tt.expected {
				actualPair, exists := actual[externalID]
				if !exists {
					t.Errorf("未找到外部ID为 %s 的变体", externalID)
					continue
				}

				if actualPair.AvailableQuantity != expectedPair.AvailableQuantity {
					t.Errorf("变体 %s 的 AvailableQuantity = %v, 期望 %v",
						externalID, actualPair.AvailableQuantity, expectedPair.AvailableQuantity)
				}

				if actualPair.AllowBackorder != expectedPair.AllowBackorder {
					t.Errorf("变体 %s 的 AllowBackorder = %v, 期望 %v",
						externalID, actualPair.AllowBackorder, expectedPair.AllowBackorder)
				}

				if actualPair.QuantityFrom != expectedPair.QuantityFrom {
					t.Errorf("变体 %s 的 QuantityFrom = %v, 期望 %v",
						externalID, actualPair.QuantityFrom, expectedPair.QuantityFrom)
				}
			}
		})
	}
}

func TestCalculateAvailableQuantitiesInput_buildQuantityPairFromInventoryLevel(t *testing.T) {
	tests := []struct {
		name                     string
		input                    *CalculateAvailableQuantitiesInput
		inventorySync            *models.InventorySync
		connectorInventoryLevels []connector_sdk_v2_inventory_level.ModelsResponseInventoryLevel2
		expected                 map[string]quantityPair
	}{
		{
			name: "基本测试-全部仓库在多仓库配置中",
			input: &CalculateAvailableQuantitiesInput{
				ProductsCenterProduct: &CalculateAvailableQuantitiesProductsCenterProductInput{
					Variants: []CalculateAvailableQuantitiesVariantInput{
						{
							ID:                      "var1",
							ExternalID:              "ext1",
							ExternalInventoryItemID: "inv1",
							AllowBackorder:          true,
						},
						{
							ID:                      "var2",
							ExternalID:              "ext2",
							ExternalInventoryItemID: "inv2",
							AllowBackorder:          false,
						},
					},
				},
			},
			inventorySync: &models.InventorySync{
				ActiveWarehouses: []models.ActiveWarehouse{
					{
						SourceWarehouseId: "wh1",
						State:             "enabled",
					},
					{
						SourceWarehouseId: "wh2",
						State:             "enabled",
					},
				},
			},
			connectorInventoryLevels: []connector_sdk_v2_inventory_level.ModelsResponseInventoryLevel2{
				{
					ExternalInventoryItemID: types.MakeString("inv1"),
					ExternalWarehouseID:     types.MakeString("wh1"),
					AvailableQuantity:       types.MakeInt(10),
				},
				{
					ExternalInventoryItemID: types.MakeString("inv1"),
					ExternalWarehouseID:     types.MakeString("wh2"),
					AvailableQuantity:       types.MakeInt(20),
				},
				{
					ExternalInventoryItemID: types.MakeString("inv2"),
					ExternalWarehouseID:     types.MakeString("wh1"),
					AvailableQuantity:       types.MakeInt(30),
				},
			},
			expected: map[string]quantityPair{
				"ext1": {
					AvailableQuantity: 30, // 10 + 20
					AllowBackorder:    true,
					QuantityFrom:      consts.QuantityFromInventoryLevel,
				},
				"ext2": {
					AvailableQuantity: 30,
					AllowBackorder:    false,
					QuantityFrom:      consts.QuantityFromInventoryLevel,
				},
			},
		},
		{
			name: "部分仓库在多仓库配置中",
			input: &CalculateAvailableQuantitiesInput{
				ProductsCenterProduct: &CalculateAvailableQuantitiesProductsCenterProductInput{
					Variants: []CalculateAvailableQuantitiesVariantInput{
						{
							ID:                      "var1",
							ExternalID:              "ext1",
							ExternalInventoryItemID: "inv1",
							AllowBackorder:          true,
						},
					},
				},
			},
			inventorySync: &models.InventorySync{
				ActiveWarehouses: []models.ActiveWarehouse{
					{
						SourceWarehouseId: "wh1",
						State:             "enabled",
					},
					{
						SourceWarehouseId: "wh3",
						State:             "enabled",
					},
				},
			},
			connectorInventoryLevels: []connector_sdk_v2_inventory_level.ModelsResponseInventoryLevel2{
				{
					ExternalInventoryItemID: types.MakeString("inv1"),
					ExternalWarehouseID:     types.MakeString("wh1"),
					AvailableQuantity:       types.MakeInt(10),
				},
				{
					ExternalInventoryItemID: types.MakeString("inv1"),
					ExternalWarehouseID:     types.MakeString("wh2"),
					AvailableQuantity:       types.MakeInt(20),
				},
				{
					ExternalInventoryItemID: types.MakeString("inv1"),
					ExternalWarehouseID:     types.MakeString("wh3"),
					AvailableQuantity:       types.MakeInt(30),
				},
			},
			expected: map[string]quantityPair{
				"ext1": {
					AvailableQuantity: 40, // 10 + 30，wh2不在配置中
					AllowBackorder:    true,
					QuantityFrom:      consts.QuantityFromInventoryLevel,
				},
			},
		},
		{
			name: "库存项不存在",
			input: &CalculateAvailableQuantitiesInput{
				ProductsCenterProduct: &CalculateAvailableQuantitiesProductsCenterProductInput{
					Variants: []CalculateAvailableQuantitiesVariantInput{
						{
							ID:                      "var1",
							ExternalID:              "ext1",
							ExternalInventoryItemID: "inv_not_exist",
							AllowBackorder:          false,
						},
					},
				},
			},
			inventorySync: &models.InventorySync{
				ActiveWarehouses: []models.ActiveWarehouse{
					{
						SourceWarehouseId: "wh1",
						State:             "enabled",
					},
				},
			},
			connectorInventoryLevels: []connector_sdk_v2_inventory_level.ModelsResponseInventoryLevel2{
				{
					ExternalInventoryItemID: types.MakeString("inv1"),
					ExternalWarehouseID:     types.MakeString("wh1"),
					AvailableQuantity:       types.MakeInt(10),
				},
			},
			expected: map[string]quantityPair{
				"ext1": {
					AvailableQuantity: 0, // 不存在的库存项默认为0
					AllowBackorder:    false,
					QuantityFrom:      consts.QuantityFromInventoryLevel,
				},
			},
		},
		{
			name: "空库存级别列表",
			input: &CalculateAvailableQuantitiesInput{
				ProductsCenterProduct: &CalculateAvailableQuantitiesProductsCenterProductInput{
					Variants: []CalculateAvailableQuantitiesVariantInput{
						{
							ID:                      "var1",
							ExternalID:              "ext1",
							ExternalInventoryItemID: "inv1",
							AllowBackorder:          true,
						},
					},
				},
			},
			inventorySync: &models.InventorySync{
				ActiveWarehouses: []models.ActiveWarehouse{
					{
						SourceWarehouseId: "wh1",
						State:             "enabled",
					},
				},
			},
			connectorInventoryLevels: []connector_sdk_v2_inventory_level.ModelsResponseInventoryLevel2{},
			expected: map[string]quantityPair{
				"ext1": {
					AvailableQuantity: 0,
					AllowBackorder:    true,
					QuantityFrom:      consts.QuantityFromInventoryLevel,
				},
			},
		},
		{
			name: "多个变体共享同一个库存项",
			input: &CalculateAvailableQuantitiesInput{
				ProductsCenterProduct: &CalculateAvailableQuantitiesProductsCenterProductInput{
					Variants: []CalculateAvailableQuantitiesVariantInput{
						{
							ID:                      "var1",
							ExternalID:              "ext1",
							ExternalInventoryItemID: "inv1",
							AllowBackorder:          true,
						},
						{
							ID:                      "var2",
							ExternalID:              "ext2",
							ExternalInventoryItemID: "inv1", // 与var1共享同一库存项
							AllowBackorder:          false,
						},
					},
				},
			},
			inventorySync: &models.InventorySync{
				ActiveWarehouses: []models.ActiveWarehouse{
					{
						SourceWarehouseId: "wh1",
						State:             "enabled",
					},
				},
			},
			connectorInventoryLevels: []connector_sdk_v2_inventory_level.ModelsResponseInventoryLevel2{
				{
					ExternalInventoryItemID: types.MakeString("inv1"),
					ExternalWarehouseID:     types.MakeString("wh1"),
					AvailableQuantity:       types.MakeInt(50),
				},
			},
			expected: map[string]quantityPair{
				"ext1": {
					AvailableQuantity: 50,
					AllowBackorder:    true,
					QuantityFrom:      consts.QuantityFromInventoryLevel,
				},
				"ext2": {
					AvailableQuantity: 50,    // 与var1相同
					AllowBackorder:    false, // 但AllowBackorder不同
					QuantityFrom:      consts.QuantityFromInventoryLevel,
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			actual := tt.input.buildQuantityPairFromInventoryLevel(tt.inventorySync, tt.connectorInventoryLevels)

			if len(actual) != len(tt.expected) {
				t.Errorf("结果长度不匹配: got %d, want %d", len(actual), len(tt.expected))
			}

			for externalID, expectedPair := range tt.expected {
				actualPair, exists := actual[externalID]
				if !exists {
					t.Errorf("未找到外部ID为 %s 的变体", externalID)
					continue
				}

				if actualPair.AvailableQuantity != expectedPair.AvailableQuantity {
					t.Errorf("变体 %s 的 AvailableQuantity = %v, 期望 %v",
						externalID, actualPair.AvailableQuantity, expectedPair.AvailableQuantity)
				}

				if actualPair.AllowBackorder != expectedPair.AllowBackorder {
					t.Errorf("变体 %s 的 AllowBackorder = %v, 期望 %v",
						externalID, actualPair.AllowBackorder, expectedPair.AllowBackorder)
				}

				if actualPair.QuantityFrom != expectedPair.QuantityFrom {
					t.Errorf("变体 %s 的 QuantityFrom = %v, 期望 %v",
						externalID, actualPair.QuantityFrom, expectedPair.QuantityFrom)
				}
			}
		})
	}
}
