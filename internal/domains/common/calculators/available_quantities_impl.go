package calculators

import (
	"context"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/AfterShip/pltf-pd-product-listings/internal/consts"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/settings"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
	"github.com/AfterShip/pltf-pd-product-listings/internal/third_party/connectors"
	"github.com/AfterShip/pltf-pd-product-listings/internal/third_party/feed"
)

func (s *serviceImpl) CalculateAvailableQuantities(ctx context.Context, input *CalculateAvailableQuantitiesInput) (*CalculateAvailableQuantitiesOutput, error) {
	if err := s.validate.Struct(input); err != nil {
		return nil, errors.WithStack(err)
	}

	inventorySync, err := s.lookUpInventorySync(ctx, input)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	isCareVariantStatus := s.IsWhitelistCareVariantStatus(ctx, input)

	quantityPairs, err := s.buildQuantities(ctx, inventorySync, input, isCareVariantStatus)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	output := input.buildOutput(inventorySync, quantityPairs)
	if input.SalesChannel.Platform == consts.TikTokShop {
		input.wrapOutputWithTikTokSpecialCases(output, isCareVariantStatus)
	}

	return output, nil
}

// nolint:dupl
func (s *serviceImpl) lookUpInventorySync(ctx context.Context, input *CalculateAvailableQuantitiesInput) (*models.InventorySync, error) {
	if input.InventorySync != nil {
		return input.InventorySync, nil
	}

	// search from settings
	storeSettings, err := s.settingService.List(ctx, &settings.SearchSettingArgs{
		OrganizationID:       input.Organization.ID,
		SourceAppKey:         input.Source.App.Key,
		SourceAppPlatform:    input.Source.App.Platform,
		SalesChannelStoreKey: input.SalesChannel.StoreKey,
		SalesChannelPlatform: input.SalesChannel.Platform,
	})
	if err != nil {
		return nil, err
	}

	if len(storeSettings) == 0 {
		return nil, settings.ErrSettingInventorySyncNotFound
	}
	return &storeSettings[0].InventorySync, nil
}

func (s *serviceImpl) IsWhitelistCareVariantStatus(ctx context.Context, input *CalculateAvailableQuantitiesInput) bool {

	if input.Source.App.Platform != consts.SFCC {
		return false
	}

	supportFeatures, err := s.feedClient.SupportFeature.GetSupportFeatures(ctx, input.Organization.ID)
	if err != nil {
		s.logger.WarnCtx(ctx, "failed to get support features", zap.Error(err)) // ignore err
		return false
	}

	if !feed.RunInGrayModel(supportFeatures, feed.FeatureCodeVariantStatusInventory) {
		return false
	}

	return true
}

func (s *serviceImpl) buildQuantities(ctx context.Context, inventorySync *models.InventorySync,
	input *CalculateAvailableQuantitiesInput, isCareVariantStatus bool) (map[string]quantityPair, error) {

	var quantityParis map[string]quantityPair
	var err error

	// 没有多仓库，从 input 里直接获取
	if !inventorySync.EnabledMultiWarehouse() {
		quantityParis = input.buildVariantsQuantityPairs(isCareVariantStatus)
	} else {
		quantityParis, err = s.buildQuantityPairFromInventoryLevel(ctx, inventorySync, input)
		if err != nil {
			return nil, err
		}
	}
	return quantityParis, nil
}

func (s *serviceImpl) buildQuantityPairFromInventoryLevel(ctx context.Context,
	inventorySync *models.InventorySync, input *CalculateAvailableQuantitiesInput) (map[string]quantityPair, error) {
	externalInventoryItemIDs := input.gatherExternalInventoryItemIDs()

	inventoryLevels, err := s.connectorService.ListInventoryLevelsV2(ctx, &connectors.ListInventoryLevelsArgs{
		OrganizationID:           input.Organization.ID,
		AppPlatform:              input.Source.App.Platform,
		AppKey:                   input.Source.App.Key,
		ExternalInventoryItemIDs: externalInventoryItemIDs,
		Limit:                    consts.CNTInventoryLevelSearchLimit,
		Page:                     1,
	})
	if err != nil {
		return nil, err
	}
	return input.buildQuantityPairFromInventoryLevel(inventorySync, inventoryLevels), nil
}
