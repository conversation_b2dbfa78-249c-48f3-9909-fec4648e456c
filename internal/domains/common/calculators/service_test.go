package calculators

import (
	"context"
	"encoding/json"
	"os"
	"testing"

	"github.com/spf13/viper"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"

	"github.com/AfterShip/gopkg/cfg"
	"github.com/AfterShip/pltf-pd-product-listings/internal/config"
	"github.com/AfterShip/pltf-pd-product-listings/internal/datastore"
	"github.com/AfterShip/pltf-pd-product-listings/internal/logger"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
)

func loadConfig(t *testing.T) *config.Config {
	configs := new(config.Config)
	_, err := cfg.LoadViperConfig(configs, func(v *viper.Viper) { v.AddConfigPath("../../../../cmd/apiserver/conf") })
	require.NoError(t, err)
	configs.DynamicConfigs.ElasticsearchAuth = &config.ElasticsearchAuthConfig{
		Host: "http://localhost:9200",
	}
	require.NoError(t, datastore.Init(configs))
	return configs
}

func newService(t *testing.T) *Service {
	cfg := loadConfig(t)
	service := NewService(cfg, logger.Get(), datastore.Get().SpannerCli,
		datastore.Get().ClientStore.CNTClient, datastore.Get().ClientStore.FeedV2Client)
	require.NotNil(t, service)
	return &service
}

func Test_CalculatorsServiceImpl_NewService(t *testing.T) {
	service := newService(t)
	require.NotNil(t, service)
}

func generateData(file string, output interface{}) error {
	// 构建需要的数据
	bytes, err := os.ReadFile(file)
	if err != nil {
		return err
	}

	return json.Unmarshal(bytes, &output)
}

func TestMockCalculatorsService_CalculatePrices(t *testing.T) {
	mockSvc := new(MockCalculatorsService)

	input := new(CalculatePricesInput)
	output := new(CalculatePricesOutput)
	err := generateData("./testdata/calculate_price_input.json", input)
	require.NoError(t, err)
	err = generateData("./testdata/calculate_price_output.json", output)
	require.NoError(t, err)

	test := []struct {
		name        string
		input       *CalculatePricesInput
		mock        func()
		checkResult func(t *testing.T, output *CalculatePricesOutput, err error)
	}{
		{
			name:  "ok",
			input: input,
			mock: func() {
				mockSvc.On("CalculatePrices", mock.Anything, mock.Anything).Return(output, nil).Once()
			},
			checkResult: func(t *testing.T, output *CalculatePricesOutput, err error) {
				require.NoError(t, err)
				require.NotNil(t, output)
				require.Equal(t, output.ProductsCenterProduct.ID, "936f74f23f9b481da79411f7417f2f54")
			},
		},
		{
			name: "missing required body param",
			input: &CalculatePricesInput{
				Organization: models.Organization{
					ID: "936f74f23f9b481da79411f7417f2f54",
				},
			},
			mock: func() {
				mockSvc.On("CalculatePrices", mock.Anything, mock.Anything).Return(output, models.ErrMissRequiredBodyParam).Once()
			},
			checkResult: func(t *testing.T, output *CalculatePricesOutput, err error) {
				require.Error(t, err)
			},
		},
	}
	for _, tt := range test {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mock != nil {
				tt.mock()
			}
			result, err := mockSvc.CalculatePrices(context.Background(), tt.input)
			tt.checkResult(t, result, err)
		})
	}
}

func TestMockCalculatorsService_CalculateAvailableQuantites(t *testing.T) {
	mockSvc := new(MockCalculatorsService)

	input := new(CalculateAvailableQuantitiesInput)
	output := new(CalculateAvailableQuantitiesOutput)
	err := generateData("./testdata/calculate_quantity_input.json", input)
	require.NoError(t, err)
	err = generateData("./testdata/calculate_quantity_output.json", output)
	require.NoError(t, err)

	test := []struct {
		name        string
		input       *CalculateAvailableQuantitiesInput
		mock        func()
		checkResult func(t *testing.T, output *CalculateAvailableQuantitiesOutput, err error)
	}{
		{
			name:  "ok",
			input: input,
			mock: func() {
				mockSvc.On("CalculateAvailableQuantities", mock.Anything, mock.Anything).Return(output, nil).Once()
			},
			checkResult: func(t *testing.T, output *CalculateAvailableQuantitiesOutput, err error) {
				require.NoError(t, err)
				require.NotNil(t, output)
				require.Equal(t, output.ProductsCenterProduct.ID, "936f74f23f9b481da79411f7417f2f54")
			},
		},
		{
			name: "missing required body param",
			input: &CalculateAvailableQuantitiesInput{
				Organization: models.Organization{
					ID: "936f74f23f9b481da79411f7417f2f54",
				},
			},
			mock: func() {
				mockSvc.On("CalculateAvailableQuantities", mock.Anything, mock.Anything).Return(output, models.ErrMissRequiredBodyParam).Once()
			},
			checkResult: func(t *testing.T, output *CalculateAvailableQuantitiesOutput, err error) {
				require.Error(t, err)
			},
		},
	}
	for _, tt := range test {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mock != nil {
				tt.mock()
			}
			result, err := mockSvc.CalculateAvailableQuantities(context.Background(), tt.input)
			tt.checkResult(t, result, err)
		})
	}
}
