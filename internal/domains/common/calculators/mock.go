package calculators

import (
	"context"

	"github.com/stretchr/testify/mock"
)

type MockCalculatorsService struct {
	mock.Mock
}

func (m *MockCalculatorsService) CalculateAvailableQuantities(ctx context.Context,
	input *CalculateAvailableQuantitiesInput) (*CalculateAvailableQuantitiesOutput, error) {
	ret := m.Called(ctx, input)
	return ret.Get(0).(*CalculateAvailableQuantitiesOutput), ret.Error(1)
}

func (m *MockCalculatorsService) CalculatePrices(ctx context.Context,
	input *CalculatePricesInput) (*CalculatePricesOutput, error) {
	ret := m.Called(ctx, input)
	return ret.Get(0).(*CalculatePricesOutput), ret.Error(1)
}
