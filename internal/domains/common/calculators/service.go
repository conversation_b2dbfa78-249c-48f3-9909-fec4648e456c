package calculators

import (
	"context"

	"github.com/AfterShip/gopkg/log"
	"github.com/AfterShip/pltf-pd-product-listings/internal/third_party/connectors"
	"github.com/AfterShip/pltf-pd-product-listings/internal/third_party/feed"

	"github.com/go-playground/validator/v10"

	platform_api_v2 "github.com/AfterShip/connectors-sdk-go/v2"
	"github.com/AfterShip/gopkg/storage/spannerx"
	"github.com/AfterShip/pltf-pd-product-listings/internal/config"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/organization_settings"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/settings"
)

type Service interface {
	CalculateAvailableQuantities(ctx context.Context, input *CalculateAvailableQuantitiesInput) (*CalculateAvailableQuantitiesOutput, error)
	CalculatePrices(ctx context.Context, input *CalculatePricesInput) (*CalculatePricesOutput, error)
}

type serviceImpl struct {
	settingService             settings.Service
	organizationSettingService organization_settings.Service
	validate                   *validator.Validate
	connectorService           connectors.Service
	feedClient                 *feed.ClientV2
	config                     *config.Config
	logger                     *log.Logger
}

func NewService(config *config.Config, logger *log.Logger, cli *spannerx.Client,
	cntCli *platform_api_v2.PlatformV2Client, feedCli *feed.ClientV2,
) Service {
	return &serviceImpl{
		settingService:             settings.NewService(cli),
		organizationSettingService: organization_settings.NewService(cli),
		validate:                   validator.New(),
		connectorService:           connectors.NewService(logger, cntCli),
		feedClient:                 feedCli,
		config:                     config,
		logger:                     logger,
	}
}
