package calculators

import (
	"errors"
	"reflect"
	"testing"

	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/require"

	connector_sdk_v2_common "github.com/AfterShip/connectors-sdk-go/v2/common"
	connector_sdk_v2_product "github.com/AfterShip/connectors-sdk-go/v2/products"
	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/pltf-pd-product-listings/internal/config"
	"github.com/AfterShip/pltf-pd-product-listings/internal/consts"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
)

func Test_ConvertWithCurrency(t *testing.T) {
	tests := []struct {
		name          string
		price         decimal.Decimal
		convertor     *models.CurrencyConvertor
		expectedPrice float64
	}{
		{
			name:          "convertor nil",
			price:         decimal.NewFromFloat(10.0),
			convertor:     nil,
			expectedPrice: 10.0,
		},
		{
			name:  "convertor not nil",
			price: decimal.NewFromFloat(10.0),
			convertor: &models.CurrencyConvertor{
				CustomExchangeRate: 2.234,
			},
			expectedPrice: 10.0 * 2.234,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotDecimal := convertWithCurrency(tt.price, tt.convertor)
			got, _ := gotDecimal.Float64()
			require.Equal(t, tt.expectedPrice, got)
		})
	}
}

// nolint:maintidx
func Test_GeneratePricePairs(t *testing.T) {
	tests := []struct {
		name                 string
		input                *CalculatePricesInput
		connectorProduct     *connector_sdk_v2_product.ModelsResponseProduct
		currencyConvertors   *models.CurrencyConvertor
		expected             map[string]pricePair
		salesChannelCurrency string
	}{
		{
			name:                 "currency is the same",
			currencyConvertors:   &models.CurrencyConvertor{},
			connectorProduct:     &connector_sdk_v2_product.ModelsResponseProduct{},
			salesChannelCurrency: "HKD",
			input: &CalculatePricesInput{
				SalesChannel: models.SalesChannel{
					Platform:      consts.TikTokShop,
					StoreKey:      "11",
					CountryRegion: "HK",
				},
				SalesChannelCurrency: "HKD",
				ProductsCenterProduct: &CalculatePriceProductsCenterProductInput{
					Variants: []CalculatePricesVariantInput{
						{
							ExternalID: "111",
							Price: models.Price{
								Amount:   decimal.NewFromFloat(10.0),
								Currency: "HKD",
							},
							CompareAtPrice: models.Price{
								Amount:   decimal.NewFromFloat(20.0),
								Currency: "HKD",
							},
						},
					},
				},
			},
			expected: map[string]pricePair{
				"111": pricePair{
					SalePrice:      decimal.NewFromFloat(10.0),
					CompareAtPrice: decimal.NewFromFloat(20.0),
					priceFrom:      consts.PriceFromInputVariant,
				},
			},
		},
		{
			name:                 "currency is the same,compare_at_price is 0",
			currencyConvertors:   &models.CurrencyConvertor{},
			connectorProduct:     &connector_sdk_v2_product.ModelsResponseProduct{},
			salesChannelCurrency: "HKD",
			input: &CalculatePricesInput{
				SalesChannel: models.SalesChannel{
					Platform:      consts.TikTokShop,
					StoreKey:      "11",
					CountryRegion: "HK",
				},
				SalesChannelCurrency: "HKD",
				ProductsCenterProduct: &CalculatePriceProductsCenterProductInput{
					Variants: []CalculatePricesVariantInput{
						{
							ExternalID: "111",
							Price: models.Price{
								Amount:   decimal.NewFromFloat(10.0),
								Currency: "HKD",
							},
							CompareAtPrice: models.Price{
								Amount:   decimal.NewFromFloat(0),
								Currency: "HKD",
							},
						},
					},
				},
			},
			expected: map[string]pricePair{
				"111": pricePair{
					SalePrice:      decimal.NewFromFloat(10.0),
					CompareAtPrice: decimal.NewFromFloat(10.0),
					priceFrom:      consts.PriceFromInputVariant,
				},
			},
		},
		{
			name:                 "currency not the same,use PresentmentPrices",
			currencyConvertors:   &models.CurrencyConvertor{},
			salesChannelCurrency: "USD",
			connectorProduct: &connector_sdk_v2_product.ModelsResponseProduct{
				Variants: []connector_sdk_v2_product.ModelsResponseProductVariant{
					{
						ExternalID: types.MakeString("111"),
						PresentmentPrices: []connector_sdk_v2_common.ModelsPrecentmentPrice{
							{
								Price: &connector_sdk_v2_common.ModelsMoney{
									Amount:   types.MakeFloat64(22.0),
									Currency: types.MakeString("USD"),
								},
								CompareAtPrice: &connector_sdk_v2_common.ModelsMoney{
									Amount:   types.MakeFloat64(46.0),
									Currency: types.MakeString("USD"),
								},
							},
						},
					},
				},
			},
			input: &CalculatePricesInput{
				SalesChannel: models.SalesChannel{
					Platform:      consts.TikTokShop,
					StoreKey:      "11",
					CountryRegion: "US",
				},
				SalesChannelCurrency: "USD",
				ProductsCenterProduct: &CalculatePriceProductsCenterProductInput{
					Variants: []CalculatePricesVariantInput{
						{
							ExternalID: "111",
							Price: models.Price{
								Amount:   decimal.NewFromFloat(10.0),
								Currency: "HKD",
							},
							CompareAtPrice: models.Price{
								Amount:   decimal.NewFromFloat(20),
								Currency: "HKD",
							},
						},
					},
				},
			},
			expected: map[string]pricePair{
				"111": pricePair{
					SalePrice:      decimal.NewFromFloat(22.0),
					CompareAtPrice: decimal.NewFromFloat(46.0),
					priceFrom:      consts.PriceFromConnectorsVariantPresentmentPrice,
				},
			},
		},
		{
			name: "currency not the same,PresentmentPrices not set",
			currencyConvertors: &models.CurrencyConvertor{
				CustomExchangeRate:   2.0,
				SalesChannelCurrency: "USD",
				SourceCurrency:       "HKD",
			},
			salesChannelCurrency: "USD",
			connectorProduct: &connector_sdk_v2_product.ModelsResponseProduct{
				Variants: []connector_sdk_v2_product.ModelsResponseProductVariant{
					{
						PresentmentPrices: []connector_sdk_v2_common.ModelsPrecentmentPrice{
							{
								Price: &connector_sdk_v2_common.ModelsMoney{
									Amount:   types.MakeFloat64(22.0),
									Currency: types.MakeString("GBP"),
								},
								CompareAtPrice: &connector_sdk_v2_common.ModelsMoney{
									Amount:   types.MakeFloat64(46.0),
									Currency: types.MakeString("GBP"),
								},
							},
						},
					},
				},
			},
			input: &CalculatePricesInput{
				SalesChannel: models.SalesChannel{
					Platform:      consts.TikTokShop,
					StoreKey:      "11",
					CountryRegion: "US",
				},
				SalesChannelCurrency: "USD",
				ProductsCenterProduct: &CalculatePriceProductsCenterProductInput{
					Variants: []CalculatePricesVariantInput{
						{
							ExternalID: "111",
							Price: models.Price{
								Amount:   decimal.NewFromFloat(10.0),
								Currency: "HKD",
							},
							CompareAtPrice: models.Price{
								Amount:   decimal.NewFromFloat(20),
								Currency: "HKD",
							},
						},
					},
				},
			},
			expected: map[string]pricePair{
				"111": pricePair{
					SalePrice:      decimal.NewFromFloat(20.0),
					CompareAtPrice: decimal.NewFromFloat(40.0),
					priceFrom:      consts.PriceFromCurrencyConvertor,
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			actual := tt.input.generatePricePairs(tt.currencyConvertors, tt.connectorProduct)
			require.Equal(t, tt.expected, actual)
		})
	}
}

func Test_validateTikTokPriceRange(t *testing.T) {
	tests := []struct {
		name         string
		price        float64
		regionConfig *config.ChannelRegionConfig
		wantErr      bool
	}{
		{
			name:  "not check",
			price: 12.2,
			regionConfig: &config.ChannelRegionConfig{
				CheckPriceRange: false,
				LocalToLocal: &config.ChannelPriceRange{
					MinPrice: 1.1,
					MaxPrice: 3.4,
				},
			},
			wantErr: false,
		},
		{
			name:  "out of range",
			price: 12.2,
			regionConfig: &config.ChannelRegionConfig{
				CheckPriceRange: true,
				LocalToLocal: &config.ChannelPriceRange{
					MinPrice: 1.1,
					MaxPrice: 3.4,
				},
			},
			wantErr: true,
		},
		{
			name:  "ok",
			price: 12.2,
			regionConfig: &config.ChannelRegionConfig{
				CheckPriceRange: true,
				LocalToLocal: &config.ChannelPriceRange{
					MinPrice: 1.1,
					MaxPrice: 14.5,
				},
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := tt.regionConfig.ValidatePriceRange(tt.price)
			if tt.wantErr {
				require.Error(t, got)
			} else {
				require.Nil(t, got)
			}
		})
	}
}

func TestCalculatePricesOutput_WrapOutputWithTikTokSpecialCases(t *testing.T) {
	type inputStruct struct {
		out          *CalculatePricesOutput
		salesChannel models.SalesChannel
		cfg          config.ChannelRegionConfig
	}
	tests := []struct {
		name      string
		input     inputStruct
		resultOut *CalculatePriceProductsCenterProductOutput
	}{
		{
			name: "JPY",
			input: inputStruct{
				out: &CalculatePricesOutput{
					ProductsCenterProduct: &CalculatePriceProductsCenterProductOutput{
						Variants: []CalculatePricesVariantOutput{
							{
								CalculatedPrice: decimal.NewFromFloat(123.556),
							},
							{
								CalculatedPrice: decimal.NewFromFloat(2001.32),
							},
							{
								CalculatedPrice: decimal.NewFromFloat(0.4),
							},
							{
								CalculatedPrice: decimal.NewFromFloat(0.5),
							},
						},
					},
				},
				salesChannel: models.SalesChannel{
					Platform:      consts.TikTokShop,
					CountryRegion: consts.RegionJP,
				},
				cfg: config.ChannelRegionConfig{
					Region:         consts.RegionJP,
					Currency:       "JPY",
					HasCrossBorder: false,
					LocalToLocal: &config.ChannelPriceRange{
						MinPrice: 1,
						MaxPrice: 500000,
					},
					CheckPriceRange: true,
				},
			},
			resultOut: &CalculatePriceProductsCenterProductOutput{
				Variants: []CalculatePricesVariantOutput{
					{
						CalculatedPrice: decimal.NewFromFloat(124),
					},
					{
						CalculatedPrice: decimal.NewFromFloat(2001),
					},
					{
						CalculatedPrice: decimal.NewFromFloat(0),
					},
					{
						CalculatedPrice: decimal.NewFromFloat(1),
					},
				},
			},
		},
		{
			name: "USD",
			input: inputStruct{
				out: &CalculatePricesOutput{
					ProductsCenterProduct: &CalculatePriceProductsCenterProductOutput{
						Variants: []CalculatePricesVariantOutput{
							{
								CalculatedPrice: decimal.NewFromFloat(123.556),
							},
							{
								CalculatedPrice: decimal.NewFromFloat(2001.32),
							},
							{
								CalculatedPrice: decimal.NewFromFloat(0.4),
							},
							{
								CalculatedPrice: decimal.NewFromFloat(0.5),
							},
						},
					},
				},
				salesChannel: models.SalesChannel{
					Platform:      consts.TikTokShop,
					CountryRegion: consts.RegionUS,
				},
				cfg: config.ChannelRegionConfig{
					Region:         consts.RegionUS,
					Currency:       "USD",
					HasCrossBorder: false,
					LocalToLocal: &config.ChannelPriceRange{
						MinPrice: 1,
						MaxPrice: 7600,
					},
					CheckPriceRange: true,
				},
			},
			resultOut: &CalculatePriceProductsCenterProductOutput{
				Variants: []CalculatePricesVariantOutput{
					{
						CalculatedPrice: decimal.NewFromFloat(123.56),
					},
					{
						CalculatedPrice: decimal.NewFromFloat(2001.32),
					},
					{
						CalculatedPrice: decimal.NewFromFloat(0.4),
					},
					{
						CalculatedPrice: decimal.NewFromFloat(0.5),
					},
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.input.out.wrapOutputWithTikTokSpecialCases(tt.input.salesChannel, &tt.input.cfg)
			// 数组长度相等
			require.Equal(t, len(tt.resultOut.Variants), len(tt.input.out.ProductsCenterProduct.Variants))
			for index := range tt.resultOut.Variants {
				if !tt.resultOut.Variants[index].CalculatedPrice.Equal(tt.input.out.ProductsCenterProduct.Variants[index].CalculatedPrice) {
					t.Errorf("wrapOutputWithTikTokSpecialCases() got = %v, want %v", tt.input.out.ProductsCenterProduct.Variants[index].CalculatedPrice, tt.resultOut.Variants[index].CalculatedPrice)
				}
			}
		})
	}
}

func TestCalculatePricesOutput_GetPrice(t *testing.T) {
	type fields struct {
		PriceSync             *models.PriceSync
		CurrencyConvertor     *models.CurrencyConvertor
		ProductsCenterProduct *CalculatePriceProductsCenterProductOutput
	}
	type args struct {
		productsCenterVariantID string
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want1  models.ProductVariantPrice
		want2  models.ProductVariantPrice
		want3  error
	}{
		{
			name: "Test Case 1: Variant exists",
			fields: fields{
				ProductsCenterProduct: &CalculatePriceProductsCenterProductOutput{
					Variants: []CalculatePricesVariantOutput{
						{
							ID:                        "variant1",
							Status:                    consts.CalculateSuccess,
							CalculatedPrice:           decimal.NewFromFloat(100.0),
							CalculatedComparedAtPrice: decimal.NewFromFloat(200.0),
							Currency:                  "USD",
						},
					},
				},
			},
			args: args{
				productsCenterVariantID: "variant1",
			},
			want1: models.ProductVariantPrice{Currency: "USD", Amount: "100"},
			want2: models.ProductVariantPrice{Currency: "USD", Amount: "200"},
			want3: nil,
		},
		{
			name: "Test Case 2: Variant does not exist",
			fields: fields{
				ProductsCenterProduct: &CalculatePriceProductsCenterProductOutput{
					Variants: []CalculatePricesVariantOutput{
						{
							ID:                        "variant1",
							Status:                    consts.CalculateSuccess,
							CalculatedPrice:           decimal.NewFromFloat(100),
							CalculatedComparedAtPrice: decimal.NewFromFloat(200),
						},
					},
				},
			},
			args: args{
				productsCenterVariantID: "variant2",
			},
			want1: models.ProductVariantPrice{},
			want2: models.ProductVariantPrice{},
			want3: ErrPriceNotFound,
		},
		{
			name: "Test Case 3: Calculation failed, ignore error",
			fields: fields{
				ProductsCenterProduct: &CalculatePriceProductsCenterProductOutput{
					Variants: []CalculatePricesVariantOutput{
						{
							ID:                        "variant1",
							Status:                    consts.CalculateFailed,
							CalculatedPrice:           decimal.NewFromFloat(100),
							CalculatedComparedAtPrice: decimal.NewFromFloat(200),
							Currency:                  "USD",
						},
					},
				},
			},
			args: args{
				productsCenterVariantID: "variant1",
			},
			want1: models.ProductVariantPrice{Currency: "USD", Amount: "100"},
			want2: models.ProductVariantPrice{Currency: "USD", Amount: "200"},
			want3: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &CalculatePricesOutput{
				PriceSync:             tt.fields.PriceSync,
				CurrencyConvertor:     tt.fields.CurrencyConvertor,
				ProductsCenterProduct: tt.fields.ProductsCenterProduct,
			}
			price, comparePrice, err := c.GetPrice(tt.args.productsCenterVariantID)
			if !reflect.DeepEqual(price, tt.want1) {
				t.Errorf("GetPrice() got = %v, want %v", price, tt.want1)
			}
			if !reflect.DeepEqual(comparePrice, tt.want2) {
				t.Errorf("GetPrice() got = %v, want %v", comparePrice, tt.want2)
			}
			if !errors.Is(err, tt.want3) {
				t.Errorf("GetPrice() got1 = %v, want %v", err, tt.want3)
			}
		})
	}
}

func TestCalculatePricesInput_customValidator(t *testing.T) {
	tests := []struct {
		name    string
		input   *CalculatePricesInput
		wantErr bool
	}{
		{
			name: "有效的完整输入",
			input: &CalculatePricesInput{
				Organization: models.Organization{
					ID: "org123",
				},
				Source: models.Source{
					App: models.App{
						Platform: "shopify",
						Key:      "app123",
					},
				},
				SalesChannel: models.SalesChannel{
					Platform:      consts.TikTokShop,
					StoreKey:      "store456",
					CountryRegion: "US",
				},
			},
			wantErr: false,
		},
		{
			name: "缺少组织ID",
			input: &CalculatePricesInput{
				Organization: models.Organization{
					ID: "",
				},
				Source: models.Source{
					App: models.App{
						Platform: "shopify",
						Key:      "app123",
					},
				},
				SalesChannel: models.SalesChannel{
					Platform:      consts.TikTokShop,
					StoreKey:      "store456",
					CountryRegion: "US",
				},
			},
			wantErr: true,
		},
		{
			name: "缺少源平台信息",
			input: &CalculatePricesInput{
				Organization: models.Organization{
					ID: "org123",
				},
				Source: models.Source{
					App: models.App{
						Platform: "",
						Key:      "app123",
					},
				},
				SalesChannel: models.SalesChannel{
					Platform:      consts.TikTokShop,
					StoreKey:      "store456",
					CountryRegion: "US",
				},
			},
			wantErr: true,
		},
		{
			name: "缺少源应用Key",
			input: &CalculatePricesInput{
				Organization: models.Organization{
					ID: "org123",
				},
				Source: models.Source{
					App: models.App{
						Platform: "shopify",
						Key:      "",
					},
				},
				SalesChannel: models.SalesChannel{
					Platform:      consts.TikTokShop,
					StoreKey:      "store456",
					CountryRegion: "US",
				},
			},
			wantErr: true,
		},
		{
			name: "缺少销售渠道StoreKey",
			input: &CalculatePricesInput{
				Organization: models.Organization{
					ID: "org123",
				},
				Source: models.Source{
					App: models.App{
						Platform: "shopify",
						Key:      "app123",
					},
				},
				SalesChannel: models.SalesChannel{
					Platform:      consts.TikTokShop,
					StoreKey:      "",
					CountryRegion: "US",
				},
			},
			wantErr: true,
		},
		{
			name: "缺少销售渠道平台",
			input: &CalculatePricesInput{
				Organization: models.Organization{
					ID: "org123",
				},
				Source: models.Source{
					App: models.App{
						Platform: "shopify",
						Key:      "app123",
					},
				},
				SalesChannel: models.SalesChannel{
					Platform:      "",
					StoreKey:      "store456",
					CountryRegion: "US",
				},
			},
			wantErr: true,
		},
		{
			name: "缺少销售渠道国家/地区",
			input: &CalculatePricesInput{
				Organization: models.Organization{
					ID: "org123",
				},
				Source: models.Source{
					App: models.App{
						Platform: "shopify",
						Key:      "app123",
					},
				},
				SalesChannel: models.SalesChannel{
					Platform:      consts.TikTokShop,
					StoreKey:      "store456",
					CountryRegion: "",
				},
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.input.customValidator()
			if (err != nil) != tt.wantErr {
				t.Errorf("customValidator() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.wantErr && !errors.Is(err, models.ErrMissRequiredBodyParam) {
				t.Errorf("customValidator() expected error = %v, got = %v", models.ErrMissRequiredBodyParam, err)
			}
		})
	}
}

func TestCalculatePricesInput_lookUpProductsCenterProductCurrency(t *testing.T) {
	tests := []struct {
		name  string
		input *CalculatePricesInput
		want  string
	}{
		{
			name: "正常情况 - 有效变体和价格",
			input: &CalculatePricesInput{
				ProductsCenterProduct: &CalculatePriceProductsCenterProductInput{
					Variants: []CalculatePricesVariantInput{
						{
							ID:         "var1",
							ExternalID: "ext1",
							Price: models.Price{
								Currency: "USD",
								Amount:   decimal.NewFromFloat(10.99),
							},
							CompareAtPrice: models.Price{
								Currency: "USD",
								Amount:   decimal.NewFromFloat(15.99),
							},
						},
					},
				},
			},
			want: "USD",
		},
		{
			name: "多个变体 - 返回第一个变体的货币",
			input: &CalculatePricesInput{
				ProductsCenterProduct: &CalculatePriceProductsCenterProductInput{
					Variants: []CalculatePricesVariantInput{
						{
							ID:         "var1",
							ExternalID: "ext1",
							Price: models.Price{
								Currency: "USD",
								Amount:   decimal.NewFromFloat(10.99),
							},
							CompareAtPrice: models.Price{
								Currency: "USD",
								Amount:   decimal.NewFromFloat(15.99),
							},
						},
						{
							ID:         "var2",
							ExternalID: "ext2",
							Price: models.Price{
								Currency: "EUR",
								Amount:   decimal.NewFromFloat(9.99),
							},
							CompareAtPrice: models.Price{
								Currency: "EUR",
								Amount:   decimal.NewFromFloat(14.99),
							},
						},
					},
				},
			},
			want: "USD",
		},
		{
			name: "没有变体",
			input: &CalculatePricesInput{
				ProductsCenterProduct: &CalculatePriceProductsCenterProductInput{
					Variants: []CalculatePricesVariantInput{},
				},
			},
			want: "",
		},
		{
			name: "ProductsCenterProduct 为 nil",
			input: &CalculatePricesInput{
				ProductsCenterProduct: nil,
			},
			want: "",
		},
		{
			name:  "整个输入对象为 nil",
			input: nil,
			want:  "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := tt.input.lookUpProductsCenterProductCurrency()
			if got != tt.want {
				t.Errorf("lookUpProductsCenterProductCurrency() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_lookUpVariantPricePairOnMultiCurrencies(t *testing.T) {
	tests := []struct {
		name                   string
		salesChannelCurrency   string
		externalVariantID      string
		product                *connector_sdk_v2_product.ModelsResponseProduct
		wantSalePrice          float64
		wantCompareAtPrice     float64
		wantHasSetUpMultiPrice bool
	}{
		{
			name:                 "找到匹配的变体和货币",
			salesChannelCurrency: "USD",
			externalVariantID:    "var1",
			product: &connector_sdk_v2_product.ModelsResponseProduct{
				Variants: []connector_sdk_v2_product.ModelsResponseProductVariant{
					{
						ExternalID: types.MakeString("var1"),
						PresentmentPrices: []connector_sdk_v2_common.ModelsPrecentmentPrice{
							{
								Price: &connector_sdk_v2_common.ModelsMoney{
									Currency: types.MakeString("USD"),
									Amount:   types.MakeFloat64(19.99),
								},
								CompareAtPrice: &connector_sdk_v2_common.ModelsMoney{
									Currency: types.MakeString("USD"),
									Amount:   types.MakeFloat64(29.99),
								},
							},
						},
					},
				},
			},
			wantSalePrice:          19.99,
			wantCompareAtPrice:     29.99,
			wantHasSetUpMultiPrice: true,
		},
		{
			name:                 "找到匹配的变体但货币不匹配",
			salesChannelCurrency: "EUR",
			externalVariantID:    "var1",
			product: &connector_sdk_v2_product.ModelsResponseProduct{
				Variants: []connector_sdk_v2_product.ModelsResponseProductVariant{
					{
						ExternalID: types.MakeString("var1"),
						PresentmentPrices: []connector_sdk_v2_common.ModelsPrecentmentPrice{
							{
								Price: &connector_sdk_v2_common.ModelsMoney{
									Currency: types.MakeString("USD"),
									Amount:   types.MakeFloat64(19.99),
								},
								CompareAtPrice: &connector_sdk_v2_common.ModelsMoney{
									Currency: types.MakeString("USD"),
									Amount:   types.MakeFloat64(29.99),
								},
							},
						},
					},
				},
			},
			wantSalePrice:          0,
			wantCompareAtPrice:     0,
			wantHasSetUpMultiPrice: false,
		},
		{
			name:                 "变体ID不匹配",
			salesChannelCurrency: "USD",
			externalVariantID:    "var2",
			product: &connector_sdk_v2_product.ModelsResponseProduct{
				Variants: []connector_sdk_v2_product.ModelsResponseProductVariant{
					{
						ExternalID: types.MakeString("var1"),
						PresentmentPrices: []connector_sdk_v2_common.ModelsPrecentmentPrice{
							{
								Price: &connector_sdk_v2_common.ModelsMoney{
									Currency: types.MakeString("USD"),
									Amount:   types.MakeFloat64(19.99),
								},
								CompareAtPrice: &connector_sdk_v2_common.ModelsMoney{
									Currency: types.MakeString("USD"),
									Amount:   types.MakeFloat64(29.99),
								},
							},
						},
					},
				},
			},
			wantSalePrice:          0,
			wantCompareAtPrice:     0,
			wantHasSetUpMultiPrice: false,
		},
		{
			name:                 "多种货币中找到匹配",
			salesChannelCurrency: "EUR",
			externalVariantID:    "var1",
			product: &connector_sdk_v2_product.ModelsResponseProduct{
				Variants: []connector_sdk_v2_product.ModelsResponseProductVariant{
					{
						ExternalID: types.MakeString("var1"),
						PresentmentPrices: []connector_sdk_v2_common.ModelsPrecentmentPrice{
							{
								Price: &connector_sdk_v2_common.ModelsMoney{
									Currency: types.MakeString("USD"),
									Amount:   types.MakeFloat64(19.99),
								},
								CompareAtPrice: &connector_sdk_v2_common.ModelsMoney{
									Currency: types.MakeString("USD"),
									Amount:   types.MakeFloat64(29.99),
								},
							},
							{
								Price: &connector_sdk_v2_common.ModelsMoney{
									Currency: types.MakeString("EUR"),
									Amount:   types.MakeFloat64(18.50),
								},
								CompareAtPrice: &connector_sdk_v2_common.ModelsMoney{
									Currency: types.MakeString("EUR"),
									Amount:   types.MakeFloat64(27.75),
								},
							},
						},
					},
				},
			},
			wantSalePrice:          18.50,
			wantCompareAtPrice:     27.75,
			wantHasSetUpMultiPrice: true,
		},
		{
			name:                 "只有销售价格匹配货币",
			salesChannelCurrency: "EUR",
			externalVariantID:    "var1",
			product: &connector_sdk_v2_product.ModelsResponseProduct{
				Variants: []connector_sdk_v2_product.ModelsResponseProductVariant{
					{
						ExternalID: types.MakeString("var1"),
						PresentmentPrices: []connector_sdk_v2_common.ModelsPrecentmentPrice{
							{
								Price: &connector_sdk_v2_common.ModelsMoney{
									Currency: types.MakeString("EUR"),
									Amount:   types.MakeFloat64(18.50),
								},
								CompareAtPrice: &connector_sdk_v2_common.ModelsMoney{
									Currency: types.MakeString("USD"),
									Amount:   types.MakeFloat64(29.99),
								},
							},
						},
					},
				},
			},
			wantSalePrice:          18.50,
			wantCompareAtPrice:     0,
			wantHasSetUpMultiPrice: true,
		},
		{
			name:                 "只有比较价格匹配货币",
			salesChannelCurrency: "EUR",
			externalVariantID:    "var1",
			product: &connector_sdk_v2_product.ModelsResponseProduct{
				Variants: []connector_sdk_v2_product.ModelsResponseProductVariant{
					{
						ExternalID: types.MakeString("var1"),
						PresentmentPrices: []connector_sdk_v2_common.ModelsPrecentmentPrice{
							{
								Price: &connector_sdk_v2_common.ModelsMoney{
									Currency: types.MakeString("USD"),
									Amount:   types.MakeFloat64(19.99),
								},
								CompareAtPrice: &connector_sdk_v2_common.ModelsMoney{
									Currency: types.MakeString("EUR"),
									Amount:   types.MakeFloat64(27.75),
								},
							},
						},
					},
				},
			},
			wantSalePrice:          0,
			wantCompareAtPrice:     27.75,
			wantHasSetUpMultiPrice: true,
		},
		{
			name:                 "空的PresentmentPrices列表",
			salesChannelCurrency: "USD",
			externalVariantID:    "var1",
			product: &connector_sdk_v2_product.ModelsResponseProduct{
				Variants: []connector_sdk_v2_product.ModelsResponseProductVariant{
					{
						ExternalID:        types.MakeString("var1"),
						PresentmentPrices: []connector_sdk_v2_common.ModelsPrecentmentPrice{},
					},
				},
			},
			wantSalePrice:          0,
			wantCompareAtPrice:     0,
			wantHasSetUpMultiPrice: false,
		},
		{
			name:                 "Price和CompareAtPrice是nil",
			salesChannelCurrency: "USD",
			externalVariantID:    "var1",
			product: &connector_sdk_v2_product.ModelsResponseProduct{
				Variants: []connector_sdk_v2_product.ModelsResponseProductVariant{
					{
						ExternalID: types.MakeString("var1"),
						PresentmentPrices: []connector_sdk_v2_common.ModelsPrecentmentPrice{
							{
								Price:          nil,
								CompareAtPrice: nil,
							},
						},
					},
				},
			},
			wantSalePrice:          0,
			wantCompareAtPrice:     0,
			wantHasSetUpMultiPrice: false,
		},
		{
			name:                 "多个变体中找到正确匹配",
			salesChannelCurrency: "USD",
			externalVariantID:    "var2",
			product: &connector_sdk_v2_product.ModelsResponseProduct{
				Variants: []connector_sdk_v2_product.ModelsResponseProductVariant{
					{
						ExternalID: types.MakeString("var1"),
						PresentmentPrices: []connector_sdk_v2_common.ModelsPrecentmentPrice{
							{
								Price: &connector_sdk_v2_common.ModelsMoney{
									Currency: types.MakeString("USD"),
									Amount:   types.MakeFloat64(19.99),
								},
								CompareAtPrice: &connector_sdk_v2_common.ModelsMoney{
									Currency: types.MakeString("USD"),
									Amount:   types.MakeFloat64(29.99),
								},
							},
						},
					},
					{
						ExternalID: types.MakeString("var2"),
						PresentmentPrices: []connector_sdk_v2_common.ModelsPrecentmentPrice{
							{
								Price: &connector_sdk_v2_common.ModelsMoney{
									Currency: types.MakeString("USD"),
									Amount:   types.MakeFloat64(24.99),
								},
								CompareAtPrice: &connector_sdk_v2_common.ModelsMoney{
									Currency: types.MakeString("USD"),
									Amount:   types.MakeFloat64(34.99),
								},
							},
						},
					},
				},
			},
			wantSalePrice:          24.99,
			wantCompareAtPrice:     34.99,
			wantHasSetUpMultiPrice: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotSalePrice, gotCompareAtPrice, gotHasSetUpMultiPrice := lookUpVariantPricePairOnMultiCurrencies(
				tt.salesChannelCurrency, tt.externalVariantID, tt.product)

			if gotSalePrice != tt.wantSalePrice {
				t.Errorf("lookUpVariantPricePairOnMultiCurrencies() gotSalePrice = %v, want %v",
					gotSalePrice, tt.wantSalePrice)
			}
			if gotCompareAtPrice != tt.wantCompareAtPrice {
				t.Errorf("lookUpVariantPricePairOnMultiCurrencies() gotCompareAtPrice = %v, want %v",
					gotCompareAtPrice, tt.wantCompareAtPrice)
			}
			if gotHasSetUpMultiPrice != tt.wantHasSetUpMultiPrice {
				t.Errorf("lookUpVariantPricePairOnMultiCurrencies() gotHasSetUpMultiPrice = %v, want %v",
					gotHasSetUpMultiPrice, tt.wantHasSetUpMultiPrice)
			}
		})
	}
}

func TestCalculatePricesOutput_WrapOutputWithSheinSpecialCases(t *testing.T) {
	tests := []struct {
		name                 string
		salesChannelPlatform string
		cfg                  *config.ChannelRegionConfig
		input                *CalculatePricesOutput
		expectedVariants     []CalculatePricesVariantOutput
	}{
		{
			name:                 "Shein平台-四舍五入到两位小数",
			salesChannelPlatform: consts.Shein,
			cfg:                  &config.ChannelRegionConfig{},
			input: &CalculatePricesOutput{
				ProductsCenterProduct: &CalculatePriceProductsCenterProductOutput{
					Variants: []CalculatePricesVariantOutput{
						{
							ID:                        "var1",
							ExternalID:                "ext1",
							Status:                    consts.CalculateSuccess,
							CalculatedPrice:           decimal.NewFromFloat(123.567),
							CalculatedComparedAtPrice: decimal.NewFromFloat(150.564),
						},
						{
							ID:                        "var2",
							ExternalID:                "ext2",
							Status:                    consts.CalculateSuccess,
							CalculatedPrice:           decimal.NewFromFloat(99.995),
							CalculatedComparedAtPrice: decimal.NewFromFloat(129.994),
						},
						{
							ID:                        "var3",
							ExternalID:                "ext3",
							Status:                    consts.CalculateFailed,
							CalculatedPrice:           decimal.NewFromFloat(88.888),
							CalculatedComparedAtPrice: decimal.NewFromFloat(99.999),
							ErrorMessage:              "价格计算失败",
						},
					},
				},
			},
			expectedVariants: []CalculatePricesVariantOutput{
				{
					ID:                        "var1",
					ExternalID:                "ext1",
					Status:                    consts.CalculateSuccess,
					CalculatedPrice:           decimal.NewFromFloat(123.57),
					CalculatedComparedAtPrice: decimal.NewFromFloat(150.56),
				},
				{
					ID:                        "var2",
					ExternalID:                "ext2",
					Status:                    consts.CalculateSuccess,
					CalculatedPrice:           decimal.NewFromFloat(100.00),
					CalculatedComparedAtPrice: decimal.NewFromFloat(129.99),
				},
				{
					ID:                        "var3",
					ExternalID:                "ext3",
					Status:                    consts.CalculateFailed,
					CalculatedPrice:           decimal.NewFromFloat(88.888),
					CalculatedComparedAtPrice: decimal.NewFromFloat(99.999),
					ErrorMessage:              "价格计算失败",
				},
			},
		},
		{
			name:                 "非Shein平台-不进行处理",
			salesChannelPlatform: consts.TikTokShop,
			cfg:                  &config.ChannelRegionConfig{},
			input: &CalculatePricesOutput{
				ProductsCenterProduct: &CalculatePriceProductsCenterProductOutput{
					Variants: []CalculatePricesVariantOutput{
						{
							ID:                        "var1",
							ExternalID:                "ext1",
							Status:                    consts.CalculateSuccess,
							CalculatedPrice:           decimal.NewFromFloat(123.567),
							CalculatedComparedAtPrice: decimal.NewFromFloat(150.564),
						},
					},
				},
			},
			expectedVariants: []CalculatePricesVariantOutput{
				{
					ID:                        "var1",
					ExternalID:                "ext1",
					Status:                    consts.CalculateSuccess,
					CalculatedPrice:           decimal.NewFromFloat(123.567),
					CalculatedComparedAtPrice: decimal.NewFromFloat(150.564),
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.input.wrapOutputWithSheinSpecialCases(tt.salesChannelPlatform, tt.cfg)

			require.Equal(t, len(tt.expectedVariants), len(tt.input.ProductsCenterProduct.Variants))

			for i, expectedVariant := range tt.expectedVariants {
				actualVariant := tt.input.ProductsCenterProduct.Variants[i]

				if !actualVariant.CalculatedPrice.Equal(expectedVariant.CalculatedPrice) {
					t.Errorf("变体 %s 的 CalculatedPrice = %s, 期望 %s",
						actualVariant.ID, actualVariant.CalculatedPrice.String(), expectedVariant.CalculatedPrice.String())
				}

				if !actualVariant.CalculatedComparedAtPrice.Equal(expectedVariant.CalculatedComparedAtPrice) {
					t.Errorf("变体 %s 的 CalculatedComparedAtPrice = %s, 期望 %s",
						actualVariant.ID, actualVariant.CalculatedComparedAtPrice.String(), expectedVariant.CalculatedComparedAtPrice.String())
				}

				if actualVariant.Status != expectedVariant.Status {
					t.Errorf("变体 %s 的 Status = %s, 期望 %s",
						actualVariant.ID, actualVariant.Status, expectedVariant.Status)
				}
			}
		})
	}
}

func TestCalculatePricesInput_connectorProductHasSetUpMultiPrice(t *testing.T) {
	tests := []struct {
		name              string
		input             *CalculatePricesInput
		connectorProduct  *connector_sdk_v2_product.ModelsResponseProduct
		wantHasMultiPrice bool
	}{
		{
			name: "有设置多币种价格-匹配当前销售渠道币种",
			input: &CalculatePricesInput{
				SalesChannelCurrency: "USD",
				ProductsCenterProduct: &CalculatePriceProductsCenterProductInput{
					Variants: []CalculatePricesVariantInput{
						{
							ExternalID: "var1",
						},
					},
				},
			},
			connectorProduct: &connector_sdk_v2_product.ModelsResponseProduct{
				Variants: []connector_sdk_v2_product.ModelsResponseProductVariant{
					{
						ExternalID: types.MakeString("var1"),
						PresentmentPrices: []connector_sdk_v2_common.ModelsPrecentmentPrice{
							{
								Price: &connector_sdk_v2_common.ModelsMoney{
									Currency: types.MakeString("USD"),
									Amount:   types.MakeFloat64(19.99),
								},
							},
						},
					},
				},
			},
			wantHasMultiPrice: true,
		},
		{
			name: "有设置多币种价格-但不匹配当前销售渠道币种",
			input: &CalculatePricesInput{
				SalesChannelCurrency: "EUR",
				ProductsCenterProduct: &CalculatePriceProductsCenterProductInput{
					Variants: []CalculatePricesVariantInput{
						{
							ExternalID: "var1",
						},
					},
				},
			},
			connectorProduct: &connector_sdk_v2_product.ModelsResponseProduct{
				Variants: []connector_sdk_v2_product.ModelsResponseProductVariant{
					{
						ExternalID: types.MakeString("var1"),
						PresentmentPrices: []connector_sdk_v2_common.ModelsPrecentmentPrice{
							{
								Price: &connector_sdk_v2_common.ModelsMoney{
									Currency: types.MakeString("USD"),
									Amount:   types.MakeFloat64(19.99),
								},
							},
						},
					},
				},
			},
			wantHasMultiPrice: false,
		},
		{
			name: "未设置多币种价格",
			input: &CalculatePricesInput{
				SalesChannelCurrency: "USD",
				ProductsCenterProduct: &CalculatePriceProductsCenterProductInput{
					Variants: []CalculatePricesVariantInput{
						{
							ExternalID: "var1",
						},
					},
				},
			},
			connectorProduct: &connector_sdk_v2_product.ModelsResponseProduct{
				Variants: []connector_sdk_v2_product.ModelsResponseProductVariant{
					{
						ExternalID:        types.MakeString("var1"),
						PresentmentPrices: []connector_sdk_v2_common.ModelsPrecentmentPrice{},
					},
				},
			},
			wantHasMultiPrice: false,
		},
		{
			name: "变体ID不匹配",
			input: &CalculatePricesInput{
				SalesChannelCurrency: "USD",
				ProductsCenterProduct: &CalculatePriceProductsCenterProductInput{
					Variants: []CalculatePricesVariantInput{
						{
							ExternalID: "var1",
						},
					},
				},
			},
			connectorProduct: &connector_sdk_v2_product.ModelsResponseProduct{
				Variants: []connector_sdk_v2_product.ModelsResponseProductVariant{
					{
						ExternalID: types.MakeString("var2"),
						PresentmentPrices: []connector_sdk_v2_common.ModelsPrecentmentPrice{
							{
								Price: &connector_sdk_v2_common.ModelsMoney{
									Currency: types.MakeString("USD"),
									Amount:   types.MakeFloat64(19.99),
								},
							},
						},
					},
				},
			},
			wantHasMultiPrice: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := tt.input.connectorProductHasSetUpMultiPrice(tt.connectorProduct)
			if got != tt.wantHasMultiPrice {
				t.Errorf("connectorProductHasSetUpMultiPrice() = %v, want %v", got, tt.wantHasMultiPrice)
			}
		})
	}
}
