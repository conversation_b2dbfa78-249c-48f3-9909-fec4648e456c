package calculators

import (
	"context"
	"errors"
	"reflect"
	"testing"

	"github.com/shopspring/decimal"

	pcp "github.com/AfterShip/connectors-sdk-go/v2/product-currency-prices"
	"github.com/AfterShip/connectors-sdk-go/v2/stores"
	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/pltf-pd-product-listings/internal/consts"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
)

func TestServiceImpl_isMarketsMappingValid(t *testing.T) {
	tests := []struct {
		name                       string
		marketsMapping             *models.MarketsCurrencyMapping
		matchCurrencyActiveMarkets []stores.ModelsCountryRegionCurrency
		want                       bool
	}{
		{
			name: "匹配成功 - 货币和国家区域都匹配",
			marketsMapping: &models.MarketsCurrencyMapping{
				Currency:      "USD",
				CountryRegion: "US",
			},
			matchCurrencyActiveMarkets: []stores.ModelsCountryRegionCurrency{
				{
					Currency:      types.MakeString("USD"),
					CountryRegion: types.MakeString("US"),
				},
			},
			want: true,
		},
		{
			name: "匹配失败 - 多个市场但没有匹配的",
			marketsMapping: &models.MarketsCurrencyMapping{
				Currency:      "USD",
				CountryRegion: "US",
			},
			matchCurrencyActiveMarkets: []stores.ModelsCountryRegionCurrency{
				{
					Currency:      types.MakeString("EUR"),
					CountryRegion: types.MakeString("FR"),
				},
				{
					Currency:      types.MakeString("GBP"),
					CountryRegion: types.MakeString("GB"),
				},
			},
			want: false,
		},
		{
			name: "匹配失败 - 货币匹配但国家区域不匹配",
			marketsMapping: &models.MarketsCurrencyMapping{
				Currency:      "USD",
				CountryRegion: "US",
			},
			matchCurrencyActiveMarkets: []stores.ModelsCountryRegionCurrency{
				{
					Currency:      types.MakeString("USD"),
					CountryRegion: types.MakeString("CA"),
				},
			},
			want: false,
		},
		{
			name: "匹配失败 - 国家区域匹配但货币不匹配",
			marketsMapping: &models.MarketsCurrencyMapping{
				Currency:      "USD",
				CountryRegion: "US",
			},
			matchCurrencyActiveMarkets: []stores.ModelsCountryRegionCurrency{
				{
					Currency:      types.MakeString("CAD"),
					CountryRegion: types.MakeString("US"),
				},
			},
			want: false,
		},
		{
			name: "匹配失败 - 空的匹配市场列表",
			marketsMapping: &models.MarketsCurrencyMapping{
				Currency:      "USD",
				CountryRegion: "US",
			},
			matchCurrencyActiveMarkets: []stores.ModelsCountryRegionCurrency{},
			want:                       false,
		},
		{
			name:           "匹配失败 - marketsMapping 为 nil",
			marketsMapping: nil,
			matchCurrencyActiveMarkets: []stores.ModelsCountryRegionCurrency{
				{
					Currency:      types.MakeString("USD"),
					CountryRegion: types.MakeString("US"),
				},
			},
			want: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &serviceImpl{}
			got := s.isMarketsMappingValid(tt.marketsMapping, tt.matchCurrencyActiveMarkets)
			if got != tt.want {
				t.Errorf("isMarketsMappingValid() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_convertStringPriceToDecimal(t *testing.T) {
	tests := []struct {
		name    string
		price   string
		want    decimal.Decimal
		wantErr bool
	}{
		{
			name:    "正常价格字符串",
			price:   "123.45",
			want:    decimal.NewFromFloat(123.45),
			wantErr: false,
		},
		{
			name:    "整数价格字符串",
			price:   "100",
			want:    decimal.NewFromFloat(100),
			wantErr: false,
		},
		{
			name:    "空字符串",
			price:   "",
			want:    decimal.NewFromFloat(0),
			wantErr: false,
		},
		{
			name:    "非数字字符串",
			price:   "abc",
			want:    decimal.Decimal{},
			wantErr: true,
		},
		{
			name:    "负数价格字符串",
			price:   "-10.50",
			want:    decimal.NewFromFloat(-10.50),
			wantErr: false,
		},
		{
			name:    "带千位分隔符的价格字符串",
			price:   "1,234.56",
			want:    decimal.Decimal{},
			wantErr: true,
		},
		{
			name:    "零值价格",
			price:   "0",
			want:    decimal.NewFromFloat(0),
			wantErr: false,
		},
		{
			name:    "高精度价格",
			price:   "123.4567890123",
			want:    decimal.RequireFromString("123.4567890123"),
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := convertStringPriceToDecimal(tt.price)
			if (err != nil) != tt.wantErr {
				t.Errorf("convertStringPriceToDecimal() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !tt.wantErr && !got.Equal(tt.want) {
				t.Errorf("convertStringPriceToDecimal() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_buildCalculatedPriceByShopifyMarkets(t *testing.T) {
	tests := []struct {
		name           string
		input          *CalculatePricesInput
		currencyPrices map[string]pcp.ModelsProductCurrencyPrices
		want           map[string]pricePair
		wantErr        bool
		errType        error
	}{
		{
			name: "正常处理 - 所有变体都有对应的货币价格",
			input: &CalculatePricesInput{
				ProductsCenterProduct: &CalculatePriceProductsCenterProductInput{
					Variants: []CalculatePricesVariantInput{
						{
							ExternalID: "var-001",
							Price: models.Price{
								Amount:   decimal.NewFromFloat(100),
								Currency: "USD",
							},
							CompareAtPrice: models.Price{
								Amount:   decimal.NewFromFloat(120),
								Currency: "USD",
							},
						},
						{
							ExternalID: "var-002",
							Price: models.Price{
								Amount:   decimal.NewFromFloat(200),
								Currency: "USD",
							},
							CompareAtPrice: models.Price{
								Amount:   decimal.NewFromFloat(220),
								Currency: "USD",
							},
						},
					},
				},
			},
			currencyPrices: map[string]pcp.ModelsProductCurrencyPrices{
				"var-001": {
					ExternalVariantID: types.MakeString("var-001"),
					Price: &pcp.ModelsProductCurrencyPricesPrice{
						Amount: types.MakeString("80.00"),
					},
					CompareAtPrice: &pcp.ModelsProductCurrencyPricesCompareAtPrice{
						Amount: types.MakeString("100.00"),
					},
				},
				"var-002": {
					ExternalVariantID: types.MakeString("var-002"),
					Price: &pcp.ModelsProductCurrencyPricesPrice{
						Amount: types.MakeString("160.00"),
					},
					CompareAtPrice: &pcp.ModelsProductCurrencyPricesCompareAtPrice{
						Amount: types.MakeString("180.00"),
					},
				},
			},
			want: map[string]pricePair{
				"var-001": {
					SalePrice:      decimal.NewFromFloat(80),
					CompareAtPrice: decimal.NewFromFloat(100),
					priceFrom:      consts.PriceFromCurrencyShopifyMarkets,
				},
				"var-002": {
					SalePrice:      decimal.NewFromFloat(160),
					CompareAtPrice: decimal.NewFromFloat(180),
					priceFrom:      consts.PriceFromCurrencyShopifyMarkets,
				},
			},
			wantErr: false,
		},
		{
			name: "正常处理 - 变体的比较价为零，使用销售价填充",
			input: &CalculatePricesInput{
				ProductsCenterProduct: &CalculatePriceProductsCenterProductInput{
					Variants: []CalculatePricesVariantInput{
						{
							ExternalID: "var-001",
							Price: models.Price{
								Amount:   decimal.NewFromFloat(100),
								Currency: "USD",
							},
							CompareAtPrice: models.Price{
								Amount:   decimal.NewFromFloat(120),
								Currency: "USD",
							},
						},
					},
				},
			},
			currencyPrices: map[string]pcp.ModelsProductCurrencyPrices{
				"var-001": {
					ExternalVariantID: types.MakeString("var-001"),
					Price: &pcp.ModelsProductCurrencyPricesPrice{
						Amount: types.MakeString("80.00"),
					},
					CompareAtPrice: &pcp.ModelsProductCurrencyPricesCompareAtPrice{
						Amount: types.MakeString("0"),
					},
				},
			},
			want: map[string]pricePair{
				"var-001": {
					SalePrice:      decimal.NewFromFloat(80),
					CompareAtPrice: decimal.NewFromFloat(80),
					priceFrom:      consts.PriceFromCurrencyShopifyMarkets,
				},
			},
			wantErr: false,
		},
		{
			name: "错误处理 - 销售价为负数",
			input: &CalculatePricesInput{
				ProductsCenterProduct: &CalculatePriceProductsCenterProductInput{
					Variants: []CalculatePricesVariantInput{
						{
							ExternalID: "var-001",
							Price: models.Price{
								Amount:   decimal.NewFromFloat(100),
								Currency: "USD",
							},
						},
					},
				},
			},
			currencyPrices: map[string]pcp.ModelsProductCurrencyPrices{
				"var-001": {
					ExternalVariantID: types.MakeString("var-001"),
					Price: &pcp.ModelsProductCurrencyPricesPrice{
						Amount: types.MakeString("-80.00"),
					},
					CompareAtPrice: &pcp.ModelsProductCurrencyPricesCompareAtPrice{
						Amount: types.MakeString("100.00"),
					},
				},
			},
			want:    nil,
			wantErr: true,
			errType: ErrPriceIsNegative,
		},
		{
			name: "错误处理 - 比较价为负数",
			input: &CalculatePricesInput{
				ProductsCenterProduct: &CalculatePriceProductsCenterProductInput{
					Variants: []CalculatePricesVariantInput{
						{
							ExternalID: "var-001",
							Price: models.Price{
								Amount:   decimal.NewFromFloat(100),
								Currency: "USD",
							},
						},
					},
				},
			},
			currencyPrices: map[string]pcp.ModelsProductCurrencyPrices{
				"var-001": {
					ExternalVariantID: types.MakeString("var-001"),
					Price: &pcp.ModelsProductCurrencyPricesPrice{
						Amount: types.MakeString("80.00"),
					},
					CompareAtPrice: &pcp.ModelsProductCurrencyPricesCompareAtPrice{
						Amount: types.MakeString("-100.00"),
					},
				},
			},
			want:    nil,
			wantErr: true,
			errType: ErrPriceIsNegative,
		},
		{
			name: "错误处理 - 销售价格非数字字符串",
			input: &CalculatePricesInput{
				ProductsCenterProduct: &CalculatePriceProductsCenterProductInput{
					Variants: []CalculatePricesVariantInput{
						{
							ExternalID: "var-001",
							Price: models.Price{
								Amount:   decimal.NewFromFloat(100),
								Currency: "USD",
							},
						},
					},
				},
			},
			currencyPrices: map[string]pcp.ModelsProductCurrencyPrices{
				"var-001": {
					ExternalVariantID: types.MakeString("var-001"),
					Price: &pcp.ModelsProductCurrencyPricesPrice{
						Amount: types.MakeString("abc"),
					},
					CompareAtPrice: &pcp.ModelsProductCurrencyPricesCompareAtPrice{
						Amount: types.MakeString("100.00"),
					},
				},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "正常处理 - 高精度价格",
			input: &CalculatePricesInput{
				ProductsCenterProduct: &CalculatePriceProductsCenterProductInput{
					Variants: []CalculatePricesVariantInput{
						{
							ExternalID: "var-001",
							Price: models.Price{
								Amount:   decimal.NewFromFloat(100),
								Currency: "USD",
							},
						},
					},
				},
			},
			currencyPrices: map[string]pcp.ModelsProductCurrencyPrices{
				"var-001": {
					ExternalVariantID: types.MakeString("var-001"),
					Price: &pcp.ModelsProductCurrencyPricesPrice{
						Amount: types.MakeString("80.1234567890"),
					},
					CompareAtPrice: &pcp.ModelsProductCurrencyPricesCompareAtPrice{
						Amount: types.MakeString("100.9876543210"),
					},
				},
			},
			want: map[string]pricePair{
				"var-001": {
					SalePrice:      decimal.RequireFromString("80.1234567890"),
					CompareAtPrice: decimal.RequireFromString("100.9876543210"),
					priceFrom:      consts.PriceFromCurrencyShopifyMarkets,
				},
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := buildCalculatedPriceByShopifyMarkets(context.Background(), tt.input, tt.currencyPrices)
			if (err != nil) != tt.wantErr {
				t.Errorf("buildCalculatedPriceByShopifyMarkets() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.wantErr && tt.errType != nil && !errors.Is(err, tt.errType) {
				t.Errorf("buildCalculatedPriceByShopifyMarkets() error type = %v, wantErrType %v", err, tt.errType)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				// 自定义比较逻辑，对 decimal 类型进行特殊处理
				if len(got) != len(tt.want) {
					t.Errorf("buildCalculatedPriceByShopifyMarkets() result length = %d, want %d", len(got), len(tt.want))
					return
				}

				for variantID, gotPrice := range got {
					wantPrice, exists := tt.want[variantID]
					if !exists {
						t.Errorf("buildCalculatedPriceByShopifyMarkets() 结果包含意外的变体ID: %s", variantID)
						continue
					}

					// 比较 decimal 值使用 Equal 方法而不是 reflect.DeepEqual
					if !gotPrice.SalePrice.Equal(wantPrice.SalePrice) {
						t.Errorf("变体 %s 的销售价格 = %s, 期望 %s",
							variantID, gotPrice.SalePrice.String(), wantPrice.SalePrice.String())
					}

					if !gotPrice.CompareAtPrice.Equal(wantPrice.CompareAtPrice) {
						t.Errorf("变体 %s 的比较价格 = %s, 期望 %s",
							variantID, gotPrice.CompareAtPrice.String(), wantPrice.CompareAtPrice.String())
					}

					if gotPrice.priceFrom != wantPrice.priceFrom {
						t.Errorf("变体 %s 的价格来源 = %s, 期望 %s",
							variantID, gotPrice.priceFrom, wantPrice.priceFrom)
					}
				}

				// 检查是否所有期望的变体ID都存在于结果中
				for variantID := range tt.want {
					if _, exists := got[variantID]; !exists {
						t.Errorf("buildCalculatedPriceByShopifyMarkets() 结果缺少变体ID: %s", variantID)
					}
				}
			}
		})
	}
}
