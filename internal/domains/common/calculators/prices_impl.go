package calculators

import (
	"context"
	"fmt"

	"github.com/pkg/errors"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"

	cnt_lib_utils "github.com/AfterShip/connectors-library/utils"
	pcp "github.com/AfterShip/connectors-sdk-go/v2/product-currency-prices"
	connector_sdk_v2_product "github.com/AfterShip/connectors-sdk-go/v2/products"
	"github.com/AfterShip/connectors-sdk-go/v2/stores"
	"github.com/AfterShip/pltf-pd-product-listings/internal/consts"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/organization_settings"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/settings"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
	"github.com/AfterShip/pltf-pd-product-listings/internal/third_party/connectors"
	"github.com/AfterShip/pltf-pd-product-listings/internal/third_party/feed"
)

// nolint:funlen
func (s *serviceImpl) CalculatePrices(ctx context.Context, input *CalculatePricesInput) (*CalculatePricesOutput, error) {
	if err := s.validate.Struct(input); err != nil {
		return nil, errors.WithStack(err)
	}

	if err := input.customValidator(); err != nil {
		return nil, errors.WithStack(errors.WithStack(err))
	}

	// lookup region config
	salesChannelRegionConfig, err := s.config.LoadChannelRegionConfig(input.SalesChannel.Platform, input.SalesChannel.CountryRegion)
	if err != nil {
		// fatal error,should alert with high priority
		s.logger.ErrorCtx(ctx, "load region config error",
			zap.String("sales_channel_region", input.SalesChannel.CountryRegion),
			zap.Error(err))
		return nil, errors.WithStack(err)
	}
	// inject sales channel currency to input
	input.SalesChannelCurrency = salesChannelRegionConfig.Currency

	syncPrice, marketsMapping, err := s.lookUpStoreSettings(ctx, input)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	pricePairs, currencyConvertor, err := s.buildPricesV2(ctx, input, marketsMapping)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	output := &CalculatePricesOutput{
		PriceSync:                syncPrice,
		CurrencyConvertor:        currencyConvertor,
		MarketsCurrencyMapping:   marketsMapping,
		Input:                    input,
		PricePairs:               pricePairs,
		SalesChannelRegionConfig: salesChannelRegionConfig,
	}
	output.FillCalculatedPrice()

	return output, nil
}

// nolint:dupl
func (s *serviceImpl) isShopifyMarketsGrayEnabled(ctx context.Context, orgID string) bool {
	var enabledShopifyMarketsMapping bool
	supportFeatures, err := s.feedClient.SupportFeature.GetSupportFeatures(ctx, orgID)
	if err != nil {
		s.logger.ErrorCtx(ctx, "failed to get support features for shopify markets mapping",
			zap.String("organization_id", orgID), zap.Error(err))
	} else {
		enabledShopifyMarketsMapping = feed.RunInGrayModel(supportFeatures, feed.FeatureCodeShopifyMarketsMapping)
	}

	return enabledShopifyMarketsMapping
}

// nolint:dupl
func (s *serviceImpl) lookUpPriceSync(ctx context.Context, input *CalculatePricesInput) (*models.PriceSync, error) {
	if input.PriceSync != nil {
		return input.PriceSync, nil
	}

	// search from settings
	storeSettings, err := s.settingService.List(ctx, &settings.SearchSettingArgs{
		OrganizationID:       input.Organization.ID,
		SourceAppKey:         input.Source.App.Key,
		SourceAppPlatform:    input.Source.App.Platform,
		SalesChannelStoreKey: input.SalesChannel.StoreKey,
		SalesChannelPlatform: input.SalesChannel.Platform,
	})
	if err != nil {
		return nil, err
	}

	if len(storeSettings) == 0 {
		return nil, settings.ErrSettingSyncPriceNotFound
	}
	return &storeSettings[0].PriceSync, nil
}

// nolint:dupl
func (s *serviceImpl) lookUpStoreSettings(ctx context.Context, input *CalculatePricesInput) (*models.PriceSync, *models.MarketsCurrencyMapping, error) {
	var storeSettings settings.Settings
	var err error
	storeSettings, err = s.settingService.List(ctx, &settings.SearchSettingArgs{
		OrganizationID:       input.Organization.ID,
		SourceAppKey:         input.Source.App.Key,
		SourceAppPlatform:    input.Source.App.Platform,
		SalesChannelStoreKey: input.SalesChannel.StoreKey,
		SalesChannelPlatform: input.SalesChannel.Platform,
	})
	if err != nil {
		return nil, nil, err
	}

	priceSync, err := storeSettings.LookUpPriceSync(input.PriceSync)
	if err != nil {
		return nil, nil, err
	}

	marketsMapping := storeSettings.LookUpShopifyMarketsMapping(input.Source.App.Platform)

	return priceSync, marketsMapping, nil
}

func (s *serviceImpl) lookUpCurrencyConvertor(ctx context.Context, input *CalculatePricesInput) (*models.CurrencyConvertor, error) {
	sourceCurrency := input.lookUpProductsCenterProductCurrency()
	organizationSettings, err := s.organizationSettingService.List(ctx, &organization_settings.SearchOrganizationSettingArgs{
		OrganizationID: input.Organization.ID,
	})
	if err != nil {
		return nil, err
	}

	if len(organizationSettings) == 0 {
		return nil, organization_settings.ErrOrganizationSettingNotFound
	}

	convertor := organizationSettings[0].LookUpCurrencyConvertor(sourceCurrency, input.SalesChannelCurrency)
	if convertor == nil {
		return nil, organization_settings.ErrSpecificCurrencyConvertorNotFound
	}
	return convertor, nil
}

// nolint:funlen
func (s *serviceImpl) buildPrices(ctx context.Context, input *CalculatePricesInput) (map[string]pricePair,
	*models.CurrencyConvertor, error) {
	sourceCurrency := input.lookUpProductsCenterProductCurrency()
	if sourceCurrency == "" {
		return nil, nil, ErrCurrencyEmpty
	}

	currencyIsSame := sourceCurrency == input.SalesChannelCurrency
	var connectorProduct *connector_sdk_v2_product.ModelsResponseProduct
	var err error
	var currencyConvertor *models.CurrencyConvertor
	if !currencyIsSame {
		connectorProduct, err = s.connectorService.GetProductByID(ctx, input.ProductsCenterProduct.ConnectorProductID, "")
		if err != nil {
			return nil, nil, err
		}

		if connectorProduct == nil {
			return nil, nil, errors.New("cntProduct is nil")
		}

		if !input.connectorProductHasSetUpMultiPrice(connectorProduct) {
			// query currency convertor
			currencyConvertor, err = s.lookUpCurrencyConvertor(ctx, input)
			if err != nil {
				return nil, nil, errors.WithStack(err)
			}
		}

		s.logger.InfoCtx(ctx, "hit multi price", zap.String("organization_id", input.Organization.ID),
			zap.String("source_platform", input.Source.App.Platform),
			zap.String("source_app_key", input.Source.App.Key),
			zap.String("source_currency", sourceCurrency),
			zap.String("sales_channel_store_key", input.SalesChannel.StoreKey),
			zap.String("sales_channel_region", input.SalesChannel.CountryRegion),
			zap.String("sales_channel_currency", input.SalesChannelCurrency),
			zap.String("inputs", cnt_lib_utils.GetJsonIndent(input)))
	}

	// 获取价格的关键逻辑，需要有单测覆盖
	// todo: 预留通过外部服务获取多币种价格,country_region 从 salesChannelRegionConfig.Region 获取
	return input.generatePricePairs(currencyConvertor, connectorProduct), currencyConvertor, nil
}

// nolint:funlen
func (s *serviceImpl) buildPricesV2(ctx context.Context, input *CalculatePricesInput,
	marketsMapping *models.MarketsCurrencyMapping) (map[string]pricePair, *models.CurrencyConvertor, error) {
	sourceCurrency := input.lookUpProductsCenterProductCurrency()
	if sourceCurrency == "" {
		return nil, nil, ErrCurrencyEmpty
	}

	// 货币一致，直接返回 ProductsCenterProduct.variants 的价格
	if sourceCurrency == input.SalesChannelCurrency {
		result, err := buildCalculatedPriceByVariants(ctx, input)
		return result, nil, err
	}

	s.logger.InfoCtx(ctx, "hit multi price", zap.String("organization_id", input.Organization.ID),
		zap.String("source_platform", input.Source.App.Platform),
		zap.String("source_app_key", input.Source.App.Key),
		zap.String("source_currency", sourceCurrency),
		zap.String("sales_channel_store_key", input.SalesChannel.StoreKey),
		zap.String("sales_channel_region", input.SalesChannel.CountryRegion),
		zap.String("sales_channel_currency", input.SalesChannelCurrency),
		zap.String("inputs", cnt_lib_utils.GetJsonIndent(input)), zap.String("version", "v2"))

	matchCurrencyActiveMarkets, isUsePriceConvertor, err := s.isUsePriceConvertor(ctx, marketsMapping, input)
	if err != nil {
		return nil, nil, errors.WithStack(err)
	}

	if isUsePriceConvertor {
		result, priceConvertor, err := s.buildCalculatedPriceByPriceConvertor(ctx, input)
		if err != nil {
			return nil, nil, errors.WithStack(err)
		}
		return result, priceConvertor, nil
	}

	if !s.isMarketsMappingValid(marketsMapping, matchCurrencyActiveMarkets) {
		return nil, nil, errors.WithStack(ErrMissingShopifyMarketsMapping)
	}

	currencyPrices, err := s.getPriceCurrenciesPricesFromCNT(ctx, input, marketsMapping)
	if err != nil {
		return nil, nil, err
	}

	// 找不到 shopify markets mapping 对应的 价格报错
	if len(currencyPrices) == 0 {
		return nil, nil, errors.WithStack(ErrMissingShopifyMarketsMapping)
	}

	result, err := buildCalculatedPriceByShopifyMarkets(ctx, input, currencyPrices)
	return result, nil, err
}

func (s *serviceImpl) isMarketsMappingValid(marketsMapping *models.MarketsCurrencyMapping, matchCurrencyActiveMarkets []stores.ModelsCountryRegionCurrency) bool {
	if marketsMapping == nil {
		return false
	}

	for _, v := range matchCurrencyActiveMarkets {
		if marketsMapping.Currency == v.Currency.String() && marketsMapping.CountryRegion == v.CountryRegion.String() {
			return true
		}
	}

	return false
}

func (s *serviceImpl) isUsePriceConvertor(
	ctx context.Context, marketsCurrencyMapping *models.MarketsCurrencyMapping, input *CalculatePricesInput,
) ([]stores.ModelsCountryRegionCurrency, bool, error) {
	// User price convertor: 1. 非 Shopify 平台 2. 没有配置 Shopify markets mapping
	if input.Source.App.Platform != consts.Shopify || marketsCurrencyMapping == nil {
		return nil, true, nil
	}

	// 有 Shopify markets mapping 配置的情况下，要结合当前的 store 配置判断
	ecommerceStore, err := s.connectorService.GetStore(ctx, stores.GetStoresParams{
		OrganizationID: input.Organization.ID,
		AppKey:         input.Source.App.Key,
		AppPlatform:    input.Source.App.Platform,
		Page:           1,
		Limit:          10,
	})
	if err != nil {
		return nil, false, errors.WithStack(err)
	}

	// User price convertor: 3. store 资源不存在 4. store 资源里没有多货币配置
	if ecommerceStore == nil || len(ecommerceStore.EnabledCurrencies) == 0 {
		return nil, true, nil
	}

	matchCurrencyActiveMarkets := make([]stores.ModelsCountryRegionCurrency, 0)
	for i, v := range ecommerceStore.EnabledCurrencies {
		if v.Currency.String() == input.SalesChannelCurrency && v.Status.String() == enabledCurrencyStatusActive {
			matchCurrencyActiveMarkets = append(matchCurrencyActiveMarkets, ecommerceStore.EnabledCurrencies[i])
		}
	}

	// User price convertor: 5. 没有找到 active 状态的货币跟 SalesChannel 货币一致
	if len(matchCurrencyActiveMarkets) == 0 {
		return nil, true, nil
	}

	// User markets mapping: active 状态的 markets 有一个货币跟 SalesChannel 货币一致，预期就不走 price convertor
	// 至于 Shopify markets mapping 是否可用，由后续的价格处理逻辑处理，如果不可用，会报错
	return matchCurrencyActiveMarkets, false, nil
}

func (s *serviceImpl) buildCalculatedPriceByPriceConvertor(ctx context.Context,
	input *CalculatePricesInput) (map[string]pricePair, *models.CurrencyConvertor, error) {
	currencyConvertor, err := s.lookUpCurrencyConvertor(ctx, input)
	if err != nil {
		return nil, nil, errors.WithStack(err)
	}

	result := make(map[string]pricePair)
	for _, v := range input.ProductsCenterProduct.Variants {
		salePrice := convertWithCurrency(v.Price.Amount, currencyConvertor)
		compareAtPrice := convertWithCurrency(v.CompareAtPrice.Amount, currencyConvertor)
		// 没有设置划线价时，使用 price 填充
		if compareAtPrice.Equal(decimal.NewFromFloat(0)) {
			compareAtPrice = salePrice
		}
		result[v.ExternalID] = pricePair{
			SalePrice:      salePrice,
			CompareAtPrice: compareAtPrice,
			priceFrom:      consts.PriceFromCurrencyConvertor,
		}
	}

	return result, currencyConvertor, nil
}

func buildCalculatedPriceByVariants(ctx context.Context, input *CalculatePricesInput) (map[string]pricePair, error) {
	result := make(map[string]pricePair)

	for _, v := range input.ProductsCenterProduct.Variants {
		compareAtPrice := v.CompareAtPrice.Amount
		// 没有设置划线价时，使用 price 填充
		if compareAtPrice.Equal(decimal.NewFromFloat(0)) {
			compareAtPrice = v.Price.Amount
		}
		result[v.ExternalID] = pricePair{
			SalePrice:      v.Price.Amount,
			CompareAtPrice: compareAtPrice,
			priceFrom:      consts.PriceFromInputVariant,
		}
	}

	return result, nil
}

func buildCalculatedPriceByShopifyMarkets(
	ctx context.Context, input *CalculatePricesInput, currencyPrices map[string]pcp.ModelsProductCurrencyPrices,
) (map[string]pricePair, error) {
	result := make(map[string]pricePair)

	zeroPrice := decimal.NewFromFloat(0)
	for _, v := range input.ProductsCenterProduct.Variants {
		currencyPrice, ok := currencyPrices[v.ExternalID]
		if !ok { // 只有 variants 级别没有价格信息，不符合预期
			return nil, errors.Errorf("currency price not found for variant %s", v.ExternalID)
		}

		salesPrice, err := convertStringPriceToDecimal(currencyPrice.Price.Amount.String())
		if err != nil {
			return nil, err
		}

		compareAtPrice, err := convertStringPriceToDecimal(currencyPrice.CompareAtPrice.Amount.String())
		if err != nil {
			return nil, err
		}

		// 没有设置划线价时，使用 price 填充
		if compareAtPrice.Equal(zeroPrice) {
			compareAtPrice = salesPrice
		}

		if salesPrice.LessThan(zeroPrice) || compareAtPrice.LessThan(zeroPrice) {
			return nil, errors.WithMessage(ErrPriceIsNegative, fmt.Sprintf("salesPrice: %s, compareAtPrice: %s",
				salesPrice.String(), compareAtPrice.String()))
		}

		result[v.ExternalID] = pricePair{
			SalePrice:      salesPrice,
			CompareAtPrice: compareAtPrice,
			priceFrom:      consts.PriceFromCurrencyShopifyMarkets,
		}
	}

	return result, nil
}

func convertStringPriceToDecimal(price string) (decimal.Decimal, error) {
	if price == "" {
		return decimal.NewFromFloat(0), nil
	}

	return decimal.NewFromString(price)
}

func (s *serviceImpl) getPriceCurrenciesPricesFromCNT(
	ctx context.Context, input *CalculatePricesInput, marketsMapping *models.MarketsCurrencyMapping,
) (map[string]pcp.ModelsProductCurrencyPrices, error) {
	cntProduct, err := s.connectorService.GetProductByID(ctx, input.ProductsCenterProduct.ConnectorProductID, "")
	if err != nil {
		return nil, err
	}

	if cntProduct == nil {
		return nil, errors.New("cntProduct is nil")
	}

	result := make(map[string]pcp.ModelsProductCurrencyPrices)

	currencyPrices, err := s.connectorService.GetProductCurrencyPrices(ctx, connectors.GetProductCurrencyPricesParams{
		CountryRegion:     marketsMapping.CountryRegion,
		Currency:          marketsMapping.Currency,
		ConnectorsProduct: cntProduct,
	})
	if err != nil {
		if errors.Is(err, connectors.ErrProductCurrencyPricesEmpty) {
			return result, nil
		}
		return nil, err
	}

	for i, v := range currencyPrices {
		result[v.ExternalVariantID.String()] = currencyPrices[i]
	}

	return result, nil
}
