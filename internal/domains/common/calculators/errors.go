package calculators

import (
	"github.com/pkg/errors"
)

var (
	ErrCurrencyEmpty                = errors.New("currency is empty")
	ErrPriceNotFound                = errors.New("price not found")
	ErrInventoryNotFound            = errors.New("inventory not found")
	ErrCalculateInventoryFailed     = errors.New("inventory calculate failed")
	ErrMissingShopifyMarketsMapping = errors.New("missing shopify markets mapping")
	ErrPriceIsNegative              = errors.New("price is negative")
)
