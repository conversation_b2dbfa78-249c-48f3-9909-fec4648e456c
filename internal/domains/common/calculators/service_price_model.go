package calculators

import (
	"github.com/shopspring/decimal"

	connector_sdk_v2_product "github.com/AfterShip/connectors-sdk-go/v2/products"
	"github.com/AfterShip/pltf-pd-product-listings/internal/config"
	"github.com/AfterShip/pltf-pd-product-listings/internal/consts"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
	"github.com/AfterShip/pltf-pd-product-listings/internal/utils/toolbox"
)

type CalculatePricesInput struct {
	SalesChannel          models.SalesChannel                       `json:"sales_channel"`
	Organization          models.Organization                       `json:"organization"`
	Source                models.Source                             `json:"source"`
	PriceSync             *models.PriceSync                         `json:"price_sync"`
	ProductsCenterProduct *CalculatePriceProductsCenterProductInput `json:"products_center_product" validate:"required"`
	SalesChannelCurrency  string
}

func (c *CalculatePricesInput) customValidator() error {
	if c.Organization.ID == "" ||
		c.Source.App.Platform == "" ||
		c.Source.App.Key == "" ||
		c.SalesChannel.StoreKey == "" ||
		c.SalesChannel.Platform == "" ||
		c.SalesChannel.CountryRegion == "" {
		return models.ErrMissRequiredBodyParam
	}
	return nil
}

func (c *CalculatePricesInput) connectorProductHasSetUpMultiPrice(connectorProduct *connector_sdk_v2_product.ModelsResponseProduct) bool {
	// 多币种是 SPU 级别，看一个 variant 是否配置即可
	for _, variant := range c.ProductsCenterProduct.Variants {
		_, _, variantHasSetUpMultiPrice := lookUpVariantPricePairOnMultiCurrencies(
			c.SalesChannelCurrency, variant.ExternalID, connectorProduct)
		return variantHasSetUpMultiPrice
	}
	return false
}

func (c *CalculatePricesInput) generatePricePairs(currencyConvertor *models.CurrencyConvertor,
	connectorProduct *connector_sdk_v2_product.ModelsResponseProduct) map[string]pricePair {
	pricePairs := make(map[string]pricePair)

	sourceCurrency := c.lookUpProductsCenterProductCurrency()
	salesChannelCurrency := c.SalesChannelCurrency
	priceFrom := consts.PriceFromInputVariant

	// nolint:gocritic
	for _, variant := range c.ProductsCenterProduct.Variants {
		var salePrice, compareAtPrice decimal.Decimal

		salePrice = variant.Price.Amount
		compareAtPrice = variant.CompareAtPrice.Amount

		/**
		1.币种不一致，先从 PresentmentPrices 里获取用户配置的多币种价格
		2.如果不一致，依然使用 price.Amount,并通过 currency_convert 转换获取
		*/
		if sourceCurrency != salesChannelCurrency {
			setupSalePrice, setupCompareAtPrice, variantHasSetUpMultiPrice := lookUpVariantPricePairOnMultiCurrencies(
				salesChannelCurrency, variant.ExternalID, connectorProduct)
			if variantHasSetUpMultiPrice {
				priceFrom = consts.PriceFromConnectorsVariantPresentmentPrice
				salePrice = decimal.NewFromFloat(setupSalePrice)
				compareAtPrice = decimal.NewFromFloat(setupCompareAtPrice)
			} else {
				priceFrom = consts.PriceFromCurrencyConvertor
				salePrice = convertWithCurrency(salePrice, currencyConvertor)
				compareAtPrice = convertWithCurrency(compareAtPrice, currencyConvertor)
			}
		}

		// compareAtPrice 为 0，可能是没有设置，ecommerce 没填写，中台会默认成本价为 0
		compareAtPriceFloat, _ := compareAtPrice.Float64()
		if compareAtPriceFloat == 0 {
			compareAtPrice = salePrice
		}
		pricePairs[variant.ExternalID] = pricePair{
			SalePrice:      salePrice,
			CompareAtPrice: compareAtPrice,
			priceFrom:      priceFrom,
		}
	}
	return pricePairs
}

func (c *CalculatePricesInput) lookUpProductsCenterProductCurrency() string {
	var currency string
	if c != nil && c.ProductsCenterProduct != nil &&
		len(c.ProductsCenterProduct.Variants) > 0 {
		currency = c.ProductsCenterProduct.Variants[0].Price.Currency
	}
	return currency
}

type CalculatePriceProductsCenterProductInput struct {
	ID                 string                        `json:"id" validate:"required"`
	ConnectorProductID string                        `json:"connector_product_id" validate:"required"`
	Variants           []CalculatePricesVariantInput `json:"variants" validate:"required,dive"`
}

type CalculatePricesVariantInput struct {
	ID             string       `json:"id" validate:"required"`
	ExternalID     string       `json:"external_id" validate:"required"`
	Price          models.Price `json:"price" validate:"required"`
	CompareAtPrice models.Price `json:"compare_at_price" validate:"required"`
}

type CalculatePricesOutput struct {
	// FillCalculatedPrice 里计算价格会用到这部分字段, 但是不需要输出到结果 payload
	Input                    *CalculatePricesInput       `json:"-"`
	PricePairs               map[string]pricePair        `json:"-"`
	SalesChannelRegionConfig *config.ChannelRegionConfig `json:"-"`

	// FillCalculatedPrice 里计算价格会用到这部分字段，而且要输出到结果 payload
	PriceSync              *models.PriceSync              `json:"price_sync"`
	CurrencyConvertor      *models.CurrencyConvertor      `json:"currency_convertor"`
	MarketsCurrencyMapping *models.MarketsCurrencyMapping `json:"markets_mapping"`

	// ProductsCenterProduct 会保存计算后的价格，在 FillCalculatedPrice 里赋值
	ProductsCenterProduct *CalculatePriceProductsCenterProductOutput `json:"products_center_product"`
}

func (c *CalculatePricesOutput) FillCalculatedPrice() {
	variants := make([]CalculatePricesVariantOutput, 0, len(c.Input.ProductsCenterProduct.Variants))
	for _, variant := range c.Input.ProductsCenterProduct.Variants {
		result := CalculatePricesVariantOutput{
			ID:         variant.ID,
			ExternalID: variant.ExternalID,
			Status:     consts.CalculateSuccess,
			Currency:   c.Input.SalesChannelCurrency,
		}
		prices, ok := c.PricePairs[variant.ExternalID]
		if !ok {
			result.Status = consts.CalculateFailed
			result.ErrorMessage = ErrPriceNotFound.Error()
			variants = append(variants, result)
			continue
		}

		// calculate prices: 最终计算出来的价格预期是写入到 TTS 的 sales_price
		calculatedPrice, updatingCompareAtPrice, err := toolbox.CalculateSalesPrice(prices.SalePrice, prices.CompareAtPrice, c.PriceSync)
		if err != nil {
			result.Status = consts.CalculateFailed
			result.ErrorMessage = err.Error()
		}

		result.CalculatedPrice = calculatedPrice
		// 没有开启同步划线价能力的客户也不会用到这个价格
		result.CalculatedComparedAtPrice = updatingCompareAtPrice
		result.Context = prices.priceFrom

		variants = append(variants, result)
	}

	c.ProductsCenterProduct = &CalculatePriceProductsCenterProductOutput{
		ID:                 c.Input.ProductsCenterProduct.ID,
		ConnectorProductID: c.Input.ProductsCenterProduct.ConnectorProductID,
		Variants:           variants,
	}

	c.wrapOutputWithTikTokSpecialCases(c.Input.SalesChannel, c.SalesChannelRegionConfig)
	c.wrapOutputWithSheinSpecialCases(c.Input.SalesChannel.Platform, c.SalesChannelRegionConfig)
}

func (c *CalculatePricesOutput) wrapOutputWithTikTokSpecialCases(salesChannel models.SalesChannel, cfg *config.ChannelRegionConfig) {
	if salesChannel.Platform != consts.TikTokShop {
		return
	}
	// no need to check price range
	if !cfg.CheckPriceRange {
		return
	}
	for i := range c.ProductsCenterProduct.Variants {
		if c.ProductsCenterProduct.Variants[i].Status == consts.CalculateFailed {
			continue
		}

		var roundPlaces int32 = 2
		if salesChannel.CountryRegion == consts.RegionJP {
			roundPlaces = 0 // tiktok 日本不允许小数
		}

		// tiktok 只允许两位小数
		roundCalculatedPrice := c.ProductsCenterProduct.Variants[i].CalculatedPrice.Round(roundPlaces)
		c.ProductsCenterProduct.Variants[i].CalculatedPrice = roundCalculatedPrice

		roundCalculatedComparedAtPrice := c.ProductsCenterProduct.Variants[i].CalculatedComparedAtPrice.Round(roundPlaces)
		c.ProductsCenterProduct.Variants[i].CalculatedComparedAtPrice = roundCalculatedComparedAtPrice

		// CalculatedPrice and CalculatedComparedAtPrice have been set with same value,so just check one of them
		priceFloat, _ := c.ProductsCenterProduct.Variants[i].CalculatedPrice.Float64()
		if err := cfg.ValidatePriceRange(priceFloat); err != nil {
			c.ProductsCenterProduct.Variants[i].Status = consts.CalculateFailed
			c.ProductsCenterProduct.Variants[i].ErrorMessage = err.Error()
		}
	}
}

func (c *CalculatePricesOutput) wrapOutputWithSheinSpecialCases(salesChannelPlatform string, cfg *config.ChannelRegionConfig) {
	if salesChannelPlatform != consts.Shein {
		return
	}

	for i := range c.ProductsCenterProduct.Variants {
		if c.ProductsCenterProduct.Variants[i].Status == consts.CalculateFailed {
			continue
		}

		// shein 只允许两位小数
		roundCalculatedPrice := c.ProductsCenterProduct.Variants[i].CalculatedPrice.Round(2)
		c.ProductsCenterProduct.Variants[i].CalculatedPrice = roundCalculatedPrice

		roundCalculatedComparedAtPrice := c.ProductsCenterProduct.Variants[i].CalculatedComparedAtPrice.Round(2)
		c.ProductsCenterProduct.Variants[i].CalculatedComparedAtPrice = roundCalculatedComparedAtPrice
	}
}

// GetPrice return price and compareAtPrice
func (c *CalculatePricesOutput) GetPrice(productsCenterVariantID string) (models.ProductVariantPrice, models.ProductVariantPrice, error) {
	if c == nil || c.ProductsCenterProduct == nil {
		return models.ProductVariantPrice{}, models.ProductVariantPrice{}, ErrPriceNotFound
	}
	for i := range c.ProductsCenterProduct.Variants {
		variant := c.ProductsCenterProduct.Variants[i]
		if variant.ID == productsCenterVariantID {
			return models.ProductVariantPrice{
					Currency: variant.Currency,
					Amount:   variant.CalculatedPrice.String(),
				}, models.ProductVariantPrice{
					Currency: variant.Currency,
					Amount:   variant.CalculatedComparedAtPrice.String(),
				}, nil
		}
	}
	return models.ProductVariantPrice{}, models.ProductVariantPrice{}, ErrPriceNotFound
}

type CalculatePriceProductsCenterProductOutput struct {
	ID                 string                         `json:"id"`
	ConnectorProductID string                         `json:"connector_product_id"`
	Variants           []CalculatePricesVariantOutput `json:"variants"`
}

type CalculatePricesVariantOutput struct {
	ID                        string          `json:"id"`
	ExternalID                string          `json:"external_id"`
	Currency                  string          `json:"currency"`
	CalculatedPrice           decimal.Decimal `json:"calculated_price"`
	CalculatedComparedAtPrice decimal.Decimal `json:"calculated_compared_at_price"`
	Status                    string          `json:"status"`
	Context                   string          `json:"context"`
	ErrorMessage              string          `json:"error_message"`
}

type pricePair struct {
	SalePrice      decimal.Decimal
	CompareAtPrice decimal.Decimal
	priceFrom      string
}

func convertWithCurrency(price decimal.Decimal, currencyConvertor *models.CurrencyConvertor) decimal.Decimal {
	if currencyConvertor == nil || currencyConvertor.CustomExchangeRate <= 0 {
		return price
	}
	exchangeRateDecimal := decimal.NewFromFloat(currencyConvertor.CustomExchangeRate)

	// 得到了需要增加或者减少的金额
	calculatedPriceDecimalFloat := price.Mul(exchangeRateDecimal)
	return calculatedPriceDecimalFloat
}

func lookUpVariantPricePairOnMultiCurrencies(salesChannelCurrency, externalVariantID string,
	product *connector_sdk_v2_product.ModelsResponseProduct) (float64, float64, bool) {
	var salePrice, compareAtPrice float64
	var variantHasSetUpMultiPrice bool
	// nolint:gocritic
	for _, variant := range product.Variants {
		if variant.ExternalID.String() != externalVariantID {
			continue
		}
		for _, presentmentPrice := range variant.PresentmentPrices {
			if presentmentPrice.Price != nil && presentmentPrice.Price.Currency.String() == salesChannelCurrency {
				salePrice = presentmentPrice.Price.Amount.Float64()
				variantHasSetUpMultiPrice = true
			}
			if presentmentPrice.CompareAtPrice != nil && presentmentPrice.CompareAtPrice.Currency.String() == salesChannelCurrency {
				compareAtPrice = presentmentPrice.CompareAtPrice.Amount.Float64()
				variantHasSetUpMultiPrice = true
			}
		}
	}

	return salePrice, compareAtPrice, variantHasSetUpMultiPrice
}
