package calculators

import (
	"github.com/pkg/errors"

	connector_sdk_v2_inventory_level "github.com/AfterShip/connectors-sdk-go/v2/inventory_levels"
	"github.com/AfterShip/pltf-pd-product-listings/internal/consts"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
	"github.com/AfterShip/pltf-pd-product-listings/internal/utils/slicex"
	"github.com/AfterShip/pltf-pd-product-listings/internal/utils/toolbox"
)

type CalculateAvailableQuantitiesInput struct {
	SalesChannel          models.SalesChannel                                     `json:"sales_channel"`
	Organization          models.Organization                                     `json:"organization"`
	Source                models.Source                                           `json:"source"`
	InventorySync         *models.InventorySync                                   `json:"inventory_sync"`
	ProductsCenterProduct *CalculateAvailableQuantitiesProductsCenterProductInput `json:"products_center_product" validate:"required"`
}

type CalculateAvailableQuantitiesProductsCenterProductInput struct {
	ID                 string                                     `json:"id" validate:"required"`
	ConnectorProductID string                                     `json:"connector_product_id" validate:"required"`
	Variants           []CalculateAvailableQuantitiesVariantInput `json:"variants" validate:"required,dive"`
}

type CalculateAvailableQuantitiesVariantInput struct {
	ID                      string  `json:"id" validate:"required"`
	ExternalID              string  `json:"external_id" validate:"required"`
	AvailableQuantity       float64 `json:"available_quantity"`
	AllowBackorder          bool    `json:"allow_backorder" validate:"omitempty"`
	ExternalInventoryItemID string  `json:"external_inventory_item_id"`
	Status                  string  `json:"status"`
}

type CalculateAvailableQuantitiesOutput struct {
	InventorySync         *models.InventorySync     `json:"inventory_sync"`
	ProductsCenterProduct *ProductsCenterProductOut `json:"products_center_product"`
}

type ProductsCenterProductOut struct {
	ID                 string                                      `json:"id"`
	ConnectorProductID string                                      `json:"connector_product_id"`
	Variants           []CalculateAvailableQuantitiesVariantOutput `json:"variants"`
}

type CalculateAvailableQuantitiesVariantOutput struct {
	ID                          string  `json:"id"`
	ExternalID                  string  `json:"external_id"`
	CalculatedAvailableQuantity float64 `json:"calculated_available_quantity"`
	HitThreshold                bool    `json:"hit_threshold"`
	Context                     string  `json:"context"`
	Status                      string  `json:"status"`
	ErrorMessage                string  `json:"error_message"`
}

func (c *CalculateAvailableQuantitiesVariantOutput) WithMaxQuantity() {
	if c == nil {
		return
	}
	c.CalculatedAvailableQuantity = float64(consts.TikTokMaxInventory)
	c.Context = consts.QuantityFromAllowBackorder
	c.Status = consts.CalculateSuccess
	c.ErrorMessage = ""
}

func (c *CalculateAvailableQuantitiesVariantOutput) WithHitThreshold() {
	if c == nil {
		return
	}
	c.CalculatedAvailableQuantity = 0
	c.Context = consts.QuantityFromHitThreshold
	c.Status = consts.CalculateSuccess
	c.ErrorMessage = ""
}

func (c *CalculateAvailableQuantitiesVariantOutput) FixNegativeQuantity() {
	if c == nil {
		return
	}

	if c.CalculatedAvailableQuantity < 0 {
		c.CalculatedAvailableQuantity = 0
		c.Context = consts.QuantityFromFixNegative
		c.Status = consts.CalculateSuccess
		c.ErrorMessage = ""
	}
}

type quantityPair struct {
	AvailableQuantity float64
	AllowBackorder    bool
	QuantityFrom      string
}

func (input *CalculateAvailableQuantitiesInput) gatherExternalInventoryItemIDs() []string {
	inventoryItemIDs := make([]string, 0)
	for _, variant := range input.ProductsCenterProduct.Variants {
		if variant.ExternalInventoryItemID != "" {
			inventoryItemIDs = append(inventoryItemIDs, variant.ExternalInventoryItemID)
		}
	}
	return slicex.UniqueSlice(inventoryItemIDs)
}

func (input *CalculateAvailableQuantitiesInput) buildVariantsQuantityPairs(isCareVariantStatus bool) map[string]quantityPair {
	paris := make(map[string]quantityPair)
	for _, variant := range input.ProductsCenterProduct.Variants {

		if isCareVariantStatus && variant.Status == consts.ProductsCenterVariantStatusInActive {
			paris[variant.ExternalID] = quantityPair{
				AvailableQuantity: 0,
				AllowBackorder:    false,
				QuantityFrom:      consts.QuantityFromInactiveVariant,
			}
			continue // 下架variant清空库存
		}

		paris[variant.ExternalID] = quantityPair{
			AvailableQuantity: variant.AvailableQuantity,
			AllowBackorder:    variant.AllowBackorder,
			QuantityFrom:      consts.QuantityFromInputVariant,
		}
	}
	return paris
}

func (input *CalculateAvailableQuantitiesInput) buildQuantityPairFromInventoryLevel(inventorySync *models.InventorySync,
	connectorInventoryLevels []connector_sdk_v2_inventory_level.ModelsResponseInventoryLevel2) map[string]quantityPair {
	enableMultiWarehouseIDs := inventorySync.GatherEnableMultiWarehouseIDs()

	/**
	库存统计算法：
	1、一个 variant 的发货地可能分布在多个不同的 warehouse_id
	2、只统计 warehouse_id 属于 multi warehouse 配置中的库存
	3、举例：inventory_item 有3个 inventory_level,库存分别是 10，20，30，其中 multi warehouse 只配置第一个、第二个仓库
		因此 inventory_item 的总库存为 10 + 20
	*/
	// quick lookup external_inventory_item's sum quantity, key: external_inventory_item_id -> sum quantity
	inventoryItems2Quantity := make(map[string]float64)
	// nolint:gocritic
	for _, level := range connectorInventoryLevels {
		externalInventoryID := level.ExternalInventoryItemID.String()
		quantity := level.AvailableQuantity.Int()

		// 不在配置的多仓库内
		if !enableMultiWarehouseIDs.Contains(level.ExternalWarehouseID.String()) {
			continue
		}
		inventoryItems2Quantity[externalInventoryID] += float64(quantity)
	}

	paris := make(map[string]quantityPair)
	for _, variant := range input.ProductsCenterProduct.Variants {
		var quantity float64 = 0
		if inventoryQuantity, ok := inventoryItems2Quantity[variant.ExternalInventoryItemID]; ok {
			quantity = inventoryQuantity
		}
		paris[variant.ExternalID] = quantityPair{
			AvailableQuantity: quantity,
			AllowBackorder:    variant.AllowBackorder,
			QuantityFrom:      consts.QuantityFromInventoryLevel,
		}
	}
	return paris
}

func (input *CalculateAvailableQuantitiesInput) buildOutput(inventorySync *models.InventorySync,
	quantityParis map[string]quantityPair) *CalculateAvailableQuantitiesOutput {
	variants := make([]CalculateAvailableQuantitiesVariantOutput, 0, len(input.ProductsCenterProduct.Variants))
	for _, variant := range input.ProductsCenterProduct.Variants {
		result := CalculateAvailableQuantitiesVariantOutput{
			ID:         variant.ID,
			ExternalID: variant.ExternalID,
			Status:     consts.CalculateSuccess,
		}
		quantity, ok := quantityParis[variant.ExternalID]
		if !ok {
			result.Status = consts.CalculateFailed
			result.ErrorMessage = "inventory level not found"
			variants = append(variants, result)
			continue
		}
		calculatedQuantity, hitThreshold := toolbox.CalculateAvailableQuantity(quantity.AvailableQuantity, inventorySync)
		result.CalculatedAvailableQuantity = calculatedQuantity
		result.HitThreshold = hitThreshold
		result.Context = quantity.QuantityFrom
		variants = append(variants, result)
	}

	return &CalculateAvailableQuantitiesOutput{
		InventorySync: inventorySync,
		ProductsCenterProduct: &ProductsCenterProductOut{
			ID:                 input.ProductsCenterProduct.ID,
			ConnectorProductID: input.ProductsCenterProduct.ConnectorProductID,
			Variants:           variants,
		},
	}
}

func (input *CalculateAvailableQuantitiesInput) wrapOutputWithTikTokSpecialCases(
	output *CalculateAvailableQuantitiesOutput, isCareVariantStatus bool) {
	// build quick lookup map
	// key is external_id
	variantsOutputMap := make(map[string]*CalculateAvailableQuantitiesVariantOutput)
	for i := range output.ProductsCenterProduct.Variants {
		variantsOutputMap[output.ProductsCenterProduct.Variants[i].ExternalID] = &output.ProductsCenterProduct.Variants[i]
	}
	newCalculatedVariantsOutput := make([]CalculateAvailableQuantitiesVariantOutput, 0, len(output.ProductsCenterProduct.Variants))
	for _, variant := range input.ProductsCenterProduct.Variants {
		variantAllowBackorder := variant.AllowBackorder
		variantStatus := variant.Status

		/* variant启用超卖, 应将库存设置最大值 */
		// 1. 配置启用超卖
		allowBackorderToMaxQuantity := input.InventorySync.FollowSourceAllowBackorderEnabled()
		// 2. variant 可超卖
		allowBackorderToMaxQuantity = allowBackorderToMaxQuantity && variantAllowBackorder
		// 3. variant 非下架状态
		if isCareVariantStatus {
			allowBackorderToMaxQuantity = allowBackorderToMaxQuantity && variantStatus != consts.ProductsCenterVariantStatusInActive
		}

		calculatedVariantOutput, ok := variantsOutputMap[variant.ExternalID]
		if !ok {
			continue
		}
		if calculatedVariantOutput.Status == consts.CalculateFailed {
			newCalculatedVariantsOutput = append(newCalculatedVariantsOutput, *calculatedVariantOutput)
			continue
		}

		// first set with max quantity
		if allowBackorderToMaxQuantity {
			calculatedVariantOutput.WithMaxQuantity()
		} else if calculatedVariantOutput.HitThreshold {
			// not enable allow backorder, set with hit threshold
			calculatedVariantOutput.WithHitThreshold()
		}

		// fix negative quantity at last
		calculatedVariantOutput.FixNegativeQuantity()
		newCalculatedVariantsOutput = append(newCalculatedVariantsOutput, *calculatedVariantOutput)
	}
	output.ProductsCenterProduct.Variants = newCalculatedVariantsOutput
}

func (output *CalculateAvailableQuantitiesOutput) GetAvailableQuantity(productsCenterVariantID string) (float64, error) {
	for _, variant := range output.ProductsCenterProduct.Variants {
		if variant.ID == productsCenterVariantID {
			if variant.Status != consts.CalculateSuccess {
				return 0, errors.Wrap(ErrCalculateInventoryFailed, variant.ErrorMessage)
			}

			return variant.CalculatedAvailableQuantity, nil
		}
	}
	return 0, ErrInventoryNotFound
}
