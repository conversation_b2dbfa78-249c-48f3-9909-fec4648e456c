package settings

import (
	"context"

	"github.com/stretchr/testify/mock"
)

type MockSettingService struct {
	mock.Mock
}

func (m *MockSettingService) GetByID(ctx context.Context, id string) (*Setting, error) {
	ret := m.Called(ctx, id)
	return ret.Get(0).(*Setting), ret.Error(1)
}

func (m *MockSettingService) Create(ctx context.Context, input *Setting) (*Setting, error) {
	ret := m.Called(ctx, input)
	return ret.Get(0).(*Setting), ret.Error(1)
}

func (m *MockSettingService) Update(ctx context.Context, id string, input *Setting) (*Setting, error) {
	ret := m.Called(ctx, id, input)
	return ret.Get(0).(*Setting), ret.Error(1)
}

func (m *MockSettingService) List(ctx context.Context, input *SearchSettingArgs) ([]*Setting, error) {
	ret := m.Called(ctx, input)
	return ret.Get(0).([]*Setting), ret.Error(1)
}
