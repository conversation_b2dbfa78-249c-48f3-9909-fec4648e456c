package settings

import (
	"context"

	"github.com/go-playground/validator/v10"

	"github.com/AfterShip/gopkg/storage/spannerx"
)

type Service interface {
	GetByID(ctx context.Context, id string) (*Setting, error)
	Create(ctx context.Context, input *Setting) (*Setting, error)
	Update(ctx context.Context, id string, input *Setting) (*Setting, error)
	List(ctx context.Context, args *SearchSettingArgs) ([]*Setting, error)
}

type serviceImpl struct {
	repo     settingRepo
	validate *validator.Validate
}

func NewService(cli *spannerx.Client) Service {
	return &serviceImpl{
		repo:     NewSettingRepo(cli),
		validate: validator.New(),
	}
}
