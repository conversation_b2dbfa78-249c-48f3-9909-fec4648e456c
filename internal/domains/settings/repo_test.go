package settings

import (
	"context"
	"encoding/json"
	"os"
	"testing"

	"github.com/spf13/viper"
	"github.com/stretchr/testify/require"

	"github.com/AfterShip/gopkg/cfg"
	"github.com/AfterShip/pltf-pd-product-listings/internal/config"
	"github.com/AfterShip/pltf-pd-product-listings/internal/datastore"
)

func loadConfig(t *testing.T) {
	configs := new(config.Config)
	_, err := cfg.LoadViperConfig(configs, func(v *viper.Viper) { v.AddConfigPath("../../../cmd/apiserver/conf") })
	require.NoError(t, err)
	configs.DynamicConfigs.ElasticsearchAuth = &config.ElasticsearchAuthConfig{
		Host: "http://localhost:9200",
	}
	require.NoError(t, datastore.Init(configs))
}

func generateSettingInput() (*settingDBModel, error) {
	// 构建需要的数据
	bytes, err := os.ReadFile("./testdata/setting_model.json")
	if err != nil {
		return nil, err
	}

	input := new(settingDBModel)
	return input, json.Unmarshal(bytes, input)
}

func Test_CreateSuccess(t *testing.T) {
	loadConfig(t)
	repo := NewSettingRepo(datastore.Get().SpannerCli)
	require.NotNil(t, repo)

	// load input
	input, err := generateSettingInput()
	require.NoError(t, err)

	// sql 里增加了创建 unique key ，需要先删再创建
	searchResult, err := repo.list(context.Background(), &SearchSettingArgs{
		OrganizationID:       input.OrganizationID,
		SourceAppPlatform:    input.SourceAppPlatform,
		SourceAppKey:         input.SourceAppKey,
		SalesChannelPlatform: input.SalesChannelPlatform,
		SalesChannelStoreKey: input.SalesChannelStoreKey,
	})
	require.NoError(t, err)

	for i := range searchResult {
		settingID := searchResult[i].SettingID
		err := repo.forceDelete(context.Background(), settingID)
		require.NoError(t, err)
	}

	err = repo.create(context.Background(), input)
	require.NoError(t, err)

	// check by id
	id := input.SettingID

	output, err := repo.getByID(context.Background(), id)
	require.NoError(t, err)
	require.NotNil(t, output)
}

func Test_Update(t *testing.T) {
	loadConfig(t)
	repo := NewSettingRepo(datastore.Get().SpannerCli)
	require.NotNil(t, repo)

	// load input
	input, err := generateSettingInput()
	require.NoError(t, err)

	searchResult, err := repo.list(context.Background(), &SearchSettingArgs{
		OrganizationID:       input.OrganizationID,
		SourceAppPlatform:    input.SourceAppPlatform,
		SourceAppKey:         input.SourceAppKey,
		SalesChannelPlatform: input.SalesChannelPlatform,
		SalesChannelStoreKey: input.SalesChannelStoreKey,
	})
	require.NoError(t, err)

	if len(searchResult) > 0 {
		updateInput := input
		id := searchResult[0].SettingID
		updateInput.SettingID = id

		input.DefaultBrand.ID = "22"
		err := repo.update(context.Background(), updateInput)
		require.NoError(t, err)

		// check if update success
		output, err := repo.getByID(context.Background(), id)
		require.NoError(t, err)
		require.NotNil(t, output)
		require.Equal(t, "22", output.DefaultBrand.ID)
	}

}
