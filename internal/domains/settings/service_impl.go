package settings

import (
	"context"

	"github.com/pkg/errors"
)

func (s *serviceImpl) GetByID(ctx context.Context, id string) (*Setting, error) {
	model, err := s.repo.getByID(ctx, id)
	if err != nil {
		return nil, err
	}

	return model.convertToSetting()
}

// Create TODO 从 testing 合并到 master 的时候 lint 报错，先临时 nolint，避免改动到逻辑
// nolint: funlen,gocyclo
func (s *serviceImpl) Create(ctx context.Context, input *Setting) (*Setting, error) {
	if err := s.validate.Struct(input); err != nil {
		return nil, errors.WithStack(err)
	}

	if err := input.customValidator(); err != nil {
		return nil, errors.WithStack(err)
	}

	settings, err := s.repo.list(ctx, &SearchSettingArgs{
		OrganizationID: input.Organization.ID,
	})
	if err != nil {
		return nil, err
	}

	for i := range settings {
		if settings[i].SourceAppPlatform == input.Source.App.Platform &&
			settings[i].SourceAppKey == input.Source.App.Key &&
			settings[i].SalesChannelPlatform == input.SalesChannel.Platform &&
			settings[i].SalesChannelStoreKey == input.SalesChannel.StoreKey {
			return nil, ErrSettingConflict
		}
	}

	model := convertSettingInputToDBModel(input)
	err = model.AssignLastEffectedAt(nil)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	err = s.repo.create(ctx, model)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	newModel, err := s.repo.getByID(ctx, model.SettingID)
	if err != nil {
		return nil, err
	}

	return newModel.convertToSetting()
}

// Update TODO 从 testing 合并到 master 的时候 lint 报错，先临时 nolint，避免改动到逻辑
// nolint: funlen,gocyclo
func (s *serviceImpl) Update(ctx context.Context, id string, input *Setting) (*Setting, error) {
	if err := s.validate.Struct(input); err != nil {
		return nil, errors.WithStack(err)
	}

	if err := input.customValidator(); err != nil {
		return nil, errors.WithStack(errors.WithStack(err))
	}

	oldSetting, err := s.repo.getByID(ctx, id)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	updateModel := convertSettingInputToDBModel(input)
	err = updateModel.AssignLastEffectedAt(oldSetting)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	//columns should not be changed,overwrite with history data
	updateModel.SettingID = oldSetting.SettingID
	updateModel.OrganizationID = oldSetting.OrganizationID
	updateModel.SalesChannelPlatform = oldSetting.SalesChannelPlatform
	updateModel.SalesChannelStoreKey = oldSetting.SalesChannelStoreKey
	updateModel.SourceAppPlatform = oldSetting.SourceAppPlatform
	updateModel.SourceAppKey = oldSetting.SourceAppKey
	updateModel.CreatedAt = oldSetting.CreatedAt

	err = s.repo.update(ctx, updateModel)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	// get new data
	model, err := s.repo.getByID(ctx, id)
	if err != nil {
		return nil, err
	}
	return model.convertToSetting()
}

func (s *serviceImpl) List(ctx context.Context, args *SearchSettingArgs) ([]*Setting, error) {
	if err := s.validate.Struct(args); err != nil {
		return nil, errors.WithStack(err)
	}

	Settings, err := s.repo.list(ctx, args)
	if err != nil {
		return nil, err
	}

	outputs := make([]*Setting, 0)
	for i := range Settings {
		output, err := Settings[i].convertToSetting()
		if err != nil {
			return nil, err
		}
		outputs = append(outputs, output)
	}
	return outputs, nil
}
