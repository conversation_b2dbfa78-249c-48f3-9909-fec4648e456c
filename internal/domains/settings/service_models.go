package settings

import (
	"time"

	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
)

type Setting struct {
	ID                     string                        `json:"id"`
	SalesChannel           models.SalesChannel           `json:"sales_channel"`
	Organization           models.Organization           `json:"organization"`
	Source                 models.Source                 `json:"source"`
	DefaultBrand           models.Brand                  `json:"default_brand"`
	DefaultCompliance      models.Compliance             `json:"default_compliance"`
	InventorySync          models.InventorySync          `json:"inventory_sync"`
	PriceSync              models.PriceSync              `json:"price_sync"`
	ProductSync            models.ProductSync            `json:"product_sync"`
	AutoLink               models.AutoLink               `json:"auto_link"`
	DimensionsMapping      models.DimensionsMapping      `json:"dimensions_mapping"`
	MarketsCurrencyMapping models.MarketsCurrencyMapping `json:"markets_currency_mapping"`
	ProductAI              models.ProductAI              `json:"product_ai"`
	CreatedAt              time.Time                     `json:"created_at"`
	UpdatedAt              time.Time                     `json:"updated_at"`
}

func (s *Setting) customValidator() error {
	// 5元组数据引用的公共包，单独校验
	if s.Organization.ID == "" ||
		s.Source.App.Platform == "" ||
		s.Source.App.Key == "" ||
		s.SalesChannel.Platform == "" ||
		s.SalesChannel.StoreKey == "" {
		return models.ErrMissRequiredBodyParam
	}
	return nil
}

type Settings []*Setting

func (s Settings) LookUpPriceSync(inputPriceSync *models.PriceSync) (*models.PriceSync, error) {
	if inputPriceSync != nil {
		return inputPriceSync, nil
	}

	if len(s) == 0 || &s[0].PriceSync == nil {
		return nil, ErrSettingSyncPriceNotFound
	}

	return &s[0].PriceSync, nil
}

func (s Settings) LookUpShopifyMarketsMapping(appPlatform string) *models.MarketsCurrencyMapping {
	var mapping *models.MarketsCurrencyMapping
	if len(s) > 0 {
		mapping = &s[0].MarketsCurrencyMapping
	}

	if mapping.Currency == "" || mapping.CountryRegion == "" {
		return nil
	}

	return mapping
}
