package settings

import (
	"context"
	"encoding/json"
	"os"
	"testing"

	"github.com/stretchr/testify/require"

	"github.com/AfterShip/pltf-pd-product-listings/internal/datastore"
)

func Test_SettingServiceImpl_NewService(t *testing.T) {
	loadConfig(t)
	service := NewService(datastore.Get().SpannerCli)
	require.NotNil(t, service)
}

func generateInput() (*Setting, error) {
	// 构建需要的数据
	bytes, err := os.ReadFile("./testdata/create_setting.json")
	if err != nil {
		return nil, err
	}

	input := new(Setting)

	return input, json.Unmarshal(bytes, input)
}

func Test_Settings(t *testing.T) {
	loadConfig(t)
	service := NewService(datastore.Get().SpannerCli)
	require.NotNil(t, service)

	// load input
	input, err := generateInput()
	require.NoError(t, err)

	// test search
	searchResult, err := service.List(context.Background(), &SearchSettingArgs{
		OrganizationID:       input.Organization.ID,
		SourceAppPlatform:    input.Source.App.Platform,
		SourceAppKey:         input.Source.App.Key,
		SalesChannelPlatform: input.SalesChannel.Platform,
		SalesChannelStoreKey: input.SalesChannel.StoreKey,
	})
	require.NoError(t, err)
	sum := len(searchResult)
	if sum == 0 {
		// test create
		result, err := service.Create(context.Background(), input)
		require.NoError(t, err)
		require.NotNil(t, result)
	} else {
		// test get，去最新的数据
		lastResult := searchResult[sum-1]
		id := lastResult.ID
		result, err := service.GetByID(context.Background(), id)
		require.NoError(t, err)
		require.NotNil(t, result)

		// test try to update brand_id
		updateInput := lastResult
		updateInput.DefaultBrand.ID = "22"
		updateResult, err := service.Update(context.Background(), id, updateInput)
		require.NoError(t, err)
		require.NotNil(t, result)
		require.Equal(t, "22", updateResult.DefaultBrand.ID)
	}

}
