package settings

import (
	"bytes"
	"encoding/json"
	"time"

	"github.com/tidwall/sjson"

	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
)

type settingDBModel struct {
	SettingID              string                        `spanner:"setting_id" json:"setting_id" `
	OrganizationID         string                        `spanner:"organization_id" json:"organization_id"`
	SourceAppPlatform      string                        `spanner:"source_app_platform" json:"source_app_platform"`
	SourceAppKey           string                        `spanner:"source_app_key" json:"source_app_key"`
	SalesChannelPlatform   string                        `spanner:"sales_channel_platform" json:"sales_channel_platform"`
	SalesChannelStoreKey   string                        `spanner:"sales_channel_store_key" json:"sales_channel_store_key"`
	DefaultBrand           models.Brand                  `spanner:"default_brand" json:"default_brand"`
	DefaultCompliance      models.Compliance             `spanner:"default_compliance" json:"default_compliance"`
	InventorySync          models.InventorySync          `spanner:"inventory_sync" json:"inventory_sync"`
	PriceSync              models.PriceSync              `spanner:"price_sync" json:"price_sync"`
	ProductSync            models.ProductSync            `spanner:"product_sync" json:"product_sync"`
	AutoLink               models.AutoLink               `spanner:"auto_link" json:"auto_link"`
	DimensionsMapping      models.DimensionsMapping      `spanner:"dimensions_mapping" json:"dimensions_mapping"`
	MarketsCurrencyMapping models.MarketsCurrencyMapping `spanner:"markets_currency_mapping" json:"markets_currency_mapping"`
	ProductAI              models.ProductAI              `spanner:"product_ai" json:"product_ai"`
	CreatedAt              time.Time                     `spanner:"created_at" json:"created_at"`
	UpdatedAt              time.Time                     `spanner:"updated_at" json:"updated_at"`
}

func (model *settingDBModel) AssignLastEffectedAt(lastDBModel *settingDBModel) error {
	now := time.Now().In(time.UTC)
	if lastDBModel == nil {
		model.InventorySync.LastEffectAt = now
		model.ProductSync.LastEffectAt = now
		return nil
	}

	// 数据更新场景
	inventorySyncChanged, err := model.inventorySyncChanged(lastDBModel)
	if err != nil {
		return err
	}
	productSyncChanged, err := model.productSyncChanged(lastDBModel)
	if err != nil {
		return err
	}
	if inventorySyncChanged {
		model.InventorySync.LastEffectAt = now
	} else {
		// assign with history data
		model.InventorySync.LastEffectAt = lastDBModel.InventorySync.LastEffectAt
	}

	if productSyncChanged {
		model.ProductSync.LastEffectAt = now
	} else {
		// assign with history data
		model.ProductSync.LastEffectAt = lastDBModel.ProductSync.LastEffectAt
	}
	return nil
}

func (model *settingDBModel) inventorySyncChanged(lastDBModel *settingDBModel) (bool, error) {
	newBytes, err := json.Marshal(model.InventorySync)
	if err != nil {
		return false, err
	}
	newBytes, err = sjson.DeleteBytes(newBytes, "last_effect_at")
	if err != nil {
		return false, err
	}

	lastBytes, err := json.Marshal(lastDBModel.InventorySync)
	if err != nil {
		return false, err
	}
	lastBytes, err = sjson.DeleteBytes(lastBytes, "last_effect_at")
	if err != nil {
		return false, err
	}
	return !bytes.Equal(newBytes, lastBytes), nil
}

func (model *settingDBModel) productSyncChanged(lastDBModel *settingDBModel) (bool, error) {
	newBytes, err := json.Marshal(model.ProductSync)
	if err != nil {
		return false, err
	}
	newBytes, err = sjson.DeleteBytes(newBytes, "last_effect_at")
	if err != nil {
		return false, err
	}

	lastBytes, err := json.Marshal(lastDBModel.ProductSync)
	if err != nil {
		return false, err
	}
	lastBytes, err = sjson.DeleteBytes(lastBytes, "last_effect_at")
	if err != nil {
		return false, err
	}
	return !bytes.Equal(newBytes, lastBytes), nil
}
