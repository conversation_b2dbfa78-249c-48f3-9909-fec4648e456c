package settings

import (
	"fmt"

	"github.com/AfterShip/pltf-pd-product-listings/internal/consts"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
)

func (model *settingDBModel) convertToSetting() (*Setting, error) {
	// spanner 新增字段,默认值填充逻辑
	model.fillDefaultValue()

	output := &Setting{
		ID: model.SettingID,
		Organization: models.Organization{
			ID: model.OrganizationID,
		},
		Source: models.Source{
			App: models.App{
				Platform: model.SourceAppPlatform,
				Key:      model.SourceAppKey,
			},
		},
		SalesChannel: models.SalesChannel{
			Platform: model.SalesChannelPlatform,
			StoreKey: model.SalesChannelStoreKey,
		},
		DefaultBrand:           model.DefaultBrand,
		DefaultCompliance:      model.DefaultCompliance,
		InventorySync:          model.InventorySync,
		PriceSync:              model.PriceSync,
		ProductSync:            model.ProductSync,
		AutoLink:               model.AutoLink,
		DimensionsMapping:      model.DimensionsMapping,
		MarketsCurrencyMapping: model.MarketsCurrencyMapping,
		ProductAI:              model.ProductAI,
		CreatedAt:              model.CreatedAt,
		UpdatedAt:              model.UpdatedAt,
	}

	// add default value for PriceSync.SyncCompareAtPrice
	if output.PriceSync.SyncCompareAtPrice == "" {
		output.PriceSync.SyncCompareAtPrice = consts.StateDisabled
	}

	return output, nil
}

func convertSettingInputToDBModel(input *Setting) *settingDBModel {
	model := settingDBModel{}
	model.SettingID = input.ID
	model.OrganizationID = input.Organization.ID
	model.SalesChannelPlatform = input.SalesChannel.Platform
	model.SalesChannelStoreKey = input.SalesChannel.StoreKey
	model.SourceAppPlatform = input.Source.App.Platform
	model.SourceAppKey = input.Source.App.Key
	model.DefaultBrand = input.DefaultBrand
	model.DefaultCompliance = input.DefaultCompliance
	model.InventorySync = input.InventorySync
	model.PriceSync = input.PriceSync
	model.ProductSync = input.ProductSync
	model.AutoLink = input.AutoLink
	model.DimensionsMapping = input.DimensionsMapping
	model.MarketsCurrencyMapping = input.MarketsCurrencyMapping
	model.ProductAI = input.ProductAI

	return &model
}

func (model *settingDBModel) fillDefaultValue() {
	if model == nil {
		return
	}
	model.fillDefaultFollowSourceAllowBackorder()
	model.fillDefaultProductAI()
}

func (model *settingDBModel) fillDefaultFollowSourceAllowBackorder() {
	// 超卖允许配置默认值为 disabled
	if model.InventorySync.FollowSourceAllowBackorder == "" {
		model.InventorySync.FollowSourceAllowBackorder = string(consts.AllowSyncDisabled)
	}
}

func (model *settingDBModel) fillDefaultProductAI() {
	// 默认值为 enabled
	if model.ProductAI.OptimizationInfo.State == "" {
		model.ProductAI.OptimizationInfo.State = consts.StateEnabled
	}
}

func (cf *settingDBModel) DecodeSpanner(val interface{}) (err error) {
	strVal, ok := val.(string)
	if !ok {
		return fmt.Errorf("failed to decode customField: %v", val)
	}
	_ = strVal
	return nil
}
