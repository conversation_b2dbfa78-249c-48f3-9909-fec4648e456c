package settings

import (
	"context"

	"cloud.google.com/go/spanner"

	"github.com/AfterShip/gopkg/storage/spannerx"
	"github.com/AfterShip/gopkg/storage/spannerx/sqlbuilder"
	"github.com/AfterShip/gopkg/uuid"
	spanner_util "github.com/AfterShip/pltf-pd-product-listings/internal/utils/spannerx"
)

const (
	tableSettings         = "settings"
	spannerFieldSettingID = "setting_id"
	tableFieldCreatedAt   = "created_at"
)

func (model *settingDBModel) SpannerTable() string {
	return tableSettings
}

type settingRepo interface {
	getByID(ctx context.Context, id string) (*settingDBModel, error)
	create(ctx context.Context, model *settingDBModel) error
	update(ctx context.Context, model *settingDBModel) error
	list(ctx context.Context, args *SearchSettingArgs) ([]*settingDBModel, error)
	forceDelete(ctx context.Context, id string) error
}

type SearchSettingArgs struct {
	OrganizationID       string `json:"organization_id" validate:"required"`
	SalesChannelPlatform string `json:"sales_channel_platform"`
	SalesChannelStoreKey string `json:"sales_channel_store_key"`
	SourceAppKey         string `json:"source_app_key"`
	SourceAppPlatform    string `json:"source_app_platform"`
}

type settingRepoImpl struct {
	cli *spannerx.Client
}

func NewSettingRepo(cli *spannerx.Client) settingRepo {
	return &settingRepoImpl{cli: cli}
}

func (impl *settingRepoImpl) getByID(ctx context.Context, id string) (*settingDBModel, error) {
	model := new(settingDBModel)
	cols, err := spanner_util.ParseColumns(model)
	if err != nil {
		return nil, err
	}

	txn := impl.cli.Single()
	defer txn.Close()

	row, err := txn.ReadRow(ctx, model.SpannerTable(), spanner.Key{id}, cols)
	if err != nil {
		if spannerx.IsNotFoundErr(err) {
			return nil, ErrSettingNotFound
		}
		return nil, err
	}

	return model, row.ToStruct(model)
}

func (impl *settingRepoImpl) create(ctx context.Context, model *settingDBModel) error {
	if model.SettingID == "" {
		model.SettingID = uuid.GenerateUUIDV4()
	}
	model.CreatedAt = spanner.CommitTimestamp
	model.UpdatedAt = spanner.CommitTimestamp
	m, err := spanner.InsertStruct(tableSettings, model)
	if err != nil {
		return err
	}

	mutations := make([]*spanner.Mutation, 0)
	mutations = append(mutations, m)

	// apply mutations
	_, err = impl.cli.Apply(ctx, mutations)
	if err != nil && spanner_util.IsAlreadyExists(err) {
		return ErrSettingConflict
	}

	return err
}

func (impl *settingRepoImpl) update(ctx context.Context, model *settingDBModel) error {
	model.UpdatedAt = spanner.CommitTimestamp

	m, err := spannerx.UpdateStruct(tableSettings, model)
	if err != nil {
		return err
	}

	mutations := make([]*spanner.Mutation, 0)
	mutations = append(mutations, m)

	// apply mutations
	_, err = impl.cli.Apply(ctx, mutations)

	return err
}

func (impl *settingRepoImpl) list(ctx context.Context, args *SearchSettingArgs) ([]*settingDBModel, error) {
	query := sqlbuilder.Model(&settingDBModel{})
	queryParams := map[string]interface{}{}
	if args.OrganizationID != "" {
		query = query.Where(sqlbuilder.Eq("organization_id", "@organization_id"))
		queryParams["organization_id"] = args.OrganizationID
	}

	if args.SourceAppPlatform != "" {
		query = query.Where(sqlbuilder.Eq("source_app_platform", "@source_app_platform"))
		queryParams["source_app_platform"] = args.SourceAppPlatform
	}

	if args.SourceAppKey != "" {
		query = query.Where(sqlbuilder.Eq("source_app_key", "@source_app_key"))
		queryParams["source_app_key"] = args.SourceAppKey
	}

	if args.SalesChannelStoreKey != "" {
		query = query.Where(sqlbuilder.Eq("sales_channel_store_key", "@sales_channel_store_key"))
		queryParams["sales_channel_store_key"] = args.SalesChannelStoreKey
	}

	if args.SalesChannelPlatform != "" {
		query = query.Where(sqlbuilder.Eq("sales_channel_platform", "@sales_channel_platform"))
		queryParams["sales_channel_platform"] = args.SalesChannelPlatform
	}
	sql := query.
		OrderAsc(tableFieldCreatedAt).
		MustToSQL()

	stmt := spanner.Statement{
		SQL:    sql,
		Params: queryParams,
	}

	// do query
	txn := impl.cli.Single()
	defer txn.Close()

	result := make([]*settingDBModel, 0)
	err := txn.Query(ctx, stmt).Do(func(r *spanner.Row) error {
		var model settingDBModel
		defer func() { result = append(result, &model) }()
		return r.ToStruct(&model)
	})
	return result, err
}

func (r *settingRepoImpl) forceDelete(ctx context.Context, id string) error {
	mut := spanner.Delete(tableSettings, spanner.Key{id})
	_, err := r.cli.Apply(ctx, []*spanner.Mutation{mut})
	return err
}
