package organization_settings

import "github.com/AfterShip/pltf-pd-product-listings/internal/models"

func (model *organizationSettingDBModel) convertToOrganizationSetting() (*OrganizationSetting, error) {
	output := &OrganizationSetting{
		ID: model.OrganizationSettingID,
		Organization: models.Organization{
			ID: model.OrganizationID,
		},
		CurrencyConvertors: model.CurrencyConvertors,
		CreatedAt:          model.CreatedAt,
		UpdatedAt:          model.UpdatedAt,
	}

	return output, nil
}

func convertOrganizationSettingInputToDBModel(input *OrganizationSetting) *organizationSettingDBModel {
	model := organizationSettingDBModel{}
	model.OrganizationSettingID = input.ID
	model.OrganizationID = input.Organization.ID
	model.CurrencyConvertors = input.CurrencyConvertors

	return &model
}
