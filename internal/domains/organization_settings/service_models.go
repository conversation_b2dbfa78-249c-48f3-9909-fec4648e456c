package organization_settings

import (
	"time"

	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
)

type OrganizationSetting struct {
	ID                 string                     `json:"id"`
	Organization       models.Organization        `json:"organization"`
	CurrencyConvertors []models.CurrencyConvertor `json:"currency_convertors"`
	CreatedAt          time.Time                  `json:"created_at"`
	UpdatedAt          time.Time                  `json:"updated_at"`
}

func (o *OrganizationSetting) LookUpCurrencyConvertor(sourceCurrency, salesChannelCurrency string) *models.CurrencyConvertor {
	for _, currencyConvertor := range o.CurrencyConvertors {
		if currencyConvertor.SourceCurrency == sourceCurrency && currencyConvertor.SalesChannelCurrency == salesChannelCurrency {
			return &currencyConvertor
		}
	}
	return nil
}
