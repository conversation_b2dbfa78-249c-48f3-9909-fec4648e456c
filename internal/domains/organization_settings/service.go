package organization_settings

import (
	"context"

	"github.com/go-playground/validator/v10"

	"github.com/AfterShip/gopkg/storage/spannerx"
)

type Service interface {
	GetByID(ctx context.Context, id string) (*OrganizationSetting, error)
	Create(ctx context.Context, input *OrganizationSetting) (*OrganizationSetting, error)
	Update(ctx context.Context, id string, input *OrganizationSetting) (*OrganizationSetting, error)
	List(ctx context.Context, args *SearchOrganizationSettingArgs) ([]*OrganizationSetting, error)
}

type serviceImpl struct {
	repo     organizationSettingRepo
	validate *validator.Validate
}

func NewService(cli *spannerx.Client) Service {
	return &serviceImpl{
		repo:     NewOrganizationSettingRepo(cli),
		validate: validator.New(),
	}
}
