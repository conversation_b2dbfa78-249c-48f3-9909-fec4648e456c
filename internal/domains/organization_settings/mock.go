package organization_settings

import (
	"context"

	"github.com/stretchr/testify/mock"
)

type MockOrganizationSettingService struct {
	mock.Mock
}

func (m *MockOrganizationSettingService) GetByID(ctx context.Context, id string) (*OrganizationSetting, error) {
	ret := m.Called(ctx, id)
	return ret.Get(0).(*OrganizationSetting), ret.Error(1)
}

func (m *MockOrganizationSettingService) Create(ctx context.Context, input *OrganizationSetting) (*OrganizationSetting, error) {
	ret := m.Called(ctx, input)
	return ret.Get(0).(*OrganizationSetting), ret.Error(1)
}

func (m *MockOrganizationSettingService) Update(ctx context.Context, id string, input *OrganizationSetting) (*OrganizationSetting, error) {
	ret := m.Called(ctx, id, input)
	return ret.Get(0).(*OrganizationSetting), ret.Error(1)
}

func (m *MockOrganizationSettingService) List(ctx context.Context, input *SearchOrganizationSettingArgs) ([]*OrganizationSetting, error) {
	ret := m.Called(ctx, input)
	return ret.Get(0).([]*OrganizationSetting), ret.Error(1)
}
