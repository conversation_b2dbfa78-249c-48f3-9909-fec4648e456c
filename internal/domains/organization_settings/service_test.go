package organization_settings

import (
	"context"
	"encoding/json"
	"os"
	"testing"

	"github.com/stretchr/testify/require"

	"github.com/AfterShip/pltf-pd-product-listings/internal/datastore"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
)

func Test_SettingServiceImpl_NewService(t *testing.T) {
	loadConfig(t)
	service := NewService(datastore.Get().SpannerCli)
	require.NotNil(t, service)
}

func generateInput() (*OrganizationSetting, error) {
	// 构建需要的数据
	bytes, err := os.ReadFile("./testdata/create_organization_setting.json")
	if err != nil {
		return nil, err
	}

	input := new(OrganizationSetting)

	return input, json.Unmarshal(bytes, input)
}

func Test_OrganizationSettings(t *testing.T) {
	loadConfig(t)
	service := NewService(datastore.Get().SpannerCli)
	require.NotNil(t, service)

	// load input
	input, err := generateInput()
	require.NoError(t, err)

	// test search
	searchResult, err := service.List(context.Background(), &SearchOrganizationSettingArgs{
		OrganizationID: input.Organization.ID,
	})
	require.NoError(t, err)
	sum := len(searchResult)
	if sum == 0 {
		// test create
		result, err := service.Create(context.Background(), input)
		require.NoError(t, err)
		require.NotNil(t, result)
	} else {
		// test get，去最新的数据
		lastResult := searchResult[sum-1]
		id := lastResult.ID
		result, err := service.GetByID(context.Background(), id)
		require.NoError(t, err)
		require.NotNil(t, result)

		// test try to update brand_id
		updateInput := lastResult
		converters := []models.CurrencyConvertor{
			{
				CustomExchangeRate:   1.1,
				SourceCurrency:       "USD",
				SalesChannelCurrency: "HKD",
			},
		}
		updateInput.CurrencyConvertors = converters
		updateResult, err := service.Update(context.Background(), id, updateInput)
		require.NoError(t, err)
		require.NotNil(t, result)
		require.Equal(t, updateInput.CurrencyConvertors, updateResult.CurrencyConvertors)
	}

}
