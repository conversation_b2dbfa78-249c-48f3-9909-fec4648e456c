package organization_settings

import (
	"context"

	"github.com/pkg/errors"
)

func (s *serviceImpl) GetByID(ctx context.Context, id string) (*OrganizationSetting, error) {
	model, err := s.repo.getByID(ctx, id)
	if err != nil {
		return nil, err
	}

	return model.convertToOrganizationSetting()
}

// Create TODO 从 testing 合并到 master 的时候 lint 报错，先临时 nolint，避免改动到逻辑
// nolint: funlen,gocyclo
func (s *serviceImpl) Create(ctx context.Context, input *OrganizationSetting) (*OrganizationSetting, error) {
	if err := s.validate.Struct(input); err != nil {
		return nil, errors.WithStack(err)
	}

	organizationSettings, err := s.repo.list(ctx, &SearchOrganizationSettingArgs{
		OrganizationID: input.Organization.ID,
	})
	if err != nil {
		return nil, err
	}

	if len(organizationSettings) > 0 {
		return nil, ErrOrganizationSettingConflict
	}

	model := convertOrganizationSettingInputToDBModel(input)
	err = model.AssignLastEffectedAt(nil)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	err = s.repo.create(ctx, model)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	newModel, err := s.repo.getByID(ctx, model.OrganizationSettingID)
	if err != nil {
		return nil, err
	}

	return newModel.convertToOrganizationSetting()
}

// Update TODO 从 testing 合并到 master 的时候 lint 报错，先临时 nolint，避免改动到逻辑
// nolint: funlen,gocyclo
func (s *serviceImpl) Update(ctx context.Context, id string, input *OrganizationSetting) (*OrganizationSetting, error) {
	if err := s.validate.Struct(input); err != nil {
		return nil, errors.WithStack(err)
	}

	oldSetting, err := s.repo.getByID(ctx, id)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	updateModel := convertOrganizationSettingInputToDBModel(input)
	err = updateModel.AssignLastEffectedAt(oldSetting)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	//columns should not be changed,overwrite with history data
	updateModel.OrganizationSettingID = oldSetting.OrganizationSettingID
	updateModel.OrganizationID = oldSetting.OrganizationID
	updateModel.CreatedAt = oldSetting.CreatedAt

	err = s.repo.update(ctx, updateModel)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	// get new data
	model, err := s.repo.getByID(ctx, id)
	if err != nil {
		return nil, err
	}
	return model.convertToOrganizationSetting()
}

func (s *serviceImpl) List(ctx context.Context, args *SearchOrganizationSettingArgs) ([]*OrganizationSetting, error) {
	if err := s.validate.Struct(args); err != nil {
		return nil, errors.WithStack(err)
	}

	settings, err := s.repo.list(ctx, args)
	if err != nil {
		return nil, err
	}

	outputs := make([]*OrganizationSetting, 0)
	for i := range settings {
		output, err := settings[i].convertToOrganizationSetting()
		if err != nil {
			return nil, err
		}
		outputs = append(outputs, output)
	}
	return outputs, nil
}
