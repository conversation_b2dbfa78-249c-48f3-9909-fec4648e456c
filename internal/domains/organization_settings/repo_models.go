package organization_settings

import (
	"time"

	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
)

type organizationSettingDBModel struct {
	OrganizationSettingID string                    `spanner:"organization_setting_id" json:"organization_setting_id" `
	OrganizationID        string                    `spanner:"organization_id" json:"organization_id"`
	CurrencyConvertors    models.CurrencyConvertors `spanner:"currency_convertors" json:"currency_convertors"`
	CreatedAt             time.Time                 `spanner:"created_at" json:"created_at"`
	UpdatedAt             time.Time                 `spanner:"updated_at" json:"updated_at"`
}

func (model *organizationSettingDBModel) AssignLastEffectedAt(lastDBModel *organizationSettingDBModel) error {
	now := time.Now().In(time.UTC)
	if lastDBModel == nil {
		for i := range model.CurrencyConvertors {
			model.CurrencyConvertors[i].LastEffectAt = now
		}
		return nil
	}

	// 数据更新场景
	return model.assignCurrencyConvertorsEffectedAtIfChanged(lastDBModel)
}

func (model *organizationSettingDBModel) assignCurrencyConvertorsEffectedAtIfChanged(lastDBModel *organizationSettingDBModel) error {
	now := time.Now().In(time.UTC)
	historyCurrencies := make(map[string]models.CurrencyConvertor)
	for _, convertor := range lastDBModel.CurrencyConvertors {
		index := convertor.GenerateCacheKey()
		historyCurrencies[index] = convertor
	}

	// attention: currency convertor can be deleted by biz service

	// fully trust the endpoint request payload and use it to cover historical data after assign last effected time
	commonConvertors := make([]models.CurrencyConvertor, 0)
	for _, curConvertor := range model.CurrencyConvertors {
		index := curConvertor.GenerateCacheKey()
		historyConvertor, ok := historyCurrencies[index]
		if ok {
			// history currency changed custom rate
			if historyConvertor.CustomExchangeRate != curConvertor.CustomExchangeRate {
				historyConvertor.CustomExchangeRate = curConvertor.CustomExchangeRate
				historyConvertor.LastEffectAt = now
			}
			commonConvertors = append(commonConvertors, historyConvertor)
		} else {
			// added new currency
			curConvertor.LastEffectAt = now
			commonConvertors = append(commonConvertors, curConvertor)
		}
	}
	model.CurrencyConvertors = commonConvertors
	return nil
}
