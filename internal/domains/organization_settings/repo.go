package organization_settings

import (
	"context"

	"cloud.google.com/go/spanner"

	"github.com/AfterShip/gopkg/storage/spannerx"
	"github.com/AfterShip/gopkg/storage/spannerx/sqlbuilder"
	"github.com/AfterShip/gopkg/uuid"
	spanner_util "github.com/AfterShip/pltf-pd-product-listings/internal/utils/spannerx"
)

const (
	tableOrganizationSettings = "organization_settings"
	spannerFieldSettingID     = "setting_id"
	tableFieldCreatedAt       = "created_at"
)

func (model *organizationSettingDBModel) SpannerTable() string {
	return tableOrganizationSettings
}

type organizationSettingRepo interface {
	getByID(ctx context.Context, id string) (*organizationSettingDBModel, error)
	create(ctx context.Context, model *organizationSettingDBModel) error
	update(ctx context.Context, model *organizationSettingDBModel) error
	list(ctx context.Context, args *SearchOrganizationSettingArgs) ([]*organizationSettingDBModel, error)
	forceDelete(ctx context.Context, id string) error
}

type SearchOrganizationSettingArgs struct {
	OrganizationID string `json:"organization_id" validate:"required"`
}

type organizationSettingRepoImpl struct {
	cli *spannerx.Client
}

func NewOrganizationSettingRepo(cli *spannerx.Client) organizationSettingRepo {
	return &organizationSettingRepoImpl{cli: cli}
}

func (impl *organizationSettingRepoImpl) getByID(ctx context.Context, id string) (*organizationSettingDBModel, error) {
	model := new(organizationSettingDBModel)
	cols, err := spanner_util.ParseColumns(model)
	if err != nil {
		return nil, err
	}

	txn := impl.cli.Single()
	defer txn.Close()

	row, err := txn.ReadRow(ctx, model.SpannerTable(), spanner.Key{id}, cols)
	if err != nil {
		if spannerx.IsNotFoundErr(err) {
			return nil, ErrOrganizationSettingNotFound
		}
		return nil, err
	}

	return model, row.ToStruct(model)
}

func (impl *organizationSettingRepoImpl) create(ctx context.Context, model *organizationSettingDBModel) error {
	if model.OrganizationSettingID == "" {
		model.OrganizationSettingID = uuid.GenerateUUIDV4()
	}
	model.CreatedAt = spanner.CommitTimestamp
	model.UpdatedAt = spanner.CommitTimestamp
	m, err := spanner.InsertStruct(tableOrganizationSettings, model)
	if err != nil {
		return err
	}

	mutations := make([]*spanner.Mutation, 0)
	mutations = append(mutations, m)

	// apply mutations
	_, err = impl.cli.Apply(ctx, mutations)
	if err != nil {
		if spanner_util.IsAlreadyExists(err) {
			return ErrOrganizationSettingConflict
		}
		return err
	}
	return nil
}

func (impl *organizationSettingRepoImpl) update(ctx context.Context, model *organizationSettingDBModel) error {
	model.UpdatedAt = spanner.CommitTimestamp

	m, err := spannerx.UpdateStruct(tableOrganizationSettings, model)
	if err != nil {
		return err
	}

	mutations := make([]*spanner.Mutation, 0)
	mutations = append(mutations, m)

	// apply mutations
	_, err = impl.cli.Apply(ctx, mutations)

	return err
}

func (impl *organizationSettingRepoImpl) list(ctx context.Context, args *SearchOrganizationSettingArgs) ([]*organizationSettingDBModel, error) {
	query := sqlbuilder.Model(&organizationSettingDBModel{}).Where(sqlbuilder.Eq("organization_id", "@organization_id"))
	queryParams := map[string]interface{}{
		"organization_id": args.OrganizationID,
	}
	sql := query.
		OrderAsc(tableFieldCreatedAt).
		MustToSQL()

	stmt := spanner.Statement{
		SQL:    sql,
		Params: queryParams,
	}

	// do query
	txn := impl.cli.Single()
	defer txn.Close()

	result := make([]*organizationSettingDBModel, 0)
	err := txn.Query(ctx, stmt).Do(func(r *spanner.Row) error {
		var model organizationSettingDBModel
		defer func() { result = append(result, &model) }()
		return r.ToStruct(&model)
	})
	return result, err
}

func (r *organizationSettingRepoImpl) forceDelete(ctx context.Context, id string) error {
	mut := spanner.Delete(tableOrganizationSettings, spanner.Key{id})
	_, err := r.cli.Apply(ctx, []*spanner.Mutation{mut})
	return err
}
