package organization_settings

import (
	"context"
	"encoding/json"
	"os"
	"testing"

	"github.com/spf13/viper"
	"github.com/stretchr/testify/require"

	"github.com/AfterShip/gopkg/cfg"
	"github.com/AfterShip/gopkg/uuid"
	"github.com/AfterShip/pltf-pd-product-listings/internal/config"
	"github.com/AfterShip/pltf-pd-product-listings/internal/datastore"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
)

func loadConfig(t *testing.T) {
	configs := new(config.Config)
	_, err := cfg.LoadViperConfig(configs, func(v *viper.Viper) { v.AddConfigPath("../../../cmd/apiserver/conf") })
	require.NoError(t, err)
	configs.DynamicConfigs.ElasticsearchAuth = &config.ElasticsearchAuthConfig{
		Host: "http://localhost:9200",
	}
	require.NoError(t, datastore.Init(configs))
}

func generateOrganizationSettingInput() (*organizationSettingDBModel, error) {
	// 构建需要的数据
	bytes, err := os.ReadFile("./testdata/organization_setting_model.json")
	if err != nil {
		return nil, err
	}

	input := new(organizationSettingDBModel)
	return input, json.Unmarshal(bytes, input)
}

func Test_CreateSuccess(t *testing.T) {
	loadConfig(t)
	repo := NewOrganizationSettingRepo(datastore.Get().SpannerCli)
	require.NotNil(t, repo)

	// load input
	input, err := generateOrganizationSettingInput()
	require.NoError(t, err)

	// sql 里增加了创建 unique key ，需要先删再创建
	searchResult, err := repo.list(context.Background(), &SearchOrganizationSettingArgs{
		OrganizationID: input.OrganizationID,
	})
	require.NoError(t, err)

	for i := range searchResult {
		settingID := searchResult[i].OrganizationSettingID
		err := repo.forceDelete(context.Background(), settingID)
		require.NoError(t, err)
	}

	err = repo.create(context.Background(), input)
	require.NoError(t, err)

	// check by id
	id := input.OrganizationSettingID

	output, err := repo.getByID(context.Background(), id)
	require.NoError(t, err)
	require.NotNil(t, output)
}

func Test_Update(t *testing.T) {
	loadConfig(t)
	repo := NewOrganizationSettingRepo(datastore.Get().SpannerCli)
	require.NotNil(t, repo)

	// load input
	input, err := generateOrganizationSettingInput()
	require.NoError(t, err)

	searchResult, err := repo.list(context.Background(), &SearchOrganizationSettingArgs{
		OrganizationID: input.OrganizationID,
	})
	require.NoError(t, err)

	if len(searchResult) > 0 {
		updateInput := input
		id := searchResult[0].OrganizationSettingID
		updateInput.OrganizationSettingID = id

		converters := []models.CurrencyConvertor{
			{
				CustomExchangeRate:   1.1,
				SourceCurrency:       "USD",
				SalesChannelCurrency: "HKD",
			},
		}
		input.CurrencyConvertors = converters
		err := repo.update(context.Background(), updateInput)
		require.NoError(t, err)

		// check if update success
		output, err := repo.getByID(context.Background(), id)
		require.NoError(t, err)
		require.NotNil(t, output)
		require.Equal(t, input.CurrencyConvertors, output.CurrencyConvertors)
	}

}

func Test_forceDelete(t *testing.T) {
	loadConfig(t)
	repo := NewOrganizationSettingRepo(datastore.Get().SpannerCli)
	require.NotNil(t, repo)

	// 创建一个测试记录
	input, err := generateOrganizationSettingInput()
	require.NoError(t, err)

	// 确保使用唯一ID
	input.OrganizationSettingID = uuid.GenerateUUIDV4()
	input.OrganizationID = uuid.GenerateUUIDV4()

	// 创建记录
	err = repo.create(context.Background(), input)
	require.NoError(t, err)

	// 验证记录已创建
	createdSetting, err := repo.getByID(context.Background(), input.OrganizationSettingID)
	require.NoError(t, err)
	require.NotNil(t, createdSetting)

	// 执行删除操作
	err = repo.forceDelete(context.Background(), input.OrganizationSettingID)
	require.NoError(t, err)

	// 验证记录已被删除
	_, err = repo.getByID(context.Background(), input.OrganizationSettingID)
	require.Error(t, err)
	require.Equal(t, ErrOrganizationSettingNotFound, err)

	// 测试删除不存在的记录
	nonExistentID := uuid.GenerateUUIDV4()
	err = repo.forceDelete(context.Background(), nonExistentID)
	require.NoError(t, err, "删除不存在的记录应该不返回错误")
}
