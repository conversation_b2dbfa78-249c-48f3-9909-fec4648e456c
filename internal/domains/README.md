## Group service(or domain) implements in this dir with the below hierarchy:
```
internal
 ├── domains
 │		├── product
 │	    │		├── models.go
 │		│		├── xx_service.go
 │		│		└── xx_repository.go
 │		├── order
 │		│		├── models.go
 │		│		├── xx_service.go
 │		│		└── xx_repository.go
 |      └── ...
 └── ...
```
### domains
> 该目录负责封装各个域的业务逻辑
> 各个域内的逻辑需要收敛, 每个域只关注自己所需要的，尽量不要与外部有过多的耦合(model, 其他域的逻辑)

#### Sub-domains
> 各领域的子域可以通过子目录来划分
* `models.go`: 定于域内所需的相关模型，定义的原则是，对域内尽可能按需去定义，尽量不要过度设计。对外也是一样，只暴露自己能提供的。
* `xx_repotory.go`: 主要是用来定义存储相关的实现。比如：Spanner\ES\Redis ..
* `xx_service.go`: service 主要是用于实现更具体的业务逻辑，需要对外提供 Domain 的相关能力，比如：商品域的商品导入、刊登、更新等。
* `<others>`: 除了上述标准的模块外，如果有必要，也可以定义其他的文件，原则是能降低项目的维护、理解成本。