package task

import (
	"context"

	"github.com/stretchr/testify/mock"

	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
)

type MockTaskService struct {
	mock.Mock
}

func (m *MockTaskService) Create(ctx context.Context, args *models.TaskArgs) (models.Task, error) {
	ret := m.Called(ctx, args)
	return ret.Get(0).(models.Task), ret.Error(1)
}

func (m *MockTaskService) List(ctx context.Context, args *ListArgs) ([]models.Task, models.Paginator, error) {
	ret := m.Called(ctx, args)
	return ret.Get(0).([]models.Task), ret.Get(1).(models.Paginator), ret.Error(2)
}

func (m *MockTaskService) GetByID(ctx context.Context, id string) (models.Task, error) {
	ret := m.Called(ctx, id)
	return ret.Get(0).(models.Task), ret.Error(1)
}

func (m *MockTaskService) Cancel(ctx context.Context, id string) (models.Task, error) {
	ret := m.Called(ctx, id)
	return ret.Get(0).(models.Task), ret.Error(1)
}
