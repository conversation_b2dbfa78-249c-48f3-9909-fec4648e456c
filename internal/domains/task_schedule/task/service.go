package task

import (
	"context"

	"github.com/AfterShip/connectors-library/sdks/jobs/jobs_svc"
	"github.com/AfterShip/gopkg/log"

	"github.com/AfterShip/pltf-pd-product-listings/internal/config"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
)

type Service interface {
	Create(ctx context.Context, args *models.TaskArgs) (models.Task, error)
	List(ctx context.Context, args *ListArgs) ([]models.Task, models.Paginator, error)
	GetByID(ctx context.Context, id string) (models.Task, error)
	Cancel(ctx context.Context, id string) (models.Task, error)
}

type serviceImpl struct {
	logger     *log.Logger
	cfg        *config.Config
	jobService *jobs_svc.JobSvc
}

func NewService(logger *log.Logger, cfg *config.Config, jobService *jobs_svc.JobSvc) *serviceImpl {
	return &serviceImpl{
		logger:     logger,
		cfg:        cfg,
		jobService: jobService,
	}
}
