package task

import (
	"github.com/AfterShip/connectors-library/sdks/jobs/jobs_svc"
	"github.com/AfterShip/pltf-pd-product-listings/internal/consts"
)

type ListArgs struct {
	GroupNames     []consts.TaskGroupName
	JobIDs         string
	ConcurrencyKey string
	OrganizationID string
	StoreKey       string
	Platform       string
	ResourceID     string
	Status         string
	Page           int64
	Limit          int64
	Type           string
}

func (args *ListArgs) toJobsQueryParams() jobs_svc.GetJobsQueryParams {
	return jobs_svc.GetJobsQueryParams{
		JobIDs:              args.JobIDs,
		ReferenceModule:     args.OrganizationID,
		ReferenceSubmodule:  buildSubmodule(args.Platform, args.StoreKey),
		ReferenceResourceID: args.ResourceID,
		ConcurrencyKey:      args.ConcurrencyKey,
		Status:              args.Status,
		Page:                args.Page,
		Limit:               args.Limit,
		Type:                args.Type,
	}
}
