package task

import (
	"context"
	"errors"
	"strings"

	"github.com/AfterShip/connectors-library/sdks/jobs/jobs_svc"

	"github.com/AfterShip/pltf-pd-product-listings/internal/consts"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
	"github.com/AfterShip/pltf-pd-product-listings/internal/utils/slicex"
)

func (s *serviceImpl) List(ctx context.Context, args *ListArgs) ([]models.Task, models.Paginator, error) {
	apiArgs := args.toJobsQueryParams()
	apiArgs.GroupIDs = strings.Join(s.getGroupIDsByGroupNames(args.GroupNames), ",")

	response, err := s.jobService.ListJobs(ctx, &apiArgs)
	if err != nil {
		return nil, models.Paginator{}, err
	}
	data := response.Data.Jobs
	tasks := make([]models.Task, 0, len(data))

	for i := range data {
		taskGroup, _ := s.getGroupNameByGroupID(data[i].Metadata.GroupID)
		task := convertToTask(&data[i], taskGroup)
		if data[i].Metadata.Type == consts.BatchTaskType {
			childTasks, err := s.getChildTask(ctx, &data[i])
			if err != nil {
				return nil, models.Paginator{}, err
			}
			task.ChildTasks = childTasks
		}
		tasks = append(tasks, task)
	}

	return tasks, convertToPaginator(response), nil
}

func (s *serviceImpl) Create(ctx context.Context, args *models.TaskArgs) (models.Task, error) {
	groupID, err := s.getGroupIDByGroupName(args.GroupName)
	if err != nil {
		return models.Task{}, err
	}

	upsertArgs, err := convertToCreateJobRequest(args)
	if err != nil {
		return models.Task{}, err
	}

	upsertArgs.Metadata.GroupID = groupID
	job, err := s.jobService.CreateJob(ctx, &upsertArgs)
	if err != nil {
		return models.Task{}, err
	}
	return s.GetByID(ctx, job.Data.ID)
}

func (s *serviceImpl) GetByID(ctx context.Context, id string) (models.Task, error) {
	job, err := s.jobService.GetJob(ctx, id)
	if err != nil {
		return models.Task{}, err
	}
	taskGroup, _ := s.getGroupNameByGroupID(job.Data.Metadata.GroupID)
	task := convertToTask(job.Data, taskGroup)

	if job.Data.Metadata.Type == consts.BatchTaskType {
		childTasks, err := s.getChildTask(ctx, job.Data)
		if err != nil {
			return models.Task{}, err
		}
		task.ChildTasks = childTasks
	}
	return task, nil
}

func (s *serviceImpl) Cancel(ctx context.Context, id string) (models.Task, error) {
	job, err := s.jobService.CancelJob(ctx, id)
	if err != nil {
		return models.Task{}, err
	}
	return s.GetByID(ctx, job.Data.ID)
}

func (s *serviceImpl) getChildTask(ctx context.Context, job *jobs_svc.Job) ([]models.Task, error) {
	tasks := make([]models.Task, 0, len(job.ChildJobIDs))
	taskGroup, err := s.getGroupNameByGroupID(job.Metadata.GroupID)
	if err != nil {
		return nil, err
	}

	ids := slicex.SplitSlice(job.ChildJobIDs, 250)
	for i := range ids {
		response, err := s.jobService.ListJobs(ctx, &jobs_svc.GetJobsQueryParams{
			GroupIDs: job.Metadata.GroupID,
			Type:     consts.BatchSubTaskType,
			JobIDs:   strings.Join(ids[i], ","),
			Page:     int64(i),
			Limit:    int64(len(ids[i])),
		})
		if err != nil {
			return nil, err
		}
		for i := range response.Data.Jobs {
			tasks = append(tasks, convertToTask(&response.Data.Jobs[i], taskGroup))
		}
	}
	return tasks, nil
}

func (s *serviceImpl) getGroupIDByGroupName(groupName consts.TaskGroupName) (string, error) {
	if groupID, ok := s.cfg.JobGroups[string(groupName)]; ok {
		return groupID, nil
	}
	return "", errors.New("task group id not found")
}

func (s *serviceImpl) getGroupNameByGroupID(groupID string) (consts.TaskGroupName, error) {
	for groupName, ID := range s.cfg.JobGroups {
		if ID == groupID {
			return consts.TaskGroupName(groupName), nil
		}
	}
	return "", errors.New("task group not found")
}

func (s *serviceImpl) getGroupIDsByGroupNames(groupNames []consts.TaskGroupName) []string {
	groupIDs := make([]string, 0, len(groupNames))
	for _, groupName := range groupNames {
		groupID, _ := s.getGroupIDByGroupName(groupName)
		groupIDs = append(groupIDs, groupID)
	}
	return groupIDs
}
