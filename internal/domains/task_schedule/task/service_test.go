package task

import (
	"testing"

	"github.com/spf13/viper"
	"github.com/stretchr/testify/require"

	"github.com/AfterShip/connectors-library/sdks/jobs/jobs_svc"
	"github.com/AfterShip/gopkg/cfg"

	"github.com/AfterShip/pltf-pd-product-listings/internal/config"
	"github.com/AfterShip/pltf-pd-product-listings/internal/consts"
	"github.com/AfterShip/pltf-pd-product-listings/internal/datastore"
	"github.com/AfterShip/pltf-pd-product-listings/internal/logger"
)

func Test_NewTask(t *testing.T) {
	service := initService(t)
	require.NotNil(t, service)
}

func Test_getGroupIDByGroupName(t *testing.T) {
	service := initService(t)
	for k, v := range service.cfg.JobGroups {
		result, err := service.getGroupIDByGroupName(consts.TaskGroupName(k))
		require.Equal(t, v, result)
		require.NoError(t, err)
	}

	_, err := service.getGroupIDByGroupName("invalid")
	require.Error(t, err)
}

func Test_getGroupNameByGroupID(t *testing.T) {
	service := initService(t)
	for k, v := range service.cfg.JobGroups {
		result, err := service.getGroupNameByGroupID(v)
		require.Equal(t, result, consts.TaskGroupName(k))
		require.NoError(t, err)
	}

	_, err := service.getGroupNameByGroupID("invalid")
	require.Error(t, err)
}

func Test_getGroupIDsByGroupNames(t *testing.T) {
	service := initService(t)
	result := service.getGroupIDsByGroupNames([]consts.TaskGroupName{consts.PublishProductListings})
	require.Len(t, result, 1)
}

func initService(t *testing.T) *serviceImpl {
	configs := new(config.Config)

	_, err := cfg.LoadViperConfig(configs, func(v *viper.Viper) { v.AddConfigPath("../../../../cmd/apiserver/conf") })
	require.NoError(t, err)
	configs.DynamicConfigs.ElasticsearchAuth = &config.ElasticsearchAuthConfig{
		Host: "http://localhost:9200",
	}
	require.NoError(t, datastore.Init(configs))

	jobService := jobs_svc.NewJobSvc(datastore.Get().ClientStore.CNTJobClient)
	taskService := NewService(logger.Get(), configs, &jobService)
	return taskService
}
