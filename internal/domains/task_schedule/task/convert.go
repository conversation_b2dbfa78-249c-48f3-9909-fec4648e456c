package task

import (
	"encoding/json"
	"errors"
	"strings"

	"github.com/AfterShip/connectors-library/sdks/jobs/jobs_svc"
	"github.com/AfterShip/pltf-pd-product-listings/internal/consts"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
)

func convertToPaginator(response *jobs_svc.JobsResponse) models.Paginator {
	return models.Paginator{
		Page:        int64(response.Data.Pagination.Page),
		Limit:       int64(response.Data.Pagination.Limit),
		HasNextPage: response.Data.Pagination.HasNextPage,
	}
}
func convertToTask(job *jobs_svc.Job, groupName consts.TaskGroupName) models.Task {
	task := models.Task{
		ID:             job.ID,
		Inputs:         job.Inputs,
		ParentTaskID:   job.ParentJobID,
		ChildTaskIDs:   job.ChildJobIDs,
		PreviousTaskID: job.PreviousJobID,
		NextTaskID:     job.NextJobID,
		CreatedAt:      job.CreatedAt,
		UpdatedAt:      job.UpdatedAt,
	}
	task.Metadata = convertToTaskMetadata(&job.Metadata)
	task.Status = convertToTaskStatus(&job.Status)
	task.Organization = convertToTaskOrganization(&job.Metadata.Reference)
	task.Outputs = convertToTaskOutput(&job.Outputs)
	task.Metadata.GroupName = groupName
	subModule := strings.SplitN(job.Metadata.Reference.Submodule, "|", 2)
	if len(subModule) == 2 {
		task.Platform = subModule[0]
		task.StoreKey = subModule[1]
	}
	return task
}

func convertToTaskStatus(jobStatus *jobs_svc.Status) models.TaskStatus {
	return models.TaskStatus{
		State:        jobStatus.State,
		PendingAt:    jobStatus.PendingAt,
		RunningAt:    jobStatus.RunningAt,
		SucceededAt:  jobStatus.SucceededAt,
		CancelledAt:  jobStatus.CancelledAt,
		LastFailedAt: jobStatus.LastFailedAt,
		TerminatedAt: jobStatus.TerminatedAt,
	}
}

func convertToTaskMetadata(jobMetadata *jobs_svc.Metadata) models.TaskMetadata {
	return models.TaskMetadata{
		Type: jobMetadata.Type,
		Reference: models.TaskMetadataReference{
			Module:     jobMetadata.Reference.Module,
			Submodule:  jobMetadata.Reference.Submodule,
			ResourceID: jobMetadata.Reference.ResourceID,
		},
		Description: jobMetadata.Description,
		Concurrency: models.TaskMetadataConcurrency{
			Key: jobMetadata.Concurrency.Key,
		},
		Controller: models.TaskMetadataController{
			RetryAttempts:  jobMetadata.Controller.RetryAttempts,
			TimeoutSeconds: jobMetadata.Controller.TimeoutSeconds,
			GuaranteeJobID: jobMetadata.Controller.GuaranteeJobID,
		},
	}
}

func convertToTaskOrganization(jobMetadataReference *jobs_svc.MetadataReference) models.Organization {
	return models.Organization{
		ID: jobMetadataReference.Module,
	}
}

func convertToTaskOutput(jobOutputs *jobs_svc.Outputs) models.TaskOutputs {
	return models.TaskOutputs{
		Code:    jobOutputs.Code,
		Message: jobOutputs.Message,
		Data:    jobOutputs.Data,
	}
}

func convertToCreateJobRequest(args *models.TaskArgs) (jobs_svc.CreateJobRequest, error) {
	jobRequest := jobs_svc.CreateJobRequest{}
	switch args.Type {
	case consts.BatchTaskType:
		if err := setJobRequestChildJob(args, &jobRequest); err != nil {
			return jobRequest, err
		}
	case consts.SingleTaskType:
		if err := setJobRequestInput(args, &jobRequest); err != nil {
			return jobRequest, err
		}
	default:
		return jobRequest, errors.New("invalid task type")
	}
	setJobRequestMetadata(args, &jobRequest)

	return jobRequest, nil
}

func setJobRequestChildJob(args *models.TaskArgs, request *jobs_svc.CreateJobRequest) error {
	childJobs := make([]jobs_svc.ChildJob, 0)
	taskInputs, ok := args.Inputs.([]models.TaskInput)
	if !ok {
		return errors.New("batch task invalid inputs")
	}
	for _, input := range taskInputs {
		inputStr, err := json.Marshal(input)
		if err != nil {
			return err
		}
		childJobs = append(childJobs, jobs_svc.ChildJob{
			Inputs: string(inputStr),
		})
	}
	request.ChildJobs = childJobs
	return nil
}

func setJobRequestInput(args *models.TaskArgs, request *jobs_svc.CreateJobRequest) error {
	inputs, err := json.Marshal(args.Inputs)
	if err != nil {
		return err
	}
	request.Inputs = string(inputs)

	return nil
}

func setJobRequestMetadata(args *models.TaskArgs, request *jobs_svc.CreateJobRequest) {
	request.Metadata = jobs_svc.Metadata{
		Type: args.Type,
		Reference: jobs_svc.MetadataReference{
			Module:     args.OrganizationID,
			Submodule:  buildSubmodule(args.Platform, args.StoreKey),
			ResourceID: args.ResourceID,
		},
		Concurrency: jobs_svc.MetadataConcurrency{
			Key:   args.ConcurrencyKey,
			Limit: args.ConcurrencyLimit,
		},
		Description: args.Description,
	}
}

func buildSubmodule(platform, storeKey string) string {
	return platform + "|" + storeKey
}
