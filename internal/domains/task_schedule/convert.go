package task_schedule

import (
	"context"
	"encoding/json"

	"github.com/pkg/errors"

	"github.com/AfterShip/pltf-pd-product-listings/internal/consts"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/product_listing"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
)

func convertToTaskSchedule(ctx context.Context, task *models.Task, domainTask models.BaseTask) TaskSchedule {
	taskSchedule := TaskSchedule{
		ID:           task.ID,
		Organization: task.Organization,
		StoreKey:     task.StoreKey,
		Platform:     task.Platform,
		GroupName:    task.Metadata.GroupName,
		Type:         task.Metadata.Type,
		Inputs:       task.Inputs,
		Outputs:      convertToTaskScheduleOutputs(task, domainTask.ParseOutput(ctx, task)),
		Status:       task.Status,
		CreatedAt:    task.CreatedAt,
		UpdatedAt:    task.UpdatedAt,
	}
	inputs, _ := convertToTaskScheduleInputs(task)
	taskSchedule.Inputs = inputs
	return taskSchedule
}

// nolint:gocyclo
func convertToTaskScheduleOutputs(task *models.Task, output models.TaskOutput) *TaskScheduleOutputs {
	result := &TaskScheduleOutputs{}
	switch task.Metadata.GroupName {
	case consts.PublishPrices:
		result.PublishPricesTaskOutput = output
	case consts.PublishInventories:
		result.PublishInventoriesTaskOutput = output
	case consts.PublishProductListings, consts.PublishSingleProductListings:
		result.PublishTaskOutput = output
	case consts.DeactivateProductListings, consts.RecoverProductListings,
		consts.ActivateProductListings, consts.DeleteProductListings, consts.RemoveProductListings:
		result.StatusTaskOutput = output
	case consts.BatchEditProductListingsProductAttributes:
		result.EditAttributesTaskOutput = output
	case consts.CreateProductListingsByProductsCenterProducts, consts.CreateProductListingsByCategoryMapping:
		result.PushToChannelTaskOutput = output
	case consts.BatchInvokeProductListingJobs:
		result.BatchInvokeProductListingTaskOutput = output
	case consts.BatchPublishPrices:
		result.BatchPublishPricesTaskOutput = output
	case consts.BatchPublishInventories:
		result.BatchPublishInventoryTaskOutput = output
	case consts.BulkEditCombinedListings:
		result.BulkEditCombinedListingsTaskOutput = output
	case consts.CreateOpenCollaboration:
		result.CreateOpenCollaborationTaskOutput = output
	case consts.CreateCombinedListings:
		result.CreateCombinedListingsTaskOutput = output
	}
	return result
}

func convertToTaskScheduleInputs(task *models.Task) (string, error) {
	if task.Metadata.Type == consts.BatchTaskType {
		inputs := make([]string, 0, len(task.ChildTasks))
		for i := range task.ChildTasks {
			inputs = append(inputs, task.ChildTasks[i].Inputs)
		}
		str, err := json.Marshal(inputs)
		if err != nil {
			// todo: log error
			return "", err
		}
		return string(str), nil
	}
	return task.Inputs, nil
}

func convertToStatusTaskInput(input string) (product_listing.StatusTaskInput, error) {
	taskInput := product_listing.StatusTaskInput{}
	err := convertToTaskInput(input, &taskInput)
	return taskInput, err
}

func convertToPublishTaskInput(input string) (product_listing.PublishTaskInput, error) {
	taskInput := product_listing.PublishTaskInput{}
	err := convertToTaskInput(input, &taskInput)
	return taskInput, err
}

func convertToTaskInput(input string, taskInput interface{}) error {
	if input == "" {
		return errors.WithMessagef(ErrUnprocessableEntity, "empty task input")
	}
	if err := json.Unmarshal([]byte(input), taskInput); err != nil {
		return err
	}
	return nil
}
