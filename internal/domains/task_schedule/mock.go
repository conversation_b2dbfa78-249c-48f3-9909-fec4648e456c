package task_schedule

import (
	"context"

	"github.com/stretchr/testify/mock"

	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
)

type MockTaskScheduleService struct {
	mock.Mock
}

func (m *MockTaskScheduleService) Create(ctx context.Context, args *CreateArgs) (TaskSchedule, error) {
	ret := m.Called(ctx, args)
	return ret.Get(0).(TaskSchedule), ret.Error(1)
}

func (m *MockTaskScheduleService) List(ctx context.Context, args *ListArgs) ([]TaskSchedule, models.Paginator, error) {
	ret := m.Called(ctx, args)
	return ret.Get(0).([]TaskSchedule), ret.Get(1).(models.Paginator), ret.Error(2)
}

func (m *MockTaskScheduleService) GetByID(ctx context.Context, id string) (TaskSchedule, error) {
	ret := m.Called(ctx, id)
	return ret.Get(0).(TaskSchedule), ret.Error(1)
}

func (m *MockTaskScheduleService) Cancel(ctx context.Context, id string, args *CancelArgs) (TaskSchedule, error) {
	ret := m.Called(ctx, id, args)
	return ret.Get(0).(TaskSchedule), ret.Error(1)
}

func (m *MockTaskScheduleService) GetIDsWithinUnfinishedTask(ctx context.Context, arg *GetIDsWithinUnfinishedTaskArg) ([]string, error) {
	ret := m.Called(ctx, arg)
	return ret.Get(0).([]string), ret.Error(1)
}
