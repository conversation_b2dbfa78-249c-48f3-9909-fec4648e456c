package task_schedule

import (
	"context"
	"errors"

	"go.uber.org/zap"

	validator "github.com/go-playground/validator/v10"

	"github.com/AfterShip/connectors-library/jobs"
	cnt_lib_utils "github.com/AfterShip/connectors-library/utils"
	"github.com/AfterShip/pltf-pd-product-listings/internal/consts"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/product_listing"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/searchable_products"
	task_domain "github.com/AfterShip/pltf-pd-product-listings/internal/domains/task_schedule/task"
	"github.com/AfterShip/pltf-pd-product-listings/internal/logger"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
)

func (s *serviceImpl) List(ctx context.Context, args *ListArgs) ([]TaskSchedule, models.Paginator, error) {
	taskServiceListArgs := args.toTaskListArgs()
	taskServiceListArgs.Type = consts.BatchTaskType + "," + consts.SingleTaskType

	tasks, paginator, err := s.taskService.List(ctx, taskServiceListArgs)
	if err != nil {
		return nil, models.Paginator{}, err
	}

	taskSchedules := make([]TaskSchedule, 0, len(tasks))
	for i := range tasks {
		domainTask := s.newDomainTask(tasks[i].Metadata.GroupName)
		if domainTask == nil {
			continue
		}
		taskSchedules = append(taskSchedules, convertToTaskSchedule(ctx, &tasks[i], domainTask))
	}

	return taskSchedules, paginator, nil
}

func (s *serviceImpl) Create(ctx context.Context, args *CreateArgs) (TaskSchedule, error) {
	baseTask := s.newDomainTask(args.GroupName)
	if baseTask == nil {
		return TaskSchedule{}, errors.New("invalid task group name")
	}

	taskArgs, err := baseTask.BuildTaskArgs(ctx, initTaskInput(args))
	if err != nil {
		return TaskSchedule{}, err
	}
	// Set common task args
	taskArgs.OrganizationID = args.OrganizationID
	taskArgs.StoreKey = args.StoreKey
	taskArgs.Platform = args.Platform
	taskArgs.Description = args.Description

	taskResponse, err := s.taskService.Create(ctx, &taskArgs)
	if err != nil {
		return TaskSchedule{}, err
	}

	return convertToTaskSchedule(ctx, &taskResponse, baseTask), nil
}

func (s *serviceImpl) GetByID(ctx context.Context, id string) (TaskSchedule, error) {
	task, err := s.taskService.GetByID(ctx, id)
	if err != nil {
		return TaskSchedule{}, err
	}

	baseTask := s.newDomainTask(task.Metadata.GroupName)
	if baseTask == nil {
		return TaskSchedule{}, errors.New("invalid task group name")
	}

	return convertToTaskSchedule(ctx, &task, baseTask), nil
}

func (s *serviceImpl) Cancel(ctx context.Context, id string, args *CancelArgs) (TaskSchedule, error) {
	task, err := s.taskService.GetByID(ctx, id)
	if err != nil {
		return TaskSchedule{}, err
	}

	// Check if the task belongs to the organization
	if task.Organization.ID != args.OrganizationID {
		return TaskSchedule{}, errors.New("organization id does not match")
	}

	// Check if the task belongs to the store
	if task.StoreKey != args.StoreKey || task.Platform != args.Platform {
		return TaskSchedule{}, errors.New("store info does not match")
	}

	task, err = s.taskService.Cancel(ctx, id)
	if err != nil {
		return TaskSchedule{}, err
	}

	baseTask := s.newDomainTask(task.Metadata.GroupName)
	if baseTask == nil {
		return TaskSchedule{}, errors.New("invalid task group name")
	}

	return convertToTaskSchedule(ctx, &task, baseTask), nil
}

// nolint:gocyclo
func (s *serviceImpl) GetIDsWithinUnfinishedTask(ctx context.Context, arg *GetIDsWithinUnfinishedTaskArg) ([]string, error) {
	result := make([]string, 0)
	tasks := make([]models.Task, 0)
	maxPage, maxLimit := 10, 250
	for page := 1; page <= maxPage; page++ {
		currentTasks, paginator, err := s.taskService.List(ctx, &task_domain.ListArgs{
			GroupNames:     []consts.TaskGroupName{arg.GroupName},
			OrganizationID: arg.OrganizationID,
			StoreKey:       arg.StoreKey,
			Platform:       arg.Platform,
			Status:         jobs.JobStatusPending + "," + jobs.JobStatusRunning,
			Type:           consts.BatchSubTaskType + "," + consts.SingleTaskType,
			Page:           int64(page),
			Limit:          int64(maxLimit),
		})
		if err != nil {
			return nil, err
		}

		tasks = append(tasks, currentTasks...)

		if !paginator.HasNextPage {
			break
		}
	}
	if len(tasks) == 0 {
		return result, nil
	}

	switch arg.GroupName {
	case consts.PublishProductListings:
		for i := range tasks {
			input, err := convertToPublishTaskInput(tasks[i].Inputs)
			if err != nil {
				s.logger.WarnCtx(ctx, "convertToPublishTaskInput error", zap.Error(err))
				continue
			}
			result = append(result, input.ProductListingIDs...)
		}
	case consts.DeactivateProductListings, consts.ActivateProductListings,
		consts.RecoverProductListings, consts.DeleteProductListings, consts.RemoveProductListings:
		for i := range tasks {
			input, err := convertToStatusTaskInput(tasks[i].Inputs)
			if err != nil {
				s.logger.WarnCtx(ctx, "convertToStatusTaskInput error", zap.Error(err))
				continue
			}
			result = append(result, input.ProductListingIDs...)
		}
	default:
	}

	return result, nil
}

func (s *serviceImpl) newDomainTask(groupName consts.TaskGroupName) models.BaseTask {
	switch groupName {
	case consts.PublishProductListings:
		return &product_listing.PublishTask{
			Logger:    s.logger,
			Validator: validator.New(),
		}
	case consts.PublishSingleProductListings:
		return &product_listing.SinglePublishTask{
			Logger:    s.logger,
			Validator: validator.New(),
		}
	case consts.ActivateProductListings, consts.DeactivateProductListings,
		consts.DeleteProductListings, consts.RecoverProductListings, consts.RemoveProductListings:
		return &product_listing.StatusTask{
			Logger:    s.logger,
			Validator: validator.New(),
		}
	case consts.BatchEditProductListingsProductAttributes:
		return &product_listing.EditAttributesTask{
			Logger:    s.logger,
			Validator: validator.New(),
		}
	case consts.CreateProductListingsByProductsCenterProducts, consts.CreateProductListingsByCategoryMapping:
		return &searchable_products.PushToChannelTask{
			Logger:    s.logger,
			Validator: validator.New(),
		}
	case consts.PublishInventories:
		return &product_listing.PublishInventoriesTask{
			Logger: s.logger,
		}
	case consts.PublishPrices:
		return &product_listing.PublishPricesTask{
			Logger: s.logger,
		}
	case consts.BatchPublishInventories:
		return &product_listing.BatchPublishInventoriesTask{
			Logger:    s.logger,
			Validator: validator.New(),
		}
	case consts.BatchPublishPrices:
		return &product_listing.BatchPublishPricesTask{
			Logger:    s.logger,
			Validator: validator.New(),
		}
	case consts.BatchInvokeProductListingJobs:
		return &product_listing.BatchInvokeProductListingTask{
			Logger:    s.logger,
			Validator: validator.New(),
		}
	case consts.CreateOpenCollaboration:
		return &product_listing.CreateOpenCollaborationTask{
			Logger:    s.logger,
			Validator: validator.New(),
		}
	case consts.CreateCombinedListings:
		return &CreateCombinedListingsTask{
			Logger:    s.logger,
			Validator: validator.New(),
		}
	case consts.BulkEditCombinedListings:
		return &searchable_products.BulkEditCombinedListingsTask{
			Logger:    s.logger,
			Validator: validator.New(),
		}
	default:
		return nil
	}
}

func initTaskInput(args *CreateArgs) models.TaskInput {
	switch args.GroupName {
	case consts.PublishProductListings, consts.PublishSingleProductListings:
		input := args.Inputs.PublishTaskInput
		input.OrganizationID = args.OrganizationID
		input.App = args.App
		if input.SalesChannel.Platform == "" || input.SalesChannel.StoreKey == "" {
			logger.Get().Info("publish product listings task input sales channel is empty, set to default",
				zap.String("args", cnt_lib_utils.GetJsonIndent(args)),
				zap.String("input", cnt_lib_utils.GetJsonIndent(input)))
			input.SalesChannel = models.SalesChannel{
				StoreKey: args.StoreKey,
				Platform: args.Platform,
			}
		}
		return input
	case consts.ActivateProductListings, consts.DeactivateProductListings,
		consts.DeleteProductListings, consts.RecoverProductListings, consts.RemoveProductListings:
		input := args.Inputs.StatusTaskInput
		input.OrganizationID = args.OrganizationID
		input.GroupName = args.GroupName
		return input
	case consts.BatchEditProductListingsProductAttributes:
		input := args.Inputs.EditAttributesTaskInput
		input.OrganizationID = args.OrganizationID
		input.SalesChannel = models.SalesChannel{
			StoreKey: args.StoreKey,
			Platform: args.Platform,
		}
		return input
	case consts.CreateProductListingsByProductsCenterProducts, consts.CreateProductListingsByCategoryMapping:
		input := args.Inputs.PushToChannelTaskInput
		input.OrganizationID = args.OrganizationID
		input.SourceStoreKey = args.StoreKey
		input.SourcePlatform = args.Platform
		input.GroupName = args.GroupName
		return input
	case consts.PublishInventories:
		return args.Inputs.PublishInventoriesTaskInput
	case consts.BatchPublishInventories:
		return args.Inputs.BatchPublishInventoryTaskInput
	case consts.PublishPrices:
		return args.Inputs.PublishPricesTaskInput
	case consts.BatchPublishPrices:
		return args.Inputs.BatchPublishPricesTaskInput
	case consts.BatchInvokeProductListingJobs:
		return args.Inputs.BatchInvokeProductListingTaskInput
	case consts.BulkEditCombinedListings:
		input := args.Inputs.BulkEditCombinedListingsTaskInput
		input.Organization = models.Organization{
			ID: args.OrganizationID,
		}
		input.SourceApp = models.App{
			Key:      args.StoreKey,
			Platform: args.Platform,
		}
		return input
	case consts.CreateOpenCollaboration:
		input := args.Inputs.CreateOpenCollaborationTaskInput
		input.OrganizationID = args.OrganizationID
		input.SalesChannel = models.SalesChannel{
			StoreKey: args.StoreKey,
			Platform: args.Platform,
		}
		return input
	case consts.CreateCombinedListings:
		input := args.Inputs.CreateCombinedListingsTaskInput
		input.OrganizationID = args.OrganizationID
		input.Source = models.App{
			Key:      args.StoreKey,
			Platform: args.Platform,
		}
		return input
	default:
		return nil
	}
}
