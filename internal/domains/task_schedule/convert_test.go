package task_schedule

import (
	"context"
	"encoding/json"
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/AfterShip/pltf-pd-product-listings/internal/consts"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
)

type mockBaseTask struct {
	output models.TaskOutput
}

func (m *mockBaseTask) ParseOutput(ctx context.Context, task *models.Task) models.TaskOutput {
	return m.output
}

func TestConvertToTaskScheduleInputs(t *testing.T) {
	task := &models.Task{
		Inputs:   "input1",
		Metadata: models.TaskMetadata{Type: "normal"},
	}
	inputs, err := convertToTaskScheduleInputs(task)
	assert.NoError(t, err)
	assert.Equal(t, "input1", inputs)

	task2 := &models.Task{
		Metadata: models.TaskMetadata{Type: consts.BatchTaskType},
		ChildTasks: []models.Task{
			{Inputs: "a"},
			{Inputs: "b"},
		},
	}
	inputs2, err := convertToTaskScheduleInputs(task2)
	assert.NoError(t, err)
	var arr []string
	json.Unmarshal([]byte(inputs2), &arr)
	assert.ElementsMatch(t, []string{"a", "b"}, arr)
}

func TestConvertToTaskScheduleOutputs(t *testing.T) {
	task := &models.Task{Metadata: models.TaskMetadata{GroupName: consts.PublishPrices}}
	output := TaskScheduleOutputs{}

	// PublishPrices
	res := convertToTaskScheduleOutputs(task, output)
	assert.Equal(t, output, res.PublishPricesTaskOutput)

	// PublishInventories
	task.Metadata.GroupName = consts.PublishInventories
	res = convertToTaskScheduleOutputs(task, output)
	assert.Equal(t, output, res.PublishInventoriesTaskOutput)

	// PublishProductListings
	task.Metadata.GroupName = consts.PublishProductListings
	res = convertToTaskScheduleOutputs(task, output)
	assert.Equal(t, output, res.PublishTaskOutput)

	// PublishSingleProductListings
	task.Metadata.GroupName = consts.PublishSingleProductListings
	res = convertToTaskScheduleOutputs(task, output)
	assert.Equal(t, output, res.PublishTaskOutput)

	// DeactivateProductListings
	task.Metadata.GroupName = consts.DeactivateProductListings
	res = convertToTaskScheduleOutputs(task, output)
	assert.Equal(t, output, res.StatusTaskOutput)

	// RecoverProductListings
	task.Metadata.GroupName = consts.RecoverProductListings
	res = convertToTaskScheduleOutputs(task, output)
	assert.Equal(t, output, res.StatusTaskOutput)

	// ActivateProductListings
	task.Metadata.GroupName = consts.ActivateProductListings
	res = convertToTaskScheduleOutputs(task, output)
	assert.Equal(t, output, res.StatusTaskOutput)

	// DeleteProductListings
	task.Metadata.GroupName = consts.DeleteProductListings
	res = convertToTaskScheduleOutputs(task, output)
	assert.Equal(t, output, res.StatusTaskOutput)

	// RemoveProductListings
	task.Metadata.GroupName = consts.RemoveProductListings
	res = convertToTaskScheduleOutputs(task, output)
	assert.Equal(t, output, res.StatusTaskOutput)

	// BatchEditProductListingsProductAttributes
	task.Metadata.GroupName = consts.BatchEditProductListingsProductAttributes
	res = convertToTaskScheduleOutputs(task, output)
	assert.Equal(t, output, res.EditAttributesTaskOutput)

	// CreateProductListingsByProductsCenterProducts
	task.Metadata.GroupName = consts.CreateProductListingsByProductsCenterProducts
	res = convertToTaskScheduleOutputs(task, output)
	assert.Equal(t, output, res.PushToChannelTaskOutput)

	// CreateProductListingsByCategoryMapping
	task.Metadata.GroupName = consts.CreateProductListingsByCategoryMapping
	res = convertToTaskScheduleOutputs(task, output)
	assert.Equal(t, output, res.PushToChannelTaskOutput)

	// BatchInvokeProductListingJobs
	task.Metadata.GroupName = consts.BatchInvokeProductListingJobs
	res = convertToTaskScheduleOutputs(task, output)
	assert.Equal(t, output, res.BatchInvokeProductListingTaskOutput)

	// BatchPublishPrices
	task.Metadata.GroupName = consts.BatchPublishPrices
	res = convertToTaskScheduleOutputs(task, output)
	assert.Equal(t, output, res.BatchPublishPricesTaskOutput)

	// BatchPublishInventories
	task.Metadata.GroupName = consts.BatchPublishInventories
	res = convertToTaskScheduleOutputs(task, output)
	assert.Equal(t, output, res.BatchPublishInventoryTaskOutput)

	// BulkEditCombinedListings
	task.Metadata.GroupName = consts.BulkEditCombinedListings
	res = convertToTaskScheduleOutputs(task, output)
	assert.Equal(t, output, res.BulkEditCombinedListingsTaskOutput)

	// CreateOpenCollaboration
	task.Metadata.GroupName = consts.CreateOpenCollaboration
	res = convertToTaskScheduleOutputs(task, output)
	assert.Equal(t, output, res.CreateOpenCollaborationTaskOutput)

	// CreateCombinedListings
	task.Metadata.GroupName = consts.CreateCombinedListings
	res = convertToTaskScheduleOutputs(task, output)
	assert.Equal(t, output, res.CreateCombinedListingsTaskOutput)
}

func TestConvertToTaskInput(t *testing.T) {
	type testStruct struct{ A string }
	var s testStruct
	// 空字符串
	err := convertToTaskInput("", &s)
	assert.Error(t, err)
	// 非法 json
	err = convertToTaskInput("{", &s)
	assert.Error(t, err)
	// 正常 json
	err = convertToTaskInput("{\"A\":\"a\"}", &s)
	assert.NoError(t, err)
	assert.Equal(t, "a", s.A)
}

func TestConvertToStatusTaskInput(t *testing.T) {
	input := `{"A":1}`
	res, err := convertToStatusTaskInput(input)
	assert.NoError(t, err)
	assert.NotNil(t, res)
}

func TestConvertToPublishTaskInput(t *testing.T) {
	input := `{"A":1}`
	res, err := convertToPublishTaskInput(input)
	assert.NoError(t, err)
	assert.NotNil(t, res)
}
