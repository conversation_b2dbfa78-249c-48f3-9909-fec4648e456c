package task_schedule

import (
	"time"

	"github.com/AfterShip/pltf-pd-product-listings/internal/consts"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/product_listing"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/searchable_products"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/task_schedule/task"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
)

type CreateArgs struct {
	GroupName      consts.TaskGroupName `json:"group_name" validate:"required"`
	OrganizationID string               `json:"organization_id"`
	StoreKey       string               `json:"store_key"`
	Platform       string               `json:"platform"`
	App            models.App           `json:"app"`
	Description    string               `json:"description"`
	Inputs         CreateArgsInputs     `json:"inputs" validate:"required"`
}

type CreateArgsInputs struct {
	PublishTaskInput                   *product_listing.PublishTaskInput                      `json:"publish_task"`
	StatusTaskInput                    *product_listing.StatusTaskInput                       `json:"status_task"`
	EditAttributesTaskInput            *product_listing.EditAttributesTaskInput               `json:"edit_attributes_task"`
	PushToChannelTaskInput             *searchable_products.PushToChannelTaskInput            `json:"push_to_channel_task"`
	PublishInventoriesTaskInput        *product_listing.PublishInventoriesTaskInput           `json:"publish_inventories_task"`
	PublishPricesTaskInput             *product_listing.PublishPricesTaskInput                `json:"publish_prices_task"`
	BatchInvokeProductListingTaskInput *product_listing.BatchInvokeProductListingTaskInput    `json:"batch_invoke_product_listing_task"`
	BatchPublishPricesTaskInput        *product_listing.BatchPublishPricesTaskInput           `json:"batch_publish_prices_task"`
	BatchPublishInventoryTaskInput     *product_listing.BatchPublishInventoriesTaskInput      `json:"batch_publish_inventories_task"`
	CreateOpenCollaborationTaskInput   *product_listing.CreateOpenCollaborationTaskInput      `json:"create_open_collaboration_task"`
	BulkEditCombinedListingsTaskInput  *searchable_products.BulkEditCombinedListingsTaskInput `json:"bulk_edit_combined_listings_task"`
	CreateCombinedListingsTaskInput    *CreateCombinedListingsTaskInput                       `json:"create_combined_listings_task"`
}

type TaskSchedule struct {
	ID           string               `json:"id"`
	Organization models.Organization  `json:"organization"`
	StoreKey     string               `json:"store_key"`
	Platform     string               `json:"platform"`
	GroupName    consts.TaskGroupName `json:"group_name"`
	Type         string               `json:"type"`
	Inputs       string               `json:"inputs"`
	Outputs      *TaskScheduleOutputs `json:"outputs"`
	Status       models.TaskStatus    `json:"status"`
	CreatedAt    time.Time            `json:"created_at"`
	UpdatedAt    time.Time            `json:"updated_at"`
}

type TaskScheduleOutputs struct {
	PublishTaskOutput                   models.TaskOutput `json:"publish_task"`
	StatusTaskOutput                    models.TaskOutput `json:"status_task"`
	EditAttributesTaskOutput            models.TaskOutput `json:"edit_attributes_task"`
	PushToChannelTaskOutput             models.TaskOutput `json:"push_to_channel_task"`
	PublishInventoriesTaskOutput        models.TaskOutput `json:"publish_inventories_task"`
	PublishPricesTaskOutput             models.TaskOutput `json:"publish_prices_task"`
	BatchInvokeProductListingTaskOutput models.TaskOutput `json:"batch_invoke_product_listing_task"`
	BatchPublishPricesTaskOutput        models.TaskOutput `json:"batch_publish_prices_task"`
	BatchPublishInventoryTaskOutput     models.TaskOutput `json:"batch_publish_inventories_task"`
	BulkEditCombinedListingsTaskOutput  models.TaskOutput `json:"bulk_edit_combined_listings_task"`
	CreateOpenCollaborationTaskOutput   models.TaskOutput `json:"create_open_collaboration_task"`
	CreateCombinedListingsTaskOutput    models.TaskOutput `json:"create_combined_listings_task"`
}

type ListArgs struct {
	GroupNames     []consts.TaskGroupName `validate:"required"`
	OrganizationID string                 `validate:"required"`
	StoreKey       string                 `validate:"required"`
	Platform       string                 `validate:"required"`
	ResourceID     string
	Status         string
	Page           int64 `validate:"required"`
	Limit          int64 `validate:"required"`
}

type CancelArgs struct {
	OrganizationID string `json:"organization_id" validate:"required"`
	StoreKey       string `json:"store_key" validate:"required"`
	Platform       string `json:"platform" validate:"required"`
}

func (arg *ListArgs) toTaskListArgs() *task.ListArgs {
	return &task.ListArgs{
		GroupNames:     arg.GroupNames,
		OrganizationID: arg.OrganizationID,
		StoreKey:       arg.StoreKey,
		Platform:       arg.Platform,
		ResourceID:     arg.ResourceID,
		Status:         arg.Status,
		Page:           arg.Page,
		Limit:          arg.Limit,
	}
}

type GetIDsWithinUnfinishedTaskArg struct {
	OrganizationID string
	StoreKey       string
	Platform       string
	GroupName      consts.TaskGroupName
}
