package task_schedule // nolint

import (
	"context"
	"encoding/json"

	validator "github.com/go-playground/validator/v10"
	"go.uber.org/zap"

	"github.com/AfterShip/gopkg/log"
	"github.com/AfterShip/pltf-pd-product-listings/internal/consts"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
	"github.com/AfterShip/pltf-pd-product-listings/internal/utils/slicex"
)

type CreateCombinedListingsTaskInput struct {
	OrganizationID         string     `json:"organization_id" validate:"required"`
	Source                 models.App `json:"source" validate:"required"`
	ConnectorCollectionIDs []string   `json:"connector_collection_ids" validate:"required"`
}

type CreateCombinedListingsTaskOutput struct {
	TotalCount     int `json:"total_count"`
	SucceededCount int `json:"succeeded_count"`
	FailedCount    int `json:"failed_count"`
}

type CreateCombinedListingsTask struct {
	Logger    *log.Logger
	Validator *validator.Validate
}

func (t *CreateCombinedListingsTask) validate(input *CreateCombinedListingsTaskInput) error {
	if err := t.Validator.Struct(input); err != nil {
		return err
	}

	if input.OrganizationID == "" {
		return ErrNoOrganizationID
	}

	if input.Source.Key == "" || input.Source.Platform == "" {
		return ErrNoSource
	}

	if len(input.ConnectorCollectionIDs) == 0 {
		return ErrNoConnectorCollectionID
	}

	return nil
}

func (t *CreateCombinedListingsTask) BuildTaskArgs(ctx context.Context, input models.TaskInput) (models.TaskArgs, error) {
	args, ok := input.(*CreateCombinedListingsTaskInput)
	if !ok {
		return models.TaskArgs{}, ErrUnprocessableEntity
	}

	if err := t.validate(args); err != nil {
		return models.TaskArgs{}, err
	}

	taskArgs := models.TaskArgs{
		GroupName:  consts.CreateCombinedListings,
		ResourceID: args.OrganizationID,
		StoreKey:   args.Source.Key,
		Platform:   args.Source.Platform,
		Type:       consts.BatchTaskType,
	}

	inputs := make([]models.TaskInput, 0)
	splitIDs := slicex.SplitSlice(args.ConnectorCollectionIDs, 50)
	for i := range splitIDs {
		inputs = append(inputs, &CreateCombinedListingsTaskInput{
			OrganizationID:         args.OrganizationID,
			Source:                 args.Source,
			ConnectorCollectionIDs: splitIDs[i],
		})
	}
	taskArgs.Inputs = inputs

	// If there are multiple IDs, we need to use the organization ID as the concurrency key
	if len(args.ConnectorCollectionIDs) > 1 {
		taskArgs.ConcurrencyKey = args.OrganizationID
	}

	return taskArgs, nil
}

// nolint:dupl
func (t *CreateCombinedListingsTask) ParseOutput(ctx context.Context, task *models.Task) models.TaskOutput {
	output := CreateCombinedListingsTaskOutput{}
	for i := range task.ChildTasks {
		// get total count
		if task.ChildTasks[i].Inputs != "" {
			input := &CreateCombinedListingsTaskInput{}
			if err := json.Unmarshal([]byte(task.ChildTasks[i].Inputs), input); err != nil {
				t.Logger.With(zap.String("Id", task.ChildTasks[i].ID)).WarnCtx(ctx, "Failed to parse edit attributes task input", zap.Error(err))
			}
			output.TotalCount += len(input.ConnectorCollectionIDs)
		}

		// get child task output
		if task.ChildTasks[i].Outputs.Data != "" {
			childTaskOutput := CreateCombinedListingsTaskOutput{}
			if err := json.Unmarshal([]byte(task.ChildTasks[i].Outputs.Data), &childTaskOutput); err != nil {
				t.Logger.With(zap.String("Id", task.ChildTasks[i].ID)).WarnCtx(ctx, "Failed to parse edit attributes task output", zap.Error(err))
			}
			output.FailedCount += childTaskOutput.FailedCount
			output.SucceededCount += childTaskOutput.SucceededCount
		}
	}

	return output
}
