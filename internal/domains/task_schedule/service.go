package task_schedule

import (
	"context"

	"github.com/AfterShip/gopkg/log"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"

	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/task_schedule/task"
)

type Service interface {
	Create(ctx context.Context, args *CreateArgs) (TaskSchedule, error)
	List(ctx context.Context, args *ListArgs) ([]TaskSchedule, models.Paginator, error)
	GetByID(ctx context.Context, id string) (TaskSchedule, error)
	Cancel(ctx context.Context, id string, args *CancelArgs) (TaskSchedule, error)
	GetIDsWithinUnfinishedTask(ctx context.Context, arg *GetIDsWithinUnfinishedTaskArg) ([]string, error)
}

type serviceImpl struct {
	logger      *log.Logger
	taskService task.Service
}

func NewService(logger *log.Logger, taskService task.Service) *serviceImpl {
	return &serviceImpl{
		logger:      logger,
		taskService: taskService,
	}
}
