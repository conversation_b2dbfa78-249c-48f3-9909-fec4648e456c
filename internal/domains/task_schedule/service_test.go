package task_schedule

import (
	"context"
	"errors"
	"testing"

	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"

	"github.com/AfterShip/pltf-pd-product-listings/internal/consts"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/product_listing"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/task_schedule/task"
	"github.com/AfterShip/pltf-pd-product-listings/internal/logger"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
)

func Test_TaskScheduleServiceImpl_NewService(t *testing.T) {
	service := initService(t)
	require.NotNil(t, service)
}

func Test_TaskScheduleServiceImpl_List(t *testing.T) {
	mockSvc := new(task.MockTaskService)
	test := []struct {
		name        string
		args        *ListArgs
		mock        func()
		checkResult func(t *testing.T, taskSchedules []TaskSchedule, paginator models.Paginator, err error)
	}{
		{
			name: "list tasks",
			args: &ListArgs{
				GroupNames: []consts.TaskGroupName{consts.PublishProductListings},
			},
			mock: func() {
				mockSvc.On("List", mock.Anything, mock.Anything).Return([]models.Task{
					{
						ID: "123",
						Metadata: models.TaskMetadata{
							GroupName: consts.PublishProductListings,
						},
						Outputs: models.TaskOutputs{
							Code:    "200",
							Message: "success",
							Data:    "123",
						},
						Status: models.TaskStatus{
							State: "succeeded",
						},
					},
				}, models.Paginator{}, nil).Once()
			},
			checkResult: func(t *testing.T, taskSchedules []TaskSchedule, paginator models.Paginator, err error) {
				require.NoError(t, err)
				require.NotNil(t, taskSchedules)
				require.Len(t, taskSchedules, 1)
				require.Equal(t, taskSchedules[0].ID, "123")
				require.Equal(t, taskSchedules[0].GroupName, consts.PublishProductListings)
				require.Equal(t, taskSchedules[0].Status.State, "succeeded")
				require.IsType(t, taskSchedules[0].Outputs.PublishTaskOutput, product_listing.PublishTaskOutput{})
			},
		},
		{
			name: "list tasks with task group name not found",
			args: &ListArgs{
				GroupNames: []consts.TaskGroupName{consts.PublishProductListings},
			},
			mock: func() {
				mockSvc.On("List", mock.Anything, mock.Anything).Return([]models.Task{
					{
						ID: "123",
						Metadata: models.TaskMetadata{
							GroupName: "invalid",
						},
						Outputs: models.TaskOutputs{
							Code:    "200",
							Message: "success",
							Data:    "123",
						},
						Status: models.TaskStatus{
							State: "succeeded",
						},
					},
				}, models.Paginator{}, nil).Once()
			},
			checkResult: func(t *testing.T, taskSchedules []TaskSchedule, paginator models.Paginator, err error) {
				require.NoError(t, err)
				require.NotNil(t, taskSchedules)
				require.Len(t, taskSchedules, 0)
			},
		},
		{
			name: "list tasks with error",
			args: &ListArgs{},
			mock: func() {
				mockSvc.On("List", mock.Anything, mock.Anything).Return([]models.Task{}, models.Paginator{}, errors.New("error")).Once()
			},
			checkResult: func(t *testing.T, taskSchedules []TaskSchedule, paginator models.Paginator, err error) {
				require.Error(t, err)
			},
		},
	}
	for _, tt := range test {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mock != nil {
				tt.mock()
			}
			service := NewService(logger.Get(), mockSvc)
			result, paginator, err := service.List(context.Background(), tt.args)
			tt.checkResult(t, result, paginator, err)
		})
	}
}

func Test_TaskScheduleServiceImpl_Create(t *testing.T) {
	mockSvc := new(task.MockTaskService)
	test := []struct {
		name        string
		args        *CreateArgs
		mock        func()
		checkResult func(t *testing.T, taskSchedule TaskSchedule, err error)
	}{
		{
			name: "create publish product listing task",
			args: &CreateArgs{
				GroupName:      consts.PublishProductListings,
				OrganizationID: "123",
				StoreKey:       "123",
				Platform:       "123",
				Inputs: CreateArgsInputs{
					PublishTaskInput: &product_listing.PublishTaskInput{
						ProductListingIDs: []string{"123"},
						SalesChannel: models.SalesChannel{
							StoreKey: "123",
							Platform: "123",
						},
					},
				},
			},
			mock: func() {
				mockSvc.On("Create", mock.Anything, mock.Anything, mock.Anything).Return(models.Task{
					Metadata: models.TaskMetadata{
						GroupName: consts.PublishProductListings,
					},
					Outputs: models.TaskOutputs{
						Code:    "200",
						Message: "success",
						Data:    "123",
					},
				}, nil).Once()
			},
			checkResult: func(t *testing.T, taskSchedule TaskSchedule, err error) {
				require.NoError(t, err)
				require.NotNil(t, taskSchedule)
				require.Equal(t, taskSchedule.GroupName, consts.PublishProductListings)
				require.IsType(t, taskSchedule.Outputs.PublishTaskOutput, product_listing.PublishTaskOutput{})
			},
		},
		{
			name: "create batch edit product listings product attributes task",
			args: &CreateArgs{
				GroupName:      consts.BatchEditProductListingsProductAttributes,
				OrganizationID: "123",
				StoreKey:       "123",
				Platform:       "123",
				Inputs: CreateArgsInputs{
					EditAttributesTaskInput: &product_listing.EditAttributesTaskInput{
						ProductListingIDs: []string{"123", "456"},
						Certifications: []models.ProductCertification{
							{
								SalesChannelID: "123",
								Files: []models.SalesChannelFile{
									{
										SalesChannelID: "123",
										URL:            "https://example.com/file1",
									},
								},
							},
						},
					},
				},
			},
			mock: func() {
				mockSvc.On("Create", mock.Anything, mock.Anything, mock.Anything).Return(models.Task{
					Metadata: models.TaskMetadata{
						GroupName: consts.BatchEditProductListingsProductAttributes,
					},
					Outputs: models.TaskOutputs{
						Code:    "200",
						Message: "success",
						Data:    "123",
					},
				}, nil).Once()
			},
			checkResult: func(t *testing.T, taskSchedule TaskSchedule, err error) {
				require.NoError(t, err)
				require.NotNil(t, taskSchedule)
				require.Equal(t, taskSchedule.GroupName, consts.BatchEditProductListingsProductAttributes)
				require.IsType(t, taskSchedule.Outputs.EditAttributesTaskOutput, product_listing.EditAttributesTaskOutput{})
			},
		},
		{
			name: "invalid task group name",
			args: &CreateArgs{
				GroupName: "invalid",
			},
			checkResult: func(t *testing.T, taskSchedule TaskSchedule, err error) {
				require.Error(t, err)
			},
		},
		{
			name: "task service create error",
			args: &CreateArgs{
				GroupName:      consts.PublishProductListings,
				OrganizationID: "123",
				StoreKey:       "123",
				Platform:       "123",
				Inputs: CreateArgsInputs{
					PublishTaskInput: &product_listing.PublishTaskInput{
						ProductListingIDs: []string{"123"},
						SalesChannel: models.SalesChannel{
							StoreKey: "123",
							Platform: "123",
						},
					},
				},
			},
			mock: func() {
				mockSvc.On("Create", mock.Anything, mock.Anything, mock.Anything).Return(models.Task{}, errors.New("error")).Once()
			},
			checkResult: func(t *testing.T, taskSchedule TaskSchedule, err error) {
				require.Error(t, err)
			},
		},
	}
	for _, tt := range test {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mock != nil {
				tt.mock()
			}
			service := NewService(logger.Get(), mockSvc)
			result, err := service.Create(context.Background(), tt.args)
			tt.checkResult(t, result, err)
		})
	}
}

func TestServiceImpl_newDomainTask(t *testing.T) {
	tests := []struct {
		name        string
		taskType    consts.TaskGroupName
		checkResult func(t *testing.T, task models.BaseTask)
	}{
		{
			name:     "publish product listing task",
			taskType: consts.PublishProductListings,
			checkResult: func(t *testing.T, task models.BaseTask) {
				require.NotNil(t, task)
				require.IsType(t, task, &product_listing.PublishTask{})
			},
		},
		{
			name:     "batch edit product listings product attributes task",
			taskType: consts.BatchEditProductListingsProductAttributes,
			checkResult: func(t *testing.T, task models.BaseTask) {
				require.NotNil(t, task)
				require.IsType(t, task, &product_listing.EditAttributesTask{})
			},
		},
		{
			name:     "invalid task group name",
			taskType: "invalid",
			checkResult: func(t *testing.T, task models.BaseTask) {
				require.Nil(t, task)
			},
		},
	}
	service := initService(t)
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := service.newDomainTask(tt.taskType)
			tt.checkResult(t, result)
		})
	}
}

func initService(t *testing.T) *serviceImpl {
	mockSvc := new(task.MockTaskService)
	service := NewService(logger.Get(), mockSvc)
	require.NotNil(t, service)
	return service
}

func TestInitTaskInput(t *testing.T) {
	tests := []struct {
		name     string
		args     *CreateArgs
		expected models.TaskInput
	}{
		{
			name: "PublishProductListings with empty SalesChannel",
			args: &CreateArgs{
				GroupName:      consts.PublishProductListings,
				OrganizationID: "org-123",
				StoreKey:       "store-123",
				Platform:       "platform-123",
				Inputs: CreateArgsInputs{
					PublishTaskInput: &product_listing.PublishTaskInput{},
				},
			},
			expected: &product_listing.PublishTaskInput{
				OrganizationID: "org-123",
				SalesChannel: models.SalesChannel{
					StoreKey: "store-123",
					Platform: "platform-123",
				},
			},
		},
		{
			name: "ActivateProductListings",
			args: &CreateArgs{
				GroupName:      consts.ActivateProductListings,
				OrganizationID: "org-123",
				Inputs: CreateArgsInputs{
					StatusTaskInput: &product_listing.StatusTaskInput{},
				},
			},
			expected: &product_listing.StatusTaskInput{
				OrganizationID: "org-123",
				GroupName:      consts.ActivateProductListings,
			},
		},
		{
			name: "BatchEditProductListingsProductAttributes",
			args: &CreateArgs{
				GroupName:      consts.BatchEditProductListingsProductAttributes,
				OrganizationID: "org-123",
				StoreKey:       "store-123",
				Platform:       "platform-123",
				Inputs: CreateArgsInputs{
					EditAttributesTaskInput: &product_listing.EditAttributesTaskInput{},
				},
			},
			expected: &product_listing.EditAttributesTaskInput{
				OrganizationID: "org-123",
				SalesChannel: models.SalesChannel{
					StoreKey: "store-123",
					Platform: "platform-123",
				},
			},
		},
		{
			name: "Invalid GroupName",
			args: &CreateArgs{
				GroupName: "invalid",
			},
			expected: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := initTaskInput(tt.args)
			require.Equal(t, tt.expected, result)
		})
	}
}
