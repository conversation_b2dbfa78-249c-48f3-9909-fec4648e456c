package warehouses

import (
	"github.com/AfterShip/pltf-pd-product-listings/internal/consts"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
)

type GetWarehousesArgs struct {
	OrganizationID string `form:"organization_id" binding:"required"`
	StoreKey       string `form:"store_key" binding:"required"`
	Platform       string `form:"platform" binding:"required"`
}

type Warehouses struct {
	SalesChannelID string              `json:"sales_channel_id"`
	Organization   models.Organization `json:"organization"`
	App            models.App          `json:"app"`
	Name           string              `json:"name"`
	// 仓库归属类型
	OwnershipType string `json:"ownership_type"`
	// 仓库作用的站点
	SalesSites []string `json:"sales_sites"`
}

func convertOwnershipType(warehouseType int64) string {
	switch warehouseType {
	case 1:
		return consts.WarehouseOwnershipTypeMerchant.String()
	case 2:
		return consts.WarehouseOwnershipTypeCertified.String()
	default:
		return ""
	}
}
