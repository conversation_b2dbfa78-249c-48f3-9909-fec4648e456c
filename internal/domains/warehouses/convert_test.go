package warehouses

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestWarehouseCountriesToSties(t *testing.T) {
	// 正常映射
	countries := []string{"US", "FR", "UK"}
	sites := warehouseCountriesToSties(countries)
	assert.Equal(t, 3, len(sites))
	assert.Equal(t, "shein-us", sites[0])
	assert.Equal(t, "shein-fr", sites[1])
	assert.Equal(t, "shein-uk", sites[2])

	// 不存在的国家
	countries = []string{"ZZ"}
	sites = warehouseCountriesToSties(countries)
	assert.Equal(t, 1, len(sites))
	assert.Equal(t, "", sites[0])

	// 空输入
	sites = warehouseCountriesToSties([]string{})
	assert.Equal(t, 0, len(sites))
}
