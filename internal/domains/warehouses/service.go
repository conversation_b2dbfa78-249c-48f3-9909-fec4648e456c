package warehouses

import (
	"context"

	validator "github.com/go-playground/validator/v10"

	"github.com/AfterShip/connectors-library/sdks/shein_proxy"
	"github.com/AfterShip/gopkg/log"
)

type WarehousesService interface {
	GetWarehouses(ctx context.Context, args GetWarehousesArgs) ([]Warehouses, error)
}

type serviceImpl struct {
	logger   *log.Logger
	validate *validator.Validate

	sheinAPIService shein_proxy.Service
}

func NewService(logger *log.Logger, sheinAPIService shein_proxy.Service) WarehousesService {
	return &serviceImpl{
		logger:          logger,
		validate:        validator.New(),
		sheinAPIService: sheinAPIService,
	}
}
