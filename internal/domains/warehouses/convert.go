package warehouses

import (
	"go.uber.org/zap"

	shein_rest "github.com/AfterShip/connectors-ecommerce-sdk-go/shein/rest"
	"github.com/AfterShip/pltf-pd-product-listings/internal/logger"
)

func warehouseCountriesToSties(countries []string) []string {
	result := make([]string, 0, len(countries))
	for _, country := range countries {
		site := shein_rest.CountryToSite(country)
		if country == "UK" { // GB 在 shein 测试环境的枚举是 UK, 需要特殊处理
			site = shein_rest.SiteUK
		}
		if site == "" {
			logger.Get().Info("mapping country to site failed", zap.String("country", country))
		}
		result = append(result, site)
	}
	return result
}
