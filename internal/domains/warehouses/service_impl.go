package warehouses

import (
	"context"

	"github.com/pkg/errors"

	"github.com/AfterShip/connectors-library/sdks/shein_proxy"
	"github.com/AfterShip/pltf-pd-product-listings/internal/consts"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
)

func (s *serviceImpl) GetWarehouses(ctx context.Context, args GetWarehousesArgs) ([]Warehouses, error) {

	switch args.Platform {
	case consts.Shein:
		return s.getSheinWarehouses(ctx, args)
	}

	return nil, errors.New("app platform not supported")
}

func (s *serviceImpl) getSheinWarehouses(ctx context.Context, args GetWarehousesArgs) ([]Warehouses, error) {
	language, err := s.getSheinDefaultLanguage(ctx, args.OrganizationID, args.StoreKey)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	warehouseList, err := s.sheinAPIService.GetWarehouses(ctx, &shein_proxy.GetWarehousesParams{
		CommonParams: shein_proxy.CommonParams{
			OrganizationID: args.OrganizationID,
			AppName:        consts.AppFeed,
			AppKey:         args.StoreKey,
		},
		Language: language,
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}

	result := make([]Warehouses, 0, len(warehouseList))
	for _, warehouse := range warehouseList {
		warehouses := Warehouses{
			SalesChannelID: warehouse.WarehouseCode.String(),
			Organization: models.Organization{
				ID: args.OrganizationID,
			},
			App: models.App{
				Platform: args.Platform,
				Key:      args.StoreKey,
			},
			Name:          warehouse.WarehouseName.String(),
			OwnershipType: convertOwnershipType(warehouse.WarehouseType.Int64()),
			SalesSites:    warehouseCountriesToSties(warehouse.SaleCountryList),
		}

		result = append(result, warehouses)
	}

	return result, nil
}

func (s *serviceImpl) getSheinDefaultLanguage(ctx context.Context, orgID, storeKey string) (string, error) {
	fillStandard, err := s.sheinAPIService.GetProductPublishFillStandard(ctx,
		&shein_proxy.GetProductPublishFillStandardParams{
			CommonParams: shein_proxy.CommonParams{
				OrganizationID: orgID,
				AppName:        consts.AppFeed,
				AppKey:         storeKey,
			},
		})
	if err != nil {
		return "", err
	}
	return fillStandard.DefaultLanguage.String(), nil
}
