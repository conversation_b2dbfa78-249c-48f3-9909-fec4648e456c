package convert

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestValidateFile(t *testing.T) {
	validFileFormat := mp4FileFormat
	validFileLength := 10000000
	invalidFileFormat := "txt"
	invalidFileLength := 30000000

	err := validateFile(validFileFormat, validFileLength)
	assert.Nil(t, err, "Expected no error for valid file format and length")

	err = validateFile(validFileFormat, invalidFileLength)
	assert.NotNil(t, err, "Expected error for valid file format but invalid length")

	err = validateFile(invalidFileFormat, validFileLength)
	assert.NotNil(t, err, "Expected error for invalid file format but valid length")

	err = validateFile(invalidFileFormat, invalidFileLength)
	assert.NotNil(t, err, "Expected error for invalid file format and length")
}
