package convert

import (
	"context"
	"regexp"
	"strings"

	c_regexp "github.com/mingrammer/commonregex"
	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/AfterShip/connectors-sdk-go/v2/stores"
	"github.com/AfterShip/gopkg/log"
	"github.com/AfterShip/pltf-pd-product-listings/internal/consts"
	"github.com/AfterShip/pltf-pd-product-listings/internal/utils/download_client"
	"github.com/AfterShip/pltf-pd-product-listings/internal/utils/renderer"
)

var (
	magentoMediaRegex = regexp.MustCompile(`[\w-]+.[jpgen]+`)
)

// nolint:gocyclo
func (s *serviceImpl) convert2TTSDescription(ctx context.Context, arg *DescriptionArg) (DescriptionOutput, error) {
	useShortDescription := s.isUseShortDescription(arg)
	if (arg.Description == "") || (useShortDescription && arg.Description != "") {
		arg.Description = arg.ShortDescription
	}

	var magentoSecureBaseMediaUrl string
	var isMagentoMedia bool
	var err error
	if arg.SourceStore != nil && arg.SourceStore.Platform == consts.Magento2 {
		// Get store logo url
		store, err := s.connectorsClient.GetStore(ctx, stores.GetStoresParams{
			AppKey:         arg.SourceStore.Key,
			AppPlatform:    arg.SourceStore.Platform,
			Limit:          10,
			OrganizationID: arg.Organization.ID,
			Page:           1,
		})
		if err != nil {
			return DescriptionOutput{}, err
		}
		magentoSecureBaseMediaUrl = strings.TrimSuffix(store.LogoUrl.String(), "/")

		isMagentoMedia = parseMagentoMedia(arg)
	}

	ttsDescription, err := renderer.ReplaceImageSrc(ctx, arg.Description, func(inputUrl string) (outputUrl string, err error) {
		if inputUrl == "" {
			return "", nil
		}

		if isMagentoMedia {
			imageSuffix := magentoMediaRegex.FindString(inputUrl)
			imageSuffix = strings.TrimPrefix(imageSuffix, "/")
			inputUrl = magentoSecureBaseMediaUrl + "/" + imageSuffix
		}
		if !download_client.IsURL(inputUrl) {
			return "", nil
		}
		ctx = log.AppendFieldsToContext(ctx, zap.String("inputUrl", inputUrl))
		result, err := s.ConvertImage(ctx, &ImageArg{
			Organization:   arg.Organization,
			SalesChannel:   arg.SalesChannel,
			ImageOriginURL: inputUrl,
			ImageUseCase:   ImageUseCaseDescriptionImage,
			IgnoreCache:    arg.IgnoreCache,
		})
		if err != nil {
			log.GlobalLogger().InfoCtx(ctx, "convert2TTSImage err", zap.Error(err))
			return "", err
		}
		return result.ImageURL, nil
	}, MaxTiktokShopDescriptionImageCount)

	if err != nil {
		return DescriptionOutput{}, errors.WithMessage(err, "failed to render TTS description")
	}

	filterTTSDescription := filterRedirectTrafficInformation(ttsDescription)
	if len(filterTTSDescription) != len(ttsDescription) {
		log.GlobalLogger().InfoCtx(ctx, "ttsDescription has been filtered",
			zap.Int("old_length", len(ttsDescription)),
			zap.Int("new_length", len(filterTTSDescription)))
	}

	return DescriptionOutput{
		Description: filterTTSDescription,
	}, nil
}

func (s *serviceImpl) isUseShortDescription(arg *DescriptionArg) bool {
	if s.config != nil &&
		s.config.DynamicConfigs != nil &&
		s.config.DynamicConfigs.TikTokSyncConfig != nil &&
		s.config.DynamicConfigs.TikTokSyncConfig.UseShortDescriptionOrganizationIDs != nil {
		for _, orgId := range s.config.DynamicConfigs.TikTokSyncConfig.UseShortDescriptionOrganizationIDs {
			if orgId == arg.Organization.ID {
				log.GlobalLogger().Info("use short_description when synchronizing product",
					zap.String("organization_id", arg.Organization.ID))
				return true
			}
		}
	}
	return false
}

func parseMagentoMedia(arg *DescriptionArg) bool {
	isMagentoMedia := false
	// 这种情况不知道会不会遇到，先保留
	if strings.Contains(arg.Description, `{{media url="`) {
		isMagentoMedia = true
		arg.Description = strings.ReplaceAll(arg.Description, `{{media url="`, "")
	}
	if strings.Contains(arg.Description, `{{media url=`) {
		isMagentoMedia = true
		arg.Description = strings.ReplaceAll(arg.Description, `{{media url=`, "")
	}
	if strings.Contains(arg.Description, `{media url="`) {
		isMagentoMedia = true
		arg.Description = strings.ReplaceAll(arg.Description, `{media url="`, "")
	}
	return isMagentoMedia
}

func filterRedirectTrafficInformation(description string) string {
	// 限制长度是为了避免过滤很长的内容
	// 第三方联系方式过滤函数：email
	description = c_regexp.EmailRegex.ReplaceAllStringFunc(description, func(matchStr string) string {
		// email 长度在 20 以下
		if len(matchStr) < 20 {
			return ""
		}
		return matchStr
	})
	// 第三方联系方式过滤函数：phone
	description = c_regexp.PhonesWithExtsRegex.ReplaceAllStringFunc(description, func(matchStr string) string {
		// phone 长度在 20 以下
		if len(matchStr) < 20 {
			return ""
		}
		return matchStr
	})
	// 第三方联系方式过滤函数：URL
	description = c_regexp.LinkRegex.ReplaceAllStringFunc(description, func(matchStr string) string {
		// 是 TTS 图片
		if strings.Contains(matchStr, "ibyteimg.com") || strings.HasSuffix(matchStr, ".jpeg") {
			return matchStr
		}
		if len(matchStr) > 5 && len(matchStr) < 30 && strings.HasPrefix(matchStr, "www.") {
			return ""
		}
		return matchStr
	})

	return description
}
