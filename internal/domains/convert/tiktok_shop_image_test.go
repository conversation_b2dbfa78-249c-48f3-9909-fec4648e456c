package convert

import (
	"context"
	"errors"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/AfterShip/connectors-ecommerce-sdk-go/common"
)

func TestValidateImageUseCase(t *testing.T) {
	validUseCases := []string{
		"MAIN_IMAGE",
		"ATTRIBUTE_IMAGE",
		"DESCRIPTION_IMAGE",
		"CERTIFICATION_IMAGE",
		"SIZE_CHART_IMAGE",
	}

	for _, useCase := range validUseCases {
		err := validateImageUseCase(useCase)
		assert.Nil(t, err, "Expected no error for valid use case")
	}

	invalidUseCases := []string{
		"INVALID_USE_CASE",
		"ANOTHER_INVALID_USE_CASE",
	}

	for _, useCase := range invalidUseCases {
		err := validateImageUseCase(useCase)
		assert.NotNil(t, err, "Expected error for invalid use case")
	}
}

func TestFormatTTSUploadAPIError(t *testing.T) {
	ctx := context.Background()

	// 测试 ResponseError 请求体过大
	t.Run("ResponseError with large request body", func(t *testing.T) {
		apiErr := common.ResponseError{
			Status:  400,
			Message: "Bad Request",
			ReqBody: make([]byte, 10240), // 创建请求体 >= 10240 字节
		}

		formattedErr := formatTTSUploadAPIError(ctx, apiErr)
		assert.NotNil(t, formattedErr)
		assert.Equal(t, apiErr.Status, formattedErr.(common.ResponseError).Status)
		assert.Equal(t, apiErr.Message, formattedErr.(common.ResponseError).Message)
		assert.Nil(t, formattedErr.(common.ResponseError).ReqBody, "ReqBody 应该被清空")
	})

	// 测试 ResponseError 请求体大小正常
	t.Run("ResponseError with normal request body", func(t *testing.T) {
		apiErr := common.ResponseError{
			Status:  400,
			Message: "Bad Request",
			ReqBody: make([]byte, 1000),
		}

		formattedErr := formatTTSUploadAPIError(ctx, apiErr)
		assert.NotNil(t, formattedErr)
		assert.Equal(t, apiErr.Status, formattedErr.(common.ResponseError).Status)
		assert.Equal(t, apiErr.Message, formattedErr.(common.ResponseError).Message)
		assert.Nil(t, formattedErr.(common.ResponseError).ReqBody, "ReqBody 应该被清空")
	})

	// 测试请求超时错误
	t.Run("Request timeout error", func(t *testing.T) {
		err := errors.New("Request timeout.The request to the endpoint timed out")
		formattedErr := formatTTSUploadAPIError(ctx, err)
		if !errors.Is(formattedErr, ErrorRequestUploadAPITimeout) {
			t.Fatalf("Expected error to be wrapped, got: %v", formattedErr)
		}
	})

	// 测试空白图片错误
	t.Run("Blank image error", func(t *testing.T) {
		err := errors.New("error message containing code 12038002")
		formattedErr := formatTTSUploadAPIError(ctx, err)
		if !errors.Is(formattedErr, ErrorImageBlank) {
			t.Fatalf("Expected error to be wrapped, got: %v", formattedErr)
		}
	})

	// 测试图片类型错误
	t.Run("Image type error", func(t *testing.T) {
		err := errors.New("error message containing code 12038004")
		formattedErr := formatTTSUploadAPIError(ctx, err)
		assert.Equal(t, ErrorImageType, formattedErr)
	})

	// 测试错误信息过大
	t.Run("Error message too large", func(t *testing.T) {
		err := errors.New(strings.Repeat("a", 10240))
		formattedErr := formatTTSUploadAPIError(ctx, err)
		assert.Equal(t, ErrorUnexpectedLargeError, formattedErr)
	})

	t.Run("Context canceled error", func(t *testing.T) {
		err := errors.New("context canceled")
		formattedErr := formatTTSUploadAPIError(ctx, err)
		if !errors.Is(formattedErr, ErrProxyServiceContextCancelError) {
			t.Fatalf("Expected error to be wrapped, got: %v", formattedErr)
		}
	})

	// 测试一般错误
	t.Run("General error", func(t *testing.T) {
		err := errors.New("generic error")
		formattedErr := formatTTSUploadAPIError(ctx, err)
		assert.NotNil(t, formattedErr)
		assert.True(t, errors.Is(formattedErr, err), "应该包含原始错误")
	})
}
