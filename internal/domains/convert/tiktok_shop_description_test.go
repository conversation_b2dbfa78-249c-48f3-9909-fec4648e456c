package convert

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/AfterShip/gopkg/log"
	"github.com/AfterShip/pltf-pd-product-listings/internal/config"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
)

func TestParseMagentoMedia(t *testing.T) {
	arg := &DescriptionArg{
		Description: `{{media url="image.jpg"}}`,
	}

	isMagentoMedia := parseMagentoMedia(arg)
	assert.True(t, isMagentoMedia, "Expected parseMagentoMedia to return true when Description contains Magento media URL")

	assert.Equal(t, `image.jpg"}}`, arg.Description, "Expected Description to be updated with MagentoSecureBaseMediaUrl")

	arg = &DescriptionArg{
		Description: `Hello world`,
	}

	isMagentoMedia = parseMagentoMedia(arg)
	assert.False(t, isMagentoMedia, "Expected parseMagentoMedia to return false when Description does not contain Magento media URL")
}

func TestFilterRedirectTrafficInformation(t *testing.T) {
	descriptionWithRedirect := "Visit our website at www.example.com for more information"
	expectedDescription := "Visit our website at  for more information"

	filteredDescription := filterRedirectTrafficInformation(descriptionWithRedirect)
	assert.Equal(t, expectedDescription, filteredDescription, "Expected redirect traffic information to be filtered out")

	descriptionWithoutRedirect := "Visit our store for more information"
	expectedDescription = "Visit our store for more information"

	filteredDescription = filterRedirectTrafficInformation(descriptionWithoutRedirect)
	assert.Equal(t, expectedDescription, filteredDescription, "Expected description without redirect traffic information to remain unchanged")
}

func TestServiceImpl_isUseShortDescription(t *testing.T) {
	tests := []struct {
		name             string
		config           *config.Config
		arg              *DescriptionArg
		expectedUseShort bool
	}{
		{
			name: "配置为空",
			config: &config.Config{
				DynamicConfigs: nil,
			},
			arg: &DescriptionArg{
				Organization: &models.Organization{
					ID: "org123",
				},
			},
			expectedUseShort: false,
		},
		{
			name: "TikTokSyncConfig为空",
			config: &config.Config{
				DynamicConfigs: &config.DynamicConfigs{
					TikTokSyncConfig: nil,
				},
			},
			arg: &DescriptionArg{
				Organization: &models.Organization{
					ID: "org123",
				},
			},
			expectedUseShort: false,
		},
		{
			name: "UseShortDescriptionOrganizationIDs为空",
			config: &config.Config{
				DynamicConfigs: &config.DynamicConfigs{
					TikTokSyncConfig: &config.TikTokSyncConfig{
						UseShortDescriptionOrganizationIDs: nil,
					},
				},
			},
			arg: &DescriptionArg{
				Organization: &models.Organization{
					ID: "org123",
				},
			},
			expectedUseShort: false,
		},
		{
			name: "组织ID不在配置列表中",
			config: &config.Config{
				DynamicConfigs: &config.DynamicConfigs{
					TikTokSyncConfig: &config.TikTokSyncConfig{
						UseShortDescriptionOrganizationIDs: []string{"org456", "org789"},
					},
				},
			},
			arg: &DescriptionArg{
				Organization: &models.Organization{
					ID: "org123",
				},
			},
			expectedUseShort: false,
		},
		{
			name: "组织ID在配置列表中",
			config: &config.Config{
				DynamicConfigs: &config.DynamicConfigs{
					TikTokSyncConfig: &config.TikTokSyncConfig{
						UseShortDescriptionOrganizationIDs: []string{"org123", "org456"},
					},
				},
			},
			arg: &DescriptionArg{
				Organization: &models.Organization{
					ID: "org123",
				},
			},
			expectedUseShort: true,
		},
		{
			name: "组织ID在配置列表中(列表中只有一个元素)",
			config: &config.Config{
				DynamicConfigs: &config.DynamicConfigs{
					TikTokSyncConfig: &config.TikTokSyncConfig{
						UseShortDescriptionOrganizationIDs: []string{"org123"},
					},
				},
			},
			arg: &DescriptionArg{
				Organization: &models.Organization{
					ID: "org123",
				},
			},
			expectedUseShort: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建服务实例
			s := &serviceImpl{
				config: tt.config,
				logger: log.GlobalLogger(),
			}

			// 调用被测试的方法
			result := s.isUseShortDescription(tt.arg)

			// 验证结果
			assert.Equal(t, tt.expectedUseShort, result, "isUseShortDescription返回结果不匹配")
		})
	}
}
