package convert

import (
	"bytes"
	"errors"
	"fmt"
	"image"
	"image/color"
	"image/jpeg"
	"image/png"
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"github.com/AfterShip/connectors-sdk-go/v2/image_upload_records"
	"github.com/AfterShip/go-sdk/converts"
	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/gopkg/log"
)

func TestGetFileName(t *testing.T) {
	url := "http://example.com/path/to/file.jpg"
	expectedFileName := "file.jpg"

	fileName := getFileName(url)

	assert.Equal(t, expectedFileName, fileName, "Expected file name to be extracted correctly from URL")
}

func TestGetFileType(t *testing.T) {
	// Prepare a byte slice representing a JPEG file
	jpegFileBytes := []byte("\xff\xd8\xff")

	// Prepare a byte slice representing a PDF file
	pdfFileBytes := []byte("%PDF-")

	fileType := getFileType(jpegFileBytes)
	assert.Equal(t, "jpg", fileType, "Expected file type to be correctly identified as JPEG")

	fileType = getFileType(pdfFileBytes)
	assert.Equal(t, "application/pdf", fileType, "Expected file type to be correctly identified as PDF")
}

// Helper function to create a test image with given width and height.
func createTestImage(width, height int) []byte {
	upLeft := image.Point{0, 0}
	lowRight := image.Point{width, height}
	img := image.NewRGBA(image.Rectangle{upLeft, lowRight})
	// Paint the image in solid color.
	for x := 0; x < width; x++ {
		for y := 0; y < height; y++ {
			img.Set(x, y, color.RGBA{
				R: uint8(x),
				G: uint8(y),
				B: 0,
				A: 255,
			})
		}
	}
	buf := new(bytes.Buffer)
	png.Encode(buf, img)
	return buf.Bytes()
}

func TestNeedResizeImage(t *testing.T) {
	tests := []struct {
		name             string
		imageWidth       int
		imageHeight      int
		minSize          int
		maxSize          int
		wantRes          bool
		wantEdge         int64
		needSquare       bool
		aspectRatioRange [2]float64
	}{
		{"Square image within range", 100, 100, 50, 200, false, 0, true, [2]float64{0, 0}},
		{"Rectangle image smaller than minSize", 40, 40, 50, 200, true, 50, true, [2]float64{0, 0}},
		{"Rectangle image larger than maxSize", 300, 300, 50, 200, true, 200, true, [2]float64{0, 0}},
		{"Rectangle image needs padding", 150, 100, 50, 200, true, 150, true, [2]float64{0, 0}},
		{"Valid rectangle image", 150, 200, 50, 300, true, 200, true, [2]float64{0, 0}},
		{"check disable square", 150, 200, 50, 300, false, 0, false, [2]float64{0, 0}},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotRes, gotEdge, _, _ := needResizeImage(tt.imageWidth, tt.imageHeight, tt.minSize, tt.maxSize, tt.needSquare, tt.aspectRatioRange)
			if gotRes != tt.wantRes || gotEdge != int64(tt.wantEdge) {
				t.Errorf("For image %s; want result %v and edge size %d, got result %v and edge size %d",
					tt.name, tt.wantRes, tt.wantEdge, gotRes, gotEdge)
			}
		})
	}
}

func TestGetImageHash(t *testing.T) {
	tests := []struct {
		name         string
		imageUseCase string
		want         string
	}{
		{
			name:         "主图用例",
			imageUseCase: ImageUseCaseMainImage,
			want:         "0",
		},
		{
			name:         "属性图用例",
			imageUseCase: ImageUseCaseAttributeImage,
			want:         ImageUseCaseAttributeImage,
		},
		{
			name:         "描述图用例",
			imageUseCase: ImageUseCaseDescriptionImage,
			want:         ImageUseCaseDescriptionImage,
		},
		{
			name:         "认证图用例",
			imageUseCase: ImageUseCaseCertificationImage,
			want:         ImageUseCaseCertificationImage,
		},
		{
			name:         "尺寸图用例",
			imageUseCase: ImageUseCaseSizeChartImage,
			want:         ImageUseCaseSizeChartImage,
		},
		{
			name:         "空字符串",
			imageUseCase: "",
			want:         "",
		},
		{
			name:         "自定义用例",
			imageUseCase: "custom_use_case",
			want:         "custom_use_case",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := getImageHash(tt.imageUseCase)
			assert.Equal(t, tt.want, got, "getImageHash() 应该返回正确的哈希值")
		})
	}
}

func TestGetImageUseCase(t *testing.T) {
	tests := []struct {
		name      string
		imageHash string
		want      string
	}{
		{
			name:      "主图哈希值",
			imageHash: "0",
			want:      ImageUseCaseMainImage,
		},
		{
			name:      "属性图哈希值",
			imageHash: ImageUseCaseAttributeImage,
			want:      ImageUseCaseAttributeImage,
		},
		{
			name:      "描述图哈希值",
			imageHash: ImageUseCaseDescriptionImage,
			want:      ImageUseCaseDescriptionImage,
		},
		{
			name:      "认证图哈希值",
			imageHash: ImageUseCaseCertificationImage,
			want:      ImageUseCaseCertificationImage,
		},
		{
			name:      "尺寸图哈希值",
			imageHash: ImageUseCaseSizeChartImage,
			want:      ImageUseCaseSizeChartImage,
		},
		{
			name:      "空字符串",
			imageHash: "",
			want:      "",
		},
		{
			name:      "自定义哈希值",
			imageHash: "custom_hash",
			want:      "custom_hash",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := getImageUseCase(tt.imageHash)
			assert.Equal(t, tt.want, got, "getImageUseCase() 应该返回正确的用例")
		})
	}
}

func TestServiceImpl_isImageNeedTransform(t *testing.T) {
	// 创建测试服务实例
	s := &serviceImpl{
		logger: log.GlobalLogger(),
	}

	// 准备测试用例
	tests := []struct {
		name                string
		arg                 *transformImageArg
		wantNeedTransform   bool
		wantIsFormatToPNG   bool
		wantInstructionsLen int // 检查指令数量
	}{
		{
			name: "需要调整尺寸的图片",
			arg: &transformImageArg{
				imageBytes:       createTestImage(400, 300), // 非正方形图片
				imageType:        "image/jpeg",
				needResize:       true,
				minSize:          500,
				maxSize:          2000,
				needSquare:       true,
				needImageFormats: []string{"jpg", "jpeg", "png"},
				maxFileSize:      1000000,
				needTransparent:  false,
			},
			wantNeedTransform:   true,
			wantIsFormatToPNG:   true,
			wantInstructionsLen: 3,
		},
		{
			name: "需要调整尺寸并强制缩小的图片",
			arg: &transformImageArg{
				imageBytes:       createTestImage(2500, 2500), // 超大图片
				imageType:        "image/jpeg",
				needResize:       true,
				minSize:          500,
				maxSize:          2000,
				needSquare:       true,
				needImageFormats: []string{"jpg", "jpeg", "png"},
				maxFileSize:      1000000,
				forceReducedSize: true,
			},
			wantNeedTransform:   true,
			wantIsFormatToPNG:   true,
			wantInstructionsLen: 3, // 格式转换、正方形处理、缩小尺寸和压缩
		},
		{
			name: "需要转换格式的图片",
			arg: &transformImageArg{
				imageBytes:       createTestImage(800, 800), // 正方形且尺寸合适
				imageType:        "image/webp",              // 不在支持列表中的格式
				needResize:       true,
				minSize:          500,
				maxSize:          2000,
				needSquare:       true,
				needImageFormats: []string{"jpg", "jpeg", "png"},
				maxFileSize:      1000000,
			},
			wantNeedTransform:   true,
			wantIsFormatToPNG:   false,
			wantInstructionsLen: 2,
		},
		{
			name: "只需要压缩文件大小的图片",
			arg: &transformImageArg{
				imageBytes:       createTestImage(800, 800), // 创建一个足够大的图片
				imageType:        "image/jpeg",
				needResize:       false, // 不需调整尺寸
				needImageFormats: []string{"jpg", "jpeg", "png"},
				maxFileSize:      100, // 设置极小的最大文件大小以触发压缩
			},
			wantNeedTransform:   true,
			wantIsFormatToPNG:   false,
			wantInstructionsLen: 2,
		},
		{
			name: "使用URL转换大图片",
			arg: &transformImageArg{
				imageBytes:       make([]byte, 11000000), // 超过10MB的图片
				imageOriginURL:   "http://example.com/large-image.jpg",
				imageType:        "image/jpeg",
				needResize:       true,
				minSize:          500,
				maxSize:          2000,
				needImageFormats: []string{"jpg", "jpeg", "png"},
				maxFileSize:      1000000,
			},
			wantNeedTransform:   true,
			wantIsFormatToPNG:   false,
			wantInstructionsLen: 2,
		},
		{
			name: "基础转换的图片",
			arg: &transformImageArg{
				imageBytes:       createTestImage(800, 800), // 尺寸合适的正方形图片
				imageType:        "image/jpeg",              // 支持的格式
				needResize:       true,
				minSize:          500,
				maxSize:          2000,
				needSquare:       true,
				needImageFormats: []string{"jpg", "jpeg", "png"},
				maxFileSize:      1000000, // 足够大的文件大小限制
			},
			wantNeedTransform:   true,
			wantIsFormatToPNG:   false,
			wantInstructionsLen: 2,
		},
		{
			name: "需要透明背景的图片",
			arg: &transformImageArg{
				imageBytes:       createTestImage(400, 300),
				imageType:        "image/jpeg",
				needResize:       true,
				minSize:          500,
				maxSize:          2000,
				needSquare:       true,
				needImageFormats: []string{"jpg", "jpeg", "png"},
				maxFileSize:      1000000,
				needTransparent:  true,
			},
			wantNeedTransform:   true,
			wantIsFormatToPNG:   true,
			wantInstructionsLen: 3,
		},
		{
			name: "基础转换的图片,但不需要正方形处理",
			arg: &transformImageArg{
				imageBytes:       createTestImage(2000, 3000), // 尺寸合适的正方形图片
				imageType:        "image/jpeg",                // 支持的格式
				needResize:       true,
				minSize:          500,
				maxSize:          2000,
				needSquare:       false,
				needImageFormats: []string{"jpg", "jpeg", "png"},
				maxFileSize:      1000000, // 足够大的文件大小限制
			},
			wantNeedTransform:   true,
			wantIsFormatToPNG:   false,
			wantInstructionsLen: 2,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotNeedTransform, gotIsFormatToPNG, gotRequest := s.isImageNeedTransform(tt.arg)

			if gotNeedTransform != tt.wantNeedTransform {
				t.Errorf("isImageNeedTransform() gotNeedTransform = %v, want %v", gotNeedTransform, tt.wantNeedTransform)
			}

			if gotIsFormatToPNG != tt.wantIsFormatToPNG {
				t.Errorf("isImageNeedTransform() gotIsFormatToPNG = %v, want %v", gotIsFormatToPNG, tt.wantIsFormatToPNG)
			}

			// 检查图像转换请求
			if gotNeedTransform {
				// 检查指令数量
				if len(gotRequest.Instructions) != tt.wantInstructionsLen {
					t.Errorf("isImageNeedTransform() instructions length = %d, want %d",
						len(gotRequest.Instructions), tt.wantInstructionsLen)
				}

				// 检查URL或base64数据是否正确设置
				if len(tt.arg.imageBytes) > 10000000 && tt.arg.imageOriginURL != "" {
					if gotRequest.ImageURL != tt.arg.imageOriginURL {
						t.Errorf("isImageNeedTransform() should use ImageURL for large images")
					}
				} else if gotNeedTransform && tt.arg.imageBytes != nil {
					if gotRequest.ImageData == "" {
						t.Errorf("isImageNeedTransform() ImageData should not be empty")
					}
				}

				// 检查透明背景设置
				if tt.arg.needTransparent && tt.wantIsFormatToPNG {
					foundSquareInstruction := false
					for _, inst := range gotRequest.Instructions {
						if inst.Operation == converts.OperationExtent {
							if opt, ok := inst.Option.(*converts.ExtentOption); ok {
								foundSquareInstruction = true
								if opt.BackgroundColor.A != 0 {
									t.Errorf("Square instruction should have transparent background (A=0)")
								}
							}
						}
					}
					if !foundSquareInstruction && tt.wantInstructionsLen > 0 {
						t.Errorf("Expected square instruction with transparent background not found")
					}
				}
			}
		})
	}
}

func TestServiceImpl_responseImageServiceError(t *testing.T) {
	// 创建测试服务实例
	s := &serviceImpl{
		logger: log.GlobalLogger(),
	}

	tests := []struct {
		name        string
		inputErr    error
		wantErrType error
		wantContain string
	}{
		{
			name:        "ErrInvalidArgument错误",
			inputErr:    converts.ErrInvalidArgument,
			wantErrType: ErrorConvertImageService422Error,
			wantContain: converts.ErrInvalidArgument.Error(),
		},
		{
			name:        "ErrMissingImageDataOrURL错误",
			inputErr:    converts.ErrMissingImageDataOrURL,
			wantErrType: ErrorConvertImageService422Error,
			wantContain: converts.ErrMissingImageDataOrURL.Error(),
		},
		{
			name:        "ErrMissingImageExt错误",
			inputErr:    converts.ErrMissingImageExt,
			wantErrType: ErrorConvertImageService422Error,
			wantContain: converts.ErrMissingImageExt.Error(),
		},
		{
			name:        "ErrMissingInstructions错误",
			inputErr:    converts.ErrMissingInstructions,
			wantErrType: ErrorConvertImageService422Error,
			wantContain: converts.ErrMissingInstructions.Error(),
		},
		{
			name:        "429错误",
			inputErr:    fmt.Errorf("received status code 429 from the service"),
			wantErrType: ErrorConvertImageService429Error,
			wantContain: "429",
		},
		{
			name:        "413错误",
			inputErr:    fmt.Errorf("received status code 413 from the service"),
			wantErrType: ErrorConvertImageService413Error,
			wantContain: "413",
		},
		{
			name:        "ErrUnknown错误",
			inputErr:    converts.ErrUnknown,
			wantErrType: ErrorConvertImageServiceError,
			wantContain: converts.ErrUnknown.Error(),
		},
		{
			name:        "其他未知错误",
			inputErr:    fmt.Errorf("some random error"),
			wantErrType: ErrorConvertImageServiceError,
			wantContain: "some random error",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotErr := s.responseImageServiceError(tt.inputErr)

			// 验证错误类型
			if !errors.Is(gotErr, tt.wantErrType) {
				t.Errorf("responseImageServiceError() 错误类型 = %v, 期望 %v", gotErr, tt.wantErrType)
			}

			// 验证错误消息中包含原始错误信息
			if !strings.Contains(gotErr.Error(), tt.wantContain) {
				t.Errorf("responseImageServiceError() 错误消息 = %v, 应该包含 %v", gotErr.Error(), tt.wantContain)
			}
		})
	}
}

func TestConvertSheinImageUseCaseToEnum(t *testing.T) {
	tests := []struct {
		name     string
		useCase  string
		expected int
	}{
		{
			name:     "主图用例",
			useCase:  ImageUseCaseMain,
			expected: 1,
		},
		{
			name:     "详情图用例",
			useCase:  ImageUseCaseDetail,
			expected: 2,
		},
		{
			name:     "方形图用例",
			useCase:  ImageUseCaseSquare,
			expected: 5,
		},
		{
			name:     "单件图用例",
			useCase:  ImageUseCasePiece,
			expected: 6,
		},
		{
			name:     "特定图用例",
			useCase:  ImageUseCaseSpecific,
			expected: 7,
		},
		{
			name:     "未知用例-默认返回主图枚举值",
			useCase:  "unknown_use_case",
			expected: 1,
		},
		{
			name:     "空字符串-默认返回主图枚举值",
			useCase:  "",
			expected: 1,
		},
		{
			name:     "主图用例(旧格式)",
			useCase:  ImageUseCaseMainImage,
			expected: 1, // 应该默认为主图
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := convertSheinImageUseCaseToEnum(tt.useCase)
			if result != tt.expected {
				t.Errorf("convertSheinImageUseCaseToEnum(%q) = %d, 期望 %d",
					tt.useCase, result, tt.expected)
			}
		})
	}
}

func TestIsCacheExpired(t *testing.T) {
	tests := []struct {
		name         string
		record       image_upload_records.ModelsResponseRecord
		cacheSeconds int64
		want         bool
	}{
		{
			name: "缓存秒数为0-永不过期",
			record: image_upload_records.ModelsResponseRecord{
				UpdatedAt: types.MakeDatetime(time.Now().Add(-1 * time.Hour)),
			},
			cacheSeconds: 0,
			want:         false,
		},
		{
			name: "缓存秒数为负数-永不过期",
			record: image_upload_records.ModelsResponseRecord{
				UpdatedAt: types.MakeDatetime(time.Now().Add(-1 * time.Hour)),
			},
			cacheSeconds: -3600,
			want:         false,
		},
		{
			name: "缓存未过期-刚刚更新",
			record: image_upload_records.ModelsResponseRecord{
				UpdatedAt: types.MakeDatetime(time.Now()),
			},
			cacheSeconds: 3600,
			want:         false,
		},
		{
			name: "缓存未过期-在有效期内",
			record: image_upload_records.ModelsResponseRecord{
				UpdatedAt: types.MakeDatetime(time.Now().Add(-30 * time.Minute)),
			},
			cacheSeconds: 3600, // 1小时
			want:         false,
		},
		{
			name: "缓存刚好在边界-不过期",
			record: image_upload_records.ModelsResponseRecord{
				UpdatedAt: types.MakeDatetime(time.Now().Add(-1 * time.Hour)),
			},
			cacheSeconds: 3600, // 1小时
			want:         false,
		},
		{
			name: "缓存已过期-超出有效期",
			record: image_upload_records.ModelsResponseRecord{
				UpdatedAt: types.MakeDatetime(time.Now().Add(-2 * time.Hour)),
			},
			cacheSeconds: 3600, // 1小时
			want:         true,
		},
		{
			name: "缓存已过期-超出很长时间",
			record: image_upload_records.ModelsResponseRecord{
				UpdatedAt: types.MakeDatetime(time.Now().Add(-24 * time.Hour)),
			},
			cacheSeconds: 3600, // 1小时
			want:         true,
		},
		{
			name: "短缓存时间-已过期",
			record: image_upload_records.ModelsResponseRecord{
				UpdatedAt: types.MakeDatetime(time.Now().Add(-2 * time.Minute)),
			},
			cacheSeconds: 60, // 1分钟
			want:         true,
		},
		{
			name: "短缓存时间-未过期",
			record: image_upload_records.ModelsResponseRecord{
				UpdatedAt: types.MakeDatetime(time.Now().Add(-30 * time.Second)),
			},
			cacheSeconds: 60, // 1分钟
			want:         false,
		},
		{
			name: "超长缓存时间-未过期",
			record: image_upload_records.ModelsResponseRecord{
				UpdatedAt: types.MakeDatetime(time.Now().Add(-7 * 24 * time.Hour)),
			},
			cacheSeconds: 30 * 24 * 3600, // 30天
			want:         false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := isCacheExpired(tt.record, tt.cacheSeconds)
			assert.Equal(t, tt.want, got, "isCacheExpired结果应该匹配")
		})
	}
}

func TestGetImageUploadRecord(t *testing.T) {
	tests := []struct {
		name       string
		recordResp *image_upload_records.GetInternalImageUploadRecordsResp
		wantRecord image_upload_records.ModelsResponseRecord
		wantExist  bool
	}{
		{
			name:       "响应为nil",
			recordResp: nil,
			wantRecord: image_upload_records.ModelsResponseRecord{},
			wantExist:  false,
		},
		{
			name: "响应Data为nil",
			recordResp: &image_upload_records.GetInternalImageUploadRecordsResp{
				Data: nil,
			},
			wantRecord: image_upload_records.ModelsResponseRecord{},
			wantExist:  false,
		},
		{
			name: "ImageUploadRecords为空切片",
			recordResp: &image_upload_records.GetInternalImageUploadRecordsResp{
				Data: &image_upload_records.GetInternalImageUploadRecordsData{
					ImageUploadRecords: []image_upload_records.ModelsResponseRecord{},
				},
			},
			wantRecord: image_upload_records.ModelsResponseRecord{},
			wantExist:  false,
		},
		{
			name: "ImageUploadRecords为nil",
			recordResp: &image_upload_records.GetInternalImageUploadRecordsResp{
				Data: &image_upload_records.GetInternalImageUploadRecordsData{
					ImageUploadRecords: nil,
				},
			},
			wantRecord: image_upload_records.ModelsResponseRecord{},
			wantExist:  false,
		},
		{
			name: "有一条记录-返回第一条",
			recordResp: &image_upload_records.GetInternalImageUploadRecordsResp{
				Data: &image_upload_records.GetInternalImageUploadRecordsData{
					ImageUploadRecords: []image_upload_records.ModelsResponseRecord{
						{
							ExternalImageID:  types.MakeString("image-id-1"),
							ExternalImageUrl: types.MakeString("http://example.com/image1.jpg"),
							ImageWidth:       types.MakeInt(800),
							ImageHeight:      types.MakeInt(600),
						},
					},
				},
			},
			wantRecord: image_upload_records.ModelsResponseRecord{
				ExternalImageID:  types.MakeString("image-id-1"),
				ExternalImageUrl: types.MakeString("http://example.com/image1.jpg"),
				ImageWidth:       types.MakeInt(800),
				ImageHeight:      types.MakeInt(600),
			},
			wantExist: true,
		},
		{
			name: "有多条记录-返回第一条",
			recordResp: &image_upload_records.GetInternalImageUploadRecordsResp{
				Data: &image_upload_records.GetInternalImageUploadRecordsData{
					ImageUploadRecords: []image_upload_records.ModelsResponseRecord{
						{
							ExternalImageID:  types.MakeString("image-id-1"),
							ExternalImageUrl: types.MakeString("http://example.com/image1.jpg"),
							ImageWidth:       types.MakeInt(800),
							ImageHeight:      types.MakeInt(600),
						},
						{
							ExternalImageID:  types.MakeString("image-id-2"),
							ExternalImageUrl: types.MakeString("http://example.com/image2.jpg"),
							ImageWidth:       types.MakeInt(1200),
							ImageHeight:      types.MakeInt(800),
						},
					},
				},
			},
			wantRecord: image_upload_records.ModelsResponseRecord{
				ExternalImageID:  types.MakeString("image-id-1"),
				ExternalImageUrl: types.MakeString("http://example.com/image1.jpg"),
				ImageWidth:       types.MakeInt(800),
				ImageHeight:      types.MakeInt(600),
			},
			wantExist: true,
		},
		{
			name: "完整的记录数据",
			recordResp: &image_upload_records.GetInternalImageUploadRecordsResp{
				Data: &image_upload_records.GetInternalImageUploadRecordsData{
					ImageUploadRecords: []image_upload_records.ModelsResponseRecord{
						{
							ExternalImageID:  types.MakeString("complete-image-id"),
							ExternalImageUrl: types.MakeString("http://example.com/complete.jpg"),
							ImageWidth:       types.MakeInt(1920),
							ImageHeight:      types.MakeInt(1080),
							ImageHash:        types.MakeString("hash123"),
							CreatedAt:        types.MakeDatetime(time.Now()),
							UpdatedAt:        types.MakeDatetime(time.Now()),
						},
					},
				},
			},
			wantRecord: image_upload_records.ModelsResponseRecord{
				ExternalImageID:  types.MakeString("complete-image-id"),
				ExternalImageUrl: types.MakeString("http://example.com/complete.jpg"),
				ImageWidth:       types.MakeInt(1920),
				ImageHeight:      types.MakeInt(1080),
				ImageHash:        types.MakeString("hash123"),
				CreatedAt:        types.MakeDatetime(time.Now()),
				UpdatedAt:        types.MakeDatetime(time.Now()),
			},
			wantExist: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotRecord, gotExist := getImageUploadRecord(tt.recordResp)

			assert.Equal(t, tt.wantExist, gotExist, "exist标志应该匹配")

			if tt.wantExist {
				assert.Equal(t, tt.wantRecord.ExternalImageID.String(), gotRecord.ExternalImageID.String(), "ExternalImageID应该匹配")
				assert.Equal(t, tt.wantRecord.ExternalImageUrl.String(), gotRecord.ExternalImageUrl.String(), "ExternalImageUrl应该匹配")
				assert.Equal(t, tt.wantRecord.ImageWidth.Int(), gotRecord.ImageWidth.Int(), "ImageWidth应该匹配")
				assert.Equal(t, tt.wantRecord.ImageHeight.Int(), gotRecord.ImageHeight.Int(), "ImageHeight应该匹配")
				assert.Equal(t, tt.wantRecord.ImageHash.String(), gotRecord.ImageHash.String(), "ImageHash应该匹配")

			} else {
				// 验证返回的是零值
				assert.Equal(t, image_upload_records.ModelsResponseRecord{}, gotRecord, "不存在时应该返回零值")
			}
		})
	}
}

func TestIs4XXStatusCode(t *testing.T) {
	tests := []struct {
		name       string
		statusCode int
		want       bool
	}{
		{
			name:       "400 Bad Request",
			statusCode: 400,
			want:       true,
		},
		{
			name:       "401 Unauthorized",
			statusCode: 401,
			want:       true,
		},
		{
			name:       "403 Forbidden",
			statusCode: 403,
			want:       true,
		},
		{
			name:       "404 Not Found",
			statusCode: 404,
			want:       true,
		},
		{
			name:       "200 OK",
			statusCode: 200,
			want:       false,
		},
		{
			name:       "201 Created",
			statusCode: 201,
			want:       false,
		},
		{
			name:       "301 Moved Permanently",
			statusCode: 301,
			want:       false,
		},
		{
			name:       "405 Method Not Allowed",
			statusCode: 405,
			want:       false,
		},
		{
			name:       "409 Conflict",
			statusCode: 409,
			want:       false,
		},
		{
			name:       "410 Gone",
			statusCode: 410,
			want:       false,
		},
		{
			name:       "422 Unprocessable Entity",
			statusCode: 422,
			want:       false,
		},
		{
			name:       "429 Too Many Requests",
			statusCode: 429,
			want:       false,
		},
		{
			name:       "500 Internal Server Error",
			statusCode: 500,
			want:       false,
		},
		{
			name:       "502 Bad Gateway",
			statusCode: 502,
			want:       false,
		},
		{
			name:       "503 Service Unavailable",
			statusCode: 503,
			want:       false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := is4XXStatusCode(tt.statusCode)
			assert.Equal(t, tt.want, got, "is4XXStatusCode() 应该返回正确的布尔值")
		})
	}
}

func TestHandleImageResize(t *testing.T) {
	tests := []struct {
		name                string
		arg                 *transformImageArg
		wantNeedResize      bool
		wantIsFormatToPNG   bool
		wantInstructionsLen int
	}{
		{
			name: "不需要调整尺寸",
			arg: &transformImageArg{
				imageBytes:       createTestImage(800, 800),
				needResize:       false,
				needSquare:       true,
				minSize:          500,
				maxSize:          2000,
				aspectRatioRange: [2]float64{0.5, 2.0},
			},
			wantNeedResize:      false,
			wantIsFormatToPNG:   false,
			wantInstructionsLen: 0,
		},
		{
			name: "需要调整尺寸-正常图片配置",
			arg: &transformImageArg{
				imageBytes:       createTestImage(400, 300),
				needResize:       true,
				needSquare:       true,
				minSize:          500,
				maxSize:          2000,
				aspectRatioRange: [2]float64{0.5, 2.0},
			},
			wantNeedResize:      true,
			wantIsFormatToPNG:   true,
			wantInstructionsLen: 2,
		},
		{
			name: "需要调整尺寸且强制缩放",
			arg: &transformImageArg{
				imageBytes:         createTestImage(2500, 2500),
				needResize:         true,
				needSquare:         true,
				minSize:            500,
				maxSize:            2000,
				forceReducedSize:   true,
				forceReducedWidth:  1000,
				forceReducedHeight: 1000,
				aspectRatioRange:   [2]float64{0.5, 2.0},
			},
			wantNeedResize:      true,
			wantIsFormatToPNG:   true,
			wantInstructionsLen: 2,
		},
		{
			name: "需要调整尺寸-不需要方形处理",
			arg: &transformImageArg{
				imageBytes:       createTestImage(3000, 2000),
				needResize:       true,
				needSquare:       false,
				minSize:          500,
				maxSize:          2000,
				forceReducedSize: true,
				aspectRatioRange: [2]float64{0.5, 2.0},
			},
			wantNeedResize:      true,
			wantIsFormatToPNG:   true,
			wantInstructionsLen: 2,
		},
		{
			name: "图片解码失败-使用默认处理",
			arg: &transformImageArg{
				imageBytes:       []byte("invalid image data"),
				needResize:       true,
				needSquare:       true,
				minSize:          500,
				maxSize:          2000,
				aspectRatioRange: [2]float64{0.5, 2.0},
			},
			wantNeedResize:      true,
			wantIsFormatToPNG:   true,
			wantInstructionsLen: 2,
		},
		{
			name: "图片解码失败-只需强制缩放",
			arg: &transformImageArg{
				imageBytes:         []byte("invalid image data"),
				needResize:         true,
				needSquare:         false,
				forceReducedSize:   true,
				forceReducedWidth:  800,
				forceReducedHeight: 600,
				minSize:            500,
				maxSize:            2000,
				aspectRatioRange:   [2]float64{0.5, 2.0},
			},
			wantNeedResize:      true,
			wantIsFormatToPNG:   true,
			wantInstructionsLen: 2,
		},
		{
			name: "图片解码失败-强制缩放零值使用默认",
			arg: &transformImageArg{
				imageBytes:         []byte("invalid image data"),
				needResize:         true,
				needSquare:         false,
				forceReducedSize:   true,
				forceReducedWidth:  0,
				forceReducedHeight: 0,
				minSize:            500,
				maxSize:            2000,
				aspectRatioRange:   [2]float64{0.5, 2.0},
			},
			wantNeedResize:      true,
			wantIsFormatToPNG:   true,
			wantInstructionsLen: 2,
		},
		{
			name: "需要透明背景的方形处理",
			arg: &transformImageArg{
				imageBytes:       createTestImage(400, 300),
				needResize:       true,
				needSquare:       true,
				needTransparent:  true,
				minSize:          500,
				maxSize:          2000,
				aspectRatioRange: [2]float64{0.5, 2.0},
			},
			wantNeedResize:      true,
			wantIsFormatToPNG:   true,
			wantInstructionsLen: 2,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotNeedResize, gotIsFormatToPNG, gotInstructions := handleImageResize(tt.arg)

			assert.Equal(t, tt.wantNeedResize, gotNeedResize, "needResize应该匹配")
			assert.Equal(t, tt.wantIsFormatToPNG, gotIsFormatToPNG, "isFormatToPNG应该匹配")
			assert.Equal(t, tt.wantInstructionsLen, len(gotInstructions), "指令数量应该匹配")

			if tt.wantNeedResize {
				// 验证指令类型的正确性
				if tt.wantIsFormatToPNG {
					assert.Equal(t, converts.OperationFormat, gotInstructions[0].Operation, "第一个指令应该是格式转换")
				}

				// 验证透明背景设置
				if tt.arg.needTransparent && tt.wantIsFormatToPNG {
					for _, inst := range gotInstructions {
						if inst.Operation == converts.OperationExtent {
							if opt, ok := inst.Option.(*converts.ExtentOption); ok {
								assert.Equal(t, uint8(0), opt.BackgroundColor.A, "透明背景的Alpha值应该为0")
							}
						}
					}
				}
			}
		})
	}
}

func TestHandleDefaultResize(t *testing.T) {
	tests := []struct {
		name                 string
		arg                  *transformImageArg
		wantNeedResize       bool
		wantIsFormatToPNG    bool
		wantInstructionsLen  int
		wantInstructionTypes []string
	}{
		{
			name: "只需要方形处理",
			arg: &transformImageArg{
				needSquare:       true,
				forceReducedSize: false,
				needTransparent:  false,
			},
			wantNeedResize:      true,
			wantIsFormatToPNG:   true,
			wantInstructionsLen: 2,
			wantInstructionTypes: []string{
				converts.OperationFormat,
				converts.OperationExtent,
			},
		},
		{
			name: "只需要强制缩放",
			arg: &transformImageArg{
				needSquare:         false,
				forceReducedSize:   true,
				forceReducedWidth:  800,
				forceReducedHeight: 600,
			},
			wantNeedResize:      true,
			wantIsFormatToPNG:   true,
			wantInstructionsLen: 2,
			wantInstructionTypes: []string{
				converts.OperationFormat,
				converts.OperationExtent,
			},
		},
		{
			name: "需要方形处理和强制缩放",
			arg: &transformImageArg{
				needSquare:         true,
				forceReducedSize:   false,
				forceReducedWidth:  1000,
				forceReducedHeight: 1000,
				needTransparent:    false,
			},
			wantNeedResize:      true,
			wantIsFormatToPNG:   true,
			wantInstructionsLen: 2,
			wantInstructionTypes: []string{
				converts.OperationFormat,
				converts.OperationExtent,
			},
		},
		{
			name: "强制缩放尺寸为零-使用默认值",
			arg: &transformImageArg{
				needSquare:         false,
				forceReducedSize:   true,
				forceReducedWidth:  0,
				forceReducedHeight: 0,
			},
			wantNeedResize:      true,
			wantIsFormatToPNG:   true,
			wantInstructionsLen: 2,
			wantInstructionTypes: []string{
				converts.OperationFormat,
				converts.OperationExtent,
			},
		},
		{
			name: "强制缩放宽度为零-使用默认值",
			arg: &transformImageArg{
				needSquare:         false,
				forceReducedSize:   true,
				forceReducedWidth:  0,
				forceReducedHeight: 800,
			},
			wantNeedResize:      true,
			wantIsFormatToPNG:   true,
			wantInstructionsLen: 2,
			wantInstructionTypes: []string{
				converts.OperationFormat,
				converts.OperationExtent,
			},
		},
		{
			name: "强制缩放高度为零-使用默认值",
			arg: &transformImageArg{
				needSquare:         false,
				forceReducedSize:   true,
				forceReducedWidth:  800,
				forceReducedHeight: 0,
			},
			wantNeedResize:      true,
			wantIsFormatToPNG:   true,
			wantInstructionsLen: 2,
			wantInstructionTypes: []string{
				converts.OperationFormat,
				converts.OperationExtent,
			},
		},
		{
			name: "需要透明背景的方形处理",
			arg: &transformImageArg{
				needSquare:       true,
				forceReducedSize: false,
				needTransparent:  true,
			},
			wantNeedResize:      true,
			wantIsFormatToPNG:   true,
			wantInstructionsLen: 2,
			wantInstructionTypes: []string{
				converts.OperationFormat,
				converts.OperationExtent,
			},
		},
		{
			name: "透明背景+方形处理+强制缩放",
			arg: &transformImageArg{
				needSquare:         true,
				forceReducedSize:   true,
				forceReducedWidth:  600,
				forceReducedHeight: 600,
				needTransparent:    true,
			},
			wantNeedResize:      true,
			wantIsFormatToPNG:   true,
			wantInstructionsLen: 2,
			wantInstructionTypes: []string{
				converts.OperationFormat,
				converts.OperationExtent,
			},
		},
		{
			name: "都不需要处理",
			arg: &transformImageArg{
				needSquare:       false,
				forceReducedSize: false,
			},
			wantNeedResize:       false,
			wantIsFormatToPNG:    false,
			wantInstructionsLen:  0,
			wantInstructionTypes: []string{},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotNeedResize, gotIsFormatToPNG, gotInstructions := handleDefaultResize(tt.arg)

			assert.Equal(t, tt.wantNeedResize, gotNeedResize, "needResize应该匹配")
			assert.Equal(t, tt.wantIsFormatToPNG, gotIsFormatToPNG, "isFormatToPNG应该匹配")
			assert.Equal(t, tt.wantInstructionsLen, len(gotInstructions), "指令数量应该匹配")

			// 验证指令类型顺序
			for i, expectedOp := range tt.wantInstructionTypes {
				if i < len(gotInstructions) {
					assert.Equal(t, expectedOp, gotInstructions[i].Operation,
						fmt.Sprintf("第%d个指令类型应该匹配", i+1))
				}
			}

			// 验证具体指令选项
			for _, instruction := range gotInstructions {
				switch instruction.Operation {
				case converts.OperationFormat:
					if opt, ok := instruction.Option.(*converts.FormatOption); ok {
						assert.Equal(t, "png", opt.Format, "格式转换应该为PNG")
					}
				case converts.OperationExtent:
					if opt, ok := instruction.Option.(*converts.ExtentOption); ok {
						if tt.arg.needTransparent {
							assert.Equal(t, uint8(0), opt.BackgroundColor.A, "透明背景Alpha值应该为0")
						}
					}
				}
			}
		})
	}
}

func TestProcessImageResize(t *testing.T) {
	tests := []struct {
		name                 string
		imgWidth             int
		imgHeight            int
		arg                  *transformImageArg
		wantNeedResize       bool
		wantIsFormatToPNG    bool
		wantInstructionsLen  int
		wantInstructionTypes []string
	}{
		{
			name:      "图片尺寸符合要求且不需要方形处理",
			imgWidth:  800,
			imgHeight: 600,
			arg: &transformImageArg{
				needSquare:       false,
				forceReducedSize: false,
				minSize:          500,
				maxSize:          2000,
				aspectRatioRange: [2]float64{0.5, 2.0},
			},
			wantNeedResize:       false,
			wantIsFormatToPNG:    false,
			wantInstructionsLen:  0,
			wantInstructionTypes: []string{},
		},
		{
			name:      "图片尺寸符合要求但需要方形处理",
			imgWidth:  800,
			imgHeight: 600,
			arg: &transformImageArg{
				needSquare:       true,
				forceReducedSize: false,
				minSize:          500,
				maxSize:          2000,
				aspectRatioRange: [2]float64{0.5, 2.0},
			},
			wantNeedResize:      true,
			wantIsFormatToPNG:   true,
			wantInstructionsLen: 2,
			wantInstructionTypes: []string{
				converts.OperationFormat,
				converts.OperationExtent,
			},
		},
		{
			name:      "图片过小需要调整尺寸",
			imgWidth:  300,
			imgHeight: 200,
			arg: &transformImageArg{
				needSquare:       false,
				forceReducedSize: true,
				minSize:          500,
				maxSize:          2000,
				aspectRatioRange: [2]float64{0.5, 2.0},
			},
			wantNeedResize:       true,
			wantIsFormatToPNG:    true,
			wantInstructionsLen:  2,
			wantInstructionTypes: []string{},
		},
		{
			name:      "图片过大需要调整尺寸",
			imgWidth:  3000,
			imgHeight: 2500,
			arg: &transformImageArg{
				needSquare:       false,
				forceReducedSize: true,
				minSize:          500,
				maxSize:          2000,
				aspectRatioRange: [2]float64{0.5, 2.0},
			},
			wantNeedResize:       true,
			wantIsFormatToPNG:    true,
			wantInstructionsLen:  2,
			wantInstructionTypes: []string{},
		},
		{
			name:      "图片需要调整尺寸且需要方形处理",
			imgWidth:  400,
			imgHeight: 300,
			arg: &transformImageArg{
				needSquare:       true,
				forceReducedSize: false,
				minSize:          500,
				maxSize:          2000,
				aspectRatioRange: [2]float64{0.5, 2.0},
			},
			wantNeedResize:      true,
			wantIsFormatToPNG:   true,
			wantInstructionsLen: 2,
			wantInstructionTypes: []string{
				converts.OperationFormat,
				converts.OperationExtent,
			},
		},
		{
			name:      "宽高比不符合要求",
			imgWidth:  1000,
			imgHeight: 200,
			arg: &transformImageArg{
				needSquare:       false,
				forceReducedSize: true,
				minSize:          500,
				maxSize:          2000,
				aspectRatioRange: [2]float64{0.5, 2.0},
			},
			wantNeedResize:       true,
			wantIsFormatToPNG:    true,
			wantInstructionsLen:  2,
			wantInstructionTypes: []string{},
		},
		{
			name:      "符合要求的正方形图片",
			imgWidth:  800,
			imgHeight: 800,
			arg: &transformImageArg{
				needSquare:       true,
				forceReducedSize: false,
				minSize:          500,
				maxSize:          2000,
				aspectRatioRange: [2]float64{0.5, 2.0},
			},
			wantNeedResize:       false,
			wantIsFormatToPNG:    false,
			wantInstructionsLen:  0,
			wantInstructionTypes: []string{},
		},
		{
			name:      "需要透明背景的方形处理",
			imgWidth:  600,
			imgHeight: 400,
			arg: &transformImageArg{
				needSquare:       true,
				forceReducedSize: false,
				needTransparent:  true,
				minSize:          500,
				maxSize:          2000,
				aspectRatioRange: [2]float64{0.5, 2.0},
			},
			wantNeedResize:      true,
			wantIsFormatToPNG:   true,
			wantInstructionsLen: 2,
			wantInstructionTypes: []string{
				converts.OperationFormat,
				converts.OperationExtent,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotNeedResize, gotIsFormatToPNG, gotInstructions := processImageResize(tt.imgWidth, tt.imgHeight, tt.arg)

			assert.Equal(t, tt.wantNeedResize, gotNeedResize, "needResize应该匹配")
			assert.Equal(t, tt.wantIsFormatToPNG, gotIsFormatToPNG, "isFormatToPNG应该匹配")
			assert.Equal(t, tt.wantInstructionsLen, len(gotInstructions), "指令数量应该匹配")

			// 验证指令类型顺序
			for i, expectedOp := range tt.wantInstructionTypes {
				if i < len(gotInstructions) {
					assert.Equal(t, expectedOp, gotInstructions[i].Operation,
						fmt.Sprintf("第%d个指令类型应该匹配", i+1))
				}
			}

			// 验证方形处理指令的边长计算
			if tt.wantNeedResize && tt.arg.needSquare {
				for _, instruction := range gotInstructions {
					if instruction.Operation == converts.OperationExtent {
						if opt, ok := instruction.Option.(*converts.SquareOption); ok {
							expectedEdgeSize := int64(max(tt.imgWidth, tt.imgHeight))
							if tt.imgWidth < tt.arg.minSize && tt.imgHeight < tt.arg.minSize {
								expectedEdgeSize = int64(tt.arg.minSize)
							} else if tt.imgWidth > tt.arg.maxSize || tt.imgHeight > tt.arg.maxSize {
								expectedEdgeSize = int64(tt.arg.maxSize)
							}
							assert.Equal(t, expectedEdgeSize, opt.EdgeSize, "方形边长应该正确计算")

							// 验证透明背景设置
							if tt.arg.needTransparent {
								assert.Equal(t, uint8(0), opt.BackgroundColor.A, "透明背景Alpha值应该为0")
							} else {
								assert.Equal(t, uint8(255), opt.BackgroundColor.A, "非透明背景Alpha值应该为255")
							}
						}
					}
				}
			}

			// 验证强制缩放指令的尺寸
			if tt.wantNeedResize && tt.arg.forceReducedSize {
				for _, instruction := range gotInstructions {
					if instruction.Operation == converts.OperationResize {
						if opt, ok := instruction.Option.(*converts.ResizeOption); ok {
							assert.Equal(t, tt.arg.forceReducedWidth, opt.Width, "强制缩放宽度应该匹配")
							assert.Equal(t, tt.arg.forceReducedHeight, opt.Height, "强制缩放高度应该匹配")
						}
					}
				}
			}
		})
	}
}

func TestGetBackgroundColor(t *testing.T) {
	tests := []struct {
		name            string
		needTransparent bool
		wantColor       converts.Color
	}{
		{
			name:            "需要透明背景",
			needTransparent: true,
			wantColor: converts.Color{
				R: 255,
				G: 255,
				B: 255,
				A: 0,
			},
		},
		{
			name:            "不需要透明背景",
			needTransparent: false,
			wantColor: converts.Color{
				R: 255,
				G: 255,
				B: 255,
				A: 255,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := getBackgroundColor(tt.needTransparent)

			assert.Equal(t, tt.wantColor.R, got.R, "红色值应该匹配")
			assert.Equal(t, tt.wantColor.G, got.G, "绿色值应该匹配")
			assert.Equal(t, tt.wantColor.B, got.B, "蓝色值应该匹配")
			assert.Equal(t, tt.wantColor.A, got.A, "Alpha值应该匹配")
		})
	}
}

func TestHandleImageFormatConversion(t *testing.T) {
	tests := []struct {
		name                string
		arg                 *transformImageArg
		isFormatToPNG       bool
		wantInstructionsLen int
		wantOperation       string
		wantFormat          string
	}{
		{
			name: "已经转换为PNG格式-无需格式转换",
			arg: &transformImageArg{
				imageType:        "image/jpeg",
				needImageFormats: []string{"image/jpeg", "image/jpg"},
			},
			isFormatToPNG:       true,
			wantInstructionsLen: 0,
		},
		{
			name: "图片格式在需要的格式列表中-无需转换",
			arg: &transformImageArg{
				imageType:        "image/jpeg",
				needImageFormats: []string{"image/jpeg", "image/png", "image/webp"},
			},
			isFormatToPNG:       false,
			wantInstructionsLen: 0,
		},
		{
			name: "图片格式在需要的格式列表中(大小写不敏感)-无需转换",
			arg: &transformImageArg{
				imageType:        "image/JPEG",
				needImageFormats: []string{"image/jpeg", "image/png"},
			},
			isFormatToPNG:       false,
			wantInstructionsLen: 0,
		},
		{
			name: "图片格式不在需要的格式列表中-需要转换为JPEG",
			arg: &transformImageArg{
				imageType:        "image/webp",
				needImageFormats: []string{"image/jpeg", "image/png"},
			},
			isFormatToPNG:       false,
			wantInstructionsLen: 1,
			wantOperation:       converts.OperationFormat,
			wantFormat:          "jpeg",
		},
		{
			name: "BMP格式不在需要列表中-需要转换为JPEG",
			arg: &transformImageArg{
				imageType:        "image/bmp",
				needImageFormats: []string{"image/jpeg"},
			},
			isFormatToPNG:       false,
			wantInstructionsLen: 1,
			wantOperation:       converts.OperationFormat,
			wantFormat:          "jpeg",
		},
		{
			name: "TIFF格式不在需要列表中-需要转换为JPEG",
			arg: &transformImageArg{
				imageType:        "image/tiff",
				needImageFormats: []string{"image/png"},
			},
			isFormatToPNG:       false,
			wantInstructionsLen: 1,
			wantOperation:       converts.OperationFormat,
			wantFormat:          "jpeg",
		},
		{
			name: "需要格式列表为空-需要转换为JPEG",
			arg: &transformImageArg{
				imageType:        "image/png",
				needImageFormats: []string{},
			},
			isFormatToPNG:       false,
			wantInstructionsLen: 1,
			wantOperation:       converts.OperationFormat,
			wantFormat:          "jpeg",
		},
		{
			name: "PNG格式在需要列表中-无需转换",
			arg: &transformImageArg{
				imageType:        "image/png",
				needImageFormats: []string{"image/png", "image/jpeg"},
			},
			isFormatToPNG:       false,
			wantInstructionsLen: 0,
		},
		{
			name: "已转PNG且原格式不在需要列表中-无需转换",
			arg: &transformImageArg{
				imageType:        "image/webp",
				needImageFormats: []string{"image/jpeg"},
			},
			isFormatToPNG:       true,
			wantInstructionsLen: 0,
		},
		{
			name: "imageType只有格式名称-需要转换",
			arg: &transformImageArg{
				imageType:        "gif",
				needImageFormats: []string{"jpeg"},
			},
			isFormatToPNG:       false,
			wantInstructionsLen: 1,
			wantOperation:       converts.OperationFormat,
			wantFormat:          "jpeg",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := handleImageFormatConversion(tt.arg, tt.isFormatToPNG)

			assert.Equal(t, tt.wantInstructionsLen, len(got), "指令数量应该匹配")

			if tt.wantInstructionsLen > 0 {
				assert.Equal(t, tt.wantOperation, got[0].Operation, "操作类型应该匹配")

				if opt, ok := got[0].Option.(*converts.FormatOption); ok {
					assert.Equal(t, tt.wantFormat, opt.Format, "格式转换目标应该匹配")
				} else {
					t.Error("指令选项应该是FormatOption类型")
				}
			}
		})
	}
}

func TestHandleImageCompression(t *testing.T) {
	tests := []struct {
		name                string
		arg                 *transformImageArg
		needTransform       bool
		wantInstructionsLen int
		wantOperation       string
		wantSize            int64
	}{
		{
			name: "不需要变换且文件大小符合要求-无需压缩",
			arg: &transformImageArg{
				imageBytes:  make([]byte, 500*1024), // 500KB
				maxFileSize: 1024 * 1024,            // 1MB
			},
			needTransform:       false,
			wantInstructionsLen: 0,
		},
		{
			name: "不需要变换但文件过大-需要压缩",
			arg: &transformImageArg{
				imageBytes:  make([]byte, 2*1024*1024), // 2MB
				maxFileSize: 1024 * 1024,               // 1MB
			},
			needTransform:       false,
			wantInstructionsLen: 1,
			wantOperation:       converts.OperationCompress,
			wantSize:            1024 * 1024,
		},
		{
			name: "需要变换-无论文件大小都需要压缩",
			arg: &transformImageArg{
				imageBytes:  make([]byte, 500*1024), // 500KB
				maxFileSize: 1024 * 1024,            // 1MB
			},
			needTransform:       true,
			wantInstructionsLen: 1,
			wantOperation:       converts.OperationCompress,
			wantSize:            1024 * 1024,
		},
		{
			name: "需要变换且文件过大-需要压缩",
			arg: &transformImageArg{
				imageBytes:  make([]byte, 3*1024*1024), // 3MB
				maxFileSize: 2 * 1024 * 1024,           // 2MB
			},
			needTransform:       true,
			wantInstructionsLen: 1,
			wantOperation:       converts.OperationCompress,
			wantSize:            2 * 1024 * 1024,
		},
		{
			name: "文件大小等于最大限制-无需压缩",
			arg: &transformImageArg{
				imageBytes:  make([]byte, 1024*1024), // 1MB
				maxFileSize: 1024 * 1024,             // 1MB
			},
			needTransform:       false,
			wantInstructionsLen: 0,
		},
		{
			name: "空文件-无需压缩",
			arg: &transformImageArg{
				imageBytes:  []byte{},
				maxFileSize: 1024 * 1024, // 1MB
			},
			needTransform:       false,
			wantInstructionsLen: 0,
		},
		{
			name: "最大文件限制为0-需要压缩",
			arg: &transformImageArg{
				imageBytes:  make([]byte, 100), // 100字节
				maxFileSize: 0,
			},
			needTransform:       false,
			wantInstructionsLen: 1,
			wantOperation:       converts.OperationCompress,
			wantSize:            0,
		},
		{
			name: "小文件但需要变换-需要压缩",
			arg: &transformImageArg{
				imageBytes:  make([]byte, 10*1024), // 10KB
				maxFileSize: 5 * 1024 * 1024,       // 5MB
			},
			needTransform:       true,
			wantInstructionsLen: 1,
			wantOperation:       converts.OperationCompress,
			wantSize:            5 * 1024 * 1024,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := handleImageCompression(tt.arg, tt.needTransform)

			assert.Equal(t, tt.wantInstructionsLen, len(got), "指令数量应该匹配")

			if tt.wantInstructionsLen > 0 {
				assert.Equal(t, tt.wantOperation, got[0].Operation, "操作类型应该匹配")

				if opt, ok := got[0].Option.(*converts.CompressOption); ok {
					assert.Equal(t, tt.wantSize, opt.Size, "压缩目标大小应该匹配")
				} else {
					t.Error("指令选项应该是CompressOption类型")
				}
			}
		})
	}
}

func TestBuildConvertRequest(t *testing.T) {
	tests := []struct {
		name         string
		arg          *transformImageArg
		instructions []converts.Instruction
		wantImageURL string
		wantImageExt string
		hasImageData bool
	}{
		{
			name: "小文件使用base64编码",
			arg: &transformImageArg{
				imageBytes:     make([]byte, 1024), // 1KB
				imageOriginURL: "https://example.com/image.jpg",
				imageType:      "image/jpeg",
			},
			instructions: []converts.Instruction{
				{
					Operation: converts.OperationFormat,
					Option:    &converts.FormatOption{Format: "png"},
				},
			},
			wantImageURL: "",
			wantImageExt: "jpeg",
			hasImageData: true,
		},
		{
			name: "大文件使用URL",
			arg: &transformImageArg{
				imageBytes:     make([]byte, 15*1024*1024), // 15MB
				imageOriginURL: "https://example.com/large-image.png",
				imageType:      "image/png",
			},
			instructions: []converts.Instruction{
				{
					Operation: converts.OperationResize,
					Option:    &converts.ResizeOption{Width: 800, Height: 600},
				},
			},
			wantImageURL: "https://example.com/large-image.png",
			wantImageExt: "png",
			hasImageData: false,
		},
		{
			name: "大文件但无URL-使用base64",
			arg: &transformImageArg{
				imageBytes:     make([]byte, 12*1024*1024), // 12MB
				imageOriginURL: "",
				imageType:      "image/gif",
			},
			instructions: []converts.Instruction{
				{
					Operation: converts.OperationCompress,
					Option:    &converts.CompressOption{Size: 1024 * 1024},
				},
			},
			wantImageURL: "",
			wantImageExt: "gif",
			hasImageData: true,
		},
		{
			name: "imageType只有格式名称",
			arg: &transformImageArg{
				imageBytes:     make([]byte, 500),
				imageOriginURL: "https://example.com/image.webp",
				imageType:      "webp",
			},
			instructions: []converts.Instruction{},
			wantImageURL: "",
			wantImageExt: "jpeg",
			hasImageData: true,
		},
		{
			name: "空指令列表",
			arg: &transformImageArg{
				imageBytes:     make([]byte, 2048),
				imageOriginURL: "https://example.com/test.bmp",
				imageType:      "image/bmp",
			},
			instructions: []converts.Instruction{},
			wantImageURL: "",
			wantImageExt: "bmp",
			hasImageData: true,
		},
		{
			name: "超过10MB的文件",
			arg: &transformImageArg{
				imageBytes:     make([]byte, 10*1024*1024+1), // 10MB+1字节
				imageOriginURL: "https://example.com/large.png",
				imageType:      "image/png",
			},
			instructions: []converts.Instruction{
				{
					Operation: converts.OperationFormat,
					Option:    &converts.FormatOption{Format: "jpeg"},
				},
			},
			wantImageURL: "https://example.com/large.png",
			wantImageExt: "png",
			hasImageData: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &serviceImpl{}
			originalImageBytes := make([]byte, len(tt.arg.imageBytes))
			copy(originalImageBytes, tt.arg.imageBytes)

			got := s.buildConvertRequest(tt.arg, tt.instructions)

			// 验证指令
			assert.Equal(t, tt.instructions, got.Instructions, "指令应该匹配")

			// 验证URL设置
			assert.Equal(t, tt.wantImageURL, got.ImageURL, "ImageURL应该匹配")

			// 验证扩展名
			assert.Equal(t, tt.wantImageExt, got.ImageExt, "ImageExt应该匹配")

			// 验证内存清理
			assert.Nil(t, tt.arg.imageBytes, "imageBytes应该被清理")
		})
	}
}

func TestDecodeImageConfig(t *testing.T) {
	// 创建有效的图片数据用于测试
	validJPEGBytes := createValidJPEGBytes(t)
	validPNGBytes := createValidPNGBytes(t)

	tests := []struct {
		name       string
		imageBytes []byte
		imageUrl   string
		wantWidth  int
		wantHeight int
		wantError  bool
	}{
		{
			name:       "有效的JPEG图片",
			imageBytes: validJPEGBytes,
			imageUrl:   "https://example.com/test.jpg",
			wantWidth:  100,
			wantHeight: 100,
			wantError:  false,
		},
		{
			name:       "有效的PNG图片",
			imageBytes: validPNGBytes,
			imageUrl:   "https://example.com/test.png",
			wantWidth:  50,
			wantHeight: 50,
			wantError:  false,
		},
		{
			name:       "无效的图片数据",
			imageBytes: []byte("invalid image data"),
			imageUrl:   "https://example.com/invalid.jpg",
			wantError:  true,
		},
		{
			name:       "空的图片数据",
			imageBytes: []byte{},
			imageUrl:   "https://example.com/empty.jpg",
			wantError:  true,
		},
		{
			name:       "损坏的JPEG头部",
			imageBytes: []byte{0xFF, 0xD8, 0xFF, 0x00, 0x01, 0x02}, // 不完整的JPEG头
			imageUrl:   "https://example.com/corrupted.jpg",
			wantError:  true,
		},
		{
			name:       "不是图片格式的数据",
			imageBytes: []byte("This is not an image file"),
			imageUrl:   "https://example.com/text.txt",
			wantError:  true,
		},
		{
			name:       "PDF文件数据",
			imageBytes: []byte("%PDF-1.4 fake pdf content"),
			imageUrl:   "https://example.com/document.pdf",
			wantError:  true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			config, err := decodeImageConfig(tt.imageBytes, tt.imageUrl)

			if tt.wantError {
				assert.Error(t, err, "应该返回错误")
				assert.Equal(t, image.Config{}, config, "错误时应该返回空配置")
			} else {
				assert.NoError(t, err, "不应该返回错误")
				assert.Equal(t, tt.wantWidth, config.Width, "图片宽度应该匹配")
				assert.Equal(t, tt.wantHeight, config.Height, "图片高度应该匹配")
			}
		})
	}
}

// 辅助函数：创建有效的JPEG图片字节数据
func createValidJPEGBytes(t *testing.T) []byte {
	// 创建一个简单的100x100像素的JPEG图片
	img := image.NewRGBA(image.Rect(0, 0, 100, 100))

	var buf bytes.Buffer
	err := jpeg.Encode(&buf, img, nil)
	assert.NoError(t, err, "创建JPEG图片数据失败")

	return buf.Bytes()
}

// 辅助函数：创建有效的PNG图片字节数据
func createValidPNGBytes(t *testing.T) []byte {
	// 创建一个简单的50x50像素的PNG图片
	img := image.NewRGBA(image.Rect(0, 0, 50, 50))

	var buf bytes.Buffer
	err := png.Encode(&buf, img)
	assert.NoError(t, err, "创建PNG图片数据失败")

	return buf.Bytes()
}

func TestCalculateEdgeSize(t *testing.T) {
	tests := []struct {
		name      string
		imgWidth  int
		imgHeight int
		minSize   int
		maxSize   int
		want      int64
	}{
		{
			name:      "宽高都小于最小尺寸-返回最小尺寸",
			imgWidth:  100,
			imgHeight: 150,
			minSize:   200,
			maxSize:   1000,
			want:      200,
		},
		{
			name:      "宽度小于最小尺寸-返回高度",
			imgWidth:  100,
			imgHeight: 300,
			minSize:   200,
			maxSize:   1000,
			want:      300,
		},
		{
			name:      "高度小于最小尺寸-返回宽度",
			imgWidth:  400,
			imgHeight: 150,
			minSize:   200,
			maxSize:   1000,
			want:      400,
		},
		{
			name:      "宽度超过最大尺寸-返回最大尺寸",
			imgWidth:  1200,
			imgHeight: 800,
			minSize:   200,
			maxSize:   1000,
			want:      1000,
		},
		{
			name:      "高度超过最大尺寸-返回最大尺寸",
			imgWidth:  800,
			imgHeight: 1200,
			minSize:   200,
			maxSize:   1000,
			want:      1000,
		},
		{
			name:      "宽高都超过最大尺寸-返回最大尺寸",
			imgWidth:  1500,
			imgHeight: 1200,
			minSize:   200,
			maxSize:   1000,
			want:      1000,
		},
		{
			name:      "宽高都在范围内-返回较小值",
			imgWidth:  600,
			imgHeight: 400,
			minSize:   200,
			maxSize:   1000,
			want:      600,
		},
		{
			name:      "宽高相等且在范围内-返回该值",
			imgWidth:  500,
			imgHeight: 500,
			minSize:   200,
			maxSize:   1000,
			want:      500,
		},
		{
			name:      "宽度等于最小尺寸-返回高度",
			imgWidth:  200,
			imgHeight: 400,
			minSize:   200,
			maxSize:   1000,
			want:      200,
		},
		{
			name:      "高度等于最小尺寸-返回宽度",
			imgWidth:  400,
			imgHeight: 200,
			minSize:   200,
			maxSize:   1000,
			want:      400,
		},
		{
			name:      "宽度等于最大尺寸-返回最大尺寸",
			imgWidth:  1000,
			imgHeight: 800,
			minSize:   200,
			maxSize:   1000,
			want:      1000,
		},
		{
			name:      "高度等于最大尺寸-返回最大尺寸",
			imgWidth:  800,
			imgHeight: 1000,
			minSize:   200,
			maxSize:   1000,
			want:      800,
		},
		{
			name:      "极小尺寸图片",
			imgWidth:  10,
			imgHeight: 20,
			minSize:   100,
			maxSize:   500,
			want:      100,
		},
		{
			name:      "极大尺寸图片",
			imgWidth:  5000,
			imgHeight: 3000,
			minSize:   100,
			maxSize:   1000,
			want:      1000,
		},
		{
			name:      "边界条件-最小最大相等",
			imgWidth:  300,
			imgHeight: 400,
			minSize:   500,
			maxSize:   500,
			want:      500,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := calculateEdgeSize(tt.imgWidth, tt.imgHeight, tt.minSize, tt.maxSize)
			assert.Equal(t, tt.want, got, "计算的边长应该匹配期望值")
		})
	}
}

func TestNeedSquareResize(t *testing.T) {
	tests := []struct {
		name       string
		imgWidth   int
		imgHeight  int
		needSquare bool
		wantNeed   bool
		wantSize   int64
	}{
		{
			name:       "不需要方形处理-needSquare为false",
			imgWidth:   800,
			imgHeight:  600,
			needSquare: false,
			wantNeed:   false,
			wantSize:   0,
		},
		{
			name:       "已经是方形图片-无需处理",
			imgWidth:   500,
			imgHeight:  500,
			needSquare: true,
			wantNeed:   false,
			wantSize:   0,
		},
		{
			name:       "宽度大于高度-需要方形处理",
			imgWidth:   800,
			imgHeight:  600,
			needSquare: true,
			wantNeed:   true,
			wantSize:   800,
		},
		{
			name:       "高度大于宽度-需要方形处理",
			imgWidth:   600,
			imgHeight:  800,
			needSquare: true,
			wantNeed:   true,
			wantSize:   800,
		},
		{
			name:       "矩形图片但不需要方形处理",
			imgWidth:   1200,
			imgHeight:  800,
			needSquare: false,
			wantNeed:   false,
			wantSize:   0,
		},
		{
			name:       "极小的方形图片",
			imgWidth:   10,
			imgHeight:  10,
			needSquare: true,
			wantNeed:   false,
			wantSize:   0,
		},
		{
			name:       "极大的方形图片",
			imgWidth:   5000,
			imgHeight:  5000,
			needSquare: true,
			wantNeed:   false,
			wantSize:   0,
		},
		{
			name:       "长条形图片-宽度更大",
			imgWidth:   1920,
			imgHeight:  1080,
			needSquare: true,
			wantNeed:   true,
			wantSize:   1920,
		},
		{
			name:       "长条形图片-高度更大",
			imgWidth:   400,
			imgHeight:  1200,
			needSquare: true,
			wantNeed:   true,
			wantSize:   1200,
		},
		{
			name:       "微小差异的接近方形图片",
			imgWidth:   500,
			imgHeight:  501,
			needSquare: true,
			wantNeed:   true,
			wantSize:   501,
		},
		{
			name:       "零值尺寸-宽度为0",
			imgWidth:   0,
			imgHeight:  100,
			needSquare: true,
			wantNeed:   true,
			wantSize:   100,
		},
		{
			name:       "零值尺寸-高度为0",
			imgWidth:   100,
			imgHeight:  0,
			needSquare: true,
			wantNeed:   true,
			wantSize:   100,
		},
		{
			name:       "零值尺寸-都为0",
			imgWidth:   0,
			imgHeight:  0,
			needSquare: true,
			wantNeed:   false,
			wantSize:   0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotNeed, gotSize := needSquareResize(tt.imgWidth, tt.imgHeight, tt.needSquare)

			assert.Equal(t, tt.wantNeed, gotNeed, "是否需要方形处理的结果应该匹配")
			assert.Equal(t, tt.wantSize, gotSize, "边长大小应该匹配")
		})
	}
}

func TestIsAspectRatioValid(t *testing.T) {
	tests := []struct {
		name             string
		width            int
		height           int
		aspectRatioRange [2]float64
		want             bool
	}{
		{
			name:             "宽高比在有效范围内-正方形",
			width:            100,
			height:           100,
			aspectRatioRange: [2]float64{0.5, 2.0},
			want:             true,
		},
		{
			name:             "宽高比在有效范围内-横向矩形",
			width:            200,
			height:           100,
			aspectRatioRange: [2]float64{0.5, 2.5},
			want:             true,
		},
		{
			name:             "宽高比在有效范围内-纵向矩形",
			width:            100,
			height:           200,
			aspectRatioRange: [2]float64{0.4, 2.0},
			want:             true,
		},
		{
			name:             "宽高比小于最小值",
			width:            100,
			height:           300,
			aspectRatioRange: [2]float64{0.5, 2.0},
			want:             false,
		},
		{
			name:             "宽高比大于最大值",
			width:            300,
			height:           100,
			aspectRatioRange: [2]float64{0.5, 2.0},
			want:             false,
		},
		{
			name:             "宽高比等于最小值",
			width:            100,
			height:           200,
			aspectRatioRange: [2]float64{0.5, 2.0},
			want:             true,
		},
		{
			name:             "宽高比等于最大值",
			width:            200,
			height:           100,
			aspectRatioRange: [2]float64{0.5, 2.0},
			want:             true,
		},
		{
			name:             "范围无效-最小值为0",
			width:            100,
			height:           100,
			aspectRatioRange: [2]float64{0, 2.0},
			want:             true,
		},
		{
			name:             "范围无效-最大值为0",
			width:            100,
			height:           100,
			aspectRatioRange: [2]float64{0.5, 0},
			want:             true,
		},
		{
			name:             "范围无效-都为0",
			width:            100,
			height:           100,
			aspectRatioRange: [2]float64{0, 0},
			want:             true,
		},
		{
			name:             "范围无效-负值",
			width:            100,
			height:           100,
			aspectRatioRange: [2]float64{-1.0, 2.0},
			want:             true,
		},
		{
			name:             "宽度为0",
			width:            0,
			height:           100,
			aspectRatioRange: [2]float64{0.5, 2.0},
			want:             false,
		},
		{
			name:             "高度为0",
			width:            100,
			height:           0,
			aspectRatioRange: [2]float64{0.5, 2.0},
			want:             false,
		},
		{
			name:             "宽高都为0",
			width:            0,
			height:           0,
			aspectRatioRange: [2]float64{0.5, 2.0},
			want:             false,
		},
		{
			name:             "负数宽度",
			width:            -100,
			height:           100,
			aspectRatioRange: [2]float64{0.5, 2.0},
			want:             false,
		},
		{
			name:             "负数高度",
			width:            100,
			height:           -100,
			aspectRatioRange: [2]float64{0.5, 2.0},
			want:             false,
		},
		{
			name:             "极窄图片-超出范围",
			width:            10,
			height:           1000,
			aspectRatioRange: [2]float64{0.5, 2.0},
			want:             false,
		},
		{
			name:             "极宽图片-超出范围",
			width:            1000,
			height:           10,
			aspectRatioRange: [2]float64{0.5, 2.0},
			want:             false,
		},
		{
			name:             "很小的有效范围",
			width:            100,
			height:           105,
			aspectRatioRange: [2]float64{0.9, 1.1},
			want:             true,
		},
		{
			name:             "很大的有效范围",
			width:            100,
			height:           500,
			aspectRatioRange: [2]float64{0.1, 10.0},
			want:             true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := isAspectRatioValid(tt.width, tt.height, tt.aspectRatioRange)
			assert.Equal(t, tt.want, got, "宽高比验证结果应该匹配")
		})
	}
}

func TestIsImageSizeValid(t *testing.T) {
	tests := []struct {
		name    string
		width   int
		height  int
		minSize int
		maxSize int
		want    bool
	}{
		{
			name:    "宽高都在有效范围内",
			width:   800,
			height:  600,
			minSize: 100,
			maxSize: 1000,
			want:    true,
		},
		{
			name:    "宽度小于最小值",
			width:   50,
			height:  600,
			minSize: 100,
			maxSize: 1000,
			want:    false,
		},
		{
			name:    "高度小于最小值",
			width:   800,
			height:  50,
			minSize: 100,
			maxSize: 1000,
			want:    false,
		},
		{
			name:    "宽高都小于最小值",
			width:   50,
			height:  80,
			minSize: 100,
			maxSize: 1000,
			want:    false,
		},
		{
			name:    "宽度大于最大值",
			width:   1200,
			height:  600,
			minSize: 100,
			maxSize: 1000,
			want:    false,
		},
		{
			name:    "高度大于最大值",
			width:   800,
			height:  1200,
			minSize: 100,
			maxSize: 1000,
			want:    false,
		},
		{
			name:    "宽高都大于最大值",
			width:   1200,
			height:  1500,
			minSize: 100,
			maxSize: 1000,
			want:    false,
		},
		{
			name:    "宽度等于最小值",
			width:   100,
			height:  600,
			minSize: 100,
			maxSize: 1000,
			want:    true,
		},
		{
			name:    "高度等于最小值",
			width:   800,
			height:  100,
			minSize: 100,
			maxSize: 1000,
			want:    true,
		},
		{
			name:    "宽度等于最大值",
			width:   1000,
			height:  600,
			minSize: 100,
			maxSize: 1000,
			want:    true,
		},
		{
			name:    "高度等于最大值",
			width:   800,
			height:  1000,
			minSize: 100,
			maxSize: 1000,
			want:    true,
		},
		{
			name:    "宽高都等于最小值",
			width:   100,
			height:  100,
			minSize: 100,
			maxSize: 1000,
			want:    true,
		},
		{
			name:    "宽高都等于最大值",
			width:   1000,
			height:  1000,
			minSize: 100,
			maxSize: 1000,
			want:    true,
		},
		{
			name:    "最小值等于最大值-在范围内",
			width:   500,
			height:  500,
			minSize: 500,
			maxSize: 500,
			want:    true,
		},
		{
			name:    "最小值等于最大值-超出范围",
			width:   600,
			height:  400,
			minSize: 500,
			maxSize: 500,
			want:    false,
		},
		{
			name:    "宽度为0",
			width:   0,
			height:  600,
			minSize: 100,
			maxSize: 1000,
			want:    false,
		},
		{
			name:    "高度为0",
			width:   800,
			height:  0,
			minSize: 100,
			maxSize: 1000,
			want:    false,
		},
		{
			name:    "宽高都为0",
			width:   0,
			height:  0,
			minSize: 100,
			maxSize: 1000,
			want:    false,
		},
		{
			name:    "负数宽度",
			width:   -100,
			height:  600,
			minSize: 100,
			maxSize: 1000,
			want:    false,
		},
		{
			name:    "负数高度",
			width:   800,
			height:  -100,
			minSize: 100,
			maxSize: 1000,
			want:    false,
		},
		{
			name:    "极小尺寸范围",
			width:   1,
			height:  1,
			minSize: 1,
			maxSize: 2,
			want:    true,
		},
		{
			name:    "极大尺寸范围",
			width:   5000,
			height:  4000,
			minSize: 1000,
			maxSize: 8000,
			want:    true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := isImageSizeValid(tt.width, tt.height, tt.minSize, tt.maxSize)
			assert.Equal(t, tt.want, got, "图片尺寸验证结果应该匹配")
		})
	}
}
