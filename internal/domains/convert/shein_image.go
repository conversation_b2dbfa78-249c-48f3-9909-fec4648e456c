package convert

import (
	"context"
	"strings"

	"github.com/pkg/errors"

	"go.uber.org/zap"

	"github.com/AfterShip/connectors-ecommerce-sdk-go/common"
	"github.com/AfterShip/connectors-ecommerce-sdk-go/shein/rest"
	commerceproxy "github.com/AfterShip/connectors-library/sdks/commerce_proxy"
	"github.com/AfterShip/connectors-library/sdks/shein_proxy"
	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/gopkg/log"
	"github.com/AfterShip/gopkg/uuid"
	"github.com/AfterShip/pltf-pd-product-listings/internal/consts"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
)

var (
	sheinSupportedImageFormats = []string{"jpg", "jpeg", "png"}
)

type sheinImageArg struct {
	organization    *models.Organization
	salesChannel    *models.SalesChannel
	imageBytes      []byte
	imageName       string
	imageType       string
	imageUseCase    string
	ignoreTransform bool
	imageOriginURL  string
}

type sheinImageUploadRecord struct {
	imageURI     string
	imageURL     string
	imageHeight  int
	imageWidth   int
	imageUseCase string
}

func (r *sheinImageUploadRecord) convertToImageOutput() ImageOutput {
	return ImageOutput{
		ImageURI:     r.imageURI,
		ImageURL:     r.imageURL,
		ImageHeight:  r.imageHeight,
		ImageWidth:   r.imageWidth,
		ImageUseCase: r.imageUseCase,
	}
}

func isUSSheinStoreKey(storeKey string) bool {
	return strings.HasSuffix(storeKey, "|shein-us")
}

func (s *serviceImpl) convert2SheinImage(ctx context.Context, arg *sheinImageArg) (sheinImageUploadRecord, error) {
	if err := validateImageUseCase(arg.imageUseCase); err != nil { // gocover:ignore
		return sheinImageUploadRecord{}, err
	}

	if !arg.ignoreTransform && // gocover:ignore
		(arg.imageUseCase == ImageUseCaseSquare || // gocover:ignore
			!isUSSheinStoreKey(arg.salesChannel.StoreKey)) { // gocover:ignore
		needTransformArg := &transformImageArg{
			imageBytes:       arg.imageBytes,
			imageType:        arg.imageType,
			imageOriginURL:   arg.imageOriginURL,
			needImageFormats: sheinSupportedImageFormats,
			needResize:       true,
			needSquare:       true,
			minSize:          900,
			maxSize:          2200,
			maxFileSize:      3000000,
		}

		if arg.imageUseCase == consts.ExternalImageTypePiece { // gocover:ignore
			needTransformArg.minSize = 80
			needTransformArg.maxSize = 80
		}

		if arg.imageUseCase != consts.ExternalImageTypeSquare &&
			arg.imageUseCase != consts.ExternalImageTypePiece {
			needTransformArg.needSquare = false
			// 直接改成 3:4 的图片大小
			needTransformArg.forceReducedSize = true
			needTransformArg.forceReducedHeight = 1200
			needTransformArg.forceReducedWidth = 900
			needTransformArg.aspectRatioRange = [2]float64{0.75, 1}
		}

		needTransform, isFormatToPNG, transformImageReq := s.isImageNeedTransform(needTransformArg)
		if needTransform { // gocover:ignore
			s.logger.InfoCtx(ctx, "SHEIN image convert isImageNeedTransform",
				zap.String("imageUseCase", arg.imageUseCase),
				zap.Bool("needTransform", needTransform),
				zap.Bool("isFormatToPNG", isFormatToPNG),
				zap.Any("transformImageReq Instructions", transformImageReq.Instructions),
			)

			newImageBytes, err := s.imageTransform(ctx, transformImageReq)
			if err != nil {
				return sheinImageUploadRecord{}, err
			}

			arg.imageBytes = newImageBytes

			// 如果需要转换为 png 格式，重新生成 imageName 和 imageType
			if isFormatToPNG {
				arg.imageName = uuid.GenerateUUIDV4() + ".png"
				arg.imageType = "image/png"
			}
		}
	}

	// shein api upload image
	response, err := s.sheinAPIService.UploadImage(ctx, &shein_proxy.UploadImageParams{
		CommonParams: shein_proxy.CommonParams{
			OrganizationID: arg.organization.ID,
			AppName:        consts.AppFeed,
			AppKey:         arg.salesChannel.StoreKey,
			ContentType:    commerceproxy.ContentTypeMultipartFormData,
		},
		UploadPicReq: rest.UploadPicReq{
			ImageType: types.MakeInt64(int64(convertSheinImageUseCaseToEnum(arg.imageUseCase))),
			File:      arg.imageBytes,
		},
		FileName: arg.imageName,
		FileType: arg.imageType,
	})
	if err != nil {
		return sheinImageUploadRecord{}, formatSHEINUploadAPIError(ctx, err)
	}

	return sheinImageUploadRecord{
		imageURI:     response.ImageURL.String(),
		imageURL:     response.ImageURL.String(),
		imageHeight:  int(response.Height.Int64()),
		imageWidth:   int(response.Width.Int64()),
		imageUseCase: arg.imageUseCase,
	}, nil
}

func formatSHEINUploadAPIError(ctx context.Context, err error) error {
	apiErr := common.ResponseError{}
	if errors.As(err, &apiErr) {
		if bodyByte, ok := apiErr.ReqBody.([]byte); ok {
			apiErr.ReqBody = nil
			if len(bodyByte) >= 10240 {
				log.GlobalLogger().WarnCtx(ctx, "Upload Resource failed, apiErr body too large",
					zap.Int("length", len(bodyByte)),
					zap.Int("code", apiErr.Status),
					zap.String("api response message", apiErr.Message),
				)
			}
			return apiErr
		}
	}
	errStr := err.Error()
	if strings.Contains(errStr, "1001001") {
		return ErrImageNotValidate
	}

	return errors.WithStack(err)
}
