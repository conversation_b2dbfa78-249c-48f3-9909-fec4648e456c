package convert

import (
	"bytes"
	"context"
	"encoding/base64"
	"fmt"
	"image"
	_ "image/color"
	_ "image/draw"
	_ "image/gif"
	_ "image/jpeg"
	_ "image/png"
	"net/http"
	"slices"
	"strings"
	"time"

	_ "golang.org/x/image/bmp"
	_ "golang.org/x/image/tiff"
	_ "golang.org/x/image/webp"

	jsoniter "github.com/json-iterator/go"
	"github.com/pkg/errors"
	"go.uber.org/zap"

	platform_api_v2_common "github.com/AfterShip/connectors-sdk-go/v2/common"
	"github.com/AfterShip/connectors-sdk-go/v2/image_upload_records"
	"github.com/AfterShip/go-sdk/converts"
	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/gopkg/log"

	"github.com/AfterShip/pltf-pd-product-listings/internal/utils/download_client"
	image_utils "github.com/AfterShip/pltf-pd-product-listings/internal/utils/image"
)

const (
	defaultEdgeSize = 900
)

func (s *serviceImpl) loadFileCache(ctx context.Context, arg *FileArg) (FileOutput, bool) {
	recordResp, err := s.imageUploadRecordService.GetInternalImageUploadRecords(ctx, image_upload_records.GetInternalImageUploadRecordsParams{
		OrganizationID: arg.Organization.ID,
		AppPlatform:    arg.SalesChannel.Platform,
		AppKey:         arg.SalesChannel.StoreKey,
		ImageUrl:       arg.FileOriginURL,
		ImageHash:      "0",
	})
	if err != nil {
		log.GlobalLogger().InfoCtx(ctx, "loadFileCache GetInternalImageUploadRecords error", zap.Error(err))
		return FileOutput{}, false
	}

	record, exist := getImageUploadRecord(recordResp)
	if !exist {
		return FileOutput{}, false
	}

	data := record.ExternalImageID.String()
	output := FileOutput{}
	if err := jsoniter.Unmarshal([]byte(data), &output); err != nil {
		log.GlobalLogger().InfoCtx(ctx, "loadFileCache json Unmarshal error",
			zap.String("data", data),
			zap.Error(err))
		return FileOutput{}, false
	}
	output.FileURL = record.ExternalImageUrl.String()
	return output, true
}

func getImageUploadRecord(recordResp *image_upload_records.GetInternalImageUploadRecordsResp) (image_upload_records.ModelsResponseRecord, bool) {
	if recordResp != nil && recordResp.Data != nil && len(recordResp.Data.ImageUploadRecords) > 0 {
		return recordResp.Data.ImageUploadRecords[0], true
	}

	return image_upload_records.ModelsResponseRecord{}, false
}

func is4XXStatusCode(statusCode int) bool {
	return statusCode == http.StatusNotFound ||
		statusCode == http.StatusBadRequest ||
		statusCode == http.StatusUnauthorized ||
		statusCode == http.StatusForbidden
}

func (s *serviceImpl) downloadFile(ctx context.Context, originURL string) ([]byte, error) {
	originURL = image_utils.FormatOriginURL(originURL)
	start := time.Now()
	resp, err := s.downloadImageCli.R().Get(originURL)
	if err != nil {
		if errors.Is(err, download_client.ErrorOnlyAllowHttps) {
			return nil, errors.WithStack(ErrorOnlyAllowHttps)
		}
		return nil, err
	}
	if resp.StatusCode() != http.StatusOK {
		if is4XXStatusCode(resp.StatusCode()) {
			return nil, errors.Wrap(ErrorDownLoadFileFromEcommerce4XX,
				fmt.Sprintf("status code:%v, url: %s", resp.StatusCode(), originURL))
		}
		return nil, errors.Wrap(ErrDownloadFileFromEcommerceFailed,
			fmt.Sprintf("status code:%v,  url: %s", resp.StatusCode(), originURL))
	}

	fileBytes := resp.Body()
	log.GlobalLogger().InfoCtx(ctx, "download file from eCommerce",
		zap.Duration("duration", time.Since(start)),
		zap.String("originURL", originURL),
		zap.Int("fileBytes len", len(fileBytes)),
	)
	return fileBytes, nil
}

func (s *serviceImpl) saveFileCache(ctx context.Context, arg *FileArg, output *FileOutput) error {
	recordByte, err := jsoniter.Marshal(output)
	if err != nil {
		log.GlobalLogger().InfoCtx(ctx, "saveFileCache json Marshal error",
			zap.Error(err))
		return err
	}

	// 把文件记录上传到 Connectors
	_, err = s.imageUploadRecordService.PostInternalImageUploadRecords(ctx, image_upload_records.PostInternalImageUploadRecordsReq{
		App: &platform_api_v2_common.ModelsApp{
			Key:      types.MakeString(arg.SalesChannel.StoreKey),
			Platform: types.MakeString(arg.SalesChannel.Platform),
		},
		Organization: &platform_api_v2_common.ModelsOrganization{
			ID: types.MakeString(arg.Organization.ID),
		},
		ImageUrl:         types.MakeString(arg.FileOriginURL),
		ImageHash:        types.MakeString("0"),
		ExternalImageUrl: types.MakeString(output.FileURL),
		ExternalImageID:  types.MakeString(string(recordByte)),
		CreatedAt:        types.MakeDatetime(time.Now()),
		UpdatedAt:        types.MakeDatetime(time.Now()),
	})

	if err != nil {
		log.GlobalLogger().InfoCtx(ctx, "saveFileCache PostInternalImageUploadRecords error",
			zap.Error(err))
		return errors.WithStack(err)
	}

	return nil
}

func getFileType(fileBytes []byte) string {
	fileTypeByMime, _ := image_utils.DetectFileMime(fileBytes)
	switch fileTypeByMime {
	case pdfFileFormat:
		return "application/pdf"
	case mp4FileFormat:
		return "video/mp4"
	default:
		return fileTypeByMime
	}
}

func getFileName(originURL string) string {
	files := strings.Split(originURL, "/")
	return files[len(files)-1]
}

func isCacheExpired(record image_upload_records.ModelsResponseRecord, cacheSeconds int64) bool {
	if cacheSeconds <= 0 {
		return false
	}

	updatedAt := record.UpdatedAt.Datetime().Unix()
	now := time.Now().Unix()

	return updatedAt+cacheSeconds < now
}

func (s *serviceImpl) loadImageCache(ctx context.Context, arg *ImageArg, cacheSeconds int64) (ImageOutput, bool) {
	// 获取图片上传记录
	recordResp, err := s.imageUploadRecordService.GetInternalImageUploadRecords(ctx, image_upload_records.GetInternalImageUploadRecordsParams{
		OrganizationID: arg.Organization.ID,
		AppPlatform:    arg.SalesChannel.Platform,
		AppKey:         arg.SalesChannel.StoreKey,
		ImageUrl:       arg.ImageOriginURL,
		ImageHash:      getImageHash(arg.ImageUseCase),
	})

	// 处理错误情况
	if err != nil {
		s.logger.InfoCtx(ctx, "loadImageCache GetInternalImageUploadRecords error",
			zap.Error(err))
		return ImageOutput{}, false
	}

	record, exist := getImageUploadRecord(recordResp)
	if !exist {
		return ImageOutput{}, false
	}

	// 验证图片尺寸是否有效
	if record.ImageHeight.Int() <= 0 || record.ImageWidth.Int() <= 0 {
		return ImageOutput{}, false
	}

	// 检查缓存是否过期
	if isCacheExpired(record, cacheSeconds) {
		s.logger.InfoCtx(ctx, "image cache expired",
			zap.Int64("cacheSeconds", cacheSeconds),
			zap.Int64("updatedAt", record.UpdatedAt.Datetime().Unix()),
			zap.String("organizationID", arg.Organization.ID),
			zap.String("originURL", arg.ImageOriginURL))
		return ImageOutput{}, false
	}

	// 返回缓存结果
	return ImageOutput{
		ImageURI:     record.ExternalImageID.String(),
		ImageURL:     record.ExternalImageUrl.String(),
		ImageHeight:  record.ImageHeight.Int(),
		ImageWidth:   record.ImageWidth.Int(),
		ImageUseCase: getImageUseCase(record.ImageHash.String()),
	}, true
}

func (s *serviceImpl) saveImageCache(ctx context.Context, arg *ImageArg, output *ImageOutput) error {
	_, err := s.imageUploadRecordService.PostInternalImageUploadRecords(ctx, image_upload_records.PostInternalImageUploadRecordsReq{
		App: &platform_api_v2_common.ModelsApp{
			Key:      types.MakeString(arg.SalesChannel.StoreKey),
			Platform: types.MakeString(arg.SalesChannel.Platform),
		},
		Organization: &platform_api_v2_common.ModelsOrganization{
			ID: types.MakeString(arg.Organization.ID),
		},
		ImageUrl:         types.MakeString(arg.ImageOriginURL),
		ImageHash:        types.MakeString(getImageHash(arg.ImageUseCase)),
		ExternalImageUrl: types.MakeString(output.ImageURL),
		ExternalImageID:  types.MakeString(output.ImageURI),
		CreatedAt:        types.MakeDatetime(time.Now()),
		UpdatedAt:        types.MakeDatetime(time.Now()),
		ImageWidth:       types.MakeInt(output.ImageWidth),
		ImageHeight:      types.MakeInt(output.ImageHeight),
	})
	return err
}

func getImageHash(imageUseCase string) string {
	// 防止缓存穿透，保留主图的缓存
	if imageUseCase == ImageUseCaseMainImage {
		return "0"
	}
	return imageUseCase
}

func getImageUseCase(imageHash string) string {
	if imageHash == "0" {
		return ImageUseCaseMainImage
	}

	return imageHash
}

func (s *serviceImpl) imageTransform(ctx context.Context, req converts.ImageRequest) ([]byte, error) {
	imageBytes, err := s.convert.ConvertImage(ctx, req)
	if err != nil {
		return nil, s.responseImageServiceError(err)
	}
	return imageBytes, nil
}

func handleImageResize(arg *transformImageArg) (bool, bool, []converts.Instruction) {
	if !arg.needResize {
		return false, false, nil
	}

	imgConfig, err := decodeImageConfig(arg.imageBytes, arg.imageOriginURL)
	if err != nil {
		return handleDefaultResize(arg)
	}

	return processImageResize(imgConfig.Width, imgConfig.Height, arg)
}

func handleDefaultResize(arg *transformImageArg) (bool, bool, []converts.Instruction) {
	instructions := make([]converts.Instruction, 0)
	isFormatToPNG := false
	// 添加方形调整指令
	if arg.needSquare {
		isFormatToPNG = true
		instructions = append(instructions,
			converts.Instruction{
				Operation: converts.OperationFormat,
				Option:    &converts.FormatOption{Format: "png"},
			},
			createResizeInstruction(defaultEdgeSize, defaultEdgeSize, arg.needTransparent),
		)

		return true, isFormatToPNG, instructions
	}

	// 添加强制缩放指令
	if arg.forceReducedSize {
		width, height := arg.forceReducedWidth, arg.forceReducedHeight
		if width == 0 || height == 0 {
			width, height = defaultEdgeSize, defaultEdgeSize
		}
		isFormatToPNG = true
		instructions = append(instructions,
			converts.Instruction{
				Operation: converts.OperationFormat,
				Option:    &converts.FormatOption{Format: "png"},
			},
			createResizeInstruction(width, height, arg.needTransparent),
		)

		return true, isFormatToPNG, instructions
	}

	return false, false, nil
}

func processImageResize(imgWidth, imgHeight int, arg *transformImageArg) (bool, bool, []converts.Instruction) {
	needResize, edgeSize, imageWidth, imageHeight := needResizeImage(
		imgWidth, imgHeight, arg.minSize, arg.maxSize, arg.needSquare, arg.aspectRatioRange)

	if !needResize {
		return false, false, nil
	}

	var instructions []converts.Instruction
	isFormatToPNG := false

	// 处理方形调整
	if arg.needSquare {
		isFormatToPNG = true
		instructions = append(instructions,
			converts.Instruction{
				Operation: converts.OperationFormat,
				Option:    &converts.FormatOption{Format: "png"},
			},
			createResizeInstruction(edgeSize, edgeSize, arg.needTransparent),
		)

		return true, isFormatToPNG, instructions
	}

	// 处理强制缩放
	if arg.forceReducedSize {
		isFormatToPNG = true
		width, height := calculateForcedDimensions(imageWidth, imageHeight, arg)
		instructions = append(instructions,
			converts.Instruction{
				Operation: converts.OperationFormat,
				Option:    &converts.FormatOption{Format: "png"},
			},
			createResizeInstruction(width, height, arg.needTransparent),
		)

		return true, isFormatToPNG, instructions
	}

	return false, false, nil
}

func calculateForcedDimensions(imageWidth, imageHeight int, arg *transformImageArg) (int64, int64) {
	width, height := arg.forceReducedWidth, arg.forceReducedHeight

	if !isAspectRatioValid(imageWidth, imageHeight, arg.aspectRatioRange) {
		if width == 0 || height == 0 {
			return int64(arg.minSize), int64(arg.minSize)
		}
		return width, height
	}

	aspectRatio := float64(imageWidth) / float64(imageHeight)
	if imageWidth < arg.minSize {
		width = int64(arg.minSize)
		height = int64(float64(width) / aspectRatio)
	} else if imageHeight < arg.minSize {
		height = int64(arg.minSize)
		width = int64(float64(height) * aspectRatio)
	}

	return width, height
}

func createResizeInstruction(width, height int64, needTransparent bool) converts.Instruction {
	// 使用新的 `converts.ExtentOption` 来处理宽高调整
	return converts.Instruction{
		Operation: converts.OperationExtent,
		Option: &converts.ExtentOption{
			Width:           width,
			Height:          height,
			BackgroundColor: getBackgroundColor(needTransparent),
		},
	}
}

func getBackgroundColor(needTransparent bool) converts.Color {
	alpha := uint8(255)
	if needTransparent {
		alpha = 0
	}

	return converts.Color{
		R: 255,
		G: 255,
		B: 255,
		A: alpha,
	}
}

func handleImageFormatConversion(arg *transformImageArg, isFormatToPNG bool) []converts.Instruction {
	if isFormatToPNG || slices.Contains(arg.needImageFormats, strings.ToLower(arg.imageType)) {
		return nil
	}

	return []converts.Instruction{
		{
			Operation: converts.OperationFormat,
			Option:    &converts.FormatOption{Format: "jpeg"},
		},
	}
}

func handleImageCompression(arg *transformImageArg, needTransform bool) []converts.Instruction {
	if !needTransform && len(arg.imageBytes) <= arg.maxFileSize {
		return nil
	}

	return []converts.Instruction{
		{
			Operation: converts.OperationCompress,
			Option:    &converts.CompressOption{Size: int64(arg.maxFileSize)},
		},
	}
}

func (s *serviceImpl) buildConvertRequest(arg *transformImageArg, instructions []converts.Instruction) converts.ImageRequest {
	convertArg := converts.ImageRequest{
		Instructions: instructions,
	}

	// 如果图片大小超过 10M，使用 URL 进行转换
	if len(arg.imageBytes) > 10000000 && arg.imageOriginURL != "" {
		convertArg.ImageURL = arg.imageOriginURL
	} else {
		convertArg.ImageData = base64.StdEncoding.EncodeToString(arg.imageBytes)
	}

	// 设置图片扩展名
	imageType := strings.Split(arg.imageType, "/")
	if len(imageType) > 1 {
		convertArg.ImageExt = imageType[1]
	} else {
		convertArg.ImageExt = "jpeg"
	}

	// 清理内存
	arg.imageBytes = nil

	return convertArg
}

func (s *serviceImpl) isImageNeedTransform(arg *transformImageArg) (bool, bool, converts.ImageRequest) {
	allInstructions := make([]converts.Instruction, 0)
	needTransform := false
	isFormatToPNG := false

	// 处理图片尺寸调整
	resizeNeeded, isPNG, resizeInstructions := handleImageResize(arg)
	if resizeNeeded {
		needTransform = true
		isFormatToPNG = isPNG
		allInstructions = append(allInstructions, resizeInstructions...)
	}

	// 处理图片格式转换
	if formatInstructions := handleImageFormatConversion(arg, isFormatToPNG); formatInstructions != nil {
		needTransform = true
		allInstructions = append(allInstructions, formatInstructions...)
	}

	// 处理图片压缩
	if compressInstructions := handleImageCompression(arg, needTransform); compressInstructions != nil {
		needTransform = true
		allInstructions = append(allInstructions, compressInstructions...)
	}

	// 构建转换请求
	var convertRequest converts.ImageRequest
	if needTransform {
		convertRequest = s.buildConvertRequest(arg, allInstructions)
	}

	return needTransform, isFormatToPNG, convertRequest
}

func decodeImageConfig(imageBytes []byte, imageUrl string) (image.Config, error) {
	imgConfig, _, err := image.DecodeConfig(bytes.NewReader(imageBytes))
	if err != nil {
		log.GlobalLogger().Warn("decode image error", zap.String("image_url", imageUrl), zap.Error(err))
		return image.Config{}, err
	}
	return imgConfig, nil
}

func calculateEdgeSize(imgWidth, imgHeight, minSize, maxSize int) int64 {
	if imgWidth < minSize && imgHeight < minSize {
		return int64(minSize)
	}
	if imgWidth > maxSize || imgHeight > maxSize {
		return int64(maxSize)
	}
	if imgWidth < minSize {
		return int64(imgHeight)
	}
	return int64(imgWidth)
}

func needSquareResize(imgWidth, imgHeight int, needSquare bool) (bool, int64) {
	if needSquare && imgWidth != imgHeight {
		if imgHeight > imgWidth {
			return true, int64(imgHeight)
		}
		return true, int64(imgWidth)
	}
	return false, 0
}

func needResizeImage(imgWidth, imgHeight, minSize, maxSize int, needSquare bool, aspectRatioRange [2]float64) (bool, int64, int, int) {
	if isImageSizeValid(imgWidth, imgHeight, minSize, maxSize) &&
		isAspectRatioValid(imgWidth, imgHeight, aspectRatioRange) {
		// 符合尺寸的图片, 检查是否需要方形处理
		if needSquare, edgeSize := needSquareResize(imgWidth, imgHeight, needSquare); needSquare {
			return true, edgeSize, imgWidth, imgHeight
		}
		return false, 0, imgWidth, imgHeight
	}

	// 不符合尺寸的图片需要调整
	edgeSize := calculateEdgeSize(imgWidth, imgHeight, minSize, maxSize)
	return true, edgeSize, imgWidth, imgHeight
}

func isAspectRatioValid(width, height int, aspectRatioRange [2]float64) bool {
	if aspectRatioRange[0] <= 0 || aspectRatioRange[1] <= 0 {
		return true
	}

	if width <= 0 || height <= 0 {
		return false
	}

	aspectRatio := float64(width) / float64(height)
	return aspectRatio >= aspectRatioRange[0] && aspectRatio <= aspectRatioRange[1]
}

func validateImageUseCase(useCase string) error {
	switch useCase {
	case ImageUseCaseMainImage, ImageUseCaseAttributeImage, ImageUseCaseDescriptionImage, ImageUseCaseCertificationImage, ImageUseCaseSizeChartImage:
		return nil
	case ImageUseCaseMain, ImageUseCaseDetail, ImageUseCaseSquare, ImageUseCasePiece, ImageUseCaseSpecific:
		return nil
	default:
		return ErrorInvalidImageUseCase
	}
}

func isImageSizeValid(width, height, minSize, maxSize int) bool {
	return width >= minSize && width <= maxSize && height >= minSize && height <= maxSize
}

func (s *serviceImpl) responseImageServiceError(err error) error {
	if errors.Is(err, converts.ErrInvalidArgument) ||
		errors.Is(err, converts.ErrMissingImageDataOrURL) ||
		errors.Is(err, converts.ErrMissingImageExt) ||
		errors.Is(err, converts.ErrMissingInstructions) {
		return errors.Wrap(ErrorConvertImageService422Error, err.Error())
	}
	if strings.Contains(err.Error(), "429") {
		return errors.Wrap(ErrorConvertImageService429Error, err.Error())
	}
	if strings.Contains(err.Error(), "413") {
		return errors.Wrap(ErrorConvertImageService413Error, err.Error())
	}
	return errors.Wrap(ErrorConvertImageServiceError, err.Error())
}

// 将 shein image usecase 转为 int枚举
func convertSheinImageUseCaseToEnum(useCase string) int {
	switch useCase {
	case ImageUseCaseMain:
		return 1
	case ImageUseCaseDetail:
		return 2
	case ImageUseCaseSquare:
		return 5
	case ImageUseCasePiece:
		return 6
	case ImageUseCaseSpecific:
		return 7
	default:
		return 1
	}
}
