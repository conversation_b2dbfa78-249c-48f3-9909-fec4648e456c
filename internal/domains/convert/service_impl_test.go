package convert

import (
	"context"
	"fmt"
	"regexp"
	"strconv"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"github.com/AfterShip/connectors-sdk-go/v2/image_upload_records"
	"github.com/AfterShip/go-sdk/converts"
	gopkg_client "github.com/AfterShip/gopkg/api/client"
	"github.com/AfterShip/gopkg/log"
	"github.com/AfterShip/pltf-pd-product-listings/internal/config"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
	"github.com/AfterShip/pltf-pd-product-listings/internal/third_party/connectors"
	tiktokapi "github.com/AfterShip/pltf-pd-product-listings/internal/third_party/tiktok_api"
)

func Test_serviceImpl_ConvertImage(t *testing.T) {
	type fields struct {
		config                   *config.Config
		logger                   *log.Logger
		tiktokAPIService         tiktokapi.Service
		imageUploadRecordService image_upload_records.ImageUploadRecordsSvc
		downloadImageCli         *gopkg_client.Client
		convert                  *converts.Convert
		connectorsClient         connectors.Service
	}
	type args struct {
		ctx context.Context
		arg *ImageArg
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    ImageOutput
		wantErr assert.ErrorAssertionFunc
	}{
		// TODO: Add test cases.
		{
			name: "Test with sales channel URL",
			fields: fields{
				config: &config.Config{},
			},
			args: args{
				ctx: context.Background(),
				arg: &ImageArg{
					Organization: &models.Organization{
						ID: "organization_id",
					},
					SalesChannel: &models.SalesChannel{
						StoreKey:      "store_key",
						Platform:      "tiktok-shop",
						CountryRegion: "",
					},
					ImageUseCase:    "MAIN_IMAGE",
					ImageName:       "",
					ImageType:       "",
					ImageBytes:      nil,
					ImageOriginURL:  "https://p19-oec-eu-common-useast2a.ibyteimg.com/tos-useast2a-i-tulkllf4y5-euttp/dd313b3db2734b64a4e2fb545f24dd9b~tplv-tulkllf4y5-origin-jpeg.jpeg?from=520841845",
					IgnoreCache:     false,
					IgnoreTransform: false,
				},
			},
			want: ImageOutput{
				ImageHeight:      0,
				ImageURI:         "tos-useast2a-i-tulkllf4y5-euttp/dd313b3db2734b64a4e2fb545f24dd9b",
				ImageURL:         "https://p19-oec-eu-common-useast2a.ibyteimg.com/tos-useast2a-i-tulkllf4y5-euttp/dd313b3db2734b64a4e2fb545f24dd9b~tplv-tulkllf4y5-origin-jpeg.jpeg?from=520841845",
				ImageUseCase:     "MAIN_IMAGE",
				ImageWidth:       0,
				FromSalesChannel: true,
			},
			wantErr: assert.NoError,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockConnectorService := new(connectors.MockService)
			mockConnectorService.On("GetBothConnections", mock.Anything, mock.Anything).
				Return(connectors.BothConnections{
					OrganizationID: "organization_id",
					App: models.App{
						Platform: "shopify",
						Key:      "s_1",
					},
					Channels: []models.App{
						{
							Platform: "tiktok-shop",
							Key:      "store_key",
						},
					},
				}, nil)

			s := &serviceImpl{
				config:                   tt.fields.config,
				logger:                   tt.fields.logger,
				tiktokAPIService:         tt.fields.tiktokAPIService,
				imageUploadRecordService: tt.fields.imageUploadRecordService,
				downloadImageCli:         tt.fields.downloadImageCli,
				convert:                  tt.fields.convert,
				connectorsClient:         mockConnectorService,
			}
			got, err := s.ConvertImage(tt.args.ctx, tt.args.arg)
			if !tt.wantErr(t, err, fmt.Sprintf("ConvertImage(%v, %v)", tt.args.ctx, tt.args.arg)) {
				return
			}
			assert.Equalf(t, tt.want, got, "ConvertImage(%v, %v)", tt.args.ctx, tt.args.arg)
		})
	}
}

func Test_extractURLVersionTimestamp(t *testing.T) {
	tests := []struct {
		name          string
		url           string
		wantTimestamp int64
		wantSuccess   bool
	}{
		{
			name:          "正常URL带版本参数",
			url:           "https://example.com/image.jpg?v=1634567890",
			wantTimestamp: 1634567890,
			wantSuccess:   true,
		},
		{
			name:          "带版本参数和其他参数",
			url:           "https://example.com/image.jpg?v=1634567890&width=200",
			wantTimestamp: 1634567890,
			wantSuccess:   true,
		},
		{
			name:          "版本参数在中间,不提取",
			url:           "https://example.com/image.jpg?size=large&v=1634567890&format=webp",
			wantTimestamp: 0,
			wantSuccess:   false,
		},
		{
			name:          "不带版本参数",
			url:           "https://example.com/image.jpg",
			wantTimestamp: 0,
			wantSuccess:   false,
		},
		{
			name:          "版本参数不是数字",
			url:           "https://example.com/image.jpg?v=abc",
			wantTimestamp: 0,
			wantSuccess:   false,
		},
		{
			name:          "空URL",
			url:           "",
			wantTimestamp: 0,
			wantSuccess:   false,
		},
		{
			name:          "特殊URL格式",
			url:           "https://p19-oec-eu-common-useast2a.ibyteimg.com/tos-useast2a-i-tulkllf4y5-euttp/dd313b3db2734b64a4e2fb545f24dd9b~tplv-tulkllf4y5-origin-jpeg.jpeg?v=1634567890&from=520841845",
			wantTimestamp: 1634567890,
			wantSuccess:   true,
		},
		{
			name:          "带多个v参数",
			url:           "https://example.com/image.jpg?v=1634567890&other=v=123",
			wantTimestamp: 1634567890,
			wantSuccess:   true,
		},
		{
			name:          "超大数字版本",
			url:           "https://example.com/image.jpg?v=9223372036854775807", // Int64 最大值
			wantTimestamp: 9223372036854775807,
			wantSuccess:   true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotTimestamp, gotSuccess := extractURLVersionTimestamp(tt.url)
			assert.Equal(t, tt.wantSuccess, gotSuccess, "提取结果状态不匹配")
			assert.Equal(t, tt.wantTimestamp, gotTimestamp, "提取的时间戳不匹配")
		})
	}
}

func Test_calculateCacheTimestamp(t *testing.T) {
	// 获取当前时间，用于验证
	now := time.Now()

	tests := []struct {
		name         string
		cacheSeconds int64
		expectedTime time.Time // 使用相对时间来测试
	}{
		{
			name:         "缓存0秒",
			cacheSeconds: 0,
			expectedTime: now,
		},
		{
			name:         "缓存1小时",
			cacheSeconds: 3600,
			expectedTime: now.Add(-time.Hour),
		},
		{
			name:         "缓存1天",
			cacheSeconds: 86400,
			expectedTime: now.Add(-24 * time.Hour),
		},
		{
			name:         "缓存7天",
			cacheSeconds: 604800,
			expectedTime: now.Add(-7 * 24 * time.Hour),
		},
		{
			name:         "缓存30天",
			cacheSeconds: 2592000,
			expectedTime: now.Add(-30 * 24 * time.Hour),
		},
		{
			name:         "负数缓存时间",
			cacheSeconds: -3600,
			expectedTime: now.Add(time.Hour),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 调用函数获取结果
			got := calculateCacheTimestamp(tt.cacheSeconds)

			// 重新计算预期结果(在测试执行时)
			expectedTimestamp := tt.expectedTime.Unix()

			// 允许有1秒的误差，因为测试执行期间可能有时间差
			timeDiff := got - expectedTimestamp
			assert.LessOrEqual(t, timeDiff, int64(1), "计算的缓存时间戳误差过大")
			assert.GreaterOrEqual(t, timeDiff, int64(-1), "计算的缓存时间戳误差过大")
		})
	}
}

func Test_removeQueryParams(t *testing.T) {
	tests := []struct {
		name string
		url  string
		want string
	}{
		{
			name: "带查询参数的标准URL",
			url:  "https://example.com/image.jpg?v=1634567890",
			want: "https://example.com/image.jpg",
		},
		{
			name: "带多个查询参数的URL",
			url:  "https://example.com/image.jpg?v=1634567890&width=200&height=300",
			want: "https://example.com/image.jpg",
		},
		{
			name: "不带查询参数的URL",
			url:  "https://example.com/image.jpg",
			want: "https://example.com/image.jpg",
		},
		{
			name: "包含多个问号的URL",
			url:  "https://example.com/image.jpg?v=123?width=200",
			want: "https://example.com/image.jpg",
		},
		{
			name: "查询参数为空的URL",
			url:  "https://example.com/image.jpg?",
			want: "https://example.com/image.jpg",
		},
		{
			name: "空URL",
			url:  "",
			want: "",
		},
		{
			name: "只有查询参数的URL",
			url:  "?v=1234",
			want: "?v=1234",
		},
		{
			name: "特殊CDN图片URL",
			url:  "https://p19-oec-eu-common-useast2a.ibyteimg.com/tos-useast2a-i-tulkllf4y5-euttp/dd313b3db2734b64a4e2fb545f24dd9b~tplv-tulkllf4y5-origin-jpeg.jpeg?v=1634567890&from=520841845",
			want: "https://p19-oec-eu-common-useast2a.ibyteimg.com/tos-useast2a-i-tulkllf4y5-euttp/dd313b3db2734b64a4e2fb545f24dd9b~tplv-tulkllf4y5-origin-jpeg.jpeg",
		},
		{
			name: "带路径参数和片段的URL",
			url:  "https://example.com/path/to/image.jpg?v=123#section",
			want: "https://example.com/path/to/image.jpg",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := removeQueryParams(tt.url)
			assert.Equal(t, tt.want, got, "移除查询参数结果不匹配")
		})
	}
}

func Test_serviceImpl_adjustImageURLVersion(t *testing.T) {
	// 创建模拟配置
	defaultCacheSeconds := int64(3600) // 1小时
	mockConfig := &config.Config{
		DynamicConfigs: &config.DynamicConfigs{
			ImageCacheConfig: []config.ImageCacheConfig{
				{
					OrganizationID: "org1",
					CacheSeconds:   defaultCacheSeconds,
				},
				{
					OrganizationID: "org2",
					CacheSeconds:   86400, // 1天
				},
				{
					OrganizationID: "org3",
					CacheSeconds:   0, // 禁用缓存
				},
			},
		},
	}

	// 创建测试用的服务实例
	service := &serviceImpl{
		config: mockConfig,
		logger: log.GlobalLogger(),
	}

	// 获取固定的当前时间用于测试
	nowTime := time.Now()
	future := nowTime.Add(time.Hour * 24).Unix() // 未来24小时
	past := nowTime.Add(-time.Hour * 48).Unix()  // 过去48小时

	tests := []struct {
		name           string
		arg            *ImageArg
		wantCacheTime  int64
		wantCleanedURL bool
	}{
		{
			name: "空URL不处理",
			arg: &ImageArg{
				Organization:   &models.Organization{ID: "org1"},
				ImageOriginURL: "",
			},
			wantCacheTime:  0,
			wantCleanedURL: false,
		},
		{
			name: "URL没有版本参数不处理",
			arg: &ImageArg{
				Organization:   &models.Organization{ID: "org1"},
				ImageOriginURL: "https://example.com/image.jpg",
			},
			wantCacheTime:  0,
			wantCleanedURL: false,
		},
		{
			name: "组织无缓存配置不处理",
			arg: &ImageArg{
				Organization:   &models.Organization{ID: "unknown-org"},
				ImageOriginURL: "https://example.com/image.jpg?v=1634567890",
			},
			wantCacheTime:  0,
			wantCleanedURL: false,
		},
		{
			name: "组织禁用缓存不处理",
			arg: &ImageArg{
				Organization:   &models.Organization{ID: "org3"},
				ImageOriginURL: "https://example.com/image.jpg?v=1634567890",
			},
			wantCacheTime:  0,
			wantCleanedURL: false,
		},
		{
			name: "版本时间戳低于缓存期限不处理",
			arg: &ImageArg{
				Organization:   &models.Organization{ID: "org1"},
				ImageOriginURL: "https://example.com/image.jpg?v=" + strconv.FormatInt(past, 10),
			},
			wantCacheTime:  0,
			wantCleanedURL: false,
		},
		{
			name: "版本时间戳高于缓存期限移除查询参数",
			arg: &ImageArg{
				Organization:   &models.Organization{ID: "org1"},
				ImageOriginURL: "https://example.com/image.jpg?v=" + strconv.FormatInt(future, 10),
			},
			wantCacheTime:  defaultCacheSeconds,
			wantCleanedURL: true,
		},
		{
			name: "多个查询参数时正确移除",
			arg: &ImageArg{
				Organization:   &models.Organization{ID: "org1"},
				ImageOriginURL: "https://example.com/image.jpg?v=" + strconv.FormatInt(future, 10) + "&width=200",
			},
			wantCacheTime:  defaultCacheSeconds,
			wantCleanedURL: true,
		},
		{
			name: "不同组织使用对应的缓存配置",
			arg: &ImageArg{
				Organization:   &models.Organization{ID: "org2"},
				ImageOriginURL: "https://example.com/image.jpg?v=" + strconv.FormatInt(future, 10),
			},
			wantCacheTime:  86400,
			wantCleanedURL: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 记录原始URL以便比较
			originalURL := tt.arg.ImageOriginURL

			// 执行测试方法
			got := service.adjustImageURLVersion(tt.arg)

			// 验证返回的缓存时间
			assert.Equal(t, tt.wantCacheTime, got, "返回的缓存秒数不匹配")

			// 检查URL是否如预期被修改
			if tt.wantCleanedURL {
				cleanURL := removeQueryParams(originalURL)
				assert.Equal(t, cleanURL, tt.arg.ImageOriginURL, "清理后的URL不匹配")
			} else {
				assert.Equal(t, originalURL, tt.arg.ImageOriginURL, "URL不应被修改")
			}
		})
	}
}

func Test_serviceImpl_getTiktokCDNDomainRegexps(t *testing.T) {
	tests := []struct {
		name           string
		config         *config.Config
		expectedLength int
		expectedRegexs []*regexp.Regexp
	}{
		{
			name: "配置为空",
			config: &config.Config{
				DynamicConfigs: nil,
			},
			expectedLength: 0,
			expectedRegexs: []*regexp.Regexp{},
		},
		{
			name: "动态配置为空",
			config: &config.Config{
				DynamicConfigs: &config.DynamicConfigs{
					TTSCDNDomainRegExps: nil,
				},
			},
			expectedLength: 0,
			expectedRegexs: []*regexp.Regexp{},
		},
		{
			name: "有配置的域名正则表达式",
			config: &config.Config{
				DynamicConfigs: &config.DynamicConfigs{
					TTSCDNDomainRegExps: []*regexp.Regexp{
						regexp.MustCompile(`^https://p\d+-.*\.ibyteimg\.com/.*$`),
						regexp.MustCompile(`^https://s\d+-.*\.tiktokcdn\.com/.*$`),
					},
				},
			},
			expectedLength: 2,
			expectedRegexs: []*regexp.Regexp{
				regexp.MustCompile(`^https://p\d+-.*\.ibyteimg\.com/.*$`),
				regexp.MustCompile(`^https://s\d+-.*\.tiktokcdn\.com/.*$`),
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建服务实例
			s := &serviceImpl{
				config: tt.config,
				logger: log.GlobalLogger(),
			}

			// 调用被测试的方法
			result := s.getTiktokCDNDomainRegexps()

			// 验证结果长度
			assert.Equal(t, tt.expectedLength, len(result), "返回的正则表达式数组长度不匹配")

			// 验证每个正则表达式
			if tt.expectedLength > 0 {
				for i, expectedRegex := range tt.expectedRegexs {
					// 比较正则表达式字符串，因为无法直接比较正则表达式对象
					assert.Equal(t, expectedRegex.String(), result[i].String(), "正则表达式不匹配")

					// 验证正则表达式功能正常
					testURL := ""
					switch i {
					case 0:
						testURL = "https://p16-amd-va.tiktokcdn.com/img/tos-useast2a-v-2774/abc123~tplv-dmt-logom:tos-useast2a-pv-0000.image"
					case 1:
						testURL = "https://s16-ies.tiktokcdn.com/obj/tos-alisg-v-2774/abc123"
					}

					if testURL != "" {
						expectedMatch := expectedRegex.MatchString(testURL)
						actualMatch := result[i].MatchString(testURL)
						assert.Equal(t, expectedMatch, actualMatch, "正则表达式匹配结果不一致")
					}
				}
			}
		})
	}
}

func Test_serviceImpl_getSheinCDNDomainRegexps(t *testing.T) {
	tests := []struct {
		name           string
		config         *config.Config
		expectedLength int
		expectedRegexs []*regexp.Regexp
	}{
		{
			name: "配置为空",
			config: &config.Config{
				DynamicConfigs: nil,
			},
			expectedLength: 0,
			expectedRegexs: []*regexp.Regexp{},
		},
		{
			name: "动态配置为空",
			config: &config.Config{
				DynamicConfigs: &config.DynamicConfigs{
					SheinCDNDomainRegExps: nil,
				},
			},
			expectedLength: 0,
			expectedRegexs: []*regexp.Regexp{},
		},
		{
			name: "有配置的域名正则表达式",
			config: &config.Config{
				DynamicConfigs: &config.DynamicConfigs{
					SheinCDNDomainRegExps: []*regexp.Regexp{
						regexp.MustCompile(`^https://img\.sheincorp\.com/.*$`),
						regexp.MustCompile(`^https://img\.shein\.com/.*$`),
					},
				},
			},
			expectedLength: 2,
			expectedRegexs: []*regexp.Regexp{
				regexp.MustCompile(`^https://img\.sheincorp\.com/.*$`),
				regexp.MustCompile(`^https://img\.shein\.com/.*$`),
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建服务实例
			s := &serviceImpl{
				config: tt.config,
				logger: log.GlobalLogger(),
			}

			// 调用被测试的方法
			result := s.getSheinCDNDomainRegexps()

			// 验证结果长度
			assert.Equal(t, tt.expectedLength, len(result), "返回的正则表达式数组长度不匹配")

			// 验证每个正则表达式
			if tt.expectedLength > 0 {
				for i, expectedRegex := range tt.expectedRegexs {
					// 比较正则表达式字符串，因为无法直接比较正则表达式对象
					assert.Equal(t, expectedRegex.String(), result[i].String(), "正则表达式不匹配")

					// 验证正则表达式功能正常
					testURL := ""
					switch i {
					case 0:
						testURL = "https://img.sheincorp.com/images/product/abc123.jpg"
					case 1:
						testURL = "https://img.shein.com/images/product/xyz456.jpg"
					}

					if testURL != "" {
						expectedMatch := expectedRegex.MatchString(testURL)
						actualMatch := result[i].MatchString(testURL)
						assert.Equal(t, expectedMatch, actualMatch, "正则表达式匹配结果不一致")
					}
				}
			}
		})
	}
}
