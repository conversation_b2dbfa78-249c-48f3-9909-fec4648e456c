package convert

import (
	"context"
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/AfterShip/connectors-ecommerce-sdk-go/common"
)

func TestValidateImageUseCase_Shein_and_TTS(t *testing.T) {
	tests := []struct {
		useCase string
		wantErr bool
	}{
		{ImageUseCaseMainImage, false},
		{ImageUseCaseAttributeImage, false},
		{ImageUseCaseDescriptionImage, false},
		{ImageUseCaseCertificationImage, false},
		{ImageUseCaseSizeChartImage, false},
		{ImageUseCaseMain, false},
		{ImageUseCaseDetail, false},
		{ImageUseCaseSquare, false},
		{ImageUseCasePiece, false},
		{ImageUseCaseSpecific, false},
		{"invalidUseCase", true},
	}

	for _, tt := range tests {
		err := validateImageUseCase(tt.useCase)
		if tt.wantErr {
			assert.Error(t, err, "Expected error for use case: %v", tt.useCase)
		} else {
			assert.NoError(t, err, "Did not expect error for use case: %v", tt.useCase)
		}
	}
}

func TestFormatSHEINUploadAPIError(t *testing.T) {
	ctx := context.Background()

	// Test ResponseError with large request body
	t.Run("ResponseError with large request body", func(t *testing.T) {
		apiErr := common.ResponseError{
			Status:  400,
			Message: "Bad Request",
			ReqBody: make([]byte, 10240), // Create request body >= 10240 bytes
		}

		formattedErr := formatSHEINUploadAPIError(ctx, apiErr)
		assert.NotNil(t, formattedErr)
		assert.Equal(t, apiErr.Status, formattedErr.(common.ResponseError).Status)
		assert.Equal(t, apiErr.Message, formattedErr.(common.ResponseError).Message)
		assert.Nil(t, formattedErr.(common.ResponseError).ReqBody, "ReqBody should be cleared")
	})

	// Test ResponseError with normal size request body
	t.Run("ResponseError with normal request body", func(t *testing.T) {
		apiErr := common.ResponseError{
			Status:  400,
			Message: "Bad Request",
			ReqBody: make([]byte, 1000),
		}

		formattedErr := formatSHEINUploadAPIError(ctx, apiErr)
		assert.NotNil(t, formattedErr)
		assert.Equal(t, apiErr.Status, formattedErr.(common.ResponseError).Status)
		assert.Equal(t, apiErr.Message, formattedErr.(common.ResponseError).Message)
		assert.Nil(t, formattedErr.(common.ResponseError).ReqBody, "ReqBody should be cleared")
	})

	// Test image validation error
	t.Run("Image validation error", func(t *testing.T) {
		err := errors.New("error message containing code 1001001")
		formattedErr := formatSHEINUploadAPIError(ctx, err)
		assert.Equal(t, ErrImageNotValidate, formattedErr)
	})

	// Test general error
	t.Run("General error", func(t *testing.T) {
		err := errors.New("generic error")
		formattedErr := formatSHEINUploadAPIError(ctx, err)
		assert.NotNil(t, formattedErr)
		assert.True(t, errors.Is(formattedErr, err), "Should contain original error")
	})
}

func TestIsUSSheinStoreKey(t *testing.T) {
	tests := []struct {
		name     string
		storeKey string
		want     bool
	}{
		{
			name:     "Valid US Shein store key",
			storeKey: "12345|shein-us",
			want:     true,
		},
		{
			name:     "Valid US Shein store key with different prefix",
			storeKey: "abc123|shein-us",
			want:     true,
		},
		{
			name:     "Non-US Shein store key",
			storeKey: "12345|shein-uk",
			want:     false,
		},
		{
			name:     "Non-US Shein store key with different suffix",
			storeKey: "12345|shein-ca",
			want:     false,
		},
		{
			name:     "Empty store key",
			storeKey: "",
			want:     false,
		},
		{
			name:     "Store key without separator",
			storeKey: "12345shein-us",
			want:     false,
		},
		{
			name:     "Store key with only suffix",
			storeKey: "|shein-us",
			want:     true,
		},
		{
			name:     "Store key with partial match",
			storeKey: "12345|shein-usa",
			want:     false,
		},
		{
			name:     "Store key with case sensitivity",
			storeKey: "12345|SHEIN-US",
			want:     false,
		},
		{
			name:     "Store key with different platform",
			storeKey: "12345|amazon-us",
			want:     false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := isUSSheinStoreKey(tt.storeKey)
			assert.Equal(t, tt.want, result, "isUSSheinStoreKey(%q) = %v, want %v", tt.storeKey, result, tt.want)
		})
	}
}
