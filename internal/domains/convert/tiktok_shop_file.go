package convert

import (
	"context"
	"strings"

	tiktokres_v202309 "github.com/AfterShip/connectors-ecommerce-sdk-go/tiktok/rest/version202309"
	standard_error "github.com/AfterShip/connectors-errors-sdk-go"
	commerceproxy "github.com/AfterShip/connectors-library/sdks/commerce_proxy"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
	tiktokapi "github.com/AfterShip/pltf-pd-product-listings/internal/third_party/tiktok_api"
)

type tiktokFileArg struct {
	organization *models.Organization
	salesChannel *models.SalesChannel
	fileBytes    []byte
	fileName     string
	fileType     string
}

type tiktokFileUploadRecord struct {
	fileID   string
	fileURL  string
	fileName string
	fileType string
}

func (r *tiktokFileUploadRecord) convertToFileOutput() FileOutput {
	return FileOutput{
		FileID:     r.fileID,
		FileURL:    r.fileURL,
		FileName:   r.fileName,
		FileFormat: r.fileType,
	}
}

func (s *serviceImpl) convert2TTSFile(ctx context.Context, arg *tiktokFileArg) (tiktokFileUploadRecord, error) {
	if len(arg.fileBytes) > 20000000 {
		return tiktokFileUploadRecord{}, standard_error.WFPTTSCreateProductMP4TooBig_603422801
	}
	file, err := s.uploadFile(ctx, arg)
	if err != nil {
		return tiktokFileUploadRecord{}, formatTTSUploadAPIError(ctx, err)
	}
	if err = validateFile(file.Format.String(), len(arg.fileBytes)); err != nil {
		return tiktokFileUploadRecord{}, err
	}
	result := tiktokFileUploadRecord{
		fileID:   file.Id.String(),
		fileURL:  file.Url.String(),
		fileName: file.Name.String(),
		fileType: file.Format.String(),
	}
	return result, nil
}

func validateFile(fileFormat string, fileLength int) error {
	switch strings.ToLower(fileFormat) {
	case mp4FileFormat:
		if fileLength > 20000000 {
			return standard_error.WFPTTSCreateProductMP4TooBig_603422801
		}
	case pdfFileFormat:
		if fileLength > 10000000 {
			return standard_error.WFPTTSCreateProductPDFTooBig_603422800
		}
	default:
		return standard_error.WFPTTSCreateProductFileFormatNotMatch_603422799
	}
	return nil
}

func (s *serviceImpl) uploadFile(ctx context.Context, arg *tiktokFileArg) (tiktokres_v202309.ProductFile, error) {
	return s.tiktokAPIService.UploadProductFile(ctx, &tiktokapi.UploadProductFileParams{
		CommonParams: tiktokapi.CommonParams{
			OrganizationID: arg.organization.ID,
			AppName:        "feed",
			AppKey:         arg.salesChannel.StoreKey,
			ContentType:    commerceproxy.ContentTypeMultipartFormData,
		},
		FileName:  arg.fileName,
		FileBytes: arg.fileBytes,
	})
}
