package convert

import "github.com/AfterShip/pltf-pd-product-listings/internal/models"

type ImageArg struct {
	Organization    *models.Organization
	SalesChannel    *models.SalesChannel
	ImageUseCase    string
	ImageName       string
	ImageType       string
	ImageBytes      []byte
	ImageOriginURL  string
	IgnoreCache     bool
	IgnoreTransform bool
}

type ImageOutput struct {
	ImageURI     string
	ImageURL     string
	ImageUseCase string
	// If image is from sales channel, ImageHeight and ImageWidth will be 0
	// means that no need to check the image size
	ImageHeight int
	ImageWidth  int
	// image url in params from sales_channel, no need upload again
	FromSalesChannel bool
}

type FileArg struct {
	Organization  *models.Organization
	SalesChannel  *models.SalesChannel
	FileName      string
	FileType      string
	FileBytes     []byte
	FileOriginURL string
	IgnoreCache   bool
}
type FileOutput struct {
	FileID     string
	FileURL    string
	FileName   string
	FileFormat string
}

type DescriptionArg struct {
	Organization     *models.Organization `validate:"required"`
	SalesChannel     *models.SalesChannel `validate:"required"`
	SourceStore      *models.App          `validate:"omitempty"`
	Description      string               `validate:"required"`
	ShortDescription string               `validate:"omitempty"`
	IgnoreCache      bool
}

type DescriptionOutput struct {
	Description string
}

type transformImageArg struct {
	imageBytes     []byte
	imageType      string
	imageOriginURL string

	needSquare         bool
	needResize         bool
	needTransparent    bool
	needImageFormats   []string
	forceReducedSize   bool
	forceReducedWidth  int64
	forceReducedHeight int64
	aspectRatioRange   [2]float64

	minSize int
	maxSize int
	// 3000000 -> 3MB
	maxFileSize int // in bytes
}
