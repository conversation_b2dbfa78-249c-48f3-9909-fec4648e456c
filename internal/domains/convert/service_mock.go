package convert

import (
	"context"

	"github.com/stretchr/testify/mock"
)

type MockConvertService struct {
	mock.Mock
}

func (m *MockConvertService) ConvertImage(ctx context.Context, arg *ImageArg) (ImageOutput, error) {
	ret := m.Called(ctx, arg)
	return ret.Get(0).(ImageOutput), ret.Error(1)
}

func (m *MockConvertService) ConvertFile(ctx context.Context, arg *FileArg) (FileOutput, error) {
	ret := m.Called(ctx, arg)
	return ret.Get(0).(FileOutput), ret.Error(1)
}

func (m *MockConvertService) ConvertDescription(ctx context.Context, arg *DescriptionArg) (DescriptionOutput, error) {
	ret := m.Called(ctx, arg)
	return ret.Get(0).(DescriptionOutput), ret.Error(1)
}
