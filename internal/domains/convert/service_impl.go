package convert

import (
	"context"
	"regexp"
	"strconv"
	"strings"
	"time"

	"go.uber.org/zap"

	"github.com/AfterShip/pltf-pd-product-listings/internal/config"
	"github.com/AfterShip/pltf-pd-product-listings/internal/consts"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
	"github.com/AfterShip/pltf-pd-product-listings/internal/utils/image"
)

// nolint:gocyclo
func (s *serviceImpl) ConvertImage(ctx context.Context, arg *ImageArg) (ImageOutput, error) {
	if !s.hasConnection(ctx, arg.Organization, arg.SalesChannel) {
		return ImageOutput{}, ErrorNoConnection
	}

	cacheSeconds := s.adjustImageURLVersion(arg)

	var channelDomainRegExps []*regexp.Regexp
	switch arg.SalesChannel.Platform {
	case consts.TikTokShop:
		channelDomainRegExps = s.getTiktokCDNDomainRegexps()
	case consts.Shein:
		channelDomainRegExps = s.getSheinCDNDomainRegexps()
	}
	uri, ok := image.IsSalesChannelURL(arg.ImageOriginURL, channelDomainRegExps)
	if ok {
		return ImageOutput{
			ImageHeight:      0,
			ImageURI:         uri,
			ImageURL:         arg.ImageOriginURL,
			ImageUseCase:     arg.ImageUseCase,
			ImageWidth:       0,
			FromSalesChannel: true,
		}, nil
	}

	if !arg.IgnoreCache && arg.ImageOriginURL != "" {
		result, ok := s.loadImageCache(ctx, arg, cacheSeconds)
		if ok {
			return result, nil
		}
	}

	if arg.ImageOriginURL != "" {
		if image.IsVideoURL(arg.ImageOriginURL) {
			return ImageOutput{}, ErrorUnsupportedVideoURL
		}

		imageBytes, err := s.downloadFile(ctx, image.FormatOriginURL(arg.ImageOriginURL))
		if err != nil {
			return ImageOutput{}, err
		}
		arg.ImageBytes = imageBytes
		arg.ImageName = getFileName(arg.ImageOriginURL)
		arg.ImageType = getFileType(imageBytes)
	}

	result := ImageOutput{}
	switch arg.SalesChannel.Platform {
	case consts.TikTokShop:
		ttsImageResponse, err := s.convert2TTSImage(ctx, &tiktokImageArg{
			organization:    arg.Organization,
			salesChannel:    arg.SalesChannel,
			imageBytes:      arg.ImageBytes,
			imageName:       arg.ImageName,
			imageType:       arg.ImageType,
			imageUseCase:    arg.ImageUseCase,
			ignoreTransform: arg.IgnoreTransform,
			imageOriginURL:  arg.ImageOriginURL,
		})
		if err != nil {
			return ImageOutput{}, err
		}
		result = ttsImageResponse.convertToImageOutput()
	case consts.Shein:
		sheinImageResponse, err := s.convert2SheinImage(ctx, &sheinImageArg{
			organization:    arg.Organization,
			salesChannel:    arg.SalesChannel,
			imageBytes:      arg.ImageBytes,
			imageName:       arg.ImageName,
			imageType:       arg.ImageType,
			imageUseCase:    arg.ImageUseCase,
			ignoreTransform: arg.IgnoreTransform,
			imageOriginURL:  arg.ImageOriginURL,
		})
		if err != nil {
			return ImageOutput{}, err
		}
		result = sheinImageResponse.convertToImageOutput()
	default:
		return ImageOutput{}, ErrorUnsupportedSalesChannel
	}

	if arg.ImageOriginURL != "" {
		if err := s.saveImageCache(ctx, arg, &result); err != nil {
			s.logger.ErrorCtx(ctx, "saveImageCache error", zap.Error(err), zap.Any("arg", arg))
		}
	}
	return result, nil
}

func (s *serviceImpl) ConvertFile(ctx context.Context, arg *FileArg) (FileOutput, error) {
	if !s.hasConnection(ctx, arg.Organization, arg.SalesChannel) {
		return FileOutput{}, ErrorNoConnection
	}

	if !arg.IgnoreCache && arg.FileOriginURL != "" {
		result, ok := s.loadFileCache(ctx, arg)
		if ok {
			return result, nil
		}
	}

	if arg.FileOriginURL != "" {
		fileBytes, err := s.downloadFile(ctx, image.FormatOriginURL(arg.FileOriginURL))
		if err != nil {
			return FileOutput{}, err
		}
		arg.FileBytes = fileBytes
		arg.FileName = getFileName(arg.FileOriginURL)
		arg.FileType = getFileType(fileBytes)
	}

	result := FileOutput{}
	switch arg.SalesChannel.Platform {
	case consts.TikTokShop:
		ttsFileResponse, err := s.convert2TTSFile(ctx, &tiktokFileArg{
			organization: arg.Organization,
			salesChannel: arg.SalesChannel,
			fileBytes:    arg.FileBytes,
			fileName:     arg.FileName,
			fileType:     arg.FileType,
		})
		if err != nil {
			return FileOutput{}, err
		}
		result = ttsFileResponse.convertToFileOutput()
	default:
		return FileOutput{}, ErrorUnsupportedSalesChannel
	}

	if arg.FileOriginURL != "" {
		if err := s.saveFileCache(ctx, arg, &result); err != nil {
			s.logger.ErrorCtx(ctx, "saveFileCache error", zap.Error(err), zap.Any("arg", arg))
		}
	}

	return result, nil
}

func (s *serviceImpl) ConvertDescription(ctx context.Context, arg *DescriptionArg) (DescriptionOutput, error) {
	if !s.hasConnection(ctx, arg.Organization, arg.SalesChannel) {
		return DescriptionOutput{}, ErrorNoConnection
	}

	switch arg.SalesChannel.Platform {
	case consts.TikTokShop:
		return s.convert2TTSDescription(ctx, arg)
	case consts.Shein:
		return s.convert2SheinDescription(ctx, arg)
	default:
		return DescriptionOutput{}, ErrorUnsupportedSalesChannel
	}
}

func (s *serviceImpl) getTiktokCDNDomainRegexps() []*regexp.Regexp {
	ttsCDNDomainRegExps := make([]*regexp.Regexp, 0)
	if s.config != nil &&
		s.config.DynamicConfigs != nil &&
		s.config.DynamicConfigs.TTSCDNDomainRegExps != nil {
		ttsCDNDomainRegExps = s.config.DynamicConfigs.TTSCDNDomainRegExps
	}

	return ttsCDNDomainRegExps
}

func (s *serviceImpl) getSheinCDNDomainRegexps() []*regexp.Regexp {
	sheinCDNDomainRegExps := make([]*regexp.Regexp, 0)
	if s.config != nil &&
		s.config.DynamicConfigs != nil &&
		s.config.DynamicConfigs.SheinCDNDomainRegExps != nil {
		sheinCDNDomainRegExps = s.config.DynamicConfigs.SheinCDNDomainRegExps
	}

	return sheinCDNDomainRegExps
}

func (s *serviceImpl) hasConnection(ctx context.Context, organization *models.Organization, salesChannel *models.SalesChannel) bool {

	bothConnections, err := s.connectorsClient.GetBothConnections(ctx, organization.ID)
	if err != nil {
		return false
	}
	if !bothConnections.IsBothConnections() {
		return false
	}

	if !bothConnections.IsBothConnectionsWithSaleChannel(*salesChannel) {
		return false
	}

	return true
}

// adjustImageURLVersion 根据组织ID调整图片URL的版本参数
func (s *serviceImpl) adjustImageURLVersion(arg *ImageArg) int64 {
	// 基础检查
	if arg.ImageOriginURL == "" {
		return 0
	}

	// 获取缓存配置
	cacheConfig := s.getImageCacheConfig(arg.Organization.ID)
	if cacheConfig == nil || cacheConfig.CacheSeconds <= 0 {
		return 0
	}

	// 提取URL版本信息
	versionTimestamp, hasVersion := extractURLVersionTimestamp(arg.ImageOriginURL)
	if !hasVersion {
		return 0
	}

	// 计算缓存期限时间戳
	cacheDateTimestamp := calculateCacheTimestamp(cacheConfig.CacheSeconds)
	// 如果版本时间戳小于缓存期限，无需处理
	if versionTimestamp < cacheDateTimestamp {
		return 0
	}

	// 移除URL中的查询参数部分
	cleanURL := removeQueryParams(arg.ImageOriginURL)

	s.logger.Info("adjustImageURLVersion",
		zap.String("originURL", cleanURL),
		zap.String("organizationID", arg.Organization.ID),
		zap.String("old originURL", arg.ImageOriginURL))

	arg.ImageOriginURL = cleanURL
	return cacheConfig.CacheSeconds
}

// getImageCacheConfig 获取指定组织的图片缓存配置
func (s *serviceImpl) getImageCacheConfig(organizationID string) *config.ImageCacheConfig {
	if s.config == nil ||
		s.config.DynamicConfigs == nil ||
		s.config.DynamicConfigs.ImageCacheConfig == nil {
		return nil
	}

	for i := range s.config.DynamicConfigs.ImageCacheConfig {
		if s.config.DynamicConfigs.ImageCacheConfig[i].OrganizationID == organizationID {
			return &s.config.DynamicConfigs.ImageCacheConfig[i]
		}
	}

	return nil
}

// extractURLVersionTimestamp 从URL中提取版本时间戳
func extractURLVersionTimestamp(url string) (int64, bool) {
	re := regexp.MustCompile(`\?v=(\d+)`)
	matches := re.FindStringSubmatch(url)
	if len(matches) < 2 {
		return 0, false
	}

	versionStr := matches[1]
	versionTimestamp, err := strconv.ParseInt(versionStr, 10, 64)
	if err != nil {
		return 0, false
	}

	return versionTimestamp, true
}

// calculateCacheTimestamp 计算缓存期限时间戳
func calculateCacheTimestamp(cacheSeconds int64) int64 {
	now := time.Now()
	cacheDate := now.Add(-time.Duration(cacheSeconds) * time.Second)
	return cacheDate.Unix()
}

// removeQueryParams 移除URL中的查询参数
func removeQueryParams(url string) string {
	queryIndex := strings.Index(url, "?")
	if queryIndex > 0 {
		return url[:queryIndex]
	}
	return url
}
