package convert

import (
	"context"
	_ "image/jpeg"
	_ "image/png"
	"slices"
	"strings"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/AfterShip/connectors-ecommerce-sdk-go/common"
	commerceproxy "github.com/AfterShip/connectors-library/sdks/commerce_proxy"
	"github.com/AfterShip/gopkg/log"
	"github.com/AfterShip/gopkg/uuid"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
	tiktokapi "github.com/AfterShip/pltf-pd-product-listings/internal/third_party/tiktok_api"
)

var (
	ttsSupportedImageFormats = []string{"jpg", "jpeg", "png"}
)

type tiktokImageArg struct {
	organization    *models.Organization
	salesChannel    *models.SalesChannel
	imageBytes      []byte
	imageName       string
	imageType       string
	imageUseCase    string
	ignoreTransform bool
	imageOriginURL  string
}

type tiktokImageUploadRecord struct {
	imageURI     string
	imageURL     string
	imageHeight  int
	imageWidth   int
	imageUseCase string
}

func (r *tiktokImageUploadRecord) convertToImageOutput() ImageOutput {
	return ImageOutput{
		ImageURI:     r.imageURI,
		ImageURL:     r.imageURL,
		ImageHeight:  r.imageHeight,
		ImageWidth:   r.imageWidth,
		ImageUseCase: r.imageUseCase,
	}
}

func (s *serviceImpl) convert2TTSImage(ctx context.Context, arg *tiktokImageArg) (tiktokImageUploadRecord, error) {
	if err := validateImageUseCase(arg.imageUseCase); err != nil {
		return tiktokImageUploadRecord{}, err
	}

	if !arg.ignoreTransform {
		needTransformArg := &transformImageArg{
			imageBytes:       arg.imageBytes,
			imageType:        arg.imageType,
			imageOriginURL:   arg.imageOriginURL,
			needImageFormats: ttsSupportedImageFormats,
			needResize:       slices.Contains([]string{ImageUseCaseMainImage, ImageUseCaseAttributeImage}, arg.imageUseCase),
			needSquare:       true,
			minSize:          300,
			maxSize:          20000,
			maxFileSize:      5000000,
		}

		needTransform, isFormatToPNG, transformImageReq := s.isImageNeedTransform(needTransformArg)
		if needTransform {
			newImageBytes, err := s.imageTransform(ctx, transformImageReq)
			if err != nil {
				return tiktokImageUploadRecord{}, err
			}
			arg.imageBytes = newImageBytes
			// 如果需要转换为 png 格式，重新生成 imageName 和 imageType
			if isFormatToPNG {
				arg.imageName = uuid.GenerateUUIDV4() + ".png"
				arg.imageType = "image/png"
			}
		}
	}

	img, err := s.tiktokAPIService.UploadProductImage(ctx, &tiktokapi.UploadProductImageParams{
		CommonParams: tiktokapi.CommonParams{
			OrganizationID: arg.organization.ID,
			AppName:        "feed",
			AppKey:         arg.salesChannel.StoreKey,
			ContentType:    commerceproxy.ContentTypeMultipartFormData,
		},
		ImageName:  arg.imageName,
		ImageType:  arg.imageType,
		ImageBytes: arg.imageBytes,
	})
	if err != nil {
		return tiktokImageUploadRecord{}, formatTTSUploadAPIError(ctx, err)
	}

	// Clear imageBytes to free up memory
	arg.imageBytes = nil

	return tiktokImageUploadRecord{
		imageURI:     img.URI,
		imageURL:     img.URL,
		imageHeight:  img.Height,
		imageWidth:   img.Width,
		imageUseCase: img.UseCase,
	}, nil
}

func formatTTSUploadAPIError(ctx context.Context, err error) error {
	apiErr := common.ResponseError{}
	if errors.As(err, &apiErr) {
		if bodyByte, ok := apiErr.ReqBody.([]byte); ok {
			apiErr.ReqBody = nil
			if len(bodyByte) >= 10240 {
				log.GlobalLogger().WarnCtx(ctx, "Upload Resource failed, apiErr body too large",
					zap.Int("length", len(bodyByte)),
					zap.Int("code", apiErr.Status),
					zap.String("api response message", apiErr.Message),
				)
			}
			return apiErr
		}
	}
	errStr := err.Error()
	if strings.Contains(errStr, "Request timeout.The request to the endpoint timed out") {
		return errors.Wrap(ErrorRequestUploadAPITimeout, err.Error())
	}
	if strings.Contains(errStr, "context canceled") {
		return errors.Wrap(ErrProxyServiceContextCancelError, err.Error())
	}
	if strings.Contains(errStr, "12038002") {
		return errors.Wrap(ErrorImageBlank, err.Error())
	}
	if strings.Contains(errStr, "12038004") {
		return ErrorImageType
	}

	if len(errStr) >= 10240 {
		log.GlobalLogger().WarnCtx(ctx, "Upload Resource failed, unexpected large error",
			zap.Int("length", len(errStr)),
			zap.Int("code", apiErr.Status),
			zap.String("api response message", apiErr.Message),
			zap.Error(err),
		)
		return ErrorUnexpectedLargeError
	}
	return errors.WithStack(err)
}
