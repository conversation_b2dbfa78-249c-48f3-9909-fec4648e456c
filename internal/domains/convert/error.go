package convert

import "github.com/pkg/errors"

var (
	ErrorUnsupportedVideoURL           = errors.New("unsupported video URL")
	ErrorUnsupportedSalesChannel       = errors.New("unsupported sales channel")
	ErrorUnexpectedLargeError          = errors.New("unexpected large error")
	ErrorInvalidImageUseCase           = errors.New("invalid image use case")
	ErrorConvertImageServiceError      = errors.New("convert image service error")
	ErrorConvertImageService422Error   = errors.New("convert image service 422 error")
	ErrorConvertImageService413Error   = errors.New("convert image service 413 error")
	ErrorConvertImageService429Error   = errors.New("convert image service 429 error")
	ErrDownloadFileFromEcommerceFailed = errors.New("download file from ecommerce failed")
	ErrorDownLoadFileFromEcommerce4XX  = errors.New("download file from ecommerce 4XX")
	ErrorOnlyAllowHttps                = errors.New("only allow https")
	ErrorNoConnection                  = errors.New("organization and sales channel have no connection")
	ErrorRequestUploadAPITimeout       = errors.New("request upload tts API timeout, please try again later")
	ErrorImageBlank                    = errors.New("image is blank, please upload a valid image")
	ErrImageNotValidate                = errors.New("image not validate, please upload a valid image")
	ErrorImageType                     = errors.New("image type not supported, please upload a valid image type")
	ErrProxyServiceContextCancelError  = errors.New("proxy service context cancelled error")
)
