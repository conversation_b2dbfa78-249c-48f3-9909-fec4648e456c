package convert

import (
	"context"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"

	"github.com/AfterShip/gopkg/log"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
)

func TestServiceImpl_convert2SheinDescription(t *testing.T) {
	// 创建测试服务实例
	s := &serviceImpl{
		logger: log.GlobalLogger(),
	}

	// 准备测试用例
	tests := []struct {
		name            string
		description     string
		wantDescription string
		wantErr         bool
		wantErrMsg      string
	}{
		{
			name:            "正常HTML文本转换",
			description:     "<p>这是一个<strong>测试</strong>描述</p><p>包含多个段落</p>",
			wantDescription: "这是一个 *测试* 描述\n\n包含多个段落",
			wantErr:         false,
		},
		{
			name:            "包含图片的HTML转换",
			description:     "<p>文本开始</p><img src=\"http://example.com/image.jpg\" alt=\"示例图片\"><p>文本结束</p>",
			wantDescription: "文本开始\n\n文本结束",
			wantErr:         false,
		},
		{
			name:            "包含列表的HTML转换",
			description:     "<ul><li>项目1</li><li>项目2</li></ul>",
			wantDescription: "* 项目1\n* 项目2",
			wantErr:         false,
		},
		{
			name:            "包含表格的HTML转换",
			description:     "<table><tr><th>标题1</th><th>标题2</th></tr><tr><td>数据1</td><td>数据2</td></tr></table>",
			wantDescription: "+-------+-------+\n| 标题1 | 标题2 |\n+-------+-------+\n| 数据1 | 数据2 |\n+-------+-------+",
			wantErr:         false,
		},
		{
			name:            "空描述",
			description:     "",
			wantDescription: "",
			wantErr:         false,
		},
		{
			name:            "纯文本描述",
			description:     "这是纯文本描述，没有HTML标签",
			wantDescription: "这是纯文本描述，没有HTML标签",
			wantErr:         false,
		},
		{
			name:            "包含特殊字符的HTML",
			description:     "<p>特殊字符: &amp; &lt; &gt; &quot;</p>",
			wantDescription: "特殊字符: & < > \"",
			wantErr:         false,
		},
		{
			name:        "超长描述",
			description: "<p>" + strings.Repeat("很长的描述文本", 1000) + "</p>", // 创建一个非常长的描述
			wantErr:     false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 调用被测试的函数
			result, err := s.convert2SheinDescription(context.Background(), &DescriptionArg{
				Description: tt.description,
				Organization: &models.Organization{
					ID: "test-org",
				},
				SalesChannel: &models.SalesChannel{
					Platform:      "shein",
					StoreKey:      "test-store",
					CountryRegion: "US",
				},
				SourceStore: &models.App{
					Platform: "shein",
					Key:      "test-store",
				},
			})

			gotDescription := result.Description
			assert.NoError(t, err, "不应返回错误")
			// 由于HTML到Markdown的转换可能有多种有效结果，这里我们可以采用更灵活的验证方式
			if tt.description != "" && gotDescription == "" {
				t.Error("返回的描述不应为空")
			}
			// 对于简单情况，可以直接比较结果
			if tt.wantDescription != "" {
				assert.Equal(t, tt.wantDescription, gotDescription, "转换结果应符合预期")
			}
		})
	}
}
