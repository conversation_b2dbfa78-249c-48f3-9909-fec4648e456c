package convert

import (
	"context"
	"time"

	"github.com/AfterShip/connectors-library/sdks/shein_proxy"
	platform_api_v2 "github.com/AfterShip/connectors-sdk-go/v2"
	connector_sdk_v2_image_upload_records "github.com/AfterShip/connectors-sdk-go/v2/image_upload_records"
	"github.com/AfterShip/go-sdk/converts"
	gopkg_client "github.com/AfterShip/gopkg/api/client"
	"github.com/AfterShip/gopkg/log"
	"github.com/AfterShip/pltf-pd-product-listings/internal/third_party/connectors"

	"github.com/AfterShip/pltf-pd-product-listings/internal/config"
	tiktokapi "github.com/AfterShip/pltf-pd-product-listings/internal/third_party/tiktok_api"
	"github.com/AfterShip/pltf-pd-product-listings/internal/utils/download_client"
)

type Service interface {
	ConvertImage(ctx context.Context, arg *ImageArg) (ImageOutput, error)
	ConvertFile(ctx context.Context, arg *FileArg) (FileOutput, error)
	ConvertDescription(ctx context.Context, arg *DescriptionArg) (DescriptionOutput, error)
}

type serviceImpl struct {
	config                   *config.Config
	logger                   *log.Logger
	tiktokAPIService         tiktokapi.Service
	sheinAPIService          shein_proxy.Service
	imageUploadRecordService connector_sdk_v2_image_upload_records.ImageUploadRecordsSvc
	downloadImageCli         *gopkg_client.Client
	convert                  *converts.Convert
	connectorsClient         connectors.Service
}

func NewService(config *config.Config, logger *log.Logger,
	tiktokAPIService tiktokapi.Service, sheinAPIService shein_proxy.Service,
	cntClient *platform_api_v2.PlatformV2Client, connectorsClient connectors.Service) *serviceImpl {
	options := []gopkg_client.Option{
		gopkg_client.WithTimeout(time.Second * 30),
	}
	convertClient := converts.New(options...)

	return &serviceImpl{
		config:                   config,
		logger:                   logger,
		sheinAPIService:          sheinAPIService,
		tiktokAPIService:         tiktokAPIService,
		imageUploadRecordService: connector_sdk_v2_image_upload_records.NewImageUploadRecordsSvc(cntClient),
		downloadImageCli:         download_client.NewDownloadClient(),
		convert:                  convertClient,
		connectorsClient:         connectorsClient,
	}
}
