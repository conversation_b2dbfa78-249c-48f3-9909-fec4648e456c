package convert

import (
	"context"
	"time"

	"go.uber.org/zap"

	"github.com/AfterShip/gopkg/log"
	"github.com/AfterShip/pltf-pd-product-listings/internal/utils/toolbox"
)

// convert2SheinDescription shein only support 'text' and '\n'
func (s *serviceImpl) convert2SheinDescription(ctx context.Context, arg *DescriptionArg) (DescriptionOutput, error) {

	if arg.Description == "" {
		return DescriptionOutput{}, nil
	}

	ctx = log.AppendFieldsToContext(ctx,
		zap.String("organization_id", arg.Organization.ID),
		zap.String("source_store_platform", arg.SourceStore.Platform),
		zap.String("source_store_key", arg.SourceStore.Key))

	startTime := time.Now()

	textDesc, err := toolbox.HtmlToRichText(arg.Description)
	s.logger.InfoCtx(ctx, "conversion shein description time record", zap.Duration("convert_duration", time.Since(startTime)))
	if err != nil {
		s.logger.WarnCtx(ctx, "failed to convert html to rich text", zap.Error(err))
		return DescriptionOutput{}, err
	}

	return DescriptionOutput{
		Description: textDesc,
	}, nil
}
