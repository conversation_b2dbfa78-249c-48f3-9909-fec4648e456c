package category

import (
	"context"
	"encoding/json"
	"fmt"
	"slices"
	"time"

	redis "github.com/go-redis/redis/v8"
	jsoniter "github.com/json-iterator/go"
	"github.com/pkg/errors"
	"go.uber.org/zap"

	tiktok_rest "github.com/AfterShip/connectors-ecommerce-sdk-go/tiktok/rest"
	tiktokres_v202309 "github.com/AfterShip/connectors-ecommerce-sdk-go/tiktok/rest/version202309"
	"github.com/AfterShip/connectors-library/sdks/shein_proxy"
	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/gopkg/log"
	"github.com/AfterShip/pltf-pd-product-listings/internal/consts"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/common"
	"github.com/AfterShip/pltf-pd-product-listings/internal/logger"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
	"github.com/AfterShip/pltf-pd-product-listings/internal/third_party/connectors"
	tiktokapi "github.com/AfterShip/pltf-pd-product-listings/internal/third_party/tiktok_api"
	"github.com/AfterShip/pltf-pd-product-listings/internal/utils/compress"
)

const (
	RedisNullValue = "Feed:NULL"

	CategoriesCacheDuration              = 8 * time.Hour
	CategoryLeafIDsCacheKeyCacheDuration = 8 * time.Hour
	CategoryAttributesCacheDuration      = 8 * time.Hour
	CategoryRulesCacheDuration           = 8 * time.Hour

	CategoriesV1CacheKeyPrefix       = "listings-api:categories:v1"
	CategoriesV2CacheKeyPrefix       = "listings-api:categories:v2"
	CategoryV2LeafIDsCacheKeyPrefix  = "listings-api:v2_category_leaf_ids"
	CategoryAttributesCacheKeyPrefix = "listings-api:category_attributes:v2:2"
	CategoryRulesCacheKeyPrefix      = "listings-api:category_rules:v2"
)

const (
	MockCategoryAttributesCacheKeyPrefix = "listings-api:mock_category_attributes"
	MockCategoryRulesCacheKeyPrefix      = "listings-api:mock_category_rules"
)

func (s *serviceImpl) PostCategoryAttributes(ctx context.Context, arg *AttributesOutput) (AttributesOutput, error) {

	marshalData, err := json.Marshal(arg)
	if err != nil {
		return AttributesOutput{}, err
	}
	redisKey := fmt.Sprintf("%s:%s", MockCategoryAttributesCacheKeyPrefix, arg.ExternalCategoryID)
	_, err = s.redisClient.Set(ctx, redisKey, marshalData, 48*time.Hour).Result()

	return *arg, err
}

func (s *serviceImpl) PostCategoryRules(ctx context.Context, arg *RulesOutput) (RulesOutput, error) {

	marshalData, err := json.Marshal(arg)
	if err != nil {
		return RulesOutput{}, err
	}
	redisKey := fmt.Sprintf("%s:%s", MockCategoryRulesCacheKeyPrefix, arg.ExternalCategoryID)
	_, err = s.redisClient.Set(ctx, redisKey, marshalData, 48*time.Hour).Result()

	return *arg, err

}

func (s *serviceImpl) List(ctx context.Context, arg *ListArg) (CategoriesOutput, error) {
	switch arg.SalesChannelPlatform {
	case consts.TikTokShop:
		return s.listTTSCategories(ctx, arg)
	case consts.Shein:
		return s.listSheinCategories(ctx, *arg)
	default:
		return CategoriesOutput{}, common.ErrSalesChannelNotSupported
	}
}

func (s *serviceImpl) listTTSCategories(ctx context.Context, arg *ListArg) (CategoriesOutput, error) {
	if arg.SalesChannelPlatform != consts.TikTokShop {
		return CategoriesOutput{}, ErrPlatformNotSupported
	}

	result := CategoriesOutput{}
	result.SalesChannel = models.SalesChannel{
		Platform: arg.SalesChannelPlatform,
		StoreKey: arg.SalesChannelStoreKey,
	}
	result.Organization = models.Organization{
		ID: arg.OrganizationID,
	}

	params := arg.convertToTikTokGetCategoriesParams()
	params.CategoryVersion = consts.CategoryVersionV1

	region, err := s.connectorService.GetConnectionRegions(ctx, connectors.GetConnectionRegionParam{
		OrganizationID: arg.OrganizationID,
		AppPlatform:    arg.SalesChannelPlatform,
		AppKey:         arg.SalesChannelStoreKey,
	})
	if err != nil {
		return CategoriesOutput{}, err
	}
	if region == consts.RegionUS {
		params.CategoryVersion = consts.CategoryVersionV2
	}

	categoryList, err := s.getTikTokCategories(ctx, params)
	if err != nil {
		logger.Get().WarnCtx(ctx, "getTikTokCategoryList failed",
			zap.String("category_version", params.CategoryVersion),
			zap.String("organization_id", params.OrganizationID),
			zap.String("sales_channel_store_key", params.CommonParams.AppKey),
			zap.Error(err))
		return CategoriesOutput{}, err
	}

	result.Categories = categoryList

	return result, nil
}

func (s *serviceImpl) listSheinCategories(ctx context.Context, arg ListArg) (CategoriesOutput, error) {
	categoryList, err := s.getSheinCategoriesWithCache(ctx, arg)
	if err != nil {
		logger.Get().WarnCtx(ctx, "getSheinCategoriesWithCache failed", zap.Any("arg", arg), zap.Error(err))
		return CategoriesOutput{}, err
	}

	return CategoriesOutput{
		Organization: models.Organization{
			ID: arg.OrganizationID,
		},
		SalesChannel: models.SalesChannel{
			Platform: arg.SalesChannelPlatform,
			StoreKey: arg.SalesChannelStoreKey,
		},
		Categories: categoryList,
	}, nil
}

func (s *serviceImpl) Versions(ctx context.Context, arg *CategoryVersionsArg) (CategoryVersionsOutput, error) {
	if arg.SalesChannelPlatform != consts.TikTokShop {
		return CategoryVersionsOutput{}, ErrPlatformNotSupported
	}

	result := CategoryVersionsOutput{}
	result.Organization = models.Organization{
		ID: arg.OrganizationID,
	}
	result.SalesChannel = models.SalesChannel{
		Platform: arg.SalesChannelPlatform,
		StoreKey: arg.SalesChannelStoreKey,
	}

	region, err := s.connectorService.GetConnectionRegions(ctx, connectors.GetConnectionRegionParam{
		OrganizationID: arg.OrganizationID,
		AppPlatform:    arg.SalesChannelPlatform,
		AppKey:         arg.SalesChannelStoreKey,
	})
	if err != nil {
		return CategoryVersionsOutput{}, err
	}
	if region != consts.RegionUS {
		result.CategoryVersions = make([]CategoryVersion, 0, len(arg.SalesChannelCategoryIDs))
		for _, categoryID := range arg.SalesChannelCategoryIDs {
			result.CategoryVersions = append(result.CategoryVersions, CategoryVersion{
				CategoryID: categoryID,
				Version:    consts.CategoryVersionV1,
			})
		}
		return result, nil
	}

	v2LeafCategoryIDs, err := s.getV2LeafCategoryIDsFromCacheOrAPI(ctx, arg)
	if err != nil {
		if !errors.Is(err, tiktokapi.ErrCategoryVersionNotMatch) {
			return CategoryVersionsOutput{}, err
		}
		v2LeafCategoryIDs = []string{} // empty v2LeafCategoryIDs
	}

	result.CategoryVersions = make([]CategoryVersion, 0, len(arg.SalesChannelCategoryIDs))
	for _, categoryID := range arg.SalesChannelCategoryIDs {
		categoryVersion := CategoryVersion{
			CategoryID: categoryID,
		}
		if slices.Contains(v2LeafCategoryIDs, categoryID) {
			categoryVersion.Version = consts.CategoryVersionV2
		} else {
			categoryVersion.Version = consts.CategoryVersionV1
		}
		result.CategoryVersions = append(result.CategoryVersions, categoryVersion)
	}

	return result, nil
}

//nolint:nestif,gocyclo
func (s *serviceImpl) getV2LeafCategoryIDsFromCacheOrAPI(ctx context.Context, arg *CategoryVersionsArg) ([]string, error) {
	v2LeafCategoryIDs := make([]string, 0)
	redisKey := fmt.Sprintf("%s:%s:%s:%s", CategoryV2LeafIDsCacheKeyPrefix, arg.OrganizationID, arg.SalesChannelPlatform, arg.SalesChannelStoreKey)

	if arg.RefreshCache {
		_, err := s.redisClient.Del(ctx, redisKey).Result()
		if err != nil {
			return nil, errors.WithStack(err)
		}
	}

	redisValue, err := s.redisClient.Get(ctx, redisKey).Result()
	if err != nil {
		if !errors.Is(err, redis.Nil) {
			return nil, errors.WithStack(err)
		}
		// 透传查询 list
		categoryList, err := s.tiktokAPIService.GetCategories(ctx, &tiktokapi.GetCategoriesParams{
			CommonParams: tiktokapi.CommonParams{
				OrganizationID: arg.OrganizationID,
				AppName:        consts.AppFeed,
				AppKey:         arg.SalesChannelStoreKey,
			},
			CategoryVersion: consts.CategoryVersionV2,
		})
		if err != nil {
			tikTokError := &tiktok_rest.TikTokError{}
			if errors.As(err, &tikTokError) && tikTokError.Code == types.MakeInt(12052230) {
				logger.Get().WarnCtx(ctx, "getV2LeafCategoryIDsFromCacheOrAPI failed",
					zap.String("organization_id", arg.OrganizationID),
					zap.String("category_id", arg.SalesChannelStoreKey),
					zap.Error(err))
				_, _ = s.redisClient.Set(ctx, redisKey, RedisNullValue, 3*time.Minute).Result()
				return nil, tiktokapi.ErrCategoryVersionNotMatch
			}
			return nil, errors.WithStack(err)
		}
		// 存储叶子节点缓存
		v2LeafCategoryIDs = getV2LeafCategoryIDs(categoryList)
		redisValue, err = jsoniter.MarshalToString(v2LeafCategoryIDs)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		_, err = s.redisClient.Set(ctx, redisKey, redisValue, CategoryLeafIDsCacheKeyCacheDuration).Result()
		if err != nil {
			return nil, errors.WithStack(err)
		}
	} else {
		if redisValue == RedisNullValue {
			return nil, tiktokapi.ErrCategoryVersionNotMatch
		}
		err = jsoniter.Unmarshal([]byte(redisValue), &v2LeafCategoryIDs)
		if err != nil {
			return nil, errors.WithStack(err)
		}
	}

	return v2LeafCategoryIDs, nil
}

func getV2LeafCategoryIDs(categories []tiktokres_v202309.Category) []string {
	leafCategoryIDs := make([]string, 0)
	for _, category := range categories {
		if category.IsLeaf.Bool() {
			leafCategoryIDs = append(leafCategoryIDs, category.Id.String())
		}
	}
	return leafCategoryIDs
}

// nolint:dupl
func (s *serviceImpl) GetCategoryAttributes(ctx context.Context, arg *GetAttributesArg) (AttributesOutput, error) {
	var attributes []Attribute
	var err error

	switch arg.SalesChannelPlatform {
	case consts.TikTokShop:
		attributes, err = s.getTTSCategoryAttributesFromCacheOrAPI(ctx, arg)
		if err != nil {
			return AttributesOutput{}, err
		}
	case consts.Shein:
		attributes, err = s.getSheinCategoryAttributesWithCache(ctx, *arg)
		if err != nil {
			return AttributesOutput{}, err
		}
	default:
		return AttributesOutput{}, ErrPlatformNotSupported
	}

	attributes = FilterProductCategoryAttributes(attributes, FilterAttributesArg{
		IsRequired: arg.IsRequired,
		AllType:    arg.AllType,
		Types:      arg.Types,
	})

	categoryAttributes := AttributesOutput{}
	categoryAttributes.Attributes = attributes
	categoryAttributes.SalesChannel = models.SalesChannel{
		Platform: arg.SalesChannelPlatform,
		StoreKey: arg.SalesChannelStoreKey,
	}
	categoryAttributes.Organization = models.Organization{
		ID: arg.OrganizationID,
	}
	categoryAttributes.ExternalCategoryID = arg.ExternalCategoryID

	return categoryAttributes, nil
}

// if category attributes not found in cache, get from tts api and set cache
func (s *serviceImpl) getTTSCategoryAttributesFromCacheOrAPI(ctx context.Context, arg *GetAttributesArg) ([]Attribute, error) {
	result := make([]Attribute, 0)
	if arg.SalesChannelRegion == "" {
		region, err := s.connectorService.GetConnectionRegions(ctx, connectors.GetConnectionRegionParam{
			OrganizationID: arg.OrganizationID,
			AppPlatform:    arg.SalesChannelPlatform,
			AppKey:         arg.SalesChannelStoreKey,
		})
		if err != nil {
			return nil, err
		}
		arg.SalesChannelRegion = region
	}
	if arg.SalesChannelRegion == consts.RegionUS {
		arg.CategoryVersion = consts.CategoryVersionV2
	}

	redisKey := fmt.Sprintf("%s:%s:%s", CategoryAttributesCacheKeyPrefix, arg.SalesChannelRegion, arg.ExternalCategoryID)

	if arg.RefreshCache {
		_, err := s.redisClient.Del(ctx, redisKey).Result()
		if err != nil {
			return nil, errors.WithStack(err)
		}
	}

	redisValue, err := s.redisClient.Get(ctx, redisKey).Result()
	if err != nil { // nolint:nestif
		if !errors.Is(err, redis.Nil) {
			return nil, errors.WithStack(err)
		}
		// 查询 tts api
		result, err = s.getTikTokCategoryAttributes(ctx, arg.convertToTikTokGetCategoryAttributesParams())
		if err != nil {
			return nil, errors.WithStack(err)
		}
		redisValue, err = jsoniter.MarshalToString(result)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		// set cache
		_, err = s.redisClient.Set(ctx, redisKey, redisValue, CategoryAttributesCacheDuration).Result()
		if err != nil {
			return nil, errors.WithStack(err)
		}
	} else {
		err = jsoniter.Unmarshal([]byte(redisValue), &result)
		if err != nil {
			return nil, errors.WithStack(err)
		}
	}

	return result, nil
}

func (s *serviceImpl) getSheinCategoryAttributesWithCache(ctx context.Context, arg GetAttributesArg) ([]Attribute, error) {
	result := make([]Attribute, 0)

	// shein attribute 是 店铺+类目 维度

	productType := ""
	categories, err := s.getSheinCategoriesWithCache(ctx, ListArg{
		OrganizationID:       arg.OrganizationID,
		SalesChannelPlatform: arg.SalesChannelPlatform,
		SalesChannelStoreKey: arg.SalesChannelStoreKey,
	})
	for _, cur := range categories {
		if cur.ID == arg.ExternalCategoryID {
			productType = cur.ProductTypeID
			break
		}
	}

	if productType == "" {
		return nil, errors.Wrap(ErrCategoryNotFound, "category_id: "+arg.ExternalCategoryID)
	}

	language, err := s.getSheinDefaultLanguage(ctx, arg.OrganizationID, arg.SalesChannelStoreKey)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	redisKey := fmt.Sprintf("%s:%s:%s:%s:%s:%s", consts.SHEINCategoryAttributesCacheKeyPrefix,
		arg.OrganizationID, arg.SalesChannelStoreKey, language, arg.ExternalCategoryID, productType)
	if arg.RefreshCache {
		_, err := s.redisClient.Del(ctx, redisKey).Result()
		if err != nil {
			return nil, errors.WithStack(err)
		}
	}

	redisValue, err := s.redisClient.Get(ctx, redisKey).Result()
	if err != nil {
		if !errors.Is(err, redis.Nil) {
			return nil, errors.WithStack(err)
		}

		// 查询
		// 查询
		sheinGetAttributesParams, err := arg.convertToSheinGetAttributesParams(productType, language)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		sheinGetAttributePermissionConfigsParams, err := arg.convertToSheinGetCustomAttributePermissionConfigsParams()
		if err != nil {
			return nil, errors.WithStack(err)
		}
		sheinAttributesResp, err := s.sheinAPIService.GetAttributes(ctx, sheinGetAttributesParams)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		if len(sheinAttributesResp.Data) == 0 {
			return nil, ErrAttributeNotFound
		}
		attributePermissionConfigsResp, err := s.sheinAPIService.GetCustomAttributePermissionConfigs(ctx, sheinGetAttributePermissionConfigsParams)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		result = convertSheinAttributes(sheinAttributesResp.Data[0].AttributeInfos, attributePermissionConfigsResp.Data)
		if redisValue, err = jsoniter.MarshalToString(result); err == nil {
			_ = s.redisClient.Set(ctx, redisKey, redisValue, consts.SHEINCategoryAttributesCacheDuration).Err()
		}

	} else {
		err = jsoniter.Unmarshal([]byte(redisValue), &result)
		if err != nil {
			return nil, errors.WithStack(err)
		}
	}

	return result, nil
}

func (s *serviceImpl) getSheinDefaultLanguage(ctx context.Context, orgID, storeKey string) (string, error) {
	language := ""
	redisKey := fmt.Sprintf("%s:%s:%s", consts.SHEINLanguageCacheKeyPrefix, orgID, storeKey)
	redisValue, err := s.redisClient.Get(ctx, redisKey).Result()
	if err != nil {
		if !errors.Is(err, redis.Nil) {
			return "", errors.WithStack(err)
		}

		fillStandard, err := s.sheinAPIService.GetProductPublishFillStandard(ctx,
			&shein_proxy.GetProductPublishFillStandardParams{
				CommonParams: shein_proxy.CommonParams{
					OrganizationID: orgID,
					AppName:        consts.AppFeed,
					AppKey:         storeKey,
				},
			})
		if err != nil {
			return "", err
		}

		language = fillStandard.DefaultLanguage.String()
		if err := s.redisClient.Set(ctx, redisKey, language, consts.SHEINLanguageCacheDuration).Err(); err != nil {
			log.GlobalLogger().WarnCtx(ctx, "failed to cache shein store language",
				zap.String("orgID", orgID),
				zap.String("storeKey", storeKey),
				zap.Error(err))
		}
	} else {
		language = redisValue
	}

	return language, nil
}

// nolint:dupl
func (s *serviceImpl) GetCategoryRules(ctx context.Context, arg *GetRulesArg) (RulesOutput, error) {
	var (
		rule Rule
		err  error
	)
	switch arg.SalesChannelPlatform {
	case consts.TikTokShop:
		rule, err = s.getCategoryRuleFromCacheOrAPI(ctx, arg)
		if err != nil {
			return RulesOutput{}, err
		}
	case consts.Shein:
		rule, err = s.getSheinCategoryRule(ctx, *arg)
		if err != nil {
			return RulesOutput{}, err
		}
	default:
		return RulesOutput{}, ErrPlatformNotSupported
	}

	if arg.IsRequired {
		rule = FilterTikTokShopRequiredRules(rule)
	}

	categoryRules := RulesOutput{}
	categoryRules.Rule = rule
	categoryRules.Rule.ExternalCategoryID = arg.ExternalCategoryID

	categoryRules.SalesChannel = models.SalesChannel{
		Platform: arg.SalesChannelPlatform,
		StoreKey: arg.SalesChannelStoreKey,
	}
	categoryRules.Organization = models.Organization{
		ID: arg.OrganizationID,
	}
	categoryRules.ExternalCategoryID = arg.ExternalCategoryID

	return categoryRules, nil
}

// if category rule not found in cache, get from tts api and set cache
func (s *serviceImpl) getCategoryRuleFromCacheOrAPI(ctx context.Context, arg *GetRulesArg) (Rule, error) {
	result := Rule{}
	if arg.SalesChannelRegion == "" {
		region, err := s.connectorService.GetConnectionRegions(ctx, connectors.GetConnectionRegionParam{
			OrganizationID: arg.OrganizationID,
			AppPlatform:    arg.SalesChannelPlatform,
			AppKey:         arg.SalesChannelStoreKey,
		})
		if err != nil {
			return Rule{}, err
		}
		arg.SalesChannelRegion = region
	}
	if arg.SalesChannelRegion == consts.RegionUS {
		arg.CategoryVersion = consts.CategoryVersionV2
	}

	redisKey := fmt.Sprintf("%s:%s:%s", CategoryRulesCacheKeyPrefix, arg.SalesChannelRegion, arg.ExternalCategoryID)

	if arg.RefreshCache {
		_, err := s.redisClient.Del(ctx, redisKey).Result()
		if err != nil {
			return Rule{}, errors.WithStack(err)
		}
	}

	redisValue, err := s.redisClient.Get(ctx, redisKey).Result()
	if err != nil { // nolint:nestif
		if !errors.Is(err, redis.Nil) {
			return Rule{}, errors.WithStack(err)
		}
		// 查询 tts  api
		result, err = s.getTikTokCategoryRule(ctx, arg.convertToTikTokGetCategoryRulesParams())
		if err != nil {
			return Rule{}, errors.WithStack(err)
		}
		redisValue, err = jsoniter.MarshalToString(result)
		if err != nil {
			return Rule{}, errors.WithStack(err)
		}
		// set cache
		_, err = s.redisClient.Set(ctx, redisKey, redisValue, CategoryRulesCacheDuration).Result()
		if err != nil {
			return Rule{}, errors.WithStack(err)
		}
	} else {
		err = jsoniter.Unmarshal([]byte(redisValue), &result)
		if err != nil {
			return Rule{}, errors.WithStack(err)
		}
	}

	return result, nil
}

// if category rule not found in cache, get from tts api and set cache
func (s *serviceImpl) getSheinCategoryRule(ctx context.Context, arg GetRulesArg) (Rule, error) {
	getProductPublishFillStandardParams, err := arg.convertGetProductPublishFillStandard()
	if err != nil {
		return Rule{}, errors.WithStack(err)
	}
	resp, err := s.sheinAPIService.GetProductPublishFillStandard(ctx, getProductPublishFillStandardParams)
	if err != nil {
		return Rule{}, errors.WithStack(err)
	}

	sizeAttributes, err := s.GetCategoryAttributes(ctx, &GetAttributesArg{
		OrganizationID:       arg.OrganizationID,
		SalesChannelStoreKey: arg.SalesChannelStoreKey,
		SalesChannelPlatform: arg.SalesChannelPlatform,
		ExternalCategoryID:   arg.ExternalCategoryID,
		Types:                []string{consts.AttributeTypeSizeProperty},
	})
	if err != nil {
		return Rule{}, errors.WithStack(err)
	}

	return convertSheinCategoryRules(resp, sizeAttributes.Attributes), nil
}

func (s *serviceImpl) getTikTokCategoryRule(ctx context.Context, params *tiktokapi.GetCategoryRulesParams) (Rule, error) {
	if params.CategoryVersion == "" {
		params.CategoryVersion = consts.CategoryVersionV1
	}

	result, err := s.tiktokAPIService.GetCategoryRules(ctx, params)
	if err != nil {
		logger.Get().WarnCtx(ctx, "getTikTokCategoryRule failed", zap.String("organization_id", params.OrganizationID),
			zap.String("category_version", params.CategoryVersion), zap.String("category_id", params.CategoryID), zap.Error(err))
		return Rule{}, err
	}

	return convertTikTokCategoryRules(&result), nil
}

func (s *serviceImpl) getTikTokCategoryAttributes(ctx context.Context, params *tiktokapi.GetCategoryAttributesParams) ([]Attribute, error) {
	if params.CategoryVersion == "" {
		params.CategoryVersion = consts.CategoryVersionV1
	}

	result, err := s.tiktokAPIService.GetCategoryAttributes(ctx, params)
	if err != nil {
		logger.Get().WarnCtx(ctx, "getTikTokCategoryAttributes failed", zap.String("organization_id", params.OrganizationID),
			zap.String("category_version", params.CategoryVersion), zap.String("category_id", params.CategoryID), zap.Error(err))
		return nil, err
	}

	return convertTikTokCategoryAttributes(result), nil
}

// nolint:gocyclo
func (s *serviceImpl) getTikTokCategories(ctx context.Context, params *tiktokapi.GetCategoriesParams) ([]Category, error) {
	var result []Category
	var redisCachePreKey string

	if params.CategoryVersion == consts.CategoryVersionV1 {
		redisCachePreKey = CategoriesV1CacheKeyPrefix
	} else {
		redisCachePreKey = CategoriesV2CacheKeyPrefix
	}

	redisKey := fmt.Sprintf("%s:%s:%s", redisCachePreKey, params.OrganizationID, params.AppKey)

	if params.RefreshCache {
		_, err := s.redisClient.Del(ctx, redisKey).Result()
		if err != nil {
			return nil, errors.WithStack(err)
		}
	}

	redisValue, err := s.redisClient.Get(ctx, redisKey).Result()
	if err != nil { // nolint:nestif
		if !errors.Is(err, redis.Nil) {
			return nil, errors.WithStack(err)
		}
		// 透传查询
		ttsCategories, err := s.tiktokAPIService.GetCategories(ctx, params)
		if err != nil {
			return nil, err
		}
		// 转换
		result = convertTikTokCategories(ttsCategories)
		// 缓存
		redisValue, err = jsoniter.MarshalToString(result)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		// 此压缩能节约5倍空间
		gzipResult, err := compress.GzipAndBase64Data([]byte(redisValue))
		if err != nil {
			return nil, errors.WithStack(err)
		}
		_, err = s.redisClient.Set(ctx, redisKey, gzipResult, CategoriesCacheDuration).Result()
		if err != nil {
			return nil, errors.WithStack(err)
		}
	} else {
		deCodeValue, err := compress.Base64GzipDecode([]byte(redisValue))
		if err != nil {
			return nil, errors.WithStack(err)
		}
		err = jsoniter.Unmarshal(deCodeValue, &result)
		if err != nil {
			return nil, errors.WithStack(err)
		}
	}
	return result, nil
}

func (s *serviceImpl) getSheinCategoriesWithCache(ctx context.Context, arg ListArg) ([]Category, error) {
	var result []Category

	redisKey := fmt.Sprintf("%s:%s:%s:%s",
		CategoriesV1CacheKeyPrefix, arg.OrganizationID, arg.SalesChannelPlatform, arg.SalesChannelStoreKey)

	if arg.RefreshCache {
		_, err := s.redisClient.Del(ctx, redisKey).Result()
		if err != nil {
			return nil, errors.WithStack(err)
		}
	}

	redisValue, err := s.redisClient.Get(ctx, redisKey).Result()
	if err != nil { // nolint:nestif
		if !errors.Is(err, redis.Nil) {
			return nil, errors.WithStack(err)
		}

		language, err := s.getSheinDefaultLanguage(ctx, arg.OrganizationID, arg.SalesChannelStoreKey)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		// 查询
		getRep, err := s.sheinAPIService.GetCategories(ctx, arg.toSheinGetCategoriesParams(language))
		if err != nil {
			return nil, err
		}

		// 转换
		result = convertSheinCategories(getRep.Data)

		// 缓存
		redisData, err := jsoniter.Marshal(result)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		gzipResult, err := compress.GzipAndBase64Data(redisData)
		if err != nil {
			return nil, errors.WithStack(err)
		}
		_, err = s.redisClient.Set(ctx, redisKey, gzipResult, CategoriesCacheDuration).Result()
		if err != nil {
			return nil, errors.WithStack(err)
		}

		return result, nil
	}

	deCodeValue, err := compress.Base64GzipDecode([]byte(redisValue))
	if err != nil {
		return nil, errors.WithStack(err)
	}
	err = jsoniter.Unmarshal(deCodeValue, &result)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	return result, nil
}
