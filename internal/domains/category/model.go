package category

import (
	"strconv"

	shein_rest "github.com/AfterShip/connectors-ecommerce-sdk-go/shein/rest"
	"github.com/AfterShip/connectors-library/sdks/shein_proxy"
	"github.com/AfterShip/gopkg/facility/set"
	"github.com/AfterShip/pltf-pd-product-listings/internal/consts"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
	tiktokapi "github.com/AfterShip/pltf-pd-product-listings/internal/third_party/tiktok_api"
)

type GetRulesArg struct {
	OrganizationID       string `validate:"required"`
	SalesChannelStoreKey string `validate:"required"`
	SalesChannelPlatform string `validate:"required"`
	ExternalCategoryID   string `validate:"required"`
	IsRequired           bool
	CategoryVersion      string `validate:"required,oneof=v1 v2"`
	SalesChannelRegion   string
	RefreshCache         bool
}

func (arg *GetRulesArg) convertToTikTokGetCategoryRulesParams() *tiktokapi.GetCategoryRulesParams {
	return &tiktokapi.GetCategoryRulesParams{
		CommonParams: tiktokapi.CommonParams{
			OrganizationID: arg.OrganizationID,
			AppName:        consts.AppFeed,
			AppKey:         arg.SalesChannelStoreKey,
		},
		CategoryID:      arg.ExternalCategoryID,
		CategoryVersion: arg.CategoryVersion,
	}
}

func (arg *GetRulesArg) convertGetProductPublishFillStandard() (*shein_proxy.GetProductPublishFillStandardParams, error) {
	categoryIDInt64, err := strconv.ParseInt(arg.ExternalCategoryID, 10, 64)
	if err != nil {
		return nil, err
	}
	return &shein_proxy.GetProductPublishFillStandardParams{
		CommonParams: shein_proxy.CommonParams{
			OrganizationID: arg.OrganizationID,
			AppName:        consts.AppFeed,
			AppKey:         arg.SalesChannelStoreKey,
		},
		GetProductPublishFillStandardParams: shein_rest.GetProductPublishFillStandardParams{
			CategoryId: categoryIDInt64,
		},
	}, nil
}

type RulesOutput struct {
	Organization       models.Organization `json:"organization"`
	SalesChannel       models.SalesChannel `json:"sales_channel"`
	ExternalCategoryID string              `json:"external_category_id"`
	Rule               Rule                `json:"rule"`
}

type Rule struct {
	ExternalCategoryID    string                  `json:"external_category_id"`
	ProductCertifications []*ProductCertification `json:"product_certifications"`
	SizeChart             *SizeChart              `json:"size_chart"`
	Cod                   *Cod                    `json:"cod"`
	PackageDimension      *PackageDimension       `json:"package_dimension"`
	Epr                   *Epr                    `json:"epr"`
	Compliance            *CategoryCompliance     `json:"compliance"`
	Brand                 *BrandRule              `json:"brand"`
}

type CategoryCompliance struct {
	ResponsiblePerson *CategoryResponsiblePerson `json:"responsible_person"`
	Manufacturer      *CategoryManufacturer      `json:"manufacturer"`
}

type CategoryResponsiblePerson struct {
	IsRequired bool `json:"is_required"`
}

type CategoryManufacturer struct {
	IsRequired bool `json:"is_required"`
}

type SizeChart struct {
	IsRequired  bool `json:"is_required"`
	IsSupported bool `json:"is_supported"`
}

type BrandRule struct {
	IsRequired  bool `json:"is_required"`
	IsSupported bool `json:"is_supported"`
}

type Cod struct {
	IsSupported bool `json:"is_supported"`
}

type ProductCertification struct {
	ExternalId            string                          `json:"external_id"`
	ExternalName          string                          `json:"external_name"`
	IsRequired            bool                            `json:"is_required"`
	SampleImageUrl        string                          `json:"sample_image_url"`
	RequirementConditions []AttributeRequirementCondition `json:"requirement_conditions"`
}

type Epr struct {
	IsRequired bool `json:"is_required"`
}

type PackageDimension struct {
	IsRequired bool `json:"is_required"`
}

type ListArg struct {
	OrganizationID       string `validate:"required"`
	SalesChannelStoreKey string `validate:"required"`
	SalesChannelPlatform string `validate:"required"`
	RefreshCache         bool
}

func (arg *ListArg) convertToTikTokGetCategoriesParams() *tiktokapi.GetCategoriesParams {
	return &tiktokapi.GetCategoriesParams{
		CommonParams: tiktokapi.CommonParams{
			OrganizationID: arg.OrganizationID,
			AppName:        consts.AppFeed,
			AppKey:         arg.SalesChannelStoreKey,
		},
		RefreshCache: arg.RefreshCache,
	}
}

func (arg *ListArg) toSheinGetCategoriesParams(language string) *shein_proxy.GetCategoriesParams {
	return &shein_proxy.GetCategoriesParams{
		CommonParams: shein_proxy.CommonParams{
			OrganizationID: arg.OrganizationID,
			AppName:        consts.AppFeed,
			AppKey:         arg.SalesChannelStoreKey,
		},
		Language: language,
	}
}

type CategoriesOutput struct {
	Organization models.Organization `json:"organization"`
	SalesChannel models.SalesChannel `json:"sales_channel"`
	Categories   []Category          `json:"categories"`
}

type Category struct {
	ID            string `json:"id"`
	ProductTypeID string `json:"product_type_id"`
	LocalName     string `json:"local_name"`
	ParentID      string `json:"parent_id"`
	Status        string `json:"status"`
	IsLeaf        bool   `json:"is_leaf"`
}

type GetAttributesArg struct {
	OrganizationID       string `validate:"required"`
	SalesChannelStoreKey string `validate:"required"`
	SalesChannelPlatform string `validate:"required"`
	ExternalCategoryID   string `validate:"required"`
	IsRequired           bool
	Types                []string
	CategoryVersion      string `validate:"omitempty,oneof=v1 v2"`
	SalesChannelRegion   string
	RefreshCache         bool
	AllType              bool
}

func (arg *GetAttributesArg) convertToTikTokGetCategoryAttributesParams() *tiktokapi.GetCategoryAttributesParams {
	return &tiktokapi.GetCategoryAttributesParams{
		CommonParams: tiktokapi.CommonParams{
			OrganizationID: arg.OrganizationID,
			AppName:        consts.AppFeed,
			AppKey:         arg.SalesChannelStoreKey,
		},
		CategoryID:      arg.ExternalCategoryID,
		CategoryVersion: arg.CategoryVersion,
	}
}

func (arg *GetAttributesArg) convertToSheinGetAttributesParams(productType, language string) (*shein_proxy.GetAttributesParams, error) {
	productTypeInt64, err := strconv.ParseInt(productType, 10, 64)
	if err != nil {
		return nil, err
	}

	return &shein_proxy.GetAttributesParams{
		CommonParams: shein_proxy.CommonParams{
			OrganizationID: arg.OrganizationID,
			AppName:        consts.AppFeed,
			AppKey:         arg.SalesChannelStoreKey,
		},
		GetAttributesParams: shein_rest.GetAttributesParams{
			ProductTypeIdList: []int64{productTypeInt64},
		},
		Language: language,
	}, nil
}

func (arg *GetAttributesArg) convertToSheinGetCustomAttributePermissionConfigsParams() (*shein_proxy.GetCustomAttributePermissionConfigsParams, error) {
	categoryIDStr, err := strconv.ParseInt(arg.ExternalCategoryID, 10, 64)
	if err != nil {
		return nil, err
	}
	return &shein_proxy.GetCustomAttributePermissionConfigsParams{
		CommonParams: shein_proxy.CommonParams{
			OrganizationID: arg.OrganizationID,
			AppName:        consts.AppFeed,
			AppKey:         arg.SalesChannelStoreKey,
		},
		GetCustomAttributePermissionConfigsParams: shein_rest.GetCustomAttributePermissionConfigsParams{
			CategoryIdList: []int64{categoryIDStr},
		},
	}, nil
}

type AttributesOutput struct {
	Organization       models.Organization `json:"organization"`
	SalesChannel       models.SalesChannel `json:"sales_channel"`
	ExternalCategoryID string              `json:"external_category_id"`
	Attributes         []Attribute         `json:"attributes"`
}

type Attribute struct {
	ID                    string                          `json:"id"`
	Name                  string                          `json:"name"`
	Type                  string                          `json:"type"`
	InputType             string                          `json:"input_type"`
	IsRequired            bool                            `json:"is_required"`
	Values                []AttributeValue                `json:"values"`
	IsMultipleSelected    bool                            `json:"is_multiple_selected"`
	IsCustomizable        bool                            `json:"is_customizable"`
	ValueDataType         string                          `json:"value_data_type"`
	RequirementConditions []AttributeRequirementCondition `json:"requirement_conditions"`
}

type AttributeRequirementCondition struct {
	AttributeID      string `json:"attribute_id"`
	AttributeValueID string `json:"attribute_value_id"`
}

type AttributeValue struct {
	ID   string `json:"id"`
	Name string `json:"name"`
}

type CategoryVersionsArg struct {
	OrganizationID          string   `validate:"required"`
	SalesChannelStoreKey    string   `validate:"required"`
	SalesChannelPlatform    string   `validate:"required"`
	SalesChannelCategoryIDs []string `validate:"required"`
	RefreshCache            bool
}

type CategoryVersionsOutput struct {
	Organization     models.Organization `json:"organization"`
	SalesChannel     models.SalesChannel `json:"sales_channel"`
	CategoryVersions []CategoryVersion   `json:"category_versions"`
}

type CategoryVersion struct {
	CategoryID string `json:"category_id"`
	// Version 优先返回 v2
	Version string `json:"version"`
}

type FilterAttributesArg struct {
	IsRequired bool
	AllType    bool
	Types      []string
}

func FilterProductCategoryAttributes(attributes []Attribute, arg FilterAttributesArg) []Attribute {
	result := make([]Attribute, 0)

	attributeTypeSet := set.NewStringSet(consts.AttributeTypeProductProperty)
	if len(arg.Types) > 0 {
		attributeTypeSet = set.NewStringSet(arg.Types...)
	}
	for _, attribute := range attributes {
		if !arg.AllType && !attributeTypeSet.Contains(attribute.Type) {
			continue
		}

		if arg.IsRequired && (!attribute.IsRequired && len(attribute.RequirementConditions) == 0) {
			continue
		}

		result = append(result, attribute)
	}

	return result
}

func FilterTikTokShopRequiredRules(rule Rule) Rule {
	results := Rule{
		ExternalCategoryID: rule.ExternalCategoryID,
		SizeChart:          rule.SizeChart,
		Cod:                rule.Cod,
		PackageDimension:   rule.PackageDimension,
		Epr:                rule.Epr,
		Compliance:         rule.Compliance,
	}

	// only filter productCertifications
	productCertifications := make([]*ProductCertification, 0)
	for i := range rule.ProductCertifications {
		// isRequired || len(RequirementConditions) > 0
		if rule.ProductCertifications[i].IsRequired || len(rule.ProductCertifications[i].RequirementConditions) > 0 {
			productCertifications = append(productCertifications, rule.ProductCertifications[i])
		}
	}
	results.ProductCertifications = productCertifications

	return results
}

type attributeNode struct {
	Attribute         Attribute
	ChildrenAttribute []*Attribute
}

func (a *AttributesOutput) BuildDepthMap() map[string]int {
	tempMap := make(map[string]*attributeNode)

	// Populate the map with attributes and initialize their children
	for i := range a.Attributes {
		tempMap[a.Attributes[i].ID] = &attributeNode{
			Attribute:         a.Attributes[i],
			ChildrenAttribute: []*Attribute{},
		}
	}

	// Build the children relationships
	for _, node := range tempMap {
		for _, condition := range node.Attribute.RequirementConditions {
			if childNode, ok := tempMap[condition.AttributeID]; ok {
				childNode.ChildrenAttribute = append(childNode.ChildrenAttribute, &node.Attribute)
			}
		}
	}

	// Create the depth map
	depthMap := make(map[string]int)
	var traverse func(node *attributeNode, depth int)
	traverse = func(node *attributeNode, depth int) {
		depthMap[node.Attribute.ID] = depth
		for _, child := range node.ChildrenAttribute {
			traverse(tempMap[child.ID], depth+1)
		}
	}

	// Start traversal from root nodes
	for _, node := range tempMap {
		if len(node.Attribute.RequirementConditions) == 0 {
			traverse(node, 0)
		}
	}

	return depthMap
}
