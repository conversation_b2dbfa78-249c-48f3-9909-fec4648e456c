package category

import (
	"context"

	validator "github.com/go-playground/validator/v10"
	redis "github.com/go-redis/redis/v8"

	"github.com/AfterShip/connectors-library/sdks/shein_proxy"
	"github.com/AfterShip/pltf-pd-product-listings/internal/third_party/connectors"
	tiktokapi "github.com/AfterShip/pltf-pd-product-listings/internal/third_party/tiktok_api"
)

type Service interface {
	List(ctx context.Context, arg *ListArg) (CategoriesOutput, error)
	Versions(ctx context.Context, arg *CategoryVersionsArg) (CategoryVersionsOutput, error)
	GetCategoryAttributes(ctx context.Context, arg *GetAttributesArg) (AttributesOutput, error)
	GetCategoryRules(ctx context.Context, arg *GetRulesArg) (RulesOutput, error)
	// test mock
	PostCategoryAttributes(ctx context.Context, arg *AttributesOutput) (AttributesOutput, error)
	PostCategoryRules(ctx context.Context, arg *RulesOutput) (RulesOutput, error)
}

type serviceImpl struct {
	connectorService connectors.Service
	tiktokAPIService tiktokapi.Service
	sheinAPIService  shein_proxy.Service
	redisClient      *redis.Client
	validate         *validator.Validate
}

func NewService(tiktokAPIService tiktokapi.Service, sheinAPIService shein_proxy.Service, connectorService connectors.Service, redisClient *redis.Client) Service {
	return &serviceImpl{
		connectorService: connectorService,
		tiktokAPIService: tiktokAPIService,
		sheinAPIService:  sheinAPIService,
		redisClient:      redisClient,
		validate:         validator.New(),
	}
}
