package category

import (
	"reflect"
	"testing"

	shein_rest "github.com/AfterShip/connectors-ecommerce-sdk-go/shein/rest"
	tiktokres_v202309 "github.com/AfterShip/connectors-ecommerce-sdk-go/tiktok/rest/version202309"
	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/pltf-pd-product-listings/internal/consts"
)

func Test_convertSheinCategories(t *testing.T) {
	tests := []struct {
		name       string
		categories []shein_rest.Category
		want       []Category
	}{
		{
			name: "single category",
			categories: []shein_rest.Category{
				{
					CategoryId:       types.MakeInt64(1),
					ProductTypeId:    types.MakeInt64(1),
					CategoryName:     types.MakeString("Category 1"),
					ParentCategoryId: types.MakeInt64(0),
					LastCategory:     types.MakeBool(true),
				},
			},
			want: []Category{
				{
					ID:            "1",
					ProductTypeID: "1",
					LocalName:     "Category 1",
					ParentID:      "0",
					IsLeaf:        true,
					Status:        consts.CategoryStatusActive,
				},
			},
		},
		{
			name: "nested categories",
			categories: []shein_rest.Category{
				{
					CategoryId:       types.MakeInt64(1),
					CategoryName:     types.MakeString("Category 1"),
					ParentCategoryId: types.MakeInt64(0),
					LastCategory:     types.MakeBool(false),
					Children: []shein_rest.Category{
						{
							CategoryId:       types.MakeInt64(2),
							ProductTypeId:    types.MakeInt64(2),
							CategoryName:     types.MakeString("Category 2"),
							ParentCategoryId: types.MakeInt64(1),
							LastCategory:     types.MakeBool(true),
						},
					},
				},
			},
			want: []Category{
				{
					ID:            "1",
					ProductTypeID: "0",
					LocalName:     "Category 1",
					ParentID:      "0",
					Status:        consts.CategoryStatusActive,
				},
				{
					ID:            "2",
					ProductTypeID: "2",
					LocalName:     "Category 2",
					ParentID:      "1",
					IsLeaf:        true,
					Status:        consts.CategoryStatusActive,
				},
			},
		},
		{
			name: "multiple nested categories",
			categories: []shein_rest.Category{
				{
					CategoryId:       types.MakeInt64(1),
					CategoryName:     types.MakeString("Category 1"),
					ParentCategoryId: types.MakeInt64(0),
					LastCategory:     types.MakeBool(false),
					Children: []shein_rest.Category{
						{
							CategoryId:       types.MakeInt64(2),
							ProductTypeId:    types.MakeInt64(2),
							CategoryName:     types.MakeString("Category 2"),
							ParentCategoryId: types.MakeInt64(1),
							LastCategory:     types.MakeBool(true),
						},
						{
							CategoryId:       types.MakeInt64(3),
							ProductTypeId:    types.MakeInt64(3),
							CategoryName:     types.MakeString("Category 3"),
							ParentCategoryId: types.MakeInt64(1),
							LastCategory:     types.MakeBool(true),
						},
					},
				},
			},
			want: []Category{
				{
					ID:            "1",
					ProductTypeID: "0",
					LocalName:     "Category 1",
					ParentID:      "0",
					Status:        consts.CategoryStatusActive,
				},
				{
					ID:            "2",
					ProductTypeID: "2",
					LocalName:     "Category 2",
					ParentID:      "1",
					IsLeaf:        true,
					Status:        consts.CategoryStatusActive,
				},
				{
					ID:            "3",
					ProductTypeID: "3",
					LocalName:     "Category 3",
					ParentID:      "1",
					IsLeaf:        true,
					Status:        consts.CategoryStatusActive,
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := convertSheinCategories(tt.categories); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("convertSheinCategories() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_convertTikTokCategories(t *testing.T) {
	tests := []struct {
		name       string
		categories []tiktokres_v202309.Category
		want       []Category
	}{
		{
			name: "单个可用分类",
			categories: []tiktokres_v202309.Category{
				{
					Id:                 types.MakeString("101"),
					LocalName:          types.MakeString("电子产品"),
					ParentID:           types.MakeString("0"),
					IsLeaf:             types.MakeBool(true),
					PermissionStatuses: []string{consts.CategoryStatusAvailable},
				},
			},
			want: []Category{
				{
					ID:        "101",
					LocalName: "电子产品",
					ParentID:  "0",
					IsLeaf:    true,
					Status:    consts.CategoryStatusActive,
				},
			},
		},
		{
			name: "单个不可用分类",
			categories: []tiktokres_v202309.Category{
				{
					Id:                 types.MakeString("102"),
					LocalName:          types.MakeString("服装"),
					ParentID:           types.MakeString("0"),
					IsLeaf:             types.MakeBool(false),
					PermissionStatuses: []string{"RESTRICTED"},
				},
			},
			want: []Category{
				{
					ID:        "102",
					LocalName: "服装",
					ParentID:  "0",
					IsLeaf:    false,
					Status:    consts.CategoryStatusInactive,
				},
			},
		},
		{
			name: "多个分类混合状态",
			categories: []tiktokres_v202309.Category{
				{
					Id:                 types.MakeString("103"),
					LocalName:          types.MakeString("玩具"),
					ParentID:           types.MakeString("0"),
					IsLeaf:             types.MakeBool(true),
					PermissionStatuses: []string{"RESTRICTED", "PENDING"},
				},
				{
					Id:                 types.MakeString("104"),
					LocalName:          types.MakeString("食品"),
					ParentID:           types.MakeString("0"),
					IsLeaf:             types.MakeBool(true),
					PermissionStatuses: []string{consts.CategoryStatusAvailable, "PENDING"},
				},
			},
			want: []Category{
				{
					ID:        "103",
					LocalName: "玩具",
					ParentID:  "0",
					IsLeaf:    true,
					Status:    consts.CategoryStatusInactive,
				},
				{
					ID:        "104",
					LocalName: "食品",
					ParentID:  "0",
					IsLeaf:    true,
					Status:    consts.CategoryStatusActive,
				},
			},
		},
		{
			name: "空状态分类",
			categories: []tiktokres_v202309.Category{
				{
					Id:                 types.MakeString("105"),
					LocalName:          types.MakeString("书籍"),
					ParentID:           types.MakeString("0"),
					IsLeaf:             types.MakeBool(true),
					PermissionStatuses: []string{},
				},
			},
			want: []Category{
				{
					ID:        "105",
					LocalName: "书籍",
					ParentID:  "0",
					IsLeaf:    true,
					Status:    consts.CategoryStatusInactive,
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := convertTikTokCategories(tt.categories); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("convertTikTokCategories() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_convertTikTokCategoryResponsiblePerson(t *testing.T) {
	tests := []struct {
		name                         string
		ttsCategoryResponsiblePerson *tiktokres_v202309.CategoryResponsiblePerson
		want                         *CategoryResponsiblePerson
	}{
		{
			name: "ResponsiblePerson 为必填",
			ttsCategoryResponsiblePerson: &tiktokres_v202309.CategoryResponsiblePerson{
				IsRequired: types.MakeBool(true),
			},
			want: &CategoryResponsiblePerson{
				IsRequired: true,
			},
		},
		{
			name: "ResponsiblePerson 为非必填",
			ttsCategoryResponsiblePerson: &tiktokres_v202309.CategoryResponsiblePerson{
				IsRequired: types.MakeBool(false),
			},
			want: &CategoryResponsiblePerson{
				IsRequired: false,
			},
		},
		{
			name:                         "ResponsiblePerson 为空",
			ttsCategoryResponsiblePerson: nil,
			want: &CategoryResponsiblePerson{
				IsRequired: false,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := convertTikTokCategoryResponsiblePerson(tt.ttsCategoryResponsiblePerson); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("convertTikTokCategoryResponsiblePerson() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_convertTikTokCategoryManufacturer(t *testing.T) {
	tests := []struct {
		name                    string
		ttsCategoryManufacturer *tiktokres_v202309.CategoryManufacturer
		want                    *CategoryManufacturer
	}{
		{
			name: "Manufacturer 为必填",
			ttsCategoryManufacturer: &tiktokres_v202309.CategoryManufacturer{
				IsRequired: types.MakeBool(true),
			},
			want: &CategoryManufacturer{
				IsRequired: true,
			},
		},
		{
			name: "Manufacturer 为非必填",
			ttsCategoryManufacturer: &tiktokres_v202309.CategoryManufacturer{
				IsRequired: types.MakeBool(false),
			},
			want: &CategoryManufacturer{
				IsRequired: false,
			},
		},
		{
			name:                    "Manufacturer 为空",
			ttsCategoryManufacturer: nil,
			want: &CategoryManufacturer{
				IsRequired: false,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := convertTikTokCategoryManufacturer(tt.ttsCategoryManufacturer); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("convertTikTokCategoryManufacturer() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_convertTikTokSizeChart(t *testing.T) {
	tests := []struct {
		name      string
		sizeChart *tiktokres_v202309.CategorySizeChart
		want      *SizeChart
	}{
		{
			name: "SizeChart 为必填且支持",
			sizeChart: &tiktokres_v202309.CategorySizeChart{
				IsRequired:  types.MakeBool(true),
				IsSupported: types.MakeBool(true),
			},
			want: &SizeChart{
				IsRequired:  true,
				IsSupported: true,
			},
		},
		{
			name: "SizeChart 为非必填但支持",
			sizeChart: &tiktokres_v202309.CategorySizeChart{
				IsRequired:  types.MakeBool(false),
				IsSupported: types.MakeBool(true),
			},
			want: &SizeChart{
				IsRequired:  false,
				IsSupported: true,
			},
		},
		{
			name: "SizeChart 为必填但不支持",
			sizeChart: &tiktokres_v202309.CategorySizeChart{
				IsRequired:  types.MakeBool(true),
				IsSupported: types.MakeBool(false),
			},
			want: &SizeChart{
				IsRequired:  true,
				IsSupported: false,
			},
		},
		{
			name: "SizeChart 为非必填且不支持",
			sizeChart: &tiktokres_v202309.CategorySizeChart{
				IsRequired:  types.MakeBool(false),
				IsSupported: types.MakeBool(false),
			},
			want: &SizeChart{
				IsRequired:  false,
				IsSupported: false,
			},
		},
		{
			name:      "SizeChart 为空",
			sizeChart: nil,
			want: &SizeChart{
				IsRequired:  false,
				IsSupported: false,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := convertTikTokSizeChart(tt.sizeChart); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("convertTikTokSizeChart() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_convertTikTokCod(t *testing.T) {
	tests := []struct {
		name string
		cod  *tiktokres_v202309.CategoryCod
		want *Cod
	}{
		{
			name: "Cod 支持",
			cod: &tiktokres_v202309.CategoryCod{
				IsSupported: types.MakeBool(true),
			},
			want: &Cod{
				IsSupported: true,
			},
		},
		{
			name: "Cod 不支持",
			cod: &tiktokres_v202309.CategoryCod{
				IsSupported: types.MakeBool(false),
			},
			want: &Cod{
				IsSupported: false,
			},
		},
		{
			name: "Cod 为空",
			cod:  nil,
			want: &Cod{
				IsSupported: false,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := convertTikTokCod(tt.cod); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("convertTikTokCod() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_convertTikTokPackageDimension(t *testing.T) {
	tests := []struct {
		name             string
		packageDimension *tiktokres_v202309.CategoryPackageDimension
		want             *PackageDimension
	}{
		{
			name: "PackageDimension 为必填",
			packageDimension: &tiktokres_v202309.CategoryPackageDimension{
				IsRequired: types.MakeBool(true),
			},
			want: &PackageDimension{
				IsRequired: true,
			},
		},
		{
			name: "PackageDimension 为非必填",
			packageDimension: &tiktokres_v202309.CategoryPackageDimension{
				IsRequired: types.MakeBool(false),
			},
			want: &PackageDimension{
				IsRequired: false,
			},
		},
		{
			name:             "PackageDimension 为空",
			packageDimension: nil,
			want: &PackageDimension{
				IsRequired: false,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := convertTikTokPackageDimension(tt.packageDimension); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("convertTikTokPackageDimension() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_convertTikTokAttributeValueDataType(t *testing.T) {
	tests := []struct {
		name          string
		valueDataType string
		want          string
	}{
		{
			name:          "正数整数或小数转为数字类型",
			valueDataType: consts.AttributeValueDataTypePositiveIntOrDecimal,
			want:          consts.AttributeValueDataTypeNumber,
		},
		{
			name:          "其他类型返回空字符串",
			valueDataType: "OTHER_DATA_TYPE",
			want:          "",
		},
		{
			name:          "空字符串输入返回空字符串",
			valueDataType: "",
			want:          "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := convertTikTokAttributeValueDataType(tt.valueDataType); got != tt.want {
				t.Errorf("convertTikTokAttributeValueDataType() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestConvertSheinAttributeValues(t *testing.T) {
	tests := []struct {
		name   string
		values []shein_rest.AttributeValueInfo
		want   []AttributeValue
	}{
		{
			name: "正常多个属性值",
			values: []shein_rest.AttributeValueInfo{
				{
					AttributeValueId: types.MakeInt64(1001),
					AttributeValue:   types.MakeString("红色"),
				},
				{
					AttributeValueId: types.MakeInt64(1002),
					AttributeValue:   types.MakeString("蓝色"),
				},
			},
			want: []AttributeValue{
				{
					ID:   "1001",
					Name: "红色",
				},
				{
					ID:   "1002",
					Name: "蓝色",
				},
			},
		},
		{
			name: "单个属性值",
			values: []shein_rest.AttributeValueInfo{
				{
					AttributeValueId: types.MakeInt64(2001),
					AttributeValue:   types.MakeString("XL"),
				},
			},
			want: []AttributeValue{
				{
					ID:   "2001",
					Name: "XL",
				},
			},
		},
		{
			name:   "空属性值列表",
			values: []shein_rest.AttributeValueInfo{},
			want:   []AttributeValue{},
		},
		{
			name: "特殊字符属性值",
			values: []shein_rest.AttributeValueInfo{
				{
					AttributeValueId: types.MakeInt64(3001),
					AttributeValue:   types.MakeString("100% 纯棉"),
				},
				{
					AttributeValueId: types.MakeInt64(3002),
					AttributeValue:   types.MakeString("聚酯纤维/尼龙"),
				},
			},
			want: []AttributeValue{
				{
					ID:   "3001",
					Name: "100% 纯棉",
				},
				{
					ID:   "3002",
					Name: "聚酯纤维/尼龙",
				},
			},
		},
		{
			name: "零值ID属性值",
			values: []shein_rest.AttributeValueInfo{
				{
					AttributeValueId: types.MakeInt64(0),
					AttributeValue:   types.MakeString("默认值"),
				},
			},
			want: []AttributeValue{
				{
					ID:   "0",
					Name: "默认值",
				},
			},
		},
		{
			name: "空字符串属性值",
			values: []shein_rest.AttributeValueInfo{
				{
					AttributeValueId: types.MakeInt64(4001),
					AttributeValue:   types.MakeString(""),
				},
			},
			want: []AttributeValue{
				{
					ID:   "4001",
					Name: "",
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := convertSheinAttributeValues(tt.values)
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("convertSheinAttributeValues() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_convertTikTokAttributeValues(t *testing.T) {
	tests := []struct {
		name   string
		values []tiktokres_v202309.AttributeValue
		want   []AttributeValue
	}{
		{
			name: "正常多个属性值",
			values: []tiktokres_v202309.AttributeValue{
				{
					Id:   types.MakeString("1001"),
					Name: types.MakeString("红色"),
				},
				{
					Id:   types.MakeString("1002"),
					Name: types.MakeString("蓝色"),
				},
			},
			want: []AttributeValue{
				{
					ID:   "1001",
					Name: "红色",
				},
				{
					ID:   "1002",
					Name: "蓝色",
				},
			},
		},
		{
			name: "单个属性值",
			values: []tiktokres_v202309.AttributeValue{
				{
					Id:   types.MakeString("2001"),
					Name: types.MakeString("XL"),
				},
			},
			want: []AttributeValue{
				{
					ID:   "2001",
					Name: "XL",
				},
			},
		},
		{
			name:   "空属性值列表",
			values: []tiktokres_v202309.AttributeValue{},
			want:   []AttributeValue{},
		},
		{
			name: "特殊字符属性值",
			values: []tiktokres_v202309.AttributeValue{
				{
					Id:   types.MakeString("3001"),
					Name: types.MakeString("100% 纯棉"),
				},
				{
					Id:   types.MakeString("3002"),
					Name: types.MakeString("聚酯纤维/尼龙"),
				},
			},
			want: []AttributeValue{
				{
					ID:   "3001",
					Name: "100% 纯棉",
				},
				{
					ID:   "3002",
					Name: "聚酯纤维/尼龙",
				},
			},
		},
		{
			name: "空字符串属性值",
			values: []tiktokres_v202309.AttributeValue{
				{
					Id:   types.MakeString("4001"),
					Name: types.MakeString(""),
				},
			},
			want: []AttributeValue{
				{
					ID:   "4001",
					Name: "",
				},
			},
		},
		{
			name: "空ID属性值",
			values: []tiktokres_v202309.AttributeValue{
				{
					Id:   types.MakeString(""),
					Name: types.MakeString("默认值"),
				},
			},
			want: []AttributeValue{
				{
					ID:   "",
					Name: "默认值",
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := convertTikTokAttributeValues(tt.values)
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("convertTikTokAttributeValues() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_convertTikTokAttributeRequirementConditions(t *testing.T) {
	tests := []struct {
		name                  string
		requirementConditions []tiktokres_v202309.AttributeRequirementCondition
		want                  []AttributeRequirementCondition
	}{
		{
			name: "多个需求条件",
			requirementConditions: []tiktokres_v202309.AttributeRequirementCondition{
				{
					AttributeID:      types.MakeString("101"),
					AttributeValueID: types.MakeString("201"),
				},
				{
					AttributeID:      types.MakeString("102"),
					AttributeValueID: types.MakeString("202"),
				},
			},
			want: []AttributeRequirementCondition{
				{
					AttributeID:      "101",
					AttributeValueID: "201",
				},
				{
					AttributeID:      "102",
					AttributeValueID: "202",
				},
			},
		},
		{
			name: "单个需求条件",
			requirementConditions: []tiktokres_v202309.AttributeRequirementCondition{
				{
					AttributeID:      types.MakeString("103"),
					AttributeValueID: types.MakeString("203"),
				},
			},
			want: []AttributeRequirementCondition{
				{
					AttributeID:      "103",
					AttributeValueID: "203",
				},
			},
		},
		{
			name:                  "空需求条件列表",
			requirementConditions: []tiktokres_v202309.AttributeRequirementCondition{},
			want:                  []AttributeRequirementCondition{},
		},
		{
			name: "特殊字符ID",
			requirementConditions: []tiktokres_v202309.AttributeRequirementCondition{
				{
					AttributeID:      types.MakeString("attr-100"),
					AttributeValueID: types.MakeString("value_200"),
				},
			},
			want: []AttributeRequirementCondition{
				{
					AttributeID:      "attr-100",
					AttributeValueID: "value_200",
				},
			},
		},
		{
			name: "空ID值",
			requirementConditions: []tiktokres_v202309.AttributeRequirementCondition{
				{
					AttributeID:      types.MakeString(""),
					AttributeValueID: types.MakeString(""),
				},
			},
			want: []AttributeRequirementCondition{
				{
					AttributeID:      "",
					AttributeValueID: "",
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := convertTikTokAttributeRequirementConditions(tt.requirementConditions)
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("convertTikTokAttributeRequirementConditions() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_buildSheinAttributeType(t *testing.T) {
	tests := []struct {
		name           string
		attributeType  int64
		attributeLabel int64
		want           string
	}{
		{
			name:           "销售属性-主选项",
			attributeType:  shein_rest.AttributeTypeSaleAttribute,
			attributeLabel: shein_rest.AttributeLabelMain,
			want:           consts.AttributeTypeSalesPropertyMainOption,
		},
		{
			name:           "销售属性-非主选项",
			attributeType:  shein_rest.AttributeTypeSaleAttribute,
			attributeLabel: 0, // 非主选项
			want:           consts.AttributeTypeSalesProperty,
		},
		{
			name:           "尺码属性",
			attributeType:  shein_rest.AttributeTypeSizeAttribute,
			attributeLabel: 0,
			want:           consts.AttributeTypeSizeProperty,
		},
		{
			name:           "普通产品属性",
			attributeType:  0, // 其他类型
			attributeLabel: 0,
			want:           consts.AttributeTypeProductProperty,
		},
		{
			name:           "未知类型默认为产品属性",
			attributeType:  999, // 未知类型
			attributeLabel: 0,
			want:           consts.AttributeTypeProductProperty,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := buildSheinAttributeType(tt.attributeType, tt.attributeLabel)
			if got != tt.want {
				t.Errorf("buildSheinAttributeType() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_convertSheinAttributes(t *testing.T) {
	tests := []struct {
		name             string
		attributes       []shein_rest.AttributeInfo
		attributeConfigs []shein_rest.CustomAttributePermissionConfig
		want             []Attribute
	}{
		{
			name: "常规属性组合",
			attributes: []shein_rest.AttributeInfo{
				{
					AttributeId:     types.MakeInt64(1001),
					AttributeName:   types.MakeString("颜色"),
					AttributeStatus: types.MakeInt64(shein_rest.AttributeStatusRequired),
					AttributeType:   types.MakeInt64(shein_rest.AttributeTypeSaleAttribute),
					AttributeLabel:  types.MakeInt64(shein_rest.AttributeLabelMain),
					AttributeMode:   types.MakeInt64(shein_rest.AttributeModeSingleSelect),
					AttributeValueInfoList: []shein_rest.AttributeValueInfo{
						{
							AttributeValueId: types.MakeInt64(2001),
							AttributeValue:   types.MakeString("红色"),
						},
						{
							AttributeValueId: types.MakeInt64(2002),
							AttributeValue:   types.MakeString("蓝色"),
						},
					},
				},
				{
					AttributeId:     types.MakeInt64(1002),
					AttributeName:   types.MakeString("尺码"),
					AttributeStatus: types.MakeInt64(shein_rest.AttributeStatusRequired),
					AttributeType:   types.MakeInt64(shein_rest.AttributeTypeSizeAttribute),
					AttributeMode:   types.MakeInt64(shein_rest.AttributeModeMultiSelect),
					AttributeValueInfoList: []shein_rest.AttributeValueInfo{
						{
							AttributeValueId: types.MakeInt64(3001),
							AttributeValue:   types.MakeString("S"),
						},
						{
							AttributeValueId: types.MakeInt64(3002),
							AttributeValue:   types.MakeString("M"),
						},
					},
				},
				{
					AttributeId:     types.MakeInt64(1003),
					AttributeName:   types.MakeString("材质"),
					AttributeStatus: types.MakeInt64(shein_rest.AttributeStatusOptional),
					AttributeType:   types.MakeInt64(0), // 产品属性
					AttributeMode:   types.MakeInt64(shein_rest.AttributeModeSingleSelectManualInput),
					AttributeValueInfoList: []shein_rest.AttributeValueInfo{
						{
							AttributeValueId: types.MakeInt64(4001),
							AttributeValue:   types.MakeString("棉"),
						},
					},
				},
				{
					AttributeId:            types.MakeInt64(1004),
					AttributeName:          types.MakeString("描述"),
					AttributeStatus:        types.MakeInt64(shein_rest.AttributeStatusOptional),
					AttributeType:          types.MakeInt64(0),
					AttributeMode:          types.MakeInt64(shein_rest.AttributeModeManualInput),
					AttributeValueInfoList: []shein_rest.AttributeValueInfo{},
				},
				{
					AttributeId:            types.MakeInt64(1005),
					AttributeName:          types.MakeString("禁用属性"),
					AttributeStatus:        types.MakeInt64(shein_rest.AttributeStatusNotFilled),
					AttributeType:          types.MakeInt64(0),
					AttributeMode:          types.MakeInt64(shein_rest.AttributeModeSingleSelect),
					AttributeValueInfoList: []shein_rest.AttributeValueInfo{},
				},
			},
			attributeConfigs: []shein_rest.CustomAttributePermissionConfig{
				{
					AttributeId:   types.MakeInt64(1001),
					HasPermission: types.MakeInt64(shein_rest.HasPermissionNo),
				},
				{
					AttributeId:   types.MakeInt64(1003),
					HasPermission: types.MakeInt64(shein_rest.HasPermissionYes),
				},
			},
			want: []Attribute{
				{
					ID:                 "1001",
					Name:               "颜色",
					Type:               consts.AttributeTypeSalesPropertyMainOption,
					IsRequired:         true,
					InputType:          "",
					IsMultipleSelected: false,
					IsCustomizable:     false,
					Values: []AttributeValue{
						{
							ID:   "2001",
							Name: "红色",
						},
						{
							ID:   "2002",
							Name: "蓝色",
						},
					},
				},
				{
					ID:                 "1002",
					Name:               "尺码",
					Type:               consts.AttributeTypeSizeProperty,
					IsRequired:         true,
					InputType:          "",
					IsMultipleSelected: true,
					IsCustomizable:     false,
					Values: []AttributeValue{
						{
							ID:   "3001",
							Name: "S",
						},
						{
							ID:   "3002",
							Name: "M",
						},
					},
				},
				{
					ID:                 "1003",
					Name:               "材质",
					Type:               consts.AttributeTypeProductProperty,
					IsRequired:         false,
					InputType:          consts.AttributeInputTypeSelectManualInput,
					IsMultipleSelected: false,
					IsCustomizable:     true,
					Values: []AttributeValue{
						{
							ID:   "4001",
							Name: "棉",
						},
					},
				},
				{
					ID:                 "1004",
					Name:               "描述",
					Type:               consts.AttributeTypeProductProperty,
					IsRequired:         false,
					InputType:          "",
					IsMultipleSelected: false,
					IsCustomizable:     true,
					Values:             []AttributeValue{},
				},
			},
		},
		{
			name:             "空属性列表",
			attributes:       []shein_rest.AttributeInfo{},
			attributeConfigs: []shein_rest.CustomAttributePermissionConfig{},
			want:             []Attribute{},
		},
		{
			name: "仅禁用属性",
			attributes: []shein_rest.AttributeInfo{
				{
					AttributeId:            types.MakeInt64(1005),
					AttributeName:          types.MakeString("禁用属性"),
					AttributeStatus:        types.MakeInt64(shein_rest.AttributeStatusNotFilled),
					AttributeType:          types.MakeInt64(0),
					AttributeMode:          types.MakeInt64(shein_rest.AttributeModeSingleSelect),
					AttributeValueInfoList: []shein_rest.AttributeValueInfo{},
				},
			},
			attributeConfigs: []shein_rest.CustomAttributePermissionConfig{},
			want:             []Attribute{},
		},
		{
			name: "销售属性非主选项",
			attributes: []shein_rest.AttributeInfo{
				{
					AttributeId:     types.MakeInt64(1006),
					AttributeName:   types.MakeString("款式"),
					AttributeStatus: types.MakeInt64(shein_rest.AttributeStatusRequired),
					AttributeType:   types.MakeInt64(shein_rest.AttributeTypeSaleAttribute),
					AttributeLabel:  types.MakeInt64(0), // 非主选项
					AttributeMode:   types.MakeInt64(shein_rest.AttributeModeSingleSelect),
					AttributeValueInfoList: []shein_rest.AttributeValueInfo{
						{
							AttributeValueId: types.MakeInt64(5001),
							AttributeValue:   types.MakeString("A款"),
						},
					},
				},
			},
			attributeConfigs: []shein_rest.CustomAttributePermissionConfig{},
			want: []Attribute{
				{
					ID:                 "1006",
					Name:               "款式",
					Type:               consts.AttributeTypeSalesProperty,
					IsRequired:         true,
					InputType:          "",
					IsMultipleSelected: false,
					IsCustomizable:     false,
					Values: []AttributeValue{
						{
							ID:   "5001",
							Name: "A款",
						},
					},
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := convertSheinAttributes(tt.attributes, tt.attributeConfigs)
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("convertSheinAttributes() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_convertTikTokCategoryAttributes(t *testing.T) {
	tests := []struct {
		name       string
		attributes []tiktokres_v202309.Attributes
		want       []Attribute
	}{
		{
			name: "多种类型属性",
			attributes: []tiktokres_v202309.Attributes{
				{
					Id:                 types.MakeString("1001"),
					Name:               types.MakeString("颜色"),
					Type:               types.MakeString(consts.AttributeTypeSalesPropertyMainOption),
					IsRequired:         types.MakeBool(true),
					IsMultipleSelected: types.MakeBool(false),
					IsCustomizable:     types.MakeBool(false),
					ValueDataFormat:    types.MakeString(""),
					Values: []tiktokres_v202309.AttributeValue{
						{
							Id:   types.MakeString("2001"),
							Name: types.MakeString("红色"),
						},
						{
							Id:   types.MakeString("2002"),
							Name: types.MakeString("蓝色"),
						},
					},
					RequirementConditions: []tiktokres_v202309.AttributeRequirementCondition{
						{
							AttributeID:      types.MakeString("3001"),
							AttributeValueID: types.MakeString("4001"),
						},
					},
				},
				{
					Id:                 types.MakeString("1002"),
					Name:               types.MakeString("尺码"),
					Type:               types.MakeString(consts.AttributeTypeSizeProperty),
					IsRequired:         types.MakeBool(true),
					IsMultipleSelected: types.MakeBool(true),
					IsCustomizable:     types.MakeBool(false),
					ValueDataFormat:    types.MakeString(""),
					Values: []tiktokres_v202309.AttributeValue{
						{
							Id:   types.MakeString("5001"),
							Name: types.MakeString("S"),
						},
						{
							Id:   types.MakeString("5002"),
							Name: types.MakeString("M"),
						},
					},
				},
				{
					Id:                 types.MakeString("1003"),
					Name:               types.MakeString("重量"),
					Type:               types.MakeString(consts.AttributeTypeProductProperty),
					IsRequired:         types.MakeBool(false),
					IsMultipleSelected: types.MakeBool(false),
					IsCustomizable:     types.MakeBool(true),
					ValueDataFormat:    types.MakeString(consts.AttributeValueDataTypePositiveIntOrDecimal),
					Values:             []tiktokres_v202309.AttributeValue{},
				},
			},
			want: []Attribute{
				{
					ID:                 "1001",
					Name:               "颜色",
					Type:               consts.AttributeTypeSalesPropertyMainOption,
					IsRequired:         true,
					IsMultipleSelected: false,
					IsCustomizable:     false,
					ValueDataType:      "",
					Values: []AttributeValue{
						{
							ID:   "2001",
							Name: "红色",
						},
						{
							ID:   "2002",
							Name: "蓝色",
						},
					},
					RequirementConditions: []AttributeRequirementCondition{
						{
							AttributeID:      "3001",
							AttributeValueID: "4001",
						},
					},
				},
				{
					ID:                 "1002",
					Name:               "尺码",
					Type:               consts.AttributeTypeSizeProperty,
					IsRequired:         true,
					IsMultipleSelected: true,
					IsCustomizable:     false,
					ValueDataType:      "",
					Values: []AttributeValue{
						{
							ID:   "5001",
							Name: "S",
						},
						{
							ID:   "5002",
							Name: "M",
						},
					},
					RequirementConditions: nil,
				},
				{
					ID:                    "1003",
					Name:                  "重量",
					Type:                  consts.AttributeTypeProductProperty,
					IsRequired:            false,
					IsMultipleSelected:    false,
					IsCustomizable:        true,
					ValueDataType:         consts.AttributeValueDataTypeNumber,
					Values:                []AttributeValue{},
					RequirementConditions: nil,
				},
			},
		},
		{
			name:       "空属性列表",
			attributes: []tiktokres_v202309.Attributes{},
			want:       []Attribute{},
		},
		{
			name: "单个属性",
			attributes: []tiktokres_v202309.Attributes{
				{
					Id:                 types.MakeString("1004"),
					Name:               types.MakeString("材质"),
					Type:               types.MakeString(consts.AttributeTypeProductProperty),
					IsRequired:         types.MakeBool(true),
					IsMultipleSelected: types.MakeBool(false),
					IsCustomizable:     types.MakeBool(true),
					Values: []tiktokres_v202309.AttributeValue{
						{
							Id:   types.MakeString("6001"),
							Name: types.MakeString("棉"),
						},
						{
							Id:   types.MakeString("6002"),
							Name: types.MakeString("聚酯"),
						},
					},
				},
			},
			want: []Attribute{
				{
					ID:                 "1004",
					Name:               "材质",
					Type:               consts.AttributeTypeProductProperty,
					IsRequired:         true,
					IsMultipleSelected: false,
					IsCustomizable:     true,
					ValueDataType:      "",
					Values: []AttributeValue{
						{
							ID:   "6001",
							Name: "棉",
						},
						{
							ID:   "6002",
							Name: "聚酯",
						},
					},
					RequirementConditions: nil,
				},
			},
		},
		{
			name: "特殊数据类型",
			attributes: []tiktokres_v202309.Attributes{
				{
					Id:                 types.MakeString("1005"),
					Name:               types.MakeString("价格"),
					Type:               types.MakeString(consts.AttributeTypeProductProperty),
					IsRequired:         types.MakeBool(true),
					IsMultipleSelected: types.MakeBool(false),
					IsCustomizable:     types.MakeBool(true),
					ValueDataFormat:    types.MakeString(consts.AttributeValueDataTypePositiveIntOrDecimal),
					Values:             []tiktokres_v202309.AttributeValue{},
				},
				{
					Id:                 types.MakeString("1006"),
					Name:               types.MakeString("描述"),
					Type:               types.MakeString(consts.AttributeTypeProductProperty),
					IsRequired:         types.MakeBool(false),
					IsMultipleSelected: types.MakeBool(false),
					IsCustomizable:     types.MakeBool(true),
					ValueDataFormat:    types.MakeString("unknown_type"),
					Values:             []tiktokres_v202309.AttributeValue{},
				},
			},
			want: []Attribute{
				{
					ID:                    "1005",
					Name:                  "价格",
					Type:                  consts.AttributeTypeProductProperty,
					IsRequired:            true,
					IsMultipleSelected:    false,
					IsCustomizable:        true,
					ValueDataType:         consts.AttributeValueDataTypeNumber,
					Values:                []AttributeValue{},
					RequirementConditions: nil,
				},
				{
					ID:                    "1006",
					Name:                  "描述",
					Type:                  consts.AttributeTypeProductProperty,
					IsRequired:            false,
					IsMultipleSelected:    false,
					IsCustomizable:        true,
					ValueDataType:         "",
					Values:                []AttributeValue{},
					RequirementConditions: nil,
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := convertTikTokCategoryAttributes(tt.attributes)

			// 检查长度是否一致
			if len(got) != len(tt.want) {
				t.Errorf("convertTikTokCategoryAttributes() length = %v, want %v", len(got), len(tt.want))
				return
			}

			// 逐个对比属性字段
			for i, gotAttr := range got {
				wantAttr := tt.want[i]

				// 对比基础字段
				if gotAttr.ID != wantAttr.ID {
					t.Errorf("Attribute[%d].ID = %v, want %v", i, gotAttr.ID, wantAttr.ID)
				}
				if gotAttr.Name != wantAttr.Name {
					t.Errorf("Attribute[%d].Name = %v, want %v", i, gotAttr.Name, wantAttr.Name)
				}
				if gotAttr.Type != wantAttr.Type {
					t.Errorf("Attribute[%d].Type = %v, want %v", i, gotAttr.Type, wantAttr.Type)
				}
				if gotAttr.IsRequired != wantAttr.IsRequired {
					t.Errorf("Attribute[%d].IsRequired = %v, want %v", i, gotAttr.IsRequired, wantAttr.IsRequired)
				}
				if gotAttr.IsMultipleSelected != wantAttr.IsMultipleSelected {
					t.Errorf("Attribute[%d].IsMultipleSelected = %v, want %v", i, gotAttr.IsMultipleSelected, wantAttr.IsMultipleSelected)
				}
				if gotAttr.IsCustomizable != wantAttr.IsCustomizable {
					t.Errorf("Attribute[%d].IsCustomizable = %v, want %v", i, gotAttr.IsCustomizable, wantAttr.IsCustomizable)
				}
				if gotAttr.InputType != wantAttr.InputType {
					t.Errorf("Attribute[%d].InputType = %v, want %v", i, gotAttr.InputType, wantAttr.InputType)
				}
				if gotAttr.ValueDataType != wantAttr.ValueDataType {
					t.Errorf("Attribute[%d].ValueDataType = %v, want %v", i, gotAttr.ValueDataType, wantAttr.ValueDataType)
				}

				// 检查Values数组
				if len(gotAttr.Values) != len(wantAttr.Values) {
					t.Errorf("Attribute[%d].Values length = %v, want %v", i, len(gotAttr.Values), len(wantAttr.Values))
				} else {
					for j, gotVal := range gotAttr.Values {
						wantVal := wantAttr.Values[j]
						if gotVal.ID != wantVal.ID || gotVal.Name != wantVal.Name {
							t.Errorf("Attribute[%d].Values[%d] = {ID:%v, Name:%v}, want {ID:%v, Name:%v}",
								i, j, gotVal.ID, gotVal.Name, wantVal.ID, wantVal.Name)
						}
					}
				}

				// 检查RequirementConditions数组
				if len(gotAttr.RequirementConditions) != len(wantAttr.RequirementConditions) {
					t.Errorf("Attribute[%d].RequirementConditions length = %v, want %v", i,
						len(gotAttr.RequirementConditions), len(wantAttr.RequirementConditions))
				} else {
					for j, gotCond := range gotAttr.RequirementConditions {
						wantCond := wantAttr.RequirementConditions[j]
						if gotCond.AttributeID != wantCond.AttributeID || gotCond.AttributeValueID != wantCond.AttributeValueID {
							t.Errorf("Attribute[%d].RequirementConditions[%d] = {ID:%v, Value:%v}, want {ID:%v, Value:%v}",
								i, j, gotCond.AttributeID, gotCond.AttributeValueID, wantCond.AttributeID, wantCond.AttributeValueID)
						}
					}
				}
			}
		})
	}
}

func Test_convertTikTokProductCertifications(t *testing.T) {
	tests := []struct {
		name           string
		certifications []tiktokres_v202309.CategoryProductCertification
		want           []*ProductCertification
	}{
		{
			name: "多个认证信息",
			certifications: []tiktokres_v202309.CategoryProductCertification{
				{
					ID:             types.MakeString("cert-001"),
					Name:           types.MakeString("CE认证"),
					IsRequired:     types.MakeBool(true),
					SampleImageUrl: types.MakeString("https://example.com/cert/ce.jpg"),
					RequirementConditions: []tiktokres_v202309.AttributeRequirementCondition{
						{
							AttributeID:      types.MakeString("attr-001"),
							AttributeValueID: types.MakeString("val-001"),
						},
					},
				},
				{
					ID:                    types.MakeString("cert-002"),
					Name:                  types.MakeString("RoHS认证"),
					IsRequired:            types.MakeBool(false),
					SampleImageUrl:        types.MakeString("https://example.com/cert/rohs.jpg"),
					RequirementConditions: []tiktokres_v202309.AttributeRequirementCondition{},
				},
			},
			want: []*ProductCertification{
				{
					ExternalId:     "cert-001",
					ExternalName:   "CE认证",
					IsRequired:     true,
					SampleImageUrl: "https://example.com/cert/ce.jpg",
					RequirementConditions: []AttributeRequirementCondition{
						{
							AttributeID:      "attr-001",
							AttributeValueID: "val-001",
						},
					},
				},
				{
					ExternalId:            "cert-002",
					ExternalName:          "RoHS认证",
					IsRequired:            false,
					SampleImageUrl:        "https://example.com/cert/rohs.jpg",
					RequirementConditions: []AttributeRequirementCondition{},
				},
			},
		},
		{
			name:           "空认证列表",
			certifications: []tiktokres_v202309.CategoryProductCertification{},
			want:           []*ProductCertification{},
		},
		{
			name: "单个认证信息",
			certifications: []tiktokres_v202309.CategoryProductCertification{
				{
					ID:             types.MakeString("cert-003"),
					Name:           types.MakeString("FDA认证"),
					IsRequired:     types.MakeBool(true),
					SampleImageUrl: types.MakeString("https://example.com/cert/fda.jpg"),
					RequirementConditions: []tiktokres_v202309.AttributeRequirementCondition{
						{
							AttributeID:      types.MakeString("attr-002"),
							AttributeValueID: types.MakeString("val-002"),
						},
						{
							AttributeID:      types.MakeString("attr-003"),
							AttributeValueID: types.MakeString("val-003"),
						},
					},
				},
			},
			want: []*ProductCertification{
				{
					ExternalId:     "cert-003",
					ExternalName:   "FDA认证",
					IsRequired:     true,
					SampleImageUrl: "https://example.com/cert/fda.jpg",
					RequirementConditions: []AttributeRequirementCondition{
						{
							AttributeID:      "attr-002",
							AttributeValueID: "val-002",
						},
						{
							AttributeID:      "attr-003",
							AttributeValueID: "val-003",
						},
					},
				},
			},
		},
		{
			name: "特殊字符认证信息",
			certifications: []tiktokres_v202309.CategoryProductCertification{
				{
					ID:                    types.MakeString("cert-004"),
					Name:                  types.MakeString("测试认证(100%)"),
					IsRequired:            types.MakeBool(true),
					SampleImageUrl:        types.MakeString("https://example.com/cert/test.jpg?param=value&other=123"),
					RequirementConditions: []tiktokres_v202309.AttributeRequirementCondition{},
				},
			},
			want: []*ProductCertification{
				{
					ExternalId:            "cert-004",
					ExternalName:          "测试认证(100%)",
					IsRequired:            true,
					SampleImageUrl:        "https://example.com/cert/test.jpg?param=value&other=123",
					RequirementConditions: []AttributeRequirementCondition{},
				},
			},
		},
		{
			name: "空字段认证信息",
			certifications: []tiktokres_v202309.CategoryProductCertification{
				{
					ID:                    types.MakeString("cert-005"),
					Name:                  types.MakeString(""),
					IsRequired:            types.MakeBool(false),
					SampleImageUrl:        types.MakeString(""),
					RequirementConditions: []tiktokres_v202309.AttributeRequirementCondition{},
				},
			},
			want: []*ProductCertification{
				{
					ExternalId:            "cert-005",
					ExternalName:          "",
					IsRequired:            false,
					SampleImageUrl:        "",
					RequirementConditions: []AttributeRequirementCondition{},
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := convertTikTokProductCertifications(tt.certifications)
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("convertTikTokProductCertifications() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_convertTikTokCategoryCompliance(t *testing.T) {
	tests := []struct {
		name string
		rule *tiktokres_v202309.CategoryRule
		want *CategoryCompliance
	}{
		{
			name: "完整合规信息",
			rule: &tiktokres_v202309.CategoryRule{
				ResponsiblePerson: &tiktokres_v202309.CategoryResponsiblePerson{
					IsRequired: types.MakeBool(true),
				},
				Manufacturer: &tiktokres_v202309.CategoryManufacturer{
					IsRequired: types.MakeBool(true),
				},
			},
			want: &CategoryCompliance{
				ResponsiblePerson: &CategoryResponsiblePerson{
					IsRequired: true,
				},
				Manufacturer: &CategoryManufacturer{
					IsRequired: true,
				},
			},
		},
		{
			name: "仅需要责任人信息",
			rule: &tiktokres_v202309.CategoryRule{
				ResponsiblePerson: &tiktokres_v202309.CategoryResponsiblePerson{
					IsRequired: types.MakeBool(true),
				},
				Manufacturer: &tiktokres_v202309.CategoryManufacturer{
					IsRequired: types.MakeBool(false),
				},
			},
			want: &CategoryCompliance{
				ResponsiblePerson: &CategoryResponsiblePerson{
					IsRequired: true,
				},
				Manufacturer: &CategoryManufacturer{
					IsRequired: false,
				},
			},
		},
		{
			name: "仅需要制造商信息",
			rule: &tiktokres_v202309.CategoryRule{
				ResponsiblePerson: &tiktokres_v202309.CategoryResponsiblePerson{
					IsRequired: types.MakeBool(false),
				},
				Manufacturer: &tiktokres_v202309.CategoryManufacturer{
					IsRequired: types.MakeBool(true),
				},
			},
			want: &CategoryCompliance{
				ResponsiblePerson: &CategoryResponsiblePerson{
					IsRequired: false,
				},
				Manufacturer: &CategoryManufacturer{
					IsRequired: true,
				},
			},
		},
		{
			name: "不需要合规信息",
			rule: &tiktokres_v202309.CategoryRule{
				ResponsiblePerson: &tiktokres_v202309.CategoryResponsiblePerson{
					IsRequired: types.MakeBool(false),
				},
				Manufacturer: &tiktokres_v202309.CategoryManufacturer{
					IsRequired: types.MakeBool(false),
				},
			},
			want: &CategoryCompliance{
				ResponsiblePerson: &CategoryResponsiblePerson{
					IsRequired: false,
				},
				Manufacturer: &CategoryManufacturer{
					IsRequired: false,
				},
			},
		},
		{
			name: "合规信息为空",
			rule: &tiktokres_v202309.CategoryRule{
				ResponsiblePerson: nil,
				Manufacturer:      nil,
			},
			want: &CategoryCompliance{
				ResponsiblePerson: &CategoryResponsiblePerson{
					IsRequired: false,
				},
				Manufacturer: &CategoryManufacturer{
					IsRequired: false,
				},
			},
		},
		{
			name: "部分合规信息为空",
			rule: &tiktokres_v202309.CategoryRule{
				ResponsiblePerson: &tiktokres_v202309.CategoryResponsiblePerson{
					IsRequired: types.MakeBool(true),
				},
				Manufacturer: nil,
			},
			want: &CategoryCompliance{
				ResponsiblePerson: &CategoryResponsiblePerson{
					IsRequired: true,
				},
				Manufacturer: &CategoryManufacturer{
					IsRequired: false,
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := convertTikTokCategoryCompliance(tt.rule)
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("convertTikTokCategoryCompliance() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_convertTikTokEpr(t *testing.T) {
	tests := []struct {
		name   string
		ttsEpr *tiktokres_v202309.CategoryEpr
		want   *Epr
	}{
		{
			name: "EPR 为必填",
			ttsEpr: &tiktokres_v202309.CategoryEpr{
				IsRequired: types.MakeBool(true),
			},
			want: &Epr{
				IsRequired: true,
			},
		},
		{
			name: "EPR 为非必填",
			ttsEpr: &tiktokres_v202309.CategoryEpr{
				IsRequired: types.MakeBool(false),
			},
			want: &Epr{
				IsRequired: false,
			},
		},
		{
			name:   "EPR 为空",
			ttsEpr: nil,
			want: &Epr{
				IsRequired: false,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := convertTikTokEpr(tt.ttsEpr)
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("convertTikTokEpr() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_convertSheinCategoryRules(t *testing.T) {
	tests := []struct {
		name           string
		rule           shein_rest.GetProductPublishFillStandardResp
		sizeAttributes []Attribute
		want           Rule
	}{
		{
			name: "有品牌要求且有尺码图要求",
			rule: shein_rest.GetProductPublishFillStandardResp{
				FillInStandardList: []shein_rest.FillInStandard{
					{
						Module:   types.MakeString(shein_rest.FillStandardModuleBasic),
						FieldKey: types.MakeString(shein_rest.FillStandardFieldKeyBrandCode),
						Required: types.MakeBool(true),
					},
					{
						Module:   types.MakeString(shein_rest.FillStandardModuleBasic),
						FieldKey: types.MakeString("other_field"),
						Required: types.MakeBool(true),
					},
				},
			},
			sizeAttributes: []Attribute{
				{
					ID:         "1001",
					Name:       "尺码",
					Type:       consts.AttributeTypeSizeProperty,
					IsRequired: true,
				},
			},
			want: Rule{
				SizeChart: &SizeChart{
					IsRequired:  true,
					IsSupported: true,
				},
				PackageDimension: &PackageDimension{
					IsRequired: true,
				},
				Brand: &BrandRule{
					IsSupported: true,
					IsRequired:  true,
				},
			},
		},
		{
			name: "无品牌要求且有非必填尺码图",
			rule: shein_rest.GetProductPublishFillStandardResp{
				FillInStandardList: []shein_rest.FillInStandard{
					{
						Module:   types.MakeString(shein_rest.FillStandardModuleBasic),
						FieldKey: types.MakeString(shein_rest.FillStandardFieldKeyBrandCode),
						Required: types.MakeBool(false),
					},
				},
			},
			sizeAttributes: []Attribute{
				{
					ID:         "1001",
					Name:       "尺码",
					Type:       consts.AttributeTypeSizeProperty,
					IsRequired: false,
				},
			},
			want: Rule{
				SizeChart: &SizeChart{
					IsRequired:  false,
					IsSupported: true,
				},
				PackageDimension: &PackageDimension{
					IsRequired: true,
				},
				Brand: &BrandRule{
					IsSupported: true,
					IsRequired:  false,
				},
			},
		},
		{
			name: "无品牌字段且无尺码属性",
			rule: shein_rest.GetProductPublishFillStandardResp{
				FillInStandardList: []shein_rest.FillInStandard{
					{
						Module:   types.MakeString("other_module"),
						FieldKey: types.MakeString("other_field"),
						Required: types.MakeBool(true),
					},
				},
			},
			sizeAttributes: []Attribute{},
			want: Rule{
				SizeChart: &SizeChart{
					IsRequired:  false,
					IsSupported: false,
				},
				PackageDimension: &PackageDimension{
					IsRequired: true,
				},
				Brand: &BrandRule{
					IsSupported: true,
					IsRequired:  false,
				},
			},
		},
		{
			name: "空填写标准列表",
			rule: shein_rest.GetProductPublishFillStandardResp{
				FillInStandardList: []shein_rest.FillInStandard{},
			},
			sizeAttributes: []Attribute{},
			want: Rule{
				SizeChart: &SizeChart{
					IsRequired:  false,
					IsSupported: false,
				},
				PackageDimension: &PackageDimension{
					IsRequired: true,
				},
				Brand: &BrandRule{
					IsSupported: true,
					IsRequired:  false,
				},
			},
		},
		{
			name: "多个尺码属性，至少一个必填",
			rule: shein_rest.GetProductPublishFillStandardResp{
				FillInStandardList: []shein_rest.FillInStandard{},
			},
			sizeAttributes: []Attribute{
				{
					ID:         "1001",
					Name:       "尺码1",
					Type:       consts.AttributeTypeSizeProperty,
					IsRequired: false,
				},
				{
					ID:         "1002",
					Name:       "尺码2",
					Type:       consts.AttributeTypeSizeProperty,
					IsRequired: true,
				},
			},
			want: Rule{
				SizeChart: &SizeChart{
					IsRequired:  true,
					IsSupported: true,
				},
				PackageDimension: &PackageDimension{
					IsRequired: true,
				},
				Brand: &BrandRule{
					IsSupported: true,
					IsRequired:  false,
				},
			},
		},
		{
			name: "多个尺码属性，全部非必填",
			rule: shein_rest.GetProductPublishFillStandardResp{
				FillInStandardList: []shein_rest.FillInStandard{},
			},
			sizeAttributes: []Attribute{
				{
					ID:         "1001",
					Name:       "尺码1",
					Type:       consts.AttributeTypeSizeProperty,
					IsRequired: false,
				},
				{
					ID:         "1002",
					Name:       "尺码2",
					Type:       consts.AttributeTypeSizeProperty,
					IsRequired: false,
				},
			},
			want: Rule{
				SizeChart: &SizeChart{
					IsRequired:  false,
					IsSupported: true,
				},
				PackageDimension: &PackageDimension{
					IsRequired: true,
				},
				Brand: &BrandRule{
					IsSupported: true,
					IsRequired:  false,
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := convertSheinCategoryRules(tt.rule, tt.sizeAttributes)
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("convertSheinCategoryRules() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_convertTikTokCategoryRules(t *testing.T) {
	tests := []struct {
		name string
		rule *tiktokres_v202309.CategoryRule
		want Rule
	}{
		{
			name: "完整规则信息",
			rule: &tiktokres_v202309.CategoryRule{
				ProductCertifications: []tiktokres_v202309.CategoryProductCertification{
					{
						ID:         types.MakeString("cert-001"),
						Name:       types.MakeString("CE认证"),
						IsRequired: types.MakeBool(true),
					},
				},
				SizeChart: &tiktokres_v202309.CategorySizeChart{
					IsRequired:  types.MakeBool(true),
					IsSupported: types.MakeBool(true),
				},
				Cod: &tiktokres_v202309.CategoryCod{
					IsSupported: types.MakeBool(true),
				},
				PackageDimension: &tiktokres_v202309.CategoryPackageDimension{
					IsRequired: types.MakeBool(true),
				},
				Epr: &tiktokres_v202309.CategoryEpr{
					IsRequired: types.MakeBool(true),
				},
				ResponsiblePerson: &tiktokres_v202309.CategoryResponsiblePerson{
					IsRequired: types.MakeBool(true),
				},
				Manufacturer: &tiktokres_v202309.CategoryManufacturer{
					IsRequired: types.MakeBool(true),
				},
			},
			want: Rule{
				ProductCertifications: []*ProductCertification{
					{
						ExternalId:            "cert-001",
						ExternalName:          "CE认证",
						IsRequired:            true,
						SampleImageUrl:        "",
						RequirementConditions: []AttributeRequirementCondition{},
					},
				},
				SizeChart: &SizeChart{
					IsRequired:  true,
					IsSupported: true,
				},
				Cod: &Cod{
					IsSupported: true,
				},
				PackageDimension: &PackageDimension{
					IsRequired: true,
				},
				Epr: &Epr{
					IsRequired: true,
				},
				Compliance: &CategoryCompliance{
					ResponsiblePerson: &CategoryResponsiblePerson{
						IsRequired: true,
					},
					Manufacturer: &CategoryManufacturer{
						IsRequired: true,
					},
				},
				Brand: &BrandRule{
					IsSupported: true,
					IsRequired:  false,
				},
			},
		},
		{
			name: "部分规则信息为空",
			rule: &tiktokres_v202309.CategoryRule{
				ProductCertifications: []tiktokres_v202309.CategoryProductCertification{},
				SizeChart:             nil,
				Cod:                   nil,
				PackageDimension:      nil,
				Epr:                   nil,
				ResponsiblePerson:     nil,
				Manufacturer:          nil,
			},
			want: Rule{
				ProductCertifications: []*ProductCertification{},
				SizeChart: &SizeChart{
					IsRequired:  false,
					IsSupported: false,
				},
				Cod: &Cod{
					IsSupported: false,
				},
				PackageDimension: &PackageDimension{
					IsRequired: false,
				},
				Epr: &Epr{
					IsRequired: false,
				},
				Compliance: &CategoryCompliance{
					ResponsiblePerson: &CategoryResponsiblePerson{
						IsRequired: false,
					},
					Manufacturer: &CategoryManufacturer{
						IsRequired: false,
					},
				},
				Brand: &BrandRule{
					IsSupported: true,
					IsRequired:  false,
				},
			},
		},
		{
			name: "混合规则信息",
			rule: &tiktokres_v202309.CategoryRule{
				ProductCertifications: []tiktokres_v202309.CategoryProductCertification{
					{
						ID:         types.MakeString("cert-002"),
						Name:       types.MakeString("RoHS认证"),
						IsRequired: types.MakeBool(false),
					},
				},
				SizeChart: &tiktokres_v202309.CategorySizeChart{
					IsRequired:  types.MakeBool(false),
					IsSupported: types.MakeBool(true),
				},
				Cod: &tiktokres_v202309.CategoryCod{
					IsSupported: types.MakeBool(false),
				},
				PackageDimension: &tiktokres_v202309.CategoryPackageDimension{
					IsRequired: types.MakeBool(true),
				},
				Epr:               nil,
				ResponsiblePerson: nil,
				Manufacturer: &tiktokres_v202309.CategoryManufacturer{
					IsRequired: types.MakeBool(true),
				},
			},
			want: Rule{
				ProductCertifications: []*ProductCertification{
					{
						ExternalId:            "cert-002",
						ExternalName:          "RoHS认证",
						IsRequired:            false,
						SampleImageUrl:        "",
						RequirementConditions: []AttributeRequirementCondition{},
					},
				},
				SizeChart: &SizeChart{
					IsRequired:  false,
					IsSupported: true,
				},
				Cod: &Cod{
					IsSupported: false,
				},
				PackageDimension: &PackageDimension{
					IsRequired: true,
				},
				Epr: &Epr{
					IsRequired: false,
				},
				Compliance: &CategoryCompliance{
					ResponsiblePerson: &CategoryResponsiblePerson{
						IsRequired: false,
					},
					Manufacturer: &CategoryManufacturer{
						IsRequired: true,
					},
				},
				Brand: &BrandRule{
					IsSupported: true,
					IsRequired:  false,
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := convertTikTokCategoryRules(tt.rule)
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("convertTikTokCategoryRules() = %v, want %v", got, tt.want)
			}
		})
	}
}
