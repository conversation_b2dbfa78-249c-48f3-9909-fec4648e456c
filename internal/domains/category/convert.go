package category

import (
	"fmt"

	shein_rest "github.com/AfterShip/connectors-ecommerce-sdk-go/shein/rest"
	tiktokres_v202309 "github.com/AfterShip/connectors-ecommerce-sdk-go/tiktok/rest/version202309"
	"github.com/AfterShip/pltf-pd-product-listings/internal/consts"
)

func convertTikTokCategoryRules(rule *tiktokres_v202309.CategoryRule) Rule {
	return Rule{
		ProductCertifications: convertTikTokProductCertifications(rule.ProductCertifications),
		SizeChart:             convertTikTokSizeChart(rule.SizeChart),
		Cod:                   convertTikTokCod(rule.Cod),
		PackageDimension:      convertTikTokPackageDimension(rule.PackageDimension),
		Epr:                   convertTikTokEpr(rule.Epr),
		Compliance:            convertTikTokCategoryCompliance(rule),
		Brand: &BrandRule{
			IsSupported: true,
			IsRequired:  false,
		},
	}
}

func convertSheinCategoryRules(rule shein_rest.GetProductPublishFillStandardResp, sizeAttributes []Attribute) Rule {
	isBrandRequired := false
	for _, cur := range rule.FillInStandardList {
		if cur.Module.String() == shein_rest.FillStandardModuleBasic && cur.FieldKey.String() == shein_rest.FillStandardFieldKeyBrandCode {
			isBrandRequired = cur.Required.Bool()
		}
	}

	isSizeChartSupported := false
	if len(sizeAttributes) > 0 {
		isSizeChartSupported = true
	}

	isSizeChartRequired := false
	for _, cur := range sizeAttributes {
		if cur.IsRequired {
			isSizeChartRequired = true
			break
		}
	}

	return Rule{
		SizeChart: &SizeChart{
			IsRequired:  isSizeChartRequired,
			IsSupported: isSizeChartSupported,
		},
		PackageDimension: &PackageDimension{
			IsRequired: true,
		},
		Brand: &BrandRule{
			IsSupported: true,
			IsRequired:  isBrandRequired,
		},
	}
}

func convertTikTokEpr(ttsEpr *tiktokres_v202309.CategoryEpr) *Epr {
	var isRequired bool
	if ttsEpr != nil {
		isRequired = ttsEpr.IsRequired.Bool()
	}
	return &Epr{
		IsRequired: isRequired,
	}
}

func convertTikTokCategoryCompliance(rule *tiktokres_v202309.CategoryRule) *CategoryCompliance {
	return &CategoryCompliance{
		ResponsiblePerson: convertTikTokCategoryResponsiblePerson(rule.ResponsiblePerson),
		Manufacturer:      convertTikTokCategoryManufacturer(rule.Manufacturer),
	}
}

func convertTikTokCategoryResponsiblePerson(ttsCategoryResponsiblePerson *tiktokres_v202309.CategoryResponsiblePerson) *CategoryResponsiblePerson {
	var isRequired bool
	if ttsCategoryResponsiblePerson != nil {
		isRequired = ttsCategoryResponsiblePerson.IsRequired.Bool()
	}
	return &CategoryResponsiblePerson{
		IsRequired: isRequired,
	}
}

func convertTikTokCategoryManufacturer(ttsCategoryManufacturer *tiktokres_v202309.CategoryManufacturer) *CategoryManufacturer {
	var isRequired bool
	if ttsCategoryManufacturer != nil {
		isRequired = ttsCategoryManufacturer.IsRequired.Bool()
	}
	return &CategoryManufacturer{
		IsRequired: isRequired,
	}
}

func convertTikTokSizeChart(sizeChart *tiktokres_v202309.CategorySizeChart) *SizeChart {
	isRequired := false
	isSupported := false

	if sizeChart != nil {
		isRequired = sizeChart.IsRequired.Bool()
		isSupported = sizeChart.IsSupported.Bool()
	}

	return &SizeChart{
		IsRequired:  isRequired,
		IsSupported: isSupported,
	}
}

func convertTikTokProductCertifications(certifications []tiktokres_v202309.CategoryProductCertification) []*ProductCertification {
	result := make([]*ProductCertification, 0, len(certifications))
	for i := range certifications {
		result = append(result, &ProductCertification{
			ExternalId:            certifications[i].ID.String(),
			ExternalName:          certifications[i].Name.String(),
			IsRequired:            certifications[i].IsRequired.Bool(),
			SampleImageUrl:        certifications[i].SampleImageUrl.String(),
			RequirementConditions: convertTikTokAttributeRequirementConditions(certifications[i].RequirementConditions),
		})
	}
	return result
}

func convertTikTokCod(cod *tiktokres_v202309.CategoryCod) *Cod {
	isSupported := false
	if cod != nil {
		isSupported = cod.IsSupported.Bool()
	}
	return &Cod{
		IsSupported: isSupported,
	}
}

func convertTikTokPackageDimension(packageDimension *tiktokres_v202309.CategoryPackageDimension) *PackageDimension {
	isRequired := false
	if packageDimension != nil {
		isRequired = packageDimension.IsRequired.Bool()
	}
	return &PackageDimension{
		IsRequired: isRequired,
	}
}

func convertTikTokCategoryAttributes(attributes []tiktokres_v202309.Attributes) []Attribute {
	result := make([]Attribute, 0, len(attributes))
	for i := range attributes {
		result = append(result, Attribute{
			ID:                    attributes[i].Id.String(),
			Name:                  attributes[i].Name.String(),
			Type:                  attributes[i].Type.String(),
			IsRequired:            attributes[i].IsRequired.Bool(),
			Values:                convertTikTokAttributeValues(attributes[i].Values),
			IsMultipleSelected:    attributes[i].IsMultipleSelected.Bool(),
			IsCustomizable:        attributes[i].IsCustomizable.Bool(),
			ValueDataType:         convertTikTokAttributeValueDataType(attributes[i].ValueDataFormat.String()),
			RequirementConditions: convertTikTokAttributeRequirementConditions(attributes[i].RequirementConditions),
		})
	}
	return result
}

func convertSheinAttributes(attributes []shein_rest.AttributeInfo, attributeConfigs []shein_rest.CustomAttributePermissionConfig) []Attribute {
	result := make([]Attribute, 0, len(attributes))

	attributeConfigMap := make(map[int64]bool)
	for _, cur := range attributeConfigs {
		hasPermission := false
		if cur.HasPermission.Int64() == shein_rest.HasPermissionYes {
			hasPermission = true
		}
		attributeConfigMap[cur.AttributeId.Int64()] = hasPermission
	}

	for _, cur := range attributes {
		// 该状态的 attribute 是禁用的属性，不需要填
		if cur.AttributeStatus.Int64() == shein_rest.AttributeStatusNotFilled {
			continue
		}

		isRequired := false
		if cur.AttributeStatus.Int64() == shein_rest.AttributeStatusRequired {
			isRequired = true
		}

		inputType := ""
		isMultipleSelected := false
		isCustomizable := false
		switch cur.AttributeMode.Int64() {
		case shein_rest.AttributeModeSingleSelectManualInput:
			inputType = consts.AttributeInputTypeSelectManualInput
		case shein_rest.AttributeModeMultiSelect:
			isMultipleSelected = true
		case shein_rest.AttributeModeSaleAttributeExclusive, shein_rest.AttributeModeSingleSelect:
		case shein_rest.AttributeModeManualInput:
			isCustomizable = true
		}

		if hasPermission, ok := attributeConfigMap[cur.AttributeId.Int64()]; ok && hasPermission {
			isCustomizable = true
		}

		attributeType := buildSheinAttributeType(cur.AttributeType.Int64(), cur.AttributeLabel.Int64())

		result = append(result, Attribute{
			ID:                 fmt.Sprintf("%d", cur.AttributeId.Int64()),
			Name:               cur.AttributeName.String(),
			Type:               attributeType,
			IsRequired:         isRequired,
			InputType:          inputType,
			Values:             convertSheinAttributeValues(cur.AttributeValueInfoList),
			IsMultipleSelected: isMultipleSelected,
			IsCustomizable:     isCustomizable,
		})
	}
	return result
}

func buildSheinAttributeType(attributeType int64, attributeLabel int64) string {
	switch attributeType {
	case shein_rest.AttributeTypeSaleAttribute:
		if attributeLabel == shein_rest.AttributeLabelMain {
			return consts.AttributeTypeSalesPropertyMainOption
		}
		return consts.AttributeTypeSalesProperty
	case shein_rest.AttributeTypeSizeAttribute:
		return consts.AttributeTypeSizeProperty
	default:
		return consts.AttributeTypeProductProperty
	}
}

func convertTikTokAttributeRequirementConditions(requirementConditions []tiktokres_v202309.AttributeRequirementCondition) []AttributeRequirementCondition {
	result := make([]AttributeRequirementCondition, 0, len(requirementConditions))
	for i := range requirementConditions {
		result = append(result, AttributeRequirementCondition{
			AttributeID:      requirementConditions[i].AttributeID.String(),
			AttributeValueID: requirementConditions[i].AttributeValueID.String(),
		})
	}
	return result
}

func convertTikTokAttributeValueDataType(valueDataType string) string {
	switch valueDataType {
	case consts.AttributeValueDataTypePositiveIntOrDecimal:
		return consts.AttributeValueDataTypeNumber
	default:
		return ""
	}
}

func convertTikTokAttributeValues(values []tiktokres_v202309.AttributeValue) []AttributeValue {
	result := make([]AttributeValue, 0, len(values))
	for i := range values {
		result = append(result, AttributeValue{
			ID:   values[i].Id.String(),
			Name: values[i].Name.String(),
		})
	}
	return result
}

func convertSheinAttributeValues(values []shein_rest.AttributeValueInfo) []AttributeValue {
	result := make([]AttributeValue, 0, len(values))
	for _, cur := range values {
		result = append(result, AttributeValue{
			ID:   fmt.Sprintf("%d", cur.AttributeValueId.Int64()),
			Name: cur.AttributeValue.String(),
		})
	}
	return result
}

func convertTikTokCategories(categories []tiktokres_v202309.Category) []Category {
	result := make([]Category, 0, len(categories))
	for i := range categories {
		status := consts.CategoryStatusInactive
		for _, permissionStatus := range categories[i].PermissionStatuses {
			if permissionStatus == consts.CategoryStatusAvailable {
				status = consts.CategoryStatusActive
				break
			}
		}
		result = append(result, Category{
			ID:        categories[i].Id.String(),
			LocalName: categories[i].LocalName.String(),
			ParentID:  categories[i].ParentID.String(),
			IsLeaf:    categories[i].IsLeaf.Bool(),
			Status:    status,
		})
	}
	return result
}

// convertSheinCategories
// shein category api 返回的是树结构，需要铺平
func convertSheinCategories(categories []shein_rest.Category) []Category {
	var result []Category
	for _, cur := range categories {
		result = append(result, Category{
			ID:            fmt.Sprintf("%d", cur.CategoryId.Int64()),
			ProductTypeID: fmt.Sprintf("%d", cur.ProductTypeId.Int64()),
			LocalName:     cur.CategoryName.String(),
			ParentID:      fmt.Sprintf("%d", cur.ParentCategoryId.Int64()),
			IsLeaf:        cur.LastCategory.Bool(),
			Status:        consts.CategoryStatusActive,
		})

		if len(cur.Children) > 0 {
			result = append(result, convertSheinCategories(cur.Children)...)
		}
	}
	return result
}
