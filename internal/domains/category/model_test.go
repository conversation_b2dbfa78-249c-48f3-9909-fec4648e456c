package category

import (
	"reflect"
	"testing"

	shein_rest "github.com/AfterShip/connectors-ecommerce-sdk-go/shein/rest"
	"github.com/AfterShip/connectors-library/sdks/shein_proxy"
	"github.com/AfterShip/pltf-pd-product-listings/internal/consts"
	tiktokapi "github.com/AfterShip/pltf-pd-product-listings/internal/third_party/tiktok_api"
)

func Test_filterProductAttributes(t *testing.T) {

	tests := []struct {
		name       string
		attributes []Attribute
		args       FilterAttributesArg
		want       []Attribute
	}{
		{
			name: "filter by isRequired",
			attributes: []Attribute{
				{
					ID:         "1",
					IsRequired: false,
					Type:       consts.AttributeTypeProductProperty,
				},
				{
					ID:         "2",
					IsRequired: true,
					Type:       consts.AttributeTypeSalesProperty,
				},
				{
					ID:         "3",
					IsRequired: true,
					Type:       consts.AttributeTypeProductProperty,
				},
			},
			args: FilterAttributesArg{
				IsRequired: true,
			},
			want: []Attribute{
				{
					ID:         "3",
					IsRequired: true,
					Type:       consts.AttributeTypeProductProperty,
				},
			},
		},
		{
			name: "filter by type",
			attributes: []Attribute{
				{ID: "1", IsRequired: false, Type: consts.AttributeTypeProductProperty},
				{ID: "2", IsRequired: false, Type: consts.AttributeTypeSalesProperty},
				{ID: "3", IsRequired: true, Type: consts.AttributeTypeSalesPropertyMainOption},
			},
			args: FilterAttributesArg{Types: []string{consts.AttributeTypeSalesProperty, consts.AttributeTypeSalesPropertyMainOption}},
			want: []Attribute{
				{ID: "2", IsRequired: false, Type: consts.AttributeTypeSalesProperty},
				{ID: "3", IsRequired: true, Type: consts.AttributeTypeSalesPropertyMainOption},
			},
		},
		{
			name: "filter by all type",
			attributes: []Attribute{
				{ID: "1", IsRequired: false, Type: consts.AttributeTypeProductProperty},
				{ID: "2", IsRequired: false, Type: consts.AttributeTypeSalesProperty},
				{ID: "3", IsRequired: true, Type: consts.AttributeTypeSalesPropertyMainOption},
			},
			args: FilterAttributesArg{AllType: true},
			want: []Attribute{
				{ID: "1", IsRequired: false, Type: consts.AttributeTypeProductProperty},
				{ID: "2", IsRequired: false, Type: consts.AttributeTypeSalesProperty},
				{ID: "3", IsRequired: true, Type: consts.AttributeTypeSalesPropertyMainOption},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := FilterProductCategoryAttributes(tt.attributes, FilterAttributesArg{
				IsRequired: tt.args.IsRequired,
				AllType:    tt.args.AllType,
				Types:      tt.args.Types,
			}); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("filterProductAttributes() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_filterProductAttributes_2(t *testing.T) {
	type args struct {
		attribute  Attribute
		isRequired bool
	}
	tests := []struct {
		name string
		args args
		len  int
	}{
		// TODO: Add test cases.
		{
			name: "should be filtered",
			args: args{
				attribute: Attribute{
					ID:         "1",
					IsRequired: false,
					Type:       consts.AttributeTypeProductProperty,
				},
				isRequired: true,
			},
			len: 0,
		},
		{
			name: "should not be filtered",
			args: args{
				attribute: Attribute{
					ID:         "1",
					IsRequired: true,
					Type:       consts.AttributeTypeProductProperty,
				},
				isRequired: true,
			},
			len: 1,
		},
		{
			name: "IsRequired is empty",
			args: args{
				attribute: Attribute{
					ID:         "1",
					IsRequired: true,
					Type:       consts.AttributeTypeProductProperty,
				},
				isRequired: false,
			},
			len: 1,
		},
		{
			name: "should not be filtered - conditions 1",
			args: args{
				attribute: Attribute{
					ID:         "1",
					IsRequired: false,
					RequirementConditions: []AttributeRequirementCondition{
						{
							AttributeID:      "2",
							AttributeValueID: "3",
						},
					},
					Type: consts.AttributeTypeProductProperty,
				},
				isRequired: true,
			},
			len: 1,
		},
		{
			name: "should not be filtered - condition 2",
			args: args{
				attribute: Attribute{
					ID:         "1",
					IsRequired: true,
					RequirementConditions: []AttributeRequirementCondition{
						{
							AttributeID:      "2",
							AttributeValueID: "3",
						},
					},
					Type: consts.AttributeTypeProductProperty,
				},
				isRequired: true,
			},
			len: 1,
		},
		{
			name: "IsRequired is empty - condition - 3",
			args: args{
				attribute: Attribute{
					ID:         "1",
					IsRequired: true,
					RequirementConditions: []AttributeRequirementCondition{
						{
							AttributeID:      "2",
							AttributeValueID: "3",
						},
					},
					Type: consts.AttributeTypeProductProperty,
				},
				isRequired: false,
			},
			len: 1,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := FilterProductCategoryAttributes([]Attribute{tt.args.attribute}, FilterAttributesArg{IsRequired: tt.args.isRequired}); len(got) != tt.len {
				t.Errorf("shouldBeFilteredAndConditions() = %v, len %v", got, tt.len)
			}
		})
	}
}

func Test_FilterTikTokShopRequiredRules(t *testing.T) {
	type args struct {
		rule Rule
	}
	tests := []struct {
		name string
		args args
		want Rule
	}{
		{
			name: "test: no filter",
			args: args{
				rule: Rule{
					ProductCertifications: []*ProductCertification{
						{
							ExternalId: "1",
							IsRequired: true,
						},
						{
							ExternalId: "2",
							IsRequired: true,
						},
					},
					SizeChart: &SizeChart{
						IsRequired:  true,
						IsSupported: true,
					},
					Cod: &Cod{
						IsSupported: true,
					},
					PackageDimension: &PackageDimension{
						IsRequired: true,
					},
					Epr: &Epr{
						IsRequired: true,
					},
					Compliance: &CategoryCompliance{
						ResponsiblePerson: &CategoryResponsiblePerson{
							IsRequired: true,
						},
						Manufacturer: &CategoryManufacturer{
							IsRequired: true,
						},
					},
				},
			},
			want: Rule{
				ProductCertifications: []*ProductCertification{
					{
						ExternalId: "1",
						IsRequired: true,
					},
					{
						ExternalId: "2",
						IsRequired: true,
					},
				},
				SizeChart: &SizeChart{
					IsRequired:  true,
					IsSupported: true,
				},
				Cod: &Cod{
					IsSupported: true,
				},
				PackageDimension: &PackageDimension{
					IsRequired: true,
				},
				Epr: &Epr{
					IsRequired: true,
				},
				Compliance: &CategoryCompliance{
					ResponsiblePerson: &CategoryResponsiblePerson{
						IsRequired: true,
					},
					Manufacturer: &CategoryManufacturer{
						IsRequired: true,
					},
				},
			},
		},
		{
			name: "test: filter - 1",
			args: args{
				rule: Rule{
					ProductCertifications: []*ProductCertification{
						{
							ExternalId: "1",
							IsRequired: false,
						},
						{
							ExternalId: "2",
							IsRequired: false,
						},
					},
					SizeChart: &SizeChart{},
					Cod:       &Cod{},
					PackageDimension: &PackageDimension{
						IsRequired: false,
					},
					Epr: &Epr{
						IsRequired: false,
					},
					Compliance: &CategoryCompliance{
						ResponsiblePerson: &CategoryResponsiblePerson{
							IsRequired: false,
						},
						Manufacturer: &CategoryManufacturer{
							IsRequired: false,
						},
					},
				},
			},
			want: Rule{
				ProductCertifications: []*ProductCertification{},
				SizeChart: &SizeChart{
					IsRequired:  false,
					IsSupported: false,
				},
				Cod: &Cod{
					IsSupported: false,
				},
				PackageDimension: &PackageDimension{
					IsRequired: false,
				},
				Epr: &Epr{
					IsRequired: false,
				},
				Compliance: &CategoryCompliance{
					ResponsiblePerson: &CategoryResponsiblePerson{
						IsRequired: false,
					},
					Manufacturer: &CategoryManufacturer{
						IsRequired: false,
					},
				},
			},
		},
		{
			name: "test: filter - 2",
			args: args{
				rule: Rule{
					ProductCertifications: []*ProductCertification{
						{
							ExternalId: "1",
							IsRequired: false,
							RequirementConditions: []AttributeRequirementCondition{
								{
									AttributeID:      "1",
									AttributeValueID: "1",
								},
							},
						},
						{
							ExternalId: "2",
							IsRequired: false,
						},
					},
					SizeChart: &SizeChart{},
					Cod:       &Cod{},
					PackageDimension: &PackageDimension{
						IsRequired: false,
					},
					Epr: &Epr{
						IsRequired: false,
					},
					Compliance: &CategoryCompliance{
						ResponsiblePerson: &CategoryResponsiblePerson{
							IsRequired: false,
						},
						Manufacturer: &CategoryManufacturer{
							IsRequired: false,
						},
					},
				},
			},
			want: Rule{
				ProductCertifications: []*ProductCertification{
					{
						ExternalId: "1",
						IsRequired: false,
						RequirementConditions: []AttributeRequirementCondition{
							{
								AttributeID:      "1",
								AttributeValueID: "1",
							},
						},
					},
				},
				SizeChart: &SizeChart{
					IsRequired:  false,
					IsSupported: false,
				},
				Cod: &Cod{
					IsSupported: false,
				},
				PackageDimension: &PackageDimension{
					IsRequired: false,
				},
				Epr: &Epr{
					IsRequired: false,
				},
				Compliance: &CategoryCompliance{
					ResponsiblePerson: &CategoryResponsiblePerson{
						IsRequired: false,
					},
					Manufacturer: &CategoryManufacturer{
						IsRequired: false,
					},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := FilterTikTokShopRequiredRules(tt.args.rule); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("filterTikTokShopRequiredRules() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestAttributesOutput_BuildDepthMap(t *testing.T) {
	tests := []struct {
		name       string
		attributes []Attribute
		want       map[string]int
	}{
		{
			name: "simple hierarchy",
			attributes: []Attribute{
				{
					ID:                    "1",
					RequirementConditions: []AttributeRequirementCondition{},
				},
				{
					ID: "2",
					RequirementConditions: []AttributeRequirementCondition{
						{AttributeID: "1"},
					},
				},
				{
					ID: "3",
					RequirementConditions: []AttributeRequirementCondition{
						{AttributeID: "2"},
					},
				},
			},
			want: map[string]int{
				"1": 0,
				"2": 1,
				"3": 2,
			},
		},
		{
			name: "multiple roots",
			attributes: []Attribute{
				{
					ID:                    "1",
					RequirementConditions: []AttributeRequirementCondition{},
				},
				{
					ID:                    "2",
					RequirementConditions: []AttributeRequirementCondition{},
				},
				{
					ID: "3",
					RequirementConditions: []AttributeRequirementCondition{
						{AttributeID: "1"},
					},
				},
				{
					ID: "4",
					RequirementConditions: []AttributeRequirementCondition{
						{AttributeID: "2"},
					},
				},
			},
			want: map[string]int{
				"1": 0,
				"2": 0,
				"3": 1,
				"4": 1,
			},
		},
		{
			name: "no conditions",
			attributes: []Attribute{
				{
					ID:                    "1",
					RequirementConditions: []AttributeRequirementCondition{},
				},
				{
					ID:                    "2",
					RequirementConditions: []AttributeRequirementCondition{},
				},
			},
			want: map[string]int{
				"1": 0,
				"2": 0,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			a := &AttributesOutput{
				Attributes: tt.attributes,
			}
			if got := a.BuildDepthMap(); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("BuildDepthMap() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestGetRulesArg_convertGetProductPublishFillStandard(t *testing.T) {
	tests := []struct {
		name           string
		arg            *GetRulesArg
		want           *shein_proxy.GetProductPublishFillStandardParams
		wantErr        bool
		expectedErrMsg string
	}{
		{
			name: "有效的分类ID",
			arg: &GetRulesArg{
				OrganizationID:       "org123",
				SalesChannelStoreKey: "store456",
				ExternalCategoryID:   "789",
			},
			want: &shein_proxy.GetProductPublishFillStandardParams{
				CommonParams: shein_proxy.CommonParams{
					OrganizationID: "org123",
					AppName:        consts.AppFeed,
					AppKey:         "store456",
				},
				GetProductPublishFillStandardParams: shein_rest.GetProductPublishFillStandardParams{
					CategoryId: int64(789),
				},
			},
			wantErr: false,
		},
		{
			name: "无效的分类ID",
			arg: &GetRulesArg{
				OrganizationID:       "org123",
				SalesChannelStoreKey: "store456",
				ExternalCategoryID:   "invalid",
			},
			want:           nil,
			wantErr:        true,
			expectedErrMsg: "strconv.ParseInt: parsing \"invalid\": invalid syntax",
		},
		{
			name: "空的分类ID",
			arg: &GetRulesArg{
				OrganizationID:       "org123",
				SalesChannelStoreKey: "store456",
				ExternalCategoryID:   "",
			},
			want:           nil,
			wantErr:        true,
			expectedErrMsg: "strconv.ParseInt: parsing \"\": invalid syntax",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := tt.arg.convertGetProductPublishFillStandard()
			if (err != nil) != tt.wantErr {
				t.Errorf("convertGetProductPublishFillStandard() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.wantErr {
				if err.Error() != tt.expectedErrMsg {
					t.Errorf("expected error message = %v, got = %v", tt.expectedErrMsg, err.Error())
				}
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("convertGetProductPublishFillStandard() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestGetAttributesArg_convertToSheinGetAttributesParams(t *testing.T) {
	tests := []struct {
		name        string
		arg         *GetAttributesArg
		productType string
		language    string
		want        *shein_proxy.GetAttributesParams
		wantErr     bool
		errMsg      string
	}{
		{
			name: "有效的商品类型ID",
			arg: &GetAttributesArg{
				OrganizationID:       "org123",
				SalesChannelStoreKey: "store456",
			},
			productType: "789",
			language:    "zh_CN",
			want: &shein_proxy.GetAttributesParams{
				CommonParams: shein_proxy.CommonParams{
					OrganizationID: "org123",
					AppName:        consts.AppFeed,
					AppKey:         "store456",
				},
				GetAttributesParams: shein_rest.GetAttributesParams{
					ProductTypeIdList: []int64{789},
				},
				Language: "zh_CN",
			},
			wantErr: false,
		},
		{
			name: "无效的商品类型ID",
			arg: &GetAttributesArg{
				OrganizationID:       "org123",
				SalesChannelStoreKey: "store456",
			},
			productType: "invalid",
			language:    "zh_CN",
			want:        nil,
			wantErr:     true,
			errMsg:      "strconv.ParseInt: parsing \"invalid\": invalid syntax",
		},
		{
			name: "空的商品类型ID",
			arg: &GetAttributesArg{
				OrganizationID:       "org123",
				SalesChannelStoreKey: "store456",
			},
			productType: "",
			language:    "zh_CN",
			want:        nil,
			wantErr:     true,
			errMsg:      "strconv.ParseInt: parsing \"\": invalid syntax",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := tt.arg.convertToSheinGetAttributesParams(tt.productType, tt.language)
			if (err != nil) != tt.wantErr {
				t.Errorf("convertToSheinGetAttributesParams() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.wantErr {
				if err.Error() != tt.errMsg {
					t.Errorf("expected error message = %v, got = %v", tt.errMsg, err.Error())
				}
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("convertToSheinGetAttributesParams() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestGetAttributesArg_convertToSheinGetCustomAttributePermissionConfigsParams(t *testing.T) {
	tests := []struct {
		name           string
		arg            *GetAttributesArg
		want           *shein_proxy.GetCustomAttributePermissionConfigsParams
		wantErr        bool
		expectedErrMsg string
	}{
		{
			name: "有效的分类ID",
			arg: &GetAttributesArg{
				OrganizationID:       "org123",
				SalesChannelStoreKey: "store456",
				ExternalCategoryID:   "789",
			},
			want: &shein_proxy.GetCustomAttributePermissionConfigsParams{
				CommonParams: shein_proxy.CommonParams{
					OrganizationID: "org123",
					AppName:        consts.AppFeed,
					AppKey:         "store456",
				},
				GetCustomAttributePermissionConfigsParams: shein_rest.GetCustomAttributePermissionConfigsParams{
					CategoryIdList: []int64{789},
				},
			},
			wantErr: false,
		},
		{
			name: "无效的分类ID",
			arg: &GetAttributesArg{
				OrganizationID:       "org123",
				SalesChannelStoreKey: "store456",
				ExternalCategoryID:   "invalid",
			},
			want:           nil,
			wantErr:        true,
			expectedErrMsg: "strconv.ParseInt: parsing \"invalid\": invalid syntax",
		},
		{
			name: "空的分类ID",
			arg: &GetAttributesArg{
				OrganizationID:       "org123",
				SalesChannelStoreKey: "store456",
				ExternalCategoryID:   "",
			},
			want:           nil,
			wantErr:        true,
			expectedErrMsg: "strconv.ParseInt: parsing \"\": invalid syntax",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := tt.arg.convertToSheinGetCustomAttributePermissionConfigsParams()
			if (err != nil) != tt.wantErr {
				t.Errorf("convertToSheinGetCustomAttributePermissionConfigsParams() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.wantErr {
				if err.Error() != tt.expectedErrMsg {
					t.Errorf("expected error message = %v, got = %v", tt.expectedErrMsg, err.Error())
				}
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("convertToSheinGetCustomAttributePermissionConfigsParams() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestGetRulesArg_convertToTikTokGetCategoryRulesParams(t *testing.T) {
	tests := []struct {
		name string
		arg  *GetRulesArg
		want *tiktokapi.GetCategoryRulesParams
	}{
		{
			name: "基本参数转换",
			arg: &GetRulesArg{
				OrganizationID:       "org123",
				SalesChannelStoreKey: "store456",
				ExternalCategoryID:   "789",
				CategoryVersion:      "v2",
			},
			want: &tiktokapi.GetCategoryRulesParams{
				CommonParams: tiktokapi.CommonParams{
					OrganizationID: "org123",
					AppName:        consts.AppFeed,
					AppKey:         "store456",
				},
				CategoryID:      "789",
				CategoryVersion: "v2",
			},
		},
		{
			name: "使用v1版本",
			arg: &GetRulesArg{
				OrganizationID:       "org456",
				SalesChannelStoreKey: "store789",
				ExternalCategoryID:   "12345",
				CategoryVersion:      "v1",
			},
			want: &tiktokapi.GetCategoryRulesParams{
				CommonParams: tiktokapi.CommonParams{
					OrganizationID: "org456",
					AppName:        consts.AppFeed,
					AppKey:         "store789",
				},
				CategoryID:      "12345",
				CategoryVersion: "v1",
			},
		},
		{
			name: "含有不相关参数",
			arg: &GetRulesArg{
				OrganizationID:       "org789",
				SalesChannelStoreKey: "store012",
				SalesChannelPlatform: "tiktok_shop",
				ExternalCategoryID:   "54321",
				IsRequired:           true,
				CategoryVersion:      "v2",
				SalesChannelRegion:   "US",
				RefreshCache:         true,
			},
			want: &tiktokapi.GetCategoryRulesParams{
				CommonParams: tiktokapi.CommonParams{
					OrganizationID: "org789",
					AppName:        consts.AppFeed,
					AppKey:         "store012",
				},
				CategoryID:      "54321",
				CategoryVersion: "v2",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := tt.arg.convertToTikTokGetCategoryRulesParams()

			// 验证 CommonParams
			if got.OrganizationID != tt.want.OrganizationID {
				t.Errorf("OrganizationID = %v, want %v", got.OrganizationID, tt.want.OrganizationID)
			}
			if got.AppName != tt.want.AppName {
				t.Errorf("AppName = %v, want %v", got.AppName, tt.want.AppName)
			}
			if got.AppKey != tt.want.AppKey {
				t.Errorf("AppKey = %v, want %v", got.AppKey, tt.want.AppKey)
			}

			// 验证其他字段
			if got.CategoryID != tt.want.CategoryID {
				t.Errorf("CategoryID = %v, want %v", got.CategoryID, tt.want.CategoryID)
			}
			if got.CategoryVersion != tt.want.CategoryVersion {
				t.Errorf("CategoryVersion = %v, want %v", got.CategoryVersion, tt.want.CategoryVersion)
			}
		})
	}
}

func TestListArg_convertToTikTokGetCategoriesParams(t *testing.T) {
	tests := []struct {
		name string
		arg  *ListArg
		want *tiktokapi.GetCategoriesParams
	}{
		{
			name: "基本参数转换",
			arg: &ListArg{
				OrganizationID:       "org123",
				SalesChannelStoreKey: "store456",
				SalesChannelPlatform: "tiktok_shop",
				RefreshCache:         false,
			},
			want: &tiktokapi.GetCategoriesParams{
				CommonParams: tiktokapi.CommonParams{
					OrganizationID: "org123",
					AppName:        consts.AppFeed,
					AppKey:         "store456",
				},
				RefreshCache: false,
			},
		},
		{
			name: "设置刷新缓存为 true",
			arg: &ListArg{
				OrganizationID:       "org789",
				SalesChannelStoreKey: "store012",
				SalesChannelPlatform: "tiktok_shop",
				RefreshCache:         true,
			},
			want: &tiktokapi.GetCategoriesParams{
				CommonParams: tiktokapi.CommonParams{
					OrganizationID: "org789",
					AppName:        consts.AppFeed,
					AppKey:         "store012",
				},
				RefreshCache: true,
			},
		},
		{
			name: "不同的组织和商店信息",
			arg: &ListArg{
				OrganizationID:       "different_org",
				SalesChannelStoreKey: "different_store",
				SalesChannelPlatform: "tiktok_shop",
				RefreshCache:         false,
			},
			want: &tiktokapi.GetCategoriesParams{
				CommonParams: tiktokapi.CommonParams{
					OrganizationID: "different_org",
					AppName:        consts.AppFeed,
					AppKey:         "different_store",
				},
				RefreshCache: false,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := tt.arg.convertToTikTokGetCategoriesParams()

			// 验证 CommonParams
			if got.OrganizationID != tt.want.OrganizationID {
				t.Errorf("OrganizationID = %v, want %v", got.OrganizationID, tt.want.OrganizationID)
			}
			if got.AppName != tt.want.AppName {
				t.Errorf("AppName = %v, want %v", got.AppName, tt.want.AppName)
			}
			if got.AppKey != tt.want.AppKey {
				t.Errorf("AppKey = %v, want %v", got.AppKey, tt.want.AppKey)
			}

			// 验证 RefreshCache 字段
			if got.RefreshCache != tt.want.RefreshCache {
				t.Errorf("RefreshCache = %v, want %v", got.RefreshCache, tt.want.RefreshCache)
			}
		})
	}
}

func TestListArg_toSheinGetCategoriesParams(t *testing.T) {
	tests := []struct {
		name     string
		arg      *ListArg
		language string
		want     *shein_proxy.GetCategoriesParams
	}{
		{
			name: "基本参数转换-英语",
			arg: &ListArg{
				OrganizationID:       "org123",
				SalesChannelStoreKey: "store456",
				SalesChannelPlatform: "shein",
				RefreshCache:         false,
			},
			language: "en",
			want: &shein_proxy.GetCategoriesParams{
				CommonParams: shein_proxy.CommonParams{
					OrganizationID: "org123",
					AppName:        consts.AppFeed,
					AppKey:         "store456",
				},
				Language: "en",
			},
		},
		{
			name: "基本参数转换-中文",
			arg: &ListArg{
				OrganizationID:       "org789",
				SalesChannelStoreKey: "store012",
				SalesChannelPlatform: "shein",
				RefreshCache:         true, // 这个字段在转换中不会被使用
			},
			language: "zh",
			want: &shein_proxy.GetCategoriesParams{
				CommonParams: shein_proxy.CommonParams{
					OrganizationID: "org789",
					AppName:        consts.AppFeed,
					AppKey:         "store012",
				},
				Language: "zh",
			},
		},
		{
			name: "不同的组织和商店信息-日语",
			arg: &ListArg{
				OrganizationID:       "different_org",
				SalesChannelStoreKey: "different_store",
				SalesChannelPlatform: "shein",
				RefreshCache:         false,
			},
			language: "ja",
			want: &shein_proxy.GetCategoriesParams{
				CommonParams: shein_proxy.CommonParams{
					OrganizationID: "different_org",
					AppName:        consts.AppFeed,
					AppKey:         "different_store",
				},
				Language: "ja",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := tt.arg.toSheinGetCategoriesParams(tt.language)

			// 验证 CommonParams
			if got.OrganizationID != tt.want.OrganizationID {
				t.Errorf("OrganizationID = %v, want %v", got.OrganizationID, tt.want.OrganizationID)
			}
			if got.AppName != tt.want.AppName {
				t.Errorf("AppName = %v, want %v", got.AppName, tt.want.AppName)
			}
			if got.AppKey != tt.want.AppKey {
				t.Errorf("AppKey = %v, want %v", got.AppKey, tt.want.AppKey)
			}

			// 验证 Language 字段
			if got.Language != tt.want.Language {
				t.Errorf("Language = %v, want %v", got.Language, tt.want.Language)
			}
		})
	}
}

func TestGetAttributesArg_convertToTikTokGetCategoryAttributesParams(t *testing.T) {
	tests := []struct {
		name string
		arg  *GetAttributesArg
		want *tiktokapi.GetCategoryAttributesParams
	}{
		{
			name: "基本参数转换-v2版本",
			arg: &GetAttributesArg{
				OrganizationID:       "org123",
				SalesChannelStoreKey: "store456",
				SalesChannelPlatform: "tiktok_shop",
				ExternalCategoryID:   "789",
				CategoryVersion:      "v2",
				IsRequired:           true,
				RefreshCache:         false,
			},
			want: &tiktokapi.GetCategoryAttributesParams{
				CommonParams: tiktokapi.CommonParams{
					OrganizationID: "org123",
					AppName:        consts.AppFeed,
					AppKey:         "store456",
				},
				CategoryID:      "789",
				CategoryVersion: "v2",
			},
		},
		{
			name: "基本参数转换-v1版本",
			arg: &GetAttributesArg{
				OrganizationID:       "org456",
				SalesChannelStoreKey: "store789",
				SalesChannelPlatform: "tiktok_shop",
				ExternalCategoryID:   "12345",
				CategoryVersion:      "v1",
				Types:                []string{consts.AttributeTypeProductProperty},
				RefreshCache:         false,
			},
			want: &tiktokapi.GetCategoryAttributesParams{
				CommonParams: tiktokapi.CommonParams{
					OrganizationID: "org456",
					AppName:        consts.AppFeed,
					AppKey:         "store789",
				},
				CategoryID:      "12345",
				CategoryVersion: "v1",
			},
		},
		{
			name: "包含不相关参数",
			arg: &GetAttributesArg{
				OrganizationID:       "org789",
				SalesChannelStoreKey: "store012",
				SalesChannelPlatform: "tiktok_shop",
				ExternalCategoryID:   "54321",
				IsRequired:           true,
				AllType:              true,
				Types:                []string{consts.AttributeTypeSalesPropertyMainOption},
				CategoryVersion:      "v2",
				SalesChannelRegion:   "US",
				RefreshCache:         true,
			},
			want: &tiktokapi.GetCategoryAttributesParams{
				CommonParams: tiktokapi.CommonParams{
					OrganizationID: "org789",
					AppName:        consts.AppFeed,
					AppKey:         "store012",
				},
				CategoryID:      "54321",
				CategoryVersion: "v2",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := tt.arg.convertToTikTokGetCategoryAttributesParams()

			// 验证 CommonParams
			if got.OrganizationID != tt.want.OrganizationID {
				t.Errorf("OrganizationID = %v, want %v", got.OrganizationID, tt.want.OrganizationID)
			}
			if got.AppName != tt.want.AppName {
				t.Errorf("AppName = %v, want %v", got.AppName, tt.want.AppName)
			}
			if got.AppKey != tt.want.AppKey {
				t.Errorf("AppKey = %v, want %v", got.AppKey, tt.want.AppKey)
			}

			// 验证其他字段
			if got.CategoryID != tt.want.CategoryID {
				t.Errorf("CategoryID = %v, want %v", got.CategoryID, tt.want.CategoryID)
			}
			if got.CategoryVersion != tt.want.CategoryVersion {
				t.Errorf("CategoryVersion = %v, want %v", got.CategoryVersion, tt.want.CategoryVersion)
			}
		})
	}
}
