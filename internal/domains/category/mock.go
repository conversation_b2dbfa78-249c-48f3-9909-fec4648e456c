package category

import (
	"context"

	"github.com/stretchr/testify/mock"
)

type MockCategoryService struct {
	mock.Mock
}

func (m *MockCategoryService) PostCategoryAttributes(ctx context.Context, arg *AttributesOutput) (AttributesOutput, error) {
	rest := m.Called(ctx, arg)
	return rest.Get(0).(AttributesOutput), rest.Error(1)
}

func (m *MockCategoryService) PostCategoryRules(ctx context.Context, arg *RulesOutput) (RulesOutput, error) {
	rest := m.Called(ctx, arg)
	return rest.Get(0).(RulesOutput), rest.Error(1)
}

func (m *MockCategoryService) List(ctx context.Context, arg *ListArg) (CategoriesOutput, error) {
	rest := m.Called(ctx, arg)
	return rest.Get(0).(CategoriesOutput), rest.Error(1)
}

func (m *MockCategoryService) Versions(ctx context.Context, arg *CategoryVersionsArg) (CategoryVersionsOutput, error) {
	rest := m.Called(ctx, arg)
	return rest.Get(0).(CategoryVersionsOutput), rest.Error(1)
}

func (m *MockCategoryService) GetCategoryAttributes(ctx context.Context, arg *GetAttributesArg) (AttributesOutput, error) {
	rest := m.Called(ctx, arg)
	return rest.Get(0).(AttributesOutput), rest.Error(1)
}

func (m *MockCategoryService) GetCategoryRules(ctx context.Context, arg *GetRulesArg) (RulesOutput, error) {
	rest := m.Called(ctx, arg)
	return rest.Get(0).(RulesOutput), rest.Error(1)
}
