package category

import (
	"reflect"
	"testing"

	tiktokres_v202309 "github.com/AfterShip/connectors-ecommerce-sdk-go/tiktok/rest/version202309"
	"github.com/AfterShip/gopkg/facility/types"
)

func Test_getV2LeafCategoryIDs(t *testing.T) {
	tests := []struct {
		name       string
		categories []tiktokres_v202309.Category
		want       []string
	}{
		{
			name:       "空分类列表",
			categories: []tiktokres_v202309.Category{},
			want:       []string{},
		},
		{
			name: "只有叶子节点的分类列表",
			categories: []tiktokres_v202309.Category{
				{
					Id:     types.MakeString("101"),
					IsLeaf: types.MakeBool(true),
				},
				{
					Id:     types.MakeString("102"),
					IsLeaf: types.MakeBool(true),
				},
			},
			want: []string{"101", "102"},
		},
		{
			name: "只有非叶子节点的分类列表",
			categories: []tiktokres_v202309.Category{
				{
					Id:     types.MakeString("201"),
					IsLeaf: types.MakeBool(false),
				},
				{
					Id:     types.MakeString("202"),
					IsLeaf: types.MakeBool(false),
				},
			},
			want: []string{},
		},
		{
			name: "混合叶子节点和非叶子节点的分类列表",
			categories: []tiktokres_v202309.Category{
				{
					Id:     types.MakeString("301"),
					IsLeaf: types.MakeBool(true),
				},
				{
					Id:     types.MakeString("302"),
					IsLeaf: types.MakeBool(false),
				},
				{
					Id:     types.MakeString("303"),
					IsLeaf: types.MakeBool(true),
				},
			},
			want: []string{"301", "303"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := getV2LeafCategoryIDs(tt.categories)
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("getV2LeafCategoryIDs() = %v, want %v", got, tt.want)
			}
		})
	}
}
