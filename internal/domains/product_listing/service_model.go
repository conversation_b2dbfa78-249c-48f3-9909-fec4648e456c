package product_listing

import (
	"encoding/json"
	"fmt"
	"regexp"
	"slices"
	"sort"
	"strings"
	"time"

	"github.com/jinzhu/copier"
	elastic "github.com/olivere/elastic/v7"

	"github.com/AfterShip/connectors-library/sdks/products_center"
	"github.com/AfterShip/connectors-library/utils/sets"
	"github.com/AfterShip/feed-sdk-go/business_events_collector"
	"github.com/AfterShip/gopkg/facility/set"
	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/gopkg/uuid"
	"github.com/AfterShip/pltf-pd-product-listings/internal/config"
	"github.com/AfterShip/pltf-pd-product-listings/internal/consts"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/category"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/searchable_products"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
	"github.com/AfterShip/pltf-pd-product-listings/internal/utils/elasticsearch"
)

const (
	optionsMapKeyTemplate = "%s:%s"
)

type ProductListingArgs struct {
	ID                      string                    `json:"id"`
	SalesChannel            models.SalesChannel       `json:"sales_channel"`
	Organization            models.Organization       `json:"organization"`
	SalesChannelProduct     SalesChannelProduct       `json:"sales_channel_product"`
	ProductsCenterProduct   ProductsCenterProduct     `json:"products_center_product"`
	Settings                SyncSettings              `json:"settings"`
	Product                 models.Product            `json:"product"`
	Relations               []*ProductListingRelation `json:"relations"`
	Ready                   Ready                     `json:"ready"`
	Audit                   Audit                     `json:"audit"`
	Publish                 Publish                   `json:"publish"`
	Version                 int64                     `json:"version"`
	FeedCustomizationParams FeedCustomizationParams   `json:"feed_customization_params"`
	NeedPublish             bool                      `json:"need_publish"`
}

func (args *ProductListingArgs) overWriteRelationByListing(listing *ProductListing) {
	for i := range args.Relations {
		for j := range listing.Relations {
			if args.Relations[i].ProductListingVariantID == listing.Relations[j].ProductListingVariantID {
				// 复用 relations id
				args.Relations[i].ID = listing.Relations[j].ID
				args.Relations[i].SalesChannelVariant = listing.Relations[j].SalesChannelVariant
			}
		}
	}
}

type FeedCustomizationParams struct {
	FeedCategoryTemplateID string `json:"feed_category_template_id"`
}

type ListAuditVersionsArgs struct {
	ProductListingID string `validate:"required"`
	Page             int64
	Limit            int64
}

type ListRelationsArgs struct {
	OrganizationID                    string `validate:"required"`
	SalesChannelStoreKey              string
	SalesChannelPlatform              string
	SourceStoreKey                    string
	SourcePlatform                    string
	SalesChannelProductID             string
	SalesChannelProductIDs            []string
	SalesChannelVariantID             string
	SalesChannelVariantIDs            []string
	ProductsCenterProductID           string
	ProductsCenterVariantID           string
	ProductsCenterConnectorProductIDs []string
	IncludeDeleted                    bool
	LinkStatus                        []string
	SyncStatus                        []string
	Page                              int64
	Limit                             int64
}

type SalesChannelOrderVariantRelationArg struct {
	OrganizationID        string `validate:"required"`
	SalesChannelStoreKey  string `validate:"required"`
	SalesChannelPlatform  string `validate:"required"`
	SourceStoreKey        string `validate:"required"`
	SourcePlatform        string `validate:"required"`
	SalesChannelProductID string `validate:"required"`
	SalesChannelVariantID string `validate:"required"`
}

type SearchProductListingArgs struct {
	OrganizationID           string
	SalesChannelStoreKey     string
	SalesChannelPlatform     string
	States                   []string
	PredefinedFilters        []string
	LinkStatusFilters        []string
	SyncStatusFilters        []string
	InventoryFilters         string
	Categories               []string
	ProductsCenterProductIds []string
	FeedCategoryTemplateID   string
	SalesChannelProductIds   []string
	ProductListingIDs        []string
	PublishState             string
	Query                    string
	Limit                    int64 `validate:"required"`
	Page                     int64
	Cursor                   string
	IDs                      []string `validate:"omitempty,max=100"`
	SKUs                     []string
}

func (args *SearchProductListingArgs) buildPaginationSearchByIDs() *models.Pagination {
	// search by IDS,一次性返回所有数据
	if len(args.IDs) == 0 {
		return nil
	}
	pagination := &models.Pagination{
		Limit:       int64(100),
		Total:       int64(len(args.IDs)),
		HasNextPage: false,
	}
	if args.Page != 0 {
		pagination.Page = args.Page
	}
	if args.Cursor != "" {
		pagination.PreviousCursor = args.Cursor
		pagination.NextCursor = consts.EndCursor
	}
	return pagination
}

func (args *SearchProductListingArgs) CustomValidate() error {
	if args.Page == 0 && args.Cursor == "" {
		return ErrEmptyPaginationParams
	}
	return nil
}

func (args *SearchProductListingArgs) buildSearchType() string {
	if args.Cursor != "" {
		return esPaginationTypeSearchAfter
	}
	return esPaginationTypePage
}

func (args *SearchProductListingArgs) buildQuery() *elastic.BoolQuery {
	query := elastic.NewBoolQuery()

	/**
	biz_logic
	1. bundles product 不参与 link_status 搜索, 因为目前没有维护 bundles 的 link_status
	*/
	bizLogicQueries := args.bizLogicFilter()
	if len(bizLogicQueries) > 0 {
		query.Filter(elastic.NewBoolQuery().Should(bizLogicQueries...))
	}

	// should not return deleted data
	query.Filter(elastic.NewTermQuery("deleted", false))

	elasticsearch.BuildTermQuery(query, "organization_id", args.OrganizationID)

	elasticsearch.BuildTermQuery(query, "sales_channel_store_key", args.SalesChannelStoreKey)

	elasticsearch.BuildTermQuery(query, "sales_channel_platform", args.SalesChannelPlatform)

	elasticsearch.BuildTermsQuery(query, "state", args.States)

	elasticsearch.BuildTermQuery(query, "feed_category_template_id", args.FeedCategoryTemplateID)

	elasticsearch.BuildTermsQuery(query, "product_sales_channel_category_ids", args.Categories)

	elasticsearch.BuildTermQuery(query, "publish_state", args.PublishState)

	if len(args.SKUs) > 0 {
		skuQuery := args.buildSKUsSearch()
		if len(skuQuery) > 0 {
			query.Filter(elastic.NewBoolQuery().Should(skuQuery...))
		}
	}

	if len(args.PredefinedFilters) > 0 {
		predefinedFiltersQueries := args.buildPredefinedFiltersQuery()
		if len(predefinedFiltersQueries) > 0 {
			query.Filter(elastic.NewBoolQuery().Should(predefinedFiltersQueries...))
		}
	}

	if len(args.LinkStatusFilters) > 0 {
		linkStatusQueries := args.buildLinkStatusFiltersQuery()
		if len(linkStatusQueries) > 0 {
			query.Filter(elastic.NewBoolQuery().Should(linkStatusQueries...))
		}
	}

	if len(args.SyncStatusFilters) > 0 {
		syncStatusQueries := args.buildSyncStatusFiltersQuery()
		if len(syncStatusQueries) > 0 {
			query.Filter(elastic.NewBoolQuery().Should(syncStatusQueries...))
		}
	}

	if args.InventoryFilters != "" {
		inventoryFiltersQueries := args.buildInventoryFiltersQuery()
		if len(inventoryFiltersQueries) > 0 {
			query.Filter(inventoryFiltersQueries...)
		}
	}

	elasticsearch.BuildTermsQuery(query, "matched_products_center_product_id", args.ProductsCenterProductIds)
	elasticsearch.BuildTermsQuery(query, "sales_channel_product_id", args.SalesChannelProductIds)

	if len(args.ProductListingIDs) > 0 {
		query.Filter(elastic.NewIdsQuery().Ids(args.ProductListingIDs...))
	}

	if len(args.IDs) > 0 {
		query.Filter(elastic.NewIdsQuery().Ids(args.IDs...))
	}

	if args.Query != "" {
		fuzzyQueries := args.buildFuzzyQuery()
		if len(fuzzyQueries) > 0 {
			query.Filter(elastic.NewBoolQuery().Should(fuzzyQueries...))
		}
	}
	return query
}

func (args *SearchProductListingArgs) bizLogicFilter() []elastic.Query {
	// bundles product 不参与 link_status 搜索, 因为目前没有维护 bundles 的 link_status
	if len(args.LinkStatusFilters) > 0 {
		return []elastic.Query{elastic.NewBoolQuery().MustNot(elastic.NewTermQuery("product_types", consts.ProductTypeBundles))}
	}
	return nil
}

// buildSKUsSearch SKU字段没有 keyword 类型, 无法使用 term query
func (args *SearchProductListingArgs) buildSKUsSearch() []elastic.Query {
	if len(args.SKUs) == 0 {
		return nil
	}
	queries := make([]elastic.Query, 0)
	for _, sku := range args.SKUs {
		queries = append(queries,
			elastic.NewNestedQuery("product_variants", elastic.NewMatchPhraseQuery("product_variants.sku", sku)))
	}
	return queries
}

func (args *SearchProductListingArgs) buildLinkStatusFiltersQuery() []elastic.Query {
	queries := make([]elastic.Query, 0)
	if len(args.LinkStatusFilters) == 0 {
		return queries
	}
	for _, item := range args.LinkStatusFilters {
		switch item {
		case consts.LinkStatusLinked.String():
			queries = append(queries, elastic.NewTermQuery("link_status", consts.LinkStatusLinked))
		case consts.LinkStatusPartialLinked.String():
			queries = append(queries, elastic.NewTermQuery("link_status", consts.LinkStatusPartialLinked))
		case consts.LinkStatusUnlink.String():
			queries = append(queries, elastic.NewTermQuery("link_status", consts.LinkStatusUnlink))
		}
	}

	return queries
}

func (args *SearchProductListingArgs) buildSyncStatusFiltersQuery() []elastic.Query {
	queries := make([]elastic.Query, 0)
	if len(args.SyncStatusFilters) == 0 {
		return queries
	}
	for _, item := range args.SyncStatusFilters {
		switch item {
		case consts.SyncStatusSynced.String():
			queries = append(queries, elastic.NewTermQuery("sync_status", consts.SyncStatusSynced))
		case consts.SyncStatusUnsync.String():
			queries = append(queries, elastic.NewTermQuery("sync_status", consts.SyncStatusUnsync))
		case consts.SyncStatusPartialSynced.String():
			queries = append(queries, elastic.NewTermQuery("sync_status", consts.SyncStatusPartialSynced))
		}
	}

	return queries
}

func (args *SearchProductListingArgs) buildPredefinedFiltersQuery() []elastic.Query {
	queries := make([]elastic.Query, 0)
	if len(args.PredefinedFilters) == 0 {
		return queries
	}

	for _, item := range args.PredefinedFilters {
		innerQuery := elastic.NewBoolQuery()
		andQuery := false
		switch item {
		case PredefinedFilterReadyToPublish:
			// ready_status == ready && publish_state == nil
			innerQuery.Filter(elastic.NewTermQuery("ready_status", consts.ReadyStatusReady))
			innerQuery.Filter(elastic.NewTermQuery("publish_state", ""))
			andQuery = true
		case PredefinedFilterInfoIncomplete:
			// ready_status == unready
			innerQuery.Filter(elastic.NewTermQuery("ready_status", consts.ReadyStatusUnready))
		case PredefinedFilterPublishedFailed:
			// publish_state == failed
			innerQuery.Filter(elastic.NewTermQuery("publish_state", consts.PublishStateFailed))
		case PredefinedFilterEditsUnderReview:
			// audit_status == reviewing
			innerQuery.Filter(elastic.NewTermQuery("audit_state", consts.AuditStateReviewing))
		case PredefinedFilterEditsNotApproved:
			// audit_status == failed
			innerQuery.Filter(elastic.NewTermQuery("audit_state", consts.AuditStateFailed))
		case PredefinedFilterReviewFailed:
			// sales_channel_product_state == failed
			innerQuery.Filter(elastic.NewTermQuery("sales_channel_product_state", consts.SalesChannelProductStateFailed))
		case PredefinedFilterFrozen:
			// sales_channel_product_state == freeze
			innerQuery.Filter(elastic.NewTermQuery("sales_channel_product_state", consts.SalesChannelProductStateFreeze))
		case PredefinedFilterCanAutoLink:
			innerQuery.Filter(elastic.NewTermsQuery("link_status", consts.LinkStatusUnlink, consts.LinkStatusPartialLinked))
			innerQuery.Filter(elastic.NewTermsQuery("sync_status", consts.SyncStatusPartialSynced, consts.SyncStatusSynced))
			innerQuery.Filter(elastic.NewTermsQuery("sales_channel_product_state", consts.SalesChannelProductStateLive))
			innerQuery.Filter(elastic.NewBoolQuery().MustNot(elastic.NewTermQuery("product_types", consts.ProductTypeBundles)))
			andQuery = true
		case PredefinedFilterCanSyncInventoryAndPrice:
			innerQuery.Filter(elastic.NewTermsQuery("link_status", consts.LinkStatusLinked, consts.LinkStatusPartialLinked))
			innerQuery.Filter(elastic.NewTermsQuery("sync_status", consts.SyncStatusSynced, consts.SyncStatusPartialSynced))
			if args.SalesChannelPlatform != consts.Shein {
				innerQuery.Filter(elastic.NewTermsQuery("sales_channel_product_state", consts.SalesChannelProductStateLive))
			}
			andQuery = true
		case PredefinedFilterCanSyncProductDetail:
			innerQuery.Filter(elastic.NewTermsQuery("link_status", consts.LinkStatusLinked, consts.LinkStatusPartialLinked))
			innerQuery.Filter(elastic.NewTermsQuery("sync_status", consts.SyncStatusSynced, consts.SyncStatusPartialSynced))
			innerQuery.Filter(elastic.NewTermsQuery("sales_channel_product_state",
				consts.SalesChannelProductStatePending,
				consts.SalesChannelProductStateFailed,
				consts.SalesChannelProductStateLive),
			)
			andQuery = true
		default:
		}
		if andQuery {
			queries = append(queries, elastic.NewBoolQuery().Must(innerQuery))
		} else {
			queries = append(queries, innerQuery)
		}
	}
	return queries
}

func (args *SearchProductListingArgs) buildInventoryFiltersQuery() []elastic.Query {
	queries := make([]elastic.Query, 0)
	if args.InventoryFilters == "" {
		return queries
	}

	innerQuery := elastic.NewBoolQuery()
	if args.InventoryFilters == InventoryFilterOutOfStock {
		// all of (!product.variants[*].allow_backorder && product.variants[*].available_quantity <= 0)
		innerQuery.Filter(elastic.NewTermQuery("variants_allow_backorder", false))
		innerQuery.Filter(elastic.NewRangeQuery("variants_inventory_quantity").Lte(0))
		queries = append(queries, elastic.NewBoolQuery().Must(innerQuery))
	} else if args.InventoryFilters == InventoryFilterInStock {
		// any of (product.variants[*].allow_backorder || product.variants[*].available_quantity > 0)
		innerQuery.Filter(elastic.NewTermQuery("variants_allow_backorder", true))
		innerQuery.Filter(elastic.NewRangeQuery("variants_inventory_quantity").Gte(0))
		queries = append(queries, elastic.NewBoolQuery().Should(innerQuery))
	}
	return queries
}

func (args *SearchProductListingArgs) buildFuzzyQuery() []elastic.Query {
	queries := make([]elastic.Query, 0)
	if args.Query == "" {
		return queries
	}

	/*
		定义 allowed fields 方便扩展:
		product_name/ sku / product_center_product_id / sales_channel_product_id / sku title

	*/
	supportedQueryFields := []string{
		FuzzyQueryProductName,
		FuzzyQuerySku,
		FuzzyQuerySalesChannelProductId,
		FuzzyQuerySkuTitle,
		FuzzyQueryProductNumber,
		TermQueryOptionsValueDetailsSalesChannelID,
		FuzzyQueryProductsCenterProductId,
	}
	for _, column := range supportedQueryFields {
		subQuery := elastic.NewBoolQuery()
		switch column {
		case FuzzyQueryProductName:
			// ngram 分词
			subQuery.Filter(elastic.NewMatchPhraseQuery("product_title", args.Query))
		case FuzzyQuerySku:
			subQuery.Filter(elastic.NewNestedQuery("product_variants", elastic.NewMatchPhraseQuery(
				"product_variants.sku", args.Query),
			))
		case FuzzyQuerySalesChannelProductId:
			subQuery.Filter(elastic.NewTermQuery("sales_channel_product_id", args.Query))
		case FuzzyQueryProductsCenterProductId:
			subQuery.Filter(elastic.NewTermQuery("matched_products_center_product_id", args.Query))
		case FuzzyQuerySkuTitle:
			subQuery.Filter(elastic.NewNestedQuery("product_variants", elastic.NewMatchPhraseQuery(
				"product_variants.title", args.Query),
			))
		case FuzzyQueryProductNumber:
			subQuery.Filter(elastic.NewMatchPhraseQuery("product_number", args.Query))
		case TermQueryOptionsValueDetailsSalesChannelID:
			subQuery.Filter(elastic.NewNestedQuery("options", elastic.NewTermQuery(
				"options.value_details_sales_channel_id", args.Query),
			))
		default:

		}
		queries = append(queries, subQuery)
	}
	return queries
}

type InternalSearchArg struct {
	IDs                  []string
	OrganizationID       string
	SalesChannelStoreKey string
	SalesChannelPlatform string
	Page                 int64
	Limit                int64
}

type SalesChannelProduct struct {
	ID                 string                          `json:"id"`
	ConnectorProductID string                          `json:"connector_product_id"`
	State              consts.SalesChannelProductState `json:"state"`
	Metrics            SalesChannelProductMetrics      `json:"metrics"`
}

type SalesChannelProductMetrics struct {
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

type ProductsCenterProduct struct {
	ID                 string                                   `json:"id"`
	ConnectorProductID string                                   `json:"connector_product_id"`
	PublishState       consts.ProductsCenterProductPublishState `json:"publish_state"`
	Source             ProductsCenterProductSource              `json:"source"`
	UpdatedAt          time.Time                                `json:"updated_at"`
}

type ProductsCenterProductSource struct {
	StoreKey string `json:"store_key"`
	Platform string `json:"platform"`
	ID       string `json:"id"`
}

type Ready struct {
	Status        consts.ReadyStatus `json:"status"`
	FailedReasons ReadyFailedReasons `json:"failed_reasons"`
	LastFailedAt  time.Time          `json:"last_failed_at"`
}

type ReadyFailedReason struct {
	Position    string   `json:"position"`
	ErrorCodes  []string `json:"error_codes"`
	Reasons     []string `json:"reasons"`
	Suggestions []string `json:"suggestions"`
}

type ReadyFailedReasons []ReadyFailedReason

// nolint:gocritic
func (c ReadyFailedReasons) EncodeSpanner() (interface{}, error) {
	bytes, err := json.Marshal(c)
	if err != nil {
		return nil, err
	}

	return string(bytes), nil
}

func (c *ReadyFailedReasons) DecodeSpanner(val interface{}) (err error) {
	strVal, _ := val.(string)
	return json.Unmarshal([]byte(strVal), c)
}

type Audit struct {
	State         consts.AuditState  `json:"state"`
	FailedReasons AuditFailedReasons `json:"failed_reasons"`
	LastFailedAt  time.Time          `json:"last_failed_at"`
}

type AuditFailedReason struct {
	Position    string   `json:"position"`
	Reasons     []string `json:"reasons"`
	Suggestions []string `json:"suggestions"`
}

type AuditFailedReasons []AuditFailedReason

// nolint:gocritic
func (c AuditFailedReasons) EncodeSpanner() (interface{}, error) {
	bytes, err := json.Marshal(c)
	if err != nil {
		return nil, err
	}

	return string(bytes), nil
}

func (c *AuditFailedReasons) DecodeSpanner(val interface{}) (err error) {
	strVal, _ := val.(string)
	return json.Unmarshal([]byte(strVal), c)
}

type Publish struct {
	LastReferenceID string              `json:"last_reference_id"`
	State           consts.PublishState `json:"state"`
	Error           PublishError        `json:"error"`
	LastFailedAt    time.Time           `json:"last_failed_at"`
}

type PublishError struct {
	Code string `json:"code"`
	Msg  string `json:"msg"`
}

type InventorySyncSetting struct {
	Preference   string               `json:"preference" validate:"omitempty,oneof='store' 'customized'"`
	Customized   models.InventorySync `json:"customized"`
	LastEffectAt time.Time            `json:"last_effect_at"`
}

type PriceSyncSetting struct {
	Preference   string           `json:"preference" validate:"omitempty,oneof='store' 'customized'"`
	Customized   models.PriceSync `json:"customized"`
	LastEffectAt time.Time        `json:"last_effect_at"`
}

type ProductSyncSetting struct {
	Preference   string             `json:"preference" validate:"omitempty,oneof='store' 'customized'"`
	Customized   models.ProductSync `json:"customized"`
	LastEffectAt time.Time          `json:"last_effect_at"`
}

type ProductSyncCustomized struct {
	Fields   string `json:"fields"`
	AutoSync string `json:"auto_sync"`
}

type SyncSettings struct {
	InventorySyncSetting InventorySyncSetting `json:"inventory_sync"`
	PriceSyncSetting     PriceSyncSetting     `json:"price_sync"`
	ProductSyncSetting   ProductSyncSetting   `json:"product_sync"`
}

// nolint:gocritic
func (c SyncSettings) EncodeSpanner() (interface{}, error) {
	bytes, err := json.Marshal(c)
	if err != nil {
		return nil, err
	}

	return string(bytes), nil
}

func (c *SyncSettings) DecodeSpanner(val interface{}) (err error) {
	strVal, _ := val.(string)
	return json.Unmarshal([]byte(strVal), c)
}

type ProductListing struct {
	ID                      string                            `json:"id"`
	SalesChannel            models.SalesChannel               `json:"sales_channel"`
	Organization            models.Organization               `json:"organization"`
	SalesChannelProduct     SalesChannelProduct               `json:"sales_channel_product"`
	ProductsCenterProduct   ProductsCenterProduct             `json:"products_center_product"`
	State                   consts.ProductListingProductState `json:"state"`
	LinkStatus              consts.LinkStatus                 `json:"link_status"`
	SyncStatus              consts.SyncStatus                 `json:"sync_status"`
	Ready                   Ready                             `json:"ready"`
	Audit                   Audit                             `json:"audit"`
	Publish                 Publish                           `json:"publish"`
	Settings                SyncSettings                      `json:"settings"`
	Product                 models.Product                    `json:"product"`
	Relations               []*ProductListingRelation         `json:"relations" validate:"dive,required"`
	FeedCustomizationParams FeedCustomizationParams           `json:"feed_customization_params"`
	Version                 int64                             `json:"version"`
	PendingDeletedAt        types.Datetime                    `json:"pending_deleted_at"`
	DeletedAt               types.Datetime                    `json:"deleted_at"`
	CreatedAt               time.Time                         `json:"created_at"`
	UpdatedAt               time.Time                         `json:"updated_at"`
}

func (p *ProductListing) GetMainOptionVariants() map[string][]*models.ProductVariant {
	mainProductOption, ok := p.Product.GetMainOption()
	if !ok {
		return nil
	}

	result := make(map[string][]*models.ProductVariant)
	mainOptionID := mainProductOption.SalesChannelOptionID

	for _, valueDetail := range mainProductOption.ValueDetails {
		skcOptionValueID := valueDetail.SalesChannelValueID
		for index := range p.Product.Variants {
			if p.Product.Variants[index].IsBelongToOption(mainOptionID, skcOptionValueID) {
				result[skcOptionValueID] = append(result[skcOptionValueID], p.Product.Variants[index])
			}
		}
	}

	return result
}

func (p *ProductListing) GetOptionVariants(salesChannelOptionId, salesChannelOptionValueID string) []*models.ProductVariant {

	result := make([]*models.ProductVariant, 0)

	for index := range p.Product.Variants {
		if p.Product.Variants[index].IsBelongToOption(salesChannelOptionId, salesChannelOptionValueID) {
			result = append(result, p.Product.Variants[index])
		}
	}

	return result
}

func (p *ProductListing) ModifyStateAndStatus() {
	switch p.SalesChannel.Platform {
	case consts.TikTokShop:
		p.ModifyTiktokShopStateAndStatus()
	case consts.Shein:
		p.ModifySheinStateAndStatus()
	default: // default to tiktok shop
		p.ModifyTiktokShopStateAndStatus()
	}
}

func (p *ProductListing) ModifyTiktokShopStateAndStatus() {
	p.SetTiktokState()
	p.SetVariantIDIfNeeded()
	p.SetRelationData()
	p.SetLinkStatus()
	p.SetSyncStatus()
}

func (p *ProductListing) ModifySheinStateAndStatus() {
	p.SetSheinState()
	p.SetVariantIDIfNeeded()
	p.SetRelationData()
	p.SetLinkStatus()
	p.SetSheinOptionSyncStatus()
	p.SetSyncStatus()
}

func (p *ProductListing) SetTiktokState() {
	switch p.SalesChannelProduct.State {
	case consts.SalesChannelProductStateLive:
		p.State = consts.ProductListingProductStateActive
	case consts.SalesChannelProductStatePending:
		p.State = consts.ProductListingProductStateReviewing
	case consts.SalesChannelProductStateSellerDeactivate, consts.SalesChannelProductStatePlatformDeactivated:
		p.State = consts.ProductListingProductStateInactive
	case consts.SalesChannelProductStateFailed, consts.SalesChannelProductStateFreeze:
		p.State = consts.ProductListingProductStateSuspended
	case consts.SalesChannelProductStateDeleted:
		p.State = consts.ProductListingProductStateDeleted
	default:
		p.State = consts.ProductListingProductStatePending
	}
}

func (p *ProductListing) SetSheinState() {

	mainOption, ok := p.Product.GetMainOption()
	if !ok {
		p.State = consts.ProductListingProductStatePending
		return
	}

	optionValueStatus := sets.StringSet{}
	for i := range mainOption.ValueDetails {
		// 补充默认状态
		if mainOption.ValueDetails[i].State == "" {
			mainOption.ValueDetails[i].State = consts.ProductOptionValueStatePending
		}

		// Pending reviewing suspend 都是初始状态
		if mainOption.ValueDetails[i].State == consts.ProductOptionValueStatePending ||
			mainOption.ValueDetails[i].State == consts.ProductOptionValueStateReviewing ||
			mainOption.ValueDetails[i].State == consts.ProductOptionValueStateSuspend {
			switch mainOption.ValueDetails[i].Audit.State {
			case consts.ProductOptionValueAuditStateReviewing:
				mainOption.ValueDetails[i].State = consts.ProductOptionValueStateReviewing
			case consts.ProductOptionValueAuditStateFailed:
				mainOption.ValueDetails[i].State = consts.ProductOptionValueStateSuspend
			}
		}

		optionValueStatus.Add(p.Product.Options[0].ValueDetails[i].State.String())
	}

	// 只有一个状态
	if len(optionValueStatus.ToList()) == 1 {
		switch optionValueStatus.ToList()[0] {
		case consts.ProductOptionValueStateActive.String():
			p.State = consts.ProductListingProductStateActive
		case consts.ProductOptionValueStateInactive.String():
			p.State = consts.ProductListingProductStateInactive
		case consts.ProductOptionValueStateReviewing.String():
			p.State = consts.ProductListingProductStateReviewing
		case consts.ProductOptionValueStateSuspend.String():
			p.State = consts.ProductListingProductStateSuspended
		case consts.ProductOptionValueStatePending.String():
			p.State = consts.ProductListingProductStatePending
		}

		return
	}

	// 多个状态
	if optionValueStatus.Contains(consts.ProductOptionValueStateActive.String()) {
		p.State = consts.ProductListingProductStatePartiallyActive
		return
	}
	if optionValueStatus.Contains(consts.ProductOptionValueStateReviewing.String()) {
		p.State = consts.ProductListingProductStatePartiallyReviewing
		return
	}
	if optionValueStatus.Contains(consts.ProductOptionValueStateSuspend.String()) {
		p.State = consts.ProductListingProductStatePartiallySuspend
		return
	}

	p.State = consts.ProductListingProductStatePending
}

func (p *ProductListing) SetSyncStatus() {
	syncCount := 0
	for _, relation := range p.Relations {
		if relation.SyncStatus == consts.SyncStatusSynced {
			syncCount++
		}
	}
	if syncCount == 0 {
		p.SyncStatus = consts.SyncStatusUnsync
		return
	}
	if syncCount < len(p.Relations) {
		p.SyncStatus = consts.SyncStatusPartialSynced
		return
	}
	p.SyncStatus = consts.SyncStatusSynced
}

func (p *ProductListing) SetSheinOptionSyncStatus() {
	if len(p.Product.Options) == 0 || len(p.Product.Options[0].ValueDetails) == 0 {
		return
	}

	for i := range p.Product.Options[0].ValueDetails {
		if p.Product.Options[0].ValueDetails[i].SalesChannelID != "" {
			p.Product.Options[0].ValueDetails[i].SyncStatus = consts.SyncStatusSynced
		} else {
			p.Product.Options[0].ValueDetails[i].SyncStatus = consts.SyncStatusUnsync
		}
	}
}

func (p *ProductListing) SetLinkStatus() {
	linkCount := 0
	for _, relation := range p.Relations {
		if relation.LinkStatus == consts.LinkStatusLinked {
			linkCount++
		}
	}
	if linkCount == 0 {
		p.LinkStatus = consts.LinkStatusUnlink
		return
	}
	if linkCount < len(p.Relations) {
		p.LinkStatus = consts.LinkStatusPartialLinked
		return
	}
	p.LinkStatus = consts.LinkStatusLinked
}

func (p *ProductListing) ClearReadyStatus() {
	p.Ready = Ready{
		Status:        consts.ReadyStatusReady,
		FailedReasons: []ReadyFailedReason{},
	}
}

func (p *ProductListing) SetReadyStatus(
	categoryRules *category.RulesOutput,
	categoryAttributes *category.AttributesOutput,
	salesChannelRegionConfig *config.ChannelRegionConfig,
	ttsCDNDomainRegExps []*regexp.Regexp,
) {
	p.Ready.Status = consts.ReadyStatusReady
	switch p.SalesChannel.Platform {
	case consts.TikTokShop:
		model := newTikTokShopReadyCheckModel(&p.Product, categoryRules, categoryAttributes, salesChannelRegionConfig, ttsCDNDomainRegExps)
		result := model.Check()
		for _, check := range result {
			reason := check.toFailReason()
			if reason != nil {
				p.Ready.FailedReasons = append(p.Ready.FailedReasons, *reason)
			}
		}
	case consts.Shein:
		model := newSheinReadyCheckModel(&p.Product, categoryRules, categoryAttributes)
		result := model.Check()
		for _, check := range result {
			reason := check.toFailReason()
			if reason != nil {
				p.Ready.FailedReasons = append(p.Ready.FailedReasons, *reason)
			}
		}
	default:
	}

	if len(p.Ready.FailedReasons) > 0 {
		p.Ready.Status = consts.ReadyStatusUnready
		p.Ready.LastFailedAt = time.Now()
	}
}

func (p *ProductListing) SetVariantIDIfNeeded() {
	for _, variant := range p.Product.Variants {
		if variant.ID == "" {
			variant.ID = uuid.GenerateUUIDV4()
		}
	}
}

func (p *ProductListing) SetRelationData() {
	for _, relation := range p.Relations {
		relation.SetRelationSyncStatus()
		relation.SetRelationLinkStatus()

		if relation.ProductListingVariantID != "" {
			continue
		}

		for _, variant := range p.Product.Variants {
			if variant.Position == relation.VariantPosition {
				relation.ProductListingVariantID = variant.ID
				break
			}
		}
	}
}

func (p *ProductListing) DeepCopy() *ProductListing {
	result := new(ProductListing)
	_ = copier.CopyWithOption(result, p, copier.Option{DeepCopy: true})
	return result
}

func (p *ProductListing) canUpdate() bool {
	return p.Publish.State != consts.PublishStateRunning
}

func (p *ProductListing) canDelete() bool {
	return p.Publish.State != consts.PublishStateRunning && p.State == consts.ProductListingProductStatePending
}

func (p *ProductListing) matched() bool {
	return p.ProductsCenterProduct.ID != ""
}

func (p *ProductListing) linked() bool {
	for i := range p.Relations {
		if p.Relations[i].ProductsCenterVariant.ID != "" {
			return true
		}
	}
	return false
}

func (p *ProductListing) canSyncPrice() bool {
	return p.State == consts.ProductListingProductStateActive
}

func (p *ProductListing) canSyncInventory() bool {
	return p.State != consts.ProductListingProductStateDeleted
}

// autoLinkAllSKUMatchCheck [全匹配] SPU match 则会返回 true, 并且返回 map[product_listing_variant_id]products_center_variant_id
// nolint:funlen,gocyclo
func (p *ProductListing) autoLinkAllSKUMatchCheck(pcProduct *products_center.Product) (map[string]string, bool) {
	// 不预期的商品信息
	if len(p.Product.Variants) == 0 || len(p.Product.Variants) != len(pcProduct.Variants) {
		return nil, false
	}

	// pc product sku 校验和收集
	if pcProductSKUMap, skuCheckPass := collectProductCenterSKUMap(pcProduct); skuCheckPass {
		// pc product sku 和 listing product sku 进行匹配
		if result, ok := p.matchByProductCenterSKU(pcProductSKUMap); ok {
			return result, true
		}
	}

	// singer product match
	if len(p.Product.Variants) == 1 && len(pcProduct.Variants) == 1 &&
		len(pcProduct.Variants[0].Options) == 0 && len(p.Product.Variants[0].Options) == 0 {
		return map[string]string{p.Product.Variants[0].ID: pcProduct.Variants[0].ID}, true
	}

	// pc product option 校验和收集
	if pcOptionsMap, optionCheckPass := collectProductOptionsMap(pcProduct); optionCheckPass {
		// pc product option 和 listing product option 进行匹配
		if result, ok := p.matchByProductCenterOptions(pcOptionsMap); ok {
			return result, true
		}
	}

	return nil, false
}

// pc product sku 校验和收集
func collectProductCenterSKUMap(pcProduct *products_center.Product) (map[string]string, bool) {
	pcSKUMap := make(map[string]string, len(pcProduct.Variants))
	for index := range pcProduct.Variants { // nolint:gocritic
		// 空 SKU
		if pcProduct.Variants[index].Sku == "" {
			return nil, false
		}
		// 重复 SKU
		if _, ok := pcSKUMap[pcProduct.Variants[index].Sku]; ok {
			return nil, false
		}
		pcSKUMap[pcProduct.Variants[index].Sku] = pcProduct.Variants[index].ID
	}
	return pcSKUMap, true
}

// pc product sku 和 listing product sku 进行匹配
func (p *ProductListing) matchByProductCenterSKU(pcSKUMap map[string]string) (map[string]string, bool) {
	result := make(map[string]string, len(p.Product.Variants))
	for _, variant := range p.Product.Variants {
		if variant.Sku == "" {
			break
		}
		if variantID, ok := pcSKUMap[variant.Sku]; ok {
			result[variant.ID] = variantID
		} else {
			return nil, false
		}
		delete(pcSKUMap, variant.Sku)
	}
	// 避免 listings sku 有重复的情况, 需要确认 pc sku map 都被摘取完了
	if len(pcSKUMap) != 0 {
		return nil, false
	}
	return result, true
}

// pc product option 校验和收集
func collectProductOptionsMap(pcProduct *products_center.Product) (map[string]string, bool) {
	pcOptionsMap := make(map[string]string, len(pcProduct.Variants))
	for _, variant := range pcProduct.Variants { // nolint:gocritic
		if len(variant.Options) == 0 { // 不预期的数据
			return pcOptionsMap, false
		}
		optionKey := generateProductsCenterVariantOptionOptionKeys(variant.Options)
		pcOptionsMap[optionKey] = variant.ID
	}

	return pcOptionsMap, true
}

// pc product option 和 listing product option 进行匹配
func (p *ProductListing) matchByProductCenterOptions(pcOptionsMap map[string]string) (map[string]string, bool) {
	result := make(map[string]string, len(p.Product.Variants))
	for _, variant := range p.Product.Variants {
		if len(variant.Options) == 0 { // 不预期的数据
			return nil, false
		}
		optionKey := generateProductListingVariantOptionKeys(variant.Options)
		if variantID, ok := pcOptionsMap[optionKey]; ok {
			result[variant.ID] = variantID
		} else {
			return nil, false
		}
		delete(pcOptionsMap, optionKey)
	}
	if len(pcOptionsMap) != 0 {
		return nil, false
	}
	return result, true
}

// autoLinkSKUMatchCheck [非全匹配]
// nolint:gocyclo
func (p *ProductListing) autoLinkSKUMatchCheck(pcProduct *products_center.Product) (map[string]string, bool) {
	// 初始化结果映射和辅助数据结构
	result := make(map[string]string, len(p.Product.Variants))
	sameSKUSet := set.NewStringSet()
	pcSKUMap := make(map[string]string, len(pcProduct.Variants))
	listingSKUMap := make(map[string]string, len(p.Product.Variants))

	// 构建 pcProduct 的 SKU 映射
	for index := range pcProduct.Variants { // nolint:gocritic
		if pcProduct.Variants[index].Sku == "" {
			continue
		}
		if _, ok := pcSKUMap[pcProduct.Variants[index].Sku]; ok {
			sameSKUSet.Add(pcProduct.Variants[index].Sku)
		}
		pcSKUMap[pcProduct.Variants[index].Sku] = pcProduct.Variants[index].ID
	}

	// 构建 listing product 的 SKU 映射
	for _, variant := range p.Product.Variants {
		if variant.Sku == "" {
			continue
		}
		if _, ok := listingSKUMap[variant.Sku]; ok {
			sameSKUSet.Add(variant.Sku)
		}
		listingSKUMap[variant.Sku] = variant.ID
	}

	// 如果任一 SKU 映射为空，则无法进行匹配
	if len(pcSKUMap) == 0 || len(listingSKUMap) == 0 {
		return nil, false
	}

	// 拿 pc product 进行 SKU 匹配
	for _, variant := range p.Product.Variants {
		if variant.Sku == "" || sameSKUSet.Contains(variant.Sku) {
			continue
		}
		if variantID, ok := pcSKUMap[variant.Sku]; ok {
			result[variant.ID] = variantID
		}
	}

	if len(result) == 0 {
		return nil, false
	}

	return result, true
}

// autoLinkOptionsMatchCheck [非全匹配]
// nolint:gocyclo
func (p *ProductListing) autoLinkOptionsMatchCheck(pcProduct *products_center.Product) (map[string]string, bool) {
	result := make(map[string]string, len(p.Product.Variants))
	optionsMatchCheck := true
	pcOptionsMap, ok := collectProductOptionsMap(pcProduct)
	if !ok {
		optionsMatchCheck = false
	}
	for _, variant := range p.Product.Variants {
		if len(variant.Options) == 0 { // 不预期的数据
			optionsMatchCheck = false
			break
		}
		optionKey := generateProductListingVariantOptionKeys(variant.Options)
		if variantID, ok := pcOptionsMap[optionKey]; ok {
			result[variant.ID] = variantID
		}
	}
	if len(result) == 0 || !optionsMatchCheck {
		return nil, false
	}
	return result, true
}

func (p *ProductListing) CollectSameSKUCodes() []string {
	someSKUCodes := set.NewStringSet()
	allVariantSKUCodes := set.NewStringSet()
	for _, variant := range p.Product.Variants {
		if variant.Sku == "" {
			continue
		}
		if allVariantSKUCodes.Contains(variant.Sku) {
			someSKUCodes.Add(variant.Sku)
		}
		allVariantSKUCodes.Add(variant.Sku)
	}
	return someSKUCodes.ToList()
}

// nolint:dupl
func generateProductListingVariantOptionKeys(options []*models.ProductVariantOption) string {
	sortedOptions := make([]*models.ProductVariantOption, len(options))
	for i, option := range options {
		sortedOptions[i] = &models.ProductVariantOption{
			Name:  option.Name,
			Value: option.Value,
		}
	}
	sort.Slice(sortedOptions, func(i, j int) bool {
		return sortedOptions[i].Name < sortedOptions[j].Name
	})

	optionsRawText := make([]string, len(sortedOptions))
	for i, option := range sortedOptions {
		optionsRawText[i] = fmt.Sprintf(optionsMapKeyTemplate, option.Name, option.Value)
	}
	return strings.ToLower(strings.Join(optionsRawText, "-"))
}

// nolint:dupl
func generateProductsCenterVariantOptionOptionKeys(options []products_center.VariantOption) string {
	sortedOptions := make([]*products_center.VariantOption, len(options))
	for i, option := range options {
		sortedOptions[i] = &products_center.VariantOption{
			Name:  option.Name,
			Value: option.Value,
		}
	}
	sort.Slice(sortedOptions, func(i, j int) bool {
		return sortedOptions[i].Name < sortedOptions[j].Name
	})

	optionsRawText := make([]string, len(sortedOptions))
	for i, option := range sortedOptions {
		optionsRawText[i] = fmt.Sprintf(optionsMapKeyTemplate, option.Name, option.Value)
	}
	return strings.ToLower(strings.Join(optionsRawText, "-"))
}

type AuditVersion struct {
	ID             string         `json:"id"`
	CreatedAt      time.Time      `json:"created_at"`
	ProductListing ProductListing `json:"product_listing"`
}

func (arg *AuditVersion) convertToAuditVersionDBModel(id string) auditVersionDBModel {
	return auditVersionDBModel{
		ProductListingID: id,
		CreatedAt:        arg.CreatedAt,
		AuditData: AuditData{
			ProductListing: arg.ProductListing,
		},
	}
}

type AuditData struct {
	ProductListing ProductListing `json:"product_listing"`
}

// nolint:gocritic
func (d AuditData) EncodeSpanner() (interface{}, error) {
	bytes, err := json.Marshal(d)
	if err != nil {
		return nil, err
	}

	return string(bytes), nil
}

func (d *AuditData) DecodeSpanner(val interface{}) (err error) {
	strVal, _ := val.(string)
	return json.Unmarshal([]byte(strVal), d)
}

type ProductListingRelation struct {
	ID                      string                 `json:"id"`
	Organization            models.Organization    `json:"organization"`
	SalesChannel            models.SalesChannel    `json:"sales_channel"`
	ProductListingID        string                 `json:"product_listing_id"`
	VariantPosition         int                    `json:"variant_position"`
	ProductListingVariantID string                 `json:"product_listing_variant_id"`
	SalesChannelVariant     SalesChannelVariant    `json:"sales_channel_variant"`
	ProductsCenterVariant   ProductsCenterVariant  `json:"products_center_variant"`
	SyncStatus              consts.SyncStatus      `json:"sync_status"`
	LinkStatus              consts.LinkStatus      `json:"link_status"`
	AllowSync               consts.AllowSyncStatus `json:"allow_sync"`
	LastSyncedAt            types.Datetime         `json:"last_synced_at"`
	LastLinkedAt            types.Datetime         `json:"last_linked_at"`
	DeletedAt               types.Datetime         `json:"deleted_at"`
	CreatedAt               time.Time              `json:"created_at"`
	UpdatedAt               time.Time              `json:"updated_at"`
}

func (r *ProductListingRelation) DeepCopy() *ProductListingRelation {
	result := new(ProductListingRelation)
	_ = copier.CopyWithOption(result, r, copier.Option{DeepCopy: true})
	return result
}

func (r *ProductListingRelation) SetRelationSyncStatus() {
	if r.SalesChannelVariant.ID == "" ||
		r.SalesChannelVariant.ConnectorProductID == "" {
		r.SyncStatus = consts.SyncStatusUnsync
	} else {
		r.SyncStatus = consts.SyncStatusSynced
	}

	if r.SyncStatus == consts.SyncStatusSynced {
		if !r.LastSyncedAt.Assigned() || r.LastSyncedAt.IsNull() {
			r.LastSyncedAt = types.MakeDatetime(time.Now())
		}
	} else {
		r.LastSyncedAt = types.NullDatetime
	}
}

func (r *ProductListingRelation) SetRelationLinkStatus() {
	if r.ProductsCenterVariant.ID == "" {
		r.LinkStatus = consts.LinkStatusUnlink
	} else {
		r.LinkStatus = consts.LinkStatusLinked
	}
	if r.LinkStatus == consts.LinkStatusLinked {
		if !r.LastLinkedAt.Assigned() || r.LastLinkedAt.IsNull() {
			r.LastLinkedAt = types.MakeDatetime(time.Now())
		}
	} else {
		r.LastLinkedAt = types.NullDatetime
	}
}

func (r *ProductListingRelation) IsDeleted() bool {
	return r.DeletedAt.Assigned() && r.DeletedAt.Datetime().Unix() > 0
}

func (r *ProductListingRelation) Unlink() {
	r.ProductsCenterVariant = ProductsCenterVariant{}
}

func (r *ProductListingRelation) IsLinkedAndSynced() bool {
	return r.LinkStatus == consts.LinkStatusLinked && r.SyncStatus == consts.SyncStatusSynced
}

type SalesChannelVariant struct {
	ID                 string `json:"id"`
	ConnectorProductID string `json:"connector_product_id"`
	ProductID          string `json:"product_id"`
	Sku                string `json:"sku"`
}

type ProductsCenterVariant struct {
	ID                 string                      `json:"id"`
	ProductID          string                      `json:"product_id"`
	ConnectorProductID string                      `json:"connector_product_id"`
	Source             ProductsCenterVariantSource `json:"source"`
}

type ProductsCenterVariantSource struct {
	StoreKey  string `json:"store_key"`
	Platform  string `json:"platform"`
	ID        string `json:"id"`
	ProductID string `json:"product_id"`
	Sku       string `json:"sku"`
}

type PushToChannelArg struct {
	SalesChannel            models.SalesChannel         `json:"sales_channel"`
	Organization            models.Organization         `json:"organization"`
	ProductsCenterProduct   ProductsCenterProduct       `json:"products_center_product"`
	Product                 models.Product              `json:"product"`
	Relations               []*PushToChannelRelationArg `json:"relations"`
	FeedCustomizationParams FeedCustomizationParams     `json:"feed_customization_params"`
	Ready                   Ready                       `json:"ready"`
}

type PushToChannelRelationArg struct {
	ProductListingVariantID string                `json:"product_listing_variant_id"`
	ProductsCenterVariant   ProductsCenterVariant `json:"products_center_variant"`
}

type SalesChannelProductEventArg struct {
	ID                  string                                 `json:"id"`
	SalesChannel        models.SalesChannel                    `json:"sales_channel"`
	Organization        models.Organization                    `json:"organization"`
	SalesChannelProduct SalesChannelProduct                    `json:"sales_channel_product"`
	Product             models.Product                         `json:"product"`
	Relations           []*SalesChannelProductEventRelationArg `json:"relations"`
	Audit               Audit                                  `json:"audit"`
	Version             int64                                  `json:"version"`
}

func (arg *SalesChannelProductEventArg) createPreCheck() error {
	switch arg.SalesChannel.Platform {
	case consts.Shein:
		return arg.sheinPlatformCreatePreCheck()
	default:
		return nil
	}
}

func (arg *SalesChannelProductEventArg) updatePreCheck() error {
	switch arg.SalesChannel.Platform {
	case consts.Shein:
		return arg.sheinPlatformUpdatePreCheck()
	default:
		return nil
	}
}

func (arg *SalesChannelProductEventArg) sheinPlatformCreatePreCheck() error {
	// https://www.notion.so/automizely/shein-skc-mapping-logic-17d9d972864f80c696a4e99a2f0594fa
	// 1. shein 侧商品，skc 都审核失败，不算有效商品
	// 2. shein 侧商品，skc 都在审核中，不算有效商品
	// shein 的 skc 信息在 product option 里面，如果是审核中和审核失败的，都是没有 option value id 的，所以只需要判断 skc 的 option 的 value detail 中都是空即可
	mainProductOption, ok := arg.Product.GetMainOption()
	if !ok {
		return ErrSheinProductIsInvalid
	}

	// main option 没有 option id
	if mainProductOption.SalesChannelOptionID == "" {
		return ErrSheinProductIsInvalid
	}

	// main option 没有 SKC 信息
	if len(mainProductOption.ValueDetails) == 0 {
		return ErrSheinProductIsInvalid
	}

	salesChannelValueIDs := make([]string, 0)
	for _, value := range mainProductOption.ValueDetails {
		if value.SalesChannelValueID != "" {
			salesChannelValueIDs = append(salesChannelValueIDs, value.SalesChannelValueID)
		}
	}

	if len(salesChannelValueIDs) == 0 {
		return ErrSheinProductIsInvalid
	}

	return nil
}

func (arg *SalesChannelProductEventArg) sheinPlatformUpdatePreCheck() error {
	// 1. 更新商品时，SKC 首次都在审核中，不需要进行处理
	mainProductOption, ok := arg.Product.GetMainOption()
	if !ok {
		return ErrSheinProductIsInvalid
	}

	reviewingCount := 0
	for _, value := range mainProductOption.ValueDetails {
		if value.Audit.State == consts.ProductOptionValueAuditStateReviewing {
			reviewingCount++
		}
	}

	if reviewingCount == len(mainProductOption.ValueDetails) {
		return ErrSheinProductIsInvalid
	}

	return nil
}

func (arg *SalesChannelProductEventArg) convertToProductListingArgs(optionNameMapping map[string]string) *ProductListingArgs {
	result := &ProductListingArgs{}
	result.ID = arg.ID
	result.Organization = arg.Organization
	result.SalesChannel = arg.SalesChannel
	result.Product = arg.Product

	// Apply option name mapping for options with names longer than 20 characters
	result.Product.OverwriteOptions(optionNameMapping)

	result.SalesChannelProduct = arg.SalesChannelProduct
	result.Audit = arg.Audit
	result.Version = arg.Version
	result.Relations = make([]*ProductListingRelation, 0, len(arg.Relations))
	for i := range arg.Relations {
		result.Relations = append(result.Relations, &ProductListingRelation{
			ID:                  arg.Relations[i].ID,
			VariantPosition:     arg.Relations[i].VariantPosition,
			SalesChannelVariant: arg.Relations[i].SalesChannelVariant,
		})
	}
	return result
}

type SalesChannelProductEventRelationArg struct {
	ID                  string              `json:"id"`
	VariantPosition     int                 `json:"variant_position"`
	SalesChannelVariant SalesChannelVariant `json:"sales_channel_variant"`
}

type ProductsCenterProductEventArg struct {
	ProductsCenterProduct ProductsCenterProduct                    `json:"products_center_product"`
	Product               models.Product                           `json:"product"`
	Relations             []*ProductsCenterProductEventRelationArg `json:"relations"`
	Ready                 Ready                                    `json:"ready"`
	ForcePublish          bool                                     `json:"force_publish"`
}

func (arg *ProductsCenterProductEventArg) convertToProductListingArgs(listing *ProductListing, removeVariantsImage bool, optionNameMapping map[string]string) *ProductListingArgs {
	result := &ProductListingArgs{}
	result.ProductsCenterProduct = arg.ProductsCenterProduct
	result.Product = arg.convertToProduct(listing, optionNameMapping)
	result.Relations = make([]*ProductListingRelation, 0, len(arg.Relations))
	for i := range arg.Relations {
		result.Relations = append(result.Relations, &ProductListingRelation{
			ProductListingVariantID: arg.Relations[i].ProductListingVariantID,
			ProductsCenterVariant:   arg.Relations[i].ProductsCenterVariant,
		})
	}
	// 指定白名单内的客户需要清空 variants 的图片
	if removeVariantsImage {
		for i := range result.Product.Variants {
			result.Product.Variants[i].ImageURL = ""
		}
	}

	return result
}

func (arg *ProductsCenterProductEventArg) convertToProduct(listing *ProductListing, optionNameMapping map[string]string) models.Product {
	product := listing.Product
	// products center 事件需要更新的字段
	product.Title = arg.Product.Title
	product.ShortDescription = arg.Product.ShortDescription
	product.Description = arg.Product.Description
	product.Tags = arg.Product.Tags
	product.Vendor = arg.Product.Vendor
	product.Options = arg.convertToOptions(optionNameMapping)
	product.Media = arg.Product.Media
	product.Variants = arg.convertToVariant(listing, optionNameMapping)
	return product
}

func (arg *ProductsCenterProductEventArg) convertToOptions(optionNameMapping map[string]string) []*models.ProductOption {
	return models.ConvertOptionsWithNameMapping(arg.Product.Options, optionNameMapping)
}

// nolint:gocyclo
func (arg *ProductsCenterProductEventArg) convertToVariant(listing *ProductListing, optionNameMapping map[string]string) []*models.ProductVariant {
	lastVariantsMap := make(map[string]*models.ProductVariant)
	for i := range listing.Product.Variants {
		lastVariantsMap[listing.Product.Variants[i].ID] = listing.Product.Variants[i]
	}
	variants := make([]*models.ProductVariant, 0, len(arg.Product.Variants))
	for i := range arg.Product.Variants {
		if lastVariant, ok := lastVariantsMap[arg.Product.Variants[i].ID]; ok {
			// 保留上次的 package 配置数据
			arg.Product.Variants[i].Length = lastVariant.Length
			arg.Product.Variants[i].Width = lastVariant.Width
			arg.Product.Variants[i].Weight = lastVariant.Weight
			arg.Product.Variants[i].Height = lastVariant.Height
		}
		// 如果 package 配置数据为空或者不合法，使用默认值覆盖
		if arg.Product.Variants[i].Length.Unit == "" ||
			arg.Product.Variants[i].Length.Value <= 0 {
			arg.Product.Variants[i].Length = listing.Product.Variants[0].Length
		}
		if arg.Product.Variants[i].Width.Unit == "" ||
			arg.Product.Variants[i].Width.Value <= 0 {
			arg.Product.Variants[i].Width = listing.Product.Variants[0].Width
		}
		if arg.Product.Variants[i].Weight.Unit == "" ||
			arg.Product.Variants[i].Weight.Value <= 0 {
			arg.Product.Variants[i].Weight = listing.Product.Variants[0].Weight
		}
		if arg.Product.Variants[i].Height.Unit == "" ||
			arg.Product.Variants[i].Height.Value <= 0 {
			arg.Product.Variants[i].Height = listing.Product.Variants[0].Height
		}

		if len(arg.Product.Variants[i].Options) > 0 {
			arg.Product.Variants[i].Options = models.ConvertVariantOptionsWithNameMapping(arg.Product.Variants[i].Options, optionNameMapping)
		}

		variants = append(variants, arg.Product.Variants[i])
	}
	return variants
}

type ProductsCenterProductEventRelationArg struct {
	ProductListingVariantID string                `json:"product_listing_variant_id"`
	ProductsCenterVariant   ProductsCenterVariant `json:"products_center_variant"`
}

type EditAttributesArgs struct {
	CategorySourceID string
	SizeChart        models.ProductSizeChart
	Certifications   []*models.ProductCertification
	Attributes       []*models.ProductAttribute
	Length           models.ProductVariantShippingSetting
	Width            models.ProductVariantShippingSetting
	Height           models.ProductVariantShippingSetting
	Weight           models.ProductVariantShippingSetting
	Brand            models.SalesChannelResource
	Compliance       models.ProductCompliance
}

func (arg *EditAttributesArgs) overWriteCompliance(listing *ProductListing) {
	if len(arg.Compliance.ResponsiblePersons) > 0 {
		listing.Product.Compliance.ResponsiblePersons = arg.Compliance.ResponsiblePersons
	}
	if len(arg.Compliance.Manufacturers) > 0 {
		listing.Product.Compliance.Manufacturers = arg.Compliance.Manufacturers
	}
}

func (arg *EditAttributesArgs) overWriteBrand(listing *ProductListing) {
	if arg.Brand.SalesChannelID == "" {
		return
	}

	listing.Product.Brand = arg.Brand
}

func (arg *EditAttributesArgs) overWriteShippingSetting(listing *ProductListing) {
	// 更新 variant 的 package 配置
	for i := range listing.Product.Variants {
		// 参数里面传递了才进行更新
		if arg.Length.Unit != "" && arg.Length.Value > 0 {
			listing.Product.Variants[i].Length = arg.Length
		}
		if arg.Width.Unit != "" && arg.Width.Value > 0 {
			listing.Product.Variants[i].Width = arg.Width
		}
		if arg.Height.Unit != "" && arg.Height.Value > 0 {
			listing.Product.Variants[i].Height = arg.Height
		}
		if arg.Weight.Unit != "" && arg.Weight.Value > 0 {
			listing.Product.Variants[i].Weight = arg.Weight
		}
	}

}

func (arg *EditAttributesArgs) overWriteSizeChart(listing *ProductListing, categoryRules *category.RulesOutput) {
	if len(arg.SizeChart.Images) == 0 {
		return
	}
	if categoryRules == nil {
		return
	}
	if categoryRules.Rule.SizeChart == nil {
		return
	}
	if !categoryRules.Rule.SizeChart.IsRequired {
		return
	}

	listing.Product.SizeChart = arg.SizeChart
}

// nolint:gocyclo
func (arg *EditAttributesArgs) overWriteCertification(listing *ProductListing, categoryRules *category.RulesOutput) {
	if len(arg.Certifications) == 0 {
		return
	}
	if categoryRules == nil {
		return
	}
	if categoryRules.Rule.ProductCertifications == nil {
		return
	}
	if len(categoryRules.Rule.ProductCertifications) == 0 {
		return
	}

	productAttributesMap := make(map[string]*models.ProductAttribute, len(listing.Product.Attributes))
	for _, productAttribute := range listing.Product.Attributes {
		productAttributesMap[productAttribute.SalesChannelID] = productAttribute
	}

	// 或许需要填写的认证信息
	requireCertificationIDs := make([]string, 0)
	for i := range categoryRules.Rule.ProductCertifications {
		if categoryRules.Rule.ProductCertifications[i].IsRequired {
			requireCertificationIDs = append(requireCertificationIDs, categoryRules.Rule.ProductCertifications[i].ExternalId)
			continue
		}
		if len(categoryRules.Rule.ProductCertifications[i].RequirementConditions) == 0 {
			continue
		}
		isConditionRequired := false
		for j := range categoryRules.Rule.ProductCertifications[i].RequirementConditions {
			productAttribute, ok := productAttributesMap[categoryRules.Rule.ProductCertifications[i].RequirementConditions[j].AttributeID]
			if !ok {
				continue
			}
			for k := range productAttribute.Values {
				if productAttribute.Values[k].SalesChannelID == categoryRules.Rule.ProductCertifications[i].RequirementConditions[j].AttributeValueID {
					isConditionRequired = true
				}
			}
		}
		if isConditionRequired {
			requireCertificationIDs = append(requireCertificationIDs, categoryRules.Rule.ProductCertifications[i].ExternalId)
		}
	}

	// 没有需要填写的认证信息
	if len(requireCertificationIDs) == 0 {
		return
	}

	// 获取当前 listing 已经填写的 certification
	currentCertifications := listing.Product.Certifications
	for i := range arg.Certifications {
		// 没有填写图片和文件
		if len(arg.Certifications[i].Images) == 0 && len(arg.Certifications[i].Files) == 0 {
			continue
		}
		// 参数中不是需要填写的认证信息
		if !slices.Contains(requireCertificationIDs, arg.Certifications[i].SalesChannelID) {
			continue
		}

		isExist := false
		for j := range currentCertifications {
			// 已经填写过的认证信息，使用参数中的图片和文件覆盖
			if currentCertifications[j].SalesChannelID == arg.Certifications[i].SalesChannelID {
				if len(arg.Certifications[i].Images) > 0 {
					currentCertifications[j].Images = arg.Certifications[i].Images
				}
				if len(arg.Certifications[j].Files) > 0 {
					currentCertifications[j].Files = arg.Certifications[i].Files
				}
				isExist = true
				break
			}
		}

		// 没有填写过的认证信息，添加到当前认证信息中
		if !isExist {
			currentCertifications = append(currentCertifications, arg.Certifications[i])
		}
	}

	listing.Product.Certifications = currentCertifications
}

// nolint:gocyclo
func (arg *EditAttributesArgs) overWriteAttributes(listing *ProductListing, categoryAttributes *category.AttributesOutput) {
	if len(arg.Attributes) == 0 {
		return
	}

	if categoryAttributes == nil {
		return
	}

	if len(categoryAttributes.Attributes) == 0 {
		return
	}

	categoryProductAttributesMap := make(map[string]category.Attribute, len(categoryAttributes.Attributes))
	for i := range categoryAttributes.Attributes {
		if categoryAttributes.Attributes[i].Type == consts.AttributeTypeProductProperty {
			categoryProductAttributesMap[categoryAttributes.Attributes[i].ID] = categoryAttributes.Attributes[i]
		}
	}

	currentAttributes := listing.Product.Attributes

	for i := range arg.Attributes {
		if len(arg.Attributes[i].Values) == 0 {
			continue
		}

		if categoryAttribute, ok := categoryProductAttributesMap[arg.Attributes[i].SalesChannelID]; ok {
			if !categoryAttribute.IsRequired && len(categoryAttribute.RequirementConditions) == 0 {
				continue // not_required, 有 condition 的属性先给其填写进去, 后续再去除
			}
		} else {
			continue // not_found
		}

		isExist := false
		for j := range currentAttributes {
			if currentAttributes[j].SalesChannelID == arg.Attributes[i].SalesChannelID {
				currentAttributes[j] = arg.Attributes[i]
				isExist = true
				break
			}
		}
		if !isExist {
			currentAttributes = append(currentAttributes, arg.Attributes[i])
		}
	}

	currentAttributesMap := make(map[string]*models.ProductAttribute, len(currentAttributes))
	for i := range currentAttributes {
		currentAttributesMap[currentAttributes[i].SalesChannelID] = currentAttributes[i]
	}

	depthMap := categoryAttributes.BuildDepthMap()
	sort.Slice(currentAttributes, func(i, j int) bool {
		return depthMap[currentAttributes[i].SalesChannelID] < depthMap[currentAttributes[j].SalesChannelID]
	})

	newCurrentAttributes := make([]*models.ProductAttribute, 0)
	for i := range currentAttributes {
		// 只过滤有 condition 的 attributes
		categoryAttribute, ok := categoryProductAttributesMap[currentAttributes[i].SalesChannelID]
		if !ok {
			// 防御性代码, 不存在的属性, 剔除
			delete(currentAttributesMap, currentAttributes[i].SalesChannelID)
			continue
		}
		if len(categoryAttribute.RequirementConditions) == 0 {
			newCurrentAttributes = append(newCurrentAttributes, currentAttributes[i])
			continue
		}

		conditionsRemove := true
		for _, condition := range categoryAttribute.RequirementConditions {
			curAttribute, inOk := currentAttributesMap[condition.AttributeID]
			if !inOk {
				continue
			}
			for _, value := range curAttribute.Values {
				if value.SalesChannelID == condition.AttributeValueID {
					conditionsRemove = false // 父属性选择了对应值, 保留
					break
				}
			}
		}
		if !conditionsRemove {
			newCurrentAttributes = append(newCurrentAttributes, currentAttributes[i])
		} else {
			delete(currentAttributesMap, currentAttributes[i].SalesChannelID)
		}
	}

	listing.Product.Attributes = newCurrentAttributes
}

func (arg *EditAttributesArgs) buildUpdateListing(listing *ProductListing,
	categoryRules *category.RulesOutput,
	categoryAttributes *category.AttributesOutput,
	salesChannelRegionConfig *config.ChannelRegionConfig,
	ttsCDNDomainRegExps []*regexp.Regexp,
) *ProductListing {
	newListing := listing.DeepCopy()

	arg.overWriteCompliance(newListing)
	arg.overWriteBrand(newListing)
	arg.overWriteSizeChart(newListing, categoryRules)
	arg.overWriteAttributes(newListing, categoryAttributes)
	// 因 condition 关系, certification 需要在 attributes 后面
	arg.overWriteCertification(newListing, categoryRules)
	arg.overWriteShippingSetting(newListing)

	newListing.ClearReadyStatus()
	newListing.SetReadyStatus(categoryRules, categoryAttributes, salesChannelRegionConfig, ttsCDNDomainRegExps)

	return newListing
}

func (arg *EditAttributesArgs) buildConductorUpdateArgs(listing *ProductListing,
	categoryRules *category.RulesOutput,
	categoryAttributes *category.AttributesOutput,
	salesChannelRegionConfig *config.ChannelRegionConfig,
	ttsCDNDomainRegExps []*regexp.Regexp,
) *conductorUpdateArgs {
	newListing := arg.buildUpdateListing(listing, categoryRules, categoryAttributes, salesChannelRegionConfig, ttsCDNDomainRegExps)

	result := convertToConductorUpdateArgs(newListing, listing)
	return &result
}

type notifySalesChannelUpsertEventPubsubMessage struct {
	Products []notifySalesChannelUpsertEventProduct `json:"products"`
}

type notifySalesChannelUpsertEventProduct struct {
	ProductsCenterProductID string `json:"products_center_product_id"` // same as searchable_product_id

	// 暂时保留, 兼容 listings-work 上线时间差异
	ConnectorsProductID string `json:"connectors_product_id"`
}

type LinkArg struct {
	ProductListingID string             `validate:"required"`
	LinkedVariants   []LinkedVariantArg `validate:"required,dive"`
}

type LinkedVariantArg struct {
	ProductListingVariantID        string `validate:"required"`
	ProductsCenterProductID        string
	ProductsCenterProductVariantID string
}

type UnLinkArg struct {
	ProductListingID         string   `validate:"required"`
	ProductListingVariantIDs []string `validate:"required"`
}

type getEcommerceVariants struct {
	OrganizationID string   `validate:"required"`
	AppPlatform    string   `validate:"required"`
	AppKey         string   `validate:"required"`
	SKUs           []string `validate:"required,gt=0"`
}

type linkEcommerceVariantInfo struct {
	hitCount                int
	productsCenterProductID string
	ProductsCenterVariantID string
}

type ListArg struct {
	IDs                      string
	OrganizationID           string
	SalesChannelPlatform     string
	SalesChannelStoreKey     string
	ProductsCenterProductIDs string
	SalesChannelProductIDs   string
	Limit                    int64
	Page                     int64
}

func (arg *ListArg) buildRepoListArgs() *repoListArgs {
	result := &repoListArgs{
		OrganizationID:       arg.OrganizationID,
		SalesChannelPlatform: arg.SalesChannelPlatform,
		SalesChannelStoreKey: arg.SalesChannelStoreKey,
		Limit:                arg.Limit,
		Page:                 arg.Page,
	}

	if arg.ProductsCenterProductIDs != "" {
		result.ProductsCenterProductIDs = strings.Split(arg.ProductsCenterProductIDs, ",")
	}
	if arg.SalesChannelProductIDs != "" {
		result.SalesChannelProductIDs = strings.Split(arg.SalesChannelProductIDs, ",")
	}
	return result
}

func (arg *LinkedVariantArg) DoLink() bool {
	return arg.ProductsCenterProductID != "" && arg.ProductsCenterProductVariantID != ""
}

func (arg *LinkedVariantArg) DoUnlink() bool {
	return arg.ProductsCenterProductID == "" || arg.ProductsCenterProductVariantID == ""
}

type GetIDsWithinUnfinishedTaskArg struct {
	OrganizationID string
	StoreKey       string
	Platform       string
	GroupName      consts.TaskGroupName
}

type AutoLinkArg struct {
	ID string `validate:"required"`
	// 当不为空时,只需要处理数组内的 VariantID
	TargetVariantIDs []string // option

	// 会使用此 ProductCenterProduct 再进行一次兜底 auto-link
	RecommendedPCProductID string `json:"recommended_pc_product_id"`
}

type autoLinkListing struct {
	app                    models.App
	productListing         ProductListing
	targetVariantIDs       []string
	recommendedPCProductID string
}

func (arg *autoLinkListing) isTargetVariant(variantID string) bool {
	for _, id := range arg.targetVariantIDs {
		if id == variantID {
			return true
		}
	}
	return false
}

func (arg *autoLinkListing) isOnlyTarget() bool {
	return len(arg.targetVariantIDs) > 0
}

type autoLinkMappingResult struct {
	// link 成功记录
	ListingsVariantIDsMapping map[string]linkPcProductVariant // product_listing_variant_id -> reason
	// link 失败原因记录
	ListingsVariantIDsLinkFailedMapping map[string]string // product_listing_variant_id -> reason
}

type linkPcProductVariant struct {
	ProductCenterProductID        string
	ProductCenterProductVariantID string
	AutoLinkSuccessReason         string
}

func (r *autoLinkMappingResult) filterUsefulMappingAndReturnUnmappingRelations(relations []*ProductListingRelation) []*ProductListingRelation {
	newListingsVariantIDsMapping := make(map[string]linkPcProductVariant, len(relations))
	unmappingRelations := make([]*ProductListingRelation, 0, len(relations))
	for _, relation := range relations {
		if v, ok := r.ListingsVariantIDsMapping[relation.ProductListingVariantID]; ok {
			newListingsVariantIDsMapping[relation.ProductListingVariantID] = v
		} else {
			unmappingRelations = append(unmappingRelations, relation)
		}
	}
	r.ListingsVariantIDsMapping = newListingsVariantIDsMapping
	return unmappingRelations
}

func (r *autoLinkMappingResult) filterTargetFailed(relations []*ProductListingRelation, autoLinkFailedReason string) []*ProductListingRelation {
	result := make([]*ProductListingRelation, 0)
	for _, relation := range relations {
		if v, ok := r.ListingsVariantIDsLinkFailedMapping[relation.ProductListingVariantID]; ok && v == autoLinkFailedReason {
			result = append(result, relation)
		}
	}
	return result
}

func (r *autoLinkMappingResult) mergeResult(other *autoLinkMappingResult) {
	if other == nil {
		return
	}
	for k, v := range other.ListingsVariantIDsMapping {
		if _, ok := r.ListingsVariantIDsMapping[k]; !ok {
			r.ListingsVariantIDsMapping[k] = v
		}
		// 后面成功的 link 会覆盖前面失败的 link
		if _, ok := r.ListingsVariantIDsLinkFailedMapping[k]; ok {
			delete(r.ListingsVariantIDsLinkFailedMapping, k)
		}
	}
	for k, v := range other.ListingsVariantIDsLinkFailedMapping {
		if _, ok := r.ListingsVariantIDsLinkFailedMapping[k]; !ok {
			r.ListingsVariantIDsLinkFailedMapping[k] = v
		}
	}
}

type autoLinkResultReportArg struct {
	listing *ProductListing
	// 来源
	triggerSource string
	// SKU autoLink 成功/失败原因 product_listing_variant_id -> reason
	variantResults map[string]autoLinkVariantResult
}

type autoLinkVariantResult struct {
	// 是否成功
	success bool
	// 原因
	reason string
}

type variantLinkRecord struct {
	variantCreatedAt time.Time
	actions          []business_events_collector.SKULinkAction
}

func removeProductInActiveVariants(variants []products_center.Variant) []products_center.Variant {
	result := make([]products_center.Variant, 0)
	for i := range variants {
		if variants[i].Status != consts.ProductsCenterVariantStatusInActive {
			result = append(result, variants[i])
		}
	}
	return result
}

func removeSearchableInActiveVariants(variants []searchable_products.Variant) []searchable_products.Variant {
	result := make([]searchable_products.Variant, 0)
	for i := range variants {
		if variants[i].Status != consts.ProductsCenterVariantStatusInActive {
			result = append(result, variants[i])
		}
	}
	return result
}

type UpdateRelationsArg struct {
	SalesChannelProduct     SalesChannelProduct       `json:"sales_channel_product"`
	Options                 []models.ProductOption    `json:"options"`
	ProductListingRelations []*ProductListingRelation `json:"relations"`
}

func (arg *UpdateRelationsArg) convertToConductorUpdateArgs(listing *ProductListing) *conductorUpdateArgs {
	result := &conductorUpdateArgs{}
	arg.convertProductListingSalesChannelProduct(listing)
	arg.convertProductListingProductOptions(listing)
	result.UpdateRelations = arg.convertProductListingUpdateRelations(listing)
	listing.ModifyStateAndStatus()
	result.ProductListingDBModel = convertToProductListingDBModel(listing)

	return result
}

func (arg *UpdateRelationsArg) convertProductListingSalesChannelProduct(pl *ProductListing) {
	if arg.SalesChannelProduct.ID != "" {
		pl.SalesChannelProduct.ID = arg.SalesChannelProduct.ID
	}
}

func (arg *UpdateRelationsArg) convertProductListingProductOptions(pl *ProductListing) {
	if len(arg.Options) > 0 &&
		len(pl.Product.Options) > 0 &&
		len(pl.Product.Options[0].ValueDetails) > 0 {

		optionsMap := make(map[string]string)
		for i := range arg.Options {
			if arg.Options[i].SalesChannelOptionID == "" || len(arg.Options[i].ValueDetails) == 0 {
				continue
			}

			for j := range arg.Options[i].ValueDetails {
				if arg.Options[i].ValueDetails[j].SalesChannelValueID == "" {
					continue
				}
				key := arg.Options[i].SalesChannelOptionID + arg.Options[i].ValueDetails[j].SalesChannelValueID
				optionsMap[key] = arg.Options[i].ValueDetails[j].SalesChannelID
			}
		}

		if len(optionsMap) > 0 {
			for i := range pl.Product.Options[0].ValueDetails {
				if pl.Product.Options[0].ValueDetails[i].SalesChannelValueID == "" {
					continue
				}

				key := pl.Product.Options[0].SalesChannelOptionID + pl.Product.Options[0].ValueDetails[i].SalesChannelValueID
				if salesChannelID, ok := optionsMap[key]; ok {
					pl.Product.Options[0].ValueDetails[i].SalesChannelID = salesChannelID
					pl.Product.Options[0].ValueDetails[i].Audit.State = consts.ProductOptionValueAuditStateReviewing
					if pl.Product.Options[0].ValueDetails[i].State == consts.ProductOptionValueStatePending {
						pl.Product.Options[0].ValueDetails[i].State = consts.ProductOptionValueStateReviewing
					}
				}
			}
		}
	}
}

func (arg *UpdateRelationsArg) convertProductListingUpdateRelations(pl *ProductListing) []*relationDBModel {
	relations := make([]*relationDBModel, 0)
	if len(arg.ProductListingRelations) > 0 {
		relationsMap := make(map[string]string)
		for i := range arg.ProductListingRelations {
			if arg.ProductListingRelations[i].SalesChannelVariant.ID == "" {
				continue
			}
			relationsMap[arg.ProductListingRelations[i].ID] = arg.ProductListingRelations[i].SalesChannelVariant.ID
		}

		for i := range pl.Relations {
			if pl.Relations[i].SalesChannelVariant.ID != "" {
				continue
			}

			if salesChannelVariantID, ok := relationsMap[pl.Relations[i].ID]; ok {
				pl.Relations[i].SalesChannelVariant.ID = salesChannelVariantID
				pl.Relations[i].SalesChannelVariant.ProductID = pl.SalesChannelProduct.ID
				pl.Relations[i].SetRelationSyncStatus()
				pl.Relations[i].SetRelationLinkStatus()
				relations = append(relations, convertToRelationDBModel(
					pl.ID,
					pl.Relations[i],
					&pl.Organization,
					&pl.SalesChannel,
					&pl.Product,
				))
			}
		}
	}

	return relations
}
