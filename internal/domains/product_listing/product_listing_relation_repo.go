package product_listing

import (
	"context"

	"cloud.google.com/go/spanner"

	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/gopkg/storage/spannerx"
	"github.com/AfterShip/gopkg/storage/spannerx/sqlbuilder"
	"github.com/AfterShip/gopkg/uuid"

	spanner_util "github.com/AfterShip/pltf-pd-product-listings/internal/utils/spannerx"
)

type relationRepo struct {
	cli *spannerx.Client
}

func (r *relationRepo) listAllByProductListingID(ctx context.Context, id string) ([]*relationDBModel, error) {
	sql := sqlbuilder.Model(&relationDBModel{}).
		Where(sqlbuilder.Eq("product_listing_id", "@product_listing_id")).
		Where(sqlbuilder.IsNull("deleted_at")).
		ForceIndex(productListingIDIndex).
		OrderAsc(tableFieldCreatedAt).
		MustToSQL()
	stmt := spanner.Statement{
		SQL:    sql,
		Params: map[string]interface{}{"product_listing_id": id},
	}
	return r.doQuery(ctx, stmt)
}

func (r *relationRepo) listAllByProductListingIDs(ctx context.Context, ids []string) ([]*relationDBModel, error) {
	sql := sqlbuilder.Model(&relationDBModel{}).
		Where(sqlbuilder.InArray("product_listing_id", "@product_listing_ids")).
		Where(sqlbuilder.IsNull("deleted_at")).
		ForceIndex(productListingIDIndex).
		OrderAsc(tableFieldCreatedAt).
		MustToSQL()
	stmt := spanner.Statement{
		SQL:    sql,
		Params: map[string]interface{}{"product_listing_ids": ids},
	}
	return r.doQuery(ctx, stmt)
}

func (r *relationRepo) list(ctx context.Context, args *repoListRelationArgs) ([]*relationDBModel, error) {
	query := sqlbuilder.Model(&relationDBModel{})
	queryParams := map[string]interface{}{}

	query, queryParams = r.buildListQuery(args, query, queryParams)

	if index := fetchRelationIndex(args); index != "" {
		query = query.ForceIndex(index)
	}

	sql, err := query.Limit(args.Limit).
		Offset((args.Page - 1) * args.Limit).
		OrderAsc(tableFieldCreatedAt).
		ToSQL()

	if err != nil {
		return nil, err
	}
	statement := spanner.Statement{
		SQL:    sql,
		Params: queryParams,
	}

	return r.doQuery(ctx, statement)
}

// func (r *relationRepo) count(ctx context.Context, args *repoListRelationArgs) (int64, error) {
//	query := sqlbuilder.Select("COUNT(*) AS count").From(tableRelation)
//	queryParams := map[string]interface{}{}
//	query, queryParams = r.buildListQuery(args, query, queryParams)
//	sql, err := query.ToSQL()
//	if err != nil {
//		return 0, err
//	}
//
//	statement := spanner.Statement{
//		SQL:    sql,
//		Params: queryParams,
//	}
//	txn := r.cli.Single()
//	defer txn.Close()
//
//	ret := struct {
//		Count int64 `spanner:"count"`
//	}{}
//	err = txn.Query(ctx, statement).Do(func(row *spanner.Row) error {
//		return row.ToStruct(&ret)
//	})
//
//	return ret.Count, err
// }

func (r *relationRepo) buildListQuery(
	args *repoListRelationArgs,
	query *sqlbuilder.SelectBuilder,
	queryParams map[string]interface{}) (*sqlbuilder.SelectBuilder, map[string]interface{}) {
	if args.OrganizationID != "" {
		query = query.Where(sqlbuilder.Eq("organization_id", "@organization_id"))
		queryParams["organization_id"] = args.OrganizationID
	}
	if args.SalesChannelStoreKey != "" {
		query = query.Where(sqlbuilder.Eq("sales_channel_store_key", "@sales_channel_store_key"))
		queryParams["sales_channel_store_key"] = args.SalesChannelStoreKey
	}
	if args.SalesChannelPlatform != "" {
		query = query.Where(sqlbuilder.Eq("sales_channel_platform", "@sales_channel_platform"))
		queryParams["sales_channel_platform"] = args.SalesChannelPlatform
	}
	if args.SourceStoreKey != "" {
		query = query.Where(sqlbuilder.Eq("products_center_source_store_key", "@source_store_key"))
		queryParams["source_store_key"] = args.SourceStoreKey
	}
	if args.SourcePlatform != "" {
		query = query.Where(sqlbuilder.Eq("products_center_source_platform", "@source_platform"))
		queryParams["source_platform"] = args.SourcePlatform
	}
	if args.SalesChannelProductID != "" {
		query = query.Where(sqlbuilder.Eq("sales_channel_product_id", "@sales_channel_product_id"))
		queryParams["sales_channel_product_id"] = args.SalesChannelProductID
	}
	if args.SalesChannelProductIDs != nil && len(args.SalesChannelProductIDs) > 0 {
		query = query.Where(sqlbuilder.InArray("sales_channel_product_id", "@sales_channel_product_ids"))
		queryParams["sales_channel_product_ids"] = args.SalesChannelProductIDs
	}
	if args.SalesChannelVariantID != "" {
		query = query.Where(sqlbuilder.Eq("sales_channel_variant_id", "@sales_channel_variant_id"))
		queryParams["sales_channel_variant_id"] = args.SalesChannelVariantID
	}
	if args.SalesChannelVariantIDs != nil && len(args.SalesChannelVariantIDs) > 0 {
		query = query.Where(sqlbuilder.InArray("sales_channel_variant_id", "@sales_channel_variant_ids"))
		queryParams["sales_channel_variant_ids"] = args.SalesChannelVariantIDs
	}
	if args.ProductsCenterProductID != "" {
		query = query.Where(sqlbuilder.Eq("products_center_product_id", "@products_center_product_id"))
		queryParams["products_center_product_id"] = args.ProductsCenterProductID
	}
	if args.ProductsCenterVariantID != "" {
		query = query.Where(sqlbuilder.Eq("products_center_variant_id", "@products_center_variant_id"))
		queryParams["products_center_variant_id"] = args.ProductsCenterVariantID
	}
	if !args.IncludeDeleted {
		query = query.Where(sqlbuilder.IsNull("deleted_at"))
	}
	if args.LinkStatus != nil && len(args.LinkStatus) > 0 {
		query = query.Where(sqlbuilder.InArray("link_status", "@link_status"))
		queryParams["link_status"] = args.LinkStatus
	}
	if args.SyncStatus != nil && len(args.SyncStatus) > 0 {
		query = query.Where(sqlbuilder.InArray("sync_status", "@sync_status"))
		queryParams["sync_status"] = args.SyncStatus
	}
	if args.ProductsCenterConnectorProductIDs != nil && len(args.ProductsCenterConnectorProductIDs) > 0 {
		query = query.Where(sqlbuilder.InArray("products_center_connector_product_id", "@products_center_connector_product_ids"))
		queryParams["products_center_connector_product_ids"] = args.ProductsCenterConnectorProductIDs
	}

	return query, queryParams
}

func (r *relationRepo) doQuery(ctx context.Context, statement spanner.Statement) ([]*relationDBModel, error) {
	txn := r.cli.Single()
	defer txn.Close()

	relations := make([]*relationDBModel, 0)
	err := txn.Query(ctx, statement).Do(func(r *spanner.Row) error {
		model := relationDBModel{}
		defer func() { relations = append(relations, &model) }()
		return r.ToStruct(&model)
	})

	return relations, err
}

func (r *relationRepo) getByID(ctx context.Context, id string) (relationDBModel, error) {
	relation := relationDBModel{}
	cols, err := spanner_util.ParseColumns(&relation)
	if err != nil {
		return relation, err
	}

	txn := r.cli.Single()
	defer txn.Close()

	row, err := txn.ReadRow(ctx, relation.SpannerTable(), spanner.Key{id}, cols)
	if err != nil {
		return relation, err
	}

	return relation, row.ToStruct(&relation)
}

func (r *relationRepo) generateCreateMutation(model *relationDBModel) (*spanner.Mutation, error) {
	if model.ProductListingRelationID == "" {
		model.ProductListingRelationID = uuid.GenerateUUIDV4()
	}
	model.DeletedAt = types.NullDatetime
	model.CreatedAt = spanner.CommitTimestamp
	model.UpdatedAt = spanner.CommitTimestamp
	return spanner.InsertStruct(tableRelation, model)
}

func (r *relationRepo) generateUpdateMutation(model *relationDBModel) (*spanner.Mutation, error) {
	model.UpdatedAt = spanner.CommitTimestamp
	return spannerx.UpdateStruct(tableRelation, model)
}

func (r *relationRepo) generateDeleteMutation(id string) *spanner.Mutation {
	data := map[string]interface{}{
		tableFieldRelationID: id,
		"deleted_at":         types.MakeDatetime(spanner.CommitTimestamp),
		"updated_at":         types.MakeDatetime(spanner.CommitTimestamp),
	}
	return spanner.UpdateMap(tableRelation, data)
}

func (r *relationRepo) generateForceDeleteMutation(id string) *spanner.Mutation {
	return spanner.Delete(tableRelation, spanner.Key{id})
}

func fetchRelationIndex(arg *repoListRelationArgs) string {
	if arg.SalesChannelProductID != "" ||
		arg.SalesChannelVariantID != "" ||
		(arg.SalesChannelProductIDs != nil && len(arg.SalesChannelProductIDs) > 0) ||
		(arg.SalesChannelVariantIDs != nil && len(arg.SalesChannelVariantIDs) > 0) {
		return salesChannelProductVariantIDIndex
	}
	if arg.ProductsCenterProductID != "" {
		return productsCenterProductVariantIDIndex
	}

	if arg.ProductsCenterConnectorProductIDs != nil && len(arg.ProductsCenterConnectorProductIDs) > 0 {
		return productsCenterConnectorProductIDIndex
	}

	return ""
}
