package product_listing

import (
	"reflect"
	"testing"

	"github.com/stretchr/testify/require"

	"github.com/AfterShip/pltf-pd-product-listings/internal/consts"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/settings"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
)

func TestNewCompareModel(t *testing.T) {
	// Initialize test data
	listing := &ProductListing{
		ID: "1",
		Product: models.Product{
			Title: "title",
		},
	}
	lastListing := &ProductListing{
		ID: "1",
		Product: models.Product{
			Title: "title",
		},
	}
	// Call newCompareModel
	model := newCompareModel(listing, lastListing, compareSetting{}, nil)

	// Verify the result
	if !reflect.DeepEqual(model.listing, listing) {
		t.Errorf("Expected listing to be %v, got %v", listing, model.listing)
	}
	if !reflect.DeepEqual(model.lastListing, lastListing) {
		t.Errorf("Expected lastListing to be %v, got %v", lastListing, model.lastListing)
	}
}

func TestCompareModel_needPublishPriceAndInventory(t *testing.T) {
	tests := []struct {
		name     string
		setup    func() *compareModel
		expected bool
	}{
		{
			name: "SyncStatus changes from Unsync to Synced",
			setup: func() *compareModel {
				return &compareModel{
					listing: &ProductListing{
						Relations: []*ProductListingRelation{
							{ID: "1", SyncStatus: consts.SyncStatusSynced},
						},
					},
					lastListing: &ProductListing{
						Relations: []*ProductListingRelation{
							{ID: "1", SyncStatus: consts.SyncStatusUnsync},
						},
					},
				}
			},
			expected: true,
		},
		{
			name: "SyncStatus remains Synced",
			setup: func() *compareModel {
				return &compareModel{
					listing: &ProductListing{
						Relations: []*ProductListingRelation{
							{ID: "1", SyncStatus: consts.SyncStatusSynced},
						},
					},
					lastListing: &ProductListing{
						Relations: []*ProductListingRelation{
							{ID: "1", SyncStatus: consts.SyncStatusSynced},
						},
					},
				}
			},
			expected: false,
		},
		{
			name: "SyncStatus remains Unsync",
			setup: func() *compareModel {
				return &compareModel{
					listing: &ProductListing{
						Relations: []*ProductListingRelation{
							{ID: "1", SyncStatus: consts.SyncStatusUnsync},
						},
					},
					lastListing: &ProductListing{
						Relations: []*ProductListingRelation{
							{ID: "1", SyncStatus: consts.SyncStatusUnsync},
						},
					},
				}
			},
			expected: false,
		},
		{
			name: "No Relations",
			setup: func() *compareModel {
				return &compareModel{
					listing:     &ProductListing{},
					lastListing: &ProductListing{},
				}
			},
			expected: false,
		},
		{
			name: "Shein option reviewing to succeeded",
			setup: func() *compareModel {
				return &compareModel{
					listing: &ProductListing{
						SalesChannel: models.SalesChannel{Platform: consts.Shein},
						Product: models.Product{
							Options: []*models.ProductOption{
								{
									Name:     "Option1",
									Position: 1,
									ValueDetails: []models.ProductOptionValueDetail{
										{
											Value:          "Value1",
											SalesChannelID: "1",
											Audit: models.ProductOptionValueDetailAudit{
												State: consts.ProductOptionValueAuditStateSucceeded,
											},
										},
									},
								},
							},
						},
					},
					lastListing: &ProductListing{
						SalesChannel: models.SalesChannel{Platform: consts.Shein},
						Product: models.Product{
							Options: []*models.ProductOption{
								{
									Name:     "Option1",
									Position: 1,
									ValueDetails: []models.ProductOptionValueDetail{
										{
											Value:          "Value1",
											SalesChannelID: "1",
											Audit: models.ProductOptionValueDetailAudit{
												State: consts.ProductOptionValueAuditStateReviewing,
											},
										},
									},
								},
							},
						},
					},
				}
			},
			expected: true,
		},
		{
			name: "Shein option reviewing to failed",
			setup: func() *compareModel {
				return &compareModel{
					listing: &ProductListing{
						SalesChannel: models.SalesChannel{Platform: consts.Shein},
						Product: models.Product{
							Options: []*models.ProductOption{
								{
									Name:     "Option1",
									Position: 1,
									ValueDetails: []models.ProductOptionValueDetail{
										{
											Value:          "Value1",
											SalesChannelID: "1",
											Audit: models.ProductOptionValueDetailAudit{
												State: consts.ProductOptionValueAuditStateFailed,
											},
										},
									},
								},
							},
						},
					},
					lastListing: &ProductListing{
						SalesChannel: models.SalesChannel{Platform: consts.Shein},
						Product: models.Product{
							Options: []*models.ProductOption{
								{
									Name:     "Option1",
									Position: 1,
									ValueDetails: []models.ProductOptionValueDetail{
										{
											Value:          "Value1",
											SalesChannelID: "1",
											Audit: models.ProductOptionValueDetailAudit{
												State: consts.ProductOptionValueAuditStateReviewing,
											},
										},
									},
								},
							},
						},
					},
				}
			},
			expected: false,
		},
		{
			name: "Shein option failed to succeeded",
			setup: func() *compareModel {
				return &compareModel{
					listing: &ProductListing{
						SalesChannel: models.SalesChannel{Platform: consts.Shein},
						Product: models.Product{
							Options: []*models.ProductOption{
								{
									Name:     "Option1",
									Position: 1,
									ValueDetails: []models.ProductOptionValueDetail{
										{
											Value:          "Value1",
											SalesChannelID: "1",
											Audit: models.ProductOptionValueDetailAudit{
												State: consts.ProductOptionValueAuditStateSucceeded,
											},
										},
									},
								},
							},
						},
					},
					lastListing: &ProductListing{
						SalesChannel: models.SalesChannel{Platform: consts.Shein},
						Product: models.Product{
							Options: []*models.ProductOption{
								{
									Name:     "Option1",
									Position: 1,
									ValueDetails: []models.ProductOptionValueDetail{
										{
											Value:          "Value1",
											SalesChannelID: "1",
											Audit: models.ProductOptionValueDetailAudit{
												State: consts.ProductOptionValueAuditStateFailed,
											},
										},
									},
								},
							},
						},
					},
				}
			},
			expected: true,
		},
		{
			name: "Shein option reviewing no change",
			setup: func() *compareModel {
				return &compareModel{
					listing: &ProductListing{
						SalesChannel: models.SalesChannel{Platform: consts.Shein},
						Product: models.Product{
							Options: []*models.ProductOption{
								{
									Name:     "Option1",
									Position: 1,
									ValueDetails: []models.ProductOptionValueDetail{
										{
											Value:          "Value1",
											SalesChannelID: "1",
											Audit: models.ProductOptionValueDetailAudit{
												State: consts.ProductOptionValueAuditStateSucceeded,
											},
										},
									},
								},
							},
						},
					},
					lastListing: &ProductListing{
						SalesChannel: models.SalesChannel{Platform: consts.Shein},
						Product: models.Product{
							Options: []*models.ProductOption{
								{
									Name:     "Option1",
									Position: 1,
									ValueDetails: []models.ProductOptionValueDetail{
										{
											Value:          "Value1",
											SalesChannelID: "1",
											Audit: models.ProductOptionValueDetailAudit{
												State: consts.ProductOptionValueAuditStateSucceeded,
											},
										},
									},
								},
							},
						},
					},
				}
			},
			expected: false,
		},
		{
			name: "Shein new option",
			setup: func() *compareModel {
				return &compareModel{
					listing: &ProductListing{
						SalesChannel: models.SalesChannel{Platform: consts.Shein},
						Product: models.Product{
							Options: []*models.ProductOption{
								{
									Name:     "Option1",
									Position: 1,
									ValueDetails: []models.ProductOptionValueDetail{
										{
											Value:          "Value1",
											SalesChannelID: "1",
											Audit: models.ProductOptionValueDetailAudit{
												State: consts.ProductOptionValueAuditStateSucceeded,
											},
										},
										{
											Value:          "Value2",
											SalesChannelID: "2",
											Audit: models.ProductOptionValueDetailAudit{
												State: consts.ProductOptionValueAuditStateSucceeded,
											},
										},
									},
								},
							},
						},
					},
					lastListing: &ProductListing{
						SalesChannel: models.SalesChannel{Platform: consts.Shein},
						Product: models.Product{
							Options: []*models.ProductOption{
								{
									Name:     "Option1",
									Position: 1,
									ValueDetails: []models.ProductOptionValueDetail{
										{
											Value:          "Value1",
											SalesChannelID: "1",
											Audit: models.ProductOptionValueDetailAudit{
												State: consts.ProductOptionValueAuditStateSucceeded,
											},
										},
									},
								},
							},
						},
					},
				}
			},
			expected: true,
		},
		{
			name: "Shein new option not succeeded",
			setup: func() *compareModel {
				return &compareModel{
					listing: &ProductListing{
						SalesChannel: models.SalesChannel{Platform: consts.Shein},
						Product: models.Product{
							Options: []*models.ProductOption{
								{
									Name:     "Option1",
									Position: 1,
									ValueDetails: []models.ProductOptionValueDetail{
										{
											Value:          "Value1",
											SalesChannelID: "1",
											Audit: models.ProductOptionValueDetailAudit{
												State: consts.ProductOptionValueAuditStateSucceeded,
											},
										},
										{
											Value:          "Value2",
											SalesChannelID: "2",
											Audit: models.ProductOptionValueDetailAudit{
												State: consts.ProductOptionValueAuditStateFailed,
											},
										},
									},
								},
							},
						},
					},
					lastListing: &ProductListing{
						SalesChannel: models.SalesChannel{Platform: consts.Shein},
						Product: models.Product{
							Options: []*models.ProductOption{
								{
									Name:     "Option1",
									Position: 1,
									ValueDetails: []models.ProductOptionValueDetail{
										{
											Value:          "Value1",
											SalesChannelID: "1",
											Audit: models.ProductOptionValueDetailAudit{
												State: consts.ProductOptionValueAuditStateSucceeded,
											},
										},
									},
								},
							},
						},
					},
				}
			},
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			model := tt.setup()
			result := model.needPublishPriceAndInventory()
			require.Equal(t, tt.expected, result)
		})
	}
}

func TestCompareModel_isTitleModify(t *testing.T) {
	tests := []struct {
		name        string
		listing     ProductListing
		lastListing ProductListing
		expected    bool
	}{
		{
			name: "Titles are different",
			listing: ProductListing{
				Product: models.Product{
					Title: "New Title",
				},
			},
			lastListing: ProductListing{
				Product: models.Product{
					Title: "Old Title",
				},
			},
			expected: true,
		},
		{
			name: "Titles are the same",
			listing: ProductListing{
				Product: models.Product{
					Title: "Same Title",
				},
			},
			lastListing: ProductListing{
				Product: models.Product{
					Title: "Same Title",
				},
			},
			expected: false,
		},
		{
			name: "Titles are the same with trim",
			listing: ProductListing{
				Product: models.Product{
					Title: " Same Title ",
				},
			},
			lastListing: ProductListing{
				Product: models.Product{
					Title: "Same Title",
				},
			},
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			model := newCompareModel(&tt.listing, &tt.lastListing, compareSetting{}, nil)
			result := model.isTitleModify()
			require.Equal(t, tt.expected, result)
		})
	}
}

func TestCompareModel_isDescriptionModify(t *testing.T) {
	tests := []struct {
		name        string
		listing     ProductListing
		lastListing ProductListing
		expected    bool
	}{
		{
			name: "Descriptions are different",
			listing: ProductListing{
				Product: models.Product{
					Description: "New Description",
				},
			},
			lastListing: ProductListing{
				Product: models.Product{
					Description: "Old Description",
				},
			},
			expected: true,
		},
		{
			name: "Descriptions are the same",
			listing: ProductListing{
				Product: models.Product{
					Description: "Same Description",
				},
			},
			lastListing: ProductListing{
				Product: models.Product{
					Description: "Same Description",
				},
			},
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			model := newCompareModel(&tt.listing, &tt.lastListing, compareSetting{}, nil)
			result := model.isDescriptionModify()
			require.Equal(t, tt.expected, result)
		})
	}
}

func TestCompareModel_isMediaModify(t *testing.T) {
	tests := []struct {
		name        string
		listing     ProductListing
		lastListing ProductListing
		expected    bool
	}{
		{
			name: "Media count differs",
			listing: ProductListing{
				Product: models.Product{
					Media: []*models.ProductMedia{{SalesChannelID: "1"}},
				},
			},
			lastListing: ProductListing{
				Product: models.Product{
					Media: []*models.ProductMedia{{SalesChannelID: "1"}, {SalesChannelID: "2"}},
				},
			},
			expected: true,
		},
		{
			name: "Media SalesChannelID differs",
			listing: ProductListing{
				Product: models.Product{
					Media: []*models.ProductMedia{{SalesChannelID: "1"}},
				},
			},
			lastListing: ProductListing{
				Product: models.Product{
					Media: []*models.ProductMedia{{SalesChannelID: "2"}},
				},
			},
			expected: true,
		},
		{
			name: "Media configurations are the same",
			listing: ProductListing{
				Product: models.Product{
					Media: []*models.ProductMedia{{SalesChannelID: "1"}},
				},
			},
			lastListing: ProductListing{
				Product: models.Product{
					Media: []*models.ProductMedia{{SalesChannelID: "1"}},
				},
			},
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			model := newCompareModel(&tt.listing, &tt.lastListing, compareSetting{}, nil)
			result := model.isMediaModify()
			require.Equal(t, tt.expected, result)
		})
	}
}

func TestCompareModel_isCategoryModify(t *testing.T) {
	tests := []struct {
		name        string
		listing     ProductListing
		lastListing ProductListing
		expected    bool
	}{
		{
			name: "Category count differs",
			listing: ProductListing{
				Product: models.Product{
					Categories: []*models.SalesChannelResource{{SalesChannelID: "1"}},
				},
			},
			lastListing: ProductListing{
				Product: models.Product{
					Categories: []*models.SalesChannelResource{{SalesChannelID: "1"}, {SalesChannelID: "2"}},
				},
			},
			expected: true,
		},
		{
			name: "Category SalesChannelID differs",
			listing: ProductListing{
				Product: models.Product{
					Categories: []*models.SalesChannelResource{{SalesChannelID: "1"}},
				},
			},
			lastListing: ProductListing{
				Product: models.Product{
					Categories: []*models.SalesChannelResource{{SalesChannelID: "2"}},
				},
			},
			expected: true,
		},
		{
			name: "Category configurations are the same",
			listing: ProductListing{
				Product: models.Product{
					Categories: []*models.SalesChannelResource{{SalesChannelID: "1"}},
				},
			},
			lastListing: ProductListing{
				Product: models.Product{
					Categories: []*models.SalesChannelResource{{SalesChannelID: "1"}},
				},
			},
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			model := newCompareModel(&tt.listing, &tt.lastListing, compareSetting{}, nil)
			result := model.isCategoryModify()
			require.Equal(t, tt.expected, result)
		})
	}
}

func TestCompareModel_isBrandModify(t *testing.T) {
	tests := []struct {
		name        string
		listing     ProductListing
		lastListing ProductListing
		expected    bool
	}{
		{
			name: "Brand SalesChannelID differs",
			listing: ProductListing{
				Product: models.Product{
					Brand: models.SalesChannelResource{SalesChannelID: "1"},
				},
			},
			lastListing: ProductListing{
				Product: models.Product{
					Brand: models.SalesChannelResource{SalesChannelID: "2"},
				},
			},
			expected: true,
		},
		{
			name: "Brand SalesChannelID is the same",
			listing: ProductListing{
				Product: models.Product{
					Brand: models.SalesChannelResource{SalesChannelID: "1"},
				},
			},
			lastListing: ProductListing{
				Product: models.Product{
					Brand: models.SalesChannelResource{SalesChannelID: "1"},
				},
			},
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			model := newCompareModel(&tt.listing, &tt.lastListing, compareSetting{}, nil)
			result := model.isBrandModify()
			require.Equal(t, tt.expected, result)
		})
	}
}

func TestCompareModel_isAttributesModify(t *testing.T) {
	tests := []struct {
		name        string
		listing     ProductListing
		lastListing ProductListing
		expected    bool
	}{
		{
			name: "Attribute count differs",
			listing: ProductListing{
				Product: models.Product{
					Attributes: []*models.ProductAttribute{
						{SalesChannelID: "1", Name: "Color", Values: []models.SalesChannelResource{{Name: "Red"}}},
					},
				},
			},
			lastListing: ProductListing{
				Product: models.Product{
					Attributes: []*models.ProductAttribute{},
				},
			},
			expected: true,
		},
		{
			name: "Attribute SalesChannelID differs",
			listing: ProductListing{
				Product: models.Product{
					Attributes: []*models.ProductAttribute{
						{SalesChannelID: "1", Name: "Color", Values: []models.SalesChannelResource{{Name: "Red"}}},
					},
				},
			},
			lastListing: ProductListing{
				Product: models.Product{
					Attributes: []*models.ProductAttribute{
						{SalesChannelID: "2", Name: "Color", Values: []models.SalesChannelResource{{Name: "Red"}}},
					},
				},
			},
			expected: true,
		},
		{
			name: "Attribute name differs",
			listing: ProductListing{
				Product: models.Product{
					Attributes: []*models.ProductAttribute{
						{SalesChannelID: "1", Name: "Color", Values: []models.SalesChannelResource{{Name: "Red"}}},
					},
				},
			},
			lastListing: ProductListing{
				Product: models.Product{
					Attributes: []*models.ProductAttribute{
						{SalesChannelID: "1", Name: "Size", Values: []models.SalesChannelResource{{Name: "Red"}}},
					},
				},
			},
			expected: false,
		},
		{
			name: "Attribute values differ",
			listing: ProductListing{
				Product: models.Product{
					Attributes: []*models.ProductAttribute{
						{SalesChannelID: "1", Name: "Color", Values: []models.SalesChannelResource{{Name: "Blue"}}},
					},
				},
			},
			lastListing: ProductListing{
				Product: models.Product{
					Attributes: []*models.ProductAttribute{
						{SalesChannelID: "1", Name: "Color", Values: []models.SalesChannelResource{{Name: "Red"}}},
					},
				},
			},
			expected: true,
		},
		{
			name: "Attributes configurations are the same",
			listing: ProductListing{
				Product: models.Product{
					Attributes: []*models.ProductAttribute{
						{SalesChannelID: "1", Name: "Color", Values: []models.SalesChannelResource{{Name: "Red"}}},
					},
				},
			},
			lastListing: ProductListing{
				Product: models.Product{
					Attributes: []*models.ProductAttribute{
						{SalesChannelID: "1", Name: "Color", Values: []models.SalesChannelResource{{Name: "Red"}}},
					},
				},
			},
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			model := newCompareModel(&tt.listing, &tt.lastListing, compareSetting{}, nil)
			result := model.isAttributesModify()
			require.Equal(t, tt.expected, result)
		})
	}
}

func TestCompareModel_isSizeChartModify(t *testing.T) {
	tests := []struct {
		name        string
		listing     ProductListing
		lastListing ProductListing
		expected    bool
	}{
		{
			name: "Size chart image count differs",
			listing: ProductListing{
				Product: models.Product{
					SizeChart: models.ProductSizeChart{
						Images: []models.SalesChannelFile{{SalesChannelID: "1"}},
					},
				},
			},
			lastListing: ProductListing{
				Product: models.Product{
					SizeChart: models.ProductSizeChart{
						Images: []models.SalesChannelFile{{SalesChannelID: "1"}, {SalesChannelID: "2"}},
					},
				},
			},
			expected: true,
		},
		{
			name: "Size chart image SalesChannelID differs",
			listing: ProductListing{
				Product: models.Product{
					SizeChart: models.ProductSizeChart{
						Images: []models.SalesChannelFile{{SalesChannelID: "1"}},
					},
				},
			},
			lastListing: ProductListing{
				Product: models.Product{
					SizeChart: models.ProductSizeChart{
						Images: []models.SalesChannelFile{{SalesChannelID: "2"}},
					},
				},
			},
			expected: true,
		},
		{
			name: "Size chart images are the same",
			listing: ProductListing{
				Product: models.Product{
					SizeChart: models.ProductSizeChart{
						Images: []models.SalesChannelFile{{SalesChannelID: "1"}},
					},
				},
			},
			lastListing: ProductListing{
				Product: models.Product{
					SizeChart: models.ProductSizeChart{
						Images: []models.SalesChannelFile{{SalesChannelID: "1"}},
					},
				},
			},
			expected: false,
		},
		{
			name: "Size chart attributes count diff",
			listing: ProductListing{
				Product: models.Product{
					SizeChart: models.ProductSizeChart{
						Images:     []models.SalesChannelFile{{SalesChannelID: "1"}},
						Attributes: []models.ProductSizeChartAttribute{{SalesChannelID: "1"}},
					},
				},
			},
			lastListing: ProductListing{
				Product: models.Product{
					SizeChart: models.ProductSizeChart{
						Images:     []models.SalesChannelFile{{SalesChannelID: "1"}},
						Attributes: []models.ProductSizeChartAttribute{{SalesChannelID: "1"}, {SalesChannelID: "2"}},
					},
				},
			},
			expected: true,
		},
		{
			name: "Size chart attributes SalesChannelID diff",
			listing: ProductListing{
				Product: models.Product{
					SizeChart: models.ProductSizeChart{
						Images:     []models.SalesChannelFile{{SalesChannelID: "1"}},
						Attributes: []models.ProductSizeChartAttribute{{SalesChannelID: "1"}},
					},
				},
			},
			lastListing: ProductListing{
				Product: models.Product{
					SizeChart: models.ProductSizeChart{
						Images:     []models.SalesChannelFile{{SalesChannelID: "1"}},
						Attributes: []models.ProductSizeChartAttribute{{SalesChannelID: "2"}},
					},
				},
			},
			expected: true,
		},
		{
			name: "Size chart attributes are the same",
			listing: ProductListing{
				Product: models.Product{
					SizeChart: models.ProductSizeChart{
						Images:     []models.SalesChannelFile{{SalesChannelID: "1"}},
						Attributes: []models.ProductSizeChartAttribute{{SalesChannelID: "1"}},
					},
				},
			},
			lastListing: ProductListing{
				Product: models.Product{
					SizeChart: models.ProductSizeChart{
						Images:     []models.SalesChannelFile{{SalesChannelID: "1"}},
						Attributes: []models.ProductSizeChartAttribute{{SalesChannelID: "1"}},
					},
				},
			},
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			model := newCompareModel(&tt.listing, &tt.lastListing, compareSetting{}, nil)
			result := model.isSizeChartModify()
			require.Equal(t, tt.expected, result)
		})
	}
}

func Test_isProductOptionModify(t *testing.T) {
	tests := []struct {
		name        string
		listing     *ProductListing
		lastListing *ProductListing
		expected    bool
	}{
		{
			name: "is not shein",
			listing: &ProductListing{
				SalesChannel: models.SalesChannel{
					Platform: consts.TikTokShop,
				},
				Product: models.Product{
					Options: []*models.ProductOption{},
				},
			},
			lastListing: &ProductListing{
				SalesChannel: models.SalesChannel{
					Platform: consts.Shein,
				},
				Product: models.Product{
					Options: []*models.ProductOption{
						{Name: "Option1", Position: 1},
					},
				},
			},
			expected: false,
		},
		{
			name: "options have different lengths",
			listing: &ProductListing{
				SalesChannel: models.SalesChannel{
					Platform: consts.Shein,
				},
				Product: models.Product{
					Options: []*models.ProductOption{
						{Name: "Option1", Position: 1},
					},
				},
			},
			lastListing: &ProductListing{
				SalesChannel: models.SalesChannel{
					Platform: consts.Shein,
				},
				Product: models.Product{
					Options: []*models.ProductOption{
						{Name: "Option1", Position: 1},
						{Name: "Option2", Position: 2},
					},
				},
			},
			expected: true,
		},
		{
			name: "no options",
			listing: &ProductListing{
				SalesChannel: models.SalesChannel{
					Platform: consts.Shein,
				},
				Product: models.Product{
					Options: []*models.ProductOption{},
				},
			},
			lastListing: &ProductListing{
				SalesChannel: models.SalesChannel{
					Platform: consts.Shein,
				},
				Product: models.Product{
					Options: []*models.ProductOption{},
				},
			},
			expected: false,
		},
		{
			name: "options are different",
			listing: &ProductListing{
				SalesChannel: models.SalesChannel{
					Platform: consts.Shein,
				},
				Product: models.Product{
					Options: []*models.ProductOption{
						{Name: "Option1", Position: 1},
					},
				},
			},
			lastListing: &ProductListing{
				SalesChannel: models.SalesChannel{
					Platform: consts.Shein,
				},
				Product: models.Product{
					Options: []*models.ProductOption{
						{Name: "Option2", Position: 1},
					},
				},
			},
			expected: true,
		},
		{
			name: "option values have different lengths",
			listing: &ProductListing{
				SalesChannel: models.SalesChannel{
					Platform: consts.Shein,
				},
				Product: models.Product{
					Options: []*models.ProductOption{
						{Name: "Option1", Position: 1},
					},
				},
			},
			lastListing: &ProductListing{
				SalesChannel: models.SalesChannel{
					Platform: consts.Shein,
				},
				Product: models.Product{
					Options: []*models.ProductOption{
						{
							Name:         "Option1",
							Position:     1,
							ValueDetails: []models.ProductOptionValueDetail{{Value: "Value1"}},
						},
					},
				},
			},
			expected: true,
		},
		{
			name: "no option values",
			listing: &ProductListing{
				SalesChannel: models.SalesChannel{
					Platform: consts.Shein,
				},
				Product: models.Product{
					Options: []*models.ProductOption{
						{Name: "Option1", Position: 1},
					},
				},
			},
			lastListing: &ProductListing{
				SalesChannel: models.SalesChannel{
					Platform: consts.Shein,
				},
				Product: models.Product{
					Options: []*models.ProductOption{
						{
							Name:     "Option1",
							Position: 1,
						},
					},
				},
			},
			expected: false,
		},
		{
			name: "have different option values",
			listing: &ProductListing{
				SalesChannel: models.SalesChannel{
					Platform: consts.Shein,
				},
				Product: models.Product{
					Options: []*models.ProductOption{
						{
							Name:     "Option1",
							Position: 1,
							ValueDetails: []models.ProductOptionValueDetail{
								{Value: "Value1"},
							},
						},
					},
				},
			},
			lastListing: &ProductListing{
				SalesChannel: models.SalesChannel{
					Platform: consts.Shein,
				},
				Product: models.Product{
					Options: []*models.ProductOption{
						{
							Name:     "Option1",
							Position: 1,
							ValueDetails: []models.ProductOptionValueDetail{
								{Value: "Value2"},
							},
						},
					},
				},
			},
			expected: true,
		},
		{
			name: "have different option media",
			listing: &ProductListing{
				SalesChannel: models.SalesChannel{
					Platform: consts.Shein,
				},
				Product: models.Product{
					Options: []*models.ProductOption{
						{
							Name:     "Option1",
							Position: 1,
							ValueDetails: []models.ProductOptionValueDetail{
								{
									Value: "Value1",
									Media: []models.ProductMedia{
										{
											URL:               "http://example.com",
											ExternalImageType: consts.ExternalImageTypeSquare,
										},
									},
								},
							},
						},
					},
				},
			},
			lastListing: &ProductListing{
				SalesChannel: models.SalesChannel{
					Platform: consts.Shein,
				},
				Product: models.Product{
					Options: []*models.ProductOption{
						{
							Name:     "Option1",
							Position: 1,
							ValueDetails: []models.ProductOptionValueDetail{
								{
									Value: "Value1",
									Media: []models.ProductMedia{
										{
											URL:               "http://example.com",
											ExternalImageType: consts.ExternalImageTypeMain,
										},
									},
								},
							},
						},
					},
				},
			},
			expected: true,
		},
		{
			name: "options are the same",
			listing: &ProductListing{
				SalesChannel: models.SalesChannel{
					Platform: consts.Shein,
				},
				Product: models.Product{
					Options: []*models.ProductOption{
						{
							Name:     "Option1",
							Position: 1,
							ValueDetails: []models.ProductOptionValueDetail{
								{
									Value: "Value1",
									Media: []models.ProductMedia{
										{
											URL:               "http://example.com",
											ExternalImageType: consts.ExternalImageTypeMain,
										},
									},
								},
							},
						},
					},
				},
			},
			lastListing: &ProductListing{
				SalesChannel: models.SalesChannel{
					Platform: consts.Shein,
				},
				Product: models.Product{
					Options: []*models.ProductOption{
						{
							Name:     "Option1",
							Position: 1,
							ValueDetails: []models.ProductOptionValueDetail{
								{
									Value: "Value1",
									Media: []models.ProductMedia{
										{
											URL:               "http://example.com",
											ExternalImageType: consts.ExternalImageTypeMain,
										},
									},
								},
							},
						},
					},
				},
			},
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			model := &compareModel{
				listing:     tt.listing,
				lastListing: tt.lastListing,
			}
			result := model.isProductOptionModify()
			require.Equal(t, tt.expected, result)
		})
	}
}

func TestCompareModel_isCertificationModify(t *testing.T) {
	tests := []struct {
		name        string
		listing     ProductListing
		lastListing ProductListing
		expected    bool
	}{
		{
			name: "Certification is same",
			listing: ProductListing{
				Product: models.Product{
					Certifications: []*models.ProductCertification{
						{
							SalesChannelID: "1",
							Images:         []models.SalesChannelFile{},
						},
					},
				},
			},
			lastListing: ProductListing{
				Product: models.Product{
					Certifications: []*models.ProductCertification{
						{
							SalesChannelID: "1",
							Images:         []models.SalesChannelFile{},
						},
					},
				},
			},
		},
		{
			name: "Certification image count differs",
			listing: ProductListing{
				Product: models.Product{
					Certifications: []*models.ProductCertification{
						{
							SalesChannelID: "1",
							Images:         []models.SalesChannelFile{{SalesChannelID: "1"}},
						},
					},
				},
			},
			lastListing: ProductListing{
				Product: models.Product{
					Certifications: []*models.ProductCertification{
						{
							SalesChannelID: "1",
							Images:         []models.SalesChannelFile{{SalesChannelID: "1"}, {SalesChannelID: "2"}},
						},
					},
				},
			},
			expected: true,
		},
		{
			name: "Certification file SalesChannelID differs",
			listing: ProductListing{
				Product: models.Product{
					Certifications: []*models.ProductCertification{
						{
							SalesChannelID: "1",
							Files:          []models.SalesChannelFile{{SalesChannelID: "1"}},
						},
					},
				},
			},
			lastListing: ProductListing{
				Product: models.Product{
					Certifications: []*models.ProductCertification{
						{
							SalesChannelID: "1",
							Files:          []models.SalesChannelFile{{SalesChannelID: "2"}},
						},
					},
				},
			},
			expected: true,
		},
		{
			name: "Certification configurations are the same",
			listing: ProductListing{
				Product: models.Product{
					Certifications: []*models.ProductCertification{
						{
							SalesChannelID: "1",
							Images:         []models.SalesChannelFile{{SalesChannelID: "1"}}, Files: []models.SalesChannelFile{{SalesChannelID: "1"}},
						},
					},
				},
			},
			lastListing: ProductListing{
				Product: models.Product{
					Certifications: []*models.ProductCertification{
						{
							SalesChannelID: "1",
							Images:         []models.SalesChannelFile{{SalesChannelID: "1"}}, Files: []models.SalesChannelFile{{SalesChannelID: "1"}},
						},
					},
				},
			},
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			model := newCompareModel(&tt.listing, &tt.lastListing, compareSetting{}, nil)
			result := model.isCertificationModify()
			require.Equal(t, tt.expected, result)
		})
	}
}

func TestCompareModel_isVariantModify(t *testing.T) {
	tests := []struct {
		name        string
		listing     ProductListing
		lastListing ProductListing
		expected    bool
	}{
		{
			name: "Variant count differs",
			listing: ProductListing{
				Product: models.Product{
					Variants: []*models.ProductVariant{
						{ID: "1", Price: models.ProductVariantPrice{Amount: "100"}},
					},
				},
			},
			lastListing: ProductListing{
				Product: models.Product{
					Variants: []*models.ProductVariant{
						{ID: "1", Price: models.ProductVariantPrice{Amount: "100"}},
						{ID: "2", Price: models.ProductVariantPrice{Amount: "200"}},
					},
				},
			},
			expected: true,
		},
		{
			name: "Variant ID differs",
			listing: ProductListing{
				Product: models.Product{
					Variants: []*models.ProductVariant{
						{ID: "1", Price: models.ProductVariantPrice{Amount: "100"}},
					},
				},
			},
			lastListing: ProductListing{
				Product: models.Product{
					Variants: []*models.ProductVariant{
						{ID: "2", Price: models.ProductVariantPrice{Amount: "100"}},
					},
				},
			},
			expected: true,
		},
		{
			name: "Variant option differ",
			listing: ProductListing{
				Product: models.Product{
					Variants: []*models.ProductVariant{
						{ID: "1", Price: models.ProductVariantPrice{Amount: "150"}, Options: []*models.ProductVariantOption{{Name: "Color", Value: "Red"}}},
					},
				},
			},
			lastListing: ProductListing{
				Product: models.Product{
					Variants: []*models.ProductVariant{
						{ID: "1", Price: models.ProductVariantPrice{Amount: "150"}, Options: []*models.ProductVariantOption{{Name: "Color", Value: "Blue"}}},
					},
				},
			},
			expected: true,
		},
		{
			name: "Variants are the same",
			listing: ProductListing{
				Product: models.Product{
					Variants: []*models.ProductVariant{
						{ID: "1", Price: models.ProductVariantPrice{Amount: "100"}},
					},
				},
			},
			lastListing: ProductListing{
				Product: models.Product{
					Variants: []*models.ProductVariant{
						{ID: "1", Price: models.ProductVariantPrice{Amount: "100"}},
					},
				},
			},
			expected: false,
		},
		{
			name: "Variants available diff",
			listing: ProductListing{
				Product: models.Product{
					Variants: []*models.ProductVariant{
						{ID: "1", Price: models.ProductVariantPrice{Amount: "100"}, Available: "available"},
					},
				},
			},
			lastListing: ProductListing{
				Product: models.Product{
					Variants: []*models.ProductVariant{
						{ID: "1", Price: models.ProductVariantPrice{Amount: "100"}, Available: "unavailable"},
					},
				},
			},
			expected: true,
		},
		{
			name: "update variant price",
			listing: ProductListing{
				Product: models.Product{
					Variants: []*models.ProductVariant{
						{ID: "1", Price: models.ProductVariantPrice{Amount: "100"}},
					},
				},
			},
			lastListing: ProductListing{
				Product: models.Product{
					Variants: []*models.ProductVariant{
						{ID: "1", Price: models.ProductVariantPrice{Amount: "1000"}},
					},
				},
			},
			expected: false,
		},
		{
			name: "update variant inventory",
			listing: ProductListing{
				Product: models.Product{
					Variants: []*models.ProductVariant{
						{ID: "1", Price: models.ProductVariantPrice{Amount: "100"}, InventoryQuantity: 10},
					},
				},
			},
			lastListing: ProductListing{
				Product: models.Product{
					Variants: []*models.ProductVariant{
						{ID: "1", Price: models.ProductVariantPrice{Amount: "100"}, InventoryQuantity: 100},
					},
				},
			},
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			model := newCompareModel(&tt.listing, &tt.lastListing, compareSetting{}, nil)
			result := model.isVariantModify()
			require.Equal(t, tt.expected, result)
		})
	}
}

func TestCompareModel_isPriceSyncSettingModify(t *testing.T) {
	tests := []struct {
		name        string
		listing     ProductListing
		lastListing ProductListing
		expected    bool
	}{
		{
			name: "PriceSyncSetting preference update",
			listing: ProductListing{
				Settings: SyncSettings{
					PriceSyncSetting: PriceSyncSetting{
						Preference: consts.SettingPreferenceCustomized,
					},
				},
			},
			lastListing: ProductListing{
				Settings: SyncSettings{
					PriceSyncSetting: PriceSyncSetting{
						Preference: consts.SettingPreferenceStore,
					},
				},
			},
			expected: true,
		},
		{
			name: "PriceSyncSetting sales price update to compare price",
			listing: ProductListing{
				Settings: SyncSettings{
					PriceSyncSetting: PriceSyncSetting{
						Preference: consts.SettingPreferenceCustomized,
						Customized: models.PriceSync{
							SourceField: consts.PriceSyncSourceFieldSalePrice,
						},
					},
				},
			},
			lastListing: ProductListing{
				Settings: SyncSettings{
					PriceSyncSetting: PriceSyncSetting{
						Preference: consts.SettingPreferenceCustomized,
						Customized: models.PriceSync{
							SourceField: consts.PriceSyncSourceFieldCompareAtPrice,
						},
					},
				},
			},
			expected: true,
		},
		{
			name: "PriceSyncSetting update auto sync",
			listing: ProductListing{
				Settings: SyncSettings{
					PriceSyncSetting: PriceSyncSetting{
						Preference: consts.SettingPreferenceCustomized,
						Customized: models.PriceSync{
							SourceField: consts.PriceSyncSourceFieldSalePrice,
							AutoSync:    string(consts.AllowSyncEnabled),
						},
					},
				},
			},
			lastListing: ProductListing{
				Settings: SyncSettings{
					PriceSyncSetting: PriceSyncSetting{
						Preference: consts.SettingPreferenceCustomized,
						Customized: models.PriceSync{
							SourceField: consts.PriceSyncSourceFieldSalePrice,
							AutoSync:    string(consts.AllowSyncDisabled),
						},
					},
				},
			},
			expected: true,
		},
		{
			name: "PriceSyncSetting update auto sync",
			listing: ProductListing{
				Settings: SyncSettings{
					PriceSyncSetting: PriceSyncSetting{
						Preference: consts.SettingPreferenceCustomized,
						Customized: models.PriceSync{
							SourceField: consts.PriceSyncSourceFieldSalePrice,
							AutoSync:    string(consts.AllowSyncEnabled),
							Rules: []models.PriceRules{
								{
									ValueType: consts.PriceSyncUsePercentage,
									Value:     "10",
								},
							},
						},
					},
				},
			},
			lastListing: ProductListing{
				Settings: SyncSettings{
					PriceSyncSetting: PriceSyncSetting{
						Preference: consts.SettingPreferenceCustomized,
						Customized: models.PriceSync{
							SourceField: consts.PriceSyncSourceFieldSalePrice,
							AutoSync:    string(consts.AllowSyncEnabled),
							Rules: []models.PriceRules{
								{
									ValueType: consts.PriceSyncUsePercentage,
									Value:     "100",
								},
							},
						},
					},
				},
			},
			expected: true,
		},
		{
			name: "same PriceSyncSetting",
			listing: ProductListing{
				Settings: SyncSettings{
					PriceSyncSetting: PriceSyncSetting{
						Preference: consts.SettingPreferenceCustomized,
						Customized: models.PriceSync{
							SourceField: consts.PriceSyncSourceFieldSalePrice,
							AutoSync:    string(consts.AllowSyncEnabled),
							Rules: []models.PriceRules{
								{
									ValueType: consts.PriceSyncUsePercentage,
									Value:     "10",
								},
							},
						},
					},
				},
			},
			lastListing: ProductListing{
				Settings: SyncSettings{
					PriceSyncSetting: PriceSyncSetting{
						Preference: consts.SettingPreferenceCustomized,
						Customized: models.PriceSync{
							SourceField: consts.PriceSyncSourceFieldSalePrice,
							AutoSync:    string(consts.AllowSyncEnabled),
							Rules: []models.PriceRules{
								{
									ValueType: consts.PriceSyncUsePercentage,
									Value:     "10",
								},
							},
						},
					},
				},
			},
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			model := newCompareModel(&tt.listing, &tt.lastListing, compareSetting{}, nil)
			result := model.isPriceSyncSettingModify()
			require.Equal(t, tt.expected, result)
		})
	}
}

func TestCompareModel_isInventorySyncSettingModify(t *testing.T) {
	tests := []struct {
		name        string
		listing     ProductListing
		lastListing ProductListing
		expected    bool
	}{
		{
			name: "InventorySyncSetting preference update",
			listing: ProductListing{
				Settings: SyncSettings{
					InventorySyncSetting: InventorySyncSetting{
						Preference: consts.SettingPreferenceCustomized,
					},
				},
			},
			lastListing: ProductListing{
				Settings: SyncSettings{
					InventorySyncSetting: InventorySyncSetting{
						Preference: consts.SettingPreferenceStore,
					},
				},
			},
			expected: true,
		},
		{
			name: "InventorySyncSetting auto sync update",
			listing: ProductListing{
				Settings: SyncSettings{
					InventorySyncSetting: InventorySyncSetting{
						Preference: consts.SettingPreferenceCustomized,
						Customized: models.InventorySync{
							AutoSync: string(consts.AllowSyncEnabled),
						},
					},
				},
			},
			lastListing: ProductListing{
				Settings: SyncSettings{
					InventorySyncSetting: InventorySyncSetting{
						Preference: consts.SettingPreferenceCustomized,
						Customized: models.InventorySync{
							AutoSync: string(consts.StateDisabled),
						},
					},
				},
			},
			expected: true,
		},
		{
			name: "InventorySyncSetting AvailableQuantityPercent update",
			listing: ProductListing{
				Settings: SyncSettings{
					InventorySyncSetting: InventorySyncSetting{
						Preference: consts.SettingPreferenceCustomized,
						Customized: models.InventorySync{
							AutoSync:                 string(consts.AllowSyncEnabled),
							AvailableQuantityPercent: 10,
						},
					},
				},
			},
			lastListing: ProductListing{
				Settings: SyncSettings{
					InventorySyncSetting: InventorySyncSetting{
						Preference: consts.SettingPreferenceCustomized,
						Customized: models.InventorySync{
							AutoSync:                 string(consts.AllowSyncEnabled),
							AvailableQuantityPercent: 90,
						},
					},
				},
			},
			expected: true,
		},
		{
			name: "InventorySyncSetting is the same",
			listing: ProductListing{
				Settings: SyncSettings{
					InventorySyncSetting: InventorySyncSetting{
						Preference: consts.SettingPreferenceCustomized,
						Customized: models.InventorySync{
							AutoSync:                 string(consts.AllowSyncEnabled),
							AvailableQuantityPercent: 10,
						},
					},
				},
			},
			lastListing: ProductListing{
				Settings: SyncSettings{
					InventorySyncSetting: InventorySyncSetting{
						Preference: consts.SettingPreferenceCustomized,
						Customized: models.InventorySync{
							AutoSync:                 string(consts.AllowSyncEnabled),
							AvailableQuantityPercent: 10,
						},
					},
				},
			},
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			model := newCompareModel(&tt.listing, &tt.lastListing, compareSetting{}, nil)
			result := model.isInventorySyncSettingModify()
			require.Equal(t, tt.expected, result)
		})
	}
}

func TestCompareModel_isComplianceModify(t *testing.T) {
	tests := []struct {
		name        string
		listing     ProductListing
		lastListing ProductListing
		expected    bool
	}{
		{
			name: "empty no modify",
			listing: ProductListing{
				Product: models.Product{
					Compliance: models.ProductCompliance{},
				},
			},
			lastListing: ProductListing{
				Product: models.Product{
					Compliance: models.ProductCompliance{},
				},
			},
			expected: false,
		},
		{
			name: "value no modify",
			listing: ProductListing{
				Product: models.Product{
					Compliance: models.ProductCompliance{
						Manufacturers: []models.ProductComplianceManufacturer{
							{
								SalesChannelID: "id_1",
							},
						},
						ResponsiblePersons: []models.ProductComplianceResponsiblePerson{
							{
								SalesChannelID: "id_1",
							},
						},
					},
				},
			},
			lastListing: ProductListing{
				Product: models.Product{
					Compliance: models.ProductCompliance{
						Manufacturers: []models.ProductComplianceManufacturer{
							{
								SalesChannelID: "id_1",
							},
						},
						ResponsiblePersons: []models.ProductComplianceResponsiblePerson{
							{
								SalesChannelID: "id_1",
							},
						},
					},
				},
			},
			expected: false,
		},
		{
			name: "manufacturer modify",
			listing: ProductListing{
				Product: models.Product{
					Compliance: models.ProductCompliance{
						Manufacturers: []models.ProductComplianceManufacturer{
							{
								SalesChannelID: "id_1_1",
							},
						},
						ResponsiblePersons: []models.ProductComplianceResponsiblePerson{
							{
								SalesChannelID: "id_1",
							},
						},
					},
				},
			},
			lastListing: ProductListing{
				Product: models.Product{
					Compliance: models.ProductCompliance{
						Manufacturers: []models.ProductComplianceManufacturer{
							{
								SalesChannelID: "id_1",
							},
						},
						ResponsiblePersons: []models.ProductComplianceResponsiblePerson{
							{
								SalesChannelID: "id_1",
							},
						},
					},
				},
			},
			expected: true,
		},
		{
			name: "responsiblePersons modify",
			listing: ProductListing{
				Product: models.Product{
					Compliance: models.ProductCompliance{
						Manufacturers: []models.ProductComplianceManufacturer{
							{
								SalesChannelID: "id_1",
							},
						},
						ResponsiblePersons: []models.ProductComplianceResponsiblePerson{
							{
								SalesChannelID: "id_1_1",
							},
						},
					},
				},
			},
			lastListing: ProductListing{
				Product: models.Product{
					Compliance: models.ProductCompliance{
						Manufacturers: []models.ProductComplianceManufacturer{
							{
								SalesChannelID: "id_1",
							},
						},
						ResponsiblePersons: []models.ProductComplianceResponsiblePerson{
							{
								SalesChannelID: "id_1",
							},
						},
					},
				},
			},
			expected: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			model := newCompareModel(&tt.listing, &tt.lastListing, compareSetting{}, nil)
			result := model.isComplianceModify()
			require.Equal(t, tt.expected, result)
		})
	}
}

func TestCompareModel_buildAuditVersion(t *testing.T) {
	listing := ProductListing{
		ID: "123",
		Product: models.Product{
			Title:       "New Title",
			Description: "New Description",
			Media:       []*models.ProductMedia{{SalesChannelID: "1"}},
			Variants:    []*models.ProductVariant{{ID: "1", Price: models.ProductVariantPrice{Currency: "100"}}},
		},
	}
	lastListing := ProductListing{
		ID: "123",
		Product: models.Product{
			Title:       "Old Title",
			Description: "Old Description",
			Media:       []*models.ProductMedia{{SalesChannelID: "2"}},
			Variants:    []*models.ProductVariant{{ID: "2", Price: models.ProductVariantPrice{Currency: "100"}}},
		},
	}
	model := newCompareModel(&listing, &lastListing, compareSetting{}, nil)

	// Act
	result := model.buildAuditVersion()

	// Assert
	require.NotNil(t, result)
	require.Equal(t, listing.ID, result.ProductListing.ID)
	require.Equal(t, listing.Product.Title, result.ProductListing.Product.Title)
	require.Equal(t, listing.Product.Description, result.ProductListing.Product.Description)
	require.True(t, reflect.DeepEqual(listing.Product.Media, result.ProductListing.Product.Media))
	require.True(t, reflect.DeepEqual(listing.Product.Variants, result.ProductListing.Product.Variants))
}

// nolint:maintidx
func TestCompareModel_buildAuditVersionWithSetting(t *testing.T) {
	tests := []struct {
		name         string
		listing      *ProductListing
		lastListing  *ProductListing
		unionSetting *storeProductListingSetting
		checkFunc    func(t *testing.T, result *AuditVersion, listing, lastListing *ProductListing)
	}{
		{
			name: "test case 1",
			listing: &ProductListing{
				ID: "123",
				Product: models.Product{
					Title:       "New Title",
					Description: "New Description",
					Media:       []*models.ProductMedia{{SalesChannelID: "1"}},
					Variants:    []*models.ProductVariant{{ID: "1", Price: models.ProductVariantPrice{Currency: "USD", Amount: "200"}}},
				},
			},
			lastListing: &ProductListing{
				ID: "123",
				Product: models.Product{
					Title:       "Old Title",
					Description: "Old Description",
					Media:       []*models.ProductMedia{{SalesChannelID: "2"}},
					Variants:    []*models.ProductVariant{{ID: "2", Price: models.ProductVariantPrice{Currency: "USD", Amount: "100"}}},
				},
			},
			unionSetting: &storeProductListingSetting{
				storeSetting: settings.Setting{},
				productListingSetting: SyncSettings{
					PriceSyncSetting: PriceSyncSetting{
						Preference: consts.SettingPreferenceCustomized,
						Customized: models.PriceSync{
							SourceField: consts.PriceSyncSourceFieldSalePrice,
							AutoSync:    string(consts.AllowSyncEnabled),
						},
					},
					InventorySyncSetting: InventorySyncSetting{
						Preference: consts.SettingPreferenceCustomized,
						Customized: models.InventorySync{
							AutoSync:                 string(consts.AllowSyncEnabled),
							AvailableQuantityPercent: 10,
						},
					},
					ProductSyncSetting: ProductSyncSetting{
						Preference: consts.SettingPreferenceCustomized,
						Customized: models.ProductSync{
							UpdateDetail: models.UpdateDetail{
								Fields: []string{
									string(consts.ProductDetailFieldTitle),
									string(consts.ProductDetailFieldMedia),
									string(consts.ProductDetailFieldDescription),
								},
								AutoSync: string(consts.AllowSyncEnabled),
							},
							UpdateVariants: models.UpdateVariants{
								AutoSync: string(consts.AllowSyncEnabled),
							},
						},
					},
				},
			},
			checkFunc: func(t *testing.T, result *AuditVersion, listing, lastListing *ProductListing) {
				require.NotNil(t, result)
				require.Equal(t, listing.ID, result.ProductListing.ID)
				require.Equal(t, listing.Product.Title, result.ProductListing.Product.Title)
				require.Equal(t, listing.Product.Description, result.ProductListing.Product.Description)
				require.Equal(t, listing.Product.Media, result.ProductListing.Product.Media)
				require.Equal(t, listing.Product.Variants, result.ProductListing.Product.Variants)
			},
		},
		{
			name: "test case 2",
			listing: &ProductListing{
				ID: "123",
				Product: models.Product{
					Title:       "New Title",
					Description: "New Description",
					Media:       []*models.ProductMedia{{SalesChannelID: "1"}},
					Variants:    []*models.ProductVariant{{ID: "1", Price: models.ProductVariantPrice{Currency: "USD", Amount: "200"}}},
				},
			},
			lastListing: &ProductListing{
				ID: "123",
				Product: models.Product{
					Title:       "Old Title",
					Description: "Old Description",
					Media:       []*models.ProductMedia{{SalesChannelID: "2"}},
					Variants:    []*models.ProductVariant{{ID: "2", Price: models.ProductVariantPrice{Currency: "USD", Amount: "100"}}},
				},
			},
			unionSetting: &storeProductListingSetting{
				storeSetting: settings.Setting{},
				productListingSetting: SyncSettings{
					PriceSyncSetting: PriceSyncSetting{
						Preference: consts.SettingPreferenceCustomized,
						Customized: models.PriceSync{
							SourceField: consts.PriceSyncSourceFieldSalePrice,
							AutoSync:    string(consts.AllowSyncEnabled),
						},
					},
					InventorySyncSetting: InventorySyncSetting{
						Preference: consts.SettingPreferenceCustomized,
						Customized: models.InventorySync{
							AutoSync:                 string(consts.AllowSyncEnabled),
							AvailableQuantityPercent: 10,
						},
					},
					ProductSyncSetting: ProductSyncSetting{
						Preference: consts.SettingPreferenceCustomized,
						Customized: models.ProductSync{
							UpdateDetail: models.UpdateDetail{
								Fields: []string{
									string(consts.ProductDetailFieldTitle),
									string(consts.ProductDetailFieldMedia),
									string(consts.ProductDetailFieldDescription),
								},
								AutoSync: string(consts.AllowSyncDisabled),
							},
							UpdateVariants: models.UpdateVariants{
								AutoSync: string(consts.AllowSyncEnabled),
							},
						},
					},
				},
			},
			checkFunc: func(t *testing.T, result *AuditVersion, listing, lastListing *ProductListing) {
				require.NotNil(t, result)
				require.Equal(t, listing.ID, result.ProductListing.ID)
				require.Equal(t, lastListing.Product.Title, result.ProductListing.Product.Title)
				require.Equal(t, lastListing.Product.Description, result.ProductListing.Product.Description)
				require.True(t, reflect.DeepEqual(lastListing.Product.Media, result.ProductListing.Product.Media))
				require.True(t, reflect.DeepEqual(listing.Product.Variants, result.ProductListing.Product.Variants))
			},
		},
		{
			name: "test case 3",
			listing: &ProductListing{
				ID: "123",
				Product: models.Product{
					Title:       "New Title",
					Description: "New Description",
					Media:       []*models.ProductMedia{{SalesChannelID: "1"}},
					Variants:    []*models.ProductVariant{{ID: "1", Price: models.ProductVariantPrice{Currency: "USD", Amount: "200"}}},
				},
			},
			lastListing: &ProductListing{
				ID: "123",
				Product: models.Product{
					Title:       "Old Title",
					Description: "Old Description",
					Media:       []*models.ProductMedia{{SalesChannelID: "2"}},
					Variants:    []*models.ProductVariant{{ID: "2", Price: models.ProductVariantPrice{Currency: "USD", Amount: "100"}}},
				},
			},
			unionSetting: &storeProductListingSetting{
				storeSetting: settings.Setting{},
				productListingSetting: SyncSettings{
					PriceSyncSetting: PriceSyncSetting{
						Preference: consts.SettingPreferenceCustomized,
						Customized: models.PriceSync{
							SourceField: consts.PriceSyncSourceFieldSalePrice,
							AutoSync:    string(consts.AllowSyncEnabled),
						},
					},
					InventorySyncSetting: InventorySyncSetting{
						Preference: consts.SettingPreferenceCustomized,
						Customized: models.InventorySync{
							AutoSync:                 string(consts.AllowSyncEnabled),
							AvailableQuantityPercent: 10,
						},
					},
					ProductSyncSetting: ProductSyncSetting{
						Preference: consts.SettingPreferenceCustomized,
						Customized: models.ProductSync{
							UpdateDetail: models.UpdateDetail{
								Fields: []string{
									string(consts.ProductDetailFieldTitle),
									string(consts.ProductDetailFieldMedia),
									string(consts.ProductDetailFieldDescription),
								},
								AutoSync: string(consts.AllowSyncDisabled),
							},
							UpdateVariants: models.UpdateVariants{
								AutoSync: string(consts.AllowSyncDisabled),
							},
						},
					},
				},
			},
			checkFunc: func(t *testing.T, result *AuditVersion, listing, lastListing *ProductListing) {
				require.NotNil(t, result)
				require.Equal(t, listing.ID, result.ProductListing.ID)
				require.Equal(t, lastListing.Product.Title, result.ProductListing.Product.Title)
				require.Equal(t, lastListing.Product.Description, result.ProductListing.Product.Description)
				require.True(t, reflect.DeepEqual(lastListing.Product.Media, result.ProductListing.Product.Media))
				require.True(t, reflect.DeepEqual(lastListing.Product.Variants, result.ProductListing.Product.Variants))
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			model := newCompareModel(tt.listing, tt.lastListing, compareSetting{}, nil)
			// Act
			result := model.buildAuditVersionWithSetting(tt.unionSetting)
			// Assert
			tt.checkFunc(t, result, tt.listing, tt.lastListing)
		})
	}
}

func TestCompareModel_isVariantsMultipleImageModify(t *testing.T) {
	t.Run("ignoreCompareVariantImage=true should return false", func(t *testing.T) {
		m := &compareModel{
			compareSetting: compareSetting{ignoreCompareVariantImage: true},
		}
		require.False(t, m.isVariantsMultipleImageModify())
	})

	t.Run("options length not equal should return true", func(t *testing.T) {
		m := &compareModel{
			compareSetting: compareSetting{},
			listing:        &ProductListing{Product: models.Product{Options: []*models.ProductOption{{Name: "A"}}}},
			lastListing:    &ProductListing{Product: models.Product{Options: []*models.ProductOption{}}},
		}
		require.True(t, m.isVariantsMultipleImageModify())
	})

	t.Run("option value details length not equal should return true", func(t *testing.T) {
		m := &compareModel{
			compareSetting: compareSetting{},
			listing:        &ProductListing{Product: models.Product{Options: []*models.ProductOption{{Name: "A", ValueDetails: []models.ProductOptionValueDetail{{}}}}}},
			lastListing:    &ProductListing{Product: models.Product{Options: []*models.ProductOption{{Name: "A", ValueDetails: []models.ProductOptionValueDetail{}}}}},
		}
		require.True(t, m.isVariantsMultipleImageModify())
	})

	t.Run("media length not equal should return true", func(t *testing.T) {
		m := &compareModel{
			compareSetting: compareSetting{},
			listing:        &ProductListing{Product: models.Product{Options: []*models.ProductOption{{Name: "A", ValueDetails: []models.ProductOptionValueDetail{{Media: []models.ProductMedia{{URL: "1"}}}}}}}},
			lastListing:    &ProductListing{Product: models.Product{Options: []*models.ProductOption{{Name: "A", ValueDetails: []models.ProductOptionValueDetail{{Media: []models.ProductMedia{}}}}}}},
		}
		require.True(t, m.isVariantsMultipleImageModify())
	})

	t.Run("media url not equal should return true", func(t *testing.T) {
		m := &compareModel{
			compareSetting: compareSetting{},
			listing:        &ProductListing{Product: models.Product{Options: []*models.ProductOption{{Name: "A", ValueDetails: []models.ProductOptionValueDetail{{Media: []models.ProductMedia{{URL: "1"}}}}}}}},
			lastListing:    &ProductListing{Product: models.Product{Options: []*models.ProductOption{{Name: "A", ValueDetails: []models.ProductOptionValueDetail{{Media: []models.ProductMedia{{URL: "2"}}}}}}}},
		}
		require.True(t, m.isVariantsMultipleImageModify())
	})

	t.Run("media all equal should return false", func(t *testing.T) {
		m := &compareModel{
			compareSetting: compareSetting{},
			listing:        &ProductListing{Product: models.Product{Options: []*models.ProductOption{{Name: "A", ValueDetails: []models.ProductOptionValueDetail{{Media: []models.ProductMedia{{URL: "1"}}}}}}}},
			lastListing:    &ProductListing{Product: models.Product{Options: []*models.ProductOption{{Name: "A", ValueDetails: []models.ProductOptionValueDetail{{Media: []models.ProductMedia{{URL: "1"}}}}}}}},
		}
		require.False(t, m.isVariantsMultipleImageModify())
	})
}
