package product_listing

import (
	"time"

	"github.com/AfterShip/gopkg/facility/types"

	"github.com/AfterShip/pltf-pd-product-listings/internal/consts"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
)

const (
	tableRelation                         = "product_listing_relations"
	tableFieldRelationID                  = "product_listing_relation_id"
	tableFieldCreatedAt                   = "created_at"
	salesChannelProductVariantIDIndex     = "product_listing_relations_by_organization_id_a_platform_a_store_key_a_sales_channel_product_id_a_sales_channel_variant_id_a" // nolint:lll
	productsCenterProductVariantIDIndex   = "product_listing_relations_by_organization_id_a_pc_product_id_a_pc_variant_id_a_platform_a_store_key_a"                       // nolint:lll
	productListingIDIndex                 = "product_listing_relations_by_product_listing_id_a_product_listing_variant_id_a_deleted_at_a_u"
	productsCenterConnectorProductIDIndex = "product_listing_relations_by_organization_id_a_products_center_connector_product_id_a"
)

type relationDBModel struct {
	ProductListingRelationID         string                 `spanner:"product_listing_relation_id"`
	ProductListingID                 string                 `spanner:"product_listing_id"`
	ProductListingVariantID          string                 `spanner:"product_listing_variant_id"`
	OrganizationID                   string                 `spanner:"organization_id"`
	SalesChannelPlatform             string                 `spanner:"sales_channel_platform"`
	SalesChannelStoreKey             string                 `spanner:"sales_channel_store_key"`
	SalesChannelVariantID            string                 `spanner:"sales_channel_variant_id"`
	SalesChannelConnectorProductID   string                 `spanner:"sales_channel_connector_product_id"`
	SalesChannelProductID            string                 `spanner:"sales_channel_product_id"`
	SalesChannelSku                  string                 `spanner:"sales_channel_sku"`
	ProductsCenterVariantID          string                 `spanner:"products_center_variant_id"`
	ProductsCenterProductID          string                 `spanner:"products_center_product_id"`
	ProductsCenterConnectorProductID string                 `spanner:"products_center_connector_product_id"`
	ProductsCenterSourceStoreKey     string                 `spanner:"products_center_source_store_key"`
	ProductsCenterSourcePlatform     string                 `spanner:"products_center_source_platform"`
	ProductsCenterSourceVariantID    string                 `spanner:"products_center_source_variant_id"`
	ProductsCenterSourceProductID    string                 `spanner:"products_center_source_product_id"`
	ProductsCenterSourceSku          string                 `spanner:"products_center_source_sku"`
	SyncStatus                       consts.SyncStatus      `spanner:"sync_status"`
	LinkStatus                       consts.LinkStatus      `spanner:"link_status"`
	AllowSync                        consts.AllowSyncStatus `spanner:"allow_sync"`
	LastSyncedAt                     types.Datetime         `spanner:"last_synced_at"`
	LastLinkedAt                     types.Datetime         `spanner:"last_linked_at"`
	DeletedAt                        types.Datetime         `spanner:"deleted_at"`
	CreatedAt                        time.Time              `spanner:"created_at"`
	UpdatedAt                        time.Time              `spanner:"updated_at"`
}

func (model *relationDBModel) SpannerTable() string {
	return tableRelation
}

func (model *relationDBModel) toProductListingRelation() ProductListingRelation {
	return ProductListingRelation{
		ID: model.ProductListingRelationID,
		Organization: models.Organization{
			ID: model.OrganizationID,
		},
		SalesChannel: models.SalesChannel{
			Platform: model.SalesChannelPlatform,
			StoreKey: model.SalesChannelStoreKey,
		},
		ProductListingID:        model.ProductListingID,
		ProductListingVariantID: model.ProductListingVariantID,
		AllowSync:               model.AllowSync,
		DeletedAt:               model.DeletedAt,
		CreatedAt:               model.CreatedAt,
		UpdatedAt:               model.UpdatedAt,
		SyncStatus:              model.SyncStatus,
		LastSyncedAt:            model.LastSyncedAt,
		LinkStatus:              model.LinkStatus,
		LastLinkedAt:            model.LastLinkedAt,
		SalesChannelVariant: SalesChannelVariant{
			ID:                 model.SalesChannelVariantID,
			ConnectorProductID: model.SalesChannelConnectorProductID,
			ProductID:          model.SalesChannelProductID,
			Sku:                model.SalesChannelSku,
		},
		ProductsCenterVariant: ProductsCenterVariant{
			ID:                 model.ProductsCenterVariantID,
			ConnectorProductID: model.ProductsCenterConnectorProductID,
			ProductID:          model.ProductsCenterProductID,
			Source: ProductsCenterVariantSource{
				StoreKey:  model.ProductsCenterSourceStoreKey,
				Platform:  model.ProductsCenterSourcePlatform,
				ID:        model.ProductsCenterSourceVariantID,
				ProductID: model.ProductsCenterSourceProductID,
				Sku:       model.ProductsCenterSourceSku,
			},
		},
	}
}
