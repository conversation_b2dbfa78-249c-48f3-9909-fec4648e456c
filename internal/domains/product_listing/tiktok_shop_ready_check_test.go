package product_listing

import (
	"fmt"
	"regexp"
	"testing"

	"github.com/stretchr/testify/require"

	"github.com/AfterShip/gopkg/facility/set"
	"github.com/AfterShip/pltf-pd-product-listings/internal/config"
	"github.com/AfterShip/pltf-pd-product-listings/internal/consts"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/category"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
)

func TestPackageCheck(t *testing.T) {
	tests := []struct {
		name    string
		product *models.Product
		want    []tiktokReadyCheck
	}{
		{
			name:    "No variants",
			product: &models.Product{},
			want:    nil,
		},
		{
			name: "Variant with missing dimensions and weight",
			product: &models.Product{
				Variants: []*models.ProductVariant{
					{},
				},
			},
			want: []tiktokReadyCheck{
				&packageDimensionsHeightIsEmpty{},
				&packageDimensionsWidthIsEmpty{},
				&packageDimensionsLengthIsEmpty{},
				&packageWeightIsEmpty{},
			},
		},
		{
			name: "Variant with all dimensions and weight filled",
			product: &models.Product{
				Variants: []*models.ProductVariant{
					{
						Length: models.ProductVariantShippingSetting{
							Value: 1,
							Unit:  "cm",
						},
						Width: models.ProductVariantShippingSetting{
							Value: 1,
							Unit:  "cm",
						},
						Height: models.ProductVariantShippingSetting{
							Value: 1,
							Unit:  "cm",
						},
						Weight: models.ProductVariantShippingSetting{
							Value: 1,
							Unit:  "g",
						},
					},
				},
			},
			want: []tiktokReadyCheck{},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			model := &tiktokShopReadyCheckModel{
				product: tt.product,
			}
			got := model.packageCheck()
			require.Equal(t, tt.want, got)
		})
	}
}

func TestVariantMediaCheck(t *testing.T) {
	tests := []struct {
		name    string
		product *models.Product
		want    []tiktokReadyCheck
	}{
		{
			name:    "No variants",
			product: &models.Product{},
			want:    []tiktokReadyCheck{},
		},
		{
			name: "Variant with valid image URL",
			product: &models.Product{
				Variants: []*models.ProductVariant{
					{ImageURL: "https://p16-oec-ttp.tiktokcdn-us.com/tos-useast5-i-omjb5zjo8w-tx/1e6ab76df1884c7ab524c61e08348556~tplv-omjb5zjo8w-origin-jpeg.jpeg"},
				},
			},
			want: []tiktokReadyCheck{},
		},
		{
			name: "Variant with invalid image URL",
			product: &models.Product{
				Variants: []*models.ProductVariant{
					{ImageURL: "invalid-url"},
				},
			},
			want: []tiktokReadyCheck{
				&variantImageInvalid{position: 0},
			},
		},
		{
			name: "Multiple variants with mixed image URLs",
			product: &models.Product{
				Variants: []*models.ProductVariant{
					{ImageURL: "https://p16-oec-ttp.tiktokcdn-us.com/tos-useast5-i-omjb5zjo8w-tx/1e6ab76df1884c7ab524c61e08348556~tplv-omjb5zjo8w-origin-jpeg.jpeg"},
					{ImageURL: "invalid-url"},
				},
			},
			want: []tiktokReadyCheck{
				&variantImageInvalid{position: 1},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			model := &tiktokShopReadyCheckModel{
				product: tt.product,
			}
			got := model.variantMediaCheck()
			require.Equal(t, tt.want, got)
		})
	}
}

func TestVariantMediaEmptyCheck(t *testing.T) {
	tests := []struct {
		name    string
		product *models.Product
		want    []tiktokReadyCheck
	}{
		{
			name:    "No variants",
			product: &models.Product{},
			want:    []tiktokReadyCheck{},
		},
		{
			name: "Variant with image URL",
			product: &models.Product{
				Variants: []*models.ProductVariant{
					{ImageURL: "https://valid.url/image.jpg"},
				},
			},
			want: []tiktokReadyCheck{},
		},
		{
			name: "Variant without image URL",
			product: &models.Product{
				Variants: []*models.ProductVariant{
					{ImageURL: ""},
				},
			},
			want: []tiktokReadyCheck{},
		},
		{
			name: "Multiple variants with mixed image URLs",
			product: &models.Product{
				Variants: []*models.ProductVariant{
					{ImageURL: "https://valid.url/image.jpg"},
					{ImageURL: ""},
				},
			},
			want: []tiktokReadyCheck{
				&variantImageIsEmpty{position: 1},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			model := &tiktokShopReadyCheckModel{
				product: tt.product,
			}
			got := model.variantMediaEmptyCheck()
			require.Equal(t, tt.want, got)
		})
	}
}

func TestVariantOptionAndSKUCheck(t *testing.T) {
	tests := []struct {
		name    string
		product *models.Product
		want    []tiktokReadyCheck
	}{
		{
			name:    "No variants",
			product: &models.Product{},
			want:    []tiktokReadyCheck{},
		},
		{
			name: "Variant with valid SKU and options",
			product: &models.Product{
				Variants: []*models.ProductVariant{
					{
						Sku: "valid-sku",
						Options: []*models.ProductVariantOption{
							{Name: "Color", Value: "Red"},
						},
					},
				},
			},
			want: []tiktokReadyCheck{},
		},
		{
			name: "Variant with long SKU",
			product: &models.Product{
				Variants: []*models.ProductVariant{
					{
						Sku: "this-is-a-very-long-sku-that-exceeds-the-maximum-length-allowed",
					},
				},
			},
			want: []tiktokReadyCheck{
				&skuTooLong{Position: 0},
			},
		},
		{
			name: "Variant with long option name",
			product: &models.Product{
				Variants: []*models.ProductVariant{
					{
						Sku: "valid-sku",
						Options: []*models.ProductVariantOption{
							{Name: "ThisIsAVeryLongOptionNameThatExceedsTheLimit", Value: "Value"},
						},
					},
				},
			},
			want: []tiktokReadyCheck{
				&optionNameTooLong{Position: 0, Name: "ThisIsAVeryLongOptionNameThatExceedsTheLimit"},
			},
		},
		{
			name: "Variant with long option value",
			product: &models.Product{
				Variants: []*models.ProductVariant{
					{
						Sku: "valid-sku",
						Options: []*models.ProductVariantOption{
							{Name: "Color", Value: "this-is-a-very-long-sku-that-exceeds-the-maximum-length-allowed"},
						},
					},
				},
			},
			want: []tiktokReadyCheck{
				&optionValueTooLong{PositionX: 0, PositionY: 0},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			model := &tiktokShopReadyCheckModel{
				product: tt.product,
			}
			got := model.variantOptionAndSKUCheck()
			require.Equal(t, tt.want, got)
		})
	}
}

func TestVariantPriceCheck(t *testing.T) {
	tests := []struct {
		name    string
		product *models.Product
		want    []tiktokReadyCheck
	}{
		{
			name:    "No variants",
			product: &models.Product{},
			want:    []tiktokReadyCheck{},
		},
		{
			name: "Variant with valid price",
			product: &models.Product{
				Variants: []*models.ProductVariant{
					{Price: models.ProductVariantPrice{Amount: "100"}},
				},
			},
			want: []tiktokReadyCheck{},
		},
		{
			name: "Variant with zero price",
			product: &models.Product{
				Variants: []*models.ProductVariant{
					{Price: models.ProductVariantPrice{Amount: "0"}},
				},
			},
			want: []tiktokReadyCheck{
				&variantPriceInvalid{position: 0},
			},
		},
		{
			name: "Variant with empty price",
			product: &models.Product{
				Variants: []*models.ProductVariant{
					{Price: models.ProductVariantPrice{Amount: ""}},
				},
			},
			want: []tiktokReadyCheck{
				&variantPriceInvalid{position: 0},
			},
		},
		{
			name: "Multiple variants with mixed prices",
			product: &models.Product{
				Variants: []*models.ProductVariant{
					{Price: models.ProductVariantPrice{Amount: "100"}},
					{Price: models.ProductVariantPrice{Amount: "0"}},
				},
			},
			want: []tiktokReadyCheck{
				&variantPriceInvalid{position: 1},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			model := &tiktokShopReadyCheckModel{
				product: tt.product,
			}
			got := model.variantPriceCheck()
			require.Equal(t, tt.want, got)
		})
	}
}

func TestAttributesCheck(t *testing.T) {
	tests := []struct {
		name                      string
		product                   *models.Product
		attributes                *category.AttributesOutput
		wantMissingAttributeNames []string
	}{
		{
			name: "is not ready",
			product: &models.Product{
				Attributes: []*models.ProductAttribute{
					{
						SalesChannelID: "1",
						Values: []models.SalesChannelResource{
							{
								SalesChannelID: "1",
							},
						},
					},
				},
			},
			attributes: &category.AttributesOutput{
				ExternalCategoryID: "1",
				Attributes: []category.Attribute{
					{
						ID:         "1",
						Name:       "1",
						Type:       consts.AttributeTypeProductProperty,
						IsRequired: true,
					},
					{
						ID:         "2",
						Name:       "2",
						Type:       consts.AttributeTypeProductProperty,
						IsRequired: true,
					},
				},
			},
			wantMissingAttributeNames: []string{"2"},
		},
		{
			name: "is ready",
			product: &models.Product{
				Attributes: []*models.ProductAttribute{
					{
						SalesChannelID: "1",
						Values: []models.SalesChannelResource{
							{
								SalesChannelID: "1",
							},
						},
					},
					{
						SalesChannelID: "2",
						Values: []models.SalesChannelResource{
							{
								SalesChannelID: "2",
							},
						},
					},
				},
			},
			attributes: &category.AttributesOutput{
				ExternalCategoryID: "1",
				Attributes: []category.Attribute{
					{
						ID:         "1",
						Name:       "1",
						Type:       consts.AttributeTypeProductProperty,
						IsRequired: true,
					},
					{
						ID:         "2",
						Name:       "2",
						Type:       consts.AttributeTypeProductProperty,
						IsRequired: true,
					},
				},
			},
			wantMissingAttributeNames: []string{},
		},
		{
			name: "is not ready by conditions",
			product: &models.Product{
				Attributes: []*models.ProductAttribute{
					{
						SalesChannelID: "1",
						Values: []models.SalesChannelResource{
							{
								SalesChannelID: "1",
							},
						},
					},
					{
						SalesChannelID: "2",
						Values: []models.SalesChannelResource{
							{
								SalesChannelID: "2",
							},
						},
					},
				},
			},
			attributes: &category.AttributesOutput{
				ExternalCategoryID: "1",
				Attributes: []category.Attribute{
					{
						ID:         "1",
						Name:       "1",
						Type:       consts.AttributeTypeProductProperty,
						IsRequired: true,
					},
					{
						ID:         "2",
						Name:       "2",
						Type:       consts.AttributeTypeProductProperty,
						IsRequired: true,
					},
					{
						ID:         "3",
						Name:       "3",
						Type:       consts.AttributeTypeProductProperty,
						IsRequired: false,
						RequirementConditions: []category.AttributeRequirementCondition{
							{
								AttributeID:      "1",
								AttributeValueID: "1",
							},
						},
					},
					{
						ID:         "4",
						Name:       "4",
						Type:       consts.AttributeTypeProductProperty,
						IsRequired: false,
						RequirementConditions: []category.AttributeRequirementCondition{
							{
								AttributeID:      "2",
								AttributeValueID: "2",
							},
						},
					},
				},
			},
			wantMissingAttributeNames: []string{"3", "4"},
		},
		{
			name: "is ready by conditions - 1",
			product: &models.Product{
				Attributes: []*models.ProductAttribute{
					{
						SalesChannelID: "1",
						Values: []models.SalesChannelResource{
							{
								SalesChannelID: "111",
							},
						},
					},
					{
						SalesChannelID: "2",
						Values: []models.SalesChannelResource{
							{
								SalesChannelID: "2",
							},
						},
					},
				},
			},
			attributes: &category.AttributesOutput{
				ExternalCategoryID: "1",
				Attributes: []category.Attribute{
					{
						ID:         "1",
						Name:       "1",
						Type:       consts.AttributeTypeProductProperty,
						IsRequired: true,
					},
					{
						ID:         "2",
						Name:       "2",
						Type:       consts.AttributeTypeProductProperty,
						IsRequired: true,
					},
					{
						ID:         "3",
						Name:       "3",
						Type:       consts.AttributeTypeProductProperty,
						IsRequired: false,
						RequirementConditions: []category.AttributeRequirementCondition{
							{
								AttributeID:      "1",
								AttributeValueID: "1",
							},
						},
					},
				},
			},
			wantMissingAttributeNames: []string{},
		},
		{
			name: "is ready by conditions - 2",
			product: &models.Product{
				Attributes: []*models.ProductAttribute{
					{
						SalesChannelID: "1",
						Values: []models.SalesChannelResource{
							{
								SalesChannelID: "1",
							},
						},
					},
					{
						SalesChannelID: "2",
						Values: []models.SalesChannelResource{
							{
								SalesChannelID: "2",
							},
						},
					},
					{
						SalesChannelID: "3",
						Values: []models.SalesChannelResource{
							{
								SalesChannelID: "3",
							},
						},
					},
				},
			},
			attributes: &category.AttributesOutput{
				ExternalCategoryID: "1",
				Attributes: []category.Attribute{
					{
						ID:         "1",
						Name:       "1",
						Type:       consts.AttributeTypeProductProperty,
						IsRequired: true,
					},
					{
						ID:         "2",
						Name:       "2",
						Type:       consts.AttributeTypeProductProperty,
						IsRequired: true,
					},
					{
						ID:         "3",
						Name:       "3",
						Type:       consts.AttributeTypeProductProperty,
						IsRequired: false,
						RequirementConditions: []category.AttributeRequirementCondition{
							{
								AttributeID:      "1",
								AttributeValueID: "1",
							},
						},
					},
				},
			},
			wantMissingAttributeNames: []string{},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			model := &tiktokShopReadyCheckModel{
				product:    tt.product,
				attributes: tt.attributes,
			}
			result := model.attributesCheck()
			resultNames := set.NewStringSet()
			for _, cur := range result {
				resultNames.Add(cur.toFailReason().Position)
			}
			if len(tt.wantMissingAttributeNames) != resultNames.Card() {
				t.Errorf("Expected no missing attributes, got %v", result)
			}
			for index := range tt.wantMissingAttributeNames {
				cur := fmt.Sprint(readyCheckAttributes, ": ", tt.wantMissingAttributeNames[index])
				require.True(t, resultNames.Contains(cur))
			}
		})
	}
}

func TestCertificationCheck(t *testing.T) {
	tests := []struct {
		name                          string
		product                       *models.Product
		rule                          *category.RulesOutput
		wantMissingCertificationNames []string
	}{
		{
			name: "is not ready",
			product: &models.Product{
				Attributes:     []*models.ProductAttribute{},
				Certifications: []*models.ProductCertification{},
			},
			rule: &category.RulesOutput{
				ExternalCategoryID: "1",
				Rule: category.Rule{
					ProductCertifications: []*category.ProductCertification{
						{
							IsRequired:   true,
							ExternalId:   "1",
							ExternalName: "name_1",
						},
					},
				},
			},
			wantMissingCertificationNames: []string{"name_1"},
		},
		{
			name: "is ready - 1",
			product: &models.Product{
				Certifications: []*models.ProductCertification{},
			},
			rule: &category.RulesOutput{
				ExternalCategoryID: "1",
				Rule: category.Rule{
					ProductCertifications: []*category.ProductCertification{
						{
							IsRequired:   false,
							ExternalId:   "1",
							ExternalName: "name_1",
						},
					},
				},
			},
			wantMissingCertificationNames: []string{},
		},
		{
			name: "is ready - 2",
			product: &models.Product{
				Certifications: []*models.ProductCertification{
					{
						SalesChannelID: "1",
						Files: []models.SalesChannelFile{
							{
								SalesChannelID: "file_1",
								URL:            "url_1",
							},
						},
					},
				},
			},
			rule: &category.RulesOutput{
				ExternalCategoryID: "1",
				Rule: category.Rule{
					ProductCertifications: []*category.ProductCertification{
						{
							IsRequired:   true,
							ExternalId:   "1",
							ExternalName: "name_1",
						},
					},
				},
			},
			wantMissingCertificationNames: []string{},
		},
		{
			name: "is not ready by conditions",
			product: &models.Product{
				Attributes: []*models.ProductAttribute{
					{
						SalesChannelID: "1",
						Values: []models.SalesChannelResource{
							{
								SalesChannelID: "1",
							},
						},
					},
				},
			},
			rule: &category.RulesOutput{
				ExternalCategoryID: "1",
				Rule: category.Rule{
					ProductCertifications: []*category.ProductCertification{
						{
							IsRequired:   false,
							ExternalId:   "1",
							ExternalName: "name_1",
							RequirementConditions: []category.AttributeRequirementCondition{
								{
									AttributeID:      "1",
									AttributeValueID: "1",
								},
							},
						},
					},
				},
			},
			wantMissingCertificationNames: []string{"name_1"},
		},
		{
			name: "is not ready by mult conditions",
			product: &models.Product{
				Attributes: []*models.ProductAttribute{
					{
						SalesChannelID: "1",
						Values: []models.SalesChannelResource{
							{
								SalesChannelID: "1",
							},
						},
					},
					{
						SalesChannelID: "2",
						Values: []models.SalesChannelResource{
							{
								SalesChannelID: "2",
							},
						},
					},
				},
				Certifications: []*models.ProductCertification{
					{
						SalesChannelID: "1",
						Files: []models.SalesChannelFile{
							{
								SalesChannelID: "file_1",
								URL:            "url_1",
							},
						},
					},
				},
			},
			rule: &category.RulesOutput{
				ExternalCategoryID: "1",
				Rule: category.Rule{
					ProductCertifications: []*category.ProductCertification{
						{
							IsRequired:   false,
							ExternalId:   "1",
							ExternalName: "name_1",
							RequirementConditions: []category.AttributeRequirementCondition{
								{
									AttributeID:      "1",
									AttributeValueID: "1",
								},
							},
						},
						{
							IsRequired:   false,
							ExternalId:   "2",
							ExternalName: "name_2",
							RequirementConditions: []category.AttributeRequirementCondition{
								{
									AttributeID:      "1",
									AttributeValueID: "1",
								},
								{
									AttributeID:      "2",
									AttributeValueID: "2",
								},
							},
						},
					},
				},
			},
			wantMissingCertificationNames: []string{"name_2"},
		},
		{
			name: "is ready by conditions",
			product: &models.Product{
				Attributes: []*models.ProductAttribute{
					{
						SalesChannelID: "1",
						Values: []models.SalesChannelResource{
							{
								SalesChannelID: "1",
							},
						},
					},
					{
						SalesChannelID: "2",
						Values: []models.SalesChannelResource{
							{
								SalesChannelID: "2",
							},
						},
					},
				},
				Certifications: []*models.ProductCertification{
					{
						SalesChannelID: "1",
						Files: []models.SalesChannelFile{
							{
								SalesChannelID: "file_1",
								URL:            "url_1",
							},
						},
					},
					{
						SalesChannelID: "2",
						Files: []models.SalesChannelFile{
							{
								SalesChannelID: "file_2",
								URL:            "url_2",
							},
						},
					},
				},
			},
			rule: &category.RulesOutput{
				ExternalCategoryID: "1",
				Rule: category.Rule{
					ProductCertifications: []*category.ProductCertification{
						{
							IsRequired:   false,
							ExternalId:   "1",
							ExternalName: "name_1",
							RequirementConditions: []category.AttributeRequirementCondition{
								{
									AttributeID:      "1",
									AttributeValueID: "1",
								},
							},
						},
						{
							IsRequired:   false,
							ExternalId:   "2",
							ExternalName: "name_2",
							RequirementConditions: []category.AttributeRequirementCondition{
								{
									AttributeID:      "1",
									AttributeValueID: "1",
								},
								{
									AttributeID:      "2",
									AttributeValueID: "2",
								},
							},
						},
						{
							IsRequired:   false,
							ExternalId:   "3",
							ExternalName: "name_3",
							RequirementConditions: []category.AttributeRequirementCondition{
								{
									AttributeID:      "3",
									AttributeValueID: "3",
								},
							},
						},
					},
				},
			},
			wantMissingCertificationNames: []string{},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			model := &tiktokShopReadyCheckModel{
				product: tt.product,
				rules:   tt.rule,
			}
			result := model.productCertificationCheck()
			resultNames := set.NewStringSet()
			for _, cur := range result {
				resultNames.Add(cur.toFailReason().Position)
			}
			if len(tt.wantMissingCertificationNames) != resultNames.Card() {
				t.Errorf("Expected no missing attributes, got %v", result)
			}
			for index := range tt.wantMissingCertificationNames {
				cur := tt.wantMissingCertificationNames[index]
				require.True(t, resultNames.Contains(cur))
			}
		})
	}
}

func TestDescriptionContainChineseCheck(t *testing.T) {
	tests := []struct {
		name         string
		product      *models.Product
		regionConfig *config.ChannelRegionConfig
		want         []tiktokReadyCheck
	}{
		{
			name:    "No description",
			product: &models.Product{},
			regionConfig: &config.ChannelRegionConfig{
				Region: consts.RegionUS,
			},
			want: []tiktokReadyCheck{
				&descriptionIsEmpty{},
			},
		},
		{
			name: "Description with Chinese characters - 1",
			product: &models.Product{
				Description: "这是一个包含中文的描述",
			},
			regionConfig: &config.ChannelRegionConfig{
				Region: consts.RegionUS,
			},
			want: []tiktokReadyCheck{
				&descriptionContainChinese{},
			},
		},
		{
			name: "Description with Chinese characters - 2",
			product: &models.Product{
				Description: "AfterShip Feed 牛",
			},
			regionConfig: &config.ChannelRegionConfig{
				Region: consts.RegionUS,
			},
			want: []tiktokReadyCheck{
				&descriptionContainChinese{},
			},
		},
		{
			name: "Description with Chinese characters - 3",
			product: &models.Product{
				Description: "AfterShip Feed 牛",
			},
			regionConfig: &config.ChannelRegionConfig{
				Region: consts.RegionJP,
			},
			want: []tiktokReadyCheck{},
		},
		{
			name: "Description without Chinese characters",
			product: &models.Product{
				Description: "This is a description without Chinese characters 6",
			},
			regionConfig: &config.ChannelRegionConfig{
				Region: consts.RegionUS,
			},
			want: []tiktokReadyCheck{},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			model := &tiktokShopReadyCheckModel{
				product:      tt.product,
				regionConfig: tt.regionConfig,
			}
			got := model.descriptionCheck()
			require.Equal(t, tt.want, got)
		})
	}
}

func TestTitleContainChineseCheck(t *testing.T) {
	tests := []struct {
		name         string
		product      *models.Product
		regionConfig *config.ChannelRegionConfig
		want         []tiktokReadyCheck
	}{
		{
			name:    "No title",
			product: &models.Product{},
			regionConfig: &config.ChannelRegionConfig{
				Region: consts.RegionUS,
			},
			want: []tiktokReadyCheck{},
		},
		{
			name: "Title with Chinese characters - 1",
			product: &models.Product{
				Title: "这是一个包含中文的标题",
			},
			regionConfig: &config.ChannelRegionConfig{
				Region: consts.RegionUS,
			},
			want: []tiktokReadyCheck{
				&titleContainChinese{},
			},
		},
		{
			name: "Title with Chinese characters - 2",
			product: &models.Product{
				Title: "AfterShip Feed 牛",
			},
			regionConfig: &config.ChannelRegionConfig{
				Region: consts.RegionUS,
			},
			want: []tiktokReadyCheck{
				&titleContainChinese{},
			},
		},
		{
			name: "Title with Chinese characters - 3",
			product: &models.Product{
				Title: "AfterShip Feed 牛",
			},
			regionConfig: &config.ChannelRegionConfig{
				Region: consts.RegionJP,
			},
			want: []tiktokReadyCheck{},
		},
		{
			name: "Title without Chinese characters",
			product: &models.Product{
				Title: "This is a title without Chinese characters 6",
			},
			regionConfig: &config.ChannelRegionConfig{
				Region: consts.RegionUS,
			},
			want: []tiktokReadyCheck{},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			model := &tiktokShopReadyCheckModel{
				product:      tt.product,
				regionConfig: tt.regionConfig,
			}
			got := model.titleCheck()
			require.Equal(t, tt.want, got)
		})
	}
}

func TestTiktokShopReadyCheckModel_Check_VariantsEmpty(t *testing.T) {
	model := &tiktokShopReadyCheckModel{
		product: &models.Product{},
	}
	result := model.Check()
	found := false
	for _, r := range result {
		if _, ok := r.(*variantIsEmpty); ok {
			found = true
		}
	}
	require.True(t, found, "should report variantIsEmpty when no variants")
}

func TestTiktokShopReadyCheckModel_Check_CategoriesEmpty(t *testing.T) {
	model := &tiktokShopReadyCheckModel{
		product: &models.Product{Variants: []*models.ProductVariant{{}}},
	}
	result := model.Check()
	found := false
	for _, r := range result {
		if _, ok := r.(*categoryIsEmpty); ok {
			found = true
		}
	}
	require.True(t, found, "should report categoryIsEmpty when no categories")
}

func TestTiktokShopReadyCheckModel_MediaCheck(t *testing.T) {
	// main image empty
	model := &tiktokShopReadyCheckModel{
		product: &models.Product{},
	}
	result := model.mediaCheck()
	found := false
	for _, r := range result {
		if _, ok := r.(*mainImageIsEmpty); ok {
			found = true
		}
	}
	require.True(t, found, "should report mainImageIsEmpty when no media")

	// main image invalid
	model = &tiktokShopReadyCheckModel{
		product: &models.Product{
			Media: []*models.ProductMedia{{URL: "invalid-url"}},
		},
		ttsCdnDomainRegExps: []*regexp.Regexp{regexp.MustCompile(`^https://valid\.domain/`)},
	}
	result = model.mediaCheck()
	found = false
	for _, r := range result {
		if _, ok := r.(*mainImageInvalid); ok {
			found = true
		}
	}
	require.True(t, found, "should report mainImageInvalid when url not match")
}

func TestTiktokShopReadyCheckModel_SizeChartCheck(t *testing.T) {
	model := &tiktokShopReadyCheckModel{
		product: &models.Product{},
		rules: &category.RulesOutput{
			ExternalCategoryID: "cat1",
			Rule: category.Rule{
				SizeChart: &category.SizeChart{IsRequired: true},
			},
		},
	}
	result := model.sizeChartCheck()
	found := false
	for _, r := range result {
		if _, ok := r.(*sizeChartIsEmpty); ok {
			found = true
		}
	}
	require.True(t, found, "should report sizeChartIsEmpty when required and not filled")
}

func TestTiktokShopReadyCheckModel_ComplianceCheck(t *testing.T) {
	model := &tiktokShopReadyCheckModel{
		product: &models.Product{
			Compliance: models.ProductCompliance{
				ResponsiblePersons: []models.ProductComplianceResponsiblePerson{},
				Manufacturers:      []models.ProductComplianceManufacturer{},
			},
		},
		rules: &category.RulesOutput{
			Rule: category.Rule{
				Compliance: &category.CategoryCompliance{
					ResponsiblePerson: &category.CategoryResponsiblePerson{IsRequired: true},
					Manufacturer:      &category.CategoryManufacturer{IsRequired: true},
				},
			},
		},
	}
	result := model.complianceCheck()
	var foundResp, foundManu bool
	for _, r := range result {
		if _, ok := r.(*complianceResponsiblePersonEmpty); ok {
			foundResp = true
		}
		if _, ok := r.(*complianceManufacturerEmpty); ok {
			foundManu = true
		}
	}
	require.True(t, foundResp, "should report complianceResponsiblePersonEmpty when required and not filled")
	require.True(t, foundManu, "should report complianceManufacturerEmpty when required and not filled")
}
