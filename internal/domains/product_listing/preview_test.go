package product_listing

import (
	"context"
	"reflect"
	"testing"

	"cloud.google.com/go/spanner"
	"github.com/go-playground/validator/v10"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"google.golang.org/grpc/codes"

	"github.com/AfterShip/connectors-library/sdks/products_center"
	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/pltf-pd-product-listings/internal/config"
	"github.com/AfterShip/pltf-pd-product-listings/internal/logger"

	"github.com/AfterShip/pltf-pd-product-listings/internal/consts"
	"github.com/AfterShip/pltf-pd-product-listings/internal/datastore"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/common/calculators"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/convert"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/settings"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
)

func Test_previewModel_overWriteProductTitle(t *testing.T) {
	type fields struct {
		service  *serviceImpl
		originPL *ProductListing
		pcp      *products_center.Product
		setting  *storeProductListingSetting
		preview  *ProductListing
	}
	type args struct {
		ctx context.Context
	}

	tests := []struct {
		name   string
		fields fields
		args   args
		check  func(t *testing.T, err error, preview *ProductListing)
	}{
		{
			name: "overwrite product title when autoSyncProductDetailField is true",
			fields: fields{
				service:  &serviceImpl{},
				originPL: &ProductListing{},
				pcp:      &products_center.Product{Title: "New Title"},
				setting: newStoreProductListingSetting(
					settings.Setting{
						ProductSync: models.ProductSync{
							UpdateDetail: models.UpdateDetail{
								AutoSync: consts.StateEnabled,
								Fields:   []string{consts.ProductDetailFieldTitle.String()},
							},
						},
					},
					SyncSettings{
						ProductSyncSetting: ProductSyncSetting{
							Preference: consts.SettingPreferenceStore,
						},
					}),
				preview: &ProductListing{Product: models.Product{Title: "Old Title"}},
			},
			args: args{
				ctx: context.Background(),
			},
			check: func(t *testing.T, err error, preview *ProductListing) {
				require.NoError(t, err)
				require.Equal(t, "New Title", preview.Product.Title)
			},
		},
		{
			name: "do not overwrite product title when autoSyncProductDetailField is false",
			fields: fields{
				service:  &serviceImpl{},
				originPL: &ProductListing{},
				pcp:      &products_center.Product{Title: "New Title"},
				setting: newStoreProductListingSetting(
					settings.Setting{
						ProductSync: models.ProductSync{
							UpdateDetail: models.UpdateDetail{
								AutoSync: consts.StateDisabled,
								Fields:   []string{consts.ProductDetailFieldTitle.String()},
							},
						},
					},
					SyncSettings{
						ProductSyncSetting: ProductSyncSetting{
							Preference: consts.SettingPreferenceStore,
						},
					}),
				preview: &ProductListing{Product: models.Product{Title: "Old Title"}},
			},
			args: args{
				ctx: context.Background(),
			},
			check: func(t *testing.T, err error, preview *ProductListing) {
				require.NoError(t, err)
				require.Equal(t, "Old Title", preview.Product.Title)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := &previewModel{
				originPL: tt.fields.originPL,
				pcp:      tt.fields.pcp,
				setting:  tt.fields.setting,
				preview:  tt.fields.preview,
			}

			err := p.overWriteProductTitle(tt.args.ctx)
			tt.check(t, err, p.preview)
		})
	}
}

func Test_previewModel_overWriteProductDescription(t *testing.T) {
	type fields struct {
		service        *serviceImpl
		originPL       *ProductListing
		pcp            *products_center.Product
		setting        *storeProductListingSetting
		preview        *ProductListing
		convertService convert.Service
	}
	type args struct {
		ctx context.Context
	}

	mockConvertService := new(convert.MockConvertService)
	tests := []struct {
		name   string
		fields fields
		args   args
		mock   func()
		check  func(t *testing.T, err error, preview *ProductListing)
	}{
		{
			name: "overwrite product description when autoSyncProductDetailField is true",
			fields: fields{
				service:  &serviceImpl{},
				originPL: &ProductListing{},
				pcp: &products_center.Product{
					Organization: products_center.Organization{ID: "test_org"},
					Source:       products_center.Source{App: products_center.SourceApp{Key: "test_key", Platform: "magento-2"}},
					Description:  "New Description"},
				setting: newStoreProductListingSetting(
					settings.Setting{
						ProductSync: models.ProductSync{
							UpdateDetail: models.UpdateDetail{
								AutoSync: consts.StateEnabled,
								Fields:   []string{consts.ProductDetailFieldTitle.String(), consts.ProductDetailFieldDescription.String()},
							},
						},
					},
					SyncSettings{
						ProductSyncSetting: ProductSyncSetting{
							Preference: consts.SettingPreferenceStore,
						},
					}),
				preview:        &ProductListing{Product: models.Product{Description: "Old Description"}},
				convertService: mockConvertService,
			},
			args: args{
				ctx: context.Background(),
			},
			mock: func() {
				mockConvertService.On("ConvertDescription", mock.Anything, mock.Anything).Return(convert.DescriptionOutput{Description: "New Description"}, nil)
			},
			check: func(t *testing.T, err error, preview *ProductListing) {
				require.NoError(t, err)
				require.Equal(t, "New Description", preview.Product.Description)
			},
		},
		{
			name: "do not overwrite product description when autoSyncProductDetailField is false",
			fields: fields{
				service:  &serviceImpl{},
				originPL: &ProductListing{},
				pcp:      &products_center.Product{Description: "New Description"},
				setting: newStoreProductListingSetting(
					settings.Setting{
						ProductSync: models.ProductSync{
							UpdateDetail: models.UpdateDetail{
								AutoSync: consts.StateEnabled,
								Fields:   []string{consts.ProductDetailFieldTitle.String()},
							},
						},
					},
					SyncSettings{
						ProductSyncSetting: ProductSyncSetting{
							Preference: consts.SettingPreferenceStore,
						},
					}),
				preview: &ProductListing{Product: models.Product{Description: "Old Description"}},
			},
			args: args{
				ctx: context.Background(),
			},
			mock: func() {},
			check: func(t *testing.T, err error, preview *ProductListing) {
				require.NoError(t, err)
				require.Equal(t, "Old Description", preview.Product.Description)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := &previewModel{
				originPL: tt.fields.originPL,
				pcp:      tt.fields.pcp,
				setting:  tt.fields.setting,
				preview:  tt.fields.preview,
				service:  &serviceImpl{convertService: tt.fields.convertService},
			}

			tt.mock()

			err := p.overWriteProductDescription(tt.args.ctx)
			tt.check(t, err, p.preview)
		})
	}
}

func Test_previewModel_overWriteProductMainMedia(t *testing.T) {
	type fields struct {
		service        *serviceImpl
		originPL       *ProductListing
		pcp            *products_center.Product
		setting        *storeProductListingSetting
		preview        *ProductListing
		convertService convert.Service
	}
	type args struct {
		ctx context.Context
	}

	mockConvertService := new(convert.MockConvertService)

	tests := []struct {
		name   string
		fields fields
		args   args
		mock   func()
		check  func(t *testing.T, err error, preview *ProductListing)
	}{
		{
			name: "overwrite product main media when autoSyncProductDetailField is true",
			fields: fields{
				service:  &serviceImpl{},
				originPL: &ProductListing{},
				pcp:      &products_center.Product{Media: []products_center.Media{{Url: "New Media Url", Type: "image", Position: 1}}},
				setting: newStoreProductListingSetting(
					settings.Setting{
						ProductSync: models.ProductSync{
							UpdateDetail: models.UpdateDetail{
								AutoSync: consts.StateEnabled,
								Fields:   []string{consts.ProductDetailFieldTitle.String(), consts.ProductDetailFieldDescription.String(), consts.ProductDetailFieldMedia.String()},
							},
						},
					},
					SyncSettings{
						ProductSyncSetting: ProductSyncSetting{
							Preference: consts.SettingPreferenceStore,
						},
					}),
				preview:        &ProductListing{Product: models.Product{Media: []*models.ProductMedia{{URL: "Old Media Url", Type: "image", Position: 1}}}},
				convertService: mockConvertService,
			},
			args: args{
				ctx: context.Background(),
			},
			mock: func() {
				mockConvertService.On("ConvertImage", mock.Anything, mock.Anything).Return(convert.ImageOutput{
					ImageURL: "url after convert",
					ImageURI: "uri after convert",
				}, nil).Once()
			},
			check: func(t *testing.T, err error, preview *ProductListing) {
				require.NoError(t, err)
				require.Equal(t, "url after convert", preview.Product.Media[0].URL)
				require.Equal(t, "uri after convert", preview.Product.Media[0].SalesChannelID)
			},
		},
		{
			name: "do not overwrite product main media when autoSyncProductDetailField is false",
			fields: fields{
				service:  &serviceImpl{},
				originPL: &ProductListing{},
				pcp:      &products_center.Product{Media: []products_center.Media{{Url: "New Media Url", Type: "image", Position: 1}}},
				setting: newStoreProductListingSetting(
					settings.Setting{
						ProductSync: models.ProductSync{
							UpdateDetail: models.UpdateDetail{
								AutoSync: consts.StateDisabled,
								Fields:   []string{consts.ProductDetailFieldTitle.String(), consts.ProductDetailFieldDescription.String(), consts.ProductDetailFieldMedia.String()},
							},
						},
					},
					SyncSettings{
						ProductSyncSetting: ProductSyncSetting{
							Preference: consts.SettingPreferenceStore,
						},
					}),
				preview: &ProductListing{Product: models.Product{Media: []*models.ProductMedia{{URL: "Old Media Url", Type: "image", Position: 1}}}},
			},
			args: args{
				ctx: context.Background(),
			},
			mock: func() {},
			check: func(t *testing.T, err error, preview *ProductListing) {
				require.NoError(t, err)
				require.Equal(t, "Old Media Url", preview.Product.Media[0].URL)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := &previewModel{
				originPL: tt.fields.originPL,
				pcp:      tt.fields.pcp,
				setting:  tt.fields.setting,
				preview:  tt.fields.preview,
				service:  &serviceImpl{convertService: tt.fields.convertService},
			}

			tt.mock()
			err := p.overWriteProductMainMedia(tt.args.ctx)
			tt.check(t, err, p.preview)
		})
	}
}

func Test_getProductCenterVariantIDByVariant(t *testing.T) {
	type args struct {
		relations []*ProductListingRelation
		variant   *models.ProductVariant
	}
	tests := []struct {
		name  string
		args  args
		want  *ProductListingRelation
		want1 bool
	}{
		{
			name: "Test case 1: Match found with non-empty variant ID",
			args: args{
				relations: []*ProductListingRelation{
					{
						ProductsCenterVariant: ProductsCenterVariant{
							ID: "123",
						},
						ProductListingVariantID: "abc",
						LinkStatus:              consts.LinkStatusLinked,
					},
					{
						ProductsCenterVariant: ProductsCenterVariant{
							ID: "456",
						},
						ProductListingVariantID: "def",
						LinkStatus:              consts.LinkStatusLinked,
					},
				},
				variant: &models.ProductVariant{ID: "abc"},
			},
			want: &ProductListingRelation{
				ProductsCenterVariant: ProductsCenterVariant{
					ID: "123",
				},
				ProductListingVariantID: "abc",
				LinkStatus:              consts.LinkStatusLinked,
			},
			want1: true,
		},
		{
			name: "Test case 2: Match found with empty variant ID",
			args: args{
				relations: []*ProductListingRelation{
					{
						ProductsCenterVariant: ProductsCenterVariant{
							ID: "123",
						},
						ProductListingVariantID: "abc",
						LinkStatus:              consts.LinkStatusLinked,
						VariantPosition:         1,
					},
					{
						ProductsCenterVariant: ProductsCenterVariant{
							ID: "456",
						},
						ProductListingVariantID: "def",
						LinkStatus:              consts.LinkStatusLinked,
						VariantPosition:         2,
					},
				},
				variant: &models.ProductVariant{ID: "", Position: 1},
			},
			want: &ProductListingRelation{
				ProductsCenterVariant: ProductsCenterVariant{
					ID: "123",
				},
				ProductListingVariantID: "abc",
				LinkStatus:              consts.LinkStatusLinked,
				VariantPosition:         1,
			},
			want1: true,
		},
		{
			name: "Test case 3: No match found",
			args: args{
				relations: []*ProductListingRelation{
					{
						ProductsCenterVariant: ProductsCenterVariant{
							ID: "123",
						},
						ProductListingVariantID: "abc",
						LinkStatus:              consts.LinkStatusLinked,
					},
					{
						ProductsCenterVariant: ProductsCenterVariant{
							ID: "456",
						},
						ProductListingVariantID: "def",
						LinkStatus:              consts.LinkStatusLinked,
					},
				},
				variant: &models.ProductVariant{ID: "ghi"},
			},
			want:  nil,
			want1: false,
		},

		{
			name: "Test case 4: Status is not linked",
			args: args{
				relations: []*ProductListingRelation{
					{
						ProductsCenterVariant: ProductsCenterVariant{
							ID: "",
						},
						ProductListingVariantID: "abc",
						LinkStatus:              consts.LinkStatusUnlink,
					},
					{
						ProductsCenterVariant: ProductsCenterVariant{
							ID: "456",
						},
						ProductListingVariantID: "def",
						LinkStatus:              consts.LinkStatusLinked,
					},
				},
				variant: &models.ProductVariant{ID: "abc"},
			},
			want:  nil,
			want1: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, got1 := getRelationWithProductCenterVariantID(tt.args.relations, tt.args.variant)
			require.Equal(t, tt.want, got)
			require.Equal(t, tt.want1, got1)
		})
	}
}

func Test_convertOptions(t *testing.T) {
	type args struct {
		pcpVariantOptions []products_center.VariantOption
	}
	tests := []struct {
		name string
		args args
		want []*models.ProductVariantOption
	}{
		{
			name: "Test case 1: Convert options",
			args: args{
				pcpVariantOptions: []products_center.VariantOption{
					{Name: "Color", Value: "Red"},
					{Name: "Size", Value: "Large"},
				},
			},
			want: []*models.ProductVariantOption{
				{Name: "Color", Value: "Red"},
				{Name: "Size", Value: "Large"},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := convertOptions(tt.args.pcpVariantOptions); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("convertOptions() = %v, want %v", got, tt.want)
			}
		})
	}
}

// nolint:maintidx
func Test_createVariantFromProductCenterVariant(t *testing.T) {
	type args struct {
		ctx  context.Context
		args createVariantFromProductCenterVariantArgs
	}
	tests := []struct {
		name    string
		args    args
		want    *models.ProductVariant
		want1   *ProductListingRelation
		wantErr bool
	}{
		{
			name: "Test case 1: Create variant from product center variant",
			args: args{
				ctx: context.Background(),
				args: createVariantFromProductCenterVariantArgs{
					position: 1,
					plID:     "test_pl_id",
					pcp: &products_center.Product{
						ID: "test_pcp_id",
						Source: products_center.Source{
							App: products_center.SourceApp{
								Key:      "test_app_key",
								Platform: "test_app_platform",
							},
							ID: "test_source_id",
						},
						Organization: products_center.Organization{
							ID: "test_organization_id",
						},
						ConnectorsProductID: "test_connectors_product_id",
					},
					pcpVariant: &products_center.Variant{
						ID:              "test_variant_id",
						SourceVariantID: "test_source_variant_id",
						Sku:             "test_sku",
						Price: products_center.Price{
							Currency: "USD",
							Amount:   "10.00",
						},
						Cost: products_center.Price{
							Currency: "USD",
							Amount:   "5.00",
						},
						ImageUrl: "test_image_url",
						CompareAtPrice: products_center.Price{
							Currency: "USD",
							Amount:   "15.00",
						},
						Length: products_center.Dimension{
							Unit:  "cm",
							Value: 10,
						},
						Width: products_center.Dimension{
							Unit:  "cm",
							Value: 5,
						},
						Height: products_center.Dimension{
							Unit:  "cm",
							Value: 2,
						},
						Weight: products_center.Dimension{
							Unit:  "kg",
							Value: 1,
						},
						AllowBackorder:    true,
						InventoryQuantity: 10,
						Barcode:           "test_barcode",
						Title:             "test_title",
						Options: []products_center.VariantOption{
							{
								Name:  "Color",
								Value: "Red",
							},
							{
								Name:  "Size",
								Value: "Large",
							},
						},
						FulfillmentService: "test_fulfillment_service",
						RequiresShipping:   true,
					},
				},
			},
			want: &models.ProductVariant{
				ID:                "",
				Position:          1,
				InventoryQuantity: 10,
				Sku:               "test_sku",
				Barcode: models.ProductVariantBarcode{
					Value: "test_barcode",
					Type:  "",
				},
				Title: "test_title",
				Price: models.ProductVariantPrice{
					Currency: "USD",
					Amount:   "10.00",
				},
				Cost: models.ProductVariantPrice{
					Currency: "USD",
					Amount:   "5.00",
				},
				ImageURL: "test_image_url",
				CompareAtPrice: models.ProductVariantPrice{
					Currency: "USD",
					Amount:   "15.00",
				},
				Length: models.ProductVariantShippingSetting{
					Unit:  "cm",
					Value: 10,
				},
				Width: models.ProductVariantShippingSetting{
					Unit:  "cm",
					Value: 5,
				},
				Height: models.ProductVariantShippingSetting{
					Unit:  "cm",
					Value: 2,
				},
				Weight: models.ProductVariantShippingSetting{
					Unit:  "kg",
					Value: 1,
				},
				AllowBackorder: true,
				Options: convertOptions([]products_center.VariantOption{
					{
						Name:  "Color",
						Value: "Red",
					},
					{
						Name:  "Size",
						Value: "Large",
					},
				}),
				FulfillmentService: "test_fulfillment_service",
				RequiresShipping:   true,
			},
			want1: &ProductListingRelation{
				ID: "",
				Organization: models.Organization{
					ID: "test_organization_id",
				},
				SalesChannel: models.SalesChannel{
					StoreKey:      "",
					Platform:      "",
					CountryRegion: "",
				},
				ProductListingID:        "test_pl_id",
				VariantPosition:         1,
				ProductListingVariantID: "",
				SalesChannelVariant: SalesChannelVariant{
					ID:                 "",
					ConnectorProductID: "",
					ProductID:          "",
					Sku:                "",
				},
				ProductsCenterVariant: ProductsCenterVariant{
					ID:                 "test_variant_id",
					ProductID:          "test_pcp_id",
					ConnectorProductID: "test_connectors_product_id",
					Source: ProductsCenterVariantSource{
						StoreKey:  "test_app_key",
						Platform:  "test_app_platform",
						ID:        "test_source_variant_id",
						ProductID: "test_source_id",
						Sku:       "test_sku",
					},
				},
				SyncStatus: consts.SyncStatusUnsync,
				LinkStatus: consts.LinkStatusLinked,
				AllowSync:  consts.AllowSyncEnabled,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			va := validator.New()
			p := &previewModel{
				validator: va,
			}
			got, got1, err := p.createVariantFromProductCenterVariant(tt.args.ctx, tt.args.args)
			if (err != nil) != tt.wantErr {
				t.Errorf("createVariantFromProductCenterVariant() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("createVariantFromProductCenterVariant() got = %v, want %v", got, tt.want)
			}
			if !reflect.DeepEqual(got1, tt.want1) {
				t.Errorf("createVariantFromProductCenterVariant() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}

func Test_getVariantsPreview(t *testing.T) {
	type args struct {
		ctx  context.Context
		args getVariantsPreviewArgs
	}
	tests := []struct {
		name    string
		args    args
		want    []*models.ProductVariant
		want1   []*ProductListingRelation
		wantErr bool
	}{
		{
			name: "Test case 1: Add and remove variants",
			args: args{
				ctx: context.Background(),
				args: getVariantsPreviewArgs{
					productListingID: "",
					variants: []*models.ProductVariant{
						{
							ID:       "abc",
							Position: 1,
							Options:  make([]*models.ProductVariantOption, 0),
						},
						{
							ID:       "def",
							Position: 2,
							Options:  make([]*models.ProductVariantOption, 0),
						},
					},
					relations: []*ProductListingRelation{
						{
							ID:                      "relation_1",
							ProductListingVariantID: "abc",
							VariantPosition:         1,
							ProductsCenterVariant: ProductsCenterVariant{
								ID: "123",
							},
							AllowSync:  consts.AllowSyncEnabled,
							SyncStatus: consts.SyncStatusUnsync,
							LinkStatus: consts.LinkStatusLinked,
						},
						{
							ID:                      "relation_2",
							ProductListingVariantID: "def",
							VariantPosition:         2,
							ProductsCenterVariant: ProductsCenterVariant{
								ID: "456",
							},
							AllowSync:  consts.AllowSyncEnabled,
							SyncStatus: consts.SyncStatusUnsync,
							LinkStatus: consts.LinkStatusLinked,
						},
					},
					pcp: &products_center.Product{
						Variants: []products_center.Variant{
							{
								ID:       "123",
								Position: 1,
							},
							{
								ID:       "789",
								Position: 2,
							},
						},
					},
				},
			},
			want: []*models.ProductVariant{
				{
					ID:       "abc",
					Position: 1,
					Options:  make([]*models.ProductVariantOption, 0),
				},
				{
					ID:       "",
					Position: 2,
					Options:  make([]*models.ProductVariantOption, 0),
				},
			},
			want1: []*ProductListingRelation{
				{
					ID:                      "relation_1",
					ProductListingVariantID: "abc",
					VariantPosition:         1,
					ProductsCenterVariant: ProductsCenterVariant{
						ID: "123",
					},
					AllowSync:  consts.AllowSyncEnabled,
					SyncStatus: consts.SyncStatusUnsync,
					LinkStatus: consts.LinkStatusLinked,
				},
				{
					ProductListingVariantID: "",
					VariantPosition:         2,
					ProductsCenterVariant: ProductsCenterVariant{
						ID: "789",
					},
					AllowSync:  consts.AllowSyncEnabled,
					SyncStatus: consts.SyncStatusUnsync,
					LinkStatus: consts.LinkStatusLinked,
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			va := validator.New()
			p := &previewModel{
				validator: va,
			}
			got, got1, err := p.getVariantsPreview(tt.args.ctx, tt.args.args)
			if (err != nil) != tt.wantErr {
				t.Errorf("getVariantsPreview() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			for i := range got {
				require.EqualValues(t, got[i], tt.want[i])
				require.EqualValues(t, got1[i], tt.want1[i])
			}
		})
	}
}

// nolint:maintidx
func Test_previewModel_overWriteVariantAndRelation(t *testing.T) {
	// Init Spanner Client
	loadConfig(t)

	repo := newRepoConductor(datastore.Get().SpannerCli)

	type fields struct {
		originPL          *ProductListing
		pcp               *products_center.Product
		setting           *storeProductListingSetting
		preview           *ProductListing
		convertService    convert.Service
		calculatorService calculators.Service
		repo              *repoConductor
	}
	type args struct {
		ctx context.Context
	}

	mockConvertService := new(convert.MockConvertService)
	mockCalculatorsService := new(calculators.MockCalculatorsService)

	tests := []struct {
		name   string
		fields fields
		args   args
		mock   func()
		check  func(preview *ProductListing, err error)
	}{
		// TODO: Add test cases.
		{
			name: "Test case 1: no need overwrite variant",
			fields: fields{
				originPL: &ProductListing{},
				pcp:      &products_center.Product{},
				setting: newStoreProductListingSetting(
					settings.Setting{
						ProductSync: models.ProductSync{
							UpdateVariants: models.UpdateVariants{
								AutoSync: consts.StateDisabled,
							},
						},
					},
					SyncSettings{
						ProductSyncSetting: ProductSyncSetting{
							Preference: consts.SettingPreferenceStore,
						},
					}),
				preview: &ProductListing{},
			},
			args: args{
				ctx: context.Background(),
			},
			mock: func() {},
			check: func(preview *ProductListing, err error) {
				require.NoError(t, err)
			},
		},
		{
			name: "Test case 2: overwrite variant",
			fields: fields{
				originPL: &ProductListing{
					ID: "07251bb931674fa18a5d0f6ed95d15fa",
					ProductsCenterProduct: ProductsCenterProduct{
						ID: "07251bb931674fa18a5d0f6ed95d15fa",
					},
					Product: models.Product{
						Variants: []*models.ProductVariant{
							{
								ID:       "abc",
								Position: 1,
								Options: []*models.ProductVariantOption{
									{
										Name:  "Color",
										Value: "Red",
									},
								},
								ImageURL: "test_image_url",
								Length: models.ProductVariantShippingSetting{
									Unit:  "cm",
									Value: 10,
								},
								Width: models.ProductVariantShippingSetting{
									Unit:  "cm",
									Value: 5,
								},
								Height: models.ProductVariantShippingSetting{
									Unit:  "cm",
									Value: 2,
								},
								Weight: models.ProductVariantShippingSetting{
									Unit:  "kg",
									Value: 1,
								},
								Sku:                "test_sku",
								AllowBackorder:     true,
								FulfillmentService: "test_fulfillment_service",
								RequiresShipping:   true,
							},
						},
					},
					Relations: []*ProductListingRelation{
						{
							ID:                      "relation_1",
							ProductListingVariantID: "abc",
							VariantPosition:         1,
							ProductsCenterVariant: ProductsCenterVariant{
								ID: "123",
							},
							LinkStatus: consts.LinkStatusLinked,
						},
					},
				},
				pcp: &products_center.Product{
					Variants: []products_center.Variant{
						{
							ID:       "123",
							Position: 1,
							Options: []products_center.VariantOption{
								{
									Name:  "Color",
									Value: "Black",
								},
							},
							ImageUrl:           "new_image_url",
							Sku:                "test_sku",
							AllowBackorder:     true,
							FulfillmentService: "test_fulfillment_service",
							RequiresShipping:   true,
						},
					},
				},
				setting: newStoreProductListingSetting(
					settings.Setting{
						ProductSync: models.ProductSync{
							UpdateVariants: models.UpdateVariants{
								AutoSync: consts.StateEnabled,
							},
						},
					},
					SyncSettings{
						ProductSyncSetting: ProductSyncSetting{
							Preference: consts.SettingPreferenceStore,
						},
					}),
				preview:           &ProductListing{},
				convertService:    mockConvertService,
				calculatorService: mockCalculatorsService,
				repo:              repo,
			},
			args: args{
				ctx: context.Background(),
			},
			mock: func() {
				mockConvertService.On("ConvertImage", mock.Anything, mock.Anything).Return(convert.ImageOutput{
					ImageURL: "new_image_url",
					ImageURI: "new_image_uri",
				}, nil)

				// Create product listing
				err := repo.create(context.Background(), &conductorProductListing{
					ProductListingDBModel: productListingDBModel{
						ProductListingID: "07251bb931674fa18a5d0f6ed95d15fa",
						Product: models.Product{
							Variants: []*models.ProductVariant{
								{
									ID:                "abc",
									Position:          1,
									InventoryQuantity: 0,
									Sku:               "test_sku",
									Barcode:           models.ProductVariantBarcode{},
									Title:             "",
									Price:             models.ProductVariantPrice{},
									Cost:              models.ProductVariantPrice{},
									ImageURL:          "test_image_url",
									CompareAtPrice:    models.ProductVariantPrice{},
									Length: models.ProductVariantShippingSetting{
										Unit:  "cm",
										Value: 10,
									},
									Width: models.ProductVariantShippingSetting{
										Unit:  "cm",
										Value: 5,
									},
									Height: models.ProductVariantShippingSetting{
										Unit:  "cm",
										Value: 2,
									},
									Weight: models.ProductVariantShippingSetting{
										Unit:  "kg",
										Value: 1,
									},
									AllowBackorder: true,
									Options: []*models.ProductVariantOption{
										{
											Name:  "Color",
											Value: "Red",
										},
									},
									FulfillmentService: "test_fulfillment_service",
									RequiresShipping:   true,
								},
							},
						},
						ProductsCenterProductID: "07251bb931674fa18a5d0f6ed95d15fa",
					},
					Relations: []*relationDBModel{
						{
							ProductListingRelationID: "relation_1",
							ProductListingID:         "07251bb931674fa18a5d0f6ed95d15fa",
							ProductListingVariantID:  "abc",
							ProductsCenterVariantID:  "123",
							LinkStatus:               consts.LinkStatusLinked,
						},
					},
				})
				if err != nil && spanner.ErrCode(err).String() != codes.AlreadyExists.String() {
					t.Fatal(err)
				}
			},
			check: func(preview *ProductListing, err error) {
				require.NoError(t, err)
				expected := &ProductListing{
					Product: models.Product{
						Variants: []*models.ProductVariant{
							{
								ID:       "abc",
								Position: 1,
								Options: []*models.ProductVariantOption{
									{
										Name:  "Color",
										Value: "Black",
									},
								},
								ImageURL: "new_image_url",
								Length: models.ProductVariantShippingSetting{
									Unit:  "cm",
									Value: 10,
								},
								Width: models.ProductVariantShippingSetting{
									Unit:  "cm",
									Value: 5,
								},
								Height: models.ProductVariantShippingSetting{
									Unit:  "cm",
									Value: 2,
								},
								Weight: models.ProductVariantShippingSetting{
									Unit:  "kg",
									Value: 1,
								},
								Sku:                "test_sku",
								AllowBackorder:     true,
								FulfillmentService: "test_fulfillment_service",
								RequiresShipping:   true,
							},
						},
					},
					// nolint:govet
					State: consts.ProductListingProductStatePending,
					// nolint:govet
					LinkStatus: consts.LinkStatusLinked,
					// nolint:govet
					SyncStatus: consts.SyncStatusUnsync,
					Relations: []*ProductListingRelation{
						{
							ID:                      "relation_1",
							ProductListingVariantID: "abc",
							ProductListingID:        "07251bb931674fa18a5d0f6ed95d15fa",
							VariantPosition:         1,
							ProductsCenterVariant: ProductsCenterVariant{
								ID: "123",
							},
							LinkStatus:   consts.LinkStatusLinked,
							SyncStatus:   consts.SyncStatusUnsync,
							LastSyncedAt: types.NullDatetime,
							LastLinkedAt: types.NullDatetime,
							DeletedAt:    types.NullDatetime,
						},
					},
				}
				require.EqualValues(t, expected.Product.Variants, preview.Product.Variants)
				require.EqualValues(t, expected.Relations[0].ID, preview.Relations[0].ID)
				require.EqualValues(t, expected.Relations[0].ProductListingVariantID, preview.Relations[0].ProductListingVariantID)
				require.EqualValues(t, expected.Relations[0].ProductListingID, preview.Relations[0].ProductListingID)
				require.EqualValues(t, expected.Relations[0].VariantPosition, preview.Relations[0].VariantPosition)
				require.EqualValues(t, expected.Relations[0].ProductsCenterVariant.ID, preview.Relations[0].ProductsCenterVariant.ID)
				require.EqualValues(t, expected.Relations[0].LinkStatus, preview.Relations[0].LinkStatus)
				require.EqualValues(t, expected.Relations[0].SyncStatus, preview.Relations[0].SyncStatus)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := &previewModel{
				validator: validator.New(),
				originPL:  tt.fields.originPL,
				pcp:       tt.fields.pcp,
				setting:   tt.fields.setting,
				preview:   tt.fields.preview,
				service:   &serviceImpl{convertService: tt.fields.convertService, repo: repo, calculatorService: tt.fields.calculatorService},
			}
			tt.mock()
			err := p.overWriteVariantAndRelation(tt.args.ctx)
			_ = p.overWriteVariantDetail(tt.args.ctx)
			tt.check(p.preview, err)
		})
	}
}

// nolint:maintidx
func Test_previewModel_overWriteVariantPrice(t *testing.T) {
	mockCalculatorService := new(calculators.MockCalculatorsService)

	type fields struct {
		calculatorService           calculators.Service
		originPL                    *ProductListing
		pcp                         *products_center.Product
		linkedRelations             []*ProductListingRelation
		linkedProductsCenterProduct map[string]*products_center.Product
		setting                     *storeProductListingSetting
		preview                     *ProductListing
	}
	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		mock   func()
		check  func(preview *ProductListing, err error)
	}{
		{
			name: "Test case 1: Overwrite variant price with valid data",
			fields: fields{
				calculatorService: mockCalculatorService,
				originPL: &ProductListing{
					Product: models.Product{
						Variants: []*models.ProductVariant{
							{
								ID: "abc",
								Price: models.ProductVariantPrice{
									Currency: "USD",
									Amount:   "10.00",
								},
							},
						},
					},
				},
				pcp: &products_center.Product{
					ID: "456",
					Variants: []products_center.Variant{
						{
							ID: "123",
							Price: products_center.Price{
								Currency: "USD",
								Amount:   "20.01",
							},
							CompareAtPrice: products_center.Price{
								Currency: "USD",
								Amount:   "30.00",
							},
						},
					},
				},
				linkedProductsCenterProduct: map[string]*products_center.Product{
					"456": &products_center.Product{
						ID: "456",
						Variants: []products_center.Variant{
							{
								ID: "123",
								Price: products_center.Price{
									Currency: "USD",
									Amount:   "20.01",
								},
								CompareAtPrice: products_center.Price{
									Currency: "USD",
									Amount:   "30.00",
								},
							},
						},
					},
				},
				setting: newStoreProductListingSetting(
					settings.Setting{
						PriceSync: models.PriceSync{
							AutoSync: consts.StateEnabled,
						},
					},
					SyncSettings{
						PriceSyncSetting: PriceSyncSetting{
							Preference: consts.SettingPreferenceStore,
						},
					}),
				preview: &ProductListing{
					Product: models.Product{
						Variants: []*models.ProductVariant{
							{
								ID: "abc",
								Price: models.ProductVariantPrice{
									Currency: "USD",
									Amount:   "10.00",
								},
								CompareAtPrice: models.ProductVariantPrice{
									Currency: "USD",
									Amount:   "15.00",
								},
							},
						},
					},
					Relations: []*ProductListingRelation{
						{
							ProductListingVariantID: "abc",
							ProductsCenterVariant: ProductsCenterVariant{
								ID:        "123",
								ProductID: "456",
							},
							LinkStatus: consts.LinkStatusLinked,
						},
					},
				},
				linkedRelations: []*ProductListingRelation{
					{
						ProductListingVariantID: "abc",
						ProductsCenterVariant: ProductsCenterVariant{
							ID:        "123",
							ProductID: "456",
						},
						LinkStatus: consts.LinkStatusLinked,
					},
				},
			},
			args: args{
				ctx: context.Background(),
			},
			mock: func() {
				mockCalculatorService.On("CalculatePrices", mock.Anything, mock.Anything).Return(
					&calculators.CalculatePricesOutput{
						ProductsCenterProduct: &calculators.CalculatePriceProductsCenterProductOutput{
							ID: "456",
							Variants: []calculators.CalculatePricesVariantOutput{
								{
									ID:                        "123",
									Status:                    consts.CalculateSuccess,
									CalculatedPrice:           decimal.NewFromFloat(20.01),
									CalculatedComparedAtPrice: decimal.NewFromFloat(30.00),
								},
							},
						},
					}, nil).Once()
			},
			check: func(preview *ProductListing, err error) {
				require.NoError(t, err)
				require.EqualValues(t, "20.01", preview.Product.Variants[0].Price.Amount)
				require.EqualValues(t, "30", preview.Product.Variants[0].CompareAtPrice.Amount)
			},
		},
		{
			name: "Test case 2: Calculate price failed, ignore error",
			fields: fields{
				calculatorService: mockCalculatorService,
				originPL: &ProductListing{
					Product: models.Product{
						Variants: []*models.ProductVariant{
							{
								ID: "abc",
								Price: models.ProductVariantPrice{
									Currency: "USD",
									Amount:   "10.00",
								},
							},
						},
					},
				},
				pcp: &products_center.Product{
					ID: "456",
					Variants: []products_center.Variant{
						{
							ID: "123",
							Price: products_center.Price{
								Currency: "USD",
								Amount:   "20.01",
							},
							CompareAtPrice: products_center.Price{
								Currency: "USD",
								Amount:   "30.00",
							},
						},
					},
				},
				linkedProductsCenterProduct: map[string]*products_center.Product{
					"456": &products_center.Product{
						ID: "456",
						Variants: []products_center.Variant{
							{
								ID: "123",
								Price: products_center.Price{
									Currency: "USD",
									Amount:   "20.01",
								},
								CompareAtPrice: products_center.Price{
									Currency: "USD",
									Amount:   "30.00",
								},
							},
						},
					},
				},
				setting: newStoreProductListingSetting(
					settings.Setting{
						PriceSync: models.PriceSync{
							AutoSync: consts.StateEnabled,
						},
					},
					SyncSettings{
						PriceSyncSetting: PriceSyncSetting{
							Preference: consts.SettingPreferenceStore,
						},
					}),
				preview: &ProductListing{
					Product: models.Product{
						Variants: []*models.ProductVariant{
							{
								ID: "abc",
								Price: models.ProductVariantPrice{
									Currency: "USD",
									Amount:   "10.00",
								},
								CompareAtPrice: models.ProductVariantPrice{
									Currency: "USD",
									Amount:   "15.00",
								},
							},
						},
					},
					Relations: []*ProductListingRelation{
						{
							ProductListingVariantID: "abc",
							ProductsCenterVariant: ProductsCenterVariant{
								ID:        "123",
								ProductID: "456",
							},
							LinkStatus: consts.LinkStatusLinked,
						},
					},
				},
				linkedRelations: []*ProductListingRelation{
					{
						ProductListingVariantID: "abc",
						ProductsCenterVariant: ProductsCenterVariant{
							ID:        "123",
							ProductID: "456",
						},
						LinkStatus: consts.LinkStatusLinked,
					},
				},
			},
			args: args{
				ctx: context.Background(),
			},
			mock: func() {
				mockCalculatorService.On("CalculatePrices", mock.Anything, mock.Anything).Return(
					&calculators.CalculatePricesOutput{
						ProductsCenterProduct: &calculators.CalculatePriceProductsCenterProductOutput{
							ID: "456",
							Variants: []calculators.CalculatePricesVariantOutput{
								{
									ID:                        "123",
									Status:                    consts.CalculateFailed,
									CalculatedPrice:           decimal.NewFromFloat(20.01),
									CalculatedComparedAtPrice: decimal.NewFromFloat(30.00),
								},
							},
						},
					}, nil).Once()
			},
			check: func(preview *ProductListing, err error) {
				require.NoError(t, err)
				require.EqualValues(t, "20.01", preview.Product.Variants[0].Price.Amount)
				require.EqualValues(t, "30", preview.Product.Variants[0].CompareAtPrice.Amount)
			},
		},
		{
			name: "Test case 3: Auto sync is disabled",
			fields: fields{
				calculatorService: mockCalculatorService,
				originPL: &ProductListing{
					Product: models.Product{
						Variants: []*models.ProductVariant{
							{
								ID: "abc",
								Price: models.ProductVariantPrice{
									Currency: "USD",
									Amount:   "10.00",
								},
								CompareAtPrice: models.ProductVariantPrice{
									Currency: "USD",
									Amount:   "15.00",
								},
							},
						},
					},
				},
				pcp: &products_center.Product{
					ID: "456",
					Variants: []products_center.Variant{
						{
							ID: "123",
							Price: products_center.Price{
								Currency: "USD",
								Amount:   "20.01",
							},
							CompareAtPrice: products_center.Price{
								Currency: "USD",
								Amount:   "30.00",
							},
						},
					},
				},
				linkedProductsCenterProduct: map[string]*products_center.Product{
					"456": &products_center.Product{
						ID: "456",
						Variants: []products_center.Variant{
							{
								ID: "123",
								Price: products_center.Price{
									Currency: "USD",
									Amount:   "20.01",
								},
								CompareAtPrice: products_center.Price{
									Currency: "USD",
									Amount:   "30.00",
								},
							},
						},
					},
				},
				setting: newStoreProductListingSetting(
					settings.Setting{
						PriceSync: models.PriceSync{
							AutoSync: consts.StateDisabled,
						},
					},
					SyncSettings{
						PriceSyncSetting: PriceSyncSetting{
							Preference: consts.SettingPreferenceStore,
						},
					}),
				preview: &ProductListing{
					Product: models.Product{
						Variants: []*models.ProductVariant{
							{
								ID: "abc",
								Price: models.ProductVariantPrice{
									Currency: "USD",
									Amount:   "10.00",
								},
								CompareAtPrice: models.ProductVariantPrice{
									Currency: "USD",
									Amount:   "15.00",
								},
							},
						},
					},
					Relations: []*ProductListingRelation{
						{
							ProductListingVariantID: "abc",
							ProductsCenterVariant: ProductsCenterVariant{
								ID:        "123",
								ProductID: "456",
							},
							LinkStatus: consts.LinkStatusLinked,
						},
					},
				},
				linkedRelations: []*ProductListingRelation{
					{
						ProductListingVariantID: "abc",
						ProductsCenterVariant: ProductsCenterVariant{
							ID:        "123",
							ProductID: "456",
						},
						LinkStatus: consts.LinkStatusLinked,
					},
				},
			},
			args: args{
				ctx: context.Background(),
			},
			mock: func() {
				mockCalculatorService.On("CalculatePrices", mock.Anything, mock.Anything).Return(
					&calculators.CalculatePricesOutput{
						ProductsCenterProduct: &calculators.CalculatePriceProductsCenterProductOutput{
							ID: "456",
							Variants: []calculators.CalculatePricesVariantOutput{
								{
									ID:                        "123",
									Status:                    consts.CalculateSuccess,
									CalculatedPrice:           decimal.NewFromFloat(20.01),
									CalculatedComparedAtPrice: decimal.NewFromFloat(30.00),
								},
							},
						},
					}, nil).Once()
			},
			check: func(preview *ProductListing, err error) {
				require.NoError(t, err)
				require.EqualValues(t, "10.00", preview.Product.Variants[0].Price.Amount)
				require.EqualValues(t, "15.00", preview.Product.Variants[0].CompareAtPrice.Amount)
			},
		},
		{
			name: "Test case 4: Relation is not linked, ignore",
			fields: fields{
				calculatorService: mockCalculatorService,
				originPL: &ProductListing{
					Product: models.Product{
						Variants: []*models.ProductVariant{
							{
								ID: "abc",
								Price: models.ProductVariantPrice{
									Currency: "USD",
									Amount:   "10.00",
								},
							},
						},
					},
				},
				pcp: &products_center.Product{
					Variants: []products_center.Variant{
						{
							ID: "123",
							Price: products_center.Price{
								Currency: "USD",
								Amount:   "20.01",
							},
							CompareAtPrice: products_center.Price{
								Currency: "USD",
								Amount:   "30.00",
							},
						},
					},
				},
				setting: newStoreProductListingSetting(
					settings.Setting{
						PriceSync: models.PriceSync{
							AutoSync: consts.StateEnabled,
						},
						ProductSync: models.ProductSync{
							UpdateVariants: models.UpdateVariants{
								AutoSync: consts.StateDisabled,
							},
						},
					},
					SyncSettings{
						PriceSyncSetting: PriceSyncSetting{
							Preference: consts.SettingPreferenceStore,
						},
						ProductSyncSetting: ProductSyncSetting{
							Preference: consts.SettingPreferenceStore,
						},
					}),
				preview: &ProductListing{
					Product: models.Product{
						Variants: []*models.ProductVariant{
							{
								ID: "abc",
								Price: models.ProductVariantPrice{
									Currency: "USD",
									Amount:   "10.00",
								},
								CompareAtPrice: models.ProductVariantPrice{
									Currency: "USD",
									Amount:   "15.00",
								},
							},
						},
					},
					Relations: []*ProductListingRelation{
						{
							ProductListingVariantID: "abc",
							LinkStatus:              consts.LinkStatusUnlink,
						},
					},
				},
			},
			args: args{
				ctx: context.Background(),
			},
			mock: func() {
				mockCalculatorService.On("CalculatePrices", mock.Anything, mock.Anything).Return(
					&calculators.CalculatePricesOutput{
						ProductsCenterProduct: &calculators.CalculatePriceProductsCenterProductOutput{
							Variants: []calculators.CalculatePricesVariantOutput{
								{
									ID:                        "123",
									Status:                    consts.CalculateSuccess,
									CalculatedPrice:           decimal.NewFromFloat(20.01),
									CalculatedComparedAtPrice: decimal.NewFromFloat(30.00),
								},
							},
						},
					}, nil).Once()
			},
			check: func(preview *ProductListing, err error) {
				require.NoError(t, err)
				require.EqualValues(t, "10.00", preview.Product.Variants[0].Price.Amount)
				require.EqualValues(t, "15.00", preview.Product.Variants[0].CompareAtPrice.Amount)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.mock()
			p := &previewModel{
				service: &serviceImpl{
					logger:            logger.Get(),
					calculatorService: tt.fields.calculatorService,
				},
				originPL:                    tt.fields.originPL,
				pcp:                         tt.fields.pcp,
				setting:                     tt.fields.setting,
				preview:                     tt.fields.preview,
				linkedProductsCenterProduct: tt.fields.linkedProductsCenterProduct,
				linkedRelations:             tt.fields.linkedRelations,
			}
			err := p.overWriteVariantPrice(tt.args.ctx)
			tt.check(p.preview, err)
		})
	}
}

// nolint:maintidx
func Test_previewModel_overWriteVariantInventory(t *testing.T) {
	mockCalculatorService := new(calculators.MockCalculatorsService)
	type fields struct {
		calculatorService           calculators.Service
		originPL                    *ProductListing
		pcp                         *products_center.Product
		setting                     *storeProductListingSetting
		preview                     *ProductListing
		linkedRelations             []*ProductListingRelation
		linkedProductsCenterProduct map[string]*products_center.Product
	}
	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		mock   func()
		check  func(preview *ProductListing, err error)
	}{
		{
			name: "Test case 1: Overwrite variant inventory with valid data",
			fields: fields{
				calculatorService: mockCalculatorService,
				originPL: &ProductListing{
					Product: models.Product{
						Variants: []*models.ProductVariant{
							{
								ID:                "abc",
								InventoryQuantity: 10,
							},
						},
					},
				},
				pcp: &products_center.Product{
					ID: "456",
					Variants: []products_center.Variant{
						{
							ID:                "123",
							InventoryQuantity: 20,
						},
					},
				},
				linkedProductsCenterProduct: map[string]*products_center.Product{
					"456": &products_center.Product{
						ID: "456",
						Variants: []products_center.Variant{
							{
								ID:                "123",
								InventoryQuantity: 20,
							},
						},
					},
				},
				setting: newStoreProductListingSetting(
					settings.Setting{
						InventorySync: models.InventorySync{
							AutoSync: consts.StateEnabled,
						},
					},
					SyncSettings{
						InventorySyncSetting: InventorySyncSetting{
							Preference: consts.SettingPreferenceStore,
						},
					}),
				preview: &ProductListing{
					Product: models.Product{
						Variants: []*models.ProductVariant{
							{
								ID:                "abc",
								InventoryQuantity: 10,
							},
						},
					},
					Relations: []*ProductListingRelation{
						{
							ProductListingVariantID: "abc",
							ProductsCenterVariant: ProductsCenterVariant{
								ID:        "123",
								ProductID: "456",
							},
							LinkStatus: consts.LinkStatusLinked,
						},
					},
				},
				linkedRelations: []*ProductListingRelation{
					{
						ProductListingVariantID: "abc",
						ProductsCenterVariant: ProductsCenterVariant{
							ID:        "123",
							ProductID: "456",
						},
						LinkStatus: consts.LinkStatusLinked,
					},
				},
			},
			args: args{
				ctx: context.Background(),
			},
			mock: func() {
				mockCalculatorService.On("CalculateAvailableQuantities", mock.Anything, mock.Anything).Return(
					&calculators.CalculateAvailableQuantitiesOutput{
						ProductsCenterProduct: &calculators.ProductsCenterProductOut{
							ID: "456",
							Variants: []calculators.CalculateAvailableQuantitiesVariantOutput{
								{
									ID:                          "123",
									CalculatedAvailableQuantity: 20,
									Status:                      consts.CalculateSuccess,
								},
							},
						},
					}, nil).Once()
			},
			check: func(preview *ProductListing, err error) {
				require.NoError(t, err)
				require.EqualValues(t, 20, preview.Product.Variants[0].InventoryQuantity)
			},
		},
		{
			name: "Test case 2: Calculate inventory failed",
			fields: fields{
				calculatorService: mockCalculatorService,
				originPL: &ProductListing{
					Product: models.Product{
						Variants: []*models.ProductVariant{
							{
								ID:                "abc",
								InventoryQuantity: 10,
							},
						},
					},
				},
				pcp: &products_center.Product{
					ID: "456",
					Variants: []products_center.Variant{
						{
							ID:                "123",
							InventoryQuantity: 20,
						},
					},
				},
				linkedProductsCenterProduct: map[string]*products_center.Product{
					"456": &products_center.Product{
						ID: "456",
						Variants: []products_center.Variant{
							{
								ID:                "123",
								InventoryQuantity: 20,
							},
						},
					},
				},
				setting: newStoreProductListingSetting(
					settings.Setting{
						InventorySync: models.InventorySync{
							AutoSync: consts.StateEnabled,
						},
					},
					SyncSettings{
						InventorySyncSetting: InventorySyncSetting{
							Preference: consts.SettingPreferenceStore,
						},
					}),
				preview: &ProductListing{
					Product: models.Product{
						Variants: []*models.ProductVariant{
							{
								ID:                "abc",
								InventoryQuantity: 10,
							},
						},
					},
					Relations: []*ProductListingRelation{
						{
							ProductListingVariantID: "abc",
							ProductsCenterVariant: ProductsCenterVariant{
								ID:        "123",
								ProductID: "456",
							},
							LinkStatus: consts.LinkStatusLinked,
						},
					},
				},
				linkedRelations: []*ProductListingRelation{
					{
						ProductListingVariantID: "abc",
						ProductsCenterVariant: ProductsCenterVariant{
							ID:        "123",
							ProductID: "456",
						},
						LinkStatus: consts.LinkStatusLinked,
					},
				},
			},
			args: args{
				ctx: context.Background(),
			},
			mock: func() {
				mockCalculatorService.On("CalculateAvailableQuantities", mock.Anything, mock.Anything).Return(
					&calculators.CalculateAvailableQuantitiesOutput{
						ProductsCenterProduct: &calculators.ProductsCenterProductOut{
							ID: "456",
							Variants: []calculators.CalculateAvailableQuantitiesVariantOutput{
								{
									ID:                          "123",
									CalculatedAvailableQuantity: 20,
									Status:                      consts.CalculateFailed,
								},
							},
						},
					}, nil).Once()
			},
			check: func(preview *ProductListing, err error) {
				require.Error(t, err)
			},
		},
		{
			name: "Test case 3: auto sync is disabled",
			fields: fields{
				calculatorService: mockCalculatorService,
				originPL: &ProductListing{
					Product: models.Product{
						Variants: []*models.ProductVariant{
							{
								ID:                "abc",
								InventoryQuantity: 10,
							},
						},
					},
				},
				pcp: &products_center.Product{
					ID: "456",
					Variants: []products_center.Variant{
						{
							ID:                "123",
							InventoryQuantity: 20,
						},
					},
				},
				linkedProductsCenterProduct: map[string]*products_center.Product{
					"456": &products_center.Product{
						ID: "456",
						Variants: []products_center.Variant{
							{
								ID:                "123",
								InventoryQuantity: 20,
							},
						},
					},
				},
				setting: newStoreProductListingSetting(
					settings.Setting{
						InventorySync: models.InventorySync{
							AutoSync: consts.StateDisabled,
						},
					},
					SyncSettings{
						InventorySyncSetting: InventorySyncSetting{
							Preference: consts.SettingPreferenceStore,
						},
					}),
				preview: &ProductListing{
					Product: models.Product{
						Variants: []*models.ProductVariant{
							{
								ID:                "abc",
								InventoryQuantity: 20,
							},
						},
					},
					Relations: []*ProductListingRelation{
						{
							ProductListingVariantID: "abc",
							ProductsCenterVariant: ProductsCenterVariant{
								ID:        "123",
								ProductID: "456",
							},
							LinkStatus: consts.LinkStatusLinked,
						},
					},
				},
				linkedRelations: []*ProductListingRelation{
					{
						ProductListingVariantID: "abc",
						ProductsCenterVariant: ProductsCenterVariant{
							ID:        "123",
							ProductID: "456",
						},
						LinkStatus: consts.LinkStatusLinked,
					},
				},
			},
			args: args{
				ctx: context.Background(),
			},
			mock: func() {
				mockCalculatorService.On("CalculateAvailableQuantities", mock.Anything, mock.Anything).Return(
					&calculators.CalculateAvailableQuantitiesOutput{
						ProductsCenterProduct: &calculators.ProductsCenterProductOut{
							ID: "456",
							Variants: []calculators.CalculateAvailableQuantitiesVariantOutput{
								{
									ID:                          "123",
									CalculatedAvailableQuantity: 20,
									Status:                      consts.CalculateSuccess,
								},
							},
						},
					}, nil).Once()
			},
			check: func(preview *ProductListing, err error) {
				require.NoError(t, err)
				require.EqualValues(t, 10, preview.Product.Variants[0].InventoryQuantity)
			},
		},
		{
			name: "Test case 4: relation not found, ignore",
			fields: fields{
				calculatorService: mockCalculatorService,
				originPL: &ProductListing{
					Product: models.Product{
						Variants: []*models.ProductVariant{
							{
								ID:                "abc",
								InventoryQuantity: 10,
							},
							{
								ID:                "def",
								InventoryQuantity: 20,
							},
						},
					},
				},
				pcp: &products_center.Product{
					ID: "456",
					Variants: []products_center.Variant{
						{
							ID:                "123",
							InventoryQuantity: 20,
						},
					},
				},
				linkedProductsCenterProduct: map[string]*products_center.Product{
					"456": &products_center.Product{
						ID: "456",
						Variants: []products_center.Variant{
							{
								ID:                "123",
								InventoryQuantity: 20,
							},
						},
					},
				},
				setting: newStoreProductListingSetting(
					settings.Setting{
						InventorySync: models.InventorySync{
							AutoSync: consts.StateEnabled,
						},
						ProductSync: models.ProductSync{
							UpdateVariants: models.UpdateVariants{
								AutoSync: consts.StateDisabled,
							},
						},
					},
					SyncSettings{
						InventorySyncSetting: InventorySyncSetting{
							Preference: consts.SettingPreferenceStore,
						},
						ProductSyncSetting: ProductSyncSetting{
							Preference: consts.SettingPreferenceStore,
						},
					}),
				preview: &ProductListing{
					Product: models.Product{
						Variants: []*models.ProductVariant{
							{
								ID:                "abc",
								InventoryQuantity: 10,
							},
							{
								ID:                "def",
								InventoryQuantity: 20,
							},
						},
					},
					Relations: []*ProductListingRelation{
						{
							ProductListingVariantID: "abc",
							ProductsCenterVariant: ProductsCenterVariant{
								ID:        "123",
								ProductID: "456",
							},
							LinkStatus: consts.LinkStatusLinked,
						},
						{
							ProductListingVariantID: "def",
							LinkStatus:              consts.LinkStatusUnlink,
						},
					},
				},
				linkedRelations: []*ProductListingRelation{
					{
						ProductListingVariantID: "abc",
						ProductsCenterVariant: ProductsCenterVariant{
							ID:        "123",
							ProductID: "456",
						},
						LinkStatus: consts.LinkStatusLinked,
					},
				},
			},
			args: args{
				ctx: context.Background(),
			},
			mock: func() {
				mockCalculatorService.On("CalculateAvailableQuantities", mock.Anything, mock.Anything).Return(
					&calculators.CalculateAvailableQuantitiesOutput{
						ProductsCenterProduct: &calculators.ProductsCenterProductOut{
							ID: "456",
							Variants: []calculators.CalculateAvailableQuantitiesVariantOutput{
								{
									ID:                          "123",
									CalculatedAvailableQuantity: 20,
									Status:                      consts.CalculateSuccess,
								},
							},
						},
					}, nil).Once()
			},
			check: func(preview *ProductListing, err error) {
				require.NoError(t, err)
				require.EqualValues(t, 20, preview.Product.Variants[0].InventoryQuantity)
				require.EqualValues(t, 20, preview.Product.Variants[1].InventoryQuantity)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.mock()
			p := &previewModel{
				service:                     &serviceImpl{calculatorService: tt.fields.calculatorService},
				originPL:                    tt.fields.originPL,
				pcp:                         tt.fields.pcp,
				setting:                     tt.fields.setting,
				preview:                     tt.fields.preview,
				linkedRelations:             tt.fields.linkedRelations,
				linkedProductsCenterProduct: tt.fields.linkedProductsCenterProduct,
			}
			err := p.overWriteVariantInventory(tt.args.ctx)
			tt.check(p.preview, err)
		})
	}
}

func TestSetRelationProductIDs(t *testing.T) {
	tests := []struct {
		name            string
		relation        *ProductListingRelation
		pcpVariant      products_center.Variant
		pcp             *products_center.Product
		wantSourceID    string
		wantConnectorID string
	}{
		{
			name: "使用 variant 的 SourceProductID 和 ConnectorsProductID（均不为空）",
			relation: &ProductListingRelation{
				ProductsCenterVariant: ProductsCenterVariant{
					Source: ProductsCenterVariantSource{},
				},
			},
			pcpVariant: products_center.Variant{
				SourceProductID:     "variant-source-id",
				ConnectorsProductID: "variant-connector-id",
			},
			pcp: &products_center.Product{
				Source: products_center.Source{
					ID: "product-source-id",
				},
				ConnectorsProductID: "product-connector-id",
			},
			wantSourceID:    "variant-source-id",
			wantConnectorID: "variant-connector-id",
		},
		{
			name: "variant 的 SourceProductID 为空，使用产品的 Source.ID",
			relation: &ProductListingRelation{
				ProductsCenterVariant: ProductsCenterVariant{
					Source: ProductsCenterVariantSource{},
				},
			},
			pcpVariant: products_center.Variant{
				SourceProductID:     "",
				ConnectorsProductID: "variant-connector-id",
			},
			pcp: &products_center.Product{
				Source: products_center.Source{
					ID: "product-source-id",
				},
				ConnectorsProductID: "product-connector-id",
			},
			wantSourceID:    "product-source-id",
			wantConnectorID: "variant-connector-id",
		},
		{
			name: "variant 的 ConnectorsProductID 为空，使用产品的 ConnectorsProductID",
			relation: &ProductListingRelation{
				ProductsCenterVariant: ProductsCenterVariant{
					Source: ProductsCenterVariantSource{},
				},
			},
			pcpVariant: products_center.Variant{
				SourceProductID:     "variant-source-id",
				ConnectorsProductID: "",
			},
			pcp: &products_center.Product{
				Source: products_center.Source{
					ID: "product-source-id",
				},
				ConnectorsProductID: "product-connector-id",
			},
			wantSourceID:    "variant-source-id",
			wantConnectorID: "product-connector-id",
		},
		{
			name: "variant 的 SourceProductID 和 ConnectorsProductID 均为空，使用产品的值",
			relation: &ProductListingRelation{
				ProductsCenterVariant: ProductsCenterVariant{
					Source: ProductsCenterVariantSource{},
				},
			},
			pcpVariant: products_center.Variant{
				SourceProductID:     "",
				ConnectorsProductID: "",
			},
			pcp: &products_center.Product{
				Source: products_center.Source{
					ID: "product-source-id",
				},
				ConnectorsProductID: "product-connector-id",
			},
			wantSourceID:    "product-source-id",
			wantConnectorID: "product-connector-id",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 执行函数
			overWriteRelationProductIDs(tt.relation, tt.pcpVariant, tt.pcp)

			// 验证结果
			require.Equal(t, tt.wantSourceID, tt.relation.ProductsCenterVariant.Source.ProductID,
				"产品 ID 应该正确设置")
			require.Equal(t, tt.wantConnectorID, tt.relation.ProductsCenterVariant.ConnectorProductID,
				"cnt 产品 ID 应该正确设置")
		})
	}
}

func Test_shouldRemoveVariantsImagesForPreview(t *testing.T) {
	tests := []struct {
		name    string
		product *products_center.Product
		config  *config.TikTokSyncConfig
		want    bool
	}{
		{
			name: "配置为nil时应返回false",
			product: &products_center.Product{
				Organization: products_center.Organization{ID: "org123"},
				Variants: []products_center.Variant{
					{
						Options: []products_center.VariantOption{
							{Name: "颜色", Value: "红色"},
						},
					},
				},
			},
			config: nil,
			want:   false,
		},
		{
			name: "移除条件为空时应返回false",
			product: &products_center.Product{
				Organization: products_center.Organization{ID: "org123"},
				Variants: []products_center.Variant{
					{
						Options: []products_center.VariantOption{
							{Name: "颜色", Value: "红色"},
						},
					},
				},
			},
			config: &config.TikTokSyncConfig{
				SkuImageRemovalConditions: []config.SkuImageRemovalConditions{},
			},
			want: false,
		},
		{
			name: "产品没有选项时应返回false",
			product: &products_center.Product{
				Organization: products_center.Organization{ID: "org123"},
				Variants: []products_center.Variant{
					{
						Options: []products_center.VariantOption{},
					},
				},
			},
			config: &config.TikTokSyncConfig{
				SkuImageRemovalConditions: []config.SkuImageRemovalConditions{
					{
						OrganizationID:  "org123",
						RequiredOptions: []string{"颜色", "尺寸"},
					},
				},
			},
			want: false,
		},
		{
			name: "产品选项名为空时应返回false",
			product: &products_center.Product{
				Organization: products_center.Organization{ID: "org123"},
				Variants: []products_center.Variant{
					{
						Options: []products_center.VariantOption{
							{Name: "", Value: "红色"},
						},
					},
				},
			},
			config: &config.TikTokSyncConfig{
				SkuImageRemovalConditions: []config.SkuImageRemovalConditions{
					{
						OrganizationID:  "org123",
						RequiredOptions: []string{"颜色", "尺寸"},
					},
				},
			},
			want: false,
		},
		{
			name: "组织ID不匹配时应返回false",
			product: &products_center.Product{
				Organization: products_center.Organization{ID: "org123"},
				Variants: []products_center.Variant{
					{
						Options: []products_center.VariantOption{
							{Name: "颜色", Value: "红色"},
							{Name: "尺寸", Value: "L"},
						},
					},
				},
			},
			config: &config.TikTokSyncConfig{
				SkuImageRemovalConditions: []config.SkuImageRemovalConditions{
					{
						OrganizationID:  "org456",
						RequiredOptions: []string{"颜色", "尺寸"},
					},
				},
			},
			want: false,
		},
		{
			name: "选项名不完全匹配时应返回false",
			product: &products_center.Product{
				Organization: products_center.Organization{ID: "org123"},
				Variants: []products_center.Variant{
					{
						Options: []products_center.VariantOption{
							{Name: "颜色1", Value: "红色"},
						},
					},
				},
			},
			config: &config.TikTokSyncConfig{
				SkuImageRemovalConditions: []config.SkuImageRemovalConditions{
					{
						OrganizationID:  "org123",
						RequiredOptions: []string{"颜色", "尺寸"},
					},
				},
			},
			want: false,
		},
		{
			name: "完全匹配条件时应返回true",
			product: &products_center.Product{
				Organization: products_center.Organization{ID: "org123"},
				Variants: []products_center.Variant{
					{
						Options: []products_center.VariantOption{
							{Name: "颜色", Value: "红色"},
							{Name: "尺寸", Value: "L"},
						},
					},
				},
			},
			config: &config.TikTokSyncConfig{
				SkuImageRemovalConditions: []config.SkuImageRemovalConditions{
					{
						OrganizationID:  "org123",
						RequiredOptions: []string{"颜色", "尺寸"},
					},
				},
			},
			want: true,
		},
		{
			name: "从多个变体中收集选项名",
			product: &products_center.Product{
				Organization: products_center.Organization{ID: "org123"},
				Variants: []products_center.Variant{
					{
						Options: []products_center.VariantOption{
							{Name: "颜色", Value: "红色"},
						},
					},
					{
						Options: []products_center.VariantOption{
							{Name: "颜色", Value: "蓝色"},
							{Name: "尺寸", Value: "M"},
						},
					},
				},
			},
			config: &config.TikTokSyncConfig{
				SkuImageRemovalConditions: []config.SkuImageRemovalConditions{
					{
						OrganizationID:  "org123",
						RequiredOptions: []string{"颜色", "尺寸"},
					},
				},
			},
			want: true,
		},
		{
			name: "配置中有多个条件时匹配其中一个应返回true",
			product: &products_center.Product{
				Organization: products_center.Organization{ID: "org123"},
				Variants: []products_center.Variant{
					{
						Options: []products_center.VariantOption{
							{Name: "颜色", Value: "红色"},
							{Name: "尺寸", Value: "L"},
						},
					},
				},
			},
			config: &config.TikTokSyncConfig{
				SkuImageRemovalConditions: []config.SkuImageRemovalConditions{
					{
						OrganizationID:  "org456",
						RequiredOptions: []string{"颜色", "尺寸"},
					},
					{
						OrganizationID:  "org123",
						RequiredOptions: []string{"颜色", "尺寸"},
					},
				},
			},
			want: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := shouldRemoveVariantsImagesForPreview(tt.product, tt.config)
			require.Equal(t, tt.want, got)
		})
	}
}
