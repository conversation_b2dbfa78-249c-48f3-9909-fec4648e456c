package product_listing

import (
	"context"
	"crypto/md5"
	"encoding/hex"

	"github.com/go-redsync/redsync/v4"
	jsoniter "github.com/json-iterator/go"
	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/AfterShip/gopkg/log"

	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/common/databus"
	"github.com/AfterShip/pltf-pd-product-listings/internal/logger"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
)

// PushToChannel 将源产品推送到 listing
func (s *serviceImpl) PushToChannel(ctx context.Context, arg *PushToChannelArg) (listing ProductListing, err error) {
	ctx = log.AppendFieldsToContext(ctx,
		zap.String("organization_id", arg.Organization.ID),
		zap.String("sales_channel_platform", arg.SalesChannel.Platform),
		zap.String("sales_channel_store_key", arg.SalesChannel.StoreKey),
		zap.String("product_center_product_id", arg.ProductsCenterProduct.ID))

	defer func() {
		if err != nil {
			s.logger.ErrorCtx(ctx, "PushToChannel failed", zap.Error(err))
			return
		}
		s.logger.InfoCtx(ctx, "PushToChannel")
	}()

	model := newPushToChannelModel(arg)
	uniqueKey := model.buildPushToChannelUniqueKey()
	mutex := s.locker.NewMutex(
		redisKeyPrefixPushToChannel+uniqueKey,
		redsync.WithExpiry(redisLockerDefaultTime),
		redsync.WithTries(redisLockerDefaultTries),
	)
	if err := mutex.LockContext(ctx); err != nil {
		return ProductListing{}, errors.WithStack(err)
	}
	defer func() {
		if _, err := mutex.Unlock(); err != nil {
			logger.Get().ErrorCtx(ctx, "Failed to unlock mutex",
				zap.String("uniqueKey", uniqueKey),
				zap.Error(err))
		}
	}()

	pushed, err := s.isPushed(ctx, arg.Organization, arg.SalesChannel, arg.ProductsCenterProduct.ID)
	if err != nil {
		return ProductListing{}, err
	}
	if pushed {
		return ProductListing{}, ErrProductsCenterProductIsPushed
	}

	listing, err = s.Create(ctx, model.buildArgs(s.conf.DynamicConfigs.AmazonOptionNameMapping))
	if err != nil {
		return ProductListing{}, err
	}

	ctx = log.AppendFieldsToContext(ctx, zap.String("product_listing_id", listing.ID))

	// Todo: activity log: create listing by push to channel

	if err = s.notifySalesChannelUpsertEvent(ctx, &listing); err != nil {
		s.logger.WarnCtx(ctx, "send relation upsert to pub/sub failed",
			zap.String("connector_product_id", listing.ProductsCenterProduct.ConnectorProductID),
			zap.Error(err))
	}

	return listing, nil
}

type pushToChannelModel struct {
	arg *PushToChannelArg
}

func newPushToChannelModel(arg *PushToChannelArg) *pushToChannelModel {
	return &pushToChannelModel{arg: arg}
}

func (m *pushToChannelModel) buildArgs(optionNameMapping map[string]string) *ProductListingArgs {
	product := m.arg.Product
	product.OverwriteOptions(optionNameMapping)
	return &ProductListingArgs{
		Organization:            m.arg.Organization,
		SalesChannel:            m.arg.SalesChannel,
		Product:                 product,
		Relations:               m.buildArgsRelations(),
		Settings:                getDefaultProductListingSetting(m.arg.SalesChannel.Platform),
		SalesChannelProduct:     SalesChannelProduct{},
		ProductsCenterProduct:   m.arg.ProductsCenterProduct,
		FeedCustomizationParams: m.arg.FeedCustomizationParams,
		Ready:                   m.arg.Ready,
	}
}

func (m *pushToChannelModel) buildArgsRelations() []*ProductListingRelation {
	relations := make([]*ProductListingRelation, 0, len(m.arg.Relations))
	for _, relation := range m.arg.Relations {
		relations = append(relations, &ProductListingRelation{
			Organization:            m.arg.Organization,
			SalesChannel:            m.arg.SalesChannel,
			ProductListingVariantID: relation.ProductListingVariantID,
			ProductsCenterVariant:   relation.ProductsCenterVariant,
		})
	}
	return relations
}

// buildPushToChannelUniqueKey 构建推送到 listing 的唯一 key
// nolint:gosec
func (m *pushToChannelModel) buildPushToChannelUniqueKey() string {
	data := m.arg.Organization.ID + m.arg.SalesChannel.Platform + m.arg.SalesChannel.StoreKey + m.arg.ProductsCenterProduct.ID
	hash := md5.Sum([]byte(data))
	return hex.EncodeToString(hash[:])
}

// isPushed 判断产品是否已经推送到 listing
func (s *serviceImpl) isPushed(ctx context.Context,
	organization models.Organization, salesChannel models.SalesChannel, productsCenterProductID string) (bool, error) {
	if productsCenterProductID == "" {
		return false, nil
	}

	query := ListArg{
		OrganizationID:           organization.ID,
		SalesChannelPlatform:     salesChannel.Platform,
		SalesChannelStoreKey:     salesChannel.StoreKey,
		ProductsCenterProductIDs: productsCenterProductID,
		Page:                     1,
		Limit:                    1,
	}
	listings, err := s.List(ctx, &query)
	if err != nil {
		return false, err
	}

	return len(listings) > 0, nil
}

func (s *serviceImpl) notifySalesChannelUpsertEvent(ctx context.Context, listing *ProductListing) error {

	if listing.ProductsCenterProduct.ID == "" {
		return nil
	}

	meta := databus.PubSubMeta{
		OrgID: listing.Organization.ID,
	}

	message := notifySalesChannelUpsertEventPubsubMessage{
		Products: []notifySalesChannelUpsertEventProduct{
			{
				ProductsCenterProductID: listing.ProductsCenterProduct.ID,
				ConnectorsProductID:     listing.ProductsCenterProduct.ConnectorProductID,
			},
		},
	}

	messageBytes, err := jsoniter.Marshal(message)
	if err != nil {
		return errors.WithStack(err)
	}

	return s.databusService.SendToPubSub(ctx, s.conf.PubSubTopics.SearchableProductModifySalesChannel, messageBytes, meta)
}
