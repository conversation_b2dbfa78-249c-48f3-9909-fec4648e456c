package product_listing

import (
	"testing"

	"github.com/go-playground/validator/v10"
	"github.com/stretchr/testify/assert"

	"github.com/AfterShip/gopkg/log"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
)

func TestBatchInvokeProductListingTask_validate(t *testing.T) {
	tests := []struct {
		name    string
		input   *BatchInvokeProductListingTaskInput
		wantErr error
	}{
		{
			name: "有效输入",
			input: &BatchInvokeProductListingTaskInput{
				Organization: models.Organization{
					ID: "org123",
				},
				SalesChannel: models.SalesChannel{
					Platform: "shein",
					StoreKey: "store123",
				},
				Source: models.Source{
					App: models.App{
						Platform: "shopify",
						Key:      "app123",
					},
				},
				TaskType:       "batch_publish_prices",
				Concurrency:    "multiple",
				QueryDataScope: "all",
				FromEvent:      "manual",
			},
			wantErr: nil,
		},
		{
			name: "Organization.ID为空",
			input: &BatchInvokeProductListingTaskInput{
				Organization: models.Organization{
					ID: "",
				},
				SalesChannel: models.SalesChannel{
					Platform: "shein",
					StoreKey: "store123",
				},
				Source: models.Source{
					App: models.App{
						Platform: "shopify",
						Key:      "app123",
					},
				},
				TaskType:       "batch_publish_prices",
				Concurrency:    "multiple",
				QueryDataScope: "all",
				FromEvent:      "manual",
			},
			wantErr: ErrUnprocessableEntity,
		},
		{
			name: "SalesChannel.Platform为空",
			input: &BatchInvokeProductListingTaskInput{
				Organization: models.Organization{
					ID: "org123",
				},
				SalesChannel: models.SalesChannel{
					Platform: "",
					StoreKey: "store123",
				},
				Source: models.Source{
					App: models.App{
						Platform: "shopify",
						Key:      "app123",
					},
				},
				TaskType:       "batch_publish_prices",
				Concurrency:    "multiple",
				QueryDataScope: "all",
				FromEvent:      "manual",
			},
			wantErr: ErrUnprocessableEntity,
		},
		{
			name: "SalesChannel.StoreKey为空",
			input: &BatchInvokeProductListingTaskInput{
				Organization: models.Organization{
					ID: "org123",
				},
				SalesChannel: models.SalesChannel{
					Platform: "shein",
					StoreKey: "",
				},
				Source: models.Source{
					App: models.App{
						Platform: "shopify",
						Key:      "app123",
					},
				},
				TaskType:       "batch_publish_prices",
				Concurrency:    "multiple",
				QueryDataScope: "all",
				FromEvent:      "manual",
			},
			wantErr: ErrUnprocessableEntity,
		},
		{
			name: "Source.App.Platform为空",
			input: &BatchInvokeProductListingTaskInput{
				Organization: models.Organization{
					ID: "org123",
				},
				SalesChannel: models.SalesChannel{
					Platform: "shein",
					StoreKey: "store123",
				},
				Source: models.Source{
					App: models.App{
						Platform: "",
						Key:      "app123",
					},
				},
				TaskType:       "batch_publish_prices",
				Concurrency:    "multiple",
				QueryDataScope: "all",
				FromEvent:      "manual",
			},
			wantErr: ErrUnprocessableEntity,
		},
		{
			name: "Source.App.Key为空",
			input: &BatchInvokeProductListingTaskInput{
				Organization: models.Organization{
					ID: "org123",
				},
				SalesChannel: models.SalesChannel{
					Platform: "shein",
					StoreKey: "store123",
				},
				Source: models.Source{
					App: models.App{
						Platform: "shopify",
						Key:      "",
					},
				},
				TaskType:       "batch_publish_prices",
				Concurrency:    "multiple",
				QueryDataScope: "all",
				FromEvent:      "manual",
			},
			wantErr: ErrUnprocessableEntity,
		},
		{
			name: "指定范围但ProductListingIDs为空",
			input: &BatchInvokeProductListingTaskInput{
				Organization: models.Organization{
					ID: "org123",
				},
				SalesChannel: models.SalesChannel{
					Platform: "shein",
					StoreKey: "store123",
				},
				Source: models.Source{
					App: models.App{
						Platform: "shopify",
						Key:      "app123",
					},
				},
				TaskType:          "batch_publish_prices",
				Concurrency:       "multiple",
				QueryDataScope:    "specified",
				FromEvent:         "manual",
				ProductListingIDs: []string{},
			},
			wantErr: ErrNoProductListingID,
		},
		{
			name: "指定范围且有ProductListingIDs",
			input: &BatchInvokeProductListingTaskInput{
				Organization: models.Organization{
					ID: "org123",
				},
				SalesChannel: models.SalesChannel{
					Platform: "shein",
					StoreKey: "store123",
				},
				Source: models.Source{
					App: models.App{
						Platform: "shopify",
						Key:      "app123",
					},
				},
				TaskType:          "batch_publish_prices",
				Concurrency:       "multiple",
				QueryDataScope:    "specified",
				FromEvent:         "manual",
				ProductListingIDs: []string{"listing1", "listing2"},
			},
			wantErr: nil,
		},
		{
			name: "无效的TaskType",
			input: &BatchInvokeProductListingTaskInput{
				Organization: models.Organization{
					ID: "org123",
				},
				SalesChannel: models.SalesChannel{
					Platform: "shein",
					StoreKey: "store123",
				},
				Source: models.Source{
					App: models.App{
						Platform: "shopify",
						Key:      "app123",
					},
				},
				TaskType:       "invalid_task_type",
				Concurrency:    "multiple",
				QueryDataScope: "all",
				FromEvent:      "manual",
			},
			wantErr: validator.ValidationErrors{},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			task := &BatchInvokeProductListingTask{
				Logger:    log.GlobalLogger(),
				Validator: validator.New(),
			}
			err := task.validate(tt.input)
			if tt.wantErr != nil {
				// 如果期望错误是validator.ValidationErrors类型，则进行类型断言
				if _, ok := tt.wantErr.(validator.ValidationErrors); ok {
					assert.IsType(t, validator.ValidationErrors{}, err)
				} else {
					assert.Equal(t, tt.wantErr, err)
				}
			} else {
				assert.NoError(t, err)
			}
		})
	}
}
