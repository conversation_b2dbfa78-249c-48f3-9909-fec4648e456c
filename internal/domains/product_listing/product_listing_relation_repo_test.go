package product_listing

import (
	"context"
	"fmt"
	"testing"

	"cloud.google.com/go/spanner"
	"github.com/spf13/viper"
	"github.com/stretchr/testify/require"

	"github.com/AfterShip/gopkg/cfg"
	"github.com/AfterShip/gopkg/storage/spannerx"
	"github.com/AfterShip/gopkg/uuid"

	"github.com/AfterShip/pltf-pd-product-listings/internal/config"
)

// nolint: maintidx
func Test_ProductListingRelationRepoImpl(t *testing.T) {
	cfgs := new(config.Config)
	_, err := cfg.LoadViperConfig(cfgs, func(v *viper.Viper) { v.AddConfigPath("../../../cmd/apiserver/conf") })
	require.NoError(t, err)

	ctx := context.Background()

	database := fmt.Sprintf("projects/%s/instances/%s/databases/%s",
		cfgs.Spanner.Project, cfgs.Spanner.Instance, cfgs.Spanner.Database)
	spannerCli, err := spannerx.NewClient(ctx, database)
	require.NoError(t, err)
	relationDBRepo := relationRepo{
		cli: spannerCli,
	}
	productListingID := uuid.GenerateUUIDV4()
	relations := []relationDBModel{
		{
			OrganizationID:           uuid.GenerateUUIDV4(),
			SalesChannelPlatform:     uuid.GenerateUUIDV4(),
			SalesChannelStoreKey:     uuid.GenerateUUIDV4(),
			SalesChannelProductID:    uuid.GenerateUUIDV4(),
			SalesChannelVariantID:    uuid.GenerateUUIDV4(),
			ProductsCenterProductID:  uuid.GenerateUUIDV4(),
			ProductsCenterVariantID:  uuid.GenerateUUIDV4(),
			ProductListingRelationID: uuid.GenerateUUIDV4(),
			ProductListingID:         uuid.GenerateUUIDV4(),
			ProductListingVariantID:  uuid.GenerateUUIDV4(),
		},
		{
			OrganizationID:           uuid.GenerateUUIDV4(),
			SalesChannelPlatform:     uuid.GenerateUUIDV4(),
			SalesChannelStoreKey:     uuid.GenerateUUIDV4(),
			SalesChannelProductID:    uuid.GenerateUUIDV4(),
			SalesChannelVariantID:    uuid.GenerateUUIDV4(),
			ProductsCenterProductID:  uuid.GenerateUUIDV4(),
			ProductsCenterVariantID:  uuid.GenerateUUIDV4(),
			ProductListingRelationID: uuid.GenerateUUIDV4(),
			ProductListingID:         uuid.GenerateUUIDV4(),
			ProductListingVariantID:  uuid.GenerateUUIDV4(),
		},
		{
			OrganizationID:           uuid.GenerateUUIDV4(),
			SalesChannelPlatform:     uuid.GenerateUUIDV4(),
			SalesChannelStoreKey:     uuid.GenerateUUIDV4(),
			SalesChannelProductID:    uuid.GenerateUUIDV4(),
			SalesChannelVariantID:    uuid.GenerateUUIDV4(),
			ProductsCenterProductID:  uuid.GenerateUUIDV4(),
			ProductsCenterVariantID:  uuid.GenerateUUIDV4(),
			ProductListingRelationID: uuid.GenerateUUIDV4(),
			ProductListingID:         productListingID,
			ProductListingVariantID:  uuid.GenerateUUIDV4(),
		},
		{
			OrganizationID:           uuid.GenerateUUIDV4(),
			SalesChannelPlatform:     uuid.GenerateUUIDV4(),
			SalesChannelStoreKey:     uuid.GenerateUUIDV4(),
			SalesChannelProductID:    uuid.GenerateUUIDV4(),
			SalesChannelVariantID:    uuid.GenerateUUIDV4(),
			ProductsCenterProductID:  uuid.GenerateUUIDV4(),
			ProductsCenterVariantID:  uuid.GenerateUUIDV4(),
			ProductListingRelationID: uuid.GenerateUUIDV4(),
			ProductListingID:         productListingID,
			ProductListingVariantID:  uuid.GenerateUUIDV4(),
		},
	}

	for _, relation := range relations {
		mut, err := relationDBRepo.generateCreateMutation(&relation)
		require.NoError(t, err)
		_, err = relationDBRepo.cli.Apply(ctx, []*spanner.Mutation{mut})
		require.NoError(t, err)
	}
	// Duplicate create
	mut, err := relationDBRepo.generateCreateMutation(&relations[0])
	require.NoError(t, err)
	_, err = relationDBRepo.cli.Apply(ctx, []*spanner.Mutation{mut})
	require.Error(t, err)

	relations[0].AllowSync = "enabled"
	mut, err = relationDBRepo.generateUpdateMutation(&relations[0])
	require.NoError(t, err)
	_, err = relationDBRepo.cli.Apply(ctx, []*spanner.Mutation{mut})
	require.NoError(t, err)

	// get all by product listing id
	founds, err := relationDBRepo.listAllByProductListingID(ctx, productListingID)
	require.NoError(t, err)
	require.Equal(t, 2, len(founds))

	// get all but with empty product listing id
	founds, err = relationDBRepo.listAllByProductListingID(ctx, "")
	require.NoError(t, err)
	require.Equal(t, 0, len(founds))

	// get all by product listing id
	founds, err = relationDBRepo.listAllByProductListingIDs(ctx, []string{relations[0].ProductListingID, relations[1].ProductListingID})
	require.NoError(t, err)
	require.Equal(t, 2, len(founds))

	founds, err = relationDBRepo.listAllByProductListingIDs(ctx, []string{relations[0].ProductListingID, relations[1].ProductListingID, relations[2].ProductListingID, relations[3].ProductListingID})
	require.NoError(t, err)
	require.Equal(t, 4, len(founds))

	// get all by product listing id with not found
	founds, err = relationDBRepo.listAllByProductListingIDs(ctx, []string{"not_found"})
	require.NoError(t, err)
	require.Equal(t, 0, len(founds))

	// get all by product listing id with not found
	founds, err = relationDBRepo.listAllByProductListingIDs(ctx, []string{relations[0].ProductListingID, relations[1].ProductListingID, "not found"})
	require.NoError(t, err)
	require.Equal(t, 2, len(founds))

	// get list by organization id
	founds, err = relationDBRepo.list(ctx, &repoListRelationArgs{
		OrganizationID: relations[0].OrganizationID,
		Page:           1,
		Limit:          10,
	})
	require.NoError(t, err)
	require.Equal(t, 1, len(founds))

	// get list by sales channel platform
	founds, err = relationDBRepo.list(ctx, &repoListRelationArgs{
		SalesChannelPlatform: relations[0].SalesChannelPlatform,
		Page:                 1,
		Limit:                10,
	})
	require.NoError(t, err)
	require.Equal(t, 1, len(founds))

	// get list by sales channel store key
	founds, err = relationDBRepo.list(ctx, &repoListRelationArgs{
		SalesChannelStoreKey: relations[0].SalesChannelStoreKey,
		Page:                 1,
		Limit:                10,
	})
	require.NoError(t, err)
	require.Equal(t, 1, len(founds))

	// get list by sales channel product id
	founds, err = relationDBRepo.list(ctx, &repoListRelationArgs{
		SalesChannelProductID: relations[0].SalesChannelProductID,
		Page:                  1,
		Limit:                 10,
	})
	require.NoError(t, err)
	require.Equal(t, 1, len(founds))

	// get list by sales channel variant id
	founds, err = relationDBRepo.list(ctx, &repoListRelationArgs{
		SalesChannelVariantID: relations[0].SalesChannelVariantID,
		Page:                  1,
		Limit:                 10,
	})
	require.NoError(t, err)
	require.Equal(t, 1, len(founds))

	// get list by product center product id
	founds, err = relationDBRepo.list(ctx, &repoListRelationArgs{
		ProductsCenterProductID: relations[0].ProductsCenterProductID,
		Page:                    1,
		Limit:                   10,
	})
	require.NoError(t, err)
	require.Equal(t, 1, len(founds))

	// get list by product center variant id
	founds, err = relationDBRepo.list(ctx, &repoListRelationArgs{
		ProductsCenterVariantID: relations[0].ProductsCenterVariantID,
		Page:                    1,
		Limit:                   10,
	})
	require.NoError(t, err)
	require.Equal(t, 1, len(founds))

	found, err := relationDBRepo.getByID(ctx, relations[0].ProductListingRelationID)
	require.NoError(t, err)
	require.Equal(t, relations[0].ProductListingRelationID, found.ProductListingRelationID)
	require.Equal(t, relations[0].ProductListingID, found.ProductListingID)
	require.Equal(t, relations[0].AllowSync, found.AllowSync)

	// Not found
	notFound, err := relationDBRepo.getByID(ctx, "not_found")
	require.Error(t, err)
	require.Equal(t, "", notFound.ProductListingRelationID)

	// Delete
	mut = relationDBRepo.generateDeleteMutation(relations[0].ProductListingRelationID)
	_, err = relationDBRepo.cli.Apply(ctx, []*spanner.Mutation{mut})
	require.NoError(t, err)
	require.NotNil(t, relations[0].DeletedAt)

	// force delete
	mut = relationDBRepo.generateForceDeleteMutation(relations[0].ProductListingRelationID)
	_, err = relationDBRepo.cli.Apply(ctx, []*spanner.Mutation{mut})
	require.NoError(t, err)
	notFound, err = relationDBRepo.getByID(ctx, relations[0].ProductListingRelationID)
	require.Error(t, err)
	require.Equal(t, "", notFound.ProductListingRelationID)

	mut = relationDBRepo.generateForceDeleteMutation(relations[1].ProductListingRelationID)
	_, err = relationDBRepo.cli.Apply(ctx, []*spanner.Mutation{mut})
	require.NoError(t, err)
	notFound, err = relationDBRepo.getByID(ctx, relations[1].ProductListingRelationID)
	require.Error(t, err)
	require.Equal(t, "", notFound.ProductListingRelationID)

}

func TestFetchRelationIndex(t *testing.T) {
	tests := []struct {
		name     string
		args     *repoListRelationArgs
		expected string
	}{
		{
			name:     "SalesChannelProductID not empty",
			args:     &repoListRelationArgs{SalesChannelProductID: "test"},
			expected: salesChannelProductVariantIDIndex,
		},
		{
			name:     "SalesChannelVariantID not empty",
			args:     &repoListRelationArgs{SalesChannelVariantID: "test"},
			expected: salesChannelProductVariantIDIndex,
		},
		{
			name:     "SalesChannelProductIDs not empty",
			args:     &repoListRelationArgs{SalesChannelProductIDs: []string{"test"}},
			expected: salesChannelProductVariantIDIndex,
		},
		{
			name:     "SalesChannelVariantIDs not empty",
			args:     &repoListRelationArgs{SalesChannelVariantIDs: []string{"test"}},
			expected: salesChannelProductVariantIDIndex,
		},
		{
			name:     "ProductsCenterProductID not empty",
			args:     &repoListRelationArgs{ProductsCenterProductID: "pcpID"},
			expected: productsCenterProductVariantIDIndex,
		},
		{
			name:     "No conditions met",
			args:     &repoListRelationArgs{},
			expected: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := fetchRelationIndex(tt.args)
			require.Equal(t, tt.expected, result)
		})
	}
}
