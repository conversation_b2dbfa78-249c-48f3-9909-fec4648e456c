package product_listing

import (
	"fmt"
	"reflect"
	"testing"

	"github.com/AfterShip/pltf-pd-product-listings/internal/consts"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/category"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
)

func Test_titleCheck(t *testing.T) {
	tests := []struct {
		name     string
		product  *models.Product
		expected []sheinReadyCheck
	}{
		{
			name: "title is empty",
			product: &models.Product{
				Title: "",
			},
			expected: []sheinReadyCheck{
				&titleIsEmpty{},
			},
		},
		{
			name: "title is not empty",
			product: &models.Product{
				Title: "Sample Title",
			},
			expected: []sheinReadyCheck{},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			model := &sheinReadyCheckModel{
				product: tt.product,
			}
			result := model.titleCheck()
			if !reflect.DeepEqual(result, tt.expected) {
				t.<PERSON><PERSON><PERSON>("titleCheck() = %v, expected %v", result, tt.expected)
			}
		})
	}
}

func Test_descriptionCheck(t *testing.T) {
	tests := []struct {
		name     string
		product  *models.Product
		expected []sheinReadyCheck
	}{
		{
			name: "description is empty",
			product: &models.Product{
				Description: "",
			},
			expected: []sheinReadyCheck{
				&descriptionIsEmpty{},
			},
		},
		{
			name: "description is not empty",
			product: &models.Product{
				Description: "Sample description",
			},
			expected: []sheinReadyCheck{},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			model := &sheinReadyCheckModel{
				product: tt.product,
			}
			result := model.descriptionCheck()
			if !reflect.DeepEqual(result, tt.expected) {
				t.Errorf("descriptionCheck() = %v, expected %v", result, tt.expected)
			}
		})
	}
}

func Test_categoryCheck(t *testing.T) {
	tests := []struct {
		name     string
		product  *models.Product
		expected []sheinReadyCheck
	}{
		{
			name: "categories are empty",
			product: &models.Product{
				Categories: []*models.SalesChannelResource{},
			},
			expected: []sheinReadyCheck{
				&categoryIsEmpty{},
			},
		},
		{
			name: "categories are not empty",
			product: &models.Product{
				Categories: []*models.SalesChannelResource{{SalesChannelID: "category1"}},
			},
			expected: []sheinReadyCheck{},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			model := &sheinReadyCheckModel{
				product: tt.product,
			}
			result := model.categoryCheck()
			if !reflect.DeepEqual(result, tt.expected) {
				t.Errorf("categoryCheck() = %v, expected %v", result, tt.expected)
			}
		})
	}
}

func Test_brandCheck(t *testing.T) {
	tests := []struct {
		name     string
		product  *models.Product
		rules    *category.RulesOutput
		expected []sheinReadyCheck
	}{
		{
			name: "brand is not required",
			product: &models.Product{
				Brand: models.SalesChannelResource{SalesChannelID: ""},
			},
			rules: &category.RulesOutput{
				ExternalCategoryID: "test",
				Rule: category.Rule{
					Brand: &category.BrandRule{IsRequired: false},
				},
			},
			expected: nil,
		},
		{
			name: "brand is required but not provided",
			product: &models.Product{
				Brand: models.SalesChannelResource{SalesChannelID: ""},
			},
			rules: &category.RulesOutput{
				ExternalCategoryID: "test",
				Rule: category.Rule{
					Brand: &category.BrandRule{IsRequired: true},
				},
			},
			expected: []sheinReadyCheck{
				&brandIsEmpty{},
			},
		},
		{
			name: "brand is required and provided",
			product: &models.Product{
				Brand: models.SalesChannelResource{SalesChannelID: "brand1"},
			},
			rules: &category.RulesOutput{
				ExternalCategoryID: "test",
				Rule: category.Rule{
					Brand: &category.BrandRule{IsRequired: true},
				},
			},
			expected: nil,
		},
		{
			name: "rules are nil",
			product: &models.Product{
				Brand: models.SalesChannelResource{SalesChannelID: ""},
			},
			rules:    nil,
			expected: nil,
		},
		{
			name: "rules.ExternalCategoryID is empty",
			product: &models.Product{
				Brand: models.SalesChannelResource{SalesChannelID: ""},
			},
			rules: &category.RulesOutput{
				ExternalCategoryID: "",
				Rule: category.Rule{
					Brand: &category.BrandRule{IsRequired: true},
				},
			},
			expected: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			model := &sheinReadyCheckModel{
				product: tt.product,
				rules:   tt.rules,
			}
			result := model.brandCheck()
			if !reflect.DeepEqual(result, tt.expected) {
				t.Errorf("brandCheck() = %v, expected %v", result, tt.expected)
			}
		})
	}
}

func Test_sizeChartCheck(t *testing.T) {
	tests := []struct {
		name     string
		product  *models.Product
		rules    *category.RulesOutput
		expected []sheinReadyCheck
	}{
		{
			name: "size chart is not required",
			product: &models.Product{
				SizeChart: models.ProductSizeChart{Attributes: []models.ProductSizeChartAttribute{}},
			},
			rules: &category.RulesOutput{
				ExternalCategoryID: "exampleCategoryID",
				Rule: category.Rule{
					SizeChart: &category.SizeChart{IsRequired: false},
				},
			},
			expected: nil,
		},
		{
			name: "size chart is required but not provided",
			product: &models.Product{
				SizeChart: models.ProductSizeChart{Attributes: []models.ProductSizeChartAttribute{}},
			},
			rules: &category.RulesOutput{
				ExternalCategoryID: "exampleCategoryID",
				Rule: category.Rule{
					SizeChart: &category.SizeChart{IsRequired: true},
				},
			},
			expected: []sheinReadyCheck{
				&sizeChartIsEmpty{},
			},
		},
		{
			name: "size chart is required and provided",
			product: &models.Product{
				SizeChart: models.ProductSizeChart{
					Attributes: []models.ProductSizeChartAttribute{
						{
							SalesChannelID:            "size_att_id",
							RelateSalesChannelID:      "sales_att_id",
							RelateSalesChannelValueID: "sales_att_value_id",
							Value:                     "M",
						}}},
			},
			rules: &category.RulesOutput{
				ExternalCategoryID: "exampleCategoryID",
				Rule: category.Rule{
					SizeChart: &category.SizeChart{IsRequired: true},
				},
			},
			expected: []sheinReadyCheck{},
		},
		{
			name: "size chart is required but missing required id",
			product: &models.Product{
				SizeChart: models.ProductSizeChart{
					Attributes: []models.ProductSizeChartAttribute{
						{
							RelateSalesChannelID:      "sales_att_id",
							RelateSalesChannelValueID: "sales_att_value_id",
							Value:                     "M",
						}}},
			},
			rules: &category.RulesOutput{
				ExternalCategoryID: "exampleCategoryID",
				Rule: category.Rule{
					SizeChart: &category.SizeChart{IsRequired: true},
				},
			},
			expected: []sheinReadyCheck{
				&sizeChartIsEmpty{},
			},
		},
		{
			name: "rules are nil",
			product: &models.Product{
				SizeChart: models.ProductSizeChart{Attributes: []models.ProductSizeChartAttribute{}},
			},
			rules:    nil,
			expected: nil,
		},
		{
			name: "rules.ExternalCategoryID is empty",
			product: &models.Product{
				SizeChart: models.ProductSizeChart{Attributes: []models.ProductSizeChartAttribute{}},
			},
			rules: &category.RulesOutput{
				ExternalCategoryID: "",
				Rule: category.Rule{
					SizeChart: &category.SizeChart{IsRequired: true},
				},
			},
			expected: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			model := &sheinReadyCheckModel{
				product: tt.product,
				rules:   tt.rules,
			}
			result := model.sizeChartCheck()
			if !reflect.DeepEqual(result, tt.expected) {
				t.Errorf("sizeChartCheck() = %v, expected %v", result, tt.expected)
			}
		})
	}
}

func Test_attributesCheck(t *testing.T) {
	tests := []struct {
		name       string
		product    *models.Product
		attributes *category.AttributesOutput
		expected   []sheinReadyCheck
	}{
		{
			name: "attributes are nil",
			product: &models.Product{
				Attributes: []*models.ProductAttribute{},
			},
			attributes: nil,
			expected:   nil,
		},
		{
			name: "attributes.ExternalCategoryID is empty",
			product: &models.Product{
				Attributes: []*models.ProductAttribute{},
			},
			attributes: &category.AttributesOutput{
				ExternalCategoryID: "",
				Attributes:         []category.Attribute{},
			},
			expected: nil,
		},
		{
			name: "required attribute is missing",
			product: &models.Product{
				Attributes: []*models.ProductAttribute{},
			},
			attributes: &category.AttributesOutput{
				ExternalCategoryID: "exampleCategoryID",
				Attributes: []category.Attribute{
					{ID: "attr1", Name: "Attribute 1", IsRequired: true, Type: consts.AttributeTypeProductProperty},
				},
			},
			expected: []sheinReadyCheck{
				&attributesIsEmpty{Name: "Attribute 1"},
			},
		},
		{
			name: "required attribute is provided",
			product: &models.Product{
				Attributes: []*models.ProductAttribute{
					{SalesChannelID: "attr1", Values: []models.SalesChannelResource{{SalesChannelID: "value1"}}},
				},
			},
			attributes: &category.AttributesOutput{
				ExternalCategoryID: "exampleCategoryID",
				Attributes: []category.Attribute{
					{ID: "attr1", Name: "Attribute 1", IsRequired: true, Type: consts.AttributeTypeProductProperty},
				},
			},
			expected: []sheinReadyCheck{},
		},
		{
			name: "non-required attribute is missing",
			product: &models.Product{
				Attributes: []*models.ProductAttribute{},
			},
			attributes: &category.AttributesOutput{
				ExternalCategoryID: "exampleCategoryID",
				Attributes: []category.Attribute{
					{ID: "attr1", Name: "Attribute 1", IsRequired: false, Type: consts.AttributeTypeProductProperty},
				},
			},
			expected: []sheinReadyCheck{},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			model := &sheinReadyCheckModel{
				product:    tt.product,
				attributes: tt.attributes,
			}
			result := model.attributesCheck()
			if !reflect.DeepEqual(result, tt.expected) {
				t.Errorf("attributesCheck() = %v, expected %v", result, tt.expected)
			}
		})
	}
}

func Test_productOptionsCheck(t *testing.T) {
	tests := []struct {
		name       string
		product    *models.Product
		attributes *category.AttributesOutput
		expected   []sheinReadyCheck
	}{
		{
			name: "options are empty",
			product: &models.Product{
				Options: []*models.ProductOption{},
			},
			expected: []sheinReadyCheck{
				&sheinProductOptionIsEmpty{},
			},
		},
		{
			name: "option value details are empty",
			product: &models.Product{
				Options: []*models.ProductOption{
					{SalesChannelOptionID: "option1", Name: "option1_name", ValueDetails: []models.ProductOptionValueDetail{}},
				},
			},
			expected: []sheinReadyCheck{
				&sheinProductOptionIsInvalid{
					Suggestion: `Select option values for "option1_name"`,
				},
			},
		},
		{
			name: "option value is invalid",
			product: &models.Product{
				Options: []*models.ProductOption{
					{SalesChannelOptionID: "option1", ValueDetails: []models.ProductOptionValueDetail{
						{SalesChannelValueID: "", Value: "option1_value"},
					}},
				},
			},
			expected: []sheinReadyCheck{
				&sheinProductOptionValueIsInvalid{OptionValue: "option1_value"},
			},
		},
		{
			name: "option value is valid with image",
			product: &models.Product{
				Options: []*models.ProductOption{
					{SalesChannelOptionID: "option1", ValueDetails: []models.ProductOptionValueDetail{
						{SalesChannelValueID: "value1", Media: []models.ProductMedia{
							{ExternalImageType: consts.ExternalImageTypeMain},
							{ExternalImageType: consts.ExternalImageTypeDetail},
							{ExternalImageType: consts.ExternalImageTypeSquare},
							{ExternalImageType: consts.ExternalImageTypePiece},
						}},
					}},
				},
			},
			expected: []sheinReadyCheck{},
		},
		{
			name: "option not found under current category",
			attributes: &category.AttributesOutput{
				Attributes: []category.Attribute{
					{
						ID: "option2", Name: "option2_name", Type: consts.AttributeTypeSalesProperty,
						Values: []category.AttributeValue{
							{ID: "value1"},
						},
					},
				},
			},
			product: &models.Product{
				Options: []*models.ProductOption{
					{SalesChannelOptionID: "option1", Name: "option1_name", ValueDetails: []models.ProductOptionValueDetail{
						{SalesChannelValueID: "value1", Media: []models.ProductMedia{
							{ExternalImageType: consts.ExternalImageTypeMain},
							{ExternalImageType: consts.ExternalImageTypeDetail},
							{ExternalImageType: consts.ExternalImageTypeSquare},
							{ExternalImageType: consts.ExternalImageTypePiece},
						}},
					}},
				},
			},
			expected: []sheinReadyCheck{
				&sheinProductOptionIsInvalid{
					Suggestion: fmt.Sprintf("Option %s is not found under current category, please manually select one", "option1_name"),
				},
			},
		},
		{
			name: "option value not found under current category",
			attributes: &category.AttributesOutput{
				Attributes: []category.Attribute{
					{
						ID: "option2", Name: "option2_name", Type: consts.AttributeTypeSalesProperty,
						Values: []category.AttributeValue{
							{ID: "value2"},
						},
					},
				},
			},
			product: &models.Product{
				Options: []*models.ProductOption{
					{SalesChannelOptionID: "option2", Name: "option2_name", ValueDetails: []models.ProductOptionValueDetail{
						{SalesChannelValueID: "value1", Value: "value1_value", Media: []models.ProductMedia{
							{ExternalImageType: consts.ExternalImageTypeMain},
							{ExternalImageType: consts.ExternalImageTypeDetail},
							{ExternalImageType: consts.ExternalImageTypeSquare},
							{ExternalImageType: consts.ExternalImageTypePiece},
						}},
					}},
				},
			},
			expected: []sheinReadyCheck{
				&sheinProductOptionValueIsInvalid{
					Suggestion: fmt.Sprintf("Option value %s is not found under current category, please manually select one", "value1_value"),
				},
			},
		},
		{
			name: "missing main option",
			attributes: &category.AttributesOutput{
				Attributes: []category.Attribute{
					{
						ID: "main_option_id", Name: "main_option_name", Type: consts.AttributeTypeSalesPropertyMainOption,
					},
					{
						ID: "option1", Name: "option1_name", Type: consts.AttributeTypeSalesProperty,
						Values: []category.AttributeValue{
							{ID: "value1"},
						},
					},
				},
			},
			product: &models.Product{
				Options: []*models.ProductOption{
					{SalesChannelOptionID: "option1", ValueDetails: []models.ProductOptionValueDetail{
						{SalesChannelValueID: "value1", Media: []models.ProductMedia{
							{ExternalImageType: consts.ExternalImageTypeMain},
							{ExternalImageType: consts.ExternalImageTypeDetail},
							{ExternalImageType: consts.ExternalImageTypeSquare},
							{ExternalImageType: consts.ExternalImageTypePiece},
						}},
					}},
				},
			},
			expected: []sheinReadyCheck{
				&sheinMissingMainOption{
					OptionNameList: []string{"main_option_name"},
				},
			},
		},
		{
			name: "missing required option",
			attributes: &category.AttributesOutput{
				Attributes: []category.Attribute{
					{
						ID: "option1", Name: "option1", IsRequired: true, Type: consts.AttributeTypeSalesPropertyMainOption,
					},
					{
						ID: "option3", Name: "option3", IsRequired: true, Type: consts.AttributeTypeSalesProperty,
					},
					{
						ID: "option4", Name: "option4", IsRequired: true, Type: consts.AttributeTypeSalesProperty,
					},
					{
						ID: "option1", Name: "option1_name", Type: consts.AttributeTypeSalesProperty,
						Values: []category.AttributeValue{
							{ID: "value1"},
						},
					},
				},
			},
			product: &models.Product{
				Options: []*models.ProductOption{
					{SalesChannelOptionID: "option1", ValueDetails: []models.ProductOptionValueDetail{
						{SalesChannelValueID: "value1", Media: []models.ProductMedia{
							{ExternalImageType: consts.ExternalImageTypeMain},
							{ExternalImageType: consts.ExternalImageTypeDetail},
							{ExternalImageType: consts.ExternalImageTypeSquare},
							{ExternalImageType: consts.ExternalImageTypePiece},
						}},
					}},
				},
			},
			expected: []sheinReadyCheck{
				&sheinMissingRequiredOption{
					OptionNameList: []string{"option3", "option4"},
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			model := &sheinReadyCheckModel{
				product:    tt.product,
				attributes: tt.attributes,
			}
			result := model.productOptionsCheck()
			if !reflect.DeepEqual(result, tt.expected) {
				t.Errorf("productOptionsCheck() = %v, expected %v", result, tt.expected)
			}
		})
	}
}

func Test_packageCheck(t *testing.T) {
	tests := []struct {
		name     string
		product  *models.Product
		expected []sheinReadyCheck
	}{
		{
			name: "no variants",
			product: &models.Product{
				Variants: []*models.ProductVariant{},
			},
			expected: nil,
		},
		{
			name: "variant height is not filled",
			product: &models.Product{
				Variants: []*models.ProductVariant{
					{
						Width:  models.ProductVariantShippingSetting{Unit: "cm", Value: 10},
						Length: models.ProductVariantShippingSetting{Unit: "cm", Value: 10},
						Weight: models.ProductVariantShippingSetting{Unit: "cm", Value: 10},
					},
				},
			},
			expected: []sheinReadyCheck{
				&packageDimensionsHeightIsEmpty{},
			},
		},
		{
			name: "variant width is not filled",
			product: &models.Product{
				Variants: []*models.ProductVariant{
					{
						Height: models.ProductVariantShippingSetting{Unit: "cm", Value: 10},
						Length: models.ProductVariantShippingSetting{Unit: "cm", Value: 10},
						Weight: models.ProductVariantShippingSetting{Unit: "cm", Value: 10},
					},
				},
			},
			expected: []sheinReadyCheck{
				&packageDimensionsWidthIsEmpty{},
			},
		},
		{
			name: "variant length is not filled",
			product: &models.Product{
				Variants: []*models.ProductVariant{
					{
						Height: models.ProductVariantShippingSetting{Unit: "cm", Value: 10},
						Width:  models.ProductVariantShippingSetting{Unit: "cm", Value: 10},
						Weight: models.ProductVariantShippingSetting{Unit: "cm", Value: 10},
					},
				},
			},
			expected: []sheinReadyCheck{
				&packageDimensionsLengthIsEmpty{},
			},
		},
		{
			name: "variant weight is not filled",
			product: &models.Product{
				Variants: []*models.ProductVariant{
					{
						Height: models.ProductVariantShippingSetting{Unit: "cm", Value: 10},
						Width:  models.ProductVariantShippingSetting{Unit: "cm", Value: 10},
						Length: models.ProductVariantShippingSetting{Unit: "cm", Value: 10},
					},
				},
			},
			expected: []sheinReadyCheck{
				&packageWeightIsEmpty{},
			},
		},
		{
			name: "all package dimensions are filled",
			product: &models.Product{
				Variants: []*models.ProductVariant{
					{
						Height: models.ProductVariantShippingSetting{Unit: "cm", Value: 10},
						Width:  models.ProductVariantShippingSetting{Unit: "cm", Value: 10},
						Length: models.ProductVariantShippingSetting{Unit: "cm", Value: 10},
						Weight: models.ProductVariantShippingSetting{Unit: "cm", Value: 10},
					},
				},
			},
			expected: []sheinReadyCheck{},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			model := &sheinReadyCheckModel{
				product: tt.product,
			}
			result := model.packageCheck()
			if !reflect.DeepEqual(result, tt.expected) {
				t.Errorf("packageCheck() = %v, expected %v", result, tt.expected)
			}
		})
	}
}

func Test_variantOptionCheck(t *testing.T) {
	tests := []struct {
		name     string
		product  *models.Product
		expected []sheinReadyCheck
	}{
		{
			name: "no variants",
			product: &models.Product{
				Variants: []*models.ProductVariant{},
			},
			expected: []sheinReadyCheck{},
		},
		{
			name: "variant options are empty",
			product: &models.Product{
				Variants: []*models.ProductVariant{
					{Options: []*models.ProductVariantOption{}},
				},
			},
			expected: []sheinReadyCheck{
				&sheinVariantOptionIsEmpty{Position: 0},
			},
		},
		{
			name: "variant option is invalid",
			product: &models.Product{
				Variants: []*models.ProductVariant{
					{Options: []*models.ProductVariantOption{
						{SalesChannelOptionID: "", SalesChannelValueID: ""},
					}},
				},
			},
			expected: []sheinReadyCheck{
				&sheinVariantOptionIsInvalid{PositionX: 0, PositionY: 0},
			},
		},
		{
			name: "variant option is valid",
			product: &models.Product{
				Variants: []*models.ProductVariant{
					{Options: []*models.ProductVariantOption{
						{SalesChannelOptionID: "option1", SalesChannelValueID: "value1"},
					}},
				},
			},
			expected: []sheinReadyCheck{},
		},
		{
			name: "multiple variants with mixed options",
			product: &models.Product{
				Variants: []*models.ProductVariant{
					{Options: []*models.ProductVariantOption{
						{SalesChannelOptionID: "option1", SalesChannelValueID: "value1"},
					}},
					{Options: []*models.ProductVariantOption{
						{SalesChannelOptionID: "", SalesChannelValueID: ""},
					}},
				},
			},
			expected: []sheinReadyCheck{
				&sheinVariantOptionIsInvalid{PositionX: 1, PositionY: 0},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			model := &sheinReadyCheckModel{
				product: tt.product,
			}
			result := model.variantOptionCheck()
			if !reflect.DeepEqual(result, tt.expected) {
				t.Errorf("variantOptionCheck() = %v, expected %v", result, tt.expected)
			}
		})
	}
}

func Test_variantPriceCheck(t *testing.T) {
	tests := []struct {
		name     string
		product  *models.Product
		expected []sheinReadyCheck
	}{
		{
			name: "no variants",
			product: &models.Product{
				Variants: []*models.ProductVariant{},
			},
			expected: []sheinReadyCheck{},
		},
		{
			name: "variant price is zero",
			product: &models.Product{
				Variants: []*models.ProductVariant{{Price: models.ProductVariantPrice{Amount: "0"}}},
			},
			expected: []sheinReadyCheck{
				&variantPriceInvalid{position: 0},
			},
		},
		{
			name: "variant price is empty",
			product: &models.Product{
				Variants: []*models.ProductVariant{{Price: models.ProductVariantPrice{Amount: ""}}},
			},
			expected: []sheinReadyCheck{
				&variantPriceInvalid{position: 0},
			},
		},
		{
			name: "variant price is valid",
			product: &models.Product{
				Variants: []*models.ProductVariant{{Price: models.ProductVariantPrice{Amount: "100"}}},
			},
			expected: []sheinReadyCheck{},
		},
		{
			name: "multiple variants with mixed prices",
			product: &models.Product{
				Variants: []*models.ProductVariant{
					{Price: models.ProductVariantPrice{Amount: "100"}},
					{Price: models.ProductVariantPrice{Amount: "0"}},
				},
			},
			expected: []sheinReadyCheck{
				&variantPriceInvalid{position: 1},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			model := &sheinReadyCheckModel{
				product: tt.product,
			}
			result := model.variantPriceCheck()
			if !reflect.DeepEqual(result, tt.expected) {
				t.Errorf("variantPriceCheck() = %v, expected %v", result, tt.expected)
			}
		})
	}
}
