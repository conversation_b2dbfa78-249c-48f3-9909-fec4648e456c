{"id": "020c641da99c453b858182bd03c64314", "sales_channel": {"store_key": "store_key", "platform": "tiktok-shop", "country_region": "US"}, "organization": {"id": "07251bb931674fa18a5d0f6ed95d15fa"}, "sales_channel_product": {"id": "", "connector_product_id": "", "state": "", "metrics": {"created_at": "0001-01-01T00:00:00Z", "updated_at": "0001-01-01T00:00:00Z"}}, "products_center_product": {"id": "720bb0010df4493b88c006e8a6a1b4f2", "connector_product_id": "fd6e1c7351d24533bd4da6ba0eaf1adf", "publish_state": "active", "source": {"store_key": "kim-test-0515", "platform": "shopify", "id": "7889478713502"}}, "ready": {"status": "ready", "failed_reasons": [], "last_failed_at": "0001-01-01T00:00:00Z"}, "settings": {"inventory_sync": {"preference": "store", "customized": {"auto_sync": "", "available_quantity_percent": 0, "low_quantity_threshold": {"state": "", "value": 0}, "active_warehouses": null, "last_effect_at": "0001-01-01T00:00:00Z", "follow_source_allow_backorder": ""}, "last_effect_at": "0001-01-01T00:00:00Z"}, "price_sync": {"preference": "store", "customized": {"source_field": "", "auto_sync": "", "rules": null}, "last_effect_at": "0001-01-01T00:00:00Z"}, "product_sync": {"preference": "customized", "customized": {"update_detail": {"fields": null, "auto_sync": "enabled"}, "update_variants": {"auto_sync": "enabled"}, "fulfillment_service": "", "last_effect_at": "0001-01-01T00:00:00Z"}, "last_effect_at": "0001-01-01T00:00:00Z"}}, "product": {"title": "Waterproof Nylon Cloth Drawstring Bucket Bag Large Capacity", "short_description": "<p><b>Product information:</b><br>Color: white, mint green, black, high grade gray, orange, Army Green, brown, beige, khaki<br>Outer bag type: Three-dimensional pocket<br>Hardness: soft<br>Material: Nylon<br>Suitcase shape: Bucket<br>Bag internal structure: zipper change bag<br>Popular elements: sewing thread<br>Number of shoulder straps: Single<br>Size: width 28cm height 30cm bottom thickness 15cm</p>\n<p><br><b>Packing list: </b><br>Crossbody bagX1</p>", "categories": [{"sales_channel_id": "903688", "name": ""}], "tags": null, "brand": {"sales_channel_id": "", "name": ""}, "vendor": "kim-test-0515", "description": "<p><b>Product information:</b><br>Color: white, mint green, black, high grade gray, orange, Army Green, brown, beige, khaki<br>Outer bag type: Three-dimensional pocket<br>Hardness: soft<br>Material: Nylon<br>Suitcase shape: Bucket<br>Bag internal structure: zipper change bag<br>Popular elements: sewing thread<br>Number of shoulder straps: Single<br>Size: width 28cm height 30cm bottom thickness 15cm</p>\n<p><br><b>Packing list: </b><br>Crossbody bagX1</p>", "size_chart": {"images": null}, "certifications": null, "attributes": null, "variants": [{"id": "df76c36eb5714137bbe0bf0e75f4d028", "position": 1, "inventory_quantity": 999, "sku": "66960342-white", "barcode": {"value": "", "type": ""}, "title": "White", "price": {"currency": "USD", "amount": "10.8654"}, "cost": {"currency": "", "amount": "0"}, "image_url": "https://p16-oec-ttp.tiktokcdn-us.com/tos-useast5-i-omjb5zjo8w-tx/0e607e8dccb648608ee20413112ada8c~tplv-omjb5zjo8w-origin-jpeg.jpeg?from=1432613627", "compare_at_price": {"currency": "USD", "amount": "10.8654"}, "length": {"unit": "cm", "value": 11}, "width": {"unit": "cm", "value": 11}, "height": {"unit": "cm", "value": 11}, "weight": {"unit": "g", "value": 240}, "allow_backorder": false, "options": [{"name": "Color", "value": "White"}], "fulfillment_service": "dropshipman-service", "requires_shipping": false}, {"id": "99240b3d483f4479a4ee3c80b7a5438e", "position": 2, "inventory_quantity": 999, "sku": "66960342-mint-green", "barcode": {"value": "", "type": ""}, "title": "Mint Green", "price": {"currency": "USD", "amount": "10.8654"}, "cost": {"currency": "", "amount": "0"}, "image_url": "https://p16-oec-ttp.tiktokcdn-us.com/tos-useast5-i-omjb5zjo8w-tx/11a8adcdc63a495ebf3bbe80a12d7a53~tplv-omjb5zjo8w-origin-jpeg.jpeg?from=1432613627", "compare_at_price": {"currency": "USD", "amount": "10.8654"}, "length": {"unit": "cm", "value": 11}, "width": {"unit": "cm", "value": 11}, "height": {"unit": "cm", "value": 11}, "weight": {"unit": "g", "value": 240}, "allow_backorder": false, "options": [{"name": "Color", "value": "Mint Green"}], "fulfillment_service": "dropshipman-service", "requires_shipping": false}], "options": [{"name": "Color", "position": 1, "values": ["White", "Mint Green"]}], "media": [{"sales_channel_id": "tos-useast5-i-omjb5zjo8w-tx/0e607e8dccb648608ee20413112ada8c", "type": "image", "position": 0, "thumbnail": {"url": "tos-useast5-i-omjb5zjo8w-tx/0e607e8dccb648608ee20413112ada8c"}, "url": "https://p16-oec-ttp.tiktokcdn-us.com/tos-useast5-i-omjb5zjo8w-tx/0e607e8dccb648608ee20413112ada8c~tplv-omjb5zjo8w-origin-jpeg.jpeg?from=1432613627", "mime_type": "", "external_video_host": ""}]}, "relations": [{"id": "2e3b0da17563412a9f48002ae3241b65", "sales_channel": {"store_key": "7495690233688722264", "platform": "tiktok-shop"}, "variant_position": 1, "product_listing_variant_id": "df76c36eb5714137bbe0bf0e75f4d028", "sales_channel_variant": {"id": "", "connector_product_id": "", "product_id": "", "sku": ""}, "products_center_variant": {"id": "df76c36eb5714137bbe0bf0e75f4d028", "product_id": "720bb0010df4493b88c006e8a6a1b4f2", "connector_product_id": "fd6e1c7351d24533bd4da6ba0eaf1adf", "source": {"store_key": "kim-test-0515", "platform": "shopify", "id": "43826877923486", "product_id": "7889478713502", "sku": "66960342-white"}}}, {"id": "e7de8e2025f6447e8e6fa511010a4fa7", "sales_channel": {"store_key": "7495690233688722264", "platform": "tiktok-shop"}, "variant_position": 2, "product_listing_variant_id": "99240b3d483f4479a4ee3c80b7a5438e", "sales_channel_variant": {"id": "", "connector_product_id": "", "product_id": "", "sku": ""}, "products_center_variant": {"id": "99240b3d483f4479a4ee3c80b7a5438e", "product_id": "720bb0010df4493b88c006e8a6a1b4f2", "connector_product_id": "fd6e1c7351d24533bd4da6ba0eaf1adf", "source": {"store_key": "kim-test-0515", "platform": "shopify", "id": "43826877956254", "product_id": "7889478713502", "sku": "66960342-mint-green"}}}], "feed_customization_params": {"feed_category_template_id": "fd6e1c7351d24533bd4da6ba0eaf1add"}}