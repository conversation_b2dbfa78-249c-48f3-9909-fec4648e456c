package product_listing

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"

	elastic "github.com/olivere/elastic/v7"
	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/AfterShip/pltf-pd-product-listings/internal/consts"
	"github.com/AfterShip/pltf-pd-product-listings/internal/logger"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
	"github.com/AfterShip/pltf-pd-product-listings/internal/utils/elasticsearch"
	"github.com/AfterShip/pltf-pd-product-listings/internal/utils/slicex"
)

type esRepo interface {
	SearchProductListingsIDs(ctx context.Context, args *SearchProductListingArgs) ([]string, *models.Pagination, error)
	Count(ctx context.Context, args *SearchProductListingArgs) (int64, error)
	BatchUpsertProductListings(ctx context.Context, productListings []*ProductListing, ops ...EsOption) error
	DeleteProductListings(ctx context.Context, productListings []*ProductListing, opes ...EsOption) error
	AggregateCategoryIDs(ctx context.Context, args *SearchProductListingArgs) ([]string, error)

	RefreshESIndex(ctx context.Context)
	DeleteProductListingByID(ctx context.Context, id string) error

	ESProxyProductListingCount(ctx context.Context, query string) (int64, error)                  // ES Proxy
	ESProxyProductListingSearch(ctx context.Context, query string) (*elastic.SearchResult, error) // ES Proxy
}

type productListingESRepoImpl struct {
	cli *elastic.Client
}

func NewProductListingESRepo(client *elastic.Client) esRepo {
	return &productListingESRepoImpl{
		cli: client,
	}
}

func (r *productListingESRepoImpl) SearchProductListingsIDs(ctx context.Context,
	args *SearchProductListingArgs) ([]string, *models.Pagination, error) {
	defaultSortColumn := "updated_at"
	// 默认倒序
	defaultSortOrderAscending := false

	index := searchIndex

	query := args.buildQuery()
	esSvc := r.cli.Search(index)
	esSvc = esSvc.Query(query).Size(int(args.Limit)).Sort(defaultSortColumn, defaultSortOrderAscending).
		Sort("id", false).FetchSource(false)

	err := buildSearchPagination(esSvc, args)
	if err != nil {
		return []string{}, nil, errors.WithStack(err)
	}

	result, err := esSvc.Do(ctx)
	if err != nil {
		return []string{}, nil, errors.WithStack(err)
	}

	productListingIDs := make([]string, 0)
	for _, hit := range result.Hits.Hits {
		productListingIDs = append(productListingIDs, hit.Id)
	}

	pagination := &models.Pagination{}
	if args.buildSearchType() == esPaginationTypePage {
		pagination.Page = args.Page
		if result.TotalHits() <= args.Limit*args.Page {
			pagination.HasNextPage = false
		} else {
			pagination.HasNextPage = true
		}
	} else {
		pagination.PreviousCursor = args.Cursor
		pagination.NextCursor = consts.EndCursor
		if len(result.Hits.Hits) > 0 {
			nextCursor, err := elasticsearch.BuildNextCursor(result.Hits.Hits[len(result.Hits.Hits)-1].Sort)
			if err != nil {
				return []string{}, nil, errors.Wrap(err, "gen next cursor error")
			}
			pagination.NextCursor = nextCursor
			pagination.HasNextPage = true
		}
	}
	pagination.Total = result.TotalHits()
	pagination.Limit = args.Limit

	return productListingIDs, pagination, nil
}

func (r *productListingESRepoImpl) Count(ctx context.Context, args *SearchProductListingArgs) (int64, error) {
	index := searchIndex

	query := args.buildQuery()
	esSvc := r.cli.Count(index)
	esSvc = esSvc.Query(query)

	result, err := esSvc.Do(ctx)
	if err != nil {
		return 0, err
	}

	return result, nil
}

func (r *productListingESRepoImpl) BatchUpsertProductListings(ctx context.Context,
	productListings []*ProductListing, ops ...EsOption) error {
	bulk := r.cli.Bulk()
	esOption := &UpsertProductListingESOption{}
	for _, op := range ops {
		op(esOption)
	}

	for i := range productListings {
		pl := productListings[i]

		index := r.getEsIndex(productListings[i])
		version := r.getVersion(pl, esOption.ForceRefresh, esOption.VersionOffset)

		body, err := toProductListingsEsModel(productListings[i])
		if err != nil {
			return err
		}
		req := elastic.NewBulkIndexRequest().
			Index(index).
			Id(productListings[i].ID).
			Version(version + 1). // document version control
			VersionType(consts.EsVersionType).
			Doc(body)
		bulk.Add(req)
	}
	resp, err := bulk.Do(ctx)
	if err != nil {
		if elastic.IsStatusCode(err, http.StatusTooManyRequests) {
			return errors.WithStack(ErrTooManyRequest)
		}
		return errors.WithStack(err)
	}
	for _, failedItem := range resp.Failed() {
		// version conflict 409 不需要报错
		if failedItem.Status == 409 {
			continue
		}
		cause := fmt.Sprintf("unknown, status: %d", failedItem.Status)
		if failedItem.Error != nil {
			cause = failedItem.Error.Reason
		}
		return errors.WithMessage(ErrESBatchUpsert, cause)
	}
	return nil
}

func (r *productListingESRepoImpl) DeleteProductListings(ctx context.Context, productListings []*ProductListing, opes ...EsOption) error {
	bulk := r.cli.Bulk()
	esOption := &UpsertProductListingESOption{}
	for _, op := range opes {
		op(esOption)
	}

	for index := range productListings {
		esIndex := r.getEsIndex(productListings[index])
		req := elastic.NewBulkDeleteRequest().Index(esIndex).Id(productListings[index].ID)
		bulk.Add(req)
	}

	resp, err := bulk.Do(ctx)
	if err != nil {
		if elastic.IsStatusCode(err, http.StatusTooManyRequests) {
			return errors.WithStack(ErrTooManyRequest)
		}
		return errors.WithStack(err)
	}

	for _, failedItem := range resp.Failed() {
		if failedItem.Status == 409 {
			continue
		}
		cause := fmt.Sprintf("unknown, status: %d", failedItem.Status)
		if failedItem.Error != nil {
			cause = failedItem.Error.Reason
		}
		return errors.WithMessage(ErrESBatchDelete, cause)
	}

	return nil
}

func (r *productListingESRepoImpl) getEsIndex(pl *ProductListing) string {
	return createIndex + strconv.Itoa(pl.CreatedAt.Year())
}

func (r *productListingESRepoImpl) getEsIndexWithEsModel(pl *productListingsEsModel) string {
	return createIndex + strconv.Itoa(pl.CreatedAt.Year())
}

// getVersion 版本号取最大值
func (r *productListingESRepoImpl) getVersion(pl *ProductListing, forceRefresh bool, offset int) int64 {
	version := pl.UpdatedAt.UnixNano()
	if forceRefresh { // 如果指定了强制更新，补充偏移量，解决版本冲突问题
		version += int64(offset)
	}
	return version
}

func (r *productListingESRepoImpl) AggregateCategoryIDs(ctx context.Context, args *SearchProductListingArgs) ([]string, error) {
	query := args.buildQuery()
	agg := elastic.NewTermsAggregation().Field("product_sales_channel_category_ids").Size(2000)

	// Ensure that the returned results contain only aggregated information and not the actual document data.
	size := 0
	aggName := "sales_channel_category_ids"
	esSvc := r.cli.Search(searchIndex)
	esSvc = esSvc.Query(query).Size(size).Aggregation(aggName, agg)

	result, err := esSvc.Do(ctx)
	if err != nil {
		return []string{}, errors.WithStack(err)
	}

	categoryIDs := make([]string, 0)
	aggResult, found := result.Aggregations.Terms(aggName)
	if found {
		for _, bucket := range aggResult.Buckets {
			id, _ := bucket.Key.(string)
			if id == "" || id == UncategoriedValueInEs {
				continue
			}
			categoryIDs = append(categoryIDs, id)
		}
	}

	return slicex.RemoveEmptyStrings(categoryIDs), nil
}

func (r *productListingESRepoImpl) RefreshESIndex(ctx context.Context) {
	_, err := r.cli.Refresh().Index(searchIndex).Do(ctx)
	if err != nil {
		logger.Get().ErrorCtx(ctx, "Elasticsearch index refresh failed", zap.Error(err))
	}
}

func (r *productListingESRepoImpl) ESProxyProductListingCount(ctx context.Context, query string) (int64, error) {
	index := searchIndex
	esSvc := r.cli.Count(index).BodyString(query)
	result, err := esSvc.Do(ctx)
	if err != nil {
		return 0, errors.WithStack(err)
	}
	return result, nil
}

func (r *productListingESRepoImpl) ESProxyProductListingSearch(ctx context.Context, query string) (*elastic.SearchResult, error) {
	index := searchIndex
	return r.cli.Search(index).Source(query).Do(ctx)
}

func (r *productListingESRepoImpl) DeleteProductListingByID(ctx context.Context, id string) error {

	esModel, err := r.getEsModelByID(ctx, id)
	if err != nil {
		return err
	}

	esIndex := r.getEsIndexWithEsModel(esModel)

	_, err = r.cli.Delete().Index(esIndex).Id(id).Do(ctx)
	if err != nil {
		return err
	}

	return nil
}

func (r *productListingESRepoImpl) getEsModelByID(ctx context.Context, id string) (*productListingsEsModel, error) {

	query := elastic.NewIdsQuery().Ids(id)
	result, err := r.cli.Search(searchIndex).
		Query(query).
		Size(10).Do(ctx)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	if result.Hits == nil || len(result.Hits.Hits) == 0 {
		return nil, errors.WithStack(ErrNotFound)
	}

	resultData := make([]*productListingsEsModel, 0, len(result.Hits.Hits))
	for _, hits := range result.Hits.Hits {
		sp := &productListingsEsModel{}
		if err = json.Unmarshal(hits.Source, sp); err != nil {
			return nil, errors.WithStack(err)
		}
		resultData = append(resultData, sp)
	}

	if len(resultData) == 0 {
		return nil, errors.WithStack(ErrNotFound)
	}

	return resultData[0], nil
}
