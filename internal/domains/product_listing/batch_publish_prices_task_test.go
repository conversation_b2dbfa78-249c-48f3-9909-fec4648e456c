package product_listing

import (
	"testing"
	"time"

	"github.com/go-playground/validator/v10"
	"github.com/stretchr/testify/assert"

	"github.com/AfterShip/gopkg/log"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
)

func TestBatchPublishPricesTask_validate(t *testing.T) {
	tests := []struct {
		name    string
		input   *BatchPublishPricesTaskInput
		wantErr error
	}{
		{
			name: "有效输入",
			input: &BatchPublishPricesTaskInput{
				Organization: models.Organization{
					ID: "org123",
				},
				SalesChannel: models.SalesChannel{
					Platform: "tiktok",
					StoreKey: "store123",
				},
				FromEvent:      "manual",
				EventTimestamp: time.Now(),
				ProductListings: []priceProductListing{
					{
						ID: "pl123",
						Relations: []priceTaskProductListingRelation{
							{
								ID: "relation123",
							},
						},
					},
				},
			},
			wantErr: nil,
		},
		{
			name: "Organization.ID为空",
			input: &BatchPublishPricesTaskInput{
				Organization: models.Organization{
					ID: "",
				},
				SalesChannel: models.SalesChannel{
					Platform: "tiktok",
					StoreKey: "store123",
				},
				FromEvent:      "manual",
				EventTimestamp: time.Now(),
				ProductListings: []priceProductListing{
					{
						ID:        "pl123",
						Relations: []priceTaskProductListingRelation{},
					},
				},
			},
			wantErr: ErrUnprocessableEntity,
		},
		{
			name: "SalesChannel.Platform为空",
			input: &BatchPublishPricesTaskInput{
				Organization: models.Organization{
					ID: "org123",
				},
				SalesChannel: models.SalesChannel{
					Platform: "",
					StoreKey: "store123",
				},
				FromEvent:      "manual",
				EventTimestamp: time.Now(),
				ProductListings: []priceProductListing{
					{
						ID:        "pl123",
						Relations: []priceTaskProductListingRelation{},
					},
				},
			},
			wantErr: ErrUnprocessableEntity,
		},
		{
			name: "SalesChannel.StoreKey为空",
			input: &BatchPublishPricesTaskInput{
				Organization: models.Organization{
					ID: "org123",
				},
				SalesChannel: models.SalesChannel{
					Platform: "tiktok",
					StoreKey: "",
				},
				FromEvent:      "manual",
				EventTimestamp: time.Now(),
				ProductListings: []priceProductListing{
					{
						ID:        "pl123",
						Relations: []priceTaskProductListingRelation{},
					},
				},
			},
			wantErr: ErrUnprocessableEntity,
		},
		{
			name: "EventTimestamp为零值",
			input: &BatchPublishPricesTaskInput{
				Organization: models.Organization{
					ID: "org123",
				},
				SalesChannel: models.SalesChannel{
					Platform: "tiktok",
					StoreKey: "store123",
				},
				FromEvent:      "manual",
				EventTimestamp: time.Time{}, // 零值时间
				ProductListings: []priceProductListing{
					{
						ID:        "pl123",
						Relations: []priceTaskProductListingRelation{},
					},
				},
			},
			wantErr: ErrUnprocessableEntity,
		},
		{
			name: "ProductListings为空",
			input: &BatchPublishPricesTaskInput{
				Organization: models.Organization{
					ID: "org123",
				},
				SalesChannel: models.SalesChannel{
					Platform: "tiktok",
					StoreKey: "store123",
				},
				FromEvent:       "manual",
				EventTimestamp:  time.Now(),
				ProductListings: []priceProductListing{},
			},
			wantErr: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			task := &BatchPublishPricesTask{
				Logger:    log.GlobalLogger(),
				Validator: validator.New(),
			}
			err := task.validate(tt.input)
			if tt.wantErr != nil {
				if _, ok := tt.wantErr.(validator.ValidationErrors); ok {
					assert.IsType(t, validator.ValidationErrors{}, err)
				} else {
					assert.Equal(t, tt.wantErr, err)
				}
			} else {
				assert.NoError(t, err)
			}
		})
	}
}
