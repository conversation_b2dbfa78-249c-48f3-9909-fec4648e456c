package product_listing

import (
	"fmt"
	"reflect"
	"regexp"
	"sort"
	"strings"
	"time"

	"go.uber.org/zap"

	"github.com/AfterShip/gopkg/facility/set"
	"github.com/AfterShip/pltf-pd-product-listings/internal/consts"
	"github.com/AfterShip/pltf-pd-product-listings/internal/logger"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
)

// compareModel listing 比较模型
type compareModel struct {
	listing             *ProductListing
	lastListing         *ProductListing
	compareSetting      compareSetting
	ttsCDNDomainRegExps []*regexp.Regexp
}

type compareSetting struct {
	ignoreCompareTitle          bool
	ignoreCompareMedia          bool
	ignoreCompareDescription    bool
	ignoreCompareVariant        bool
	ignoreCompareVariantPackage bool
	ignoreCompareVariantImage   bool
	ignoreCompareCategory       bool
	ignoreCompareBrand          bool
	ignoreCompareAttributes     bool
	ignoreCompareSize<PERSON>hart      bool
	ignoreCompareCertification  bool
	ignoreCompareBarcode        bool
}

// compareProductModel listing 比较模型初始化
func newCompareModel(listing, lastListing *ProductListing, setting compareSetting, ttsCDNDomainRegExps []*regexp.Regexp) *compareModel {
	return &compareModel{
		listing:             listing,
		lastListing:         lastListing,
		compareSetting:      setting,
		ttsCDNDomainRegExps: ttsCDNDomainRegExps,
	}
}

// needPublish 是否需要刊登
func (m *compareModel) needPublish() bool {
	titleModify := m.isTitleModify()
	descriptionModify := m.isDescriptionModify()
	mediaModify := m.isMediaModify()
	variantModify := m.isVariantModify()
	categoryModify := m.isCategoryModify()
	brandModify := m.isBrandModify()
	attributesModify := m.isAttributesModify()
	sizeChartModify := m.isSizeChartModify()
	certificationModify := m.isCertificationModify()
	complianceModify := m.isComplianceModify()
	productOptionModify := m.isProductOptionModify()

	logger.Get().Info("needPublish",
		zap.String("product_listing_id", m.listing.ID),
		zap.Bool("titleModify", titleModify),
		zap.Bool("descriptionModify", descriptionModify),
		zap.Bool("mediaModify", mediaModify),
		zap.Bool("variantModify", variantModify),
		zap.Bool("categoryModify", categoryModify),
		zap.Bool("brandModify", brandModify),
		zap.Bool("attributesModify", attributesModify),
		zap.Bool("sizeChartModify", sizeChartModify),
		zap.Bool("certificationModify", certificationModify),
		zap.Bool("complianceModify", complianceModify),
		zap.Bool("productOptionModify", productOptionModify))

	return titleModify ||
		descriptionModify ||
		mediaModify ||
		variantModify ||
		categoryModify ||
		brandModify ||
		attributesModify ||
		sizeChartModify ||
		certificationModify ||
		complianceModify ||
		productOptionModify
}

// needPublishWithSetting 是否需要刊登
func (m *compareModel) needPublishWithSetting(unionSetting *storeProductListingSetting) bool {
	titleModify := unionSetting.autoSyncProductDetailField(consts.ProductDetailFieldTitle) && m.isTitleModify()
	descriptionModify := unionSetting.autoSyncProductDetailField(consts.ProductDetailFieldDescription) && m.isDescriptionModify()
	mediaModify := unionSetting.autoSyncProductDetailField(consts.ProductDetailFieldMedia) && m.isMediaModify()
	variantModify := unionSetting.autoSyncVariant() && m.isVariantModify()

	logger.Get().Info("needPublishWithSetting",
		zap.String("product_listing_id", m.listing.ID),
		zap.Bool("titleModify", titleModify),
		zap.Bool("descriptionModify", descriptionModify),
		zap.Bool("mediaModify", mediaModify),
		zap.Bool("variantModify", variantModify))

	return titleModify ||
		descriptionModify ||
		mediaModify ||
		variantModify
}

// needPublishPriceAndInventory 是否需要刊登价格和库存，link variant 从 unsync 变成了 sync
func (m *compareModel) needPublishPriceAndInventory() bool {
	// listing 从非 active 变成 active
	if m.lastListing.State != consts.ProductListingProductStateActive &&
		m.listing.State == consts.ProductListingProductStateActive {
		return true
	}

	// sku 从 unsync 变成 sync
	listingRelationMap := make(map[string]*ProductListingRelation, len(m.listing.Relations))
	for i := range m.listing.Relations {
		listingRelationMap[m.listing.Relations[i].ID] = m.listing.Relations[i]
	}

	for i := range m.lastListing.Relations {
		if relation, ok := listingRelationMap[m.lastListing.Relations[i].ID]; ok {
			if m.lastListing.Relations[i].SyncStatus == consts.SyncStatusUnsync &&
				relation.SyncStatus == consts.SyncStatusSynced {
				return true
			}
		}
	}

	// shein skc audit 从 review 到 success 需要刊登
	if m.lastListing.SalesChannel.Platform == consts.Shein {
		mainOption, ok1 := m.listing.Product.GetMainOption()
		lastMainOption, ok2 := m.lastListing.Product.GetMainOption()
		if ok1 && ok2 {
			lastMainOptionValueMap := make(map[string]models.ProductOptionValueDetail, len(lastMainOption.Values))
			for i := range lastMainOption.ValueDetails {
				if lastMainOption.ValueDetails[i].SalesChannelID == "" {
					continue
				}
				lastMainOptionValueMap[lastMainOption.ValueDetails[i].SalesChannelID] = lastMainOption.ValueDetails[i]
			}

			for i := range mainOption.ValueDetails {
				if mainOption.ValueDetails[i].SalesChannelID == "" {
					continue
				}
				lastMainOptionValue, ok := lastMainOptionValueMap[mainOption.ValueDetails[i].SalesChannelID]
				// new skc
				if !ok && mainOption.ValueDetails[i].Audit.State == consts.ProductOptionValueAuditStateSucceeded {
					return true
				}

				// skc failed, reviewing -> success
				if (lastMainOptionValue.Audit.State == consts.ProductOptionValueAuditStateReviewing ||
					lastMainOptionValue.Audit.State == consts.ProductOptionValueAuditStateFailed) &&
					mainOption.ValueDetails[i].Audit.State == consts.ProductOptionValueAuditStateSucceeded {
					return true
				}
			}
		}
	}

	return false
}

// isTitleModify 比较配置以及产品标题是否变化
func (m *compareModel) isTitleModify() bool {
	if m.compareSetting.ignoreCompareTitle {
		return false
	}

	return strings.TrimSpace(m.listing.Product.Title) != strings.TrimSpace(m.lastListing.Product.Title)
}

// isDescriptionModify 比较配置以及产品描述是否变化
func (m *compareModel) isDescriptionModify() bool {
	if m.compareSetting.ignoreCompareDescription {
		return false
	}

	return m.listing.Product.Description != m.lastListing.Product.Description
}

// isMediaModify 比较配置以及产品图片是否变化
func (m *compareModel) isMediaModify() bool {
	if m.compareSetting.ignoreCompareMedia {
		return false
	}

	// 只对比前 9 张图片
	lastMedia := m.lastListing.Product.Media
	if len(lastMedia) > 9 {
		lastMedia = lastMedia[:9]
	}

	media := m.listing.Product.Media
	if len(media) > 9 {
		media = media[:9]
	}

	// 数量不同是变化
	if len(media) != len(lastMedia) {
		return true
	}
	// 顺序不同也是变化
	for i := range media {
		if media[i].SalesChannelID != lastMedia[i].SalesChannelID {
			return true
		}
	}

	return false
}

func (m *compareModel) isCategoryModify() bool {
	if m.compareSetting.ignoreCompareCategory {
		return false
	}

	if len(m.listing.Product.Categories) != len(m.lastListing.Product.Categories) {
		return true
	}

	for i := range m.listing.Product.Categories {
		if m.listing.Product.Categories[i].SalesChannelID != m.lastListing.Product.Categories[i].SalesChannelID {
			return true
		}
	}

	return false
}

func (m *compareModel) isBrandModify() bool {
	if m.compareSetting.ignoreCompareBrand {
		return false
	}

	return m.listing.Product.Brand.SalesChannelID != m.lastListing.Product.Brand.SalesChannelID
}

// nolint:gocyclo
func (m *compareModel) isAttributesModify() bool {
	if m.compareSetting.ignoreCompareAttributes {
		return false
	}

	lastAttributes := map[string]*models.ProductAttribute{}
	newAttributes := map[string]*models.ProductAttribute{}

	for i := range m.lastListing.Product.Attributes {
		if m.lastListing.Product.Attributes[i] == nil {
			continue
		}
		if m.lastListing.Product.Attributes[i].SalesChannelID != "" &&
			len(m.lastListing.Product.Attributes[i].Values) > 0 {
			lastAttributes[m.lastListing.Product.Attributes[i].SalesChannelID] = m.lastListing.Product.Attributes[i]
		}
	}
	for i := range m.listing.Product.Attributes {
		if m.listing.Product.Attributes[i] == nil {
			continue
		}
		if m.listing.Product.Attributes[i].SalesChannelID != "" &&
			len(m.listing.Product.Attributes[i].Values) > 0 {
			newAttributes[m.listing.Product.Attributes[i].SalesChannelID] = m.listing.Product.Attributes[i]
		}
	}

	if len(newAttributes) != len(lastAttributes) {
		return true
	}

	for key := range newAttributes {
		if lastAttributes[key] == nil {
			return true
		}
		if len(newAttributes[key].Values) != len(lastAttributes[key].Values) {
			return true
		}

		for i := range newAttributes[key].Values {
			if newAttributes[key].Values[i].SalesChannelID != lastAttributes[key].Values[i].SalesChannelID ||
				newAttributes[key].Values[i].Name != lastAttributes[key].Values[i].Name {
				return true
			}
		}
	}

	return false
}

func (m *compareModel) isSizeChartModify() bool {
	if m.compareSetting.ignoreCompareSizeChart {
		return false
	}

	return m.isSizeChartImagesModify() || m.isSizeChartAttributesModify()
}

func (m *compareModel) isSizeChartImagesModify() bool {
	if len(m.listing.Product.SizeChart.Images) != len(m.lastListing.Product.SizeChart.Images) {
		return true
	}

	lastImageSalesChannelIDSet := set.NewStringSet()
	newImageSalesChannelIDSet := set.NewStringSet()
	for i := range m.lastListing.Product.SizeChart.Images {
		lastImageSalesChannelIDSet.Add(m.lastListing.Product.SizeChart.Images[i].SalesChannelID)
	}
	for i := range m.listing.Product.SizeChart.Images {
		newImageSalesChannelIDSet.Add(m.listing.Product.SizeChart.Images[i].SalesChannelID)
	}

	return !lastImageSalesChannelIDSet.Equal(newImageSalesChannelIDSet)
}

func (m *compareModel) isSizeChartAttributesModify() bool {
	if len(m.listing.Product.SizeChart.Attributes) != len(m.lastListing.Product.SizeChart.Attributes) {
		return true
	}
	if len(m.listing.Product.SizeChart.Attributes) == 0 {
		return false
	}

	listingAttributes := m.listing.Product.SizeChart.Attributes
	lastListingAttributes := m.lastListing.Product.SizeChart.Attributes
	sort.Slice(listingAttributes, func(i, j int) bool {
		return listingAttributes[i].SalesChannelID < listingAttributes[j].SalesChannelID
	})
	sort.Slice(lastListingAttributes, func(i, j int) bool {
		return lastListingAttributes[i].SalesChannelID < lastListingAttributes[j].SalesChannelID
	})
	for i := range listingAttributes {
		listingAtt := listingAttributes[i]
		lastListingAtt := lastListingAttributes[i]
		if listingAtt.SalesChannelID != lastListingAtt.SalesChannelID ||
			listingAtt.SalesChannelValueID != lastListingAtt.SalesChannelValueID ||
			listingAtt.RelateSalesChannelID != lastListingAtt.RelateSalesChannelID ||
			listingAtt.RelateSalesChannelValueID != lastListingAtt.RelateSalesChannelValueID ||
			listingAtt.Value != lastListingAtt.Value {
			return true
		}
	}

	return false
}

// nolint:gocyclo
func (m *compareModel) isCertificationModify() bool {
	if m.compareSetting.ignoreCompareCertification {
		return false
	}

	lastCertification := map[string]*models.ProductCertification{}
	newCertification := map[string]*models.ProductCertification{}

	for i := range m.lastListing.Product.Certifications {
		if m.lastListing.Product.Certifications[i].SalesChannelID != "" &&
			(len(m.lastListing.Product.Certifications[i].Images) > 0 || len(m.lastListing.Product.Certifications[i].Files) > 0) {
			lastCertification[m.lastListing.Product.Certifications[i].SalesChannelID] = m.lastListing.Product.Certifications[i]
		}
	}

	for i := range m.listing.Product.Certifications {
		if m.listing.Product.Certifications[i].SalesChannelID != "" &&
			(len(m.listing.Product.Certifications[i].Images) > 0 || len(m.listing.Product.Certifications[i].Files) > 0) {
			newCertification[m.listing.Product.Certifications[i].SalesChannelID] = m.listing.Product.Certifications[i]
		}
	}

	if len(newCertification) != len(lastCertification) {
		return true
	}

	for key := range newCertification {
		if lastCertification[key] == nil {
			return true
		}
		if len(newCertification[key].Images) != len(lastCertification[key].Images) ||
			len(newCertification[key].Files) != len(lastCertification[key].Files) {
			return true
		}
		for i := range newCertification[key].Images {
			if newCertification[key].Images[i].SalesChannelID != lastCertification[key].Images[i].SalesChannelID {
				return true
			}
		}
		for i := range newCertification[key].Files {
			if newCertification[key].Files[i].SalesChannelID != lastCertification[key].Files[i].SalesChannelID {
				return true
			}
		}
	}

	return false
}

func (m *compareModel) isComplianceModify() bool {

	// manufacturer check
	manufacturerIDsSet := set.NewStringSet()
	for index := range m.listing.Product.Compliance.Manufacturers {
		manufacturerIDsSet.Add(m.listing.Product.Compliance.Manufacturers[index].SalesChannelID)
	}
	for index := range m.lastListing.Product.Compliance.Manufacturers {
		salesChannelID := m.lastListing.Product.Compliance.Manufacturers[index].SalesChannelID
		if !manufacturerIDsSet.Contains(salesChannelID) {
			return true
		}
		manufacturerIDsSet.Remove(salesChannelID)
	}
	if manufacturerIDsSet.Card() != 0 {
		return true
	}

	// responsible_persons check
	responsiblePersonsIDsSet := set.NewStringSet()
	for index := range m.listing.Product.Compliance.ResponsiblePersons {
		responsiblePersonsIDsSet.Add(m.listing.Product.Compliance.ResponsiblePersons[index].SalesChannelID)
	}
	for index := range m.lastListing.Product.Compliance.ResponsiblePersons {
		salesChannelID := m.lastListing.Product.Compliance.ResponsiblePersons[index].SalesChannelID
		if !responsiblePersonsIDsSet.Contains(salesChannelID) {
			return true
		}
		responsiblePersonsIDsSet.Remove(salesChannelID)
	}
	if responsiblePersonsIDsSet.Card() != 0 {
		return true
	}

	return false
}

func (m *compareModel) isProductOptionModify() bool {
	if m.listing.SalesChannel.Platform != consts.Shein {
		return false
	}

	if len(m.listing.Product.Options) != len(m.lastListing.Product.Options) {
		return true
	}
	if len(m.listing.Product.Options) == 0 {
		return false
	}

	listingMainOption, ok := m.listing.Product.GetMainOption()
	if !ok {
		return false
	}
	lastListingMainOption, ok := m.lastListing.Product.GetMainOption()
	if !ok {
		return false
	}
	if listingMainOption.SalesChannelOptionID != lastListingMainOption.SalesChannelOptionID ||
		listingMainOption.Name != lastListingMainOption.Name ||
		len(listingMainOption.Values) != len(lastListingMainOption.Values) ||
		len(listingMainOption.ValueDetails) != len(lastListingMainOption.ValueDetails) {
		return true
	}

	valueDetails := listingMainOption.ValueDetails
	lastValueDetails := lastListingMainOption.ValueDetails
	if len(valueDetails) == 0 {
		return false
	}
	sort.Slice(valueDetails, func(i, j int) bool {
		return valueDetails[i].Value < valueDetails[j].Value
	})
	sort.Slice(lastValueDetails, func(i, j int) bool {
		return lastValueDetails[i].Value < lastValueDetails[j].Value
	})

	for i := range valueDetails {
		valueDetail := valueDetails[i]
		lastValueDetail := lastValueDetails[i]
		if valueDetail.Value != lastValueDetail.Value ||
			valueDetail.SalesChannelValueID != lastValueDetail.SalesChannelValueID ||
			len(valueDetail.Media) != len(lastValueDetail.Media) {
			return true
		}

		mediaSet := set.NewStringSet()
		lastMediaSet := set.NewStringSet()
		for _, cur := range valueDetail.Media {
			mediaSet.Add(cur.ExternalImageType + "_" + cur.URL)
		}
		for _, cur := range lastValueDetail.Media {
			lastMediaSet.Add(cur.ExternalImageType + "_" + cur.URL)
		}
		if !mediaSet.Equal(lastMediaSet) {
			return true
		}
	}

	return false
}

// isVariantModify 比较 variant 是否变化
//
//nolint:gocyclo
func (m *compareModel) isVariantModify() bool {
	if m.compareSetting.ignoreCompareVariant {
		return false
	}

	// variant 数量不同
	if len(m.listing.Product.Variants) != len(m.lastListing.Product.Variants) {
		return true
	}

	// 比较 variant 的值
	variantCompareSetting := models.VariantCompareSetting{}
	if m.compareSetting.ignoreCompareVariantPackage {
		variantCompareSetting.IgnorePackage = true
	}
	if m.compareSetting.ignoreCompareVariantImage {
		variantCompareSetting.IgnoreImage = true
	}
	if m.compareSetting.ignoreCompareBarcode {
		variantCompareSetting.IgnoreBarcode = true
	}

	// 比较 variant 的值
	lastVariantMap := make(map[string]*models.ProductVariant, len(m.lastListing.Product.Variants))
	for i := range m.lastListing.Product.Variants {
		if m.lastListing.Product.Variants[i].ID != "" {
			lastVariantMap[m.lastListing.Product.Variants[i].ID] = m.lastListing.Product.Variants[i]
		}
	}

	for i := range m.listing.Product.Variants {
		variant := m.listing.Product.Variants[i]
		lastVariant, ok := lastVariantMap[variant.ID]
		if !ok {
			return true
		}
		if variant.IsDiff(lastVariant, variantCompareSetting, m.ttsCDNDomainRegExps) {
			return true
		}
	}

	return m.isVariantsMultipleImageModify()
}

// variants 多图改动
func (m *compareModel) isVariantsMultipleImageModify() bool {
	if m.compareSetting.ignoreCompareVariantImage {
		return false
	}

	if len(m.listing.Product.Options) != len(m.lastListing.Product.Options) {
		return true
	}
	lastOptionMap := make(map[string]*models.ProductOption, len(m.lastListing.Product.Options))
	for i := range m.lastListing.Product.Options {
		lastOptionMap[m.lastListing.Product.Options[i].Name] = m.lastListing.Product.Options[i]
	}
	for i := range m.listing.Product.Options {
		option := m.listing.Product.Options[i]
		lastOption, ok := lastOptionMap[option.Name]
		if !ok {
			return true
		}
		if len(option.ValueDetails) != len(lastOption.ValueDetails) {
			return true
		}
		// 对于每个 SKC 的图片
		for j := range option.ValueDetails {
			valueDetail := option.ValueDetails[j]
			lastValueDetail := lastOption.ValueDetails[j]
			// 对比图片数量
			if len(valueDetail.Media) != len(lastValueDetail.Media) {
				return true
			}
			// 对比图片顺序以及内容
			for k := range valueDetail.Media {
				if valueDetail.Media[k].URL != lastValueDetail.Media[k].URL {
					return true
				}
			}
		}
	}

	return false
}

func (m *compareModel) isPriceSyncSettingModify() bool {
	return !reflect.DeepEqual(m.listing.Settings.PriceSyncSetting, m.lastListing.Settings.PriceSyncSetting)
}

func (m *compareModel) isInventorySyncSettingModify() bool {
	return !reflect.DeepEqual(m.listing.Settings.InventorySyncSetting, m.lastListing.Settings.InventorySyncSetting)
}

func (m *compareModel) isSyncedVariantLinkChange() bool {
	lastRelationsMappingSet := set.NewStringSet()
	newRelationsMappingSet := set.NewStringSet()

	for i := range m.lastListing.Relations {
		if m.lastListing.Relations[i].IsLinkedAndSynced() && m.lastListing.Relations[i].ProductsCenterVariant.ID != "" {
			lastRelationsMappingSet.Add(fmt.Sprintf("%s-%s", m.lastListing.Relations[i].ID, m.lastListing.Relations[i].ProductsCenterVariant.ID))
		}
	}
	for i := range m.listing.Relations {
		if m.listing.Relations[i].IsLinkedAndSynced() && m.listing.Relations[i].ProductsCenterVariant.ID != "" {
			newRelationsMappingSet.Add(fmt.Sprintf("%s-%s", m.listing.Relations[i].ID, m.listing.Relations[i].ProductsCenterVariant.ID))
		}
	}
	return !lastRelationsMappingSet.Equal(newRelationsMappingSet)
}

func (m *compareModel) buildAuditVersion() *AuditVersion {
	pl := ProductListing{}
	// 只保留需要的字段
	pl.ID = m.listing.ID
	pl.Organization = m.listing.Organization
	pl.SalesChannel = m.listing.SalesChannel
	pl.SalesChannelProduct = m.listing.SalesChannelProduct
	pl.ProductsCenterProduct = m.listing.ProductsCenterProduct
	pl.Settings = m.listing.Settings
	pl.Product = m.buildAuditVersionProduct()
	pl.Relations = m.buildAuditVersionRelationsWithProduct(&pl.Product)
	return &AuditVersion{
		CreatedAt:      time.Now(),
		ProductListing: pl,
	}
}

func (m *compareModel) buildAuditVersionProduct() models.Product {
	product := m.lastListing.Product
	product.Title = m.listing.Product.Title
	product.Description = m.listing.Product.Description
	product.Media = m.listing.Product.Media
	product.Categories = m.listing.Product.Categories
	product.Attributes = m.listing.Product.Attributes
	product.Certifications = m.listing.Product.Certifications
	product.SizeChart = m.listing.Product.SizeChart
	product.Compliance = m.listing.Product.Compliance
	product.Options = m.listing.Product.Options
	product.Brand = m.listing.Product.Brand
	product.Variants = m.buildAuditVersionVariant()
	return product
}

func (m *compareModel) buildAuditVersionRelationsWithProduct(product *models.Product) []*ProductListingRelation {
	relations := make([]*ProductListingRelation, 0)

	relationMap := make(map[string]*ProductListingRelation, len(m.listing.Relations))
	for i := range m.listing.Relations {
		relationMap[m.listing.Relations[i].ProductListingVariantID] = m.listing.Relations[i]
	}

	for i := range product.Variants {
		if relation, ok := relationMap[product.Variants[i].ID]; ok {
			relations = append(relations, relation)
		}
	}

	return relations
}

func (m *compareModel) buildAuditVersionVariant() []*models.ProductVariant {
	return m.listing.Product.Variants
}

func (m *compareModel) buildAuditVersionWithSetting(unionSetting *storeProductListingSetting) *AuditVersion {
	pl := ProductListing{}
	// 只保留需要的字段
	pl.ID = m.listing.ID
	pl.Organization = m.listing.Organization
	pl.SalesChannel = m.listing.SalesChannel
	pl.SalesChannelProduct = m.listing.SalesChannelProduct
	pl.ProductsCenterProduct = m.listing.ProductsCenterProduct
	pl.Settings = m.listing.Settings
	pl.Product = m.buildAuditVersionProductWithSetting(unionSetting)
	pl.Relations = m.buildAuditVersionRelationsWithProduct(&pl.Product)
	return &AuditVersion{
		CreatedAt:      time.Now(),
		ProductListing: pl,
	}
}

func (m *compareModel) buildAuditVersionProductWithSetting(unionSetting *storeProductListingSetting) models.Product {
	product := m.lastListing.Product
	if unionSetting.autoSyncProductDetailField(consts.ProductDetailFieldTitle) && m.isTitleModify() {
		product.Title = m.listing.Product.Title
	}
	if unionSetting.autoSyncProductDetailField(consts.ProductDetailFieldDescription) && m.isDescriptionModify() {
		product.Description = m.listing.Product.Description
	}
	if unionSetting.autoSyncProductDetailField(consts.ProductDetailFieldMedia) && m.isMediaModify() {
		product.Media = m.listing.Product.Media
	}
	if unionSetting.autoSyncVariant() && m.isVariantModify() {
		product.Variants = m.buildAuditVersionVariant()
		product.Options = m.listing.Product.Options
	}
	return product
}
