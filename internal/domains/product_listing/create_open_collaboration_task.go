package product_listing

import (
	"context"
	"encoding/json"
	"strconv"

	"github.com/go-playground/validator/v10"
	"go.uber.org/zap"

	"github.com/AfterShip/gopkg/log"

	"github.com/AfterShip/pltf-pd-product-listings/internal/consts"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
	"github.com/AfterShip/pltf-pd-product-listings/internal/utils/slicex"
)

type CreateOpenCollaborationTaskInput struct {
	OrganizationID              string              `json:"organization_id" validate:"required"`
	SalesChannel                models.SalesChannel `json:"sales_channel" validate:"required"`
	ProductListingIDs           []string            `json:"product_listing_ids" validate:"required"`
	CommissionRate              string              `json:"commission_rate" validate:"required"`
	RequireSellerApproveCreator bool                `json:"require_seller_approve_creator"`
}

type CreateOpenCollaborationTaskOutput struct {
	TotalCount     int `json:"total_count"`
	SucceededCount int `json:"succeeded_count"`
	FailedCount    int `json:"failed_count"`
}

type CreateOpenCollaborationTask struct {
	Logger    *log.Logger
	Validator *validator.Validate
}

func (t *CreateOpenCollaborationTask) validate(input *CreateOpenCollaborationTaskInput) error {
	if err := t.Validator.Struct(input); err != nil {
		return err
	}

	if len(input.ProductListingIDs) == 0 {
		return ErrNoProductListingID
	}

	commissionRate, err := strconv.ParseFloat(input.CommissionRate, 64)
	if err != nil {
		return ErrInvalidCommissionRate
	}

	if commissionRate < 1 || commissionRate > 80 {
		return ErrInvalidCommissionRate
	}

	return nil
}

func (t *CreateOpenCollaborationTask) BuildTaskArgs(ctx context.Context, input models.TaskInput) (models.TaskArgs, error) {
	args, ok := input.(*CreateOpenCollaborationTaskInput)
	if !ok {
		return models.TaskArgs{}, ErrUnprocessableEntity
	}

	if err := t.validate(args); err != nil {
		return models.TaskArgs{}, err
	}

	taskArgs := models.TaskArgs{
		GroupName:  consts.CreateOpenCollaboration,
		ResourceID: args.OrganizationID,
		StoreKey:   args.SalesChannel.StoreKey,
		Platform:   args.SalesChannel.Platform,
		Type:       consts.BatchTaskType,
	}

	inputs := make([]models.TaskInput, 0)
	splitIDs := slicex.SplitSlice(args.ProductListingIDs, 50)
	for i := range splitIDs {
		inputs = append(inputs, &CreateOpenCollaborationTaskInput{
			OrganizationID:              args.OrganizationID,
			SalesChannel:                args.SalesChannel,
			ProductListingIDs:           splitIDs[i],
			CommissionRate:              args.CommissionRate,
			RequireSellerApproveCreator: args.RequireSellerApproveCreator,
		})
	}
	taskArgs.Inputs = inputs

	// If there are multiple IDs, we need to use the organization ID as the concurrency key
	if len(args.ProductListingIDs) > 1 {
		taskArgs.ConcurrencyKey = args.OrganizationID
	}

	return taskArgs, nil
}

// nolint:dupl
func (t *CreateOpenCollaborationTask) ParseOutput(ctx context.Context, task *models.Task) models.TaskOutput {
	output := CreateOpenCollaborationTaskOutput{}
	for i := range task.ChildTasks {
		// get total count
		if task.ChildTasks[i].Inputs != "" {
			input := &CreateOpenCollaborationTaskInput{}
			if err := json.Unmarshal([]byte(task.ChildTasks[i].Inputs), input); err != nil {
				t.Logger.With(zap.String("Id", task.ChildTasks[i].ID)).WarnCtx(ctx, "Failed to parse edit attributes task input", zap.Error(err))
			}
			output.TotalCount += len(input.ProductListingIDs)
		}

		// get child task output
		if task.ChildTasks[i].Outputs.Data != "" {
			childTaskOutput := CreateOpenCollaborationTaskOutput{}
			if err := json.Unmarshal([]byte(task.ChildTasks[i].Outputs.Data), &childTaskOutput); err != nil {
				t.Logger.With(zap.String("Id", task.ChildTasks[i].ID)).WarnCtx(ctx, "Failed to parse edit attributes task output", zap.Error(err))
			}
			output.FailedCount += childTaskOutput.FailedCount
			output.SucceededCount += childTaskOutput.SucceededCount
		}
	}

	return output
}
