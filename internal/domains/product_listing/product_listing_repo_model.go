package product_listing

import (
	"time"

	"github.com/AfterShip/gopkg/facility/types"

	"github.com/AfterShip/pltf-pd-product-listings/internal/consts"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
)

const (
	tableProductListing          = "product_listings"
	tableFieldProductListingID   = "product_listing_id"
	productsCenterProductIDIndex = "product_listings_by_organization_id_a_products_center_product_id_a_sales_channel_platform_a_sales_channel_store_key_a_u"
	salesChannelProductIDIndex   = "product_listings_by_organization_id_a_sales_channel_platform_a_sales_channel_store_key_a_sales_channel_product_id_a_u"
)

type productListingDBModel struct {
	ProductListingID                 string                                   `spanner:"product_listing_id"`
	OrganizationID                   string                                   `spanner:"organization_id"`
	SalesChannelPlatform             string                                   `spanner:"sales_channel_platform"`
	SalesChannelStoreKey             string                                   `spanner:"sales_channel_store_key"`
	SalesChannelCountryRegion        string                                   `spanner:"sales_channel_country_region"`
	SalesChannelProductID            types.String                             `spanner:"sales_channel_product_id"`
	SalesChannelConnectorProductID   string                                   `spanner:"sales_channel_connector_product_id"`
	SalesChannelState                consts.SalesChannelProductState          `spanner:"sales_channel_state"`
	SalesChannelMetricsCreatedAt     time.Time                                `spanner:"sales_channel_metrics_created_at"`
	SalesChannelMetricsUpdatedAt     time.Time                                `spanner:"sales_channel_metrics_updated_at"`
	ProductsCenterProductID          string                                   `spanner:"products_center_product_id"`
	ProductsCenterConnectorProductID string                                   `spanner:"products_center_connector_product_id"`
	ProductsCenterState              consts.ProductsCenterProductPublishState `spanner:"products_center_state"`
	ProductsCenterSourceStoreKey     string                                   `spanner:"products_center_source_store_key"`
	ProductsCenterSourcePlatform     string                                   `spanner:"products_center_source_platform"`
	ProductsCenterSourceProductID    string                                   `spanner:"products_center_source_product_id"`
	State                            consts.ProductListingProductState        `spanner:"state"`
	LinkStatus                       consts.LinkStatus                        `spanner:"link_status"`
	SyncStatus                       consts.SyncStatus                        `spanner:"sync_status"`
	ReadyStatus                      consts.ReadyStatus                       `spanner:"ready_status"`
	ReadyFailedReasons               ReadyFailedReasons                       `spanner:"ready_failed_reasons"`
	ReadyLastFailedAt                time.Time                                `spanner:"ready_last_failed_at"`
	AuditState                       consts.AuditState                        `spanner:"audit_state"`
	AuditFailedReasons               AuditFailedReasons                       `spanner:"audit_failed_reasons"`
	AuditLastFailedAt                time.Time                                `spanner:"audit_last_failed_at"`
	PublishLastReferenceID           string                                   `spanner:"publish_last_reference_id"`
	PublishState                     consts.PublishState                      `spanner:"publish_state"`
	PublishErrorCode                 string                                   `spanner:"publish_error_code"`
	PublishErrorMsg                  string                                   `spanner:"publish_error_msg"`
	PublishLastFailedAt              time.Time                                `spanner:"publish_last_failed_at"`
	Product                          models.Product                           `spanner:"product"`
	Settings                         SyncSettings                             `spanner:"settings"`
	FeedCategoryTemplateID           types.String                             `spanner:"feed_category_template_id"`
	Version                          int64                                    `spanner:"version"`
	PendingDeletedAt                 types.Datetime                           `spanner:"pending_deleted_at"`
	DeletedAt                        types.Datetime                           `spanner:"deleted_at"`
	CreatedAt                        time.Time                                `spanner:"created_at"`
	UpdatedAt                        time.Time                                `spanner:"updated_at"`
}

func (model *productListingDBModel) SpannerTable() string {
	return tableProductListing
}

func (model *productListingDBModel) toProductListingSalesChannelProduct() SalesChannelProduct {
	return SalesChannelProduct{
		ID:                 model.SalesChannelProductID.String(),
		ConnectorProductID: model.SalesChannelConnectorProductID,
		State:              model.SalesChannelState,
		Metrics: SalesChannelProductMetrics{
			CreatedAt: model.SalesChannelMetricsCreatedAt,
			UpdatedAt: model.SalesChannelMetricsUpdatedAt,
		},
	}
}

func (model *productListingDBModel) toProductListingProductsCenterProduct() ProductsCenterProduct {
	return ProductsCenterProduct{
		ID:                 model.ProductsCenterProductID,
		ConnectorProductID: model.ProductsCenterConnectorProductID,
		PublishState:       model.ProductsCenterState,
		Source: ProductsCenterProductSource{
			StoreKey: model.ProductsCenterSourceStoreKey,
			Platform: model.ProductsCenterSourcePlatform,
			ID:       model.ProductsCenterSourceProductID,
		},
	}
}

func (model *productListingDBModel) toProductListingPublish() Publish {
	return Publish{
		LastReferenceID: model.PublishLastReferenceID,
		State:           model.PublishState,
		Error: PublishError{
			Code: model.PublishErrorCode,
			Msg:  model.PublishErrorMsg,
		},
		LastFailedAt: model.PublishLastFailedAt,
	}
}

func (model *productListingDBModel) toProductListingAudit() Audit {
	return Audit{
		State:         model.AuditState,
		LastFailedAt:  model.AuditLastFailedAt,
		FailedReasons: model.AuditFailedReasons,
	}
}

func (model *productListingDBModel) toProductListingReady() Ready {
	return Ready{
		Status:        model.ReadyStatus,
		LastFailedAt:  model.ReadyLastFailedAt,
		FailedReasons: model.ReadyFailedReasons,
	}
}
