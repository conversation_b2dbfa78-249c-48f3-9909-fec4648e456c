package product_listing

import (
	"fmt"
	"strings"

	errors_sdk "github.com/AfterShip/connectors-errors-sdk-go"
	"github.com/AfterShip/gopkg/facility/set"
	"github.com/AfterShip/pltf-pd-product-listings/internal/consts"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/category"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"

	"github.com/pkg/errors"
	"github.com/samber/lo"
)

const (
	readyCheckProductOption      = "product_option"
	readyCheckProductOptionValue = "product_option_value"
	readyCheckVariantOption      = "variant_option"
)

type sheinReadyCheckModel struct {
	product    *models.Product
	rules      *category.RulesOutput
	attributes *category.AttributesOutput
}

type sheinReadyCheck interface {
	toFailReason() *ReadyFailedReason
}

func newSheinReadyCheckModel(product *models.Product, rules *category.RulesOutput, attributes *category.AttributesOutput) *sheinReadyCheckModel {
	return &sheinReadyCheckModel{
		product:    product,
		rules:      rules,
		attributes: attributes,
	}
}

// nolint:gocyclo
func (model *sheinReadyCheckModel) Check() []sheinReadyCheck {
	result := make([]sheinReadyCheck, 0)

	result = append(result, model.titleCheck()...)
	result = append(result, model.descriptionCheck()...)
	result = append(result, model.productOptionsCheck()...)

	if len(model.product.Categories) == 0 {
		result = append(result, model.categoryCheck()...)
	} else {
		// brand
		result = append(result, model.brandCheck()...)
		// size chart check
		result = append(result, model.sizeChartCheck()...)
		// attributes
		result = append(result, model.attributesCheck()...)
	}

	if len(model.product.Variants) == 0 {
		result = append(result, &variantIsEmpty{})
	} else {
		result = append(result, model.packageCheck()...)
		result = append(result, model.variantPriceCheck()...)
	}

	return result
}

func (model *sheinReadyCheckModel) titleCheck() []sheinReadyCheck {
	result := make([]sheinReadyCheck, 0)
	if model.product.Title == "" {
		result = append(result, &titleIsEmpty{})
	}

	return result
}

func (model *sheinReadyCheckModel) descriptionCheck() []sheinReadyCheck {
	result := make([]sheinReadyCheck, 0)
	if model.product.Description == "" {
		result = append(result, &descriptionIsEmpty{})
	}

	return result
}

func (model *sheinReadyCheckModel) categoryCheck() []sheinReadyCheck {
	result := make([]sheinReadyCheck, 0)
	if len(model.product.Categories) == 0 {
		result = append(result, &categoryIsEmpty{})
	}

	return result
}

func (model *sheinReadyCheckModel) brandCheck() []sheinReadyCheck {
	if model.rules == nil || model.rules.ExternalCategoryID == "" {
		return nil
	}

	if model.rules.Rule.Brand == nil || !model.rules.Rule.Brand.IsRequired {
		return nil
	}

	if model.product.Brand.SalesChannelID != "" {
		return nil
	}

	result := make([]sheinReadyCheck, 0)
	result = append(result, &brandIsEmpty{})
	return result
}

func (model *sheinReadyCheckModel) sizeChartCheck() []sheinReadyCheck {
	if model.rules == nil || model.rules.ExternalCategoryID == "" {
		return nil
	}

	if model.rules.Rule.SizeChart == nil || !model.rules.Rule.SizeChart.IsRequired {
		return nil
	}

	if len(model.product.SizeChart.Attributes) == 0 {
		return []sheinReadyCheck{&sizeChartIsEmpty{}}
	}

	result := make([]sheinReadyCheck, 0)
	for _, att := range model.product.SizeChart.Attributes {
		if att.SalesChannelID == "" || // 横轴表头
			att.RelateSalesChannelID == "" || // 纵轴：销售属性 name
			att.RelateSalesChannelValueID == "" { // 纵轴：销售属性 value
			result = append(result, &sizeChartIsEmpty{})
			break
		}
	}

	return result
}

func (model *sheinReadyCheckModel) attributesCheck() []sheinReadyCheck {
	if model.attributes == nil {
		return nil
	}
	if model.attributes.ExternalCategoryID == "" {
		return nil
	}

	result := make([]sheinReadyCheck, 0)

	// 收集已经填写的 attributes
	productAttributes := model.product.Attributes
	productAttributesMap := make(map[string][]models.SalesChannelResource)
	for _, attr := range productAttributes {
		if attr == nil || attr.SalesChannelID == "" {
			continue
		}
		productAttributesMap[attr.SalesChannelID] = attr.Values
	}

	// 收集全部必填的 attributes
	requiredAttributes := model.collectRequiredProductAttributes()

	// 检查已经必填的 attributes
	for _, requiredAttribute := range requiredAttributes {
		if values, ok := productAttributesMap[requiredAttribute.ID]; !ok || len(values) == 0 {
			result = append(result, &attributesIsEmpty{
				Name: requiredAttribute.Name,
			})
		}
	}

	return result
}

func (model *sheinReadyCheckModel) collectRequiredProductAttributes() []category.Attribute {

	if model.product == nil || model.attributes == nil {
		return nil
	}

	result := make([]category.Attribute, 0)
	for _, attribute := range model.attributes.Attributes {
		if attribute.Type != consts.AttributeTypeProductProperty {
			continue
		}

		// IsRequired 字段判断
		if attribute.IsRequired {
			result = append(result, attribute)
		}
	}

	return result
}

func (model *sheinReadyCheckModel) packageCheck() []sheinReadyCheck {
	if len(model.product.Variants) == 0 {
		return nil
	}

	variant := model.product.Variants[0]
	result := make([]sheinReadyCheck, 0)
	if !variant.HeightFilled() {
		result = append(result, &packageDimensionsHeightIsEmpty{})
	}
	if !variant.WidthFilled() {
		result = append(result, &packageDimensionsWidthIsEmpty{})
	}
	if !variant.LengthFilled() {
		result = append(result, &packageDimensionsLengthIsEmpty{})
	}
	if !variant.WeightFilled() {
		result = append(result, &packageWeightIsEmpty{})
	}

	return result
}

func (model *sheinReadyCheckModel) variantPriceCheck() []sheinReadyCheck {
	result := make([]sheinReadyCheck, 0)
	for i := range model.product.Variants {
		if model.product.Variants[i].Price.Amount == "0" || model.product.Variants[i].Price.Amount == "" {
			result = append(result, &variantPriceInvalid{position: i})
		}
	}

	return result
}

func (model *sheinReadyCheckModel) productOptionsCheck() []sheinReadyCheck {
	if len(model.product.Options) == 0 {
		return []sheinReadyCheck{
			&sheinProductOptionIsEmpty{},
		}
	}

	mainProductOption, found := model.product.GetMainOption()
	if !found {
		return []sheinReadyCheck{
			&sheinProductOptionIsEmpty{},
		}
	}

	result := make([]sheinReadyCheck, 0)

	// option list
	optionList := model.getSalesAttributes()
	mainOptionList := lo.Filter(optionList, func(attr category.Attribute, _ int) bool {
		return attr.Type == consts.AttributeTypeSalesPropertyMainOption
	})
	requiredOptionList := lo.Filter(optionList, func(attr category.Attribute, _ int) bool {
		return attr.IsRequired
	})

	if result = model.checkProductOptionID(); len(result) > 0 {
		return result
	}
	if result = model.checkProductOptionBelongToCurrentCategory(optionList); len(result) > 0 {
		return result
	}

	// check main option
	result = append(result, model.checkProductMainOption(mainProductOption, mainOptionList)...)
	// check require option
	result = append(result, model.checkProductRequiredOption(requiredOptionList)...)

	return result
}

func (model *sheinReadyCheckModel) getSalesAttributes() []category.Attribute {
	if model.attributes == nil {
		return []category.Attribute{}
	}
	return lo.Filter(model.attributes.Attributes, func(attr category.Attribute, _ int) bool {
		return attr.Type == consts.AttributeTypeSalesProperty || attr.Type == consts.AttributeTypeSalesPropertyMainOption
	})
}

// checkProductRequiredOption
// 检查 shein 必填的 option 是否填了
func (model *sheinReadyCheckModel) checkProductRequiredOption(requiredOptionList []category.Attribute) []sheinReadyCheck {
	result := make([]sheinReadyCheck, 0)

	if len(requiredOptionList) == 0 {
		return result
	}

	requiredOptionIdSet := set.NewStringSet(lo.Map(requiredOptionList, func(attr category.Attribute, _ int) string {
		return attr.ID
	})...)
	optionIDSet := set.NewStringSet(lo.Map(model.product.Options, func(o *models.ProductOption, _ int) string {
		return o.SalesChannelOptionID
	})...)

	diffSet := requiredOptionIdSet.Diff(optionIDSet)
	diffList := diffSet.ToList()
	if len(diffList) > 0 {
		result = append(result, &sheinMissingRequiredOption{
			OptionNameList: lo.Map(
				lo.Filter(requiredOptionList, func(attr category.Attribute, _ int) bool { return diffSet.Contains(attr.ID) }),
				func(attr category.Attribute, _ int) string { return attr.Name },
			),
		})
	}

	return result
}

// checkProductMainOption
// 1.main option 只能从 shein 可选的 main option list 中选
// 2.main option 图片类型和数量必须符合要求
func (model *sheinReadyCheckModel) checkProductMainOption(mainProductOption *models.ProductOption, mainOptionList []category.Attribute) []sheinReadyCheck {
	result := make([]sheinReadyCheck, 0)

	if len(mainOptionList) > 0 {
		mainOptionIdSet := set.NewStringSet(lo.Map(mainOptionList, func(attr category.Attribute, _ int) string {
			return attr.ID
		})...)
		if _, found := lo.Find(model.product.Options, func(o *models.ProductOption) bool {
			return mainOptionIdSet.Contains(o.SalesChannelOptionID)
		}); !found {
			result = append(result, &sheinMissingMainOption{
				OptionNameList: lo.Map(mainOptionList, func(attr category.Attribute, _ int) string {
					return attr.Name
				}),
			})
		}
	}

	// check images
	for _, valueDetail := range mainProductOption.ValueDetails {
		if mediaCheckResult := checkProductOptionMedia(*mainProductOption, valueDetail); len(mediaCheckResult) > 0 {
			result = append(result, mediaCheckResult...)
		}
	}
	return result
}

// checkProductOptionID
// 检查是否 mapping 了对应的 shein option
func (model *sheinReadyCheckModel) checkProductOptionID() []sheinReadyCheck {
	result := make([]sheinReadyCheck, 0)
	for _, option := range model.product.Options {
		if option.SalesChannelOptionID == "" {
			result = append(result, &sheinProductOptionIsInvalid{OptionName: option.Name})
			continue
		}
		if len(option.ValueDetails) == 0 {
			result = append(result,
				&sheinProductOptionIsInvalid{
					Suggestion: fmt.Sprintf(`Select option values for "%s"`, option.Name),
				},
			)
			continue
		}

		for _, valueDetail := range option.ValueDetails {
			if valueDetail.SalesChannelValueID == "" {
				result = append(result, &sheinProductOptionValueIsInvalid{OptionValue: valueDetail.Value})
				continue
			}
		}
	}
	return result
}

// checkProductOptionBelongToCurrentCategory
// 检查 option 的值是否属于当前分类
func (model *sheinReadyCheckModel) checkProductOptionBelongToCurrentCategory(optionList []category.Attribute) []sheinReadyCheck {
	result := make([]sheinReadyCheck, 0)

	if len(optionList) == 0 {
		return result
	}

	// 当前分类的 option 值
	// key: option id; value: option value id set
	optionMap := lo.SliceToMap(optionList, func(att category.Attribute) (string, *set.StringSet) {
		attValueIDs := lo.Map(att.Values, func(attVal category.AttributeValue, _ int) string { return attVal.ID })
		attValueIDSet := set.NewStringSet(attValueIDs...)
		return att.ID, attValueIDSet
	})

	for _, option := range model.product.Options {
		optionValueIDSet, ok := optionMap[option.SalesChannelOptionID]
		if !ok {
			result = append(result, &sheinProductOptionIsInvalid{
				Suggestion: fmt.Sprintf("Option %s is not found under current category, please manually select one", option.Name)})
			continue
		}

		for _, valueDetail := range option.ValueDetails {
			if !optionValueIDSet.Contains(valueDetail.SalesChannelValueID) {
				result = append(result, &sheinProductOptionValueIsInvalid{
					Suggestion: fmt.Sprintf("Option value %s is not found under current category, please manually select one", valueDetail.Value)})
				continue
			}
		}
	}

	return result
}

func checkProductOptionMedia(option models.ProductOption, optionValueDetail models.ProductOptionValueDetail) []sheinReadyCheck {
	imgMap := make(map[string][]models.ProductMedia)
	for _, img := range optionValueDetail.Media {
		if _, ok := imgMap[img.ExternalImageType]; !ok {
			imgMap[img.ExternalImageType] = make([]models.ProductMedia, 0)
		}
		imgMap[img.ExternalImageType] = append(imgMap[img.ExternalImageType], img)
	}

	optionValue := optionValueDetail.Value
	result := make([]sheinReadyCheck, 0)

	// 主图必传且最多一张
	if mainImgs, ok := imgMap[consts.ExternalImageTypeMain]; !ok {
		result = append(result, &sheinProductOptionValueMissRequiredImgType{OptionValue: optionValue, ImageType: consts.ExternalImageTypeMain})
	} else {
		if len(mainImgs) > 1 {
			result = append(result, &sheinProductOptionValueImgCountOverLimit{OptionValue: optionValue, ImageType: consts.ExternalImageTypeMain})
		}
	}
	// 细节图至少一张，最多10张
	if detailImgs, ok := imgMap[consts.ExternalImageTypeDetail]; !ok {
		result = append(result, &sheinProductOptionValueMissRequiredImgType{OptionValue: optionValue, ImageType: consts.ExternalImageTypeDetail})
	} else {
		if len(detailImgs) > 10 {
			result = append(result, &sheinProductOptionValueImgCountOverLimit{OptionValue: optionValue, ImageType: consts.ExternalImageTypeDetail})
		}
	}
	// 方形图必传且最多一张
	// 多 skc 必传，单 skc 非必传
	if len(option.ValueDetails) > 1 {
		if squareImgs, ok := imgMap[consts.ExternalImageTypeSquare]; !ok {
			result = append(result, &sheinProductOptionValueMissRequiredImgType{OptionValue: optionValue, ImageType: consts.ExternalImageTypeSquare})
		} else {
			if len(squareImgs) > 1 {
				result = append(result, &sheinProductOptionValueImgCountOverLimit{OptionValue: optionValue, ImageType: consts.ExternalImageTypeSquare})
			}
		}
	}
	// 细节图至少一张
	if pieceImgs, ok := imgMap[consts.ExternalImageTypePiece]; !ok {
		result = append(result, &sheinProductOptionValueMissRequiredImgType{OptionValue: optionValue, ImageType: consts.ExternalImageTypePiece})
	} else {
		if len(pieceImgs) > 1 {
			result = append(result, &sheinProductOptionValueImgCountOverLimit{OptionValue: optionValue, ImageType: consts.ExternalImageTypePiece})
		}
	}

	return result
}

func (model *sheinReadyCheckModel) variantOptionCheck() []sheinReadyCheck {
	result := make([]sheinReadyCheck, 0)
	for i, variant := range model.product.Variants {
		if len(variant.Options) == 0 {
			result = append(result, &sheinVariantOptionIsEmpty{Position: i})
		}

		for j, option := range variant.Options {
			if option.SalesChannelOptionID == "" || option.SalesChannelValueID == "" {
				result = append(result, &sheinVariantOptionIsInvalid{PositionX: i, PositionY: j})
			}
		}
	}

	return result
}

type sheinProductOptionIsEmpty struct {
}

func (p *sheinProductOptionIsEmpty) toFailReason() *ReadyFailedReason {
	return &ReadyFailedReason{
		Position:    readyCheckProductOption,
		ErrorCodes:  []string{errors_sdk.ProductListingUnreadySheinProductOptionIsEmpty_6064120031.Code().String()},
		Reasons:     []string{errors_sdk.ProductListingUnreadySheinProductOptionIsEmpty_6064120031.Error()},
		Suggestions: []string{errors_sdk.ProductListingUnreadySheinProductOptionIsEmpty_6064120031.Error()},
	}
}

type sheinProductOptionIsInvalid struct {
	OptionName string
	Suggestion string
}

func (p *sheinProductOptionIsInvalid) toFailReason() *ReadyFailedReason {
	msg := errors_sdk.ProductListingUnreadySheinProductOptionIsInvalid_6064120030.Error()
	if p.Suggestion != "" {
		msg = p.Suggestion
	} else if p.OptionName != "" {
		msg = fmt.Sprintf(`No matching SHEIN option found for "%s", please manually select one`, p.OptionName)
	}
	return &ReadyFailedReason{
		Position:    fmt.Sprint(readyCheckProductOption, ": ", p.OptionName),
		ErrorCodes:  []string{errors_sdk.ProductListingUnreadySheinProductOptionIsInvalid_6064120030.Code().String()},
		Reasons:     []string{errors_sdk.ProductListingUnreadySheinProductOptionIsInvalid_6064120030.Error()},
		Suggestions: []string{msg},
	}
}

type sheinProductOptionValueIsInvalid struct {
	OptionValue string
	Suggestion  string
}

func (p *sheinProductOptionValueIsInvalid) toFailReason() *ReadyFailedReason {
	msg := errors_sdk.ProductListingUnreadySheinProductOptionIsInvalid_6064120030.Error()
	if p.Suggestion != "" {
		msg = p.Suggestion
	} else if p.OptionValue != "" {
		msg = fmt.Sprintf(`Option value "%s" is invalid, select another option value`, p.OptionValue)
	}
	return &ReadyFailedReason{
		Position:    fmt.Sprint(readyCheckProductOptionValue, ": ", p.OptionValue),
		ErrorCodes:  []string{errors_sdk.ProductListingUnreadySheinProductOptionValueIsInvalid_6064120029.Code().String()},
		Reasons:     []string{errors.Wrap(errors_sdk.ProductListingUnreadySheinProductOptionValueIsInvalid_6064120029, p.OptionValue).Error()},
		Suggestions: []string{msg},
	}
}

type sheinMissingRequiredOption struct {
	OptionNameList []string
}

func (p *sheinMissingRequiredOption) toFailReason() *ReadyFailedReason {
	return &ReadyFailedReason{
		Position:    fmt.Sprint(readyCheckProductOption),
		ErrorCodes:  []string{errors_sdk.ProductListingSheinUnreadyMissingRequiredOption_6064120036.Code().String()},
		Reasons:     []string{errors_sdk.ProductListingSheinUnreadyMissingRequiredOption_6064120036.Error()},
		Suggestions: []string{fmt.Sprintf(`Please fill in the following required options: %s`, strings.Join(p.OptionNameList, ", "))},
	}
}

type sheinMissingMainOption struct {
	OptionNameList []string
}

func (p *sheinMissingMainOption) toFailReason() *ReadyFailedReason {
	return &ReadyFailedReason{
		Position:    fmt.Sprint(readyCheckProductOption),
		ErrorCodes:  []string{errors_sdk.ProductListingSheinUnreadyMissingMainOption_6064120035.Code().String()},
		Reasons:     []string{errors_sdk.ProductListingSheinUnreadyMissingMainOption_6064120035.Error()},
		Suggestions: []string{fmt.Sprintf(`Please select one main option from the list below: %s`, strings.Join(p.OptionNameList, ", "))},
	}
}

type sheinProductOptionValueMissRequiredImgType struct {
	OptionValue string
	ImageType   string
}

func (p *sheinProductOptionValueMissRequiredImgType) toFailReason() *ReadyFailedReason {
	return &ReadyFailedReason{
		Position:   fmt.Sprint(readyCheckProductOptionValue, ": ", p.OptionValue),
		ErrorCodes: []string{errors_sdk.ProductListingUnreadySheinProductOptionValueMissRequiredImgType_6064120028.Code().String()},
		Reasons:    []string{errors_sdk.ProductListingUnreadySheinProductOptionValueMissRequiredImgType_6064120028.Error()},
		Suggestions: []string{
			fmt.Sprintf(`Option value "%s" is missing "%s" image(s)`, p.OptionValue, p.ImageType),
		},
	}
}

type sheinProductOptionValueImgCountOverLimit struct {
	OptionValue string
	ImageType   string
}

func (p *sheinProductOptionValueImgCountOverLimit) toFailReason() *ReadyFailedReason {
	return &ReadyFailedReason{
		Position:   fmt.Sprint(readyCheckProductOptionValue, ": ", p.OptionValue),
		ErrorCodes: []string{errors_sdk.ProductListingUnreadySheinProductOptionValueImgCountOverLimit_6064120027.Code().String()},
		Reasons:    []string{errors_sdk.ProductListingUnreadySheinProductOptionValueImgCountOverLimit_6064120027.Error()},
		Suggestions: []string{
			fmt.Sprintf(`Option value "%s" has exceeded the image limit for "%s" images`, p.OptionValue, p.ImageType),
		},
	}
}

type sheinVariantOptionIsEmpty struct {
	Position int
}

func (p *sheinVariantOptionIsEmpty) toFailReason() *ReadyFailedReason {
	return &ReadyFailedReason{
		Position:    readyCheckVariantOption + fmt.Sprintf("[%d]", p.Position),
		ErrorCodes:  []string{errors_sdk.ProductListingUnreadySheinVariantOptionIsEmpty_6064120026.Code().String()},
		Reasons:     []string{errors_sdk.ProductListingUnreadySheinVariantOptionIsEmpty_6064120026.Error()},
		Suggestions: []string{errors_sdk.ProductListingUnreadySheinVariantOptionIsEmpty_6064120026.Error()},
	}
}

type sheinVariantOptionIsInvalid struct {
	PositionX int
	PositionY int
}

func (p *sheinVariantOptionIsInvalid) toFailReason() *ReadyFailedReason {
	return &ReadyFailedReason{
		Position:    readyCheckVariantOption + fmt.Sprintf("[%d][%d]", p.PositionX, p.PositionY),
		ErrorCodes:  []string{errors_sdk.ProductListingUnreadySheinVariantOptionIsInvalid_6064120025.Code().String()},
		Reasons:     []string{errors_sdk.ProductListingUnreadySheinVariantOptionIsInvalid_6064120025.Error()},
		Suggestions: []string{errors_sdk.ProductListingUnreadySheinVariantOptionIsInvalid_6064120025.Error()},
	}
}
