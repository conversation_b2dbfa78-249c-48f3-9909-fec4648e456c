package product_listing

import (
	"context"
	"encoding/json"

	"github.com/go-playground/validator/v10"
	"go.uber.org/zap"

	"github.com/AfterShip/gopkg/log"

	"github.com/AfterShip/pltf-pd-product-listings/internal/consts"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
)

type BatchInvokeProductListingTaskInput struct {
	Organization      models.Organization `json:"organization" validate:"required"`
	SalesChannel      models.SalesChannel `json:"sales_channel" validate:"required"`
	Source            models.Source       `json:"source" validate:"required"`
	TaskType          string              `json:"task_type" validate:"required,oneof=batch_publish_prices batch_publish_inventories batch_auto_link"`
	Concurrency       string              `json:"concurrency" validate:"required,oneof=multiple single"`
	QueryDataScope    string              `json:"query_data_scope" validate:"required,oneof=all specified"`
	FromEvent         string              `json:"from_event" validate:"required"` // 事件来源，日志记录用
	ProductListingIDs []string            `json:"product_listing_ids" validate:"omitempty"`
}

type BatchInvokeProductListingTaskOutput struct {
	Organization      models.Organization `json:"organization" validate:"required"`
	SalesChannel      models.SalesChannel `json:"sales_channel" validate:"required"`
	Source            models.Source       `json:"source" validate:"omitempty"`
	TaskType          string              `json:"task_type" validate:"required,oneof=batch_publish_prices batch_publish_inventories batch_auto_link"`
	Concurrency       string              `json:"concurrency" validate:"required,oneof=multiple single"`
	QueryDataScope    string              `json:"query_data_scope" validate:"required,oneof=all specified"`
	FromEvent         string              `json:"from_event" validate:"required"` // 事件来源，日志记录用
	ProductListingIDs []string            `json:"product_listing_ids" validate:"omitempty"`
	SuccessCount      int64               `json:"success_count"`
	FailedCount       int64               `json:"failed_count"`
}

type BatchInvokeProductListingTask struct {
	Logger    *log.Logger
	Validator *validator.Validate
}

func (t *BatchInvokeProductListingTask) validate(input *BatchInvokeProductListingTaskInput) error {
	if err := t.Validator.Struct(input); err != nil {
		return err
	}
	if input.Organization.ID == "" {
		return ErrUnprocessableEntity
	}
	if input.SalesChannel.Platform == "" {
		return ErrUnprocessableEntity
	}
	if input.SalesChannel.StoreKey == "" {
		return ErrUnprocessableEntity
	}
	if input.Source.App.Platform == "" {
		return ErrUnprocessableEntity
	}
	if input.Source.App.Key == "" {
		return ErrUnprocessableEntity
	}
	if input.QueryDataScope == "specified" && len(input.ProductListingIDs) == 0 {
		return ErrNoProductListingID
	}

	return nil
}

// nolint:dupl
func (t *BatchInvokeProductListingTask) BuildTaskArgs(ctx context.Context, input models.TaskInput) (models.TaskArgs, error) {
	args, ok := input.(*BatchInvokeProductListingTaskInput)
	if !ok {
		return models.TaskArgs{}, ErrUnprocessableEntity
	}

	if err := t.validate(args); err != nil {
		return models.TaskArgs{}, err
	}
	taskArgs := models.TaskArgs{
		GroupName:      consts.BatchInvokeProductListingJobs,
		Type:           consts.SingleTaskType,
		OrganizationID: args.Organization.ID,
		StoreKey:       args.SalesChannel.StoreKey,
		Platform:       args.SalesChannel.Platform,
		Inputs:         args,
	}
	return taskArgs, nil
}

func (t *BatchInvokeProductListingTask) ParseOutput(ctx context.Context, task *models.Task) models.TaskOutput {
	outputs := BatchInvokeProductListingTaskOutput{}
	if err := json.Unmarshal([]byte(task.Inputs), &outputs); err != nil {
		t.Logger.With(zap.String("Id", task.ID)).WarnCtx(ctx, "Failed to parse batch invoke product listing task output", zap.Error(err))
	}
	return outputs
}
