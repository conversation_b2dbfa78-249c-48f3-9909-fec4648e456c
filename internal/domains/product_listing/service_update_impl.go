package product_listing

import (
	"context"
	"regexp"
	"sort"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	connector_lib_utils "github.com/AfterShip/connectors-library/utils"
	"github.com/AfterShip/connectors-library/utils/sets"
	"github.com/AfterShip/feed-sdk-go/events"
	"github.com/AfterShip/gopkg/log"
	"github.com/AfterShip/gopkg/uuid"
	"github.com/AfterShip/pltf-pd-product-listings/internal/config"
	"github.com/AfterShip/pltf-pd-product-listings/internal/consts"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/category"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
)

//nolint:gocyclo
func (s *serviceImpl) Update(ctx context.Context, id string, args *ProductListingArgs) (ProductListing, error) {
	ctx = log.AppendFieldsToContext(ctx, zap.String("product_listing_id", id))
	var (
		err         error
		lastListing ProductListing
		newListing  ProductListing
	)

	mutex, err := s.acquireListingLock(ctx, id)
	if err != nil {
		return ProductListing{}, errors.WithStack(err)
	}
	defer func() {
		s.releaseListingLock(ctx, mutex)

		// activity log
		s.saveModifyActivityLog(ctx, &lastListing, &newListing)
		s.saveVariantActivityLog(ctx, &lastListing, &newListing)
		s.saveEventActivityLog(ctx, &newListing, events.TypeListingUpdateEvent, err)

		logZaps := make([]zap.Field, 0)
		if err != nil || lastListing.SalesChannel.Platform == consts.Shein {
			logZaps = append(logZaps, zap.String("arg", connector_lib_utils.GetJsonIndent(args)))
			if err != nil {
				logZaps = append(logZaps, zap.String("error", err.Error()))
			}
		}
		s.logger.InfoCtx(ctx, "handler listing update", logZaps...)
	}()

	// 验证参数
	if err := s.validate.Struct(args); err != nil {
		return ProductListing{}, err
	}

	listing, err := s.GetByID(ctx, id)
	if err != nil {
		return ProductListing{}, err
	}

	// 记录日志使用
	lastListing = *listing.DeepCopy()

	// 验证状态是否可以更新
	if !listing.canUpdate() ||
		listing.State == consts.ProductListingProductStateReviewing {
		return ProductListing{}, ErrPublishStateInUpdate
	}

	args.overWriteRelationByListing(&listing)

	previewListing, err := s.GeneratePreview(ctx, &ProductListing{
		ID:                    listing.ID,
		Organization:          listing.Organization,
		SalesChannel:          listing.SalesChannel,
		Product:               args.Product,
		Relations:             args.Relations,
		Settings:              args.Settings,
		ProductsCenterProduct: args.ProductsCenterProduct,
	})
	if err != nil {
		return ProductListing{}, err
	}

	// 生成更新 model
	model, err := s.generateUpdateModel(ctx, &listing, previewListing)
	if err != nil {
		return ProductListing{}, err
	}

	// 调用 repo 更新
	if err = s.repo.update(ctx, model.buildUpdateArgs()); err != nil {
		return ProductListing{}, err
	}

	// 获取更新后的 listing
	newListing, err = s.GetByID(ctx, listing.ID)
	if err != nil {
		return ProductListing{}, err
	}

	// 更新 ES
	if err = s.esRepo.BatchUpsertProductListings(ctx, []*ProductListing{&newListing}); err != nil {
		s.logger.With(zap.String("Id", newListing.ID)).WarnCtx(ctx, "Failed to upsert listing data to ES", zap.Error(err))
	}

	// after update 刊登/价格/库存同步
	copyNewListing := newListing.DeepCopy()
	if err = s.afterUpdate(ctx, copyNewListing, model, args.NeedPublish); err != nil {
		return ProductListing{}, err
	}

	return newListing, nil
}

func (s *serviceImpl) generateUpdateModel(ctx context.Context, listing, previewListing *ProductListing) (*updateModel, error) {
	unionSetting, err := s.getUnionSettingByListing(ctx, previewListing)
	if err != nil {
		return nil, err
	}

	categoryRules := &category.RulesOutput{}
	categoryAttributes := &category.AttributesOutput{}
	// 获取 category 的 rules 和 attributes
	if len(previewListing.Product.Categories) > 0 && previewListing.Product.Categories[0].SalesChannelID != "" {
		categoryRules, err = s.getCategoryRules(ctx, previewListing.Organization, previewListing.SalesChannel, previewListing.Product.Categories)
		if err != nil {
			return nil, err
		}
		categoryAttributes, err = s.getCategoryAttributes(ctx, previewListing.Organization, previewListing.SalesChannel, previewListing.Product.Categories)
		if err != nil {
			return nil, err
		}
	}

	salesChannelRegionConfig, err := s.conf.LoadChannelRegionConfig(listing.SalesChannel.Platform, listing.SalesChannel.CountryRegion)
	if err != nil {
		return nil, err
	}

	return newUpdateModel(previewListing, listing,
		unionSetting, categoryRules, categoryAttributes, salesChannelRegionConfig, s.getTiktokCDNDomainRegexps()), nil
}

type updateModel struct {
	newListing               *ProductListing
	lastListing              *ProductListing // 不可参与更新
	unionSetting             *storeProductListingSetting
	categoryRules            *category.RulesOutput
	categoryAttributes       *category.AttributesOutput
	salesChannelRegionConfig *config.ChannelRegionConfig
	ttsCDNDomainRegExps      []*regexp.Regexp
}

func newUpdateModel(newListing, lastListing *ProductListing,
	unionSetting *storeProductListingSetting,
	categoryRules *category.RulesOutput,
	categoryAttributes *category.AttributesOutput,
	salesChannelRegionConfig *config.ChannelRegionConfig,
	ttsCDNDomainRegExps []*regexp.Regexp,
) *updateModel {
	return &updateModel{
		newListing:               newListing,
		lastListing:              lastListing,
		unionSetting:             unionSetting,
		categoryRules:            categoryRules,
		categoryAttributes:       categoryAttributes,
		salesChannelRegionConfig: salesChannelRegionConfig,
		ttsCDNDomainRegExps:      ttsCDNDomainRegExps,
	}
}

// nolint:dupl
func (m *updateModel) getCreateAndDeleteVariantIDSet() (*sets.StringSet, *sets.StringSet) {
	oldVariantIDs := sets.NewStringSet()
	newVariantIDs := sets.NewStringSet()
	for i := range m.newListing.Product.Variants {
		newVariantIDs.Add(m.newListing.Product.Variants[i].ID)
	}
	for i := range m.lastListing.Product.Variants {
		oldVariantIDs.Add(m.lastListing.Product.Variants[i].ID)
	}
	createVariantIDs := newVariantIDs.Diff(oldVariantIDs)
	deleteVariantIDs := oldVariantIDs.Diff(newVariantIDs)
	return createVariantIDs, deleteVariantIDs
}

func (m *updateModel) buildUpdateArgs() *conductorUpdateArgs {
	listing := m.lastListing.DeepCopy()
	listing.ProductsCenterProduct = m.newListing.ProductsCenterProduct
	// 更新 Settings
	listing.Settings = m.newListing.Settings

	if m.lastListing.State == consts.ProductListingProductStatePending &&
		m.lastListing.Audit.State == "" {
		// pending 状态
		listing.Product = m.newListing.Product
		listing.Relations = m.newListing.Relations
	} else {
		m.generateNewVariantID()
		// 非 pending 状态
		createVariantIDs, deleteVariantIDs := m.getCreateAndDeleteVariantIDSet()
		listing.Product.Variants = m.buildVariants(createVariantIDs)
		listing.Relations = m.buildRelations(createVariantIDs, deleteVariantIDs)

		// 更新 Product options
		listing.Product.Options = m.buildOptions()
	}

	// 更新状态
	listing.ModifyStateAndStatus()
	listing.ClearReadyStatus()
	if listing.State == consts.ProductListingProductStatePending {
		listing.SetReadyStatus(m.categoryRules, m.categoryAttributes, m.salesChannelRegionConfig, m.ttsCDNDomainRegExps)
	}

	result := convertToConductorUpdateArgs(listing, m.lastListing)

	return &result
}

func (m *updateModel) generateNewVariantID() {
	// 补充新增的 variant ID
	for i := range m.newListing.Product.Variants {
		if m.newListing.Product.Variants[i].ID != "" {
			continue
		}
		// add variant id
		variantID := uuid.GenerateUUIDV4()
		m.newListing.Product.Variants[i].ID = variantID

		// add relation id
		for j := range m.newListing.Relations {
			if m.newListing.Relations[j].VariantPosition == m.newListing.Product.Variants[i].Position &&
				m.newListing.Relations[j].ProductListingVariantID == "" {
				m.newListing.Relations[j].ProductListingVariantID = variantID
				break
			}
		}
	}
}

// nolint:dupl
func (m *updateModel) buildVariants(createVariantIDs *sets.StringSet) []*models.ProductVariant {
	variants := m.lastListing.Product.Variants
	if len(createVariantIDs.ToList()) == 0 {
		return variants
	}

	for i := range m.newListing.Product.Variants {
		if createVariantIDs.Contains(m.newListing.Product.Variants[i].ID) {
			variants = append(variants, m.newListing.Product.Variants[i])
		}
	}
	sort.Slice(variants, func(i, j int) bool {
		return variants[i].Position < variants[j].Position
	})

	return variants
}

func (m *updateModel) buildOptions() []*models.ProductOption {
	options := make([]*models.ProductOption, 0)
	options = append(options, m.lastListing.Product.Options...)
	if len(options) == 0 {
		return m.newListing.Product.Options
	}

	newMainOption, ok := m.newListing.Product.GetMainOption()
	if !ok {
		return options
	}

	lastMainOption := options[0]
	position := options[0].Position

	for i := range options {
		if options[i].Position < position {
			position = options[i].Position
			lastMainOption = options[i]
		}
	}

	// 只处理新增的 Skc
	for i := range newMainOption.ValueDetails {
		if newMainOption.ValueDetails[i].SalesChannelID == "" {
			lastMainOption.ValueDetails = append(lastMainOption.ValueDetails, newMainOption.ValueDetails[i])
		}
	}

	return options
}

func (m *updateModel) buildRelations(createVariantIDs *sets.StringSet, deleteVariantIDs *sets.StringSet) []*ProductListingRelation {
	relations := make([]*ProductListingRelation, 0)
	for i := range m.lastListing.Relations {
		relations = append(relations, m.lastListing.Relations[i].DeepCopy())
	}

	for i := range relations {
		for j := range m.newListing.Relations {
			if relations[i].ID == m.newListing.Relations[j].ID {
				// link 操作
				relations[i].ProductsCenterVariant = m.newListing.Relations[j].ProductsCenterVariant
			}
		}
	}

	if len(createVariantIDs.ToList()) > 0 {
		for i := range m.newListing.Relations {
			if createVariantIDs.Contains(m.newListing.Relations[i].ProductListingVariantID) {
				relations = append(relations, m.newListing.Relations[i])
			}
		}
	}

	return relations
}

func (s *serviceImpl) afterUpdate(ctx context.Context, newListing *ProductListing, model *updateModel, needPublish bool) error {
	// 如果 unready 状态，不需要进行后续刊登操作
	if newListing.Ready.Status != consts.ReadyStatusReady {
		return nil
	}

	// product 需要使用页面传递的数据
	newListing.Product = model.newListing.Product
	listingCompareModel := newCompareModel(newListing, model.lastListing, compareSetting{}, s.getTiktokCDNDomainRegexps())

	// 非 pending 状态处理，默认需要刊登
	if newListing.State != consts.ProductListingProductStatePending {
		switch newListing.SalesChannel.Platform {
		case consts.Shein:
			needPublish = listingCompareModel.needPublish()
			if newListing.State == consts.ProductListingProductStateActive ||
				newListing.State == consts.ProductListingProductStatePartiallyActive {
				s.afterUpdatePublishPrice(ctx, listingCompareModel, newListing)
				s.afterUpdatePublishInventory(ctx, listingCompareModel, newListing)
			}
		default:
			needPublish = true
			// active 的时候需要进行对比是否有变更，如果有变更则需要重新刊登，以及 setting 变更触发价格/库存同步时间
			if newListing.State == consts.ProductListingProductStateActive {
				needPublish = listingCompareModel.needPublish()
				s.afterUpdatePublishPrice(ctx, listingCompareModel, newListing)
				s.afterUpdatePublishInventory(ctx, listingCompareModel, newListing)
			}
		}
	}

	if !needPublish {
		return nil
	}

	// 发起刊登任务
	auditVersion := listingCompareModel.buildAuditVersion()
	if err := s.createAuditVersion(ctx, newListing.ID, auditVersion, false); err != nil {
		return err
	}

	return s.createPublishTask(ctx, newListing)
}

// 价格刊登事件
func (s *serviceImpl) afterUpdatePublishPrice(ctx context.Context, model *compareModel, listing *ProductListing) {
	publishPriceEvent := ""
	// setting 变更
	if model.isPriceSyncSettingModify() {
		publishPriceEvent = "customized_settings"
	}
	// link 变更
	if publishPriceEvent == "" && model.isSyncedVariantLinkChange() {
		publishPriceEvent = "link"
	}
	if publishPriceEvent != "" {
		// 发起价格补偿事件
		if err := s.createPublishPricesTask(ctx, &priceTaskArg{
			Listing:   listing,
			FromEvent: publishPriceEvent,
		}); err != nil {
			s.logger.ErrorCtx(ctx, "Failed to create publish prices task", zap.Error(err))
		}
	}
}

// 库存刊登事件
func (s *serviceImpl) afterUpdatePublishInventory(ctx context.Context, model *compareModel, listing *ProductListing) {
	publishInventoryEvent := ""
	// setting 变更
	if model.isInventorySyncSettingModify() {
		publishInventoryEvent = "customized_settings"
	}
	// link 变更
	if publishInventoryEvent == "" && model.isSyncedVariantLinkChange() {
		publishInventoryEvent = "link"
	}
	if publishInventoryEvent != "" {
		// 发起库存补偿事件
		if err := s.createPublishInventoriesTask(ctx, &InventoriesTaskArg{
			Listing:   listing,
			FromEvent: publishInventoryEvent,
			Enforced:  true,
		}); err != nil {
			s.logger.ErrorCtx(ctx, "Failed to create publish inventory task", zap.Error(err))
		}
	}
}
