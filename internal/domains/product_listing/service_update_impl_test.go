package product_listing

import (
	"context"
	"encoding/json"
	"os"
	"testing"

	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"

	"github.com/AfterShip/connectors-library/sdks/products_center"
	"github.com/AfterShip/feed-sdk-go/events"
	"github.com/AfterShip/gopkg/uuid"
	"github.com/AfterShip/pltf-pd-product-listings/internal/third_party/bme"

	"github.com/AfterShip/pltf-pd-product-listings/internal/consts"
	"github.com/AfterShip/pltf-pd-product-listings/internal/datastore"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/category"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/common/calculators"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/convert"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/settings"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/task_schedule/task"
	"github.com/AfterShip/pltf-pd-product-listings/internal/logger"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
	"github.com/AfterShip/pltf-pd-product-listings/internal/third_party/connectors"
	"github.com/AfterShip/pltf-pd-product-listings/internal/utils/elasticsearch"
)

func Test_serviceImpl_ListingUpdate_unmatch_unsync_add_variant(t *testing.T) {
	basePath := "./testdata/listing_update/unmatch_unsync/add_variant"
	s := initTestListingUpdateService(t, false, basePath)
	listing := createUpdateEventTestBaseListing(t, s, basePath+"/base_listing.json")
	arg := readUpdateEventArg(t, basePath+"/arg.json")
	ctx := context.Background()
	listing, err := s.Update(ctx, listing.ID, arg)
	require.NoError(t, err)
	require.NotEmpty(t, listing.ID)
	require.Equal(t, consts.SyncStatusUnsync, listing.SyncStatus)
	require.Equal(t, consts.LinkStatusUnlink, listing.LinkStatus)
	require.Equal(t, arg.Product.Title, listing.Product.Title)
	require.Equal(t, arg.Product.Description, listing.Product.Description)
	require.Equal(t, len(arg.Product.Media), len(listing.Product.Media))
	require.Equal(t, len(arg.Product.Variants), len(listing.Product.Variants))
	require.Equal(t, len(arg.Relations), len(listing.Relations))
	require.Equal(t, len(listing.Product.Variants), len(listing.Relations))
	for i := range listing.Product.Variants {
		require.Equal(t, arg.Product.Variants[i].Sku, listing.Product.Variants[i].Sku)
		require.Equal(t, arg.Product.Variants[i].Title, listing.Product.Variants[i].Title)
		require.Equal(t, arg.Product.Variants[i].Price.Amount, listing.Product.Variants[i].Price.Amount)
		require.Equal(t, arg.Product.Variants[i].CompareAtPrice.Amount, listing.Product.Variants[i].CompareAtPrice.Amount)
	}
	for i := range listing.Relations {
		require.Equal(t, listing.Product.Variants[i].ID, listing.Relations[i].ProductListingVariantID)
		require.Equal(t, consts.SyncStatusUnsync, listing.Relations[i].SyncStatus)
		require.Equal(t, consts.LinkStatusUnlink, listing.Relations[i].LinkStatus)
	}

	lastAuditVersion, err := s.latestAuditVersion(ctx, listing.ID)
	require.NoError(t, err)
	require.NotEmpty(t, lastAuditVersion)
	require.Equal(t, listing.ID, lastAuditVersion.ProductListing.ID)
	require.Equal(t, arg.Product.Title, lastAuditVersion.ProductListing.Product.Title)
	require.Equal(t, arg.Product.Description, lastAuditVersion.ProductListing.Product.Description)
	require.Equal(t, len(arg.Product.Media), len(lastAuditVersion.ProductListing.Product.Media))
	require.Equal(t, len(arg.Product.Variants), len(lastAuditVersion.ProductListing.Product.Variants))
	require.Equal(t, len(arg.Relations), len(lastAuditVersion.ProductListing.Relations))
	for i := range lastAuditVersion.ProductListing.Product.Variants {
		require.Equal(t, arg.Product.Variants[i].Sku, lastAuditVersion.ProductListing.Product.Variants[i].Sku)
		require.Equal(t, arg.Product.Variants[i].Title, lastAuditVersion.ProductListing.Product.Variants[i].Title)
		require.Equal(t, arg.Product.Variants[i].Price.Amount, lastAuditVersion.ProductListing.Product.Variants[i].Price.Amount)
		require.Equal(t, arg.Product.Variants[i].CompareAtPrice.Amount, lastAuditVersion.ProductListing.Product.Variants[i].CompareAtPrice.Amount)
	}
}

func Test_serviceImpl_ListingUpdate_unmatch_unsync_delete_variant(t *testing.T) {
	basePath := "./testdata/listing_update/unmatch_unsync/delete_variant"
	s := initTestListingUpdateService(t, false, basePath)
	listing := createUpdateEventTestBaseListing(t, s, basePath+"/base_listing.json")
	arg := readUpdateEventArg(t, basePath+"/arg.json")
	ctx := context.Background()
	listing, err := s.Update(ctx, listing.ID, arg)
	require.NoError(t, err)
	require.NotEmpty(t, listing.ID)
	require.Equal(t, consts.SyncStatusUnsync, listing.SyncStatus)
	require.Equal(t, consts.LinkStatusUnlink, listing.LinkStatus)
	require.Equal(t, arg.Product.Title, listing.Product.Title)
	require.Equal(t, arg.Product.Description, listing.Product.Description)
	require.Equal(t, len(arg.Product.Media), len(listing.Product.Media))
	require.Equal(t, len(arg.Product.Variants), len(listing.Product.Variants))
	require.Equal(t, len(arg.Relations), len(listing.Relations))
	require.Equal(t, len(listing.Product.Variants), len(listing.Relations))
	for i := range listing.Product.Variants {
		require.Equal(t, arg.Product.Variants[i].Sku, listing.Product.Variants[i].Sku)
		require.Equal(t, arg.Product.Variants[i].Title, listing.Product.Variants[i].Title)
		require.Equal(t, arg.Product.Variants[i].Price.Amount, listing.Product.Variants[i].Price.Amount)
		require.Equal(t, arg.Product.Variants[i].CompareAtPrice.Amount, listing.Product.Variants[i].CompareAtPrice.Amount)
	}
	for i := range listing.Relations {
		require.Equal(t, listing.Product.Variants[i].ID, listing.Relations[i].ProductListingVariantID)
		require.Equal(t, consts.SyncStatusUnsync, listing.Relations[i].SyncStatus)
		require.Equal(t, consts.LinkStatusUnlink, listing.Relations[i].LinkStatus)
	}

	lastAuditVersion, err := s.latestAuditVersion(ctx, listing.ID)
	require.NoError(t, err)
	require.NotEmpty(t, lastAuditVersion)
	require.Equal(t, listing.ID, lastAuditVersion.ProductListing.ID)
	require.Equal(t, arg.Product.Title, lastAuditVersion.ProductListing.Product.Title)
	require.Equal(t, arg.Product.Description, lastAuditVersion.ProductListing.Product.Description)
	require.Equal(t, len(arg.Product.Media), len(lastAuditVersion.ProductListing.Product.Media))
	require.Equal(t, len(arg.Product.Variants), len(lastAuditVersion.ProductListing.Product.Variants))
	require.Equal(t, len(arg.Relations), len(lastAuditVersion.ProductListing.Relations))
	for i := range lastAuditVersion.ProductListing.Product.Variants {
		require.Equal(t, arg.Product.Variants[i].Sku, lastAuditVersion.ProductListing.Product.Variants[i].Sku)
		require.Equal(t, arg.Product.Variants[i].Title, lastAuditVersion.ProductListing.Product.Variants[i].Title)
		require.Equal(t, arg.Product.Variants[i].Price.Amount, lastAuditVersion.ProductListing.Product.Variants[i].Price.Amount)
		require.Equal(t, arg.Product.Variants[i].CompareAtPrice.Amount, lastAuditVersion.ProductListing.Product.Variants[i].CompareAtPrice.Amount)
	}
}

func Test_serviceImpl_ListingUpdate_unmatch_unsync_update_variant(t *testing.T) {
	basePath := "./testdata/listing_update/unmatch_unsync/update_variant"
	s := initTestListingUpdateService(t, false, basePath)
	listing := createUpdateEventTestBaseListing(t, s, basePath+"/base_listing.json")
	arg := readUpdateEventArg(t, basePath+"/arg.json")
	ctx := context.Background()
	listing, err := s.Update(ctx, listing.ID, arg)
	require.NoError(t, err)
	require.NotEmpty(t, listing.ID)
	require.Equal(t, consts.SyncStatusUnsync, listing.SyncStatus)
	require.Equal(t, consts.LinkStatusUnlink, listing.LinkStatus)
	require.Equal(t, arg.Product.Title, listing.Product.Title)
	require.Equal(t, arg.Product.Description, listing.Product.Description)
	require.Equal(t, len(arg.Product.Media), len(listing.Product.Media))
	require.Equal(t, len(arg.Product.Variants), len(listing.Product.Variants))
	require.Equal(t, len(arg.Relations), len(listing.Relations))
	require.Equal(t, len(listing.Product.Variants), len(listing.Relations))
	for i := range listing.Product.Variants {
		require.Equal(t, arg.Product.Variants[i].Sku, listing.Product.Variants[i].Sku)
		require.Equal(t, arg.Product.Variants[i].Title, listing.Product.Variants[i].Title)
		require.Equal(t, arg.Product.Variants[i].Price.Amount, listing.Product.Variants[i].Price.Amount)
		require.Equal(t, arg.Product.Variants[i].CompareAtPrice.Amount, listing.Product.Variants[i].CompareAtPrice.Amount)
	}
	for i := range listing.Relations {
		require.Equal(t, listing.Product.Variants[i].ID, listing.Relations[i].ProductListingVariantID)
		require.Equal(t, consts.SyncStatusUnsync, listing.Relations[i].SyncStatus)
		require.Equal(t, consts.LinkStatusUnlink, listing.Relations[i].LinkStatus)
	}

	lastAuditVersion, err := s.latestAuditVersion(ctx, listing.ID)
	require.NoError(t, err)
	require.NotEmpty(t, lastAuditVersion)
	require.Equal(t, listing.ID, lastAuditVersion.ProductListing.ID)
	require.Equal(t, arg.Product.Title, lastAuditVersion.ProductListing.Product.Title)
	require.Equal(t, arg.Product.Description, lastAuditVersion.ProductListing.Product.Description)
	require.Equal(t, len(arg.Product.Media), len(lastAuditVersion.ProductListing.Product.Media))
	require.Equal(t, len(arg.Product.Variants), len(lastAuditVersion.ProductListing.Product.Variants))
	require.Equal(t, len(arg.Relations), len(lastAuditVersion.ProductListing.Relations))
	for i := range lastAuditVersion.ProductListing.Product.Variants {
		require.Equal(t, arg.Product.Variants[i].Sku, lastAuditVersion.ProductListing.Product.Variants[i].Sku)
		require.Equal(t, arg.Product.Variants[i].Title, lastAuditVersion.ProductListing.Product.Variants[i].Title)
		require.Equal(t, arg.Product.Variants[i].Price.Amount, lastAuditVersion.ProductListing.Product.Variants[i].Price.Amount)
		require.Equal(t, arg.Product.Variants[i].CompareAtPrice.Amount, lastAuditVersion.ProductListing.Product.Variants[i].CompareAtPrice.Amount)
	}
}

func Test_serviceImpl_ListingUpdate_unmatch_unsync_modify_product(t *testing.T) {
	basePath := "./testdata/listing_update/unmatch_unsync/modify_product"
	s := initTestListingUpdateService(t, false, basePath)
	listing := createUpdateEventTestBaseListing(t, s, basePath+"/base_listing.json")
	arg := readUpdateEventArg(t, basePath+"/arg.json")
	ctx := context.Background()
	listing, err := s.Update(ctx, listing.ID, arg)
	require.NoError(t, err)
	require.NotEmpty(t, listing.ID)
	require.Equal(t, consts.SyncStatusUnsync, listing.SyncStatus)
	require.Equal(t, consts.LinkStatusUnlink, listing.LinkStatus)
	require.Equal(t, arg.Product.Title, listing.Product.Title)
	require.Equal(t, arg.Product.Description, listing.Product.Description)
	require.Equal(t, len(arg.Product.Media), len(listing.Product.Media))
	require.Equal(t, len(arg.Product.Variants), len(listing.Product.Variants))
	require.Equal(t, len(arg.Relations), len(listing.Relations))
	require.Equal(t, len(listing.Product.Variants), len(listing.Relations))
	for i := range listing.Product.Variants {
		require.Equal(t, arg.Product.Variants[i].Sku, listing.Product.Variants[i].Sku)
		require.Equal(t, arg.Product.Variants[i].Title, listing.Product.Variants[i].Title)
		require.Equal(t, arg.Product.Variants[i].Price.Amount, listing.Product.Variants[i].Price.Amount)
		require.Equal(t, arg.Product.Variants[i].CompareAtPrice.Amount, listing.Product.Variants[i].CompareAtPrice.Amount)
	}
	for i := range listing.Relations {
		require.Equal(t, listing.Product.Variants[i].ID, listing.Relations[i].ProductListingVariantID)
		require.Equal(t, consts.SyncStatusUnsync, listing.Relations[i].SyncStatus)
		require.Equal(t, consts.LinkStatusUnlink, listing.Relations[i].LinkStatus)
	}

	lastAuditVersion, err := s.latestAuditVersion(ctx, listing.ID)
	require.NoError(t, err)
	require.NotEmpty(t, lastAuditVersion)
	require.Equal(t, listing.ID, lastAuditVersion.ProductListing.ID)
	require.Equal(t, arg.Product.Title, lastAuditVersion.ProductListing.Product.Title)
	require.Equal(t, arg.Product.Description, lastAuditVersion.ProductListing.Product.Description)
	require.Equal(t, len(arg.Product.Media), len(lastAuditVersion.ProductListing.Product.Media))
	require.Equal(t, len(arg.Product.Variants), len(lastAuditVersion.ProductListing.Product.Variants))
	require.Equal(t, len(arg.Relations), len(lastAuditVersion.ProductListing.Relations))
	for i := range lastAuditVersion.ProductListing.Product.Variants {
		require.Equal(t, arg.Product.Variants[i].Sku, lastAuditVersion.ProductListing.Product.Variants[i].Sku)
		require.Equal(t, arg.Product.Variants[i].Title, lastAuditVersion.ProductListing.Product.Variants[i].Title)
		require.Equal(t, arg.Product.Variants[i].Price.Amount, lastAuditVersion.ProductListing.Product.Variants[i].Price.Amount)
		require.Equal(t, arg.Product.Variants[i].CompareAtPrice.Amount, lastAuditVersion.ProductListing.Product.Variants[i].CompareAtPrice.Amount)
	}
}

func Test_serviceImpl_ListingUpdate_unmatch_synced_add_variant(t *testing.T) {
	basePath := "./testdata/listing_update/unmatch_synced/add_variant"
	s := initTestListingUpdateService(t, false, basePath)
	listing := createUpdateEventTestBaseListing(t, s, basePath+"/base_listing.json")
	lastListing := listing
	arg := readUpdateEventArg(t, basePath+"/arg.json")
	ctx := context.Background()
	listing, err := s.Update(ctx, listing.ID, arg)
	require.NoError(t, err)
	require.NotEmpty(t, listing.ID)
	require.Equal(t, consts.SyncStatusPartialSynced, listing.SyncStatus)
	require.Equal(t, consts.LinkStatusUnlink, listing.LinkStatus)
	require.Equal(t, lastListing.Product.Title, listing.Product.Title)
	require.Equal(t, lastListing.Product.Description, listing.Product.Description)
	require.Equal(t, len(lastListing.Product.Media), len(listing.Product.Media))
	require.Equal(t, len(arg.Product.Variants), len(listing.Product.Variants))
	require.Equal(t, len(arg.Relations), len(listing.Relations))
	require.Equal(t, len(listing.Product.Variants), len(listing.Relations))
	for i := range listing.Product.Variants {
		require.Equal(t, arg.Product.Variants[i].Sku, listing.Product.Variants[i].Sku)
		require.Equal(t, arg.Product.Variants[i].Title, listing.Product.Variants[i].Title)
		require.Equal(t, arg.Product.Variants[i].Price.Amount, listing.Product.Variants[i].Price.Amount)
		require.Equal(t, arg.Product.Variants[i].CompareAtPrice.Amount, listing.Product.Variants[i].CompareAtPrice.Amount)
	}
	for i := range listing.Relations {
		require.Equal(t, listing.Product.Variants[i].ID, listing.Relations[i].ProductListingVariantID)
		if i == 2 {
			require.Equal(t, consts.SyncStatusUnsync, listing.Relations[i].SyncStatus)
		} else {
			require.Equal(t, consts.SyncStatusSynced, listing.Relations[i].SyncStatus)
		}
		require.Equal(t, consts.LinkStatusUnlink, listing.Relations[i].LinkStatus)
	}

	lastAuditVersion, err := s.latestAuditVersion(ctx, listing.ID)
	require.NoError(t, err)
	require.NotEmpty(t, lastAuditVersion)
	require.Equal(t, listing.ID, lastAuditVersion.ProductListing.ID)
	require.Equal(t, arg.Product.Title, lastAuditVersion.ProductListing.Product.Title)
	require.Equal(t, arg.Product.Description, lastAuditVersion.ProductListing.Product.Description)
	require.Equal(t, len(arg.Product.Media), len(lastAuditVersion.ProductListing.Product.Media))
	require.Equal(t, len(arg.Product.Variants), len(lastAuditVersion.ProductListing.Product.Variants))
	require.Equal(t, len(arg.Relations), len(lastAuditVersion.ProductListing.Relations))
	for i := range lastAuditVersion.ProductListing.Product.Variants {
		require.Equal(t, arg.Product.Variants[i].Sku, lastAuditVersion.ProductListing.Product.Variants[i].Sku)
		require.Equal(t, arg.Product.Variants[i].Title, lastAuditVersion.ProductListing.Product.Variants[i].Title)
		require.Equal(t, arg.Product.Variants[i].Price.Amount, lastAuditVersion.ProductListing.Product.Variants[i].Price.Amount)
		require.Equal(t, arg.Product.Variants[i].CompareAtPrice.Amount, lastAuditVersion.ProductListing.Product.Variants[i].CompareAtPrice.Amount)
	}
}

func Test_serviceImpl_ListingUpdate_unmatch_synced_delete_variant(t *testing.T) {
	basePath := "./testdata/listing_update/unmatch_synced/delete_variant"
	s := initTestListingUpdateService(t, false, basePath)
	listing := createUpdateEventTestBaseListing(t, s, basePath+"/base_listing.json")
	lastListing := listing
	arg := readUpdateEventArg(t, basePath+"/arg.json")
	ctx := context.Background()
	listing, err := s.Update(ctx, listing.ID, arg)
	require.NoError(t, err)
	require.NotEmpty(t, listing.ID)
	require.Equal(t, consts.SyncStatusSynced, listing.SyncStatus)
	require.Equal(t, consts.LinkStatusUnlink, listing.LinkStatus)
	require.Equal(t, lastListing.Product.Title, listing.Product.Title)
	require.Equal(t, lastListing.Product.Description, listing.Product.Description)
	require.Equal(t, len(lastListing.Product.Media), len(listing.Product.Media))
	require.Equal(t, len(lastListing.Product.Variants), len(listing.Product.Variants))
	require.Equal(t, len(lastListing.Relations), len(listing.Relations))
	require.Equal(t, len(listing.Product.Variants), len(listing.Relations))
	for i := range listing.Product.Variants {
		require.Equal(t, lastListing.Product.Variants[i].Sku, listing.Product.Variants[i].Sku)
		require.Equal(t, lastListing.Product.Variants[i].Title, listing.Product.Variants[i].Title)
		require.Equal(t, lastListing.Product.Variants[i].Price.Amount, listing.Product.Variants[i].Price.Amount)
		require.Equal(t, lastListing.Product.Variants[i].CompareAtPrice.Amount, listing.Product.Variants[i].CompareAtPrice.Amount)
	}
	for i := range listing.Relations {
		require.Equal(t, listing.Product.Variants[i].ID, listing.Relations[i].ProductListingVariantID)
		require.Equal(t, consts.SyncStatusSynced, listing.Relations[i].SyncStatus)
		require.Equal(t, consts.LinkStatusUnlink, listing.Relations[i].LinkStatus)
	}

	lastAuditVersion, err := s.latestAuditVersion(ctx, listing.ID)
	require.NoError(t, err)
	require.NotEmpty(t, lastAuditVersion)
	require.Equal(t, listing.ID, lastAuditVersion.ProductListing.ID)
	require.Equal(t, arg.Product.Title, lastAuditVersion.ProductListing.Product.Title)
	require.Equal(t, arg.Product.Description, lastAuditVersion.ProductListing.Product.Description)
	require.Equal(t, len(arg.Product.Media), len(lastAuditVersion.ProductListing.Product.Media))
	require.Equal(t, len(arg.Product.Variants), len(lastAuditVersion.ProductListing.Product.Variants))
	require.Equal(t, len(arg.Relations), len(lastAuditVersion.ProductListing.Relations))
	for i := range lastAuditVersion.ProductListing.Product.Variants {
		require.Equal(t, arg.Product.Variants[i].Sku, lastAuditVersion.ProductListing.Product.Variants[i].Sku)
		require.Equal(t, arg.Product.Variants[i].Title, lastAuditVersion.ProductListing.Product.Variants[i].Title)
		require.Equal(t, arg.Product.Variants[i].Price.Amount, lastAuditVersion.ProductListing.Product.Variants[i].Price.Amount)
		require.Equal(t, arg.Product.Variants[i].CompareAtPrice.Amount, lastAuditVersion.ProductListing.Product.Variants[i].CompareAtPrice.Amount)
	}
}

func Test_serviceImpl_ListingUpdate_unmatch_synced_update_variant(t *testing.T) {
	basePath := "./testdata/listing_update/unmatch_synced/update_variant"
	s := initTestListingUpdateService(t, false, basePath)
	listing := createUpdateEventTestBaseListing(t, s, basePath+"/base_listing.json")
	lastListing := listing
	arg := readUpdateEventArg(t, basePath+"/arg.json")
	ctx := context.Background()
	listing, err := s.Update(ctx, listing.ID, arg)
	require.NoError(t, err)
	require.NotEmpty(t, listing.ID)
	require.Equal(t, consts.SyncStatusSynced, listing.SyncStatus)
	require.Equal(t, consts.LinkStatusUnlink, listing.LinkStatus)
	require.Equal(t, lastListing.Product.Title, listing.Product.Title)
	require.Equal(t, lastListing.Product.Description, listing.Product.Description)
	require.Equal(t, len(lastListing.Product.Media), len(listing.Product.Media))
	require.Equal(t, len(lastListing.Product.Variants), len(listing.Product.Variants))
	require.Equal(t, len(lastListing.Relations), len(listing.Relations))
	require.Equal(t, len(listing.Product.Variants), len(listing.Relations))
	for i := range listing.Product.Variants {
		require.Equal(t, lastListing.Product.Variants[i].Sku, listing.Product.Variants[i].Sku)
		require.Equal(t, lastListing.Product.Variants[i].Title, listing.Product.Variants[i].Title)
		require.Equal(t, lastListing.Product.Variants[i].Price.Amount, listing.Product.Variants[i].Price.Amount)
		require.Equal(t, lastListing.Product.Variants[i].CompareAtPrice.Amount, listing.Product.Variants[i].CompareAtPrice.Amount)
	}
	for i := range listing.Relations {
		require.Equal(t, listing.Product.Variants[i].ID, listing.Relations[i].ProductListingVariantID)
		require.Equal(t, consts.SyncStatusSynced, listing.Relations[i].SyncStatus)
		require.Equal(t, consts.LinkStatusUnlink, listing.Relations[i].LinkStatus)
	}

	lastAuditVersion, err := s.latestAuditVersion(ctx, listing.ID)
	require.NoError(t, err)
	require.NotEmpty(t, lastAuditVersion)
	require.Equal(t, listing.ID, lastAuditVersion.ProductListing.ID)
	require.Equal(t, arg.Product.Title, lastAuditVersion.ProductListing.Product.Title)
	require.Equal(t, arg.Product.Description, lastAuditVersion.ProductListing.Product.Description)
	require.Equal(t, len(arg.Product.Media), len(lastAuditVersion.ProductListing.Product.Media))
	require.Equal(t, len(arg.Product.Variants), len(lastAuditVersion.ProductListing.Product.Variants))
	require.Equal(t, len(arg.Relations), len(lastAuditVersion.ProductListing.Relations))
	for i := range lastAuditVersion.ProductListing.Product.Variants {
		require.Equal(t, arg.Product.Variants[i].Sku, lastAuditVersion.ProductListing.Product.Variants[i].Sku)
		require.Equal(t, arg.Product.Variants[i].Title, lastAuditVersion.ProductListing.Product.Variants[i].Title)
		require.Equal(t, arg.Product.Variants[i].Price.Amount, lastAuditVersion.ProductListing.Product.Variants[i].Price.Amount)
		require.Equal(t, arg.Product.Variants[i].CompareAtPrice.Amount, lastAuditVersion.ProductListing.Product.Variants[i].CompareAtPrice.Amount)
	}
}

func Test_serviceImpl_ListingUpdate_unmatch_synced_modify_product(t *testing.T) {
	basePath := "./testdata/listing_update/unmatch_synced/modify_product"
	s := initTestListingUpdateService(t, false, basePath)
	listing := createUpdateEventTestBaseListing(t, s, basePath+"/base_listing.json")
	lastListing := listing
	arg := readUpdateEventArg(t, basePath+"/arg.json")
	ctx := context.Background()
	listing, err := s.Update(ctx, listing.ID, arg)
	require.NoError(t, err)
	require.NotEmpty(t, listing.ID)
	require.Equal(t, consts.SyncStatusSynced, listing.SyncStatus)
	require.Equal(t, consts.LinkStatusUnlink, listing.LinkStatus)
	require.Equal(t, lastListing.Product.Title, listing.Product.Title)
	require.Equal(t, lastListing.Product.Description, listing.Product.Description)
	require.Equal(t, len(lastListing.Product.Media), len(listing.Product.Media))
	require.Equal(t, len(lastListing.Product.Variants), len(listing.Product.Variants))
	require.Equal(t, len(lastListing.Relations), len(listing.Relations))
	require.Equal(t, len(listing.Product.Variants), len(listing.Relations))
	for i := range listing.Product.Variants {
		require.Equal(t, lastListing.Product.Variants[i].Sku, listing.Product.Variants[i].Sku)
		require.Equal(t, lastListing.Product.Variants[i].Title, listing.Product.Variants[i].Title)
		require.Equal(t, lastListing.Product.Variants[i].Price.Amount, listing.Product.Variants[i].Price.Amount)
		require.Equal(t, lastListing.Product.Variants[i].CompareAtPrice.Amount, listing.Product.Variants[i].CompareAtPrice.Amount)
	}
	for i := range listing.Relations {
		require.Equal(t, listing.Product.Variants[i].ID, listing.Relations[i].ProductListingVariantID)
		require.Equal(t, consts.SyncStatusSynced, listing.Relations[i].SyncStatus)
		require.Equal(t, consts.LinkStatusUnlink, listing.Relations[i].LinkStatus)
	}

	lastAuditVersion, err := s.latestAuditVersion(ctx, listing.ID)
	require.NoError(t, err)
	require.NotEmpty(t, lastAuditVersion)
	require.Equal(t, listing.ID, lastAuditVersion.ProductListing.ID)
	require.Equal(t, arg.Product.Title, lastAuditVersion.ProductListing.Product.Title)
	require.Equal(t, arg.Product.Description, lastAuditVersion.ProductListing.Product.Description)
	require.Equal(t, len(arg.Product.Media), len(lastAuditVersion.ProductListing.Product.Media))
	require.Equal(t, len(arg.Product.Variants), len(lastAuditVersion.ProductListing.Product.Variants))
	require.Equal(t, len(arg.Relations), len(lastAuditVersion.ProductListing.Relations))
	for i := range lastAuditVersion.ProductListing.Product.Variants {
		require.Equal(t, arg.Product.Variants[i].Sku, lastAuditVersion.ProductListing.Product.Variants[i].Sku)
		require.Equal(t, arg.Product.Variants[i].Title, lastAuditVersion.ProductListing.Product.Variants[i].Title)
		require.Equal(t, arg.Product.Variants[i].Price.Amount, lastAuditVersion.ProductListing.Product.Variants[i].Price.Amount)
		require.Equal(t, arg.Product.Variants[i].CompareAtPrice.Amount, lastAuditVersion.ProductListing.Product.Variants[i].CompareAtPrice.Amount)
	}
}

func Test_serviceImpl_ListingUpdate_match_unsync_not_auto_sync_add_variant(t *testing.T) {
	basePath := "./testdata/listing_update/match_unsync/not_auto_sync/add_variant"
	s := initTestListingUpdateService(t, true, basePath)
	listing := createUpdateEventTestBaseListing(t, s, basePath+"/base_listing.json")
	arg := readUpdateEventArg(t, basePath+"/arg.json")
	ctx := context.Background()
	listing, err := s.Update(ctx, listing.ID, arg)
	require.NoError(t, err)
	require.NotEmpty(t, listing.ID)
	require.Equal(t, consts.SyncStatusUnsync, listing.SyncStatus)
	require.Equal(t, consts.LinkStatusPartialLinked, listing.LinkStatus)
	require.Equal(t, arg.Product.Title, listing.Product.Title)
	require.Equal(t, arg.Product.Description, listing.Product.Description)
	require.Equal(t, len(arg.Product.Media), len(listing.Product.Media))
	require.Equal(t, len(arg.Product.Variants), len(listing.Product.Variants))
	require.Equal(t, len(arg.Relations), len(listing.Relations))
	require.Equal(t, len(listing.Product.Variants), len(listing.Relations))
	for i := range listing.Product.Variants {
		require.Equal(t, arg.Product.Variants[i].Sku, listing.Product.Variants[i].Sku)
		require.Equal(t, arg.Product.Variants[i].Title, listing.Product.Variants[i].Title)
		require.Equal(t, arg.Product.Variants[i].Price.Amount, listing.Product.Variants[i].Price.Amount)
		require.Equal(t, arg.Product.Variants[i].CompareAtPrice.Amount, listing.Product.Variants[i].CompareAtPrice.Amount)
	}
	for i := range listing.Relations {
		require.Equal(t, listing.Product.Variants[i].ID, listing.Relations[i].ProductListingVariantID)
		require.Equal(t, consts.SyncStatusUnsync, listing.Relations[i].SyncStatus)
		if i == 2 {
			require.Equal(t, consts.LinkStatusUnlink, listing.Relations[i].LinkStatus)
		} else {
			require.Equal(t, consts.LinkStatusLinked, listing.Relations[i].LinkStatus)
		}
	}

	lastAuditVersion, err := s.latestAuditVersion(ctx, listing.ID)
	require.NoError(t, err)
	require.NotEmpty(t, lastAuditVersion)
	require.Equal(t, listing.ID, lastAuditVersion.ProductListing.ID)
	require.Equal(t, arg.Product.Title, lastAuditVersion.ProductListing.Product.Title)
	require.Equal(t, arg.Product.Description, lastAuditVersion.ProductListing.Product.Description)
	require.Equal(t, len(arg.Product.Media), len(lastAuditVersion.ProductListing.Product.Media))
	require.Equal(t, len(arg.Product.Variants), len(lastAuditVersion.ProductListing.Product.Variants))
	require.Equal(t, len(arg.Relations), len(lastAuditVersion.ProductListing.Relations))
	for i := range lastAuditVersion.ProductListing.Product.Variants {
		require.Equal(t, arg.Product.Variants[i].Sku, lastAuditVersion.ProductListing.Product.Variants[i].Sku)
		require.Equal(t, arg.Product.Variants[i].Title, lastAuditVersion.ProductListing.Product.Variants[i].Title)
		require.Equal(t, arg.Product.Variants[i].Price.Amount, lastAuditVersion.ProductListing.Product.Variants[i].Price.Amount)
		require.Equal(t, arg.Product.Variants[i].CompareAtPrice.Amount, lastAuditVersion.ProductListing.Product.Variants[i].CompareAtPrice.Amount)
	}
}

func Test_serviceImpl_ListingUpdate_match_unsync_not_auto_sync_delete_variant(t *testing.T) {
	basePath := "./testdata/listing_update/match_unsync/not_auto_sync/delete_variant"
	s := initTestListingUpdateService(t, true, basePath)
	listing := createUpdateEventTestBaseListing(t, s, basePath+"/base_listing.json")
	arg := readUpdateEventArg(t, basePath+"/arg.json")
	ctx := context.Background()
	listing, err := s.Update(ctx, listing.ID, arg)
	require.NoError(t, err)
	require.NotEmpty(t, listing.ID)
	require.Equal(t, consts.SyncStatusUnsync, listing.SyncStatus)
	require.Equal(t, consts.LinkStatusLinked, listing.LinkStatus)
	require.Equal(t, arg.Product.Title, listing.Product.Title)
	require.Equal(t, arg.Product.Description, listing.Product.Description)
	require.Equal(t, len(arg.Product.Media), len(listing.Product.Media))
	require.Equal(t, len(arg.Product.Variants), len(listing.Product.Variants))
	require.Equal(t, len(arg.Relations), len(listing.Relations))
	require.Equal(t, len(listing.Product.Variants), len(listing.Relations))
	for i := range listing.Product.Variants {
		require.Equal(t, arg.Product.Variants[i].Sku, listing.Product.Variants[i].Sku)
		require.Equal(t, arg.Product.Variants[i].Title, listing.Product.Variants[i].Title)
		require.Equal(t, arg.Product.Variants[i].Price.Amount, listing.Product.Variants[i].Price.Amount)
		require.Equal(t, arg.Product.Variants[i].CompareAtPrice.Amount, listing.Product.Variants[i].CompareAtPrice.Amount)
	}
	for i := range listing.Relations {
		require.Equal(t, listing.Product.Variants[i].ID, listing.Relations[i].ProductListingVariantID)
		require.Equal(t, consts.SyncStatusUnsync, listing.Relations[i].SyncStatus)
		require.Equal(t, consts.LinkStatusLinked, listing.Relations[i].LinkStatus)
	}

	lastAuditVersion, err := s.latestAuditVersion(ctx, listing.ID)
	require.NoError(t, err)
	require.NotEmpty(t, lastAuditVersion)
	require.Equal(t, listing.ID, lastAuditVersion.ProductListing.ID)
	require.Equal(t, arg.Product.Title, lastAuditVersion.ProductListing.Product.Title)
	require.Equal(t, arg.Product.Description, lastAuditVersion.ProductListing.Product.Description)
	require.Equal(t, len(arg.Product.Media), len(lastAuditVersion.ProductListing.Product.Media))
	require.Equal(t, len(arg.Product.Variants), len(lastAuditVersion.ProductListing.Product.Variants))
	require.Equal(t, len(arg.Relations), len(lastAuditVersion.ProductListing.Relations))
	for i := range lastAuditVersion.ProductListing.Product.Variants {
		require.Equal(t, arg.Product.Variants[i].Sku, lastAuditVersion.ProductListing.Product.Variants[i].Sku)
		require.Equal(t, arg.Product.Variants[i].Title, lastAuditVersion.ProductListing.Product.Variants[i].Title)
		require.Equal(t, arg.Product.Variants[i].Price.Amount, lastAuditVersion.ProductListing.Product.Variants[i].Price.Amount)
		require.Equal(t, arg.Product.Variants[i].CompareAtPrice.Amount, lastAuditVersion.ProductListing.Product.Variants[i].CompareAtPrice.Amount)
	}
}

func Test_serviceImpl_ListingUpdate_match_unsync_not_auto_sync_update_variant(t *testing.T) {
	basePath := "./testdata/listing_update/match_unsync/not_auto_sync/update_variant"
	s := initTestListingUpdateService(t, true, basePath)
	listing := createUpdateEventTestBaseListing(t, s, basePath+"/base_listing.json")
	arg := readUpdateEventArg(t, basePath+"/arg.json")
	ctx := context.Background()
	listing, err := s.Update(ctx, listing.ID, arg)
	require.NoError(t, err)
	require.NotEmpty(t, listing.ID)
	require.Equal(t, consts.SyncStatusUnsync, listing.SyncStatus)
	require.Equal(t, consts.LinkStatusLinked, listing.LinkStatus)
	require.Equal(t, arg.Product.Title, listing.Product.Title)
	require.Equal(t, arg.Product.Description, listing.Product.Description)
	require.Equal(t, len(arg.Product.Media), len(listing.Product.Media))
	require.Equal(t, len(arg.Product.Variants), len(listing.Product.Variants))
	require.Equal(t, len(arg.Relations), len(listing.Relations))
	require.Equal(t, len(listing.Product.Variants), len(listing.Relations))
	for i := range listing.Product.Variants {
		require.Equal(t, arg.Product.Variants[i].Sku, listing.Product.Variants[i].Sku)
		require.Equal(t, arg.Product.Variants[i].Title, listing.Product.Variants[i].Title)
		require.Equal(t, arg.Product.Variants[i].Price.Amount, listing.Product.Variants[i].Price.Amount)
		require.Equal(t, arg.Product.Variants[i].CompareAtPrice.Amount, listing.Product.Variants[i].CompareAtPrice.Amount)
	}
	for i := range listing.Relations {
		require.Equal(t, listing.Product.Variants[i].ID, listing.Relations[i].ProductListingVariantID)
		require.Equal(t, consts.SyncStatusUnsync, listing.Relations[i].SyncStatus)
		require.Equal(t, consts.LinkStatusLinked, listing.Relations[i].LinkStatus)
	}

	lastAuditVersion, err := s.latestAuditVersion(ctx, listing.ID)
	require.NoError(t, err)
	require.NotEmpty(t, lastAuditVersion)
	require.Equal(t, listing.ID, lastAuditVersion.ProductListing.ID)
	require.Equal(t, arg.Product.Title, lastAuditVersion.ProductListing.Product.Title)
	require.Equal(t, arg.Product.Description, lastAuditVersion.ProductListing.Product.Description)
	require.Equal(t, len(arg.Product.Media), len(lastAuditVersion.ProductListing.Product.Media))
	require.Equal(t, len(arg.Product.Variants), len(lastAuditVersion.ProductListing.Product.Variants))
	require.Equal(t, len(arg.Relations), len(lastAuditVersion.ProductListing.Relations))
	for i := range lastAuditVersion.ProductListing.Product.Variants {
		require.Equal(t, arg.Product.Variants[i].Sku, lastAuditVersion.ProductListing.Product.Variants[i].Sku)
		require.Equal(t, arg.Product.Variants[i].Title, lastAuditVersion.ProductListing.Product.Variants[i].Title)
		require.Equal(t, arg.Product.Variants[i].Price.Amount, lastAuditVersion.ProductListing.Product.Variants[i].Price.Amount)
		require.Equal(t, arg.Product.Variants[i].CompareAtPrice.Amount, lastAuditVersion.ProductListing.Product.Variants[i].CompareAtPrice.Amount)
	}
}

func Test_serviceImpl_ListingUpdate_match_unsync_not_auto_sync_modify_product(t *testing.T) {
	basePath := "./testdata/listing_update/match_unsync/not_auto_sync/modify_product"
	s := initTestListingUpdateService(t, true, basePath)
	listing := createUpdateEventTestBaseListing(t, s, basePath+"/base_listing.json")
	arg := readUpdateEventArg(t, basePath+"/arg.json")
	ctx := context.Background()
	listing, err := s.Update(ctx, listing.ID, arg)
	require.NoError(t, err)
	require.NotEmpty(t, listing.ID)
	require.Equal(t, consts.SyncStatusUnsync, listing.SyncStatus)
	require.Equal(t, consts.LinkStatusLinked, listing.LinkStatus)
	require.Equal(t, arg.Product.Title, listing.Product.Title)
	require.Equal(t, arg.Product.Description, listing.Product.Description)
	require.Equal(t, len(arg.Product.Media), len(listing.Product.Media))
	require.Equal(t, len(arg.Product.Variants), len(listing.Product.Variants))
	require.Equal(t, len(arg.Relations), len(listing.Relations))
	require.Equal(t, len(listing.Product.Variants), len(listing.Relations))
	for i := range listing.Product.Variants {
		require.Equal(t, arg.Product.Variants[i].Sku, listing.Product.Variants[i].Sku)
		require.Equal(t, arg.Product.Variants[i].Title, listing.Product.Variants[i].Title)
		require.Equal(t, arg.Product.Variants[i].Price.Amount, listing.Product.Variants[i].Price.Amount)
		require.Equal(t, arg.Product.Variants[i].CompareAtPrice.Amount, listing.Product.Variants[i].CompareAtPrice.Amount)
	}
	for i := range listing.Relations {
		require.Equal(t, listing.Product.Variants[i].ID, listing.Relations[i].ProductListingVariantID)
		require.Equal(t, consts.SyncStatusUnsync, listing.Relations[i].SyncStatus)
		require.Equal(t, consts.LinkStatusLinked, listing.Relations[i].LinkStatus)
	}

	lastAuditVersion, err := s.latestAuditVersion(ctx, listing.ID)
	require.NoError(t, err)
	require.NotEmpty(t, lastAuditVersion)
	require.Equal(t, listing.ID, lastAuditVersion.ProductListing.ID)
	require.Equal(t, arg.Product.Title, lastAuditVersion.ProductListing.Product.Title)
	require.Equal(t, arg.Product.Description, lastAuditVersion.ProductListing.Product.Description)
	require.Equal(t, len(arg.Product.Media), len(lastAuditVersion.ProductListing.Product.Media))
	require.Equal(t, len(arg.Product.Variants), len(lastAuditVersion.ProductListing.Product.Variants))
	require.Equal(t, len(arg.Relations), len(lastAuditVersion.ProductListing.Relations))
	for i := range lastAuditVersion.ProductListing.Product.Variants {
		require.Equal(t, arg.Product.Variants[i].Sku, lastAuditVersion.ProductListing.Product.Variants[i].Sku)
		require.Equal(t, arg.Product.Variants[i].Title, lastAuditVersion.ProductListing.Product.Variants[i].Title)
		require.Equal(t, arg.Product.Variants[i].Price.Amount, lastAuditVersion.ProductListing.Product.Variants[i].Price.Amount)
		require.Equal(t, arg.Product.Variants[i].CompareAtPrice.Amount, lastAuditVersion.ProductListing.Product.Variants[i].CompareAtPrice.Amount)
	}
}

func Test_serviceImpl_ListingUpdate_match_unsync_auto_sync_field_add_variant(t *testing.T) {
	basePath := "./testdata/listing_update/match_unsync/auto_sync_field/add_variant"
	s := initTestListingUpdateService(t, true, basePath)
	listing := createUpdateEventTestBaseListing(t, s, basePath+"/base_listing.json")
	arg := readUpdateEventArg(t, basePath+"/arg.json")
	ctx := context.Background()
	listing, err := s.Update(ctx, listing.ID, arg)
	require.NoError(t, err)
	require.NotEmpty(t, listing.ID)
	require.Equal(t, consts.SyncStatusUnsync, listing.SyncStatus)
	require.Equal(t, consts.LinkStatusPartialLinked, listing.LinkStatus)
	require.Equal(t, arg.Product.Title, listing.Product.Title)
	require.Equal(t, arg.Product.Description, listing.Product.Description)
	require.Equal(t, len(arg.Product.Media), len(listing.Product.Media))
	require.Equal(t, len(arg.Product.Variants), len(listing.Product.Variants))
	require.Equal(t, len(arg.Relations), len(listing.Relations))
	require.Equal(t, len(listing.Product.Variants), len(listing.Relations))
	for i := range listing.Product.Variants {
		require.Equal(t, arg.Product.Variants[i].Sku, listing.Product.Variants[i].Sku)
		require.Equal(t, arg.Product.Variants[i].Title, listing.Product.Variants[i].Title)
		require.Equal(t, arg.Product.Variants[i].Price.Amount, listing.Product.Variants[i].Price.Amount)
		require.Equal(t, arg.Product.Variants[i].CompareAtPrice.Amount, listing.Product.Variants[i].CompareAtPrice.Amount)
	}
	for i := range listing.Relations {
		require.Equal(t, listing.Product.Variants[i].ID, listing.Relations[i].ProductListingVariantID)
		require.Equal(t, consts.SyncStatusUnsync, listing.Relations[i].SyncStatus)
		if i == 2 {
			require.Equal(t, consts.LinkStatusUnlink, listing.Relations[i].LinkStatus)
		} else {
			require.Equal(t, consts.LinkStatusLinked, listing.Relations[i].LinkStatus)
		}
	}

	lastAuditVersion, err := s.latestAuditVersion(ctx, listing.ID)
	require.NoError(t, err)
	require.NotEmpty(t, lastAuditVersion)
	require.Equal(t, listing.ID, lastAuditVersion.ProductListing.ID)
	require.Equal(t, arg.Product.Title, lastAuditVersion.ProductListing.Product.Title)
	require.Equal(t, arg.Product.Description, lastAuditVersion.ProductListing.Product.Description)
	require.Equal(t, len(arg.Product.Variants), len(lastAuditVersion.ProductListing.Product.Variants))
	require.Equal(t, len(arg.Relations), len(lastAuditVersion.ProductListing.Relations))
	for i := range lastAuditVersion.ProductListing.Product.Variants {
		require.Equal(t, arg.Product.Variants[i].Sku, lastAuditVersion.ProductListing.Product.Variants[i].Sku)
		require.Equal(t, arg.Product.Variants[i].Title, lastAuditVersion.ProductListing.Product.Variants[i].Title)
		require.Equal(t, arg.Product.Variants[i].Price.Amount, lastAuditVersion.ProductListing.Product.Variants[i].Price.Amount)
		require.Equal(t, arg.Product.Variants[i].CompareAtPrice.Amount, lastAuditVersion.ProductListing.Product.Variants[i].CompareAtPrice.Amount)
	}
}

func Test_serviceImpl_ListingUpdate_match_unsync_auto_sync_field_delete_variant(t *testing.T) {
	basePath := "./testdata/listing_update/match_unsync/auto_sync_field/delete_variant"
	s := initTestListingUpdateService(t, true, basePath)
	listing := createUpdateEventTestBaseListing(t, s, basePath+"/base_listing.json")
	arg := readUpdateEventArg(t, basePath+"/arg.json")
	ctx := context.Background()
	listing, err := s.Update(ctx, listing.ID, arg)
	require.NoError(t, err)
	require.NotEmpty(t, listing.ID)
	require.Equal(t, consts.SyncStatusUnsync, listing.SyncStatus)
	require.Equal(t, consts.LinkStatusLinked, listing.LinkStatus)
	require.Equal(t, arg.Product.Title, listing.Product.Title)
	require.Equal(t, arg.Product.Description, listing.Product.Description)
	require.Equal(t, len(arg.Product.Media), len(listing.Product.Media))
	require.Equal(t, len(arg.Product.Variants), len(listing.Product.Variants))
	require.Equal(t, len(arg.Relations), len(listing.Relations))
	require.Equal(t, len(listing.Product.Variants), len(listing.Relations))
	for i := range listing.Product.Variants {
		require.Equal(t, arg.Product.Variants[i].Sku, listing.Product.Variants[i].Sku)
		require.Equal(t, arg.Product.Variants[i].Title, listing.Product.Variants[i].Title)
		require.Equal(t, arg.Product.Variants[i].Price.Amount, listing.Product.Variants[i].Price.Amount)
		require.Equal(t, arg.Product.Variants[i].CompareAtPrice.Amount, listing.Product.Variants[i].CompareAtPrice.Amount)
	}
	for i := range listing.Relations {
		require.Equal(t, listing.Product.Variants[i].ID, listing.Relations[i].ProductListingVariantID)
		require.Equal(t, consts.SyncStatusUnsync, listing.Relations[i].SyncStatus)
		require.Equal(t, consts.LinkStatusLinked, listing.Relations[i].LinkStatus)
	}

	lastAuditVersion, err := s.latestAuditVersion(ctx, listing.ID)
	require.NoError(t, err)
	require.NotEmpty(t, lastAuditVersion)
	require.Equal(t, listing.ID, lastAuditVersion.ProductListing.ID)
	require.Equal(t, arg.Product.Title, lastAuditVersion.ProductListing.Product.Title)
	require.Equal(t, arg.Product.Description, lastAuditVersion.ProductListing.Product.Description)
	require.Equal(t, len(arg.Product.Variants), len(lastAuditVersion.ProductListing.Product.Variants))
	require.Equal(t, len(arg.Relations), len(lastAuditVersion.ProductListing.Relations))
	for i := range lastAuditVersion.ProductListing.Product.Variants {
		require.Equal(t, arg.Product.Variants[i].Sku, lastAuditVersion.ProductListing.Product.Variants[i].Sku)
		require.Equal(t, arg.Product.Variants[i].Title, lastAuditVersion.ProductListing.Product.Variants[i].Title)
		require.Equal(t, arg.Product.Variants[i].Price.Amount, lastAuditVersion.ProductListing.Product.Variants[i].Price.Amount)
		require.Equal(t, arg.Product.Variants[i].CompareAtPrice.Amount, lastAuditVersion.ProductListing.Product.Variants[i].CompareAtPrice.Amount)
	}
}

func Test_serviceImpl_ListingUpdate_match_unsync_auto_sync_field_update_variant(t *testing.T) {
	basePath := "./testdata/listing_update/match_unsync/auto_sync_field/update_variant"
	s := initTestListingUpdateService(t, true, basePath)
	listing := createUpdateEventTestBaseListing(t, s, basePath+"/base_listing.json")
	arg := readUpdateEventArg(t, basePath+"/arg.json")
	ctx := context.Background()
	listing, err := s.Update(ctx, listing.ID, arg)
	require.NoError(t, err)
	require.NotEmpty(t, listing.ID)
	require.Equal(t, consts.SyncStatusUnsync, listing.SyncStatus)
	require.Equal(t, consts.LinkStatusLinked, listing.LinkStatus)
	require.Equal(t, arg.Product.Title, listing.Product.Title)
	require.Equal(t, arg.Product.Description, listing.Product.Description)
	require.Equal(t, len(arg.Product.Media), len(listing.Product.Media))
	require.Equal(t, len(arg.Product.Variants), len(listing.Product.Variants))
	require.Equal(t, len(arg.Relations), len(listing.Relations))
	require.Equal(t, len(listing.Product.Variants), len(listing.Relations))
	for i := range listing.Product.Variants {
		require.Equal(t, arg.Product.Variants[i].Sku, listing.Product.Variants[i].Sku)
		require.Equal(t, arg.Product.Variants[i].Title, listing.Product.Variants[i].Title)
		require.Equal(t, arg.Product.Variants[i].Price.Amount, listing.Product.Variants[i].Price.Amount)
		require.Equal(t, arg.Product.Variants[i].CompareAtPrice.Amount, listing.Product.Variants[i].CompareAtPrice.Amount)
	}
	for i := range listing.Relations {
		require.Equal(t, listing.Product.Variants[i].ID, listing.Relations[i].ProductListingVariantID)
		require.Equal(t, consts.SyncStatusUnsync, listing.Relations[i].SyncStatus)
		require.Equal(t, consts.LinkStatusLinked, listing.Relations[i].LinkStatus)
	}

	lastAuditVersion, err := s.latestAuditVersion(ctx, listing.ID)
	require.NoError(t, err)
	require.NotEmpty(t, lastAuditVersion)
	require.Equal(t, listing.ID, lastAuditVersion.ProductListing.ID)
	require.Equal(t, arg.Product.Title, lastAuditVersion.ProductListing.Product.Title)
	require.Equal(t, arg.Product.Description, lastAuditVersion.ProductListing.Product.Description)
	require.Equal(t, len(arg.Product.Variants), len(lastAuditVersion.ProductListing.Product.Variants))
	require.Equal(t, len(arg.Relations), len(lastAuditVersion.ProductListing.Relations))
	for i := range lastAuditVersion.ProductListing.Product.Variants {
		require.Equal(t, arg.Product.Variants[i].Sku, lastAuditVersion.ProductListing.Product.Variants[i].Sku)
		require.Equal(t, arg.Product.Variants[i].Title, lastAuditVersion.ProductListing.Product.Variants[i].Title)
		require.Equal(t, arg.Product.Variants[i].Price.Amount, lastAuditVersion.ProductListing.Product.Variants[i].Price.Amount)
		require.Equal(t, arg.Product.Variants[i].CompareAtPrice.Amount, lastAuditVersion.ProductListing.Product.Variants[i].CompareAtPrice.Amount)
	}
}

func Test_serviceImpl_ListingUpdate_match_unsync_auto_sync_field_modify_product(t *testing.T) {
	basePath := "./testdata/listing_update/match_unsync/auto_sync_field/modify_product"
	product := readUpdateEventProductsCenterProduct(t, basePath+"/product.json")
	s := initTestListingUpdateService(t, true, basePath)
	listing := createUpdateEventTestBaseListing(t, s, basePath+"/base_listing.json")
	arg := readUpdateEventArg(t, basePath+"/arg.json")
	ctx := context.Background()
	listing, err := s.Update(ctx, listing.ID, arg)
	require.NoError(t, err)
	require.NotEmpty(t, listing.ID)
	require.Equal(t, consts.SyncStatusUnsync, listing.SyncStatus)
	require.Equal(t, consts.LinkStatusLinked, listing.LinkStatus)
	require.Equal(t, product.Title, listing.Product.Title)
	require.Equal(t, product.Description, listing.Product.Description)
	require.Equal(t, len(product.Media), len(listing.Product.Media))
	require.Equal(t, len(arg.Product.Variants), len(listing.Product.Variants))
	require.Equal(t, len(arg.Relations), len(listing.Relations))
	require.Equal(t, len(listing.Product.Variants), len(listing.Relations))
	for i := range listing.Product.Variants {
		require.Equal(t, arg.Product.Variants[i].Sku, listing.Product.Variants[i].Sku)
		require.Equal(t, arg.Product.Variants[i].Title, listing.Product.Variants[i].Title)
		require.Equal(t, arg.Product.Variants[i].Price.Amount, listing.Product.Variants[i].Price.Amount)
		require.Equal(t, arg.Product.Variants[i].CompareAtPrice.Amount, listing.Product.Variants[i].CompareAtPrice.Amount)
	}
	for i := range listing.Relations {
		require.Equal(t, listing.Product.Variants[i].ID, listing.Relations[i].ProductListingVariantID)
		require.Equal(t, consts.SyncStatusUnsync, listing.Relations[i].SyncStatus)
		require.Equal(t, consts.LinkStatusLinked, listing.Relations[i].LinkStatus)
	}

	lastAuditVersion, err := s.latestAuditVersion(ctx, listing.ID)
	require.NoError(t, err)
	require.NotEmpty(t, lastAuditVersion)
	require.Equal(t, listing.ID, lastAuditVersion.ProductListing.ID)
	require.Equal(t, product.Title, lastAuditVersion.ProductListing.Product.Title)
	require.Equal(t, product.Description, lastAuditVersion.ProductListing.Product.Description)
	require.Equal(t, len(product.Media), len(lastAuditVersion.ProductListing.Product.Media))
	require.Equal(t, len(arg.Product.Variants), len(lastAuditVersion.ProductListing.Product.Variants))
	require.Equal(t, len(arg.Relations), len(lastAuditVersion.ProductListing.Relations))
	for i := range lastAuditVersion.ProductListing.Product.Variants {
		require.Equal(t, arg.Product.Variants[i].Sku, lastAuditVersion.ProductListing.Product.Variants[i].Sku)
		require.Equal(t, arg.Product.Variants[i].Title, lastAuditVersion.ProductListing.Product.Variants[i].Title)
		require.Equal(t, arg.Product.Variants[i].Price.Amount, lastAuditVersion.ProductListing.Product.Variants[i].Price.Amount)
		require.Equal(t, arg.Product.Variants[i].CompareAtPrice.Amount, lastAuditVersion.ProductListing.Product.Variants[i].CompareAtPrice.Amount)
	}
}

func Test_serviceImpl_ListingUpdate_match_unsync_auto_sync_variant_add_variant(t *testing.T) {
	basePath := "./testdata/listing_update/match_unsync/auto_sync_variant/add_variant"
	s := initTestListingUpdateService(t, true, basePath)
	product := readUpdateEventProductsCenterProduct(t, basePath+"/product.json")
	listing := createUpdateEventTestBaseListing(t, s, basePath+"/base_listing.json")
	arg := readUpdateEventArg(t, basePath+"/arg.json")
	ctx := context.Background()
	listing, err := s.Update(ctx, listing.ID, arg)
	require.NoError(t, err)
	require.NotEmpty(t, listing.ID)
	require.Equal(t, consts.SyncStatusUnsync, listing.SyncStatus)
	require.Equal(t, consts.LinkStatusLinked, listing.LinkStatus)
	require.Equal(t, arg.Product.Title, listing.Product.Title)
	require.Equal(t, arg.Product.Description, listing.Product.Description)
	require.Equal(t, len(arg.Product.Media), len(listing.Product.Media))
	require.Equal(t, len(product.Variants), len(listing.Product.Variants))
	require.Equal(t, len(product.Variants), len(listing.Relations))
	require.Equal(t, len(listing.Product.Variants), len(listing.Relations))
	for i := range listing.Product.Variants {
		require.Equal(t, product.Variants[i].Sku, listing.Product.Variants[i].Sku)
		require.Equal(t, product.Variants[i].Title, listing.Product.Variants[i].Title)
		require.Equal(t, product.Variants[i].Price.Amount, listing.Product.Variants[i].Price.Amount)
		require.Equal(t, product.Variants[i].CompareAtPrice.Amount, listing.Product.Variants[i].CompareAtPrice.Amount)
	}
	for i := range listing.Relations {
		require.Equal(t, listing.Product.Variants[i].ID, listing.Relations[i].ProductListingVariantID)
		require.Equal(t, consts.SyncStatusUnsync, listing.Relations[i].SyncStatus)
		require.Equal(t, consts.LinkStatusLinked, listing.Relations[i].LinkStatus)
	}

	lastAuditVersion, err := s.latestAuditVersion(ctx, listing.ID)
	require.NoError(t, err)
	require.NotEmpty(t, lastAuditVersion)
	require.Equal(t, listing.ID, lastAuditVersion.ProductListing.ID)
	require.Equal(t, arg.Product.Title, lastAuditVersion.ProductListing.Product.Title)
	require.Equal(t, arg.Product.Description, lastAuditVersion.ProductListing.Product.Description)
	require.Equal(t, len(product.Variants), len(lastAuditVersion.ProductListing.Product.Variants))
	require.Equal(t, len(product.Variants), len(lastAuditVersion.ProductListing.Relations))
	for i := range lastAuditVersion.ProductListing.Product.Variants {
		require.Equal(t, product.Variants[i].Sku, lastAuditVersion.ProductListing.Product.Variants[i].Sku)
		require.Equal(t, product.Variants[i].Title, lastAuditVersion.ProductListing.Product.Variants[i].Title)
		require.Equal(t, product.Variants[i].Price.Amount, lastAuditVersion.ProductListing.Product.Variants[i].Price.Amount)
		require.Equal(t, product.Variants[i].CompareAtPrice.Amount, lastAuditVersion.ProductListing.Product.Variants[i].CompareAtPrice.Amount)
	}
}

func Test_serviceImpl_ListingUpdate_match_unsync_auto_sync_variant_delete_variant(t *testing.T) {
	basePath := "./testdata/listing_update/match_unsync/auto_sync_variant/delete_variant"
	s := initTestListingUpdateService(t, true, basePath)
	product := readUpdateEventProductsCenterProduct(t, basePath+"/product.json")
	listing := createUpdateEventTestBaseListing(t, s, basePath+"/base_listing.json")
	arg := readUpdateEventArg(t, basePath+"/arg.json")
	ctx := context.Background()
	listing, err := s.Update(ctx, listing.ID, arg)
	require.NoError(t, err)
	require.NotEmpty(t, listing.ID)
	require.Equal(t, consts.SyncStatusUnsync, listing.SyncStatus)
	require.Equal(t, consts.LinkStatusLinked, listing.LinkStatus)
	require.Equal(t, arg.Product.Title, listing.Product.Title)
	require.Equal(t, arg.Product.Description, listing.Product.Description)
	require.Equal(t, len(arg.Product.Media), len(listing.Product.Media))
	require.Equal(t, len(product.Variants), len(listing.Product.Variants))
	require.Equal(t, len(product.Variants), len(listing.Relations))
	require.Equal(t, len(listing.Product.Variants), len(listing.Relations))
	for i := range listing.Product.Variants {
		require.Equal(t, product.Variants[i].Sku, listing.Product.Variants[i].Sku)
		require.Equal(t, product.Variants[i].Title, listing.Product.Variants[i].Title)
		require.Equal(t, product.Variants[i].Price.Amount, listing.Product.Variants[i].Price.Amount)
		require.Equal(t, product.Variants[i].CompareAtPrice.Amount, listing.Product.Variants[i].CompareAtPrice.Amount)
	}
	for i := range listing.Relations {
		require.Equal(t, listing.Product.Variants[i].ID, listing.Relations[i].ProductListingVariantID)
		require.Equal(t, consts.SyncStatusUnsync, listing.Relations[i].SyncStatus)
		require.Equal(t, consts.LinkStatusLinked, listing.Relations[i].LinkStatus)
	}

	lastAuditVersion, err := s.latestAuditVersion(ctx, listing.ID)
	require.NoError(t, err)
	require.NotEmpty(t, lastAuditVersion)
	require.Equal(t, listing.ID, lastAuditVersion.ProductListing.ID)
	require.Equal(t, arg.Product.Title, lastAuditVersion.ProductListing.Product.Title)
	require.Equal(t, arg.Product.Description, lastAuditVersion.ProductListing.Product.Description)
	require.Equal(t, len(product.Variants), len(lastAuditVersion.ProductListing.Product.Variants))
	require.Equal(t, len(product.Variants), len(lastAuditVersion.ProductListing.Relations))
	for i := range lastAuditVersion.ProductListing.Product.Variants {
		require.Equal(t, product.Variants[i].Sku, lastAuditVersion.ProductListing.Product.Variants[i].Sku)
		require.Equal(t, product.Variants[i].Title, lastAuditVersion.ProductListing.Product.Variants[i].Title)
		require.Equal(t, product.Variants[i].Price.Amount, lastAuditVersion.ProductListing.Product.Variants[i].Price.Amount)
		require.Equal(t, product.Variants[i].CompareAtPrice.Amount, lastAuditVersion.ProductListing.Product.Variants[i].CompareAtPrice.Amount)
	}
}

func Test_serviceImpl_ListingUpdate_match_unsync_auto_sync_variant_update_variant(t *testing.T) {
	basePath := "./testdata/listing_update/match_unsync/auto_sync_variant/update_variant"
	s := initTestListingUpdateService(t, true, basePath)
	product := readUpdateEventProductsCenterProduct(t, basePath+"/product.json")
	listing := createUpdateEventTestBaseListing(t, s, basePath+"/base_listing.json")
	arg := readUpdateEventArg(t, basePath+"/arg.json")
	ctx := context.Background()
	listing, err := s.Update(ctx, listing.ID, arg)
	require.NoError(t, err)
	require.NotEmpty(t, listing.ID)
	require.Equal(t, consts.SyncStatusUnsync, listing.SyncStatus)
	require.Equal(t, consts.LinkStatusLinked, listing.LinkStatus)
	require.Equal(t, arg.Product.Title, listing.Product.Title)
	require.Equal(t, arg.Product.Description, listing.Product.Description)
	require.Equal(t, len(arg.Product.Media), len(listing.Product.Media))
	require.Equal(t, len(product.Variants), len(listing.Product.Variants))
	require.Equal(t, len(product.Variants), len(listing.Relations))
	require.Equal(t, len(listing.Product.Variants), len(listing.Relations))
	for i := range listing.Product.Variants {
		require.Equal(t, product.Variants[i].Sku, listing.Product.Variants[i].Sku)
		// require.Equal(t, product.Variants[i].Title, listing.Product.Variants[i].Title)
		require.Equal(t, product.Variants[i].Price.Amount, listing.Product.Variants[i].Price.Amount)
		require.Equal(t, product.Variants[i].CompareAtPrice.Amount, listing.Product.Variants[i].CompareAtPrice.Amount)
	}
	for i := range listing.Relations {
		require.Equal(t, listing.Product.Variants[i].ID, listing.Relations[i].ProductListingVariantID)
		require.Equal(t, consts.SyncStatusUnsync, listing.Relations[i].SyncStatus)
		require.Equal(t, consts.LinkStatusLinked, listing.Relations[i].LinkStatus)
	}

	lastAuditVersion, err := s.latestAuditVersion(ctx, listing.ID)
	require.NoError(t, err)
	require.NotEmpty(t, lastAuditVersion)
	require.Equal(t, listing.ID, lastAuditVersion.ProductListing.ID)
	require.Equal(t, arg.Product.Title, lastAuditVersion.ProductListing.Product.Title)
	require.Equal(t, arg.Product.Description, lastAuditVersion.ProductListing.Product.Description)
	require.Equal(t, len(product.Variants), len(lastAuditVersion.ProductListing.Product.Variants))
	require.Equal(t, len(product.Variants), len(lastAuditVersion.ProductListing.Relations))
	for i := range lastAuditVersion.ProductListing.Product.Variants {
		require.Equal(t, product.Variants[i].Sku, lastAuditVersion.ProductListing.Product.Variants[i].Sku)
		// require.Equal(t, product.Variants[i].Title, lastAuditVersion.ProductListing.Product.Variants[i].Title)
		require.Equal(t, product.Variants[i].Price.Amount, lastAuditVersion.ProductListing.Product.Variants[i].Price.Amount)
		require.Equal(t, product.Variants[i].CompareAtPrice.Amount, lastAuditVersion.ProductListing.Product.Variants[i].CompareAtPrice.Amount)
	}
}

func Test_serviceImpl_ListingUpdate_match_unsync_auto_sync_variant_modify_product(t *testing.T) {
	basePath := "./testdata/listing_update/match_unsync/auto_sync_variant/modify_product"
	product := readUpdateEventProductsCenterProduct(t, basePath+"/product.json")
	s := initTestListingUpdateService(t, true, basePath)
	listing := createUpdateEventTestBaseListing(t, s, basePath+"/base_listing.json")
	arg := readUpdateEventArg(t, basePath+"/arg.json")
	ctx := context.Background()
	listing, err := s.Update(ctx, listing.ID, arg)
	require.NoError(t, err)
	require.NotEmpty(t, listing.ID)
	require.Equal(t, consts.SyncStatusUnsync, listing.SyncStatus)
	require.Equal(t, consts.LinkStatusLinked, listing.LinkStatus)
	require.Equal(t, arg.Product.Title, listing.Product.Title)
	require.Equal(t, arg.Product.Description, listing.Product.Description)
	require.Equal(t, len(arg.Product.Media), len(listing.Product.Media))
	require.Equal(t, len(product.Variants), len(listing.Product.Variants))
	require.Equal(t, len(product.Variants), len(listing.Relations))
	require.Equal(t, len(listing.Product.Variants), len(listing.Relations))
	for i := range listing.Product.Variants {
		require.Equal(t, product.Variants[i].Sku, listing.Product.Variants[i].Sku)
		require.Equal(t, product.Variants[i].Title, listing.Product.Variants[i].Title)
		require.Equal(t, product.Variants[i].Price.Amount, listing.Product.Variants[i].Price.Amount)
		require.Equal(t, product.Variants[i].CompareAtPrice.Amount, listing.Product.Variants[i].CompareAtPrice.Amount)
	}
	for i := range listing.Relations {
		require.Equal(t, listing.Product.Variants[i].ID, listing.Relations[i].ProductListingVariantID)
		require.Equal(t, consts.SyncStatusUnsync, listing.Relations[i].SyncStatus)
		require.Equal(t, consts.LinkStatusLinked, listing.Relations[i].LinkStatus)
	}

	lastAuditVersion, err := s.latestAuditVersion(ctx, listing.ID)
	require.NoError(t, err)
	require.NotEmpty(t, lastAuditVersion)
	require.Equal(t, listing.ID, lastAuditVersion.ProductListing.ID)
	require.Equal(t, arg.Product.Title, lastAuditVersion.ProductListing.Product.Title)
	require.Equal(t, arg.Product.Description, lastAuditVersion.ProductListing.Product.Description)
	require.Equal(t, len(arg.Product.Media), len(lastAuditVersion.ProductListing.Product.Media))
	require.Equal(t, len(product.Variants), len(lastAuditVersion.ProductListing.Product.Variants))
	require.Equal(t, len(product.Variants), len(lastAuditVersion.ProductListing.Relations))
	for i := range lastAuditVersion.ProductListing.Product.Variants {
		require.Equal(t, product.Variants[i].Sku, lastAuditVersion.ProductListing.Product.Variants[i].Sku)
		require.Equal(t, product.Variants[i].Title, lastAuditVersion.ProductListing.Product.Variants[i].Title)
		require.Equal(t, product.Variants[i].Price.Amount, lastAuditVersion.ProductListing.Product.Variants[i].Price.Amount)
		require.Equal(t, product.Variants[i].CompareAtPrice.Amount, lastAuditVersion.ProductListing.Product.Variants[i].CompareAtPrice.Amount)
	}
}

func Test_serviceImpl_ListingUpdate_match_unsync_auto_sync_all_add_variant(t *testing.T) {
	basePath := "./testdata/listing_update/match_unsync/auto_sync_all/add_variant"
	s := initTestListingUpdateService(t, true, basePath)
	product := readUpdateEventProductsCenterProduct(t, basePath+"/product.json")
	listing := createUpdateEventTestBaseListing(t, s, basePath+"/base_listing.json")
	arg := readUpdateEventArg(t, basePath+"/arg.json")
	ctx := context.Background()
	listing, err := s.Update(ctx, listing.ID, arg)
	require.NoError(t, err)
	require.NotEmpty(t, listing.ID)
	require.Equal(t, consts.SyncStatusUnsync, listing.SyncStatus)
	require.Equal(t, consts.LinkStatusLinked, listing.LinkStatus)
	require.Equal(t, product.Title, listing.Product.Title)
	require.Equal(t, product.Description, listing.Product.Description)
	require.Equal(t, len(product.Media), len(listing.Product.Media))
	require.Equal(t, len(product.Variants), len(listing.Product.Variants))
	require.Equal(t, len(product.Variants), len(listing.Relations))
	require.Equal(t, len(listing.Product.Variants), len(listing.Relations))
	for i := range listing.Product.Variants {
		require.Equal(t, product.Variants[i].Sku, listing.Product.Variants[i].Sku)
		require.Equal(t, product.Variants[i].Title, listing.Product.Variants[i].Title)
		require.Equal(t, product.Variants[i].Price.Amount, listing.Product.Variants[i].Price.Amount)
		require.Equal(t, product.Variants[i].CompareAtPrice.Amount, listing.Product.Variants[i].CompareAtPrice.Amount)
	}
	for i := range listing.Relations {
		require.Equal(t, listing.Product.Variants[i].ID, listing.Relations[i].ProductListingVariantID)
		require.Equal(t, consts.SyncStatusUnsync, listing.Relations[i].SyncStatus)
		require.Equal(t, consts.LinkStatusLinked, listing.Relations[i].LinkStatus)
	}

	lastAuditVersion, err := s.latestAuditVersion(ctx, listing.ID)
	require.NoError(t, err)
	require.NotEmpty(t, lastAuditVersion)
	require.Equal(t, listing.ID, lastAuditVersion.ProductListing.ID)
	require.Equal(t, product.Title, lastAuditVersion.ProductListing.Product.Title)
	require.Equal(t, product.Description, lastAuditVersion.ProductListing.Product.Description)
	require.Equal(t, len(product.Variants), len(lastAuditVersion.ProductListing.Product.Variants))
	require.Equal(t, len(product.Variants), len(lastAuditVersion.ProductListing.Relations))
	for i := range lastAuditVersion.ProductListing.Product.Variants {
		require.Equal(t, product.Variants[i].Sku, lastAuditVersion.ProductListing.Product.Variants[i].Sku)
		require.Equal(t, product.Variants[i].Title, lastAuditVersion.ProductListing.Product.Variants[i].Title)
		require.Equal(t, product.Variants[i].Price.Amount, lastAuditVersion.ProductListing.Product.Variants[i].Price.Amount)
		require.Equal(t, product.Variants[i].CompareAtPrice.Amount, lastAuditVersion.ProductListing.Product.Variants[i].CompareAtPrice.Amount)
	}
}

func Test_serviceImpl_ListingUpdate_match_unsync_auto_sync_all_delete_variant(t *testing.T) {
	basePath := "./testdata/listing_update/match_unsync/auto_sync_all/delete_variant"
	s := initTestListingUpdateService(t, true, basePath)
	product := readUpdateEventProductsCenterProduct(t, basePath+"/product.json")
	listing := createUpdateEventTestBaseListing(t, s, basePath+"/base_listing.json")
	arg := readUpdateEventArg(t, basePath+"/arg.json")
	ctx := context.Background()
	listing, err := s.Update(ctx, listing.ID, arg)
	require.NoError(t, err)
	require.NotEmpty(t, listing.ID)
	require.Equal(t, consts.SyncStatusUnsync, listing.SyncStatus)
	require.Equal(t, consts.LinkStatusLinked, listing.LinkStatus)
	require.Equal(t, product.Title, listing.Product.Title)
	require.Equal(t, product.Description, listing.Product.Description)
	require.Equal(t, len(product.Media), len(listing.Product.Media))
	require.Equal(t, len(product.Variants), len(listing.Product.Variants))
	require.Equal(t, len(product.Variants), len(listing.Relations))
	require.Equal(t, len(listing.Product.Variants), len(listing.Relations))
	for i := range listing.Product.Variants {
		require.Equal(t, product.Variants[i].Sku, listing.Product.Variants[i].Sku)
		require.Equal(t, product.Variants[i].Title, listing.Product.Variants[i].Title)
		require.Equal(t, product.Variants[i].Price.Amount, listing.Product.Variants[i].Price.Amount)
		require.Equal(t, product.Variants[i].CompareAtPrice.Amount, listing.Product.Variants[i].CompareAtPrice.Amount)
	}
	for i := range listing.Relations {
		require.Equal(t, listing.Product.Variants[i].ID, listing.Relations[i].ProductListingVariantID)
		require.Equal(t, consts.SyncStatusUnsync, listing.Relations[i].SyncStatus)
		require.Equal(t, consts.LinkStatusLinked, listing.Relations[i].LinkStatus)
	}

	lastAuditVersion, err := s.latestAuditVersion(ctx, listing.ID)
	require.NoError(t, err)
	require.NotEmpty(t, lastAuditVersion)
	require.Equal(t, listing.ID, lastAuditVersion.ProductListing.ID)
	require.Equal(t, product.Title, lastAuditVersion.ProductListing.Product.Title)
	require.Equal(t, product.Description, lastAuditVersion.ProductListing.Product.Description)
	require.Equal(t, len(product.Variants), len(lastAuditVersion.ProductListing.Product.Variants))
	require.Equal(t, len(product.Variants), len(lastAuditVersion.ProductListing.Relations))
	for i := range lastAuditVersion.ProductListing.Product.Variants {
		require.Equal(t, product.Variants[i].Sku, lastAuditVersion.ProductListing.Product.Variants[i].Sku)
		require.Equal(t, product.Variants[i].Title, lastAuditVersion.ProductListing.Product.Variants[i].Title)
		require.Equal(t, product.Variants[i].Price.Amount, lastAuditVersion.ProductListing.Product.Variants[i].Price.Amount)
		require.Equal(t, product.Variants[i].CompareAtPrice.Amount, lastAuditVersion.ProductListing.Product.Variants[i].CompareAtPrice.Amount)
	}
}

func Test_serviceImpl_ListingUpdate_match_unsync_auto_sync_all_update_variant(t *testing.T) {
	basePath := "./testdata/listing_update/match_unsync/auto_sync_all/update_variant"
	s := initTestListingUpdateService(t, true, basePath)
	product := readUpdateEventProductsCenterProduct(t, basePath+"/product.json")
	listing := createUpdateEventTestBaseListing(t, s, basePath+"/base_listing.json")
	arg := readUpdateEventArg(t, basePath+"/arg.json")
	ctx := context.Background()
	listing, err := s.Update(ctx, listing.ID, arg)
	require.NoError(t, err)
	require.NotEmpty(t, listing.ID)
	require.Equal(t, consts.SyncStatusUnsync, listing.SyncStatus)
	require.Equal(t, consts.LinkStatusLinked, listing.LinkStatus)
	require.Equal(t, product.Title, listing.Product.Title)
	require.Equal(t, product.Description, listing.Product.Description)
	require.Equal(t, len(product.Media), len(listing.Product.Media))
	require.Equal(t, len(product.Variants), len(listing.Product.Variants))
	require.Equal(t, len(product.Variants), len(listing.Relations))
	require.Equal(t, len(listing.Product.Variants), len(listing.Relations))
	for i := range listing.Product.Variants {
		require.Equal(t, product.Variants[i].Sku, listing.Product.Variants[i].Sku)
		// require.Equal(t, product.Variants[i].Title, listing.Product.Variants[i].Title)
		require.Equal(t, product.Variants[i].Price.Amount, listing.Product.Variants[i].Price.Amount)
		require.Equal(t, product.Variants[i].CompareAtPrice.Amount, listing.Product.Variants[i].CompareAtPrice.Amount)
	}
	for i := range listing.Relations {
		require.Equal(t, listing.Product.Variants[i].ID, listing.Relations[i].ProductListingVariantID)
		require.Equal(t, consts.SyncStatusUnsync, listing.Relations[i].SyncStatus)
		require.Equal(t, consts.LinkStatusLinked, listing.Relations[i].LinkStatus)
	}

	lastAuditVersion, err := s.latestAuditVersion(ctx, listing.ID)
	require.NoError(t, err)
	require.NotEmpty(t, lastAuditVersion)
	require.Equal(t, listing.ID, lastAuditVersion.ProductListing.ID)
	require.Equal(t, product.Title, lastAuditVersion.ProductListing.Product.Title)
	require.Equal(t, product.Description, lastAuditVersion.ProductListing.Product.Description)
	require.Equal(t, len(product.Variants), len(lastAuditVersion.ProductListing.Product.Variants))
	require.Equal(t, len(product.Variants), len(lastAuditVersion.ProductListing.Relations))
	for i := range lastAuditVersion.ProductListing.Product.Variants {
		require.Equal(t, product.Variants[i].Sku, lastAuditVersion.ProductListing.Product.Variants[i].Sku)
		// require.Equal(t, product.Variants[i].Title, lastAuditVersion.ProductListing.Product.Variants[i].Title)
		require.Equal(t, product.Variants[i].Price.Amount, lastAuditVersion.ProductListing.Product.Variants[i].Price.Amount)
		require.Equal(t, product.Variants[i].CompareAtPrice.Amount, lastAuditVersion.ProductListing.Product.Variants[i].CompareAtPrice.Amount)
	}
}

func Test_serviceImpl_ListingUpdate_match_unsync_auto_sync_all_modify_product(t *testing.T) {
	basePath := "./testdata/listing_update/match_unsync/auto_sync_all/modify_product"
	product := readUpdateEventProductsCenterProduct(t, basePath+"/product.json")
	s := initTestListingUpdateService(t, true, basePath)
	listing := createUpdateEventTestBaseListing(t, s, basePath+"/base_listing.json")
	arg := readUpdateEventArg(t, basePath+"/arg.json")
	ctx := context.Background()
	listing, err := s.Update(ctx, listing.ID, arg)
	require.NoError(t, err)
	require.NotEmpty(t, listing.ID)
	require.Equal(t, consts.SyncStatusUnsync, listing.SyncStatus)
	require.Equal(t, consts.LinkStatusLinked, listing.LinkStatus)
	require.Equal(t, product.Title, listing.Product.Title)
	require.Equal(t, product.Description, listing.Product.Description)
	require.Equal(t, len(product.Media), len(listing.Product.Media))
	require.Equal(t, len(product.Variants), len(listing.Product.Variants))
	require.Equal(t, len(product.Variants), len(listing.Relations))
	require.Equal(t, len(listing.Product.Variants), len(listing.Relations))
	for i := range listing.Product.Variants {
		require.Equal(t, product.Variants[i].Sku, listing.Product.Variants[i].Sku)
		require.Equal(t, product.Variants[i].Title, listing.Product.Variants[i].Title)
		require.Equal(t, product.Variants[i].Price.Amount, listing.Product.Variants[i].Price.Amount)
		require.Equal(t, product.Variants[i].CompareAtPrice.Amount, listing.Product.Variants[i].CompareAtPrice.Amount)
	}
	for i := range listing.Relations {
		require.Equal(t, listing.Product.Variants[i].ID, listing.Relations[i].ProductListingVariantID)
		require.Equal(t, consts.SyncStatusUnsync, listing.Relations[i].SyncStatus)
		require.Equal(t, consts.LinkStatusLinked, listing.Relations[i].LinkStatus)
	}

	lastAuditVersion, err := s.latestAuditVersion(ctx, listing.ID)
	require.NoError(t, err)
	require.NotEmpty(t, lastAuditVersion)
	require.Equal(t, listing.ID, lastAuditVersion.ProductListing.ID)
	require.Equal(t, product.Title, lastAuditVersion.ProductListing.Product.Title)
	require.Equal(t, product.Description, lastAuditVersion.ProductListing.Product.Description)
	require.Equal(t, len(product.Media), len(lastAuditVersion.ProductListing.Product.Media))
	require.Equal(t, len(product.Variants), len(lastAuditVersion.ProductListing.Product.Variants))
	require.Equal(t, len(product.Variants), len(lastAuditVersion.ProductListing.Relations))
	for i := range lastAuditVersion.ProductListing.Product.Variants {
		require.Equal(t, product.Variants[i].Sku, lastAuditVersion.ProductListing.Product.Variants[i].Sku)
		require.Equal(t, product.Variants[i].Title, lastAuditVersion.ProductListing.Product.Variants[i].Title)
		require.Equal(t, product.Variants[i].Price.Amount, lastAuditVersion.ProductListing.Product.Variants[i].Price.Amount)
		require.Equal(t, product.Variants[i].CompareAtPrice.Amount, lastAuditVersion.ProductListing.Product.Variants[i].CompareAtPrice.Amount)
	}
}

func Test_serviceImpl_ListingUpdate_match_synced_not_auto_sync_add_variant(t *testing.T) {
	basePath := "./testdata/listing_update/match_synced/not_auto_sync/add_variant"
	s := initTestListingUpdateService(t, true, basePath)
	listing := createUpdateEventTestBaseListing(t, s, basePath+"/base_listing.json")
	lastListing := listing
	arg := readUpdateEventArg(t, basePath+"/arg.json")
	ctx := context.Background()
	listing, err := s.Update(ctx, listing.ID, arg)
	require.NoError(t, err)
	require.NotEmpty(t, listing.ID)
	require.Equal(t, consts.SyncStatusPartialSynced, listing.SyncStatus)
	require.Equal(t, consts.LinkStatusPartialLinked, listing.LinkStatus)
	require.Equal(t, lastListing.Product.Title, listing.Product.Title)
	require.Equal(t, lastListing.Product.Description, listing.Product.Description)
	require.Equal(t, len(lastListing.Product.Media), len(listing.Product.Media))
	require.Equal(t, len(arg.Product.Variants), len(listing.Product.Variants))
	require.Equal(t, len(arg.Relations), len(listing.Relations))
	require.Equal(t, len(listing.Product.Variants), len(listing.Relations))
	for i := range listing.Product.Variants {
		require.Equal(t, arg.Product.Variants[i].Sku, listing.Product.Variants[i].Sku)
		require.Equal(t, arg.Product.Variants[i].Title, listing.Product.Variants[i].Title)
		require.Equal(t, arg.Product.Variants[i].Price.Amount, listing.Product.Variants[i].Price.Amount)
		require.Equal(t, arg.Product.Variants[i].CompareAtPrice.Amount, listing.Product.Variants[i].CompareAtPrice.Amount)
	}
	for i := range listing.Relations {
		require.Equal(t, listing.Product.Variants[i].ID, listing.Relations[i].ProductListingVariantID)
		if i == 2 {
			require.Equal(t, consts.LinkStatusUnlink, listing.Relations[i].LinkStatus)
			require.Equal(t, consts.SyncStatusUnsync, listing.Relations[i].SyncStatus)
		} else {
			require.Equal(t, consts.LinkStatusLinked, listing.Relations[i].LinkStatus)
			require.Equal(t, consts.SyncStatusSynced, listing.Relations[i].SyncStatus)
		}
	}

	lastAuditVersion, err := s.latestAuditVersion(ctx, listing.ID)
	require.NoError(t, err)
	require.NotEmpty(t, lastAuditVersion)
	require.Equal(t, listing.ID, lastAuditVersion.ProductListing.ID)
	require.Equal(t, arg.Product.Title, lastAuditVersion.ProductListing.Product.Title)
	require.Equal(t, arg.Product.Description, lastAuditVersion.ProductListing.Product.Description)
	require.Equal(t, len(arg.Product.Media), len(lastAuditVersion.ProductListing.Product.Media))
	require.Equal(t, len(arg.Product.Variants), len(lastAuditVersion.ProductListing.Product.Variants))
	require.Equal(t, len(arg.Relations), len(lastAuditVersion.ProductListing.Relations))
	for i := range lastAuditVersion.ProductListing.Product.Variants {
		require.Equal(t, arg.Product.Variants[i].Sku, lastAuditVersion.ProductListing.Product.Variants[i].Sku)
		require.Equal(t, arg.Product.Variants[i].Title, lastAuditVersion.ProductListing.Product.Variants[i].Title)
		require.Equal(t, arg.Product.Variants[i].Price.Amount, lastAuditVersion.ProductListing.Product.Variants[i].Price.Amount)
		require.Equal(t, arg.Product.Variants[i].CompareAtPrice.Amount, lastAuditVersion.ProductListing.Product.Variants[i].CompareAtPrice.Amount)
	}
}

func Test_serviceImpl_ListingUpdate_match_synced_not_auto_sync_delete_variant(t *testing.T) {
	basePath := "./testdata/listing_update/match_synced/not_auto_sync/delete_variant"
	s := initTestListingUpdateService(t, true, basePath)
	listing := createUpdateEventTestBaseListing(t, s, basePath+"/base_listing.json")
	lastListing := listing
	arg := readUpdateEventArg(t, basePath+"/arg.json")
	ctx := context.Background()
	listing, err := s.Update(ctx, listing.ID, arg)
	require.NoError(t, err)
	require.NotEmpty(t, listing.ID)
	require.Equal(t, consts.SyncStatusSynced, listing.SyncStatus)
	require.Equal(t, consts.LinkStatusLinked, listing.LinkStatus)
	require.Equal(t, lastListing.Product.Title, listing.Product.Title)
	require.Equal(t, lastListing.Product.Description, listing.Product.Description)
	require.Equal(t, len(lastListing.Product.Media), len(listing.Product.Media))
	require.Equal(t, len(lastListing.Product.Variants), len(listing.Product.Variants))
	require.Equal(t, len(lastListing.Relations), len(listing.Relations))
	require.Equal(t, len(listing.Product.Variants), len(listing.Relations))
	for i := range listing.Product.Variants {
		require.Equal(t, lastListing.Product.Variants[i].Sku, listing.Product.Variants[i].Sku)
		require.Equal(t, lastListing.Product.Variants[i].Title, listing.Product.Variants[i].Title)
		require.Equal(t, lastListing.Product.Variants[i].Price.Amount, listing.Product.Variants[i].Price.Amount)
		require.Equal(t, lastListing.Product.Variants[i].CompareAtPrice.Amount, listing.Product.Variants[i].CompareAtPrice.Amount)
	}
	for i := range listing.Relations {
		require.Equal(t, listing.Product.Variants[i].ID, listing.Relations[i].ProductListingVariantID)
		require.Equal(t, consts.SyncStatusSynced, listing.Relations[i].SyncStatus)
		require.Equal(t, consts.LinkStatusLinked, listing.Relations[i].LinkStatus)
	}

	lastAuditVersion, err := s.latestAuditVersion(ctx, listing.ID)
	require.NoError(t, err)
	require.NotEmpty(t, lastAuditVersion)
	require.Equal(t, listing.ID, lastAuditVersion.ProductListing.ID)
	require.Equal(t, arg.Product.Title, lastAuditVersion.ProductListing.Product.Title)
	require.Equal(t, arg.Product.Description, lastAuditVersion.ProductListing.Product.Description)
	require.Equal(t, len(arg.Product.Media), len(lastAuditVersion.ProductListing.Product.Media))
	require.Equal(t, len(arg.Product.Variants), len(lastAuditVersion.ProductListing.Product.Variants))
	require.Equal(t, len(arg.Relations), len(lastAuditVersion.ProductListing.Relations))
	for i := range lastAuditVersion.ProductListing.Product.Variants {
		require.Equal(t, arg.Product.Variants[i].Sku, lastAuditVersion.ProductListing.Product.Variants[i].Sku)
		require.Equal(t, arg.Product.Variants[i].Title, lastAuditVersion.ProductListing.Product.Variants[i].Title)
		require.Equal(t, arg.Product.Variants[i].Price.Amount, lastAuditVersion.ProductListing.Product.Variants[i].Price.Amount)
		require.Equal(t, arg.Product.Variants[i].CompareAtPrice.Amount, lastAuditVersion.ProductListing.Product.Variants[i].CompareAtPrice.Amount)
	}
}

func Test_serviceImpl_ListingUpdate_match_synced_not_auto_sync_update_variant(t *testing.T) {
	basePath := "./testdata/listing_update/match_synced/not_auto_sync/update_variant"
	s := initTestListingUpdateService(t, true, basePath)
	listing := createUpdateEventTestBaseListing(t, s, basePath+"/base_listing.json")
	lastListing := listing
	arg := readUpdateEventArg(t, basePath+"/arg.json")
	ctx := context.Background()
	listing, err := s.Update(ctx, listing.ID, arg)
	require.NoError(t, err)
	require.NotEmpty(t, listing.ID)
	require.Equal(t, consts.SyncStatusSynced, listing.SyncStatus)
	require.Equal(t, consts.LinkStatusLinked, listing.LinkStatus)
	require.Equal(t, lastListing.Product.Title, listing.Product.Title)
	require.Equal(t, lastListing.Product.Description, listing.Product.Description)
	require.Equal(t, len(lastListing.Product.Media), len(listing.Product.Media))
	require.Equal(t, len(lastListing.Product.Variants), len(listing.Product.Variants))
	require.Equal(t, len(lastListing.Relations), len(listing.Relations))
	require.Equal(t, len(listing.Product.Variants), len(listing.Relations))
	for i := range listing.Product.Variants {
		require.Equal(t, lastListing.Product.Variants[i].Sku, listing.Product.Variants[i].Sku)
		require.Equal(t, lastListing.Product.Variants[i].Title, listing.Product.Variants[i].Title)
		require.Equal(t, lastListing.Product.Variants[i].Price.Amount, listing.Product.Variants[i].Price.Amount)
		require.Equal(t, lastListing.Product.Variants[i].CompareAtPrice.Amount, listing.Product.Variants[i].CompareAtPrice.Amount)
	}
	for i := range listing.Relations {
		require.Equal(t, listing.Product.Variants[i].ID, listing.Relations[i].ProductListingVariantID)
		require.Equal(t, consts.SyncStatusSynced, listing.Relations[i].SyncStatus)
		require.Equal(t, consts.LinkStatusLinked, listing.Relations[i].LinkStatus)
	}

	lastAuditVersion, err := s.latestAuditVersion(ctx, listing.ID)
	require.NoError(t, err)
	require.NotEmpty(t, lastAuditVersion)
	require.Equal(t, listing.ID, lastAuditVersion.ProductListing.ID)
	require.Equal(t, arg.Product.Title, lastAuditVersion.ProductListing.Product.Title)
	require.Equal(t, arg.Product.Description, lastAuditVersion.ProductListing.Product.Description)
	require.Equal(t, len(arg.Product.Media), len(lastAuditVersion.ProductListing.Product.Media))
	require.Equal(t, len(arg.Product.Variants), len(lastAuditVersion.ProductListing.Product.Variants))
	require.Equal(t, len(arg.Relations), len(lastAuditVersion.ProductListing.Relations))
	for i := range lastAuditVersion.ProductListing.Product.Variants {
		require.Equal(t, arg.Product.Variants[i].Sku, lastAuditVersion.ProductListing.Product.Variants[i].Sku)
		require.Equal(t, arg.Product.Variants[i].Title, lastAuditVersion.ProductListing.Product.Variants[i].Title)
		require.Equal(t, arg.Product.Variants[i].Price.Amount, lastAuditVersion.ProductListing.Product.Variants[i].Price.Amount)
		require.Equal(t, arg.Product.Variants[i].CompareAtPrice.Amount, lastAuditVersion.ProductListing.Product.Variants[i].CompareAtPrice.Amount)
	}
}

func Test_serviceImpl_ListingUpdate_match_synced_not_auto_sync_modify_product(t *testing.T) {
	basePath := "./testdata/listing_update/match_synced/not_auto_sync/modify_product"
	s := initTestListingUpdateService(t, true, basePath)
	listing := createUpdateEventTestBaseListing(t, s, basePath+"/base_listing.json")
	lastListing := listing
	arg := readUpdateEventArg(t, basePath+"/arg.json")
	ctx := context.Background()
	listing, err := s.Update(ctx, listing.ID, arg)
	require.NoError(t, err)
	require.NotEmpty(t, listing.ID)
	require.Equal(t, consts.SyncStatusSynced, listing.SyncStatus)
	require.Equal(t, consts.LinkStatusLinked, listing.LinkStatus)
	require.Equal(t, lastListing.Product.Title, listing.Product.Title)
	require.Equal(t, lastListing.Product.Description, listing.Product.Description)
	require.Equal(t, len(lastListing.Product.Media), len(listing.Product.Media))
	require.Equal(t, len(lastListing.Product.Variants), len(listing.Product.Variants))
	require.Equal(t, len(lastListing.Relations), len(listing.Relations))
	require.Equal(t, len(listing.Product.Variants), len(listing.Relations))
	for i := range listing.Product.Variants {
		require.Equal(t, lastListing.Product.Variants[i].Sku, listing.Product.Variants[i].Sku)
		require.Equal(t, lastListing.Product.Variants[i].Title, listing.Product.Variants[i].Title)
		require.Equal(t, lastListing.Product.Variants[i].Price.Amount, listing.Product.Variants[i].Price.Amount)
		require.Equal(t, lastListing.Product.Variants[i].CompareAtPrice.Amount, listing.Product.Variants[i].CompareAtPrice.Amount)
	}
	for i := range listing.Relations {
		require.Equal(t, listing.Product.Variants[i].ID, listing.Relations[i].ProductListingVariantID)
		require.Equal(t, consts.SyncStatusSynced, listing.Relations[i].SyncStatus)
		require.Equal(t, consts.LinkStatusLinked, listing.Relations[i].LinkStatus)
	}

	lastAuditVersion, err := s.latestAuditVersion(ctx, listing.ID)
	require.NoError(t, err)
	require.NotEmpty(t, lastAuditVersion)
	require.Equal(t, listing.ID, lastAuditVersion.ProductListing.ID)
	require.Equal(t, arg.Product.Title, lastAuditVersion.ProductListing.Product.Title)
	require.Equal(t, arg.Product.Description, lastAuditVersion.ProductListing.Product.Description)
	require.Equal(t, len(arg.Product.Media), len(lastAuditVersion.ProductListing.Product.Media))
	require.Equal(t, len(arg.Product.Variants), len(lastAuditVersion.ProductListing.Product.Variants))
	require.Equal(t, len(arg.Relations), len(lastAuditVersion.ProductListing.Relations))
	for i := range lastAuditVersion.ProductListing.Product.Variants {
		require.Equal(t, arg.Product.Variants[i].Sku, lastAuditVersion.ProductListing.Product.Variants[i].Sku)
		require.Equal(t, arg.Product.Variants[i].Title, lastAuditVersion.ProductListing.Product.Variants[i].Title)
		require.Equal(t, arg.Product.Variants[i].Price.Amount, lastAuditVersion.ProductListing.Product.Variants[i].Price.Amount)
		require.Equal(t, arg.Product.Variants[i].CompareAtPrice.Amount, lastAuditVersion.ProductListing.Product.Variants[i].CompareAtPrice.Amount)
	}
}

func Test_serviceImpl_ListingUpdate_match_synced_auto_sync_field_add_variant(t *testing.T) {
	basePath := "./testdata/listing_update/match_synced/auto_sync_field/add_variant"
	s := initTestListingUpdateService(t, true, basePath)
	listing := createUpdateEventTestBaseListing(t, s, basePath+"/base_listing.json")
	lastListing := listing
	arg := readUpdateEventArg(t, basePath+"/arg.json")
	ctx := context.Background()
	listing, err := s.Update(ctx, listing.ID, arg)
	require.NoError(t, err)
	require.NotEmpty(t, listing.ID)
	require.Equal(t, consts.SyncStatusPartialSynced, listing.SyncStatus)
	require.Equal(t, consts.LinkStatusPartialLinked, listing.LinkStatus)
	require.Equal(t, lastListing.Product.Title, listing.Product.Title)
	require.Equal(t, lastListing.Product.Description, listing.Product.Description)
	require.Equal(t, len(lastListing.Product.Media), len(listing.Product.Media))
	require.Equal(t, len(arg.Product.Variants), len(listing.Product.Variants))
	require.Equal(t, len(arg.Relations), len(listing.Relations))
	require.Equal(t, len(listing.Product.Variants), len(listing.Relations))
	for i := range listing.Product.Variants {
		require.Equal(t, arg.Product.Variants[i].Sku, listing.Product.Variants[i].Sku)
		require.Equal(t, arg.Product.Variants[i].Title, listing.Product.Variants[i].Title)
		require.Equal(t, arg.Product.Variants[i].Price.Amount, listing.Product.Variants[i].Price.Amount)
		require.Equal(t, arg.Product.Variants[i].CompareAtPrice.Amount, listing.Product.Variants[i].CompareAtPrice.Amount)
	}
	for i := range listing.Relations {
		require.Equal(t, listing.Product.Variants[i].ID, listing.Relations[i].ProductListingVariantID)
		if i == 2 {
			require.Equal(t, consts.SyncStatusUnsync, listing.Relations[i].SyncStatus)
			require.Equal(t, consts.LinkStatusUnlink, listing.Relations[i].LinkStatus)
		} else {
			require.Equal(t, consts.SyncStatusSynced, listing.Relations[i].SyncStatus)
			require.Equal(t, consts.LinkStatusLinked, listing.Relations[i].LinkStatus)
		}
	}

	lastAuditVersion, err := s.latestAuditVersion(ctx, listing.ID)
	require.NoError(t, err)
	require.NotEmpty(t, lastAuditVersion)
	require.Equal(t, listing.ID, lastAuditVersion.ProductListing.ID)
	require.Equal(t, arg.Product.Title, lastAuditVersion.ProductListing.Product.Title)
	require.Equal(t, arg.Product.Description, lastAuditVersion.ProductListing.Product.Description)
	require.Equal(t, len(arg.Product.Variants), len(lastAuditVersion.ProductListing.Product.Variants))
	require.Equal(t, len(arg.Relations), len(lastAuditVersion.ProductListing.Relations))
	for i := range lastAuditVersion.ProductListing.Product.Variants {
		require.Equal(t, arg.Product.Variants[i].Sku, lastAuditVersion.ProductListing.Product.Variants[i].Sku)
		require.Equal(t, arg.Product.Variants[i].Title, lastAuditVersion.ProductListing.Product.Variants[i].Title)
		require.Equal(t, arg.Product.Variants[i].Price.Amount, lastAuditVersion.ProductListing.Product.Variants[i].Price.Amount)
		require.Equal(t, arg.Product.Variants[i].CompareAtPrice.Amount, lastAuditVersion.ProductListing.Product.Variants[i].CompareAtPrice.Amount)
	}
}

func Test_serviceImpl_ListingUpdate_match_synced_auto_sync_field_delete_variant(t *testing.T) {
	basePath := "./testdata/listing_update/match_synced/auto_sync_field/delete_variant"
	s := initTestListingUpdateService(t, true, basePath)
	listing := createUpdateEventTestBaseListing(t, s, basePath+"/base_listing.json")
	lastListing := listing
	arg := readUpdateEventArg(t, basePath+"/arg.json")
	ctx := context.Background()
	listing, err := s.Update(ctx, listing.ID, arg)
	require.NoError(t, err)
	require.NotEmpty(t, listing.ID)
	require.Equal(t, consts.SyncStatusSynced, listing.SyncStatus)
	require.Equal(t, consts.LinkStatusLinked, listing.LinkStatus)
	require.Equal(t, lastListing.Product.Title, listing.Product.Title)
	require.Equal(t, lastListing.Product.Description, listing.Product.Description)
	require.Equal(t, len(lastListing.Product.Media), len(listing.Product.Media))
	require.Equal(t, len(lastListing.Product.Variants), len(listing.Product.Variants))
	require.Equal(t, len(lastListing.Relations), len(listing.Relations))
	require.Equal(t, len(listing.Product.Variants), len(listing.Relations))
	for i := range listing.Product.Variants {
		require.Equal(t, lastListing.Product.Variants[i].Sku, listing.Product.Variants[i].Sku)
		require.Equal(t, lastListing.Product.Variants[i].Title, listing.Product.Variants[i].Title)
		require.Equal(t, lastListing.Product.Variants[i].Price.Amount, listing.Product.Variants[i].Price.Amount)
		require.Equal(t, lastListing.Product.Variants[i].CompareAtPrice.Amount, listing.Product.Variants[i].CompareAtPrice.Amount)
	}
	for i := range listing.Relations {
		require.Equal(t, listing.Product.Variants[i].ID, listing.Relations[i].ProductListingVariantID)
		require.Equal(t, consts.SyncStatusSynced, listing.Relations[i].SyncStatus)
		require.Equal(t, consts.LinkStatusLinked, listing.Relations[i].LinkStatus)
	}

	lastAuditVersion, err := s.latestAuditVersion(ctx, listing.ID)
	require.NoError(t, err)
	require.NotEmpty(t, lastAuditVersion)
	require.Equal(t, listing.ID, lastAuditVersion.ProductListing.ID)
	require.Equal(t, arg.Product.Title, lastAuditVersion.ProductListing.Product.Title)
	require.Equal(t, arg.Product.Description, lastAuditVersion.ProductListing.Product.Description)
	require.Equal(t, len(arg.Product.Variants), len(lastAuditVersion.ProductListing.Product.Variants))
	require.Equal(t, len(arg.Relations), len(lastAuditVersion.ProductListing.Relations))
	for i := range lastAuditVersion.ProductListing.Product.Variants {
		require.Equal(t, arg.Product.Variants[i].Sku, lastAuditVersion.ProductListing.Product.Variants[i].Sku)
		require.Equal(t, arg.Product.Variants[i].Title, lastAuditVersion.ProductListing.Product.Variants[i].Title)
		require.Equal(t, arg.Product.Variants[i].Price.Amount, lastAuditVersion.ProductListing.Product.Variants[i].Price.Amount)
		require.Equal(t, arg.Product.Variants[i].CompareAtPrice.Amount, lastAuditVersion.ProductListing.Product.Variants[i].CompareAtPrice.Amount)
	}
}

func Test_serviceImpl_ListingUpdate_match_synced_auto_sync_field_update_variant(t *testing.T) {
	basePath := "./testdata/listing_update/match_synced/auto_sync_field/update_variant"
	s := initTestListingUpdateService(t, true, basePath)
	listing := createUpdateEventTestBaseListing(t, s, basePath+"/base_listing.json")
	lastListing := listing
	arg := readUpdateEventArg(t, basePath+"/arg.json")
	ctx := context.Background()
	listing, err := s.Update(ctx, listing.ID, arg)
	require.NoError(t, err)
	require.NotEmpty(t, listing.ID)
	require.Equal(t, consts.SyncStatusSynced, listing.SyncStatus)
	require.Equal(t, consts.LinkStatusLinked, listing.LinkStatus)
	require.Equal(t, lastListing.Product.Title, listing.Product.Title)
	require.Equal(t, lastListing.Product.Description, listing.Product.Description)
	require.Equal(t, len(lastListing.Product.Media), len(listing.Product.Media))
	require.Equal(t, len(lastListing.Product.Variants), len(listing.Product.Variants))
	require.Equal(t, len(lastListing.Relations), len(listing.Relations))
	require.Equal(t, len(listing.Product.Variants), len(listing.Relations))
	for i := range listing.Product.Variants {
		require.Equal(t, lastListing.Product.Variants[i].Sku, listing.Product.Variants[i].Sku)
		require.Equal(t, lastListing.Product.Variants[i].Title, listing.Product.Variants[i].Title)
		require.Equal(t, lastListing.Product.Variants[i].Price.Amount, listing.Product.Variants[i].Price.Amount)
		require.Equal(t, lastListing.Product.Variants[i].CompareAtPrice.Amount, listing.Product.Variants[i].CompareAtPrice.Amount)
	}
	for i := range listing.Relations {
		require.Equal(t, listing.Product.Variants[i].ID, listing.Relations[i].ProductListingVariantID)
		require.Equal(t, consts.SyncStatusSynced, listing.Relations[i].SyncStatus)
		require.Equal(t, consts.LinkStatusLinked, listing.Relations[i].LinkStatus)
	}

	lastAuditVersion, err := s.latestAuditVersion(ctx, listing.ID)
	require.NoError(t, err)
	require.NotEmpty(t, lastAuditVersion)
	require.Equal(t, listing.ID, lastAuditVersion.ProductListing.ID)
	require.Equal(t, arg.Product.Title, lastAuditVersion.ProductListing.Product.Title)
	require.Equal(t, arg.Product.Description, lastAuditVersion.ProductListing.Product.Description)
	require.Equal(t, len(arg.Product.Variants), len(lastAuditVersion.ProductListing.Product.Variants))
	require.Equal(t, len(arg.Relations), len(lastAuditVersion.ProductListing.Relations))
	for i := range lastAuditVersion.ProductListing.Product.Variants {
		require.Equal(t, arg.Product.Variants[i].Sku, lastAuditVersion.ProductListing.Product.Variants[i].Sku)
		require.Equal(t, arg.Product.Variants[i].Title, lastAuditVersion.ProductListing.Product.Variants[i].Title)
		require.Equal(t, arg.Product.Variants[i].Price.Amount, lastAuditVersion.ProductListing.Product.Variants[i].Price.Amount)
		require.Equal(t, arg.Product.Variants[i].CompareAtPrice.Amount, lastAuditVersion.ProductListing.Product.Variants[i].CompareAtPrice.Amount)
	}
}

func Test_serviceImpl_ListingUpdate_match_synced_auto_sync_field_modify_product(t *testing.T) {
	basePath := "./testdata/listing_update/match_synced/auto_sync_field/modify_product"
	product := readUpdateEventProductsCenterProduct(t, basePath+"/product.json")
	s := initTestListingUpdateService(t, true, basePath)
	listing := createUpdateEventTestBaseListing(t, s, basePath+"/base_listing.json")
	lastListing := listing
	arg := readUpdateEventArg(t, basePath+"/arg.json")
	ctx := context.Background()
	listing, err := s.Update(ctx, listing.ID, arg)
	require.NoError(t, err)
	require.NotEmpty(t, listing.ID)
	require.Equal(t, consts.SyncStatusSynced, listing.SyncStatus)
	require.Equal(t, consts.LinkStatusLinked, listing.LinkStatus)
	require.Equal(t, lastListing.Product.Title, listing.Product.Title)
	require.Equal(t, lastListing.Product.Description, listing.Product.Description)
	require.Equal(t, len(lastListing.Product.Media), len(listing.Product.Media))
	require.Equal(t, len(lastListing.Product.Variants), len(listing.Product.Variants))
	require.Equal(t, len(lastListing.Relations), len(listing.Relations))
	require.Equal(t, len(listing.Product.Variants), len(listing.Relations))
	for i := range listing.Product.Variants {
		require.Equal(t, lastListing.Product.Variants[i].Sku, listing.Product.Variants[i].Sku)
		require.Equal(t, lastListing.Product.Variants[i].Title, listing.Product.Variants[i].Title)
		require.Equal(t, lastListing.Product.Variants[i].Price.Amount, listing.Product.Variants[i].Price.Amount)
		require.Equal(t, lastListing.Product.Variants[i].CompareAtPrice.Amount, listing.Product.Variants[i].CompareAtPrice.Amount)
	}
	for i := range listing.Relations {
		require.Equal(t, listing.Product.Variants[i].ID, listing.Relations[i].ProductListingVariantID)
		require.Equal(t, consts.SyncStatusSynced, listing.Relations[i].SyncStatus)
		require.Equal(t, consts.LinkStatusLinked, listing.Relations[i].LinkStatus)
	}

	lastAuditVersion, err := s.latestAuditVersion(ctx, listing.ID)
	require.NoError(t, err)
	require.NotEmpty(t, lastAuditVersion)
	require.Equal(t, listing.ID, lastAuditVersion.ProductListing.ID)
	require.Equal(t, product.Title, lastAuditVersion.ProductListing.Product.Title)
	require.Equal(t, product.Description, lastAuditVersion.ProductListing.Product.Description)
	require.Equal(t, len(product.Media), len(lastAuditVersion.ProductListing.Product.Media))
	require.Equal(t, len(arg.Product.Variants), len(lastAuditVersion.ProductListing.Product.Variants))
	require.Equal(t, len(arg.Relations), len(lastAuditVersion.ProductListing.Relations))
	for i := range lastAuditVersion.ProductListing.Product.Variants {
		require.Equal(t, arg.Product.Variants[i].Sku, lastAuditVersion.ProductListing.Product.Variants[i].Sku)
		require.Equal(t, arg.Product.Variants[i].Title, lastAuditVersion.ProductListing.Product.Variants[i].Title)
		require.Equal(t, arg.Product.Variants[i].Price.Amount, lastAuditVersion.ProductListing.Product.Variants[i].Price.Amount)
		require.Equal(t, arg.Product.Variants[i].CompareAtPrice.Amount, lastAuditVersion.ProductListing.Product.Variants[i].CompareAtPrice.Amount)
	}
}

func Test_serviceImpl_ListingUpdate_match_synced_auto_sync_variant_add_variant(t *testing.T) {
	basePath := "./testdata/listing_update/match_synced/auto_sync_variant/add_variant"
	s := initTestListingUpdateService(t, true, basePath)
	product := readUpdateEventProductsCenterProduct(t, basePath+"/product.json")
	listing := createUpdateEventTestBaseListing(t, s, basePath+"/base_listing.json")
	lastListing := listing
	arg := readUpdateEventArg(t, basePath+"/arg.json")
	ctx := context.Background()
	listing, err := s.Update(ctx, listing.ID, arg)
	require.NoError(t, err)
	require.NotEmpty(t, listing.ID)
	require.Equal(t, consts.SyncStatusSynced, listing.SyncStatus)
	require.Equal(t, consts.LinkStatusLinked, listing.LinkStatus)
	require.Equal(t, lastListing.Product.Title, listing.Product.Title)
	require.Equal(t, lastListing.Product.Description, listing.Product.Description)
	require.Equal(t, len(lastListing.Product.Media), len(listing.Product.Media))
	require.Equal(t, len(product.Variants), len(listing.Product.Variants))
	require.Equal(t, len(product.Variants), len(listing.Relations))
	require.Equal(t, len(listing.Product.Variants), len(listing.Relations))
	for i := range listing.Product.Variants {
		require.Equal(t, product.Variants[i].Sku, listing.Product.Variants[i].Sku)
		require.Equal(t, product.Variants[i].Title, listing.Product.Variants[i].Title)
		require.Equal(t, product.Variants[i].Price.Amount, listing.Product.Variants[i].Price.Amount)
		require.Equal(t, product.Variants[i].CompareAtPrice.Amount, listing.Product.Variants[i].CompareAtPrice.Amount)
	}
	for i := range listing.Relations {
		require.Equal(t, listing.Product.Variants[i].ID, listing.Relations[i].ProductListingVariantID)
		require.Equal(t, consts.SyncStatusSynced, listing.Relations[i].SyncStatus)
		require.Equal(t, consts.LinkStatusLinked, listing.Relations[i].LinkStatus)
	}

	lastAuditVersion, err := s.latestAuditVersion(ctx, listing.ID)
	require.NoError(t, err)
	require.NotEmpty(t, lastAuditVersion)
	require.Equal(t, listing.ID, lastAuditVersion.ProductListing.ID)
	require.Equal(t, arg.Product.Title, lastAuditVersion.ProductListing.Product.Title)
	require.Equal(t, arg.Product.Description, lastAuditVersion.ProductListing.Product.Description)
	require.Equal(t, len(product.Variants), len(lastAuditVersion.ProductListing.Product.Variants))
	require.Equal(t, len(product.Variants), len(lastAuditVersion.ProductListing.Relations))
	for i := range lastAuditVersion.ProductListing.Product.Variants {
		require.Equal(t, product.Variants[i].Sku, lastAuditVersion.ProductListing.Product.Variants[i].Sku)
		require.Equal(t, product.Variants[i].Title, lastAuditVersion.ProductListing.Product.Variants[i].Title)
		require.Equal(t, product.Variants[i].Price.Amount, lastAuditVersion.ProductListing.Product.Variants[i].Price.Amount)
		require.Equal(t, product.Variants[i].CompareAtPrice.Amount, lastAuditVersion.ProductListing.Product.Variants[i].CompareAtPrice.Amount)
	}
}

func Test_serviceImpl_ListingUpdate_match_synced_auto_sync_variant_delete_variant(t *testing.T) {
	basePath := "./testdata/listing_update/match_synced/auto_sync_variant/delete_variant"
	s := initTestListingUpdateService(t, true, basePath)
	product := readUpdateEventProductsCenterProduct(t, basePath+"/product.json")
	listing := createUpdateEventTestBaseListing(t, s, basePath+"/base_listing.json")
	lastListing := listing
	arg := readUpdateEventArg(t, basePath+"/arg.json")
	ctx := context.Background()
	listing, err := s.Update(ctx, listing.ID, arg)
	require.NoError(t, err)
	require.NotEmpty(t, listing.ID)
	require.Equal(t, consts.SyncStatusSynced, listing.SyncStatus)
	require.Equal(t, consts.LinkStatusLinked, listing.LinkStatus)
	require.Equal(t, lastListing.Product.Title, listing.Product.Title)
	require.Equal(t, lastListing.Product.Description, listing.Product.Description)
	require.Equal(t, len(lastListing.Product.Media), len(listing.Product.Media))
	// 删除 variant 的时候，由于 arg 传递的参数缺少了 relation，对于 preview 来说，就是需要将 products center 中的 variant 创建一个新的，删除旧的，所以比对失效
	require.Equal(t, len(product.Variants), len(listing.Product.Variants))
	require.Equal(t, len(product.Variants), len(listing.Relations))
	require.Equal(t, len(listing.Product.Variants), len(listing.Relations))
	for i := range listing.Product.Variants {
		require.Equal(t, product.Variants[i].Sku, listing.Product.Variants[i].Sku)
		require.Equal(t, product.Variants[i].Title, listing.Product.Variants[i].Title)
		require.Equal(t, product.Variants[i].Price.Amount, listing.Product.Variants[i].Price.Amount)
		require.Equal(t, product.Variants[i].CompareAtPrice.Amount, listing.Product.Variants[i].CompareAtPrice.Amount)
	}
	for i := range listing.Relations {
		require.Equal(t, listing.Product.Variants[i].ID, listing.Relations[i].ProductListingVariantID)
		if i == 0 {
			require.Equal(t, consts.SyncStatusSynced, listing.Relations[i].SyncStatus)
			require.Equal(t, consts.LinkStatusLinked, listing.Relations[i].LinkStatus)
		} else {
			require.Equal(t, consts.SyncStatusSynced, listing.Relations[i].SyncStatus)
			require.Equal(t, consts.LinkStatusLinked, listing.Relations[i].LinkStatus)
		}
	}

	lastAuditVersion, err := s.latestAuditVersion(ctx, listing.ID)
	require.NoError(t, err)
	require.NotEmpty(t, lastAuditVersion)
	require.Equal(t, listing.ID, lastAuditVersion.ProductListing.ID)

	require.Equal(t, arg.Product.Title, lastAuditVersion.ProductListing.Product.Title)
	require.Equal(t, arg.Product.Description, lastAuditVersion.ProductListing.Product.Description)
	require.Equal(t, len(product.Variants), len(lastAuditVersion.ProductListing.Product.Variants))
	require.Equal(t, len(product.Variants), len(lastAuditVersion.ProductListing.Relations))
	for i := range lastAuditVersion.ProductListing.Product.Variants {
		require.Equal(t, product.Variants[i].Sku, lastAuditVersion.ProductListing.Product.Variants[i].Sku)
		require.Equal(t, product.Variants[i].Title, lastAuditVersion.ProductListing.Product.Variants[i].Title)
		require.Equal(t, product.Variants[i].Price.Amount, lastAuditVersion.ProductListing.Product.Variants[i].Price.Amount)
		require.Equal(t, product.Variants[i].CompareAtPrice.Amount, lastAuditVersion.ProductListing.Product.Variants[i].CompareAtPrice.Amount)
	}
}

func Test_serviceImpl_ListingUpdate_match_synced_auto_sync_variant_update_variant(t *testing.T) {
	basePath := "./testdata/listing_update/match_synced/auto_sync_variant/update_variant"
	s := initTestListingUpdateService(t, true, basePath)
	product := readUpdateEventProductsCenterProduct(t, basePath+"/product.json")
	listing := createUpdateEventTestBaseListing(t, s, basePath+"/base_listing.json")
	lastListing := listing
	arg := readUpdateEventArg(t, basePath+"/arg.json")
	ctx := context.Background()
	listing, err := s.Update(ctx, listing.ID, arg)
	require.NoError(t, err)
	require.NotEmpty(t, listing.ID)
	require.Equal(t, consts.SyncStatusSynced, listing.SyncStatus)
	require.Equal(t, consts.LinkStatusLinked, listing.LinkStatus)
	require.Equal(t, lastListing.Product.Title, listing.Product.Title)
	require.Equal(t, lastListing.Product.Description, listing.Product.Description)
	require.Equal(t, len(lastListing.Product.Media), len(listing.Product.Media))
	require.Equal(t, len(product.Variants), len(listing.Product.Variants))
	require.Equal(t, len(product.Variants), len(listing.Relations))
	require.Equal(t, len(listing.Product.Variants), len(listing.Relations))
	for i := range listing.Product.Variants {
		require.Equal(t, product.Variants[i].Sku, listing.Product.Variants[i].Sku)
		// require.Equal(t, product.Variants[i].Title, listing.Product.Variants[i].Title)
		require.Equal(t, product.Variants[i].Price.Amount, listing.Product.Variants[i].Price.Amount)
		require.Equal(t, product.Variants[i].CompareAtPrice.Amount, listing.Product.Variants[i].CompareAtPrice.Amount)
	}
	for i := range listing.Relations {
		require.Equal(t, listing.Product.Variants[i].ID, listing.Relations[i].ProductListingVariantID)
		require.Equal(t, consts.SyncStatusSynced, listing.Relations[i].SyncStatus)
		require.Equal(t, consts.LinkStatusLinked, listing.Relations[i].LinkStatus)
	}

	lastAuditVersion, err := s.latestAuditVersion(ctx, listing.ID)
	require.NoError(t, err)
	require.NotEmpty(t, lastAuditVersion)
	require.Equal(t, listing.ID, lastAuditVersion.ProductListing.ID)
	require.Equal(t, arg.Product.Title, lastAuditVersion.ProductListing.Product.Title)
	require.Equal(t, arg.Product.Description, lastAuditVersion.ProductListing.Product.Description)
	require.Equal(t, len(product.Variants), len(lastAuditVersion.ProductListing.Product.Variants))
	require.Equal(t, len(product.Variants), len(lastAuditVersion.ProductListing.Relations))
	for i := range lastAuditVersion.ProductListing.Product.Variants {
		require.Equal(t, product.Variants[i].Sku, lastAuditVersion.ProductListing.Product.Variants[i].Sku)
		// require.Equal(t, product.Variants[i].Title, lastAuditVersion.ProductListing.Product.Variants[i].Title)
		require.Equal(t, product.Variants[i].Price.Amount, lastAuditVersion.ProductListing.Product.Variants[i].Price.Amount)
		require.Equal(t, product.Variants[i].CompareAtPrice.Amount, lastAuditVersion.ProductListing.Product.Variants[i].CompareAtPrice.Amount)
	}
}

func Test_serviceImpl_ListingUpdate_match_synced_auto_sync_variant_modify_product(t *testing.T) {
	basePath := "./testdata/listing_update/match_synced/auto_sync_variant/modify_product"
	product := readUpdateEventProductsCenterProduct(t, basePath+"/product.json")
	s := initTestListingUpdateService(t, true, basePath)
	listing := createUpdateEventTestBaseListing(t, s, basePath+"/base_listing.json")
	lastListing := listing
	arg := readUpdateEventArg(t, basePath+"/arg.json")
	ctx := context.Background()
	listing, err := s.Update(ctx, listing.ID, arg)
	require.NoError(t, err)
	require.NotEmpty(t, listing.ID)
	require.Equal(t, consts.SyncStatusSynced, listing.SyncStatus)
	require.Equal(t, consts.LinkStatusLinked, listing.LinkStatus)
	require.Equal(t, lastListing.Product.Title, listing.Product.Title)
	require.Equal(t, lastListing.Product.Description, listing.Product.Description)
	require.Equal(t, len(lastListing.Product.Media), len(listing.Product.Media))
	require.Equal(t, len(product.Variants), len(listing.Product.Variants))
	require.Equal(t, len(product.Variants), len(listing.Relations))
	require.Equal(t, len(listing.Product.Variants), len(listing.Relations))
	for i := range listing.Product.Variants {
		require.Equal(t, product.Variants[i].Sku, listing.Product.Variants[i].Sku)
		require.Equal(t, product.Variants[i].Title, listing.Product.Variants[i].Title)
		require.Equal(t, product.Variants[i].Price.Amount, listing.Product.Variants[i].Price.Amount)
		require.Equal(t, product.Variants[i].CompareAtPrice.Amount, listing.Product.Variants[i].CompareAtPrice.Amount)
	}
	for i := range listing.Relations {
		require.Equal(t, listing.Product.Variants[i].ID, listing.Relations[i].ProductListingVariantID)
		require.Equal(t, consts.SyncStatusSynced, listing.Relations[i].SyncStatus)
		require.Equal(t, consts.LinkStatusLinked, listing.Relations[i].LinkStatus)
	}

	lastAuditVersion, err := s.latestAuditVersion(ctx, listing.ID)
	require.NoError(t, err)
	require.NotEmpty(t, lastAuditVersion)
	require.Equal(t, listing.ID, lastAuditVersion.ProductListing.ID)
	require.Equal(t, arg.Product.Title, lastAuditVersion.ProductListing.Product.Title)
	require.Equal(t, arg.Product.Description, lastAuditVersion.ProductListing.Product.Description)
	require.Equal(t, len(arg.Product.Media), len(lastAuditVersion.ProductListing.Product.Media))
	require.Equal(t, len(product.Variants), len(lastAuditVersion.ProductListing.Product.Variants))
	require.Equal(t, len(product.Variants), len(lastAuditVersion.ProductListing.Relations))
	for i := range lastAuditVersion.ProductListing.Product.Variants {
		require.Equal(t, product.Variants[i].Sku, lastAuditVersion.ProductListing.Product.Variants[i].Sku)
		require.Equal(t, product.Variants[i].Title, lastAuditVersion.ProductListing.Product.Variants[i].Title)
		require.Equal(t, product.Variants[i].Price.Amount, lastAuditVersion.ProductListing.Product.Variants[i].Price.Amount)
		require.Equal(t, product.Variants[i].CompareAtPrice.Amount, lastAuditVersion.ProductListing.Product.Variants[i].CompareAtPrice.Amount)
	}
}

func Test_serviceImpl_ListingUpdate_match_synced_auto_sync_all_add_variant(t *testing.T) {
	basePath := "./testdata/listing_update/match_synced/auto_sync_all/add_variant"
	s := initTestListingUpdateService(t, true, basePath)
	product := readUpdateEventProductsCenterProduct(t, basePath+"/product.json")
	listing := createUpdateEventTestBaseListing(t, s, basePath+"/base_listing.json")
	lastListing := listing
	arg := readUpdateEventArg(t, basePath+"/arg.json")
	ctx := context.Background()
	listing, err := s.Update(ctx, listing.ID, arg)
	require.NoError(t, err)
	require.NotEmpty(t, listing.ID)
	require.Equal(t, consts.SyncStatusSynced, listing.SyncStatus)
	require.Equal(t, consts.LinkStatusLinked, listing.LinkStatus)
	require.Equal(t, lastListing.Product.Title, listing.Product.Title)
	require.Equal(t, lastListing.Product.Description, listing.Product.Description)
	require.Equal(t, len(lastListing.Product.Media), len(listing.Product.Media))
	require.Equal(t, len(product.Variants), len(listing.Product.Variants))
	require.Equal(t, len(product.Variants), len(listing.Relations))
	require.Equal(t, len(listing.Product.Variants), len(listing.Relations))
	for i := range listing.Product.Variants {
		require.Equal(t, product.Variants[i].Sku, listing.Product.Variants[i].Sku)
		require.Equal(t, product.Variants[i].Title, listing.Product.Variants[i].Title)
		require.Equal(t, product.Variants[i].Price.Amount, listing.Product.Variants[i].Price.Amount)
		require.Equal(t, product.Variants[i].CompareAtPrice.Amount, listing.Product.Variants[i].CompareAtPrice.Amount)
	}
	for i := range listing.Relations {
		require.Equal(t, listing.Product.Variants[i].ID, listing.Relations[i].ProductListingVariantID)
		require.Equal(t, consts.SyncStatusSynced, listing.Relations[i].SyncStatus)
		require.Equal(t, consts.LinkStatusLinked, listing.Relations[i].LinkStatus)
	}

	lastAuditVersion, err := s.latestAuditVersion(ctx, listing.ID)
	require.NoError(t, err)
	require.NotEmpty(t, lastAuditVersion)
	require.Equal(t, listing.ID, lastAuditVersion.ProductListing.ID)
	require.Equal(t, product.Title, lastAuditVersion.ProductListing.Product.Title)
	require.Equal(t, product.Description, lastAuditVersion.ProductListing.Product.Description)
	require.Equal(t, len(product.Variants), len(lastAuditVersion.ProductListing.Product.Variants))
	require.Equal(t, len(product.Variants), len(lastAuditVersion.ProductListing.Relations))
	for i := range lastAuditVersion.ProductListing.Product.Variants {
		require.Equal(t, product.Variants[i].Sku, lastAuditVersion.ProductListing.Product.Variants[i].Sku)
		require.Equal(t, product.Variants[i].Title, lastAuditVersion.ProductListing.Product.Variants[i].Title)
		require.Equal(t, product.Variants[i].Price.Amount, lastAuditVersion.ProductListing.Product.Variants[i].Price.Amount)
		require.Equal(t, product.Variants[i].CompareAtPrice.Amount, lastAuditVersion.ProductListing.Product.Variants[i].CompareAtPrice.Amount)
	}
}

func Test_serviceImpl_ListingUpdate_match_synced_auto_sync_all_delete_variant(t *testing.T) {
	basePath := "./testdata/listing_update/match_synced/auto_sync_all/delete_variant"
	s := initTestListingUpdateService(t, true, basePath)
	product := readUpdateEventProductsCenterProduct(t, basePath+"/product.json")
	listing := createUpdateEventTestBaseListing(t, s, basePath+"/base_listing.json")
	arg := readUpdateEventArg(t, basePath+"/arg.json")
	ctx := context.Background()
	listing, err := s.Update(ctx, listing.ID, arg)
	require.NoError(t, err)
	require.NotEmpty(t, listing.ID)
	require.Equal(t, consts.SyncStatusSynced, listing.SyncStatus)
	require.Equal(t, consts.LinkStatusLinked, listing.LinkStatus)
	require.Equal(t, product.Title, listing.Product.Title)
	require.Equal(t, product.Description, listing.Product.Description)
	require.Equal(t, len(product.Media), len(listing.Product.Media))
	require.Equal(t, len(product.Variants), len(listing.Product.Variants))
	require.Equal(t, len(product.Variants), len(listing.Relations))
	require.Equal(t, len(listing.Product.Variants), len(listing.Relations))
	for i := range listing.Product.Variants {
		require.Equal(t, product.Variants[i].Sku, listing.Product.Variants[i].Sku)
		require.Equal(t, product.Variants[i].Title, listing.Product.Variants[i].Title)
		require.Equal(t, product.Variants[i].Price.Amount, listing.Product.Variants[i].Price.Amount)
		require.Equal(t, product.Variants[i].CompareAtPrice.Amount, listing.Product.Variants[i].CompareAtPrice.Amount)
	}
	for i := range listing.Relations {
		require.Equal(t, listing.Product.Variants[i].ID, listing.Relations[i].ProductListingVariantID)
		require.Equal(t, consts.SyncStatusSynced, listing.Relations[i].SyncStatus)
		require.Equal(t, consts.LinkStatusLinked, listing.Relations[i].LinkStatus)
	}

	lastAuditVersion, err := s.latestAuditVersion(ctx, listing.ID)
	require.NoError(t, err)
	require.NotEmpty(t, lastAuditVersion)
	require.Equal(t, listing.ID, lastAuditVersion.ProductListing.ID)
	require.Equal(t, product.Title, lastAuditVersion.ProductListing.Product.Title)
	require.Equal(t, product.Description, lastAuditVersion.ProductListing.Product.Description)
	require.Equal(t, len(product.Variants), len(lastAuditVersion.ProductListing.Product.Variants))
	require.Equal(t, len(product.Variants), len(lastAuditVersion.ProductListing.Relations))
	for i := range lastAuditVersion.ProductListing.Product.Variants {
		require.Equal(t, product.Variants[i].Sku, lastAuditVersion.ProductListing.Product.Variants[i].Sku)
		require.Equal(t, product.Variants[i].Title, lastAuditVersion.ProductListing.Product.Variants[i].Title)
		require.Equal(t, product.Variants[i].Price.Amount, lastAuditVersion.ProductListing.Product.Variants[i].Price.Amount)
		require.Equal(t, product.Variants[i].CompareAtPrice.Amount, lastAuditVersion.ProductListing.Product.Variants[i].CompareAtPrice.Amount)
	}
}

func Test_serviceImpl_ListingUpdate_match_synced_auto_sync_all_update_variant(t *testing.T) {
	basePath := "./testdata/listing_update/match_synced/auto_sync_all/update_variant"
	s := initTestListingUpdateService(t, true, basePath)
	product := readUpdateEventProductsCenterProduct(t, basePath+"/product.json")
	listing := createUpdateEventTestBaseListing(t, s, basePath+"/base_listing.json")
	arg := readUpdateEventArg(t, basePath+"/arg.json")
	ctx := context.Background()
	listing, err := s.Update(ctx, listing.ID, arg)
	require.NoError(t, err)
	require.NotEmpty(t, listing.ID)
	require.Equal(t, consts.SyncStatusSynced, listing.SyncStatus)
	require.Equal(t, consts.LinkStatusLinked, listing.LinkStatus)
	require.Equal(t, product.Title, listing.Product.Title)
	require.Equal(t, product.Description, listing.Product.Description)
	require.Equal(t, len(product.Media), len(listing.Product.Media))
	require.Equal(t, len(product.Variants), len(listing.Product.Variants))
	require.Equal(t, len(product.Variants), len(listing.Relations))
	require.Equal(t, len(listing.Product.Variants), len(listing.Relations))
	for i := range listing.Product.Variants {
		require.Equal(t, product.Variants[i].Sku, listing.Product.Variants[i].Sku)
		// require.Equal(t, product.Variants[i].Title, listing.Product.Variants[i].Title)
		require.Equal(t, product.Variants[i].Price.Amount, listing.Product.Variants[i].Price.Amount)
		require.Equal(t, product.Variants[i].CompareAtPrice.Amount, listing.Product.Variants[i].CompareAtPrice.Amount)
	}
	for i := range listing.Relations {
		require.Equal(t, listing.Product.Variants[i].ID, listing.Relations[i].ProductListingVariantID)
		require.Equal(t, consts.SyncStatusSynced, listing.Relations[i].SyncStatus)
		require.Equal(t, consts.LinkStatusLinked, listing.Relations[i].LinkStatus)
	}

	lastAuditVersion, err := s.latestAuditVersion(ctx, listing.ID)
	require.NoError(t, err)
	require.NotEmpty(t, lastAuditVersion)
	require.Equal(t, listing.ID, lastAuditVersion.ProductListing.ID)
	require.Equal(t, product.Title, lastAuditVersion.ProductListing.Product.Title)
	require.Equal(t, product.Description, lastAuditVersion.ProductListing.Product.Description)
	require.Equal(t, len(product.Variants), len(lastAuditVersion.ProductListing.Product.Variants))
	require.Equal(t, len(product.Variants), len(lastAuditVersion.ProductListing.Relations))
	for i := range lastAuditVersion.ProductListing.Product.Variants {
		require.Equal(t, product.Variants[i].Sku, lastAuditVersion.ProductListing.Product.Variants[i].Sku)
		// require.Equal(t, product.Variants[i].Title, lastAuditVersion.ProductListing.Product.Variants[i].Title)
		require.Equal(t, product.Variants[i].Price.Amount, lastAuditVersion.ProductListing.Product.Variants[i].Price.Amount)
		require.Equal(t, product.Variants[i].CompareAtPrice.Amount, lastAuditVersion.ProductListing.Product.Variants[i].CompareAtPrice.Amount)
	}

	// same update params create audit version
	arg = readUpdateEventArg(t, basePath+"/arg.json")
	listing, err = s.Update(ctx, listing.ID, arg)
	require.NoError(t, err)
	newLastAuditVersion, err := s.latestAuditVersion(context.Background(), listing.ID)
	require.NoError(t, err)
	require.NotEqual(t, newLastAuditVersion.ID, lastAuditVersion.ID)
}

func Test_serviceImpl_ListingUpdate_match_synced_auto_sync_all_modify_product(t *testing.T) {
	basePath := "./testdata/listing_update/match_synced/auto_sync_all/modify_product"
	product := readUpdateEventProductsCenterProduct(t, basePath+"/product.json")
	s := initTestListingUpdateService(t, true, basePath)
	listing := createUpdateEventTestBaseListing(t, s, basePath+"/base_listing.json")
	arg := readUpdateEventArg(t, basePath+"/arg.json")
	ctx := context.Background()
	listing, err := s.Update(ctx, listing.ID, arg)
	require.NoError(t, err)
	require.NotEmpty(t, listing.ID)
	require.Equal(t, consts.SyncStatusSynced, listing.SyncStatus)
	require.Equal(t, consts.LinkStatusLinked, listing.LinkStatus)
	require.Equal(t, product.Title, listing.Product.Title)
	require.Equal(t, product.Description, listing.Product.Description)
	require.Equal(t, len(product.Media), len(listing.Product.Media))
	require.Equal(t, len(product.Variants), len(listing.Product.Variants))
	require.Equal(t, len(product.Variants), len(listing.Relations))
	require.Equal(t, len(listing.Product.Variants), len(listing.Relations))
	for i := range listing.Product.Variants {
		require.Equal(t, product.Variants[i].Sku, listing.Product.Variants[i].Sku)
		require.Equal(t, product.Variants[i].Title, listing.Product.Variants[i].Title)
		require.Equal(t, product.Variants[i].Price.Amount, listing.Product.Variants[i].Price.Amount)
		require.Equal(t, product.Variants[i].CompareAtPrice.Amount, listing.Product.Variants[i].CompareAtPrice.Amount)
	}
	for i := range listing.Relations {
		require.Equal(t, listing.Product.Variants[i].ID, listing.Relations[i].ProductListingVariantID)
		require.Equal(t, consts.SyncStatusSynced, listing.Relations[i].SyncStatus)
		require.Equal(t, consts.LinkStatusLinked, listing.Relations[i].LinkStatus)
	}

	lastAuditVersion, err := s.latestAuditVersion(ctx, listing.ID)
	require.NoError(t, err)
	require.NotEmpty(t, lastAuditVersion)
	require.Equal(t, listing.ID, lastAuditVersion.ProductListing.ID)
	require.Equal(t, product.Title, lastAuditVersion.ProductListing.Product.Title)
	require.Equal(t, product.Description, lastAuditVersion.ProductListing.Product.Description)
	require.Equal(t, len(product.Media), len(lastAuditVersion.ProductListing.Product.Media))
	require.Equal(t, len(product.Variants), len(lastAuditVersion.ProductListing.Product.Variants))
	require.Equal(t, len(product.Variants), len(lastAuditVersion.ProductListing.Relations))
	for i := range lastAuditVersion.ProductListing.Product.Variants {
		require.Equal(t, product.Variants[i].Sku, lastAuditVersion.ProductListing.Product.Variants[i].Sku)
		require.Equal(t, product.Variants[i].Title, lastAuditVersion.ProductListing.Product.Variants[i].Title)
		require.Equal(t, product.Variants[i].Price.Amount, lastAuditVersion.ProductListing.Product.Variants[i].Price.Amount)
		require.Equal(t, product.Variants[i].CompareAtPrice.Amount, lastAuditVersion.ProductListing.Product.Variants[i].CompareAtPrice.Amount)
	}
}

func Test_serviceImpl_ListingUpdate_link(t *testing.T) {
	basePath := "./testdata/listing_update/link"
	s := initTestListingUpdateService(t, true, basePath)
	listing := createUpdateEventTestBaseListing(t, s, basePath+"/base_listing.json")
	arg := readUpdateEventArg(t, basePath+"/arg.json")
	ctx := context.Background()
	listing, err := s.Update(ctx, listing.ID, arg)
	require.NoError(t, err)
	require.NotEmpty(t, listing.ID)
	require.Equal(t, consts.SyncStatusSynced, listing.SyncStatus)
	require.Equal(t, consts.LinkStatusLinked, listing.LinkStatus)
	for i := range listing.Relations {
		require.Equal(t, listing.Product.Variants[i].ID, listing.Relations[i].ProductListingVariantID)
		require.Equal(t, consts.SyncStatusSynced, listing.Relations[i].SyncStatus)
		require.Equal(t, consts.LinkStatusLinked, listing.Relations[i].LinkStatus)
		require.Equal(t, arg.Relations[i].ProductsCenterVariant.ProductID, listing.Relations[i].ProductsCenterVariant.ProductID)
		require.Equal(t, arg.Relations[i].ProductsCenterVariant.ID, listing.Relations[i].ProductsCenterVariant.ID)
		require.Equal(t, arg.Relations[i].ProductsCenterVariant.ConnectorProductID, listing.Relations[i].ProductsCenterVariant.ConnectorProductID)
		require.Equal(t, arg.Relations[i].ProductsCenterVariant.Source.ID, listing.Relations[i].ProductsCenterVariant.Source.ID)
		require.Equal(t, arg.Relations[i].ProductsCenterVariant.Source.ProductID, listing.Relations[i].ProductsCenterVariant.Source.ProductID)
		require.Equal(t, arg.Relations[i].ProductsCenterVariant.Source.Platform, listing.Relations[i].ProductsCenterVariant.Source.Platform)
		require.Equal(t, arg.Relations[i].ProductsCenterVariant.Source.Sku, listing.Relations[i].ProductsCenterVariant.Source.Sku)
		require.Equal(t, arg.Relations[i].ProductsCenterVariant.Source.StoreKey, listing.Relations[i].ProductsCenterVariant.Source.StoreKey)
	}
}

func Test_serviceImpl_ListingUpdate_match(t *testing.T) {
	basePath := "./testdata/listing_update/match"
	s := initTestListingUpdateService(t, true, basePath)
	listing := createUpdateEventTestBaseListing(t, s, basePath+"/base_listing.json")
	arg := readUpdateEventArg(t, basePath+"/arg.json")
	ctx := context.Background()
	listing, err := s.Update(ctx, listing.ID, arg)
	require.NoError(t, err)
	require.NotEmpty(t, listing.ID)
	require.Equal(t, consts.SyncStatusSynced, listing.SyncStatus)
	require.Equal(t, consts.LinkStatusLinked, listing.LinkStatus)
	compareProductsCenterProduct(t, arg.ProductsCenterProduct, listing.ProductsCenterProduct)
	for i := range listing.Relations {
		require.Equal(t, listing.Product.Variants[i].ID, listing.Relations[i].ProductListingVariantID)
		require.Equal(t, consts.SyncStatusSynced, listing.Relations[i].SyncStatus)
		require.Equal(t, consts.LinkStatusLinked, listing.Relations[i].LinkStatus)
		require.Equal(t, arg.Relations[i].ProductsCenterVariant.ProductID, listing.Relations[i].ProductsCenterVariant.ProductID)
		require.Equal(t, arg.Relations[i].ProductsCenterVariant.ID, listing.Relations[i].ProductsCenterVariant.ID)
		require.Equal(t, arg.Relations[i].ProductsCenterVariant.ConnectorProductID, listing.Relations[i].ProductsCenterVariant.ConnectorProductID)
		require.Equal(t, arg.Relations[i].ProductsCenterVariant.Source.ID, listing.Relations[i].ProductsCenterVariant.Source.ID)
		require.Equal(t, arg.Relations[i].ProductsCenterVariant.Source.ProductID, listing.Relations[i].ProductsCenterVariant.Source.ProductID)
		require.Equal(t, arg.Relations[i].ProductsCenterVariant.Source.Platform, listing.Relations[i].ProductsCenterVariant.Source.Platform)
		require.Equal(t, arg.Relations[i].ProductsCenterVariant.Source.Sku, listing.Relations[i].ProductsCenterVariant.Source.Sku)
		require.Equal(t, arg.Relations[i].ProductsCenterVariant.Source.StoreKey, listing.Relations[i].ProductsCenterVariant.Source.StoreKey)
	}
}

func initTestListingUpdateService(t *testing.T, mockProduct bool, basePath string) *serviceImpl {
	config := loadConfig(t)
	redisCli := datastore.Get().RedisClient
	spannerCli := datastore.Get().SpannerCli
	esCli := datastore.Get().ESClient
	err := elasticsearch.CreateTestIndexWithAlias(esCli, "pd_product_listings_2024", searchIndex, "product_listings_mapping.json")
	require.NoError(t, err)

	locker := datastore.Get().RedisLocker
	categoryMock := new(category.MockCategoryService)
	categoryMock.On("GetCategoryRules", mock.Anything, mock.Anything).Return(category.RulesOutput{
		ExternalCategoryID: "601419",
		Rule: category.Rule{
			ProductCertifications: []*category.ProductCertification{},
			SizeChart: &category.SizeChart{
				IsRequired:  false,
				IsSupported: false,
			},
			PackageDimension: &category.PackageDimension{
				IsRequired: false,
			},
		},
	}, nil)
	categoryMock.On("GetCategoryAttributes", mock.Anything, mock.Anything).Return(category.AttributesOutput{
		ExternalCategoryID: "601419",
		Attributes: []category.Attribute{
			{
				ID:         "1",
				IsRequired: false,
			},
		},
	}, nil)
	categoryMock.On("Versions", mock.Anything, mock.Anything).Return(category.CategoryVersionsOutput{
		CategoryVersions: []category.CategoryVersion{
			{
				Version: consts.CategoryVersionV2,
			},
		},
	}, nil)

	settingMock := new(settings.MockSettingService)
	settingMock.On("List", mock.Anything, mock.Anything).Return([]*settings.Setting{
		{
			ID: uuid.GenerateUUIDV4(),
		},
	}, nil)
	taskMock := new(task.MockTaskService)
	taskMock.On("Create", mock.Anything, mock.Anything).Return(models.Task{}, nil)

	mockConnectorService := new(connectors.MockService)
	mockConnectorService.On("GetBothConnections", mock.Anything, mock.Anything).
		Return(connectors.BothConnections{
			OrganizationID: "organization_id",
			App: models.App{
				Platform: "shopify",
				Key:      "s_1",
			},
			Channels: []models.App{
				{
					Platform: "tiktok-shop",
					Key:      "store_key",
				},
			},
		}, nil)

	listingEventsServiceMock := new(events.MockListingEventsService)
	listingEventsServiceMock.On("CommonEvent", mock.Anything, mock.Anything).Return(nil)
	listingEventsServiceMock.On("ModifyStateEvent", mock.Anything, mock.Anything).Return(nil)
	listingLinkEventsServiceMock := new(events.MockListingLinkEventsService)
	listingLinkEventsServiceMock.On("AutoLinkEvent", mock.Anything, mock.Anything).Return(nil)
	listingLinkEventsServiceMock.On("LinkRecordEvent", mock.Anything, mock.Anything).Return(nil)
	feedEventService := &events.FeedEventsService{
		ListingEventsService:     listingEventsServiceMock,
		ListingLinkEventsService: listingLinkEventsServiceMock,
	}

	bmeService := new(bme.MockBmeService)
	bmeService.On("Recording", mock.Anything).Return(nil)
	bmeService.On("CollectBusinessEvents", mock.Anything).Return(nil)
	bmeCli := &bme.Client{
		RecordService: bmeService,
	}

	if mockProduct {
		productsCenterProductMock := new(products_center.MockProductAPICollection)
		productsCenterCli := &products_center.Client{
			Product: productsCenterProductMock,
		}
		product := readUpdateEventProductsCenterProduct(t, basePath+"/product.json")
		productsCenterProductMock.On("GetByID", mock.Anything, mock.Anything).Return(product, nil)
		productsCenterProductMock.On("List", mock.Anything, mock.Anything).Return([]*products_center.Product{product}, nil)
		calculatorMock := new(calculators.MockCalculatorsService)

		calVariantsPrice := make([]calculators.CalculatePricesVariantOutput, 0, len(product.Variants))
		for i := range product.Variants {
			price, _ := decimal.NewFromString(product.Variants[i].Price.Amount)
			calVariantsPrice = append(calVariantsPrice, calculators.CalculatePricesVariantOutput{
				ID:                        product.Variants[i].ID,
				ExternalID:                product.Variants[i].SourceVariantID,
				CalculatedPrice:           price,
				Currency:                  product.Variants[i].Price.Currency,
				CalculatedComparedAtPrice: price,
				Status:                    consts.CalculateSuccess,
			})
		}

		calPriceOutput := &calculators.CalculatePricesOutput{
			ProductsCenterProduct: &calculators.CalculatePriceProductsCenterProductOutput{
				ID:                 product.ID,
				ConnectorProductID: product.ConnectorsProductID,
				Variants:           calVariantsPrice,
			},
		}

		calVariantsQuantity := make([]calculators.CalculateAvailableQuantitiesVariantOutput, 0, len(product.Variants))
		for i := range product.Variants {
			calVariantsQuantity = append(calVariantsQuantity, calculators.CalculateAvailableQuantitiesVariantOutput{
				ID:                          product.Variants[i].ID,
				ExternalID:                  product.Variants[i].SourceVariantID,
				CalculatedAvailableQuantity: product.Variants[i].InventoryQuantity,
				Status:                      consts.CalculateSuccess,
			})
		}

		calQuantityOutput := &calculators.CalculateAvailableQuantitiesOutput{
			InventorySync: &models.InventorySync{
				AutoSync: "enabled",
			},
			ProductsCenterProduct: &calculators.ProductsCenterProductOut{
				ID:                 product.ID,
				ConnectorProductID: product.ConnectorsProductID,
				Variants:           calVariantsQuantity,
			},
		}

		calculatorMock.On("CalculatePrices", mock.Anything, mock.Anything).Return(calPriceOutput, nil)
		calculatorMock.On("CalculateAvailableQuantities", mock.Anything, mock.Anything).Return(calQuantityOutput, nil)

		convertMock := new(convert.MockConvertService)
		convertMock.On("ConvertImage", mock.Anything, mock.Anything).Return(convert.ImageOutput{
			FromSalesChannel: true,
			ImageURI:         "tos-useast5-i-omjb5zjo8w-tx/a722e09440f64ed3bd8bdcb428bc046f",
			ImageURL:         "https://p16-oec-ttp.tiktokcdn-us.com/tos-useast5-i-omjb5zjo8w-tx/a722e09440f64ed3bd8bdcb428bc046f~tplv-omjb5zjo8w-origin-jpeg.jpeg?from=1432613627",
			ImageUseCase:     "MAIN_IMAGE",
			ImageHeight:      2000,
			ImageWidth:       2000,
		}, nil)
		convertMock.On("ConvertDescription", mock.Anything, mock.Anything).Return(convert.DescriptionOutput{Description: product.Description}, nil)
		return NewService(logger.Get(), spannerCli, esCli, redisCli, locker, productsCenterCli,
			settingMock, calculatorMock, categoryMock, taskMock, convertMock, nil,
			config, nil, mockConnectorService, feedEventService, bmeCli, nil, nil)
	}

	productsCenterProductMock := new(products_center.MockProductAPICollection)
	productsCenterCli := &products_center.Client{
		Product: productsCenterProductMock,
	}
	productsCenterProductMock.On("GetByID", mock.Anything, mock.Anything).Return(nil, nil)
	productsCenterProductMock.On("List", mock.Anything, mock.Anything).Return([]*products_center.Product{}, nil)
	return NewService(logger.Get(), spannerCli, esCli, redisCli, locker, productsCenterCli,
		settingMock, nil, categoryMock, taskMock, nil, nil,
		config, nil, mockConnectorService, feedEventService, bmeCli, nil, nil)
}

func readUpdateEventArg(t *testing.T, path string) *ProductListingArgs {
	bytes, err := os.ReadFile(path)
	require.NoError(t, err)
	outputs := ProductListingArgs{}
	err = json.Unmarshal(bytes, &outputs)
	require.NoError(t, err)
	return &outputs
}

func createUpdateEventTestBaseListing(t *testing.T, s *serviceImpl, path string) ProductListing {
	bytes, err := os.ReadFile(path)
	require.NoError(t, err)
	createArg := ProductListingArgs{}
	err = json.Unmarshal(bytes, &createArg)
	require.NoError(t, err)
	ctx := context.Background()
	listing, err := s.Create(ctx, &createArg)
	require.NoError(t, err)
	require.NotEmpty(t, listing.ID)
	s.esRepo.RefreshESIndex(ctx)
	return listing
}

func readUpdateEventProductsCenterProduct(t *testing.T, path string) *products_center.Product {
	bytes, err := os.ReadFile(path)
	require.NoError(t, err)
	outputs := products_center.Product{}
	err = json.Unmarshal(bytes, &outputs)
	require.NoError(t, err)
	return &outputs
}

func TestBuildOptions(t *testing.T) {
	tests := []struct {
		name        string
		newOptions  []*models.ProductOption
		lastOptions []*models.ProductOption
		expected    []*models.ProductOption
	}{
		{
			name:        "No options",
			newOptions:  []*models.ProductOption{},
			lastOptions: []*models.ProductOption{},
			expected:    []*models.ProductOption{},
		},
		{
			name: "New options only",
			newOptions: []*models.ProductOption{
				{
					SalesChannelOptionID: "option_1",
					ValueDetails: []models.ProductOptionValueDetail{
						{SalesChannelValueID: "value_1"},
					},
				},
			},
			lastOptions: []*models.ProductOption{},
			expected: []*models.ProductOption{
				{
					SalesChannelOptionID: "option_1",
					ValueDetails: []models.ProductOptionValueDetail{
						{SalesChannelValueID: "value_1"},
					},
				},
			},
		},
		{
			name:       "Last options only",
			newOptions: []*models.ProductOption{},
			lastOptions: []*models.ProductOption{
				{
					SalesChannelOptionID: "option_1",
					ValueDetails: []models.ProductOptionValueDetail{
						{SalesChannelValueID: "value_1"},
					},
				},
			},
			expected: []*models.ProductOption{
				{
					SalesChannelOptionID: "option_1",
					ValueDetails: []models.ProductOptionValueDetail{
						{SalesChannelValueID: "value_1"},
					},
				},
			},
		},
		{
			name: "new options",
			newOptions: []*models.ProductOption{
				{
					SalesChannelOptionID: "option_1",
					ValueDetails: []models.ProductOptionValueDetail{
						{SalesChannelValueID: "value_1", SalesChannelID: "111"},
						{SalesChannelValueID: "value_2"},
					},
				},
			},
			lastOptions: []*models.ProductOption{
				{
					SalesChannelOptionID: "option_1",
					ValueDetails: []models.ProductOptionValueDetail{
						{SalesChannelValueID: "value_1", State: "active", SalesChannelID: "111"},
					},
				},
			},
			expected: []*models.ProductOption{
				{
					SalesChannelOptionID: "option_1",
					ValueDetails: []models.ProductOptionValueDetail{
						{SalesChannelValueID: "value_1", State: "active", SalesChannelID: "111"},
						{SalesChannelValueID: "value_2"},
					},
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			model := &updateModel{
				newListing: &ProductListing{
					Product: models.Product{
						Options: tt.newOptions,
					},
				},
				lastListing: &ProductListing{
					Product: models.Product{
						Options: tt.lastOptions,
					},
				},
			}
			got := model.buildOptions()
			require.Equal(t, tt.expected, got)
		})
	}
}

func Test_generateNewVariantID(t *testing.T) {
	// Initialize a `updateModel` instance with necessary fields
	model := &updateModel{
		newListing: &ProductListing{
			Product: models.Product{
				Variants: []*models.ProductVariant{
					{
						ID:       "variant_1",
						Position: 1,
					},
					{
						ID:       "",
						Position: 2,
					},
				},
			},
			Relations: []*ProductListingRelation{
				{
					ID:                      "relation_1",
					VariantPosition:         1,
					ProductListingVariantID: "variant_1",
				},
				{
					ID:                      "",
					VariantPosition:         2,
					ProductListingVariantID: "",
				},
			},
		},
	}

	// Call the `generateNewVariantID` method
	model.generateNewVariantID()

	for i, variant := range model.newListing.Product.Variants {
		require.NotEmpty(t, variant.ID)
		require.Equal(t, model.newListing.Relations[i].ProductListingVariantID, variant.ID)
	}
}
