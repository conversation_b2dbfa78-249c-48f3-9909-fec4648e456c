package product_listing

import (
	"context"
	"fmt"
	"testing"
	"time"

	"cloud.google.com/go/spanner"
	"github.com/spf13/viper"
	"github.com/stretchr/testify/require"

	"github.com/AfterShip/gopkg/cfg"
	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/gopkg/storage/spannerx"
	"github.com/AfterShip/gopkg/uuid"

	"github.com/AfterShip/pltf-pd-product-listings/internal/config"
	"github.com/AfterShip/pltf-pd-product-listings/internal/consts"
)

func Test_ProductListingRepoImpl_GetByID(t *testing.T) {
	cfgs := new(config.Config)
	_, err := cfg.LoadViperConfig(cfgs, func(v *viper.Viper) { v.AddConfigPath("../../../cmd/apiserver/conf") })
	require.NoError(t, err)

	ctx := context.Background()

	database := fmt.Sprintf("projects/%s/instances/%s/databases/%s",
		cfgs.Spanner.Project, cfgs.Spanner.Instance, cfgs.Spanner.Database)
	spannerCli, err := spannerx.NewClient(ctx, database)
	require.NoError(t, err)

	// create product listing data
	plRepo := productListingRepo{
		cli: spannerCli,
	}
	relationDBRepo := relationRepo{
		cli: spannerCli,
	}
	pl := productListingDBModel{
		ProductListingID:      uuid.GenerateUUIDV4(),
		OrganizationID:        uuid.GenerateUUIDV4(),
		SalesChannelPlatform:  "tiktok-shop",
		SalesChannelStoreKey:  uuid.GenerateUUIDV4(),
		SalesChannelProductID: types.MakeString(uuid.GenerateUUIDV4()),
	}
	relations := []relationDBModel{
		{
			ProductListingRelationID: uuid.GenerateUUIDV4(),
			ProductListingID:         pl.ProductListingID,
			ProductListingVariantID:  uuid.GenerateUUIDV4(),
		},
		{
			ProductListingRelationID: uuid.GenerateUUIDV4(),
			ProductListingID:         pl.ProductListingID,
			ProductListingVariantID:  uuid.GenerateUUIDV4(),
		},
	}

	mutations := make([]*spanner.Mutation, 0)
	m, productListingErr := plRepo.generateCreateMutation(&pl)
	require.NoError(t, productListingErr)
	mutations = append(mutations, m)
	for _, relation := range relations {
		m, relationErr := relationDBRepo.generateCreateMutation(&relation)
		require.NoError(t, relationErr)
		mutations = append(mutations, m)
	}
	_, err = spannerCli.Apply(ctx, mutations)
	require.NoError(t, err)

	repo := newRepoConductor(spannerCli)
	// Test case 1: Get product listing by ID
	found, err := repo.getByID(ctx, pl.ProductListingID)
	require.NoError(t, err)
	require.Equal(t, pl.ProductListingID, found.ProductListingDBModel.ProductListingID)
	require.Equal(t, pl.State, found.ProductListingDBModel.State)
	require.Equal(t, 2, len(found.Relations))

	// Test case 2: Get product listing by ID not found
	notFound, err := repo.getByID(ctx, "not_found")
	require.Error(t, err)
	require.Equal(t, "", notFound.ProductListingDBModel.ProductListingID)
	require.Equal(t, 0, len(notFound.Relations))
}

func Test_ProductListingRepoImpl_GetByIDs(t *testing.T) {
	cfgs := new(config.Config)
	_, err := cfg.LoadViperConfig(cfgs, func(v *viper.Viper) { v.AddConfigPath("../../../cmd/apiserver/conf") })
	require.NoError(t, err)

	ctx := context.Background()

	database := fmt.Sprintf("projects/%s/instances/%s/databases/%s",
		cfgs.Spanner.Project, cfgs.Spanner.Instance, cfgs.Spanner.Database)
	spannerCli, err := spannerx.NewClient(ctx, database)
	require.NoError(t, err)
	repo := newRepoConductor(spannerCli)
	ids := make([]string, 0)
	args := make([]*conductorProductListing, 0)
	for i := 0; i < 10; i++ {
		productListingID := uuid.GenerateUUIDV4()
		ids = append(ids, productListingID)
		arg := conductorProductListing{
			ProductListingDBModel: generateTestingProductListingDBModel(productListingID),
		}
		arg.Relations = append(arg.Relations, generateRelationDBModel(productListingID))
		arg.Relations = append(arg.Relations, generateRelationDBModel(productListingID))
		args = append(args, &arg)
		err = repo.create(ctx, &arg)
		require.NoError(t, err)
	}
	// Test case 1: Get product listing by IDs
	founds, err := repo.listByIDs(ctx, ids)
	require.NoError(t, err)
	require.Len(t, founds, 10, founds)
	for _, found := range founds {
		for _, arg := range args {
			if arg.ProductListingDBModel.ProductListingID == found.ProductListingDBModel.ProductListingID {
				compareProductListingDBModel(t, &found.ProductListingDBModel, &arg.ProductListingDBModel)
				require.Equal(t, 2, len(found.Relations))
				for _, relation := range arg.Relations {
					for _, repoRelation := range found.Relations {
						if relation.ProductListingRelationID == repoRelation.ProductListingRelationID {
							compareRelationDBModel(t, relation, repoRelation)
						}
					}
				}
			}
		}
	}
	// Test case 1: Get product listing by IDs with not found
	ids = append(ids, "not_found")
	founds, err = repo.listByIDs(ctx, ids)
	require.NoError(t, err)
	require.Len(t, founds, 10, founds)
}

func Test_ProductListingRepoImpl_Create(t *testing.T) {
	cfgs := new(config.Config)
	_, err := cfg.LoadViperConfig(cfgs, func(v *viper.Viper) { v.AddConfigPath("../../../cmd/apiserver/conf") })
	require.NoError(t, err)

	ctx := context.Background()

	database := fmt.Sprintf("projects/%s/instances/%s/databases/%s",
		cfgs.Spanner.Project, cfgs.Spanner.Instance, cfgs.Spanner.Database)
	spannerCli, err := spannerx.NewClient(ctx, database)
	require.NoError(t, err)

	repo := newRepoConductor(spannerCli)

	// Test case 1: Create product listing
	productListingID := uuid.GenerateUUIDV4()
	args := conductorProductListing{
		ProductListingDBModel: generateTestingProductListingDBModel(productListingID),
	}
	args.Relations = append(args.Relations, generateRelationDBModel(productListingID))
	args.Relations = append(args.Relations, generateRelationDBModel(productListingID))
	err = repo.create(ctx, &args)
	require.NoError(t, err)
	// check product listing
	found, err := repo.getByID(ctx, productListingID)
	require.NoError(t, err)
	compareProductListingDBModel(t, &found.ProductListingDBModel, &args.ProductListingDBModel)
	require.Equal(t, 2, len(found.Relations))
	for _, relation := range args.Relations {
		for _, repoRelation := range found.Relations {
			if relation.ProductListingRelationID == repoRelation.ProductListingRelationID {
				compareRelationDBModel(t, relation, repoRelation)
			}
		}
	}
}

func Test_ProductListingRepoImpl_Delete(t *testing.T) {
	cfgs := new(config.Config)
	_, err := cfg.LoadViperConfig(cfgs, func(v *viper.Viper) { v.AddConfigPath("../../../cmd/apiserver/conf") })
	require.NoError(t, err)

	ctx := context.Background()

	database := fmt.Sprintf("projects/%s/instances/%s/databases/%s",
		cfgs.Spanner.Project, cfgs.Spanner.Instance, cfgs.Spanner.Database)
	spannerCli, err := spannerx.NewClient(ctx, database)
	require.NoError(t, err)

	repo := newRepoConductor(spannerCli)

	productListingID := uuid.GenerateUUIDV4()
	args := conductorProductListing{
		ProductListingDBModel: generateTestingProductListingDBModel(productListingID),
	}
	args.Relations = append(args.Relations, generateRelationDBModel(productListingID))
	args.Relations = append(args.Relations, generateRelationDBModel(productListingID))
	err = repo.create(ctx, &args)
	require.NoError(t, err)
	// check product listing
	found, err := repo.getByID(ctx, productListingID)
	require.NoError(t, err)
	require.True(t, found.ProductListingDBModel.DeletedAt.IsNull())
	require.True(t, found.Relations[0].DeletedAt.IsNull())
	require.True(t, found.Relations[1].DeletedAt.IsNull())
	// Test case 1: Delete product listing
	err = repo.delete(ctx, productListingID)
	require.NoError(t, err)
	// check product listing
	found, err = repo.getByID(ctx, productListingID)
	require.NoError(t, err)
	require.False(t, found.ProductListingDBModel.DeletedAt.IsNull())
	require.Equal(t, 0, len(found.Relations))
	// test case 2: delete not found
	err = repo.delete(ctx, "not_found")
	require.Error(t, err)

	// test case 3: delete is deleted product_listing
	err = repo.delete(ctx, productListingID)
	require.NoError(t, err)
}

func Test_ProductListingRepoImpl_Update(t *testing.T) {
	cfgs := new(config.Config)
	_, err := cfg.LoadViperConfig(cfgs, func(v *viper.Viper) { v.AddConfigPath("../../../cmd/apiserver/conf") })
	require.NoError(t, err)

	ctx := context.Background()

	database := fmt.Sprintf("projects/%s/instances/%s/databases/%s",
		cfgs.Spanner.Project, cfgs.Spanner.Instance, cfgs.Spanner.Database)
	spannerCli, err := spannerx.NewClient(ctx, database)
	require.NoError(t, err)

	repo := newRepoConductor(spannerCli)

	productListingID := uuid.GenerateUUIDV4()
	args := conductorProductListing{
		ProductListingDBModel: generateTestingProductListingDBModel(productListingID),
	}
	args.Relations = append(args.Relations, generateRelationDBModel(productListingID))
	args.Relations = append(args.Relations, generateRelationDBModel(productListingID))
	args.Relations = append(args.Relations, generateRelationDBModel(productListingID))
	args.Relations = append(args.Relations, generateRelationDBModel(productListingID))
	err = repo.create(ctx, &args)
	require.NoError(t, err)

	// test case 1: nothing to update
	updateArgs := conductorUpdateArgs{}
	err = repo.update(ctx, &updateArgs)
	require.NoError(t, err)

	// test case 2: update product listing
	updateArgs = conductorUpdateArgs{}
	updateArgs.ProductListingDBModel = args.ProductListingDBModel
	updateArgs.ProductListingDBModel.ReadyStatus = consts.ReadyStatusUnready
	updateArgs.ProductListingDBModel.Product = generateProductListingProduct()
	updateArgs.ProductListingDBModel.Settings = generateProductListingSettings()
	err = repo.update(ctx, &updateArgs)
	require.NoError(t, err)
	// check product listing
	found, err := repo.getByID(ctx, productListingID)
	require.NoError(t, err)
	compareProductListingDBModel(t, &found.ProductListingDBModel, &updateArgs.ProductListingDBModel)

	// test case 3 : update product listing with update relations
	updateArgs = conductorUpdateArgs{}
	updateRelation := generateRelationDBModel(productListingID)
	updateRelation.ProductListingRelationID = args.Relations[0].ProductListingRelationID
	updateArgs.UpdateRelations = append(updateArgs.UpdateRelations, updateRelation)
	err = repo.update(ctx, &updateArgs)
	require.NoError(t, err)
	// check product listing
	found, err = repo.getByID(ctx, productListingID)
	require.NoError(t, err)
	for _, relation := range found.Relations {
		if relation.ProductListingRelationID == updateRelation.ProductListingRelationID {
			compareRelationDBModel(t, updateRelation, relation)
		}
	}

	// test case 4 : update product listing with create relations
	updateArgs = conductorUpdateArgs{}
	createRelation := generateRelationDBModel(productListingID)
	updateArgs.CreateRelations = append(updateArgs.CreateRelations, createRelation)
	err = repo.update(ctx, &updateArgs)
	require.NoError(t, err)
	// check product listing
	found, err = repo.getByID(ctx, productListingID)
	require.NoError(t, err)
	for _, relation := range found.Relations {
		if relation.ProductListingRelationID == createRelation.ProductListingRelationID {
			compareRelationDBModel(t, createRelation, relation)
		}
	}

	// test case 5 : update product listing with delete relations
	updateArgs = conductorUpdateArgs{}
	updateArgs.DeleteRelationIDs = append(updateArgs.DeleteRelationIDs, args.Relations[0].ProductListingRelationID)
	err = repo.update(ctx, &updateArgs)
	require.NoError(t, err)
	// check product listing
	found, err = repo.getByID(ctx, productListingID)
	require.NoError(t, err)
	for _, relation := range found.Relations {
		if relation.ProductListingRelationID == args.Relations[0].ProductListingRelationID {
			require.False(t, relation.DeletedAt.IsNull())
		}
	}

	// test case 6 :update product listing with create, update and delete relations
	updateArgs = conductorUpdateArgs{}
	updateArgs.ProductListingDBModel = args.ProductListingDBModel
	updateArgs.ProductListingDBModel.ReadyStatus = consts.ReadyStatusUnready
	updateArgs.ProductListingDBModel.Product = generateProductListingProduct()
	updateArgs.ProductListingDBModel.Settings = generateProductListingSettings()
	updateRelation = generateRelationDBModel(productListingID)
	updateRelation.ProductListingRelationID = args.Relations[0].ProductListingRelationID
	updateArgs.UpdateRelations = append(updateArgs.UpdateRelations, updateRelation)
	createRelation = generateRelationDBModel(productListingID)
	updateArgs.CreateRelations = append(updateArgs.CreateRelations, createRelation)
	updateArgs.DeleteRelationIDs = append(updateArgs.DeleteRelationIDs, args.Relations[1].ProductListingRelationID)
	err = repo.update(ctx, &updateArgs)
	require.NoError(t, err)
	// check product listing
	found, err = repo.getByID(ctx, productListingID)
	require.NoError(t, err)
	compareProductListingDBModel(t, &found.ProductListingDBModel, &updateArgs.ProductListingDBModel)
	for _, relation := range found.Relations {
		if relation.ProductListingRelationID == updateRelation.ProductListingRelationID {
			compareRelationDBModel(t, updateRelation, relation)
		}
		if relation.ProductListingRelationID == createRelation.ProductListingRelationID {
			compareRelationDBModel(t, createRelation, relation)
		}
		if relation.ProductListingRelationID == args.Relations[1].ProductListingRelationID {
			require.False(t, relation.DeletedAt.IsNull())
		}
	}

}

func generateTestingProductListingDBModel(id string) productListingDBModel {
	return productListingDBModel{
		ProductListingID:                 id,
		State:                            consts.ProductListingProductStateActive,
		OrganizationID:                   uuid.GenerateUUIDV4(),
		SalesChannelPlatform:             "shopify",
		SalesChannelStoreKey:             "shopify_store_key",
		SalesChannelProductID:            types.MakeString(uuid.GenerateUUIDV4()),
		SalesChannelConnectorProductID:   uuid.GenerateUUIDV4(),
		SalesChannelState:                "active",
		SalesChannelMetricsCreatedAt:     time.Now().UTC(),
		SalesChannelMetricsUpdatedAt:     time.Now().UTC(),
		ProductsCenterProductID:          uuid.GenerateUUIDV4(),
		ProductsCenterConnectorProductID: uuid.GenerateUUIDV4(),
		ProductsCenterState:              "active",
		ProductsCenterSourceStoreKey:     "source_store_key",
		ProductsCenterSourcePlatform:     "source_platform",
		ProductsCenterSourceProductID:    uuid.GenerateUUIDV4(),
		LinkStatus:                       "linked",
		SyncStatus:                       "synced",
		ReadyStatus:                      consts.ReadyStatusReady,
		AuditState:                       "active",
		AuditFailedReasons: []AuditFailedReason{
			{
				Position:    "position",
				Reasons:     []string{"reason1", "reason2"},
				Suggestions: []string{"suggestion1", "suggestion2"},
			},
		},
		AuditLastFailedAt:      time.Now().UTC(),
		PublishLastReferenceID: uuid.GenerateUUIDV4(),
		PublishState:           "active",
		PublishErrorCode:       "error_code",
		PublishErrorMsg:        "error_msg",
		PublishLastFailedAt:    time.Now().UTC(),
		Product:                generateProductListingProduct(),
		Settings:               generateProductListingSettings(),
	}
}

func generateRelationDBModel(id string) *relationDBModel {
	return &relationDBModel{
		ProductListingRelationID:         uuid.GenerateUUIDV4(),
		ProductListingID:                 id,
		ProductListingVariantID:          uuid.GenerateUUIDV4(),
		SalesChannelVariantID:            uuid.GenerateUUIDV4(),
		SalesChannelConnectorProductID:   uuid.GenerateUUIDV4(),
		SalesChannelProductID:            uuid.GenerateUUIDV4(),
		SalesChannelSku:                  "sku",
		ProductsCenterVariantID:          uuid.GenerateUUIDV4(),
		ProductsCenterProductID:          uuid.GenerateUUIDV4(),
		ProductsCenterConnectorProductID: uuid.GenerateUUIDV4(),
		ProductsCenterSourceStoreKey:     "source_store_key",
		ProductsCenterSourcePlatform:     "source_platform",
		ProductsCenterSourceVariantID:    uuid.GenerateUUIDV4(),
		ProductsCenterSourceProductID:    uuid.GenerateUUIDV4(),
		ProductsCenterSourceSku:          "source_sku",
		SyncStatus:                       "synced",
		LinkStatus:                       "linked",
		AllowSync:                        "allow_sync",
	}
}

func compareProductListingDBModel(t *testing.T, expected, actual *productListingDBModel) {
	require.Equal(t, expected.ProductListingID, actual.ProductListingID)
	require.Equal(t, expected.State, actual.State)
	require.Equal(t, expected.OrganizationID, actual.OrganizationID)
	require.Equal(t, expected.SalesChannelPlatform, actual.SalesChannelPlatform)
	require.Equal(t, expected.SalesChannelStoreKey, actual.SalesChannelStoreKey)
	require.Equal(t, expected.SalesChannelProductID, actual.SalesChannelProductID)
	require.Equal(t, expected.SalesChannelConnectorProductID, actual.SalesChannelConnectorProductID)
	require.Equal(t, expected.SalesChannelState, actual.SalesChannelState)
	require.Equal(t, expected.SalesChannelMetricsUpdatedAt, actual.SalesChannelMetricsUpdatedAt)
	require.Equal(t, expected.SalesChannelMetricsCreatedAt, actual.SalesChannelMetricsCreatedAt)
	require.Equal(t, expected.ProductsCenterProductID, actual.ProductsCenterProductID)
	require.Equal(t, expected.ProductsCenterConnectorProductID, actual.ProductsCenterConnectorProductID)
	require.Equal(t, expected.ProductsCenterState, actual.ProductsCenterState)
	require.Equal(t, expected.ProductsCenterSourceStoreKey, actual.ProductsCenterSourceStoreKey)
	require.Equal(t, expected.ProductsCenterSourcePlatform, actual.ProductsCenterSourcePlatform)
	require.Equal(t, expected.ProductsCenterSourceProductID, actual.ProductsCenterSourceProductID)
	require.Equal(t, expected.State, actual.State)
	require.Equal(t, expected.LinkStatus, actual.LinkStatus)
	require.Equal(t, expected.SyncStatus, actual.SyncStatus)
	require.Equal(t, expected.ReadyStatus, actual.ReadyStatus)
	require.Equal(t, expected.AuditState, actual.AuditState)
	require.Equal(t, expected.AuditFailedReasons, actual.AuditFailedReasons)
	require.Equal(t, expected.AuditLastFailedAt, actual.AuditLastFailedAt)
	require.Equal(t, expected.PublishLastReferenceID, actual.PublishLastReferenceID)
	require.Equal(t, expected.PublishState, actual.PublishState)
	require.Equal(t, expected.PublishErrorCode, actual.PublishErrorCode)
	require.Equal(t, expected.PublishErrorMsg, actual.PublishErrorMsg)
	require.Equal(t, expected.PublishLastFailedAt, actual.PublishLastFailedAt)
	require.Equal(t, expected.Product, actual.Product)
	require.Equal(t, expected.Settings, actual.Settings)
}

func compareRelationDBModel(t *testing.T, expected, actual *relationDBModel) {
	require.Equal(t, expected.ProductListingRelationID, actual.ProductListingRelationID)
	require.Equal(t, expected.ProductListingID, actual.ProductListingID)
	require.Equal(t, expected.ProductListingVariantID, actual.ProductListingVariantID)
	require.Equal(t, expected.SalesChannelVariantID, actual.SalesChannelVariantID)
	require.Equal(t, expected.SalesChannelConnectorProductID, actual.SalesChannelConnectorProductID)
	require.Equal(t, expected.SalesChannelProductID, actual.SalesChannelProductID)
	require.Equal(t, expected.SalesChannelSku, actual.SalesChannelSku)
	require.Equal(t, expected.ProductsCenterVariantID, actual.ProductsCenterVariantID)
	require.Equal(t, expected.ProductsCenterProductID, actual.ProductsCenterProductID)
	require.Equal(t, expected.ProductsCenterConnectorProductID, actual.ProductsCenterConnectorProductID)
	require.Equal(t, expected.ProductsCenterSourceStoreKey, actual.ProductsCenterSourceStoreKey)
	require.Equal(t, expected.ProductsCenterSourcePlatform, actual.ProductsCenterSourcePlatform)
	require.Equal(t, expected.ProductsCenterSourceVariantID, actual.ProductsCenterSourceVariantID)
	require.Equal(t, expected.ProductsCenterSourceProductID, actual.ProductsCenterSourceProductID)
	require.Equal(t, expected.ProductsCenterSourceSku, actual.ProductsCenterSourceSku)
	require.Equal(t, expected.SyncStatus, actual.SyncStatus)
	require.Equal(t, expected.LinkStatus, actual.LinkStatus)
	require.Equal(t, expected.AllowSync, actual.AllowSync)
}
