package product_listing

import (
	"context"

	"cloud.google.com/go/spanner"

	"github.com/AfterShip/gopkg/storage/spannerx"
)

type repoConductor struct {
	cli                *spannerx.Client
	plRepo             *productListingRepo
	plRelationRepo     *relationRepo
	plAuditVersionRepo *auditVersionRepo
}

func newRepoConductor(cli *spannerx.Client) *repoConductor {
	return &repoConductor{
		cli: cli,
		plRepo: &productListingRepo{
			cli: cli,
		},
		plRelationRepo: &relationRepo{
			cli: cli,
		},
		plAuditVersionRepo: &auditVersionRepo{
			cli: cli,
		},
	}
}

func (r *repoConductor) getByID(ctx context.Context, id string) (conductorProductListing, error) {
	result := conductorProductListing{}

	// Get product listing
	pl, err := r.plRepo.getByID(ctx, id)
	if err != nil {
		return result, err
	}
	result.ProductListingDBModel = pl

	// get relations
	relations, err := r.plRelationRepo.listAllByProductListingID(ctx, id)
	if err != nil {
		return result, err
	}
	result.Relations = relations

	return result, nil
}

func (r *repoConductor) create(ctx context.Context, args *conductorProductListing) error {
	mutations := make([]*spanner.Mutation, 0)

	// generate create mutation for product listing
	m, err := r.plRepo.generateCreateMutation(&args.ProductListingDBModel)
	if err != nil {
		return err
	}
	mutations = append(mutations, m)

	// generate create mutation for relations
	for _, relation := range args.Relations {
		m, err := r.plRelationRepo.generateCreateMutation(relation)
		if err != nil {
			return err
		}
		mutations = append(mutations, m)
	}

	// apply mutations
	_, err = r.cli.Apply(ctx, mutations)

	return err
}

func (r *repoConductor) list(ctx context.Context, arg *repoListArgs) ([]*conductorProductListing, error) {
	// get product listings
	pls, err := r.plRepo.list(ctx, arg)
	if err != nil {
		return nil, err
	}

	ids := make([]string, 0, len(pls))
	for _, pl := range pls {
		ids = append(ids, pl.ProductListingID)
	}

	// get product listing relations
	relationDBModels, err := r.plRelationRepo.listAllByProductListingIDs(ctx, ids)
	if err != nil {
		return nil, err
	}

	// merge product listing and relations
	result := make([]*conductorProductListing, 0)
	for _, pl := range pls {
		relations := make([]*relationDBModel, 0)
		for _, relation := range relationDBModels {
			if relation.ProductListingID == pl.ProductListingID {
				relations = append(relations, relation)
			}
		}
		result = append(result, &conductorProductListing{
			ProductListingDBModel: *pl,
			Relations:             relations,
		})
	}

	return result, nil
}

func (r *repoConductor) listByIDs(ctx context.Context, ids []string) ([]*conductorProductListing, error) {
	// get product listings
	pls, err := r.plRepo.listByIDs(ctx, ids)
	if err != nil {
		return nil, err
	}

	// get product listing relations
	relationDBModels, err := r.plRelationRepo.listAllByProductListingIDs(ctx, ids)
	if err != nil {
		return nil, err
	}

	// merge product listing and relations
	result := make([]*conductorProductListing, 0)
	for _, pl := range pls {
		relations := make([]*relationDBModel, 0)
		for _, relation := range relationDBModels {
			if relation.ProductListingID == pl.ProductListingID {
				relations = append(relations, relation)
			}
		}
		result = append(result, &conductorProductListing{
			ProductListingDBModel: *pl,
			Relations:             relations,
		})
	}

	return result, nil
}

func (r *repoConductor) delete(ctx context.Context, id string) error {
	mutations := make([]*spanner.Mutation, 0)

	// delete product listing
	mut := r.plRepo.generateDeleteMutation(id)
	mutations = append(mutations, mut)

	// get relations
	relations, err := r.plRelationRepo.listAllByProductListingID(ctx, id)
	if err != nil {
		return err
	}

	// delete relations
	for _, relation := range relations {
		m := r.plRelationRepo.generateDeleteMutation(relation.ProductListingRelationID)
		mutations = append(mutations, m)
	}

	_, err = r.cli.Apply(ctx, mutations)

	return err
}

//nolint:unused
func (r *repoConductor) forceDelete(ctx context.Context, id string) error {
	mutations := make([]*spanner.Mutation, 0)

	// delete product listing
	mut := r.plRepo.generateForceDeleteMutation(id)
	mutations = append(mutations, mut)

	// get relations
	relations, err := r.plRelationRepo.listAllByProductListingID(ctx, id)
	if err != nil {
		return err
	}

	// delete relations
	for _, relation := range relations {
		m := r.plRelationRepo.generateForceDeleteMutation(relation.ProductListingRelationID)
		mutations = append(mutations, m)
	}

	_, err = r.cli.Apply(ctx, mutations)

	return err
}

func (r *repoConductor) update(ctx context.Context, args *conductorUpdateArgs) error {
	mutations := make([]*spanner.Mutation, 0)

	// generate update mutation for product listing
	if args.ProductListingDBModel.ProductListingID != "" {
		m, err := r.plRepo.generateUpdateMutation(&args.ProductListingDBModel)
		if err != nil {
			return err
		}
		mutations = append(mutations, m)
	}

	// generate create mutation for relations
	for _, relation := range args.CreateRelations {
		m, err := r.plRelationRepo.generateCreateMutation(relation)
		if err != nil {
			return err
		}
		mutations = append(mutations, m)
	}

	// generate update mutation for relations
	for _, relation := range args.UpdateRelations {
		m, err := r.plRelationRepo.generateUpdateMutation(relation)
		if err != nil {
			return err
		}
		mutations = append(mutations, m)
	}

	// generate delete mutation for relations
	for _, relation := range args.DeleteRelationIDs {
		m := r.plRelationRepo.generateDeleteMutation(relation)
		mutations = append(mutations, m)
	}

	_, err := r.cli.Apply(ctx, mutations)
	return err
}
