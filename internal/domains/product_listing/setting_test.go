package product_listing

import (
	"reflect"
	"testing"
	"time"

	"github.com/stretchr/testify/require"

	"github.com/AfterShip/pltf-pd-product-listings/internal/consts"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/settings"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
)

// nolint:maintidx
func Test_autoSyncProductDetailField(t *testing.T) {
	type fields struct {
		storeSetting          settings.Setting
		productListingSetting SyncSettings
	}
	type args struct {
		field consts.ProductDetailField
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   bool
	}{
		// TODO: Add test cases.
		{
			name: "Test case 1: flow store setting, auto sync title",
			fields: fields{
				storeSetting: settings.Setting{
					ProductSync: models.ProductSync{
						UpdateDetail: models.UpdateDetail{
							AutoSync: consts.StateEnabled,
							Fields: []string{
								consts.ProductDetailFieldTitle.String(),
								consts.ProductDetailFieldDescription.String(),
								consts.ProductDetailFieldMedia.String(),
							},
						},
					},
				},
				productListingSetting: SyncSettings{
					ProductSyncSetting: ProductSyncSetting{
						Preference: consts.SettingPreferenceStore,
						Customized: models.ProductSync{
							UpdateDetail: models.UpdateDetail{
								AutoSync: consts.StateDisabled,
								Fields: []string{
									consts.ProductDetailFieldTitle.String(),
									consts.ProductDetailFieldDescription.String(),
									consts.ProductDetailFieldMedia.String(),
								},
							},
						},
					},
				},
			},
			args: args{
				field: consts.ProductDetailFieldTitle,
			},
			want: true,
		},

		{
			name: "Test case 2: prefer store setting, disable auto sync title",
			fields: fields{
				storeSetting: settings.Setting{
					ProductSync: models.ProductSync{
						UpdateDetail: models.UpdateDetail{
							AutoSync: consts.StateDisabled,
							Fields: []string{
								consts.ProductDetailFieldTitle.String(),
								consts.ProductDetailFieldDescription.String(),
								consts.ProductDetailFieldMedia.String(),
							},
						},
					},
				},
				productListingSetting: SyncSettings{
					ProductSyncSetting: ProductSyncSetting{
						Preference: consts.SettingPreferenceStore,
						Customized: models.ProductSync{
							UpdateDetail: models.UpdateDetail{
								AutoSync: consts.StateEnabled,
								Fields: []string{
									consts.ProductDetailFieldTitle.String(),
									consts.ProductDetailFieldDescription.String(),
									consts.ProductDetailFieldMedia.String(),
								},
							},
						},
					},
				},
			},
			args: args{
				field: consts.ProductDetailFieldTitle,
			},
			want: false,
		},

		{
			name: "Test case 3: prefer product listing setting, auto sync title",
			fields: fields{
				storeSetting: settings.Setting{
					ProductSync: models.ProductSync{
						UpdateDetail: models.UpdateDetail{
							AutoSync: consts.StateDisabled,
							Fields: []string{
								consts.ProductDetailFieldTitle.String(),
								consts.ProductDetailFieldDescription.String(),
								consts.ProductDetailFieldMedia.String(),
							},
						},
					},
				},
				productListingSetting: SyncSettings{
					ProductSyncSetting: ProductSyncSetting{
						Preference: consts.SettingPreferenceCustomized,
						Customized: models.ProductSync{
							UpdateDetail: models.UpdateDetail{
								AutoSync: consts.StateEnabled,
								Fields: []string{
									consts.ProductDetailFieldTitle.String(),
									consts.ProductDetailFieldDescription.String(),
									consts.ProductDetailFieldMedia.String(),
								},
							},
						},
					},
				},
			},
			args: args{
				field: consts.ProductDetailFieldTitle,
			},
			want: true,
		},
		{
			name: "Test case 4: prefer product listing setting, disable auto sync title",
			fields: fields{
				storeSetting: settings.Setting{
					ProductSync: models.ProductSync{
						UpdateDetail: models.UpdateDetail{
							AutoSync: consts.StateEnabled,
							Fields: []string{
								consts.ProductDetailFieldTitle.String(),
								consts.ProductDetailFieldDescription.String(),
								consts.ProductDetailFieldMedia.String(),
							},
						},
					},
				},
				productListingSetting: SyncSettings{
					ProductSyncSetting: ProductSyncSetting{
						Preference: consts.SettingPreferenceCustomized,
						Customized: models.ProductSync{
							UpdateDetail: models.UpdateDetail{
								AutoSync: consts.StateDisabled,
								Fields: []string{
									consts.ProductDetailFieldTitle.String(),
									consts.ProductDetailFieldDescription.String(),
									consts.ProductDetailFieldMedia.String(),
								},
							},
						},
					},
				},
			},
			args: args{
				field: consts.ProductDetailFieldTitle,
			},
			want: false,
		},
		{
			name: "Test case 5: fields slice is empty",
			fields: fields{
				storeSetting: settings.Setting{
					ProductSync: models.ProductSync{
						UpdateDetail: models.UpdateDetail{
							AutoSync: consts.StateEnabled,
							Fields:   []string{},
						},
					},
				},
				productListingSetting: SyncSettings{
					ProductSyncSetting: ProductSyncSetting{
						Preference: consts.SettingPreferenceStore,
						Customized: models.ProductSync{
							UpdateDetail: models.UpdateDetail{
								AutoSync: consts.StateEnabled,
								Fields:   []string{},
							},
						},
					},
				},
			},
			args: args{
				field: consts.ProductDetailFieldTitle,
			},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &storeProductListingSetting{
				storeSetting:          tt.fields.storeSetting,
				productListingSetting: tt.fields.productListingSetting,
			}
			if got := s.autoSyncProductDetailField(tt.args.field); got != tt.want {
				t.Errorf("autoSyncProductDetailField() = %v, want %v", got, tt.want)
			}
		})
	}
}

// nolint:maintidx
func Test_autoSyncVariant(t *testing.T) {
	type fields struct {
		storeSetting          settings.Setting
		productListingSetting SyncSettings
	}
	tests := []struct {
		name   string
		fields fields
		want   bool
	}{
		{
			name: "Test case 1: prefer store setting, auto sync variant",
			fields: fields{
				storeSetting: settings.Setting{
					ProductSync: models.ProductSync{
						UpdateVariants: models.UpdateVariants{
							AutoSync: consts.StateEnabled,
						},
					},
				},
				productListingSetting: SyncSettings{
					ProductSyncSetting: ProductSyncSetting{
						Preference: consts.SettingPreferenceStore,
						Customized: models.ProductSync{
							UpdateVariants: models.UpdateVariants{
								AutoSync: consts.StateDisabled,
							},
						},
					},
				},
			},
			want: true,
		},
		{
			name: "Test case 2: prefer store setting, disable auto sync variant",
			fields: fields{
				storeSetting: settings.Setting{
					ProductSync: models.ProductSync{
						UpdateVariants: models.UpdateVariants{
							AutoSync: consts.StateDisabled,
						},
					},
				},
				productListingSetting: SyncSettings{
					ProductSyncSetting: ProductSyncSetting{
						Preference: consts.SettingPreferenceStore,
						Customized: models.ProductSync{
							UpdateVariants: models.UpdateVariants{
								AutoSync: consts.StateEnabled,
							},
						},
					},
				},
			},
			want: false,
		},
		{
			name: "Test case 3: prefer product listing setting, auto sync variant",
			fields: fields{
				storeSetting: settings.Setting{
					ProductSync: models.ProductSync{
						UpdateVariants: models.UpdateVariants{
							AutoSync: consts.StateDisabled,
						},
					},
				},
				productListingSetting: SyncSettings{
					ProductSyncSetting: ProductSyncSetting{
						Preference: consts.SettingPreferenceCustomized,
						Customized: models.ProductSync{
							UpdateVariants: models.UpdateVariants{
								AutoSync: consts.StateEnabled,
							},
						},
					},
				},
			},
			want: true,
		},
		{
			name: "Test case 4: prefer product listing setting, disable auto sync variant",
			fields: fields{
				storeSetting: settings.Setting{
					ProductSync: models.ProductSync{
						UpdateVariants: models.UpdateVariants{
							AutoSync: consts.StateEnabled,
						},
					},
				},
				productListingSetting: SyncSettings{
					ProductSyncSetting: ProductSyncSetting{
						Preference: consts.SettingPreferenceCustomized,
						Customized: models.ProductSync{
							UpdateVariants: models.UpdateVariants{
								AutoSync: consts.StateDisabled,
							},
						},
					},
				},
			},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &storeProductListingSetting{
				storeSetting:          tt.fields.storeSetting,
				productListingSetting: tt.fields.productListingSetting,
			}
			if got := s.autoSyncVariant(); got != tt.want {
				t.Errorf("autoSyncVariant() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_storeProductListingSetting_getPriceSyncSetting(t *testing.T) {
	type fields struct {
		storeSetting          settings.Setting
		productListingSetting SyncSettings
	}
	tests := []struct {
		name   string
		fields fields
		want   models.PriceSync
	}{
		{
			name: "Test case 1: Default settings",
			fields: fields{
				storeSetting: settings.Setting{
					PriceSync: models.PriceSync{
						SourceField: "compare_at_price",
						AutoSync:    "enabled",
						Rules:       nil,
					},
				},
				productListingSetting: SyncSettings{
					PriceSyncSetting: PriceSyncSetting{
						Preference: consts.SettingPreferenceStore,
						Customized: models.PriceSync{
							SourceField: "price",
							AutoSync:    "disabled",
							Rules:       nil,
						},
					},
				},
			},
			want: models.PriceSync{
				SourceField: "compare_at_price",
				AutoSync:    "enabled",
				Rules:       nil,
			},
		},
		{
			name: "Test case 2: Custom settings",
			fields: fields{
				storeSetting: settings.Setting{
					PriceSync: models.PriceSync{
						SourceField: "compare_at_price",
						AutoSync:    "enabled",
						Rules:       nil,
					},
				},
				productListingSetting: SyncSettings{
					PriceSyncSetting: PriceSyncSetting{
						Preference: consts.SettingPreferenceCustomized,
						Customized: models.PriceSync{
							SourceField: "price",
							AutoSync:    "disabled",
							Rules:       nil,
						},
					},
				},
			},
			want: models.PriceSync{
				SourceField: "price",
				AutoSync:    "disabled",
				Rules:       nil,
			},
		},
		// TODO: Add more test cases as needed.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &storeProductListingSetting{
				storeSetting:          tt.fields.storeSetting,
				productListingSetting: tt.fields.productListingSetting,
			}
			if got := s.getPriceSyncSetting(); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("getPriceSyncSetting() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_storeProductListingSetting_getInventorySyncSetting(t *testing.T) {
	type fields struct {
		storeSetting          settings.Setting
		productListingSetting SyncSettings
	}
	tests := []struct {
		name   string
		fields fields
		want   models.InventorySync
	}{
		{
			name: "Test case 1: Default settings",
			fields: fields{
				storeSetting: settings.Setting{
					InventorySync: models.InventorySync{
						AutoSync:                   "enabled",
						AvailableQuantityPercent:   1,
						LowQuantityThreshold:       models.LowQuantityThreshold{},
						ActiveWarehouses:           nil,
						LastEffectAt:               time.Time{},
						FollowSourceAllowBackorder: "enabled",
					}},
				productListingSetting: SyncSettings{
					InventorySyncSetting: InventorySyncSetting{
						Preference: consts.SettingPreferenceStore,
						Customized: models.InventorySync{
							AutoSync:                   "enabled",
							AvailableQuantityPercent:   0.8,
							LowQuantityThreshold:       models.LowQuantityThreshold{},
							ActiveWarehouses:           nil,
							LastEffectAt:               time.Time{},
							FollowSourceAllowBackorder: "enabled",
						},
					}},
			},
			want: models.InventorySync{
				AutoSync:                   "enabled",
				AvailableQuantityPercent:   1,
				LowQuantityThreshold:       models.LowQuantityThreshold{},
				ActiveWarehouses:           nil,
				LastEffectAt:               time.Time{},
				FollowSourceAllowBackorder: "enabled",
			},
		},
		{
			name: "Test case 2: Custom settings",
			fields: fields{
				storeSetting: settings.Setting{
					InventorySync: models.InventorySync{
						AutoSync:                 "enabled",
						AvailableQuantityPercent: 1,
						LowQuantityThreshold:     models.LowQuantityThreshold{},
						ActiveWarehouses: []models.ActiveWarehouse{
							{
								State:             "enabled",
								SourceWarehouseId: "123",
							},
						},
						LastEffectAt:               time.Time{},
						FollowSourceAllowBackorder: "enabled",
					},
				},
				productListingSetting: SyncSettings{
					InventorySyncSetting: InventorySyncSetting{
						Preference: consts.SettingPreferenceCustomized,
						Customized: models.InventorySync{
							AutoSync:                   "enabled",
							AvailableQuantityPercent:   0.8,
							LowQuantityThreshold:       models.LowQuantityThreshold{},
							ActiveWarehouses:           nil,
							LastEffectAt:               time.Time{},
							FollowSourceAllowBackorder: "enabled",
						},
					}},
			},
			want: models.InventorySync{
				AutoSync:                 "enabled",
				AvailableQuantityPercent: 0.8,
				LowQuantityThreshold:     models.LowQuantityThreshold{},
				ActiveWarehouses: []models.ActiveWarehouse{
					{
						State:             "enabled",
						SourceWarehouseId: "123",
					},
				},
				LastEffectAt:               time.Time{},
				FollowSourceAllowBackorder: "enabled",
			},
		},
		// TODO: Add more test cases as needed.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &storeProductListingSetting{
				storeSetting:          tt.fields.storeSetting,
				productListingSetting: tt.fields.productListingSetting,
			}
			if got := s.getInventorySyncSetting(); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("getInventorySyncSetting() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestGetDefaultProductListingSetting(t *testing.T) {
	tests := []struct {
		name     string
		platform string
		expected SyncSettings
	}{
		{
			name:     "Tiktok platform",
			platform: consts.TikTokShop,
			expected: SyncSettings{
				ProductSyncSetting: ProductSyncSetting{
					Preference: consts.SettingPreferenceStore,
				},
				PriceSyncSetting: PriceSyncSetting{
					Preference: consts.SettingPreferenceStore,
				},
				InventorySyncSetting: InventorySyncSetting{
					Preference: consts.SettingPreferenceStore,
				},
			},
		},
		{
			name:     "Shein platform",
			platform: consts.Shein,
			expected: SyncSettings{
				ProductSyncSetting: ProductSyncSetting{
					Preference: consts.SettingPreferenceCustomized,
					Customized: models.ProductSync{
						UpdateDetail: models.UpdateDetail{
							AutoSync: consts.StateDisabled,
						},
						UpdateVariants: models.UpdateVariants{
							AutoSync: consts.StateDisabled,
						},
					},
				},
				PriceSyncSetting: PriceSyncSetting{
					Preference: consts.SettingPreferenceStore,
				},
				InventorySyncSetting: InventorySyncSetting{
					Preference: consts.SettingPreferenceStore,
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := getDefaultProductListingSetting(tt.platform)
			require.Equal(t, tt.expected, result)
		})
	}
}
