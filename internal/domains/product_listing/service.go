package product_listing

import (
	"context"

	"github.com/go-playground/validator/v10"
	"github.com/go-redis/redis/v8"
	"github.com/go-redsync/redsync/v4"
	elastic "github.com/olivere/elastic/v7"

	"github.com/AfterShip/connectors-library/sdks/products_center"
	"github.com/AfterShip/feed-sdk-go/events"
	"github.com/AfterShip/gopkg/log"
	"github.com/AfterShip/gopkg/storage/spannerx"

	"github.com/AfterShip/pltf-pd-product-listings/internal/config"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/category"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/common/calculators"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/common/databus"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/convert"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/searchable_products"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/settings"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/task_schedule/task"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
	"github.com/AfterShip/pltf-pd-product-listings/internal/third_party/bme"
	"github.com/AfterShip/pltf-pd-product-listings/internal/third_party/connectors"
	"github.com/AfterShip/pltf-pd-product-listings/internal/third_party/feed"
)

type Service interface {
	GetByID(ctx context.Context, id string) (ProductListing, error)
	Search(ctx context.Context, args *SearchProductListingArgs) ([]*ProductListing, *models.Pagination, error)
	SearchIDs(ctx context.Context, args *SearchProductListingArgs) ([]string, *models.Pagination, error)
	Count(ctx context.Context, args *SearchProductListingArgs) (int64, error)
	AggregateCategoryIDs(ctx context.Context, args *SearchProductListingArgs) ([]string, error)
	Create(ctx context.Context, args *ProductListingArgs) (ProductListing, error)
	UpdatePublishState(ctx context.Context, id string, publish *Publish) (ProductListing, error)
	UpdateAuditState(ctx context.Context, id string, audit *Audit) (ProductListing, error)
	ListAuditVersions(ctx context.Context, args *ListAuditVersionsArgs) ([]*AuditVersion, error)
	ListRelations(ctx context.Context, args *ListRelationsArgs) ([]*ProductListingRelation, error)
	Duplicate(ctx context.Context, id string) (ProductListing, error)
	Delete(ctx context.Context, id string, force bool) error
	Update(ctx context.Context, id string, args *ProductListingArgs) (ProductListing, error)
	List(ctx context.Context, arg *ListArg) ([]*ProductListing, error)
	PushToChannel(ctx context.Context, arg *PushToChannelArg) (ProductListing, error)
	SalesChannelProductEvent(ctx context.Context, arg *SalesChannelProductEventArg) (ProductListing, error)
	ProductsCenterProductEvent(ctx context.Context, id string, arg *ProductsCenterProductEventArg) (ProductListing, error)
	ProductsCenterProductDeleteEvent(ctx context.Context, id string) (ProductListing, error)
	GeneratePreview(ctx context.Context, pl *ProductListing) (*ProductListing, error)
	EditAttributes(ctx context.Context, id string, args *EditAttributesArgs) (ProductListing, error)
	AutoLink(ctx context.Context, arg *AutoLinkArg) (ProductListing, error)
	LinkOrUnlink(ctx context.Context, arg *LinkArg) (ProductListing, error)
	SalesChannelOrderVariantRelation(ctx context.Context, arg *SalesChannelOrderVariantRelationArg) (ProductListingRelation, error)
	UpdateRelations(ctx context.Context, id string, arg *UpdateRelationsArg) (ProductListing, error)

	// Internal use only
	ESProxyProductListingCount(ctx context.Context, query string) (int64, error)
	ESProxyProductListingSearch(ctx context.Context, query string) (*elastic.SearchResult, error)
	RefreshES(ctx context.Context, id string, ops ...EsOption) error
	NotifySearchableProductUpsertEvent(ctx context.Context, productsCenterProductIDs string) error
	PublishUpdate(ctx context.Context, id string) error
	UnMatchProduct(ctx context.Context, id string) (ProductListing, error)
	MatchProduct(ctx context.Context, id string) (ProductListing, error)
	CheckAndFixStatus(ctx context.Context, dryRun bool, processedLimitCount int) error
}

type serviceImpl struct {
	logger                     *log.Logger
	repo                       *repoConductor
	esRepo                     esRepo
	validate                   *validator.Validate
	taskService                task.Service
	redisClient                *redis.Client
	locker                     *redsync.Redsync
	calculatorService          calculators.Service
	productsCenterClient       *products_center.Client
	settingService             settings.Service
	categoryService            category.Service
	convertService             convert.Service
	databusService             databus.Service
	conf                       *config.Config
	searchableProductService   searchable_products.Service
	connectorService           connectors.Service
	feedEventsService          *events.FeedEventsService
	businessMonitoringExporter *bme.Client
	feedCliV1                  *feed.ClientV1
	feedCliV2                  *feed.ClientV2
}

func NewService(logger *log.Logger,
	cli *spannerx.Client,
	esCli *elastic.Client,
	redisClient *redis.Client,
	locker *redsync.Redsync,
	productsCenterClient *products_center.Client,
	settingService settings.Service,
	calculatorService calculators.Service,
	categoryService category.Service,
	taskService task.Service,
	convertService convert.Service,
	databusService databus.Service,
	conf *config.Config,
	searchableProductService searchable_products.Service,
	connectorService connectors.Service,
	feedEventsService *events.FeedEventsService,
	bmeCli *bme.Client,
	feedCliV1 *feed.ClientV1,
	feedCliV2 *feed.ClientV2,
) *serviceImpl {
	return &serviceImpl{
		logger:                     logger,
		repo:                       newRepoConductor(cli),
		esRepo:                     NewProductListingESRepo(esCli),
		validate:                   validator.New(),
		redisClient:                redisClient,
		locker:                     locker,
		calculatorService:          calculatorService,
		productsCenterClient:       productsCenterClient,
		settingService:             settingService,
		categoryService:            categoryService,
		taskService:                taskService,
		convertService:             convertService,
		databusService:             databusService,
		conf:                       conf,
		searchableProductService:   searchableProductService,
		connectorService:           connectorService,
		feedEventsService:          feedEventsService,
		businessMonitoringExporter: bmeCli,
		feedCliV1:                  feedCliV1,
		feedCliV2:                  feedCliV2,
	}
}
