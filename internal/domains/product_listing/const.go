package product_listing

import "time"

const (
	PredefinedFilterReadyToPublish             = "ready_to_publish"   // 允许同步商品
	PredefinedFilterInfoIncomplete             = "info_incomplete"    // 信息不完成
	PredefinedFilterPublishedFailed            = "published_failed"   // 同步任务失败
	PredefinedFilterEditsUnderReview           = "edits_under_review" // 商品改动同步到 sales channel 成功正在进行审核
	PredefinedFilterEditsNotApproved           = "edits_not_approved" // 商品改动 sales channel 审核不通过
	PredefinedFilterReviewFailed               = "review_failed"      // sales channel 商品首次同步审核失败
	PredefinedFilterFrozen                     = "frozen"
	PredefinedFilterCanAutoLink                = "auto_link"                // 可以 auto_link 的 spu
	PredefinedFilterCanSyncInventoryAndPrice   = "sync_inventory_and_price" // 可以同步 inventory 和 price spu
	PredefinedFilterCanSyncProductDetail       = "sync_product_detail"      // 可以同步 product detail spu
	InventoryFilterOutOfStock                  = "out_of_stock"
	InventoryFilterInStock                     = "in_stock"
	FuzzyQueryProductName                      = "product_name"                           // 按 product_name 模糊搜索
	FuzzyQuerySku                              = "sku"                                    // 按 sku 值模糊搜索
	FuzzyQueryProductsCenterProductId          = "product_center_product_id"              // 按 product_center_product_id 模糊查询
	FuzzyQuerySkuTitle                         = "sku_title"                              // 按 sku title 模糊搜索
	FuzzyQuerySalesChannelProductId            = "sales_channel_product_id"               // 按 sales_channel_product_id 模糊搜索
	FuzzyQueryProductNumber                    = "product_number"                         // 按 product_number 模糊搜索
	TermQueryOptionsValueDetailsSalesChannelID = "options_value_details_sales_channel_id" // 按 options.value_details.sales_channel_id 精确查询
	UncategoriedValueInEs                      = "0"                                      // 查询没有分配 category
)

const (
	IndexTemplate               = "pd_product_listings_index_template"
	createIndex                 = "pd_product_listings_"
	searchIndex                 = "pd_product_listings_all"
	esPaginationTypePage        = "page"
	esPaginationTypeSearchAfter = "search_after"
)

const (
	redisLockerDefaultTries     = 3
	redisLockerDefaultTime      = 10 * time.Second
	redisKeyPrefixUpdateProduct = "Update:Listing:"
	redisKeyPrefixPushToChannel = "PushToChannel:Listing:"
	redisKeyPrefixUpsert        = "Upsert:Listing:"
)

const (
	FromEventListingUpdateSetting              = "listing_update_setting"
	FromEventListingLinkVariant                = "listing_link_variant"
	FromEventListingUnlinkVariant              = "listing_unlink_variant"
	FromEventSalesChannelActiveOrSyncedVariant = "sales_channel_active_or_synced_variant"
)
