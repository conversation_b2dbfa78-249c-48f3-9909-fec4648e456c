package product_listing

import (
	"testing"

	"github.com/stretchr/testify/require"

	"github.com/AfterShip/pltf-pd-product-listings/internal/consts"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
)

func TestSheinPlatformPreCheck(t *testing.T) {
	tests := []struct {
		name     string
		arg      SalesChannelProductEventArg
		expected error
	}{
		{
			name: "No SKC information",
			arg: SalesChannelProductEventArg{
				Product: models.Product{
					Options: []*models.ProductOption{},
				},
			},
			expected: ErrSheinProductIsInvalid,
		},
		{
			name: "SKC information with no sales channel option ID",
			arg: SalesChannelProductEventArg{
				Product: models.Product{
					Options: []*models.ProductOption{
						{
							Position:             1,
							SalesChannelOptionID: "",
							ValueDetails: []models.ProductOptionValueDetail{
								{
									SalesChannelID: "1",
								},
							},
						},
					},
				},
			},
			expected: ErrSheinProductIsInvalid,
		},
		{
			name: "SKC information with no skcs",
			arg: SalesChannelProductEventArg{
				Product: models.Product{
					Options: []*models.ProductOption{
						{
							Position:             1,
							SalesChannelOptionID: "1",
							ValueDetails:         []models.ProductOptionValueDetail{},
						},
					},
				},
			},
			expected: ErrSheinProductIsInvalid,
		},
		{
			name: "skc information with no sales channel value ID",
			arg: SalesChannelProductEventArg{
				Product: models.Product{
					Options: []*models.ProductOption{
						{
							Position:             1,
							SalesChannelOptionID: "2",
							ValueDetails: []models.ProductOptionValueDetail{
								{
									SalesChannelID: "1",
								},
								{
									SalesChannelID: "2",
								},
							},
						},
					},
				},
			},
			expected: ErrSheinProductIsInvalid,
		},
		{
			name: "Valid SKC information",
			arg: SalesChannelProductEventArg{
				Product: models.Product{
					Options: []*models.ProductOption{
						{
							Position:             1,
							SalesChannelOptionID: "1",
							ValueDetails: []models.ProductOptionValueDetail{
								{
									State:               consts.ProductOptionValueStateActive,
									SalesChannelID:      "1",
									SalesChannelValueID: "1",
								},
								{
									State:               consts.ProductOptionValueStateActive,
									SalesChannelID:      "2",
									SalesChannelValueID: "2",
								},
							},
						},
					},
				},
			},
			expected: nil,
		},
		{
			name: "Valid SKC information with part sales channel value id",
			arg: SalesChannelProductEventArg{
				Product: models.Product{
					Options: []*models.ProductOption{
						{
							Position:             1,
							SalesChannelOptionID: "1",
							ValueDetails: []models.ProductOptionValueDetail{
								{
									State:               consts.ProductOptionValueStateActive,
									SalesChannelID:      "1",
									SalesChannelValueID: "1",
								},
								{
									State:          consts.ProductOptionValueStateActive,
									SalesChannelID: "2",
								},
							},
						},
					},
				},
			},
			expected: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.arg.sheinPlatformCreatePreCheck()
			require.Equal(t, tt.expected, err)
		})
	}
}

func TestGetMainOptionVariants(t *testing.T) {
	tests := []struct {
		name     string
		product  models.Product
		expected map[string][]*models.ProductVariant
	}{
		{
			name: "No options",
			product: models.Product{
				Options:  []*models.ProductOption{},
				Variants: []*models.ProductVariant{},
			},
			expected: nil,
		},
		{
			name: "Main option with variants",
			product: models.Product{
				Options: []*models.ProductOption{
					{SalesChannelOptionID: "option1", Position: 1, ValueDetails: []models.ProductOptionValueDetail{
						{SalesChannelValueID: "value1"},
					}},
				},
				Variants: []*models.ProductVariant{
					{ID: "variant1", Options: []*models.ProductVariantOption{
						{SalesChannelOptionID: "option1", SalesChannelValueID: "value1"},
					}},
				},
			},
			expected: map[string][]*models.ProductVariant{
				"value1": {
					{ID: "variant1", Options: []*models.ProductVariantOption{
						{SalesChannelOptionID: "option1", SalesChannelValueID: "value1"},
					}},
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pl := &ProductListing{Product: tt.product}
			result := pl.GetMainOptionVariants()
			require.Equal(t, tt.expected, result)
		})
	}
}

func TestGetOptionVariants(t *testing.T) {
	tests := []struct {
		name             string
		product          models.Product
		optionID         string
		optionValueID    string
		expectedVariants []*models.ProductVariant
	}{
		{
			name: "No variants",
			product: models.Product{
				Variants: []*models.ProductVariant{},
			},
			optionID:         "option1",
			optionValueID:    "value1",
			expectedVariants: []*models.ProductVariant{},
		},
		{
			name: "No matching variants",
			product: models.Product{
				Variants: []*models.ProductVariant{
					{ID: "variant1", Options: []*models.ProductVariantOption{
						{SalesChannelOptionID: "option2", SalesChannelValueID: "value2"},
					}},
				},
			},
			optionID:         "option1",
			optionValueID:    "value1",
			expectedVariants: []*models.ProductVariant{},
		},
		{
			name: "Matching variants",
			product: models.Product{
				Variants: []*models.ProductVariant{
					{ID: "variant1", Options: []*models.ProductVariantOption{
						{SalesChannelOptionID: "option1", SalesChannelValueID: "value1"},
					}},
					{ID: "variant2", Options: []*models.ProductVariantOption{
						{SalesChannelOptionID: "option1", SalesChannelValueID: "value1"},
					}},
				},
			},
			optionID:      "option1",
			optionValueID: "value1",
			expectedVariants: []*models.ProductVariant{
				{ID: "variant1", Options: []*models.ProductVariantOption{
					{SalesChannelOptionID: "option1", SalesChannelValueID: "value1"},
				}},
				{ID: "variant2", Options: []*models.ProductVariantOption{
					{SalesChannelOptionID: "option1", SalesChannelValueID: "value1"},
				}},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pl := &ProductListing{Product: tt.product}
			result := pl.GetOptionVariants(tt.optionID, tt.optionValueID)
			require.Equal(t, tt.expectedVariants, result)
		})
	}
}
