package product_listing

import (
	"context"
	"encoding/json"

	"github.com/go-playground/validator/v10"
	"go.uber.org/zap"

	"github.com/AfterShip/gopkg/log"

	"github.com/AfterShip/pltf-pd-product-listings/internal/consts"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
	"github.com/AfterShip/pltf-pd-product-listings/internal/utils/slicex"
)

type EditAttributesTaskInput struct {
	OrganizationID    string                               `json:"organization_id" validate:"required"`
	SalesChannel      models.SalesChannel                  `json:"sales_channel" validate:"required"`
	ProductListingIDs []string                             `json:"product_listing_ids" validate:"required"`
	CategorySourceID  string                               `json:"category_source_id"`
	SizeChart         models.ProductSizeChart              `json:"size_chart"`
	Certifications    []models.ProductCertification        `json:"certifications"`
	Attributes        []models.ProductAttribute            `json:"attributes"`
	Length            models.ProductVariantShippingSetting `json:"length"`
	Width             models.ProductVariantShippingSetting `json:"width"`
	Height            models.ProductVariantShippingSetting `json:"height"`
	Weight            models.ProductVariantShippingSetting `json:"weight"`
	Brand             models.SalesChannelResource          `json:"brand"`
	Compliance        models.ProductCompliance             `json:"compliance"`
}

type EditAttributesTaskOutput struct {
	TotalCount     int `json:"total_count"`
	SucceededCount int `json:"succeeded_count"`
	FailedCount    int `json:"failed_count"`
}

type EditAttributesTask struct {
	Logger    *log.Logger
	Validator *validator.Validate
}

func (t *EditAttributesTask) validate(input *EditAttributesTaskInput) error {
	if err := t.Validator.Struct(input); err != nil {
		return err
	}

	if len(input.ProductListingIDs) == 0 {
		return ErrNoProductListingID
	}

	return nil
}

func (t *EditAttributesTask) BuildTaskArgs(ctx context.Context, input models.TaskInput) (models.TaskArgs, error) {
	args, ok := input.(*EditAttributesTaskInput)
	if !ok {
		return models.TaskArgs{}, ErrUnprocessableEntity
	}

	if err := t.validate(args); err != nil {
		return models.TaskArgs{}, err
	}

	taskArgs := models.TaskArgs{
		GroupName:  consts.BatchEditProductListingsProductAttributes,
		ResourceID: args.OrganizationID,
		StoreKey:   args.SalesChannel.StoreKey,
		Platform:   args.SalesChannel.Platform,
		Type:       consts.BatchTaskType,
	}

	inputs := make([]models.TaskInput, 0)
	splitIDs := slicex.SplitSlice(args.ProductListingIDs, 50)
	for i := range splitIDs {
		inputs = append(inputs, &EditAttributesTaskInput{
			OrganizationID:    args.OrganizationID,
			SalesChannel:      args.SalesChannel,
			ProductListingIDs: splitIDs[i],
			CategorySourceID:  args.CategorySourceID,
			SizeChart:         args.SizeChart,
			Certifications:    args.Certifications,
			Attributes:        args.Attributes,
			Length:            args.Length,
			Width:             args.Width,
			Height:            args.Height,
			Weight:            args.Weight,
			Brand:             args.Brand,
			Compliance:        args.Compliance,
		})
	}
	taskArgs.Inputs = inputs

	// If there are multiple IDs, we need to use the organization ID as the concurrency key
	if len(args.ProductListingIDs) > 1 {
		taskArgs.ConcurrencyKey = args.OrganizationID
	}

	return taskArgs, nil
}

// nolint:dupl
func (t *EditAttributesTask) ParseOutput(ctx context.Context, task *models.Task) models.TaskOutput {
	output := EditAttributesTaskOutput{}
	for i := range task.ChildTasks {
		// get total count
		if task.ChildTasks[i].Inputs != "" {
			input := &EditAttributesTaskInput{}
			if err := json.Unmarshal([]byte(task.ChildTasks[i].Inputs), input); err != nil {
				t.Logger.With(zap.String("Id", task.ChildTasks[i].ID)).WarnCtx(ctx, "Failed to parse edit attributes task input", zap.Error(err))
			}
			output.TotalCount += len(input.ProductListingIDs)
		}

		// get child task output
		if task.ChildTasks[i].Outputs.Data != "" {
			childTaskOutput := EditAttributesTaskOutput{}
			if err := json.Unmarshal([]byte(task.ChildTasks[i].Outputs.Data), &childTaskOutput); err != nil {
				t.Logger.With(zap.String("Id", task.ChildTasks[i].ID)).WarnCtx(ctx, "Failed to parse edit attributes task output", zap.Error(err))
			}
			output.FailedCount += childTaskOutput.FailedCount
			output.SucceededCount += childTaskOutput.SucceededCount
		}
	}

	return output
}
