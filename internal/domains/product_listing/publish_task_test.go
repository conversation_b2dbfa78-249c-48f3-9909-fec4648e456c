package product_listing

import (
	"testing"

	"github.com/go-playground/validator/v10"
	"github.com/stretchr/testify/assert"

	"github.com/AfterShip/gopkg/log"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
)

func TestPublishTask_validate(t *testing.T) {
	tests := []struct {
		name    string
		input   *PublishTaskInput
		wantErr error
	}{
		{
			name: "有效输入",
			input: &PublishTaskInput{
				OrganizationID: "org123",
				SalesChannel: models.SalesChannel{
					Platform: "shein",
					StoreKey: "store123",
				},
				ProductListingIDs: []string{"listing1", "listing2"},
				App: models.App{
					Platform: "shopify",
					Key:      "app123",
				},
			},
			wantErr: nil,
		},
		{
			name: "OrganizationID为空",
			input: &PublishTaskInput{
				OrganizationID: "",
				SalesChannel: models.SalesChannel{
					Platform: "shein",
					StoreKey: "store123",
				},
				ProductListingIDs: []string{"listing1", "listing2"},
			},
			wantErr: validator.ValidationErrors{},
		},
		{
			name: "ProductListingIDs为空切片",
			input: &PublishTaskInput{
				OrganizationID: "org123",
				SalesChannel: models.SalesChannel{
					Platform: "shein",
					StoreKey: "store123",
				},
				ProductListingIDs: []string{},
			},
			wantErr: ErrNoProductListingID,
		},
		{
			name: "ProductListingIDs为nil",
			input: &PublishTaskInput{
				OrganizationID: "org123",
				SalesChannel: models.SalesChannel{
					Platform: "shein",
					StoreKey: "store123",
				},
				ProductListingIDs: nil,
			},
			wantErr: validator.ValidationErrors{},
		},
		{
			name: "App为空（可选字段）",
			input: &PublishTaskInput{
				OrganizationID: "org123",
				SalesChannel: models.SalesChannel{
					Platform: "shein",
					StoreKey: "store123",
				},
				ProductListingIDs: []string{"listing1"},
				App:               models.App{},
			},
			wantErr: nil,
		},
		{
			name: "单个ProductListingID",
			input: &PublishTaskInput{
				OrganizationID: "org123",
				SalesChannel: models.SalesChannel{
					Platform: "shein",
					StoreKey: "store123",
				},
				ProductListingIDs: []string{"listing1"},
			},
			wantErr: nil,
		},
		{
			name: "多个ProductListingID",
			input: &PublishTaskInput{
				OrganizationID: "org123",
				SalesChannel: models.SalesChannel{
					Platform: "shein",
					StoreKey: "store123",
				},
				ProductListingIDs: []string{"listing1", "listing2", "listing3"},
			},
			wantErr: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			task := &PublishTask{
				Logger:    log.GlobalLogger(),
				Validator: validator.New(),
			}
			err := task.validate(tt.input)

			if tt.wantErr != nil {
				// 如果期望错误是validator.ValidationErrors类型，则进行类型断言
				if _, ok := tt.wantErr.(validator.ValidationErrors); ok {
					assert.IsType(t, validator.ValidationErrors{}, err)
				} else {
					assert.Equal(t, tt.wantErr, err)
				}
			} else {
				assert.NoError(t, err)
			}
		})
	}
}
