package product_listing

import (
	"testing"

	"github.com/go-playground/validator/v10"
	"github.com/stretchr/testify/assert"

	"github.com/AfterShip/gopkg/log"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
)

func TestEditAttributesTask_validate(t *testing.T) {
	tests := []struct {
		name    string
		input   *EditAttributesTaskInput
		wantErr error
	}{
		{
			name: "有效输入",
			input: &EditAttributesTaskInput{
				OrganizationID: "org123",
				SalesChannel: models.SalesChannel{
					Platform: "tiktok",
					StoreKey: "store123",
				},
				ProductListingIDs: []string{"pl1", "pl2"},
				CategorySourceID:  "cat123",
				SizeChart: models.ProductSizeChart{
					Attributes: []models.ProductSizeChartAttribute{
						{
							SalesChannelValueID: "size123",
						},
					},
				},
				Certifications: []models.ProductCertification{
					{
						SalesChannelID: "size123",
					},
				},
				Attributes: []models.ProductAttribute{
					{
						Name:           "color",
						SalesChannelID: "color123",
					},
				},
			},
			wantErr: nil,
		},
		{
			name: "OrganizationID为空",
			input: &EditAttributesTaskInput{
				OrganizationID: "",
				SalesChannel: models.SalesChannel{
					Platform: "tiktok",
					StoreKey: "store123",
				},
				ProductListingIDs: []string{"pl1", "pl2"},
			},
			wantErr: validator.ValidationErrors{},
		},
		{
			name: "ProductListingIDs为nil",
			input: &EditAttributesTaskInput{
				OrganizationID: "org123",
				SalesChannel: models.SalesChannel{
					Platform: "tiktok",
					StoreKey: "store123",
				},
				ProductListingIDs: nil,
			},
			wantErr: validator.ValidationErrors{},
		},
		{
			name: "ProductListingIDs为空数组",
			input: &EditAttributesTaskInput{
				OrganizationID: "org123",
				SalesChannel: models.SalesChannel{
					Platform: "tiktok",
					StoreKey: "store123",
				},
				ProductListingIDs: []string{},
			},
			wantErr: ErrNoProductListingID,
		},
		{
			name: "可选字段为空",
			input: &EditAttributesTaskInput{
				OrganizationID: "org123",
				SalesChannel: models.SalesChannel{
					Platform: "tiktok",
					StoreKey: "store123",
				},
				ProductListingIDs: []string{"pl1", "pl2"},
				CategorySourceID:  "",
				SizeChart:         models.ProductSizeChart{},
				Certifications:    nil,
				Attributes:        nil,
			},
			wantErr: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			task := &EditAttributesTask{
				Logger:    log.GlobalLogger(),
				Validator: validator.New(),
			}
			err := task.validate(tt.input)
			if tt.wantErr != nil {
				if _, ok := tt.wantErr.(validator.ValidationErrors); ok {
					assert.IsType(t, validator.ValidationErrors{}, err)
				} else {
					assert.Equal(t, tt.wantErr, err)
				}
			} else {
				assert.NoError(t, err)
			}
		})
	}
}
