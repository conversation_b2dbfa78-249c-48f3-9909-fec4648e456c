package product_listing

import (
	"github.com/AfterShip/pltf-pd-product-listings/internal/consts"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/settings"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
)

type storeProductListingSetting struct {
	storeSetting          settings.Setting
	productListingSetting SyncSettings
}

// nolint:gocritic
func newStoreProductListingSetting(storeSetting settings.Setting, productListingSetting SyncSettings) *storeProductListingSetting {
	// 超卖设置看 store setting
	productListingSetting.InventorySyncSetting.Customized.FollowSourceAllowBackorder = storeSetting.InventorySync.FollowSourceAllowBackorder
	// warehouse 设置看 store setting
	productListingSetting.InventorySyncSetting.Customized.ActiveWarehouses = storeSetting.InventorySync.ActiveWarehouses

	return &storeProductListingSetting{
		storeSetting:          storeSetting,
		productListingSetting: productListingSetting,
	}
}

func (s *storeProductListingSetting) autoSyncProductDetailField(field consts.ProductDetailField) bool {
	fields := make([]string, 0)

	if s.productListingSetting.ProductSyncSetting.Preference == consts.SettingPreferenceCustomized {
		if s.productListingSetting.ProductSyncSetting.Customized.UpdateDetail.AutoSync == consts.StateEnabled {
			fields = s.productListingSetting.ProductSyncSetting.Customized.UpdateDetail.Fields
		}
	} else {
		if s.storeSetting.ProductSync.UpdateDetail.AutoSync == consts.StateEnabled {
			fields = s.storeSetting.ProductSync.UpdateDetail.Fields
		}
	}

	for _, f := range fields {
		if f == field.String() {
			return true
		}
	}

	return false
}

func (s *storeProductListingSetting) autoSyncVariant() bool {
	if s.productListingSetting.ProductSyncSetting.Preference == consts.SettingPreferenceCustomized {
		return s.productListingSetting.ProductSyncSetting.Customized.UpdateVariants.AutoSync == consts.StateEnabled
	}

	return s.storeSetting.ProductSync.UpdateVariants.AutoSync == consts.StateEnabled
}

func (s *storeProductListingSetting) getPriceSyncSetting() models.PriceSync {
	if s.productListingSetting.PriceSyncSetting.Preference == consts.SettingPreferenceCustomized {
		return s.productListingSetting.PriceSyncSetting.Customized
	}

	return s.storeSetting.PriceSync
}

func (s *storeProductListingSetting) getInventorySyncSetting() models.InventorySync {
	if s.productListingSetting.InventorySyncSetting.Preference == consts.SettingPreferenceCustomized {
		inventorySyncSetting := s.productListingSetting.InventorySyncSetting.Customized

		// ActiveWarehouses follow store setting, not support customized setting
		inventorySyncSetting.ActiveWarehouses = s.storeSetting.InventorySync.ActiveWarehouses
		return inventorySyncSetting
	}

	return s.storeSetting.InventorySync
}

func getDefaultProductListingSetting(platform string) SyncSettings {
	setting := SyncSettings{
		ProductSyncSetting: ProductSyncSetting{
			Preference: consts.SettingPreferenceStore,
		},
		PriceSyncSetting: PriceSyncSetting{
			Preference: consts.SettingPreferenceStore,
		},
		InventorySyncSetting: InventorySyncSetting{
			Preference: consts.SettingPreferenceStore,
		},
	}

	switch platform {
	case consts.Shein:
		// shein 默认关闭同步 detail 和 variants
		setting.ProductSyncSetting.Preference = consts.SettingPreferenceCustomized
		setting.ProductSyncSetting.Customized = models.ProductSync{
			UpdateDetail: models.UpdateDetail{
				AutoSync: consts.StateDisabled,
			},
			UpdateVariants: models.UpdateVariants{
				AutoSync: consts.StateDisabled,
			},
		}
	default:
		// do nothing
	}

	return setting
}
