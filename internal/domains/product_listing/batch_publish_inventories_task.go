// nolint:dupl
package product_listing

import (
	"context"
	"time"

	"github.com/go-playground/validator/v10"

	"github.com/AfterShip/gopkg/log"

	"github.com/AfterShip/pltf-pd-product-listings/internal/consts"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
)

type BatchPublishInventoriesTaskInput struct {
	Organization   models.Organization `json:"organization" validate:"required"`
	SalesChannel   models.SalesChannel `json:"sales_channel" validate:"required"`
	Source         models.Source       `json:"source" validate:"omitempty"`
	FromEvent      string              `json:"from_event" validate:"required"` // 事件来源，日志记录用
	Enforced       bool                `json:"enforced"`                       // 忽略防超卖逻辑限制，强制更新库存
	EventTimestamp time.Time           `json:"event_timestamp"`
	// jobs 批量处理，mutation 最多支持 500
	ProductListings []inventoryProductListing `json:"product_listings" validate:"required,max=500,dive"`
}

type inventoryProductListing struct {
	ID        string                                `json:"id" validate:"required"`
	Relations []inventoryTaskProductListingRelation `json:"relations" validate:"required,dive"`
}

type BatchPublishInventoriesTaskOutput struct {
	ChildJobIDs []string `json:"child_job_ids"`
}

type BatchPublishInventoriesTask struct {
	Logger    *log.Logger
	Validator *validator.Validate
}

func (t *BatchPublishInventoriesTask) validate(input *BatchPublishInventoriesTaskInput) error {
	if err := t.Validator.Struct(input); err != nil {
		return err
	}
	if input.Organization.ID == "" {
		return ErrUnprocessableEntity
	}
	if input.SalesChannel.Platform == "" {
		return ErrUnprocessableEntity
	}
	if input.SalesChannel.StoreKey == "" {
		return ErrUnprocessableEntity
	}
	if input.EventTimestamp.Unix() <= 0 {
		return ErrUnprocessableEntity
	}

	return nil
}

// nolint:dupl
func (t *BatchPublishInventoriesTask) BuildTaskArgs(ctx context.Context, input models.TaskInput) (models.TaskArgs, error) {
	args, ok := input.(*BatchPublishInventoriesTaskInput)
	if !ok {
		return models.TaskArgs{}, ErrUnprocessableEntity
	}

	if err := t.validate(args); err != nil {
		return models.TaskArgs{}, err
	}

	// set default event timestamp
	if args.EventTimestamp.Unix() <= 0 {
		args.EventTimestamp = time.Now()
	}
	taskArgs := models.TaskArgs{
		GroupName:      consts.BatchPublishInventories,
		Type:           consts.BatchTaskType,
		OrganizationID: args.Organization.ID,
		StoreKey:       args.SalesChannel.StoreKey,
		Platform:       args.SalesChannel.Platform,
		// DO NOT SET ConcurrencyKey
		// 每 5 个子任务并发执行,
		// 其中 BatchPublishInventories group 设置的 Concurrency 为 10，第 11 个需要等前面 10 个全部执行完成才会被调度
		// 相当于有 50 个子任务同时执行
		ConcurrencyLimit: 5,
	}
	productListingIDsMap := make(map[string]struct{})
	// 组装任务，结构保持与 PublishInventoriesTask 一致
	inputs := make([]models.TaskInput, 0)
	for i := range args.ProductListings {
		productListingID := args.ProductListings[i].ID
		if _, ok := productListingIDsMap[productListingID]; ok {
			continue
		}
		inputs = append(inputs, &PublishInventoriesTaskInput{
			Organization:            args.Organization,
			Source:                  args.Source,
			SalesChannel:            args.SalesChannel,
			FromEvent:               args.FromEvent,
			Enforced:                args.Enforced,
			ProductListingID:        productListingID,
			EventTimestamp:          args.EventTimestamp,
			ProductListingRelations: args.ProductListings[i].Relations,
		})
		productListingIDsMap[productListingID] = struct{}{}
	}
	taskArgs.Inputs = inputs
	return taskArgs, nil
}

func (t *BatchPublishInventoriesTask) ParseOutput(ctx context.Context, task *models.Task) models.TaskOutput {
	outputs := BatchPublishInventoriesTaskOutput{}
	outputs.ChildJobIDs = task.ChildTaskIDs
	return outputs
}
