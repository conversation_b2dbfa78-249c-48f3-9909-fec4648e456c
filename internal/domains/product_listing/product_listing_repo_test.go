package product_listing

import (
	"context"
	"fmt"
	"testing"

	"github.com/spf13/viper"
	"github.com/stretchr/testify/require"

	"github.com/AfterShip/gopkg/cfg"
	"github.com/AfterShip/gopkg/storage/spannerx"
	"github.com/AfterShip/gopkg/uuid"

	"github.com/AfterShip/pltf-pd-product-listings/internal/config"
)

func Test_ProductListingRepoImpl(t *testing.T) {
	cfgs := new(config.Config)
	_, err := cfg.LoadViperConfig(cfgs, func(v *viper.Viper) { v.AddConfigPath("../../../cmd/apiserver/conf") })
	require.NoError(t, err)

	ctx := context.Background()

	database := fmt.Sprintf("projects/%s/instances/%s/databases/%s",
		cfgs.Spanner.Project, cfgs.Spanner.Instance, cfgs.Spanner.Database)
	spannerCli, err := spannerx.NewClient(ctx, database)
	require.NoError(t, err)
	plRepo := productListingRepo{
		cli: spannerCli,
	}
	pl := productListingDBModel{
		ProductListingID:     uuid.GenerateUUIDV4(),
		OrganizationID:       uuid.GenerateUUIDV4(),
		SalesChannelStoreKey: "test",
		SalesChannelPlatform: "tiktok-shop",
	}

	err = plRepo.create(ctx, &pl)
	require.NoError(t, err)
	require.Equal(t, true, pl.DeletedAt.IsNull())
	require.Equal(t, true, pl.PendingDeletedAt.IsNull())

	// Duplicate create
	err = plRepo.create(ctx, &pl)
	require.Error(t, err)

	pl.State = "active"
	err = plRepo.update(ctx, &pl)
	require.NoError(t, err)

	found, err := plRepo.getByID(ctx, pl.ProductListingID)
	require.NoError(t, err)
	require.Equal(t, pl.ProductListingID, found.ProductListingID)
	require.Equal(t, pl.State, found.State)

	// get list by ids
	searchIDs := make([]string, 0)
	for i := 0; i < 10; i++ {
		id := uuid.GenerateUUIDV4()
		err = plRepo.create(ctx, &productListingDBModel{
			ProductListingID:     id,
			OrganizationID:       uuid.GenerateUUIDV4(),
			SalesChannelStoreKey: "test",
			SalesChannelPlatform: "tiktok-shop",
		})
		require.NoError(t, err)
		if i%2 == 0 {
			searchIDs = append(searchIDs, id)
		}
	}
	pls, err := plRepo.listByIDs(ctx, searchIDs)
	require.NoError(t, err)
	require.Len(t, pls, 5)
	for _, pl := range pls {
		require.Contains(t, searchIDs, pl.ProductListingID)
	}

	// get list by ids with not found
	searchIDs = append(searchIDs, "not_found")
	pls, err = plRepo.listByIDs(ctx, searchIDs)
	require.NoError(t, err)
	require.Len(t, pls, 5)

	// Not found
	notFound, err := plRepo.getByID(ctx, "not_found")
	require.Error(t, err)
	require.Equal(t, "", notFound.ProductListingID)

	// Delete
	err = plRepo.delete(ctx, pl.ProductListingID)
	require.NoError(t, err)
	require.NotNil(t, pl.DeletedAt)

	// force delete
	err = plRepo.forceDelete(ctx, pl.ProductListingID)
	require.NoError(t, err)
	notFound, err = plRepo.getByID(ctx, pl.ProductListingID)
	require.Error(t, err)
	require.Equal(t, "", notFound.ProductListingID)
}

func Test_fetchListIndex(t *testing.T) {
	testCase := []struct {
		arg      *repoListArgs
		expected string
	}{
		{
			arg: &repoListArgs{
				OrganizationID: "org",
			},
			expected: "",
		},
		{
			arg: &repoListArgs{
				OrganizationID:           "org",
				ProductsCenterProductIDs: []string{"1", "2"},
			},
			expected: productsCenterProductIDIndex,
		},
		{
			arg: &repoListArgs{
				OrganizationID:         "org",
				SalesChannelProductIDs: []string{"1", "2"},
			},
			expected: salesChannelProductIDIndex,
		},
	}

	for _, tc := range testCase {
		require.Equal(t, tc.expected, fetchListIndex(tc.arg))
	}
}
