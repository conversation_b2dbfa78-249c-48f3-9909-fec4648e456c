package product_listing

import (
	"context"
	"encoding/json"
	"time"

	"go.uber.org/zap"

	"github.com/AfterShip/gopkg/log"
	"github.com/AfterShip/pltf-pd-product-listings/internal/utils/slicex"

	"github.com/AfterShip/pltf-pd-product-listings/internal/consts"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
)

type PublishInventoriesTaskInput struct {
	Organization            models.Organization                   `json:"organization" validate:"required"`
	SalesChannel            models.SalesChannel                   `json:"sales_channel" validate:"required"`
	Source                  models.Source                         `json:"source" validate:"omitempty"`
	ProductListingID        string                                `json:"product_listing_id" validate:"required"`
	FromEvent               string                                `json:"from_event" validate:"required"` // 事件来源，日志记录用
	Enforced                bool                                  `json:"enforced"`                       // 忽略防超卖逻辑限制，强制更新库存
	EventTimestamp          time.Time                             `json:"event_timestamp"`
	ProductListingRelations []inventoryTaskProductListingRelation `json:"product_listing_relations" validate:"required,dive"`
}

type inventoryTaskProductListingRelation struct {
	ID                 string              `json:"id" validate:"required"`
	ChannelProductID   string              `json:"channel_product_id"`
	ChannelVariantID   string              `json:"channel_variant_id"`
	SourceProductID    string              `json:"source_product_id"`
	SourceVariantID    string              `json:"source_variant_id"`
	SetOutOfStock      bool                `json:"set_out_of_stock"`                                                          // 商品下架，指定库存为0
	QuantityTrend      string              `json:"quantity_trend" validate:"omitempty,oneof=increment decrement no_change"`   // 库存变化趋
	OverSellTrend      string              `json:"oversell_trend" validate:"omitempty,oneof=allow deny no_change"`            // 超卖变化趋势
	VariantStatusTrend string              `json:"variant_status_trend" validate:"omitempty,oneof=active inactive no_change"` // 变体状态变化趋势
	OverWriteInventory *OverWriteInventory `json:"overwrite_inventory"`
}

type OverWriteInventory struct {
	Quantity float64 `json:"quantity"`
}

type PublishInventoriesTaskOutput struct {
	Organization            models.Organization                   `json:"organization" validate:"required"`
	SalesChannel            models.SalesChannel                   `json:"sales_channel" validate:"required"`
	Source                  models.Source                         `json:"source" validate:"omitempty"`
	ProductListingID        string                                `json:"product_listing_id" validate:"required"`
	FromEvent               string                                `json:"from_event" validate:"required"` // 事件来源，日志记录用
	Enforced                bool                                  `json:"enforced"`                       // 忽略防超卖逻辑限制，强制更新库存
	EventTimestamp          time.Time                             `json:"event_timestamp"`
	ProductListingRelations []inventoryTaskProductListingRelation `json:"product_listing_relations" validate:"required,dive"`
}

type PublishInventoriesTask struct {
	Logger *log.Logger
}

func (t *PublishInventoriesTask) validate(input *PublishInventoriesTaskInput) error {
	if input.Organization.ID == "" {
		return ErrUnprocessableEntity
	}
	if input.SalesChannel.Platform == "" {
		return ErrUnprocessableEntity
	}
	if input.SalesChannel.StoreKey == "" {
		return ErrUnprocessableEntity
	}
	if input.ProductListingID == "" {
		return ErrNoProductListingID
	}
	if input.EventTimestamp.Unix() <= 0 {
		return ErrUnprocessableEntity
	}

	return nil
}

// nolint:dupl
func (t *PublishInventoriesTask) BuildTaskArgs(ctx context.Context, input models.TaskInput) (models.TaskArgs, error) {
	args, ok := input.(*PublishInventoriesTaskInput)
	if !ok {
		return models.TaskArgs{}, ErrUnprocessableEntity
	}

	if err := t.validate(args); err != nil {
		return models.TaskArgs{}, err
	}
	// set default event timestamp
	if args.EventTimestamp.Unix() <= 0 {
		args.EventTimestamp = time.Now()
	}
	taskArgs := models.TaskArgs{
		GroupName:      consts.PublishInventories,
		Type:           consts.SingleTaskType,
		OrganizationID: args.Organization.ID,
		StoreKey:       args.SalesChannel.StoreKey,
		Platform:       args.SalesChannel.Platform,
		ResourceID:     args.ProductListingID,
		Inputs:         args,
	}
	return taskArgs, nil
}

func (t *PublishInventoriesTask) ParseOutput(ctx context.Context, task *models.Task) models.TaskOutput {
	outputs := PublishInventoriesTaskOutput{}
	if err := json.Unmarshal([]byte(task.Inputs), &outputs); err != nil {
		t.Logger.With(zap.String("Id", task.ID)).WarnCtx(ctx, "Failed to parse publish inventories task output", zap.Error(err))
	}
	return outputs
}

type InventoriesTaskArg struct {
	Listing          *ProductListing
	ClearRelationIDs []string
	FromEvent        string
	Enforced         bool
}

func (p *InventoriesTaskArg) BuildTaskInput() *PublishInventoriesTaskInput {
	relations := make([]inventoryTaskProductListingRelation, 0)

	sourceAppPlatform := p.Listing.ProductsCenterProduct.Source.Platform
	sourceAppKey := p.Listing.ProductsCenterProduct.Source.StoreKey

	for i := range p.Listing.Relations {
		if sourceAppPlatform == "" &&
			p.Listing.Relations[i].ProductsCenterVariant.Source.Platform != "" {
			sourceAppPlatform = p.Listing.Relations[i].ProductsCenterVariant.Source.Platform
		}
		if sourceAppKey == "" &&
			p.Listing.Relations[i].ProductsCenterVariant.Source.StoreKey != "" {
			sourceAppKey = p.Listing.Relations[i].ProductsCenterVariant.Source.StoreKey
		}

		if slicex.ContainsString(p.ClearRelationIDs, p.Listing.Relations[i].ID) {
			// 清理库存
			relations = append(relations, inventoryTaskProductListingRelation{
				ID:               p.Listing.Relations[i].ID,
				ChannelProductID: p.Listing.Relations[i].SalesChannelVariant.ProductID,
				ChannelVariantID: p.Listing.Relations[i].SalesChannelVariant.ID,
				SourceProductID:  p.Listing.Relations[i].ProductsCenterVariant.ProductID,
				SourceVariantID:  p.Listing.Relations[i].ProductsCenterVariant.ID,
				SetOutOfStock:    true,
			})
		} else {
			// 同步库存
			if p.Listing.Relations[i].LinkStatus != consts.LinkStatusLinked ||
				p.Listing.Relations[i].SyncStatus != consts.SyncStatusSynced {
				continue
			}
			relations = append(relations, inventoryTaskProductListingRelation{
				ID:               p.Listing.Relations[i].ID,
				ChannelProductID: p.Listing.Relations[i].SalesChannelVariant.ProductID,
				ChannelVariantID: p.Listing.Relations[i].SalesChannelVariant.ID,
				SourceProductID:  p.Listing.Relations[i].ProductsCenterVariant.ProductID,
				SourceVariantID:  p.Listing.Relations[i].ProductsCenterVariant.ID,
			})
		}
	}

	return &PublishInventoriesTaskInput{
		Organization: p.Listing.Organization,
		SalesChannel: p.Listing.SalesChannel,
		Source: models.Source{
			App: models.App{
				Platform: sourceAppPlatform,
				Key:      sourceAppKey,
			},
			ID: p.Listing.ID,
		},
		ProductListingID:        p.Listing.ID,
		Enforced:                p.Enforced,
		FromEvent:               p.FromEvent,
		EventTimestamp:          time.Now(),
		ProductListingRelations: relations,
	}
}
