// nolint:dupl
package product_listing

import (
	"context"
	"time"

	"github.com/go-playground/validator/v10"

	"github.com/AfterShip/gopkg/log"

	"github.com/AfterShip/pltf-pd-product-listings/internal/consts"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
)

type BatchPublishPricesTaskInput struct {
	Organization       models.Organization `json:"organization" validate:"required"`
	SalesChannel       models.SalesChannel `json:"sales_channel" validate:"required"`
	Source             models.Source       `json:"source" validate:"omitempty"`
	SalesChannelRegion string              `json:"sales_channel_region"`
	FromEvent          string              `json:"from_event" validate:"required"` // 事件来源，日志记录用
	EventTimestamp     time.Time           `json:"event_timestamp"`
	// jobs 批量处理，mutation 最多支持 500
	ProductListings []priceProductListing `json:"product_listings" validate:"required,max=500,dive"`
}

type priceProductListing struct {
	ID        string                            `json:"id" validate:"required"`
	Relations []priceTaskProductListingRelation `json:"relations" validate:"required,dive"`
}

type BatchPublishPricesTaskOutput struct {
	ChildJobIDs []string `json:"child_job_ids"`
}

type BatchPublishPricesTask struct {
	Logger    *log.Logger
	Validator *validator.Validate
}

func (t *BatchPublishPricesTask) validate(input *BatchPublishPricesTaskInput) error {
	if err := t.Validator.Struct(input); err != nil {
		return err
	}
	if input.Organization.ID == "" {
		return ErrUnprocessableEntity
	}
	if input.SalesChannel.Platform == "" {
		return ErrUnprocessableEntity
	}
	if input.SalesChannel.StoreKey == "" {
		return ErrUnprocessableEntity
	}
	if input.EventTimestamp.Unix() <= 0 {
		return ErrUnprocessableEntity
	}

	return nil
}

// nolint:dupl
func (t *BatchPublishPricesTask) BuildTaskArgs(ctx context.Context, input models.TaskInput) (models.TaskArgs, error) {
	args, ok := input.(*BatchPublishPricesTaskInput)
	if !ok {
		return models.TaskArgs{}, ErrUnprocessableEntity
	}

	if err := t.validate(args); err != nil {
		return models.TaskArgs{}, err
	}
	// set default event timestamp
	if args.EventTimestamp.Unix() <= 0 {
		args.EventTimestamp = time.Now()
	}
	taskArgs := models.TaskArgs{
		GroupName:      consts.BatchPublishPrices,
		Type:           consts.BatchTaskType,
		OrganizationID: args.Organization.ID,
		StoreKey:       args.SalesChannel.StoreKey,
		Platform:       args.SalesChannel.Platform,
		// DO NOT SET ConcurrencyKey
		// 每 5 个子任务并发执行,
		// 其中 batch_publish_prices group 设置的 Concurrency 为 10，第 11 个需要等前面 10 个全部执行完成才会被调度
		// 相当于有 50 个子任务同时执行
		ConcurrencyLimit: 5,
	}

	productListingIDsMap := make(map[string]struct{})
	// 组装任务，结构保持与 PublishPricesTask 一致
	inputs := make([]models.TaskInput, 0)
	for i := range args.ProductListings {
		productListingID := args.ProductListings[i].ID
		if _, ok := productListingIDsMap[productListingID]; ok {
			continue
		}
		inputs = append(inputs, &PublishPricesTaskInput{
			Organization:            args.Organization,
			Source:                  args.Source,
			SalesChannel:            args.SalesChannel,
			SalesChannelRegion:      args.SalesChannelRegion,
			FromEvent:               args.FromEvent,
			ProductListingID:        productListingID,
			EventTimestamp:          args.EventTimestamp,
			ProductListingRelations: args.ProductListings[i].Relations,
		})
		productListingIDsMap[productListingID] = struct{}{}
	}
	taskArgs.Inputs = inputs
	return taskArgs, nil
}

func (t *BatchPublishPricesTask) ParseOutput(ctx context.Context, task *models.Task) models.TaskOutput {
	outputs := BatchPublishPricesTaskOutput{}
	outputs.ChildJobIDs = task.ChildTaskIDs
	return outputs
}
