package product_listing

import (
	"context"

	elastic "github.com/olivere/elastic/v7"
)

func (s *serviceImpl) ESProxyProductListingCount(ctx context.Context, query string) (int64, error) {
	return s.esRepo.ESProxyProductListingCount(ctx, query)
}

func (s *serviceImpl) ESProxyProductListingSearch(ctx context.Context, query string) (*elastic.SearchResult, error) {
	return s.esRepo.ESProxyProductListingSearch(ctx, query)
}
