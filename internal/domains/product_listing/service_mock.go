package product_listing

import (
	"context"

	"github.com/olivere/elastic/v7"
	"github.com/stretchr/testify/mock"

	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
)

type MockProductListingService struct {
	mock.Mock
}

func (_m *MockProductListingService) GetByID(ctx context.Context, id string) (ProductListing, error) {
	ret := _m.Called(ctx, id)
	return ret.Get(0).(ProductListing), ret.Error(1)
}

func (_m *MockProductListingService) Search(ctx context.Context, args *SearchProductListingArgs) ([]*ProductListing, *models.Pagination, error) {
	ret := _m.Called(ctx, args)
	return ret.Get(0).([]*ProductListing), ret.Get(1).(*models.Pagination), ret.Error(2)
}

func (_m *MockProductListingService) SearchIDs(ctx context.Context, args *SearchProductListingArgs) ([]string, *models.Pagination, error) {
	ret := _m.Called(ctx, args)
	return ret.Get(0).([]string), ret.Get(1).(*models.Pagination), ret.Error(2)
}

func (_m *MockProductListingService) Count(ctx context.Context, args *SearchProductListingArgs) (int64, error) {
	ret := _m.Called(ctx, args)
	return ret.Get(0).(int64), ret.Error(1)
}

func (_m *MockProductListingService) Create(ctx context.Context, args *ProductListingArgs) (ProductListing, error) {
	ret := _m.Called(ctx, args)
	return ret.Get(0).(ProductListing), ret.Error(1)
}

func (_m *MockProductListingService) UpdateAuditState(ctx context.Context, id string, audit *Audit) (ProductListing, error) {
	ret := _m.Called(ctx, id, audit)
	return ret.Get(0).(ProductListing), ret.Error(1)
}

func (_m *MockProductListingService) UpdatePublishState(ctx context.Context, id string, publishArgs *Publish) (ProductListing, error) {
	ret := _m.Called(ctx, id, publishArgs)
	return ret.Get(0).(ProductListing), ret.Error(1)
}

func (_m *MockProductListingService) ListAuditVersions(ctx context.Context, args *ListAuditVersionsArgs) ([]*AuditVersion, error) {
	ret := _m.Called(ctx, args)
	return ret.Get(0).([]*AuditVersion), ret.Error(1)
}

func (_m *MockProductListingService) ListRelations(ctx context.Context, args *ListRelationsArgs) ([]*ProductListingRelation, error) {
	ret := _m.Called(ctx, args)
	return ret.Get(0).([]*ProductListingRelation), ret.Error(1)
}

func (_m *MockProductListingService) Duplicate(ctx context.Context, id string) (ProductListing, error) {
	ret := _m.Called(ctx, id)
	return ret.Get(0).(ProductListing), ret.Error(1)
}

func (_m *MockProductListingService) Delete(ctx context.Context, id string, force bool) error {
	ret := _m.Called(ctx, id, force)
	return ret.Error(0)
}

func (_m *MockProductListingService) Update(ctx context.Context, id string, args *ProductListingArgs) (ProductListing, error) {
	ret := _m.Called(ctx, id, args)
	return ret.Get(0).(ProductListing), ret.Error(1)
}

func (_m *MockProductListingService) PushToChannel(ctx context.Context, arg *PushToChannelArg) (ProductListing, error) {
	ret := _m.Called(ctx, arg)
	return ret.Get(0).(ProductListing), ret.Error(1)
}

func (_m *MockProductListingService) SalesChannelProductEvent(ctx context.Context, arg *SalesChannelProductEventArg) (ProductListing, error) {
	ret := _m.Called(ctx, arg)
	return ret.Get(0).(ProductListing), ret.Error(1)
}

func (_m *MockProductListingService) ProductsCenterProductEvent(ctx context.Context, id string, arg *ProductsCenterProductEventArg) (ProductListing, error) {
	ret := _m.Called(ctx, id, arg)
	return ret.Get(0).(ProductListing), ret.Error(1)
}

func (_m *MockProductListingService) GeneratePreview(ctx context.Context, pl *ProductListing) (*ProductListing, error) {
	ret := _m.Called(ctx, pl)
	return ret.Get(0).(*ProductListing), ret.Error(1)
}

func (_m *MockProductListingService) EditAttributes(ctx context.Context, id string, args *EditAttributesArgs) (ProductListing, error) {
	ret := _m.Called(ctx, id, args)
	return ret.Get(0).(ProductListing), ret.Error(1)
}

func (_m *MockProductListingService) AggregateCategoryIDs(ctx context.Context, args *SearchProductListingArgs) ([]string, error) {
	ret := _m.Called(ctx, args)
	return ret.Get(0).([]string), ret.Error(1)
}

func (_m *MockProductListingService) AutoLink(ctx context.Context, arg *AutoLinkArg) (ProductListing, error) {
	ret := _m.Called(ctx, arg)
	return ret.Get(0).(ProductListing), ret.Error(1)
}

func (_m *MockProductListingService) LinkOrUnlink(ctx context.Context, arg *LinkArg) (ProductListing, error) {
	ret := _m.Called(ctx, arg)
	return ret.Get(0).(ProductListing), ret.Error(1)
}

func (_m *MockProductListingService) List(ctx context.Context, arg *ListArg) ([]*ProductListing, error) {
	ret := _m.Called(ctx, arg)
	return ret.Get(0).([]*ProductListing), ret.Error(1)
}

func (_m *MockProductListingService) ProductsCenterProductDeleteEvent(ctx context.Context, id string) (ProductListing, error) {
	ret := _m.Called(ctx, id)
	return ret.Get(0).(ProductListing), ret.Error(1)
}

func (_m *MockProductListingService) SalesChannelOrderVariantRelation(ctx context.Context, arg *SalesChannelOrderVariantRelationArg) (ProductListingRelation, error) {
	ret := _m.Called(ctx, arg)
	return ret.Get(0).(ProductListingRelation), ret.Error(1)
}

func (_m *MockProductListingService) ESProxyProductListingCount(ctx context.Context, query string) (int64, error) {
	ret := _m.Called(ctx, query)
	return ret.Get(0).(int64), ret.Error(1)
}

func (_m *MockProductListingService) RefreshES(ctx context.Context, id string, ops ...EsOption) error {
	ret := _m.Called(ctx, id, ops)
	return ret.Error(0)
}

func (_m *MockProductListingService) NotifySearchableProductUpsertEvent(ctx context.Context, productsCenterProductID string) error {
	ret := _m.Called(ctx, productsCenterProductID)
	return ret.Error(0)
}

func (_m *MockProductListingService) PublishUpdate(ctx context.Context, id string) error {
	ret := _m.Called(ctx, id)
	return ret.Error(0)
}

func (_m *MockProductListingService) UpdateRelations(ctx context.Context, id string, arg *UpdateRelationsArg) (ProductListing, error) {
	ret := _m.Called(ctx, id, arg)
	return ret.Get(0).(ProductListing), ret.Error(1)
}

func (_m *MockProductListingService) ESProxyProductListingSearch(ctx context.Context, query string) (*elastic.SearchResult, error) {
	ret := _m.Called(ctx, query)
	return ret.Get(0).(*elastic.SearchResult), ret.Error(1)
}

func (_m *MockProductListingService) UnMatchProduct(ctx context.Context, id string) (ProductListing, error) {
	ret := _m.Called(ctx, id)
	return ret.Get(0).(ProductListing), ret.Error(1)
}

func (_m *MockProductListingService) MatchProduct(ctx context.Context, id string) (ProductListing, error) {
	ret := _m.Called(ctx, id)
	return ret.Get(0).(ProductListing), ret.Error(1)
}

func (_m *MockProductListingService) CheckAndFixStatus(ctx context.Context, dryRun bool, processedLimitCount int) error {
	ret := _m.Called(ctx, dryRun, processedLimitCount)
	return ret.Error(0)
}
