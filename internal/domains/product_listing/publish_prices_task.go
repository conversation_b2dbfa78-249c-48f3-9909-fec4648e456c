package product_listing

import (
	"context"
	"encoding/json"
	"time"

	"go.uber.org/zap"

	"github.com/AfterShip/gopkg/log"
	"github.com/AfterShip/pltf-pd-product-listings/internal/consts"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
)

type PublishPricesTaskInput struct {
	Organization            models.Organization               `json:"organization" validate:"required"`
	SalesChannel            models.SalesChannel               `json:"sales_channel" validate:"required"`
	Source                  models.Source                     `json:"source" validate:"omitempty"`
	ProductListingID        string                            `json:"product_listing_id" validate:"required"`
	SalesChannelRegion      string                            `json:"sales_channel_region"`
	FromEvent               string                            `json:"from_event" validate:"required"` // 事件来源，日志记录用
	EventTimestamp          time.Time                         `json:"event_timestamp"`
	ProductListingRelations []priceTaskProductListingRelation `json:"product_listing_relations" validate:"required,dive"`
}

type priceTaskProductListingRelation struct {
	ID               string        `json:"id" validate:"required"`
	ChannelProductID string        `json:"channel_product_id"`
	ChannelVariantID string        `json:"channel_variant_id"`
	SourceProductID  string        `json:"source_product_id"`
	SourceVariantID  string        `json:"source_variant_id"`
	OverWritePrice   *models.Price `json:"overwrite_price" ` // 直接使用 price 覆盖价格，不需要计算
}

type PublishPricesTaskOutput struct {
	Organization            models.Organization               `json:"organization" validate:"required"`
	SalesChannel            models.SalesChannel               `json:"sales_channel" validate:"required"`
	Source                  models.Source                     `json:"source" validate:"omitempty"`
	ProductListingID        string                            `json:"product_listing_id" validate:"required"`
	SalesChannelRegion      string                            `json:"sales_channel_region"`
	FromEvent               string                            `json:"from_event" validate:"required"` // 事件来源，日志记录用
	EventTimestamp          time.Time                         `json:"event_timestamp"`
	ProductListingRelations []priceTaskProductListingRelation `json:"product_listing_relations" validate:"required,dive"`
}

type PublishPricesTask struct {
	Logger *log.Logger
}

func (t *PublishPricesTask) validate(input *PublishPricesTaskInput) error {
	if input.Organization.ID == "" {
		return ErrUnprocessableEntity
	}
	if input.SalesChannel.Platform == "" {
		return ErrUnprocessableEntity
	}
	if input.SalesChannel.StoreKey == "" {
		return ErrUnprocessableEntity
	}
	if input.ProductListingID == "" {
		return ErrNoProductListingID
	}
	if input.EventTimestamp.Unix() <= 0 {
		return ErrUnprocessableEntity
	}

	return nil
}

// nolint:dupl
func (t *PublishPricesTask) BuildTaskArgs(ctx context.Context, input models.TaskInput) (models.TaskArgs, error) {
	args, ok := input.(*PublishPricesTaskInput)
	if !ok {
		return models.TaskArgs{}, ErrUnprocessableEntity
	}

	if err := t.validate(args); err != nil {
		return models.TaskArgs{}, err
	}
	// set default event timestamp
	if args.EventTimestamp.Unix() <= 0 {
		args.EventTimestamp = time.Now()
	}
	taskArgs := models.TaskArgs{
		GroupName:      consts.PublishPrices,
		Type:           consts.SingleTaskType,
		OrganizationID: args.Organization.ID,
		StoreKey:       args.SalesChannel.StoreKey,
		Platform:       args.SalesChannel.Platform,
		ResourceID:     args.ProductListingID,
		Inputs:         args,
	}
	return taskArgs, nil
}

func (t *PublishPricesTask) ParseOutput(ctx context.Context, task *models.Task) models.TaskOutput {
	outputs := PublishPricesTaskOutput{}
	if err := json.Unmarshal([]byte(task.Inputs), &outputs); err != nil {
		t.Logger.With(zap.String("Id", task.ID)).WarnCtx(ctx, "Failed to parse publish prices task output", zap.Error(err))
	}
	return outputs
}

type priceTaskArg struct {
	Listing   *ProductListing
	FromEvent string
}

func (p *priceTaskArg) BuildTaskInput() *PublishPricesTaskInput {
	relations := make([]priceTaskProductListingRelation, 0)
	for i := range p.Listing.Relations {
		// 同步库存
		if p.Listing.Relations[i].IsLinkedAndSynced() {
			relations = append(relations, priceTaskProductListingRelation{
				ID:               p.Listing.Relations[i].ID,
				ChannelProductID: p.Listing.Relations[i].SalesChannelVariant.ProductID,
				ChannelVariantID: p.Listing.Relations[i].SalesChannelVariant.ID,
				SourceProductID:  p.Listing.Relations[i].ProductsCenterVariant.ProductID,
				SourceVariantID:  p.Listing.Relations[i].ProductsCenterVariant.ID,
			})
		}
	}

	return &PublishPricesTaskInput{
		Organization: p.Listing.Organization,
		SalesChannel: p.Listing.SalesChannel,
		Source: models.Source{
			App: models.App{
				Platform: p.Listing.ProductsCenterProduct.Source.Platform,
				Key:      p.Listing.ProductsCenterProduct.Source.StoreKey,
			},
			ID: p.Listing.ID,
		},
		SalesChannelRegion:      p.Listing.SalesChannel.CountryRegion,
		ProductListingID:        p.Listing.ID,
		FromEvent:               p.FromEvent,
		EventTimestamp:          time.Now(),
		ProductListingRelations: relations,
	}
}
