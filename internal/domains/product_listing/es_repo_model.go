package product_listing

import (
	"strconv"
	"time"

	"github.com/olivere/elastic/v7"
	"github.com/pkg/errors"

	"github.com/AfterShip/gopkg/facility/types"

	"github.com/AfterShip/pltf-pd-product-listings/internal/consts"
	"github.com/AfterShip/pltf-pd-product-listings/internal/utils/elasticsearch"
)

type productListingsEsModel struct {
	ID                             string                            `json:"id"`
	OrganizationId                 string                            `json:"organization_id"`
	SalesChannelStoreKey           string                            `json:"sales_channel_store_key"`
	SalesChannelPlatform           string                            `json:"sales_channel_platform"`
	SalesChannelProductId          string                            `json:"sales_channel_product_id"`
	SalesChannelConnectorProductId string                            `json:"sales_channel_connector_product_id"`
	SalesChannelProductState       consts.SalesChannelProductState   `json:"sales_channel_product_state"`
	MatchedProductsCenterProductId string                            `json:"matched_products_center_product_id"`
	State                          consts.ProductListingProductState `json:"state"`
	LinkStatus                     consts.LinkStatus                 `json:"link_status"`
	SyncStatus                     consts.SyncStatus                 `json:"sync_status"`
	ReadyStatus                    consts.ReadyStatus                `json:"ready_status"`
	PublishState                   consts.PublishState               `json:"publish_state"`
	AuditState                     consts.AuditState                 `json:"audit_state"`
	Deleted                        types.Bool                        `json:"deleted"`
	ProductTitle                   string                            `json:"product_title"`
	ProductSalesChannelCategoryIds []string                          `json:"product_sales_channel_category_ids"`
	ProductTags                    []string                          `json:"product_tags"`
	CreatedAt                      time.Time                         `json:"created_at"`
	UpdatedAt                      time.Time                         `json:"updated_at"`
	VariantsAllowBackOrder         types.Bool                        `json:"variants_allow_backorder"`
	VariantsInventoryQuantity      float64                           `json:"variants_inventory_quantity"`
	ProductVariants                []ProductListingVariantEsModel    `json:"product_variants"`
	FeedCategoryTemplateID         string                            `json:"feed_category_template_id"`
	Options                        []OptionEsModel                   `json:"options"`
	ProductNumber                  string                            `json:"product_number"`
	ProductTypes                   []string                          `json:"product_types"`
}

type ProductListingVariantEsModel struct {
	ID                     string     `json:"id"`
	Sku                    string     `json:"sku"`
	Title                  string     `json:"title"`
	InventoryQuantity      float64    `json:"inventory_quantity"`
	AllowBackOrder         types.Bool `json:"allow_backorder"`
	PriceCurrency          string     `json:"price_currency"`
	PriceAmount            float64    `json:"price_amount"`
	CompareAtPriceCurrency string     `json:"compare_at_price_currency"`
	CompareAtPriceAmount   float64    `json:"compare_at_price_amount"`
}

type OptionEsModel struct {
	Name                       string `json:"name"`
	ValueDetailsSalesChannelID string `json:"value_details_sales_channel_id"`
	ValueDetailsState          string `json:"value_details_state"`
	ValueDetailsSyncStatus     string `json:"value_details_sync_status"`
}

type EsOption func(op *UpsertProductListingESOption)

type UpsertProductListingESOption struct {
	ForceRefresh  bool   // if set true, es version will add 100 ms for update.
	VersionOffset int    // if set ForceRefresh true, user VersionOffset
	Action        string // 更新 es 的动作
}

func WithForceRefresh(forceRefresh bool) EsOption {
	return func(op *UpsertProductListingESOption) {
		op.ForceRefresh = forceRefresh
	}
}

func WithVersionOffset(offset int) EsOption {
	return func(op *UpsertProductListingESOption) {
		op.VersionOffset = offset
	}
}

func WithAction(action string) EsOption {
	return func(op *UpsertProductListingESOption) {
		op.Action = action
	}
}

func buildSearchPagination(esSvc *elastic.SearchService, args *SearchProductListingArgs) error {
	// 分页方式
	if args.buildSearchType() == esPaginationTypeSearchAfter {
		cursor, err := elasticsearch.ParseCursor(args.Cursor)
		if err != nil {
			return err
		}
		esSvc.SearchAfter(cursor...)
	} else {
		esSvc.From(int((args.Page - 1) * args.Limit))
	}
	return nil
}

func toProductListingsEsModel(pl *ProductListing) (*productListingsEsModel, error) {
	if pl == nil {
		return nil, ErrProductListingIsEmpty
	}
	esModel := &productListingsEsModel{
		ID:                             pl.ID,
		OrganizationId:                 pl.Organization.ID,
		SalesChannelStoreKey:           pl.SalesChannel.StoreKey,
		SalesChannelPlatform:           pl.SalesChannel.Platform,
		SalesChannelProductId:          pl.SalesChannelProduct.ID,
		SalesChannelConnectorProductId: pl.SalesChannelProduct.ConnectorProductID,
		SalesChannelProductState:       pl.SalesChannelProduct.State,
		MatchedProductsCenterProductId: pl.ProductsCenterProduct.ID,
		State:                          pl.State,
		LinkStatus:                     pl.LinkStatus,
		SyncStatus:                     pl.SyncStatus,
		ReadyStatus:                    pl.Ready.Status,
		PublishState:                   pl.Publish.State,
		AuditState:                     pl.Audit.State,
		ProductTags:                    pl.Product.Tags,
		CreatedAt:                      pl.CreatedAt,
		UpdatedAt:                      pl.UpdatedAt,
	}

	if pl.DeletedAt.Datetime().Unix() > 0 {
		esModel.Deleted = types.MakeBool(true)
	} else {
		esModel.Deleted = types.MakeBool(false)
	}

	esModel.ProductTitle = pl.Product.Title

	esModel.ProductSalesChannelCategoryIds = toProductListingsEsCategory(pl)

	esModel.FeedCategoryTemplateID = pl.FeedCustomizationParams.FeedCategoryTemplateID

	anyOfVariantsAllowBackOrder, allVariantsInventoryQuantity := toProductListingsEsInventories(pl)
	esModel.VariantsAllowBackOrder = types.MakeBool(anyOfVariantsAllowBackOrder)
	esModel.VariantsInventoryQuantity = allVariantsInventoryQuantity

	productVariants, err := toVariantsEsModel(pl)
	if err != nil {
		return nil, err
	}
	esModel.ProductVariants = productVariants

	esModel.ProductNumber = pl.Product.ProductNumber

	esModel.ProductTypes = pl.Product.ProductTypes

	esModel.Options = toOptionEsModel(pl)

	return esModel, nil
}

func toProductListingsEsCategory(pl *ProductListing) []string {
	channelCategoryIds := make([]string, 0)
	if len(pl.Product.Categories) > 0 {
		for i := range pl.Product.Categories {
			channelCategoryIds = append(channelCategoryIds, pl.Product.Categories[i].SalesChannelID)
		}
	} else {
		channelCategoryIds = append(channelCategoryIds, UncategoriedValueInEs)
	}
	return channelCategoryIds
}

func toProductListingsEsInventories(pl *ProductListing) (bool, float64) {
	var anyOfVariantsAllowBackOrder bool
	var allVariantsInventoryQuantity float64
	for i := range pl.Product.Variants {
		anyOfVariantsAllowBackOrder = anyOfVariantsAllowBackOrder || pl.Product.Variants[i].AllowBackorder
		allVariantsInventoryQuantity += pl.Product.Variants[i].InventoryQuantity
	}
	return anyOfVariantsAllowBackOrder, allVariantsInventoryQuantity
}

func toVariantsEsModel(pl *ProductListing) ([]ProductListingVariantEsModel, error) {
	variantsEsModel := make([]ProductListingVariantEsModel, 0)
	for i := range pl.Product.Variants {
		var (
			priceAmount          float64
			compareAtPriceAmount float64
			err                  error
		)
		variant := pl.Product.Variants[i]
		if variant.Price.Amount != "" {
			priceAmount, err = strconv.ParseFloat(variant.Price.Amount, 64)
			if err != nil {
				return variantsEsModel, errors.WithStack(err)
			}
		}
		if variant.CompareAtPrice.Amount != "" {
			compareAtPriceAmount, err = strconv.ParseFloat(variant.CompareAtPrice.Amount, 64)
			if err != nil {
				return variantsEsModel, errors.WithStack(err)
			}
		}

		variantsEsModel = append(variantsEsModel, ProductListingVariantEsModel{
			ID:                     variant.ID,
			Sku:                    variant.Sku,
			Title:                  variant.Title,
			InventoryQuantity:      variant.InventoryQuantity,
			AllowBackOrder:         types.MakeBool(variant.AllowBackorder),
			PriceCurrency:          variant.Price.Currency,
			PriceAmount:            priceAmount,
			CompareAtPriceCurrency: variant.CompareAtPrice.Currency,
			CompareAtPriceAmount:   compareAtPriceAmount,
		})
	}
	return variantsEsModel, nil
}

func toOptionEsModel(pl *ProductListing) []OptionEsModel {
	optionEsModels := make([]OptionEsModel, 0)
	mainOption, ok := pl.Product.GetMainOption()
	if ok {
		for _, valueDetail := range mainOption.ValueDetails {
			optionEsModel := OptionEsModel{
				Name: valueDetail.Value,
			}
			optionEsModel.ValueDetailsSalesChannelID = valueDetail.SalesChannelID
			optionEsModel.ValueDetailsState = valueDetail.State.String()
			optionEsModel.ValueDetailsSyncStatus = valueDetail.SyncStatus.String()
			optionEsModels = append(optionEsModels, optionEsModel)
		}
	}
	return optionEsModels
}
