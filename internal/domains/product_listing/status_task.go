package product_listing

import (
	"context"
	"encoding/json"

	"github.com/go-playground/validator/v10"
	"go.uber.org/zap"

	"github.com/AfterShip/gopkg/log"

	"github.com/AfterShip/pltf-pd-product-listings/internal/consts"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
	"github.com/AfterShip/pltf-pd-product-listings/internal/utils/slicex"
)

type StatusTaskInput struct {
	OrganizationID    string               `json:"organization_id" validate:"required"`
	SalesChannel      models.SalesChannel  `json:"sales_channel" validate:"required"`
	ProductListingIDs []string             `json:"product_listing_ids" validate:"required"`
	GroupName         consts.TaskGroupName `json:"group_name"`
}

type StatusTaskOutput struct {
	TotalCount     int `json:"total_count"`
	SucceededCount int `json:"succeeded_count"`
	FailedCount    int `json:"failed_count"`
}

type StatusTask struct {
	Logger    *log.Logger
	Validator *validator.Validate
}

func (t *StatusTask) validate(input *StatusTaskInput) error {
	if err := t.Validator.Struct(input); err != nil {
		return err
	}

	if len(input.ProductListingIDs) == 0 {
		return ErrNoProductListingID
	}

	return nil
}

// nolint:dupl
func (t *StatusTask) BuildTaskArgs(ctx context.Context, input models.TaskInput) (models.TaskArgs, error) {
	args, ok := input.(*StatusTaskInput)
	if !ok {
		t.Logger.WarnCtx(ctx, "Failed to parse publish product_listings task input", zap.Any("input", input))
		return models.TaskArgs{}, ErrUnprocessableEntity
	}

	if err := t.validate(args); err != nil {
		return models.TaskArgs{}, err
	}

	taskArgs := models.TaskArgs{
		GroupName:  args.GroupName,
		ResourceID: args.OrganizationID,
		StoreKey:   args.SalesChannel.StoreKey,
		Platform:   args.SalesChannel.Platform,
		Type:       consts.BatchTaskType,
	}

	inputs := make([]models.TaskInput, 0)
	splitIDs := slicex.SplitSlice(args.ProductListingIDs, 20)
	for i := range splitIDs {
		inputs = append(inputs, &StatusTaskInput{
			OrganizationID:    args.OrganizationID,
			ProductListingIDs: splitIDs[i],
			SalesChannel:      args.SalesChannel,
		})
	}
	taskArgs.Inputs = inputs

	// If there are multiple IDs, we need to use the organization ID as the concurrency key
	if len(args.ProductListingIDs) > 1 {
		taskArgs.ConcurrencyKey = args.OrganizationID
	}

	return taskArgs, nil
}

func (t *StatusTask) ParseOutput(ctx context.Context, task *models.Task) models.TaskOutput {
	output := StatusTaskOutput{}
	for i := range task.ChildTasks {
		// get total count
		if task.ChildTasks[i].Inputs == "" {
			continue
		}

		input := &StatusTaskInput{}
		if err := json.Unmarshal([]byte(task.ChildTasks[i].Inputs), input); err != nil {
			t.Logger.With(zap.String("Id", task.ID)).WarnCtx(ctx, "Failed to parse publish listing task input", zap.Error(err))
			continue
		}

		output.TotalCount += len(input.ProductListingIDs)

		if task.ChildTasks[i].Outputs.Data == "" {
			continue
		}

		// get child task output
		childTaskOutput := StatusTaskOutput{}
		if err := json.Unmarshal([]byte(task.ChildTasks[i].Outputs.Data), &childTaskOutput); err != nil {
			t.Logger.With(zap.String("Id", task.ID)).WarnCtx(ctx, "Failed to parse status task output", zap.Error(err))
			continue
		}
		output.FailedCount += childTaskOutput.FailedCount
		output.SucceededCount += childTaskOutput.SucceededCount
	}

	return output
}
