package product_listing

import (
	"context"
	"fmt"
	"testing"

	"github.com/spf13/viper"
	"github.com/stretchr/testify/require"

	"github.com/AfterShip/gopkg/cfg"
	"github.com/AfterShip/gopkg/storage/spannerx"
	"github.com/AfterShip/gopkg/uuid"

	"github.com/AfterShip/pltf-pd-product-listings/internal/config"
)

func Test_AuditVersionRepoImpl(t *testing.T) {
	cfgs := new(config.Config)
	_, err := cfg.LoadViperConfig(cfgs, func(v *viper.Viper) { v.AddConfigPath("../../../cmd/apiserver/conf") })
	require.NoError(t, err)

	ctx := context.Background()

	database := fmt.Sprintf("projects/%s/instances/%s/databases/%s",
		cfgs.Spanner.Project, cfgs.Spanner.Instance, cfgs.Spanner.Database)
	spannerCli, err := spannerx.NewClient(ctx, database)
	require.NoError(t, err)
	auditVersionDBRepo := auditVersionRepo{
		cli: spannerCli,
	}
	productListingID := uuid.GenerateUUIDV4()
	auditVersions := []auditVersionDBModel{
		{
			ProductListingAuditVersionID: uuid.GenerateUUIDV4(),
			ProductListingID:             productListingID,
			AuditData: AuditData{
				ProductListing: ProductListing{ID: productListingID},
			},
		},
		{
			ProductListingAuditVersionID: uuid.GenerateUUIDV4(),
			ProductListingID:             productListingID,
			AuditData: AuditData{
				ProductListing: ProductListing{ID: productListingID},
			},
		},
		{
			ProductListingAuditVersionID: uuid.GenerateUUIDV4(),
			ProductListingID:             uuid.GenerateUUIDV4(),
			AuditData: AuditData{
				ProductListing: ProductListing{ID: productListingID},
			},
		},
	}

	for _, auditVersion := range auditVersions {
		err = auditVersionDBRepo.create(ctx, &auditVersion)
		require.NoError(t, err)
	}

	// Get list
	filter := repoListAuditVersionArgs{
		ProductListingID: productListingID,
		Limit:            10,
		Page:             1,
	}
	founds, err := auditVersionDBRepo.list(ctx, &filter)
	require.NoError(t, err)
	require.Equal(t, 2, len(founds))

	// Get list with limit
	filter = repoListAuditVersionArgs{
		ProductListingID: productListingID,
		Limit:            1,
		Page:             1,
	}
	founds, err = auditVersionDBRepo.list(ctx, &filter)
	require.NoError(t, err)
	require.Equal(t, 1, len(founds))

	// Get list without product listing id
	filter = repoListAuditVersionArgs{
		Limit: 1,
		Page:  1,
	}
	founds, err = auditVersionDBRepo.list(ctx, &filter)
	require.NoError(t, err)
	require.NotEqual(t, 0, len(founds))

	// Get list with page
	filter = repoListAuditVersionArgs{
		ProductListingID: productListingID,
		Limit:            1,
		Page:             2,
	}
	founds, err = auditVersionDBRepo.list(ctx, &filter)
	require.NoError(t, err)
	require.Equal(t, 1, len(founds))

	// Get list with page
	filter = repoListAuditVersionArgs{
		ProductListingID: productListingID,
		Limit:            -1,
		Page:             -10,
	}
	founds, err = auditVersionDBRepo.list(ctx, &filter)
	require.NoError(t, err)
	require.Equal(t, 0, len(founds))

	// Get by id
	auditVersion, err := auditVersionDBRepo.getByID(ctx, auditVersions[0].ProductListingAuditVersionID)
	require.NoError(t, err)
	require.Equal(t, auditVersions[0].ProductListingAuditVersionID, auditVersion.ProductListingAuditVersionID)

	// Get by id not found
	auditVersion, err = auditVersionDBRepo.getByID(ctx, uuid.GenerateUUIDV4())
	require.Error(t, err)
	require.Equal(t, "", auditVersion.ProductListingAuditVersionID)

	// force delete
	err = auditVersionDBRepo.forceDelete(ctx, auditVersions[0].ProductListingAuditVersionID)
	require.NoError(t, err)
	notFound, err := auditVersionDBRepo.getByID(ctx, auditVersions[0].ProductListingAuditVersionID)
	require.Error(t, err)
	require.Equal(t, "", notFound.ProductListingAuditVersionID)

	err = auditVersionDBRepo.forceDelete(ctx, auditVersions[1].ProductListingAuditVersionID)
	require.NoError(t, err)
	notFound, err = auditVersionDBRepo.getByID(ctx, auditVersions[1].ProductListingAuditVersionID)
	require.Error(t, err)
	require.Equal(t, "", notFound.ProductListingAuditVersionID)

	err = auditVersionDBRepo.forceDelete(ctx, auditVersions[2].ProductListingAuditVersionID)
	require.NoError(t, err)
	notFound, err = auditVersionDBRepo.getByID(ctx, auditVersions[2].ProductListingAuditVersionID)
	require.Error(t, err)
	require.Equal(t, "", notFound.ProductListingAuditVersionID)

}
