package product_listing

import (
	"context"
	"encoding/json"

	"github.com/go-playground/validator/v10"
	"go.uber.org/zap"

	"github.com/AfterShip/gopkg/log"

	"github.com/AfterShip/pltf-pd-product-listings/internal/consts"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
	"github.com/AfterShip/pltf-pd-product-listings/internal/utils/slicex"
)

type PublishTaskInput struct {
	OrganizationID    string              `json:"organization_id" validate:"required"`
	SalesChannel      models.SalesChannel `json:"sales_channel" validate:"required"`
	ProductListingIDs []string            `json:"product_listing_ids" validate:"required"`
	App               models.App          `json:"app" validate:"omitempty"`
}

type PublishTaskOutput struct {
	TotalCount     int `json:"total_count"`
	SucceededCount int `json:"succeeded_count"`
	FailedCount    int `json:"failed_count"`
}

type PublishTask struct {
	Logger    *log.Logger
	Validator *validator.Validate
}

func (t *PublishTask) validate(input *PublishTaskInput) error {
	if err := t.Validator.Struct(input); err != nil {
		return err
	}

	if len(input.ProductListingIDs) == 0 {
		return ErrNoProductListingID
	}

	return nil
}

// nolint:dupl
func (t *PublishTask) BuildTaskArgs(ctx context.Context, input models.TaskInput) (models.TaskArgs, error) {
	args, ok := input.(*PublishTaskInput)
	if !ok {
		t.Logger.WarnCtx(ctx, "Failed to parse publish product_listings task input", zap.Any("input", input))
		return models.TaskArgs{}, ErrUnprocessableEntity
	}

	if err := t.validate(args); err != nil {
		return models.TaskArgs{}, err
	}

	taskArgs := models.TaskArgs{
		GroupName:  consts.PublishProductListings,
		ResourceID: args.OrganizationID,
		Type:       consts.BatchTaskType,
		StoreKey:   args.SalesChannel.StoreKey,
		Platform:   args.SalesChannel.Platform,
	}

	inputs := make([]models.TaskInput, 0)
	splitIDs := slicex.SplitSlice(args.ProductListingIDs, 50)
	for i := range splitIDs {
		inputs = append(inputs, &PublishTaskInput{
			OrganizationID:    args.OrganizationID,
			ProductListingIDs: splitIDs[i],
			SalesChannel:      args.SalesChannel,
			App:               args.App,
		})
	}
	taskArgs.Inputs = inputs

	// If there are multiple IDs, we need to use the organization ID as the concurrency key
	if len(args.ProductListingIDs) > 1 {
		taskArgs.ConcurrencyKey = args.OrganizationID
	}

	return taskArgs, nil
}

func (t *PublishTask) ParseOutput(ctx context.Context, task *models.Task) models.TaskOutput {
	output := PublishTaskOutput{}
	for i := range task.ChildTasks {
		// get total count
		if task.ChildTasks[i].Inputs == "" {
			continue
		}

		input := &PublishTaskInput{}
		if err := json.Unmarshal([]byte(task.ChildTasks[i].Inputs), input); err != nil {
			t.Logger.With(zap.String("Id", task.ID)).WarnCtx(ctx, "Failed to parse publish listing task input", zap.Error(err))
			continue
		}

		output.TotalCount += len(input.ProductListingIDs)

		if task.ChildTasks[i].Outputs.Data == "" {
			continue
		}

		// get child task output
		childTaskOutput := PublishTaskOutput{}
		if err := json.Unmarshal([]byte(task.ChildTasks[i].Outputs.Data), &childTaskOutput); err != nil {
			t.Logger.With(zap.String("Id", task.ID)).WarnCtx(ctx, "Failed to parse publish listing task output", zap.Error(err))
			continue
		}
		output.FailedCount += childTaskOutput.FailedCount
		output.SucceededCount += childTaskOutput.SucceededCount
	}

	return output
}

type SinglePublishTask struct {
	Logger    *log.Logger
	Validator *validator.Validate
}

func (t *SinglePublishTask) validate(input *PublishTaskInput) error {
	if err := t.Validator.Struct(input); err != nil {
		return err
	}

	if len(input.ProductListingIDs) == 0 {
		return ErrNoProductListingID
	}

	return nil
}

// nolint:dupl
func (t *SinglePublishTask) BuildTaskArgs(ctx context.Context, input models.TaskInput) (models.TaskArgs, error) {
	args, ok := input.(*PublishTaskInput)
	if !ok {
		t.Logger.WarnCtx(ctx, "Failed to parse single publish product_listings task input", zap.Any("input", input))
		return models.TaskArgs{}, ErrUnprocessableEntity
	}

	if err := t.validate(args); err != nil {
		return models.TaskArgs{}, err
	}

	taskArgs := models.TaskArgs{
		GroupName:      consts.PublishSingleProductListings,
		OrganizationID: args.OrganizationID,
		ResourceID:     args.OrganizationID,
		StoreKey:       args.SalesChannel.StoreKey,
		Platform:       args.SalesChannel.Platform,
		Type:           consts.SingleTaskType,
		Inputs:         args,
	}

	return taskArgs, nil
}

func (t *SinglePublishTask) ParseOutput(ctx context.Context, task *models.Task) models.TaskOutput {
	output := PublishTaskOutput{}
	if err := json.Unmarshal([]byte(task.Outputs.Data), &output); err != nil {
		t.Logger.With(zap.String("Id", task.ID)).WarnCtx(ctx, "fail parse single publish product listing output", zap.Error(err))
	}
	return output
}
