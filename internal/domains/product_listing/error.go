package product_listing

import "github.com/pkg/errors"

var (
	ErrEmptyPaginationParams             = errors.New("page and cursor should not both empty")
	ErrNotFound                          = errors.New("resource not found")
	ErrTooManyRequest                    = errors.New("ES server reject request due to too many request")
	ErrUnprocessableEntity               = errors.New("invalid input parameters")
	ErrProductNotFound                   = errors.New("product not found")
	ErrESBatchUpsert                     = errors.New("failed to batch upsert to ES")
	ErrESBatchDelete                     = errors.New("failed to batch delete from ES")
	ErrUpdatePublicationByRefID          = errors.New("the publish still in running it can not update by the reference id")
	ErrInvalidPublishState               = errors.New("invalid publish state")
	ErrPublishStateInUpdate              = errors.New("product listing in publish can not update")
	ErrPublishStateInUpdateNotAck        = errors.New("product listing in publish can not update but not ack")
	ErrProductsCenterProductIsPushed     = errors.New("products center product is pushed")
	ErrSettingNotFound                   = errors.New("setting resource not found")
	ErrListingProductCategoryNotSet      = errors.New("listing product category not set")
	ErrNotLiveProduct                    = errors.New("product listing in sales channel state is not live")
	ErrNoVariant                         = errors.New("no variant")
	ErrNoProductsCenterVariantID         = errors.New("no products center variant ID")
	ErrProductsCenterProductNotFound     = errors.New("products center product not found")
	ErrAlreadyLinked                     = errors.New("product listing variant already linked")
	ErrVersionConflict                   = errors.New("version conflict")
	ErrSalesChannelProductConflict       = errors.New("sales channel product conflict")
	ErrStateInDelete                     = errors.New("product listing state can not delete")
	ErrSameAuditVersion                  = errors.New("the audit version is the same as the current version")
	ErrNoValidVariantsToAutoLink         = errors.New("no valid variants to auto link")
	ErrBothConnectionIsNotFound          = errors.New("both connection is not found")
	ErrAutoLinkIsDisabled                = errors.New("auto link is disabled")
	ErrNotMatchedAndLinkedProduct        = errors.New("product listing has not been matched and linked")
	ErrProductListingNotAllowLink        = errors.New("product listing not allow link")
	ErrNoProductListingID                = errors.New("no product listing ID")
	ErrProductListingIsEmpty             = errors.New("product listing is empty")
	ErrCalculateVariantPriceNotFound     = errors.New("calculate variant price not found")
	ErrCalculateVariantInventoryNotFound = errors.New("calculate variant inventory not found")
	ErrAuditVersionRelationIDIsEmpty     = errors.New("audit version relation ID is empty")
	ErrInvalidCommissionRate             = errors.New("invalid commission rate")
	ErrSalesChannelProductIsExist        = errors.New("sales channel product is exist")
	ErrSheinProductIsInvalid             = errors.New("shein product is invalid")
	ErrorBundlesProductNotAllowAutoLink  = errors.New("bundles product not allow auto link")
	ErrEditAttributesNotAllowUpdate      = errors.New("edit attributes not allow update")
)
