package product_listing

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"testing"
	"time"

	"cloud.google.com/go/spanner"
	"github.com/go-playground/validator/v10"
	elastic "github.com/olivere/elastic/v7"
	"github.com/pkg/errors"
	"github.com/shopspring/decimal"
	"github.com/spf13/viper"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"

	"github.com/AfterShip/connectors-library/sdks/products_center"
	"github.com/AfterShip/connectors-sdk-go/v2/products"
	"github.com/AfterShip/feed-sdk-go/events"
	"github.com/AfterShip/feed-sdk-go/v2/support_features"
	"github.com/AfterShip/gopkg/cfg"
	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/gopkg/uuid"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/common/calculators"
	"github.com/AfterShip/pltf-pd-product-listings/internal/third_party/bme"
	"github.com/AfterShip/pltf-pd-product-listings/internal/third_party/feed"
	"github.com/AfterShip/pltf-pd-product-listings/internal/utils/elasticsearch"

	"github.com/AfterShip/pltf-pd-product-listings/internal/config"
	"github.com/AfterShip/pltf-pd-product-listings/internal/consts"
	"github.com/AfterShip/pltf-pd-product-listings/internal/datastore"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/category"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/common/databus"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/searchable_products"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/settings"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/task_schedule/task"
	"github.com/AfterShip/pltf-pd-product-listings/internal/logger"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
	"github.com/AfterShip/pltf-pd-product-listings/internal/third_party/connectors"
)

func Test_ProductListingServiceImpl_NewService(t *testing.T) {
	service := initService(t)
	require.NotNil(t, service)
}

func Test_ProductListingServiceImpl_GetByID(t *testing.T) {
	service := initService(t)
	require.NotNil(t, service)

	// create product listing data
	args := generateProductListingArgs()
	pl, err := service.Create(context.Background(), &args)
	require.NoError(t, err)
	require.NotNil(t, pl)

	// product listing found
	productListing, err := service.GetByID(context.Background(), pl.ID)
	require.NoError(t, err)
	compareProductListing(t, &args, &productListing)

	// product listing not found
	productListing, err = service.GetByID(context.Background(), "not found")
	require.Error(t, err)
	require.Equal(t, ProductListing{}, productListing)
}

func Test_ProductListingServiceImpl_UpdatePublishState(t *testing.T) {
	service := initService(t)
	require.NotNil(t, service)

	pl := productListingDBModel{
		ProductListingID:     uuid.GenerateUUIDV4(),
		State:                consts.ProductListingProductStateActive,
		OrganizationID:       uuid.GenerateUUIDV4(),
		SalesChannelPlatform: "tiktok-shop",
		SalesChannelStoreKey: "test",
	}
	err := service.repo.plRepo.create(context.Background(), &pl)
	require.NoError(t, err)

	// test case 1
	args := Publish{
		State: consts.PublishStatePending,
	}
	productListing, err := service.UpdatePublishState(context.Background(), pl.ProductListingID, &args)
	require.NoError(t, err)
	require.Equal(t, productListing.Publish.State, args.State)
	require.Equal(t, productListing.State, pl.State)

	// test case 2 pending to running
	lastReferenceID := uuid.GenerateUUIDV4()
	args = Publish{
		State:           consts.PublishStateRunning,
		LastReferenceID: lastReferenceID,
	}
	productListing, err = service.UpdatePublishState(context.Background(), pl.ProductListingID, &args)
	require.NoError(t, err)
	require.Equal(t, productListing.Publish.State, args.State)

	// test case 3 running to other with other last reference id
	args = Publish{
		State:           consts.PublishStateFailed,
		LastReferenceID: uuid.GenerateUUIDV4(),
	}
	productListing, err = service.UpdatePublishState(context.Background(), pl.ProductListingID, &args)
	require.Error(t, err, ErrUpdatePublicationByRefID)

	// test case 4 failed publish state update
	args = Publish{
		State:           consts.PublishStateFailed,
		LastReferenceID: lastReferenceID,
		Error: PublishError{
			Code: "error code",
			Msg:  "error msg",
		},
		LastFailedAt: time.Now(),
	}
	productListing, err = service.UpdatePublishState(context.Background(), pl.ProductListingID, &args)
	require.NoError(t, err)
	require.Equal(t, productListing.Publish.State, args.State)
	require.Equal(t, productListing.Publish.Error.Msg, args.Error.Msg)
	require.Equal(t, productListing.Publish.Error.Code, args.Error.Code)
	require.Equal(t, productListing.Publish.LastReferenceID, args.LastReferenceID)
	require.True(t, productListing.Publish.LastFailedAt.UTC().After(time.Now().Add(-10*time.Second)))

	// test case 5 update not exist product_listing
	productListing, err = service.UpdatePublishState(context.Background(), uuid.GenerateUUIDV4(), &args)
	require.Error(t, err, "row not found")
}

func Test_ProductListingServiceImpl_validatePublishState(t *testing.T) {
	service := initService(t)
	require.NotNil(t, service)
	tests := []struct {
		name    string
		current consts.PublishState
		target  consts.PublishState
		want    bool
	}{
		{
			name:    " null to pending",
			current: "",
			target:  consts.PublishStatePending,
			want:    true,
		},
		{
			name:    "pending to pending",
			current: consts.PublishStatePending,
			target:  consts.PublishStatePending,
			want:    true,
		},
		{
			name:    "pending to running",
			current: consts.PublishStatePending,
			target:  consts.PublishStateRunning,
			want:    true,
		},
		{
			name:    "pending to block",
			current: consts.PublishStatePending,
			target:  consts.PublishStateBlocked,
			want:    true,
		},
		{
			name:    "pending to ignored",
			current: consts.PublishStatePending,
			target:  consts.PublishStateIgnored,
			want:    true,
		},
		{
			name:    "running to running",
			current: consts.PublishStateRunning,
			target:  consts.PublishStateRunning,
			want:    true,
		},
		{
			name:    "running to failed",
			current: consts.PublishStateRunning,
			target:  consts.PublishStateFailed,
			want:    true,
		},
		{
			name:    "running to succeeded",
			current: consts.PublishStateRunning,
			target:  consts.PublishStateSucceeded,
			want:    true,
		},
		{
			name:    "running to blocked",
			current: consts.PublishStateRunning,
			target:  consts.PublishStateBlocked,
			want:    true,
		},
		{
			name:    "running to ignored",
			current: consts.PublishStateRunning,
			target:  consts.PublishStateIgnored,
			want:    true,
		},
		{
			name:    "failed to pending",
			current: consts.PublishStateFailed,
			target:  consts.PublishStatePending,
			want:    true,
		},
		{
			name:    "failed to failed",
			current: consts.PublishStateFailed,
			target:  consts.PublishStateFailed,
			want:    true,
		},
		{
			name:    "succeeded to pending",
			current: consts.PublishStateSucceeded,
			target:  consts.PublishStatePending,
			want:    true,
		},
		{
			name:    "succeeded to succeeded",
			current: consts.PublishStateSucceeded,
			target:  consts.PublishStateSucceeded,
			want:    true,
		},
		{
			name:    "ignored to pending",
			current: consts.PublishStateIgnored,
			target:  consts.PublishStatePending,
			want:    true,
		},
		{
			name:    "ignored to ignored",
			current: consts.PublishStateIgnored,
			target:  consts.PublishStateIgnored,
			want:    true,
		},
		{
			name:    "blocked to pending",
			current: consts.PublishStateBlocked,
			target:  consts.PublishStatePending,
			want:    true,
		},
		{
			name:    "blocked to blocked",
			current: consts.PublishStateBlocked,
			target:  consts.PublishStateBlocked,
			want:    true,
		},
		{
			name:    "running to pending",
			current: consts.PublishStateRunning,
			target:  consts.PublishStatePending,
			want:    false,
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			require.Equal(t, service.validatePublishState(test.current, test.target), test.want)
		})
	}
}

func Test_ProductListingServiceImpl_Create(t *testing.T) {
	service := initService(t)
	require.NotNil(t, service)
	createArgs := generateProductListingArgs()

	pl, err := service.Create(context.Background(), &createArgs)
	require.NoError(t, err)
	require.NotNil(t, pl)
	require.Equal(t, 2, len(pl.Relations))
	compareProductListing(t, &createArgs, &pl)
}

func Test_ProductListingServiceImpl_UpdateAuditState(t *testing.T) {
	service := initService(t)
	require.NotNil(t, service)

	pl := productListingDBModel{
		ProductListingID:     uuid.GenerateUUIDV4(),
		State:                consts.ProductListingProductStateActive,
		PublishState:         consts.PublishStatePending,
		OrganizationID:       uuid.GenerateUUIDV4(),
		SalesChannelStoreKey: "test",
		SalesChannelPlatform: "tiktok-shop",
	}
	err := service.repo.plRepo.create(context.Background(), &pl)
	require.NoError(t, err)

	// update audit state
	args := Audit{
		State:        "state",
		LastFailedAt: time.Now(),
		FailedReasons: []AuditFailedReason{
			{
				Position:    "product title",
				Reasons:     []string{"reason1"},
				Suggestions: []string{"suggestions1"},
			},
			{
				Position:    "product description",
				Reasons:     []string{"reason2"},
				Suggestions: []string{"suggestions2"},
			},
		},
	}
	productListing, err := service.UpdateAuditState(context.Background(), pl.ProductListingID, &args)
	require.NoError(t, err)
	require.Equal(t, productListing.State, pl.State)
	require.Equal(t, productListing.Publish.State, pl.PublishState)
	require.Equal(t, productListing.Audit.State, args.State)
	require.Equal(t, productListing.Audit.LastFailedAt.UTC(), args.LastFailedAt.UTC())
	require.Equal(t, len(productListing.Audit.FailedReasons), len(args.FailedReasons))
	for _, argFailedReason := range args.FailedReasons {
		for _, auditFailedReason := range productListing.Audit.FailedReasons {
			if argFailedReason.Position == auditFailedReason.Position {
				require.Equal(t, argFailedReason.Reasons, auditFailedReason.Reasons)
				require.Equal(t, argFailedReason.Suggestions, auditFailedReason.Suggestions)
			}
		}
	}
	// product listing id is empty
	productListing, err = service.UpdateAuditState(context.Background(), "", &args)
	require.Error(t, err)
	require.Errorf(t, err, "NotFound")

	// product listing not found
	productListing, err = service.UpdateAuditState(context.Background(), "not found", &args)
	require.Error(t, err)
	require.Errorf(t, err, "NotFound")
}

func Test_ProductListingServiceImpl_Delete(t *testing.T) {
	service := initService(t)
	require.NotNil(t, service)
	createArgs := generateProductListingArgs()
	createArgs.SalesChannelProduct = SalesChannelProduct{}
	createArgs.Publish = Publish{}
	pl, err := service.Create(context.Background(), &createArgs)
	require.NoError(t, err)
	require.NotNil(t, pl)

	// test case 1 delete product_listing
	err = service.Delete(context.Background(), pl.ID, false)
	require.NoError(t, err)

	pl, err = service.GetByID(context.Background(), pl.ID)
	require.Error(t, err, ErrNotFound)

	// test case 2 delete not exist product_listing
	err = service.Delete(context.Background(), "not found", false)
	require.Error(t, err)

	// test case 3 delete is deleted product_listing
	err = service.Delete(context.Background(), pl.ID, false)
	require.Error(t, err, ErrNotFound)
}

func Test_ProductListingServiceImpl_Delete_force(t *testing.T) {
	service := initService(t)
	require.NotNil(t, service)
	createArgs := generateProductListingArgs()
	createArgs.SalesChannelProduct = SalesChannelProduct{
		State: consts.SalesChannelProductStateLive,
	}
	createArgs.Publish = Publish{}
	pl, err := service.Create(context.Background(), &createArgs)
	require.NoError(t, err)
	require.NotNil(t, pl)

	// test case 1 delete product_listing
	err = service.Delete(context.Background(), pl.ID, true)
	require.NoError(t, err)

	pl, err = service.GetByID(context.Background(), pl.ID)
	require.Error(t, err, ErrNotFound)

	// test case 2 delete not exist product_listing
	err = service.Delete(context.Background(), "not found", false)
	require.Error(t, err)

	// test case 3 delete is deleted product_listing
	err = service.Delete(context.Background(), pl.ID, false)
	require.Error(t, err, ErrNotFound)
}

func loadConfig(t *testing.T) *config.Config {
	configs := new(config.Config)

	_, err := cfg.LoadViperConfig(configs, func(v *viper.Viper) { v.AddConfigPath("../../../cmd/apiserver/conf") })
	require.NoError(t, err)
	configs.DynamicConfigs.ElasticsearchAuth = &config.ElasticsearchAuthConfig{
		Host: "http://localhost:9200",
	}
	configs.DynamicConfigs.ChannelRegionConfig = map[string][]config.ChannelRegionConfig{
		consts.TikTokShop: []config.ChannelRegionConfig{
			{
				Region:          "US",
				Currency:        "USD",
				HasCrossBorder:  true,
				CheckPriceRange: true,
				LocalToLocal: &config.ChannelPriceRange{
					MinPrice: 0.01,
					MaxPrice: 7600,
				},
				CrossBorder: &config.ChannelPriceRange{
					MinPrice: 0.01,
					MaxPrice: 800,
				},
			},
		},
	}

	configs.DynamicConfigs.ChannelRegionConfig = map[string][]config.ChannelRegionConfig{
		consts.TikTokShop: {
			{
				Region:          "US",
				Currency:        "USD",
				HasCrossBorder:  true,
				CheckPriceRange: true,
				LocalToLocal: &config.ChannelPriceRange{
					MinPrice: 0.01,
					MaxPrice: 7600,
				},
				CrossBorder: &config.ChannelPriceRange{
					MinPrice: 0.01,
					MaxPrice: 800,
				},
			},
		},
	}
	require.NoError(t, datastore.Init(configs))
	return configs
}

func initService(t *testing.T) *serviceImpl {
	config := loadConfig(t)
	spannerCli := datastore.Get().SpannerCli
	esCli := datastore.Get().ESClient
	redisCli := datastore.Get().RedisClient
	locker := datastore.Get().RedisLocker
	settingMock := new(settings.MockSettingService)
	settingService := settingMock
	settingMock.On("List", mock.Anything, mock.Anything).Return([]*settings.Setting{
		{
			ID: uuid.GenerateUUIDV4(),
		},
	}, nil)
	categoryMock := new(category.MockCategoryService)
	categoryService := categoryMock
	categoryMock.On("GetCategoryRules", mock.Anything, mock.Anything).Return(category.RulesOutput{
		Rule: category.Rule{
			ProductCertifications: []*category.ProductCertification{},
			SizeChart: &category.SizeChart{
				IsRequired:  false,
				IsSupported: false,
			},
			PackageDimension: &category.PackageDimension{
				IsRequired: false,
			},
		},
	}, nil)
	categoryMock.On("GetCategoryAttributes", mock.Anything, mock.Anything).Return(category.AttributesOutput{
		ExternalCategoryID: "601419",
		Attributes: []category.Attribute{
			{
				ID:         "1",
				IsRequired: false,
			},
		},
	}, nil)

	productsCenterProductMock := new(products_center.MockProductAPICollection)
	productsCenterCli := &products_center.Client{
		Product: productsCenterProductMock,
	}
	productsCenterProductMock.On("GetByID", mock.Anything, mock.Anything).Return(nil, nil)
	productsCenterProductMock.On("List", mock.Anything, mock.Anything).Return([]*products_center.Product{}, nil)

	databusMock := new(databus.MockDatabusService)
	databusMock.On("SendToPubSub", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(nil)
	listingEventsServiceMock := new(events.MockListingEventsService)
	listingEventsServiceMock.On("CommonEvent", mock.Anything, mock.Anything).Return(nil)
	listingEventsServiceMock.On("ModifyStateEvent", mock.Anything, mock.Anything).Return(nil)
	listingLinkEventsServiceMock := new(events.MockListingLinkEventsService)
	listingLinkEventsServiceMock.On("AutoLinkEvent", mock.Anything, mock.Anything).Return(nil)
	listingLinkEventsServiceMock.On("LinkRecordEvent", mock.Anything, mock.Anything).Return(nil)
	feedEventService := &events.FeedEventsService{
		ListingEventsService:     listingEventsServiceMock,
		ListingLinkEventsService: listingLinkEventsServiceMock,
	}

	bmeService := new(bme.MockBmeService)
	bmeService.On("Recording", mock.Anything).Return(nil)
	bmeService.On("CollectBusinessEvents", mock.Anything).Return(nil)
	bmeCli := &bme.Client{
		RecordService: bmeService,
	}

	return NewService(logger.Get(),
		spannerCli, esCli, redisCli, locker, productsCenterCli, settingService,
		nil, categoryService, nil, nil, databusMock, config, nil, nil, feedEventService, bmeCli, nil, nil)
}

func generateProductListingArgs() ProductListingArgs {
	args := ProductListingArgs{}
	orgID := uuid.GenerateUUIDV4()

	productId := strconv.Itoa(int(time.Now().UnixNano()))
	connectorProductID := uuid.GenerateUUIDV4()
	productCenterProductID := uuid.GenerateUUIDV4()
	productCenterConnectorProductID := uuid.GenerateUUIDV4()
	sourceProductID := strconv.Itoa(int(time.Now().UnixNano()))

	args.SalesChannel = models.SalesChannel{
		StoreKey:      "store_key",
		Platform:      "tiktok-shop",
		CountryRegion: "US",
	}
	args.Organization = models.Organization{
		ID: orgID,
	}
	args.SalesChannelProduct = SalesChannelProduct{
		ID:                 productId,
		ConnectorProductID: connectorProductID,
		State:              "default",
	}
	args.SalesChannelProduct.Metrics = SalesChannelProductMetrics{
		CreatedAt: time.Now().UTC(),
		UpdatedAt: time.Now().UTC(),
	}
	args.ProductsCenterProduct = ProductsCenterProduct{
		ID:                 productCenterProductID,
		ConnectorProductID: productCenterConnectorProductID,
		PublishState:       consts.ProductsCenterProductPublishStateActive,
	}
	args.ProductsCenterProduct.Source = ProductsCenterProductSource{
		StoreKey: "source_store_key",
		Platform: "shopify",
		ID:       sourceProductID,
	}
	args.Settings = generateProductListingSettings()
	args.Product = generateProductListingProduct()
	args.Relations = []*ProductListingRelation{
		{
			VariantPosition: 1,
			SalesChannelVariant: SalesChannelVariant{
				ID:                 strconv.Itoa(int(time.Now().UnixNano())),
				ConnectorProductID: connectorProductID,
				ProductID:          productId,
			},
			ProductsCenterVariant: ProductsCenterVariant{
				ID:                 uuid.GenerateUUIDV4(),
				ConnectorProductID: productCenterConnectorProductID,
				ProductID:          productCenterProductID,
				Source: ProductsCenterVariantSource{
					StoreKey:  "source_store_key",
					Platform:  "shopify",
					ID:        strconv.Itoa(int(time.Now().UnixNano())),
					ProductID: sourceProductID,
					Sku:       "1008",
				},
			},
			SyncStatus: consts.SyncStatusSynced,
			LinkStatus: consts.LinkStatusLinked,
			AllowSync:  consts.AllowSyncEnabled,
		},
		{
			VariantPosition: 2,
			SalesChannelVariant: SalesChannelVariant{
				ID:                 strconv.Itoa(int(time.Now().UnixNano())),
				ConnectorProductID: connectorProductID,
				ProductID:          productId,
			},
			ProductsCenterVariant: ProductsCenterVariant{
				ID:                 uuid.GenerateUUIDV4(),
				ConnectorProductID: productCenterConnectorProductID,
				ProductID:          productCenterProductID,
				Source: ProductsCenterVariantSource{
					StoreKey:  "source_store_key",
					Platform:  "shopify",
					ID:        strconv.Itoa(int(time.Now().UnixNano())),
					ProductID: sourceProductID,
					Sku:       "1009",
				},
			},
			SyncStatus: consts.SyncStatusSynced,
			LinkStatus: consts.LinkStatusLinked,
			AllowSync:  consts.AllowSyncEnabled,
		},
	}
	return args
}

func generateMatchProductListingArgs() ProductListingArgs {
	args := ProductListingArgs{}
	orgID := uuid.GenerateUUIDV4()

	productId := strconv.Itoa(int(time.Now().UnixNano()))
	connectorProductID := uuid.GenerateUUIDV4()
	productCenterProductID := "pc_id_1"
	productCenterConnectorProductID := uuid.GenerateUUIDV4()
	sourceProductID := strconv.Itoa(int(time.Now().UnixNano()))

	args.SalesChannel = models.SalesChannel{
		StoreKey:      "store_key",
		Platform:      "tiktok-shop",
		CountryRegion: "US",
	}
	args.Organization = models.Organization{
		ID: orgID,
	}
	args.SalesChannelProduct = SalesChannelProduct{
		ID:                 productId,
		ConnectorProductID: connectorProductID,
		State:              "default",
	}
	args.SalesChannelProduct.Metrics = SalesChannelProductMetrics{
		CreatedAt: time.Now().UTC(),
		UpdatedAt: time.Now().UTC(),
	}
	args.ProductsCenterProduct = ProductsCenterProduct{}
	args.Settings = generateProductListingSettings()
	args.Product = generateProductListingProduct()
	args.Relations = []*ProductListingRelation{
		{
			VariantPosition: 1,
			SalesChannelVariant: SalesChannelVariant{
				ID:                 strconv.Itoa(int(time.Now().UnixNano())),
				ConnectorProductID: connectorProductID,
				ProductID:          productId,
			},
			ProductsCenterVariant: ProductsCenterVariant{
				ID:                 "pc_id_variant_1",
				ConnectorProductID: productCenterConnectorProductID,
				ProductID:          productCenterProductID,
				Source: ProductsCenterVariantSource{
					StoreKey:  "source_store_key",
					Platform:  "shopify",
					ID:        "pc_id_variant_1",
					ProductID: sourceProductID,
					Sku:       "1008",
				},
			},
			SyncStatus: consts.SyncStatusSynced,
			LinkStatus: consts.LinkStatusLinked,
			AllowSync:  consts.AllowSyncEnabled,
		},
		{
			VariantPosition: 2,
			SalesChannelVariant: SalesChannelVariant{
				ID:                 strconv.Itoa(int(time.Now().UnixNano())),
				ConnectorProductID: connectorProductID,
				ProductID:          productId,
			},
			ProductsCenterVariant: ProductsCenterVariant{
				ID:                 "pc_id_variant_2",
				ConnectorProductID: productCenterConnectorProductID,
				ProductID:          productCenterProductID,
				Source: ProductsCenterVariantSource{
					StoreKey:  "source_store_key",
					Platform:  "shopify",
					ID:        "pc_id_variant_2",
					ProductID: sourceProductID,
					Sku:       "1009",
				},
			},
			SyncStatus: consts.SyncStatusSynced,
			LinkStatus: consts.LinkStatusLinked,
			AllowSync:  consts.AllowSyncEnabled,
		},
	}
	return args
}

func generateAutoLinkProductListingArgs() ProductListingArgs {
	args := ProductListingArgs{}
	orgID := uuid.GenerateUUIDV4()

	productId := strconv.Itoa(int(time.Now().UnixNano()))
	connectorProductID := uuid.GenerateUUIDV4()
	productCenterProductID := uuid.GenerateUUIDV4()
	productCenterConnectorProductID := uuid.GenerateUUIDV4()
	sourceProductID := strconv.Itoa(int(time.Now().UnixNano()))

	args.SalesChannel = models.SalesChannel{
		StoreKey: "store_key",
		Platform: "tiktok-shop",
	}
	args.Organization = models.Organization{
		ID: orgID,
	}
	args.SalesChannelProduct = SalesChannelProduct{
		ID:                 productId,
		ConnectorProductID: connectorProductID,
		State:              "default",
	}
	args.SalesChannelProduct.Metrics = SalesChannelProductMetrics{
		CreatedAt: time.Now().UTC(),
		UpdatedAt: time.Now().UTC(),
	}
	args.ProductsCenterProduct = ProductsCenterProduct{
		ID:                 productCenterProductID,
		ConnectorProductID: productCenterConnectorProductID,
		PublishState:       consts.ProductsCenterProductPublishStateActive,
	}
	args.ProductsCenterProduct.Source = ProductsCenterProductSource{
		StoreKey: "source_store_key",
		Platform: "shopify",
		ID:       sourceProductID,
	}
	args.Settings = generateProductListingSettings()
	args.Product = generateProductListingProduct()
	args.Relations = []*ProductListingRelation{
		{
			VariantPosition: 1,
			SalesChannelVariant: SalesChannelVariant{
				ID:                 strconv.Itoa(int(time.Now().UnixNano())),
				ConnectorProductID: connectorProductID,
				ProductID:          productId,
				Sku:                "1008",
			},
			SyncStatus: consts.SyncStatusSynced,
			LinkStatus: consts.LinkStatusUnlink,
			AllowSync:  consts.AllowSyncEnabled,
		},
		{
			VariantPosition: 2,
			SalesChannelVariant: SalesChannelVariant{
				ID:                 strconv.Itoa(int(time.Now().UnixNano())),
				ConnectorProductID: connectorProductID,
				ProductID:          productId,
				Sku:                "1008",
			},
			SyncStatus: consts.SyncStatusSynced,
			LinkStatus: consts.LinkStatusUnlink,
			AllowSync:  consts.AllowSyncEnabled,
		},
	}
	return args
}

func generateAutoLinkProductListingArgs_no_relation_singer_product() ProductListingArgs {
	args := ProductListingArgs{}
	orgID := uuid.GenerateUUIDV4()

	productId := strconv.Itoa(int(time.Now().UnixNano()))
	connectorProductID := uuid.GenerateUUIDV4()
	// productCenterProductID := uuid.GenerateUUIDV4()
	// productCenterConnectorProductID := uuid.GenerateUUIDV4()
	// sourceProductID := strconv.Itoa(int(time.Now().UnixNano()))

	args.SalesChannel = models.SalesChannel{
		StoreKey: "store_key",
		Platform: "tiktok-shop",
	}
	args.Organization = models.Organization{
		ID: orgID,
	}
	args.SalesChannelProduct = SalesChannelProduct{
		ID:                 productId,
		ConnectorProductID: connectorProductID,
		State:              "default",
	}
	args.SalesChannelProduct.Metrics = SalesChannelProductMetrics{
		CreatedAt: time.Now().UTC(),
		UpdatedAt: time.Now().UTC(),
	}
	// args.ProductsCenterProduct = ProductsCenterProduct{
	//	ID:                 productCenterProductID,
	//	ConnectorProductID: productCenterConnectorProductID,
	//	PublishState:       consts.ProductsCenterProductPublishStateActive,
	// }
	// args.ProductsCenterProduct.Source = ProductsCenterProductSource{
	//	StoreKey: "source_store_key",
	//	Platform: "shopify",
	//	ID:       sourceProductID,
	// }
	args.Settings = generateProductListingSettings()
	args.Product = generateProductListingProduct_singer_product()
	args.Relations = []*ProductListingRelation{
		{
			VariantPosition: 1,
			SalesChannelVariant: SalesChannelVariant{
				ID:                 strconv.Itoa(int(time.Now().UnixNano())),
				ConnectorProductID: connectorProductID,
				ProductID:          productId,
				Sku:                "sku_1",
			},
			SyncStatus: consts.SyncStatusSynced,
			LinkStatus: consts.LinkStatusUnlink,
			AllowSync:  consts.AllowSyncEnabled,
		},
	}
	return args
}

func generateAutoLinkProductListingArgs_pcpID() ProductListingArgs {
	args := ProductListingArgs{}
	orgID := uuid.GenerateUUIDV4()

	productId := strconv.Itoa(int(time.Now().UnixNano()))
	connectorProductID := uuid.GenerateUUIDV4()
	// productCenterProductID := uuid.GenerateUUIDV4()
	// productCenterConnectorProductID := uuid.GenerateUUIDV4()
	// sourceProductID := strconv.Itoa(int(time.Now().UnixNano()))

	args.SalesChannel = models.SalesChannel{
		StoreKey: "store_key",
		Platform: "tiktok-shop",
	}
	args.Organization = models.Organization{
		ID: orgID,
	}
	args.SalesChannelProduct = SalesChannelProduct{
		ID:                 productId,
		ConnectorProductID: connectorProductID,
		State:              "default",
	}
	args.SalesChannelProduct.Metrics = SalesChannelProductMetrics{
		CreatedAt: time.Now().UTC(),
		UpdatedAt: time.Now().UTC(),
	}
	args.Settings = generateProductListingSettings()
	args.Product = generateProductListingProduct()
	args.Relations = []*ProductListingRelation{
		{
			VariantPosition: 1,
			SalesChannelVariant: SalesChannelVariant{
				ID:                 "variant_1",
				ConnectorProductID: connectorProductID,
				ProductID:          productId,
				Sku:                "1008",
			},
			SyncStatus: consts.SyncStatusSynced,
			LinkStatus: consts.LinkStatusUnlink,
			AllowSync:  consts.AllowSyncEnabled,
		},
		{
			VariantPosition: 2,
			SalesChannelVariant: SalesChannelVariant{
				ID:                 "variant_2",
				ConnectorProductID: connectorProductID,
				ProductID:          productId,
				Sku:                "1009",
			},
			SyncStatus: consts.SyncStatusSynced,
			LinkStatus: consts.LinkStatusUnlink,
			AllowSync:  consts.AllowSyncEnabled,
		},
	}
	return args
}

func generateAutoLinkProductListingArgs_reference_id() ProductListingArgs {
	args := ProductListingArgs{}
	orgID := uuid.GenerateUUIDV4()

	productId := strconv.Itoa(int(time.Now().UnixNano()))
	connectorProductID := uuid.GenerateUUIDV4()
	// productCenterProductID := uuid.GenerateUUIDV4()
	// productCenterConnectorProductID := uuid.GenerateUUIDV4()
	// sourceProductID := strconv.Itoa(int(time.Now().UnixNano()))

	args.SalesChannel = models.SalesChannel{
		StoreKey: "store_key",
		Platform: "tiktok-shop",
	}
	args.Organization = models.Organization{
		ID: orgID,
	}
	args.SalesChannelProduct = SalesChannelProduct{
		ID:                 productId,
		ConnectorProductID: connectorProductID,
		State:              "default",
	}
	args.SalesChannelProduct.Metrics = SalesChannelProductMetrics{
		CreatedAt: time.Now().UTC(),
		UpdatedAt: time.Now().UTC(),
	}
	args.Settings = generateProductListingSettings()
	args.Product = generateProductListingProduct()
	args.Relations = []*ProductListingRelation{
		{
			VariantPosition: 1,
			SalesChannelVariant: SalesChannelVariant{
				ID:                 "variant_1",
				ConnectorProductID: connectorProductID,
				ProductID:          productId,
				Sku:                "sku_1",
			},
			SyncStatus: consts.SyncStatusSynced,
			LinkStatus: consts.LinkStatusUnlink,
			AllowSync:  consts.AllowSyncEnabled,
		},
		{
			VariantPosition: 2,
			SalesChannelVariant: SalesChannelVariant{
				ID:                 "variant_2",
				ConnectorProductID: connectorProductID,
				ProductID:          productId,
				Sku:                "sku_2",
			},
			SyncStatus: consts.SyncStatusSynced,
			LinkStatus: consts.LinkStatusUnlink,
			AllowSync:  consts.AllowSyncEnabled,
		},
	}
	return args
}

func generateAutoLinkProductListingArgs_no_relation() ProductListingArgs {
	args := ProductListingArgs{}
	orgID := uuid.GenerateUUIDV4()

	productId := strconv.Itoa(int(time.Now().UnixNano()))
	connectorProductID := uuid.GenerateUUIDV4()
	// productCenterProductID := uuid.GenerateUUIDV4()
	// productCenterConnectorProductID := uuid.GenerateUUIDV4()
	// sourceProductID := strconv.Itoa(int(time.Now().UnixNano()))

	args.SalesChannel = models.SalesChannel{
		StoreKey: "store_key",
		Platform: "tiktok-shop",
	}
	args.Organization = models.Organization{
		ID: orgID,
	}
	args.SalesChannelProduct = SalesChannelProduct{
		ID:                 productId,
		ConnectorProductID: connectorProductID,
		State:              "default",
	}
	args.SalesChannelProduct.Metrics = SalesChannelProductMetrics{
		CreatedAt: time.Now().UTC(),
		UpdatedAt: time.Now().UTC(),
	}
	// args.ProductsCenterProduct = ProductsCenterProduct{
	//	ID:                 productCenterProductID,
	//	ConnectorProductID: productCenterConnectorProductID,
	//	PublishState:       consts.ProductsCenterProductPublishStateActive,
	// }
	// args.ProductsCenterProduct.Source = ProductsCenterProductSource{
	//	StoreKey: "source_store_key",
	//	Platform: "shopify",
	//	ID:       sourceProductID,
	// }
	args.Settings = generateProductListingSettings()
	args.Product = generateProductListingProduct()
	args.Relations = []*ProductListingRelation{
		{
			VariantPosition: 1,
			SalesChannelVariant: SalesChannelVariant{
				ID:                 strconv.Itoa(int(time.Now().UnixNano())),
				ConnectorProductID: connectorProductID,
				ProductID:          productId,
				Sku:                "sku_1",
			},
			SyncStatus: consts.SyncStatusSynced,
			LinkStatus: consts.LinkStatusUnlink,
			AllowSync:  consts.AllowSyncEnabled,
		},
		{
			VariantPosition: 2,
			SalesChannelVariant: SalesChannelVariant{
				ID:                 strconv.Itoa(int(time.Now().UnixNano())),
				ConnectorProductID: connectorProductID,
				ProductID:          productId,
				Sku:                "sku_2",
			},
			SyncStatus: consts.SyncStatusSynced,
			LinkStatus: consts.LinkStatusUnlink,
			AllowSync:  consts.AllowSyncEnabled,
		},
	}
	return args
}

func generateProductListingProduct_singer_product() models.Product {
	product := models.Product{
		Title:            "Snuggle Blooming Bouquet Fabric Conditioner 4L + Snuggle Bear Plushie",
		ShortDescription: "Snuggle Blooming Bouquet Fabric Conditioner 4L + Snuggle Bear Plushie",
		Vendor:           "The Good Cart SG",
		Description:      "Snuggle Blooming Bouquet Fabric Conditioner 4L + Snuggle Bear Plushie",
	}
	product.Categories = []*models.SalesChannelResource{
		{
			SalesChannelID: "853000",
			Name:           "Botol & Stoples Penyimpanan",
		},
	}
	product.Tags = []string{
		"123",
		"456",
	}
	product.Brand = models.SalesChannelResource{
		SalesChannelID: "7082427311584347905",
		Name:           "brand xxx aaa",
	}

	product.SizeChart = models.ProductSizeChart{
		Images: []models.SalesChannelFile{
			{
				SalesChannelID: "tos-maliva-i-o3syd03w52-us/c668cdf70b7f483c94dbe",
				URL:            "https://p16-oec-va.ibyteimg.com/tos-maliva-i-o3syd03w52-us/c668cdf70b7f483c94dbe~tplv-o3syd03w52-origin-jpeg.jpeg?from=520841845",
			},
		},
	}
	product.Certifications = []*models.ProductCertification{
		{
			SalesChannelID: "123456",
			Files: []models.SalesChannelFile{
				{
					SalesChannelID: "tos-maliva-i-o3syd03w52-us/c668cdf70b7f483c94dbe",
					URL:            "https://p16-oec-va.ibyteimg.com/tos-maliva-i-o3syd03w52-us/c668cdf70b7f483c94dbe",
				},
			},
			Images: []models.SalesChannelFile{
				{
					SalesChannelID: "tos-maliva-i-o3syd03w52-us/c668cdf70b7f483c94dbe",
					URL:            "https://p16-oec-va.ibyteimg.com/tos-maliva-i-o3syd03w52-us/c668cdf70b7f483c94dbe~tplv-o3syd03w52-origin-jpeg.jpeg?from=520841845",
				},
			},
		},
	}
	product.Attributes = []*models.ProductAttribute{
		{
			SalesChannelID: "100492",
			Name:           "Manufacturer",
			Values: []models.SalesChannelResource{
				{
					SalesChannelID: "1001533",
					Name:           "Pokemon",
				},
			},
		},
	}
	product.Variants = []*models.ProductVariant{
		{
			ID:                "001_id",
			InventoryQuantity: 123,
			Sku:               "1008",
			Barcode: models.ProductVariantBarcode{
				Value: "0195751059831",
				Type:  "ean",
			},
			Title: "te12",
			Price: models.ProductVariantPrice{
				Currency: "SGD",
				Amount:   "19.6",
			},
			Cost: models.ProductVariantPrice{
				Currency: "SGD",
				Amount:   "19.6",
			},
			ImageURL: "https://cdn.shopify.com/s/files/1/0629/9697/1763/products/sssgid.zone-1703053820-sssgid.zone-1698370622-SNUGGLE_BLOOMING_BOUQUET.png?v=1703053851",
			CompareAtPrice: models.ProductVariantPrice{
				Currency: "SGD",
				Amount:   "0",
			},
			Length: models.ProductVariantShippingSetting{
				Unit:  "123",
				Value: 123,
			},
			Width: models.ProductVariantShippingSetting{
				Unit:  "123",
				Value: 123,
			},
			Height: models.ProductVariantShippingSetting{
				Unit:  "123",
				Value: 123,
			},
			Weight: models.ProductVariantShippingSetting{
				Unit:  "123",
				Value: 123,
			},
			AllowBackorder:     false,
			FulfillmentService: "manual",
			RequiresShipping:   false,
			Position:           1,
		},
	}
	product.Media = []*models.ProductMedia{
		{
			Type:              "image",
			Position:          1,
			URL:               "https://cdn.shopify.com/s/files/1/0629/9697/1763/products/sssgid.zone-1703053820-sssgid.zone-1698370622-SNUGGLE_BLOOMING_BOUQUET.png?v=1703053851",
			MimeType:          "123",
			ExternalVideoHost: "123",
			Thumbnail: models.ProductMediaThumbnail{
				URL: "https://cdn.shopify.com/s/files/1/0629/9697/1763/products/sssgid.zone-1703053820-sssgid.zone-1698370622-SNUGGLE_BLOOMING_BOUQUET.png?v=1703053851",
			},
		},
	}
	return product
}

func generateAutoLinkProductListingArgs_multiple_relations() ProductListingArgs {
	args := ProductListingArgs{}
	orgID := uuid.GenerateUUIDV4()

	productId := strconv.Itoa(int(time.Now().UnixNano()))
	connectorProductID := uuid.GenerateUUIDV4()
	productCenterProductID := "pc_1"
	productCenterConnectorProductID := uuid.GenerateUUIDV4()
	sourceProductID := strconv.Itoa(int(time.Now().UnixNano()))

	args.SalesChannel = models.SalesChannel{
		StoreKey: "store_key",
		Platform: "tiktok-shop",
	}
	args.Organization = models.Organization{
		ID: orgID,
	}
	args.SalesChannelProduct = SalesChannelProduct{
		ID:                 productId,
		ConnectorProductID: connectorProductID,
		State:              "default",
	}
	args.SalesChannelProduct.Metrics = SalesChannelProductMetrics{
		CreatedAt: time.Now().UTC(),
		UpdatedAt: time.Now().UTC(),
	}
	args.ProductsCenterProduct = ProductsCenterProduct{
		ID:                 productCenterProductID,
		ConnectorProductID: productCenterConnectorProductID,
		PublishState:       consts.ProductsCenterProductPublishStateActive,
	}
	args.ProductsCenterProduct.Source = ProductsCenterProductSource{
		StoreKey: "source_store_key",
		Platform: "shopify",
		ID:       sourceProductID,
	}
	args.Settings = generateProductListingSettings()
	args.Product = generateProductListingProductMultipleSKUs()
	args.Relations = []*ProductListingRelation{
		{
			VariantPosition: 1,
			SalesChannelVariant: SalesChannelVariant{
				ID:                 strconv.Itoa(int(time.Now().UnixNano())),
				ConnectorProductID: connectorProductID,
				ProductID:          productId,
			},
			SyncStatus:              consts.SyncStatusSynced,
			LinkStatus:              consts.LinkStatusUnlink,
			AllowSync:               consts.AllowSyncEnabled,
			ProductListingVariantID: "v_1",
			ProductsCenterVariant: ProductsCenterVariant{
				ProductID: "pc_id_1",
				ID:        "pc_id_1_01",
			},
		},
		{
			VariantPosition: 2,
			SalesChannelVariant: SalesChannelVariant{
				ID:                 strconv.Itoa(int(time.Now().UnixNano())),
				ConnectorProductID: connectorProductID,
				ProductID:          productId,
			},
			SyncStatus:              consts.SyncStatusSynced,
			LinkStatus:              consts.LinkStatusUnlink,
			AllowSync:               consts.AllowSyncEnabled,
			ProductListingVariantID: "v_2",
			ProductsCenterVariant: ProductsCenterVariant{
				ProductID: "pc_id_2",
				ID:        "pc_id_2_01",
			},
		},
		{
			VariantPosition: 3,
			SalesChannelVariant: SalesChannelVariant{
				ID:                 strconv.Itoa(int(time.Now().UnixNano())),
				ConnectorProductID: connectorProductID,
				ProductID:          productId,
			},
			ProductListingVariantID: "v_3",
			SyncStatus:              consts.SyncStatusSynced,
			LinkStatus:              consts.LinkStatusUnlink,
			AllowSync:               consts.AllowSyncEnabled,
		},
		{
			VariantPosition: 4,
			SalesChannelVariant: SalesChannelVariant{
				ID:                 strconv.Itoa(int(time.Now().UnixNano())),
				ConnectorProductID: connectorProductID,
				ProductID:          productId,
			},
			ProductListingVariantID: "v_4",
			SyncStatus:              consts.SyncStatusSynced,
			LinkStatus:              consts.LinkStatusUnlink,
			AllowSync:               consts.AllowSyncEnabled,
		},
		{
			VariantPosition: 4,
			SalesChannelVariant: SalesChannelVariant{
				ID:                 strconv.Itoa(int(time.Now().UnixNano())),
				ConnectorProductID: connectorProductID,
				ProductID:          productId,
			},
			ProductListingVariantID: "v_5",
			SyncStatus:              consts.SyncStatusSynced,
			LinkStatus:              consts.LinkStatusUnlink,
			AllowSync:               consts.AllowSyncEnabled,
		},
	}
	return args
}

func generateProductListingProduct() models.Product {
	product := models.Product{
		Title:            "Snuggle Blooming Bouquet Fabric Conditioner 4L + Snuggle Bear Plushie",
		ShortDescription: "Snuggle Blooming Bouquet Fabric Conditioner 4L + Snuggle Bear Plushie",
		Vendor:           "The Good Cart SG",
		Description:      "Snuggle Blooming Bouquet Fabric Conditioner 4L + Snuggle Bear Plushie",
	}
	product.Categories = []*models.SalesChannelResource{
		{
			SalesChannelID: "853000",
			Name:           "Botol & Stoples Penyimpanan",
		},
	}
	product.Tags = []string{
		"123",
		"456",
	}
	product.Brand = models.SalesChannelResource{
		SalesChannelID: "7082427311584347905",
		Name:           "brand xxx aaa",
	}

	product.SizeChart = models.ProductSizeChart{
		Images: []models.SalesChannelFile{
			{
				SalesChannelID: "tos-maliva-i-o3syd03w52-us/c668cdf70b7f483c94dbe",
				URL:            "https://p16-oec-va.ibyteimg.com/tos-maliva-i-o3syd03w52-us/c668cdf70b7f483c94dbe~tplv-o3syd03w52-origin-jpeg.jpeg?from=520841845",
			},
		},
	}
	product.Certifications = []*models.ProductCertification{
		{
			SalesChannelID: "123456",
			Files: []models.SalesChannelFile{
				{
					SalesChannelID: "tos-maliva-i-o3syd03w52-us/c668cdf70b7f483c94dbe",
					URL:            "https://p16-oec-va.ibyteimg.com/tos-maliva-i-o3syd03w52-us/c668cdf70b7f483c94dbe",
				},
			},
			Images: []models.SalesChannelFile{
				{
					SalesChannelID: "tos-maliva-i-o3syd03w52-us/c668cdf70b7f483c94dbe",
					URL:            "https://p16-oec-va.ibyteimg.com/tos-maliva-i-o3syd03w52-us/c668cdf70b7f483c94dbe~tplv-o3syd03w52-origin-jpeg.jpeg?from=520841845",
				},
			},
		},
	}
	product.Attributes = []*models.ProductAttribute{
		{
			SalesChannelID: "100492",
			Name:           "Manufacturer",
			Values: []models.SalesChannelResource{
				{
					SalesChannelID: "1001533",
					Name:           "Pokemon",
				},
			},
		},
	}
	product.Variants = []*models.ProductVariant{
		{
			ID:                "001_id",
			InventoryQuantity: 123,
			Sku:               "1008",
			Barcode: models.ProductVariantBarcode{
				Value: "0195751059831",
				Type:  "ean",
			},
			Title: "te12",
			Price: models.ProductVariantPrice{
				Currency: "SGD",
				Amount:   "19.6",
			},
			Cost: models.ProductVariantPrice{
				Currency: "SGD",
				Amount:   "19.6",
			},
			ImageURL: "https://cdn.shopify.com/s/files/1/0629/9697/1763/products/sssgid.zone-1703053820-sssgid.zone-1698370622-SNUGGLE_BLOOMING_BOUQUET.png?v=1703053851",
			CompareAtPrice: models.ProductVariantPrice{
				Currency: "SGD",
				Amount:   "0",
			},
			Length: models.ProductVariantShippingSetting{
				Unit:  "123",
				Value: 123,
			},
			Width: models.ProductVariantShippingSetting{
				Unit:  "123",
				Value: 123,
			},
			Height: models.ProductVariantShippingSetting{
				Unit:  "123",
				Value: 123,
			},
			Weight: models.ProductVariantShippingSetting{
				Unit:  "123",
				Value: 123,
			},
			AllowBackorder: false,
			Options: []*models.ProductVariantOption{
				{
					Name:  "Color",
					Value: "Black",
				},
				{
					Name:  "Size",
					Value: "XL",
				},
			},
			FulfillmentService: "manual",
			RequiresShipping:   false,
			Position:           1,
		},
		{
			InventoryQuantity: 123,
			Sku:               "1009",
			Barcode: models.ProductVariantBarcode{
				Value: "0195751059831",
				Type:  "ean",
			},
			Title: "te12",
			Price: models.ProductVariantPrice{
				Currency: "SGD",
				Amount:   "19.6",
			},
			Cost: models.ProductVariantPrice{
				Currency: "SGD",
				Amount:   "19.6",
			},
			ImageURL: "https://cdn.shopify.com/s/files/1/0629/9697/1763/products/sssgid.zone-1703053820-sssgid.zone-1698370622-SNUGGLE_BLOOMING_BOUQUET.png?v=1703053851",
			CompareAtPrice: models.ProductVariantPrice{
				Currency: "SGD",
				Amount:   "0",
			},
			Length: models.ProductVariantShippingSetting{
				Unit:  "123",
				Value: 123,
			},
			Width: models.ProductVariantShippingSetting{
				Unit:  "123",
				Value: 123,
			},
			Height: models.ProductVariantShippingSetting{
				Unit:  "123",
				Value: 123,
			},
			Weight: models.ProductVariantShippingSetting{
				Unit:  "123",
				Value: 123,
			},
			AllowBackorder: false,
			Options: []*models.ProductVariantOption{
				{
					Name:  "Color",
					Value: "Black",
				},
				{
					Name:  "Size",
					Value: "L",
				},
			},
			FulfillmentService: "manual",
			RequiresShipping:   false,
			Position:           2,
		},
	}
	product.Options = []*models.ProductOption{
		{
			Name:     "Color",
			Position: 1,
			Values: []string{
				"Red",
				"Blue",
			},
		},
		{
			Name:     "Size",
			Position: 2,
			Values: []string{
				"XL",
				"L",
			},
		},
	}
	product.Media = []*models.ProductMedia{
		{
			Type:              "image",
			Position:          1,
			URL:               "https://cdn.shopify.com/s/files/1/0629/9697/1763/products/sssgid.zone-1703053820-sssgid.zone-1698370622-SNUGGLE_BLOOMING_BOUQUET.png?v=1703053851",
			MimeType:          "123",
			ExternalVideoHost: "123",
			Thumbnail: models.ProductMediaThumbnail{
				URL: "https://cdn.shopify.com/s/files/1/0629/9697/1763/products/sssgid.zone-1703053820-sssgid.zone-1698370622-SNUGGLE_BLOOMING_BOUQUET.png?v=1703053851",
			},
		},
	}
	return product
}

// nolint:maintidx
func generateProductListingProductMultipleSKUs() models.Product {
	product := models.Product{
		Title:            "auto-link-title-1",
		ShortDescription: "Snuggle Blooming Bouquet Fabric Conditioner 4L + Snuggle Bear Plushie",
		Vendor:           "The Good Cart SG",
		Description:      "Snuggle Blooming Bouquet Fabric Conditioner 4L + Snuggle Bear Plushie",
	}
	product.Categories = []*models.SalesChannelResource{
		{
			SalesChannelID: "853000",
			Name:           "Botol & Stoples Penyimpanan",
		},
	}
	product.Tags = []string{
		"123",
		"456",
	}
	product.Brand = models.SalesChannelResource{
		SalesChannelID: "7082427311584347905",
		Name:           "brand xxx aaa",
	}

	product.SizeChart = models.ProductSizeChart{
		Images: []models.SalesChannelFile{
			{
				SalesChannelID: "tos-maliva-i-o3syd03w52-us/c668cdf70b7f483c94dbe",
				URL:            "https://p16-oec-va.ibyteimg.com/tos-maliva-i-o3syd03w52-us/c668cdf70b7f483c94dbe~tplv-o3syd03w52-origin-jpeg.jpeg?from=520841845",
			},
		},
	}
	product.Certifications = []*models.ProductCertification{
		{
			SalesChannelID: "123456",
			Files: []models.SalesChannelFile{
				{
					SalesChannelID: "tos-maliva-i-o3syd03w52-us/c668cdf70b7f483c94dbe",
					URL:            "https://p16-oec-va.ibyteimg.com/tos-maliva-i-o3syd03w52-us/c668cdf70b7f483c94dbe",
				},
			},
			Images: []models.SalesChannelFile{
				{
					SalesChannelID: "tos-maliva-i-o3syd03w52-us/c668cdf70b7f483c94dbe",
					URL:            "https://p16-oec-va.ibyteimg.com/tos-maliva-i-o3syd03w52-us/c668cdf70b7f483c94dbe~tplv-o3syd03w52-origin-jpeg.jpeg?from=520841845",
				},
			},
		},
	}
	product.Attributes = []*models.ProductAttribute{
		{
			SalesChannelID: "100492",
			Name:           "Manufacturer",
			Values: []models.SalesChannelResource{
				{
					SalesChannelID: "1001533",
					Name:           "Pokemon",
				},
			},
		},
	}
	product.Variants = []*models.ProductVariant{
		{
			ID:                "v_1",
			InventoryQuantity: 123,
			Sku:               "1008",
			Barcode: models.ProductVariantBarcode{
				Value: "0195751059831",
				Type:  "ean",
			},
			Title: "te12",
			Price: models.ProductVariantPrice{
				Currency: "SGD",
				Amount:   "19.6",
			},
			Cost: models.ProductVariantPrice{
				Currency: "SGD",
				Amount:   "19.6",
			},
			ImageURL: "https://cdn.shopify.com/s/files/1/0629/9697/1763/products/sssgid.zone-1703053820-sssgid.zone-1698370622-SNUGGLE_BLOOMING_BOUQUET.png?v=1703053851",
			CompareAtPrice: models.ProductVariantPrice{
				Currency: "SGD",
				Amount:   "0",
			},
			Length: models.ProductVariantShippingSetting{
				Unit:  "123",
				Value: 123,
			},
			Width: models.ProductVariantShippingSetting{
				Unit:  "123",
				Value: 123,
			},
			Height: models.ProductVariantShippingSetting{
				Unit:  "123",
				Value: 123,
			},
			Weight: models.ProductVariantShippingSetting{
				Unit:  "123",
				Value: 123,
			},
			AllowBackorder: false,
			Options: []*models.ProductVariantOption{
				{
					Name:  "Color",
					Value: "Black",
				},
				{
					Name:  "Size",
					Value: "XL",
				},
			},
			FulfillmentService: "manual",
			RequiresShipping:   false,
			Position:           1,
		},
		{
			ID:                "v_2",
			InventoryQuantity: 123,
			Sku:               "1009",
			Barcode: models.ProductVariantBarcode{
				Value: "0195751059831",
				Type:  "ean",
			},
			Title: "te12",
			Price: models.ProductVariantPrice{
				Currency: "SGD",
				Amount:   "19.6",
			},
			Cost: models.ProductVariantPrice{
				Currency: "SGD",
				Amount:   "19.6",
			},
			ImageURL: "https://cdn.shopify.com/s/files/1/0629/9697/1763/products/sssgid.zone-1703053820-sssgid.zone-1698370622-SNUGGLE_BLOOMING_BOUQUET.png?v=1703053851",
			CompareAtPrice: models.ProductVariantPrice{
				Currency: "SGD",
				Amount:   "0",
			},
			Length: models.ProductVariantShippingSetting{
				Unit:  "123",
				Value: 123,
			},
			Width: models.ProductVariantShippingSetting{
				Unit:  "123",
				Value: 123,
			},
			Height: models.ProductVariantShippingSetting{
				Unit:  "123",
				Value: 123,
			},
			Weight: models.ProductVariantShippingSetting{
				Unit:  "123",
				Value: 123,
			},
			AllowBackorder: false,
			Options: []*models.ProductVariantOption{
				{
					Name:  "Color",
					Value: "Black",
				},
				{
					Name:  "Size",
					Value: "L",
				},
			},
			FulfillmentService: "manual",
			RequiresShipping:   false,
			Position:           2,
		},
		{
			ID:                "v_3",
			InventoryQuantity: 123,
			Sku:               "1010",
			Barcode: models.ProductVariantBarcode{
				Value: "0195751059831",
				Type:  "ean",
			},
			Title: "te12",
			Price: models.ProductVariantPrice{
				Currency: "SGD",
				Amount:   "19.6",
			},
			Cost: models.ProductVariantPrice{
				Currency: "SGD",
				Amount:   "19.6",
			},
			ImageURL: "https://cdn.shopify.com/s/files/1/0629/9697/1763/products/sssgid.zone-1703053820-sssgid.zone-1698370622-SNUGGLE_BLOOMING_BOUQUET.png?v=1703053851",
			CompareAtPrice: models.ProductVariantPrice{
				Currency: "SGD",
				Amount:   "0",
			},
			Length: models.ProductVariantShippingSetting{
				Unit:  "123",
				Value: 123,
			},
			Width: models.ProductVariantShippingSetting{
				Unit:  "123",
				Value: 123,
			},
			Height: models.ProductVariantShippingSetting{
				Unit:  "123",
				Value: 123,
			},
			Weight: models.ProductVariantShippingSetting{
				Unit:  "123",
				Value: 123,
			},
			AllowBackorder: false,
			Options: []*models.ProductVariantOption{
				{
					Name:  "Color",
					Value: "Black",
				},
				{
					Name:  "Size",
					Value: "XXL",
				},
			},
			FulfillmentService: "manual",
			RequiresShipping:   false,
			Position:           3,
		},
		{
			ID:                "v_4",
			InventoryQuantity: 123,
			Sku:               "1011",
			Barcode: models.ProductVariantBarcode{
				Value: "0195751059831",
				Type:  "ean",
			},
			Title: "te12",
			Price: models.ProductVariantPrice{
				Currency: "SGD",
				Amount:   "19.6",
			},
			Cost: models.ProductVariantPrice{
				Currency: "SGD",
				Amount:   "19.6",
			},
			ImageURL: "https://cdn.shopify.com/s/files/1/0629/9697/1763/products/sssgid.zone-1703053820-sssgid.zone-1698370622-SNUGGLE_BLOOMING_BOUQUET.png?v=1703053851",
			CompareAtPrice: models.ProductVariantPrice{
				Currency: "SGD",
				Amount:   "0",
			},
			Length: models.ProductVariantShippingSetting{
				Unit:  "123",
				Value: 123,
			},
			Width: models.ProductVariantShippingSetting{
				Unit:  "123",
				Value: 123,
			},
			Height: models.ProductVariantShippingSetting{
				Unit:  "123",
				Value: 123,
			},
			Weight: models.ProductVariantShippingSetting{
				Unit:  "123",
				Value: 123,
			},
			AllowBackorder: false,
			Options: []*models.ProductVariantOption{
				{
					Name:  "Color",
					Value: "Black",
				},
				{
					Name:  "Size",
					Value: "XXXL",
				},
			},
			FulfillmentService: "manual",
			RequiresShipping:   false,
			Position:           4,
		},
		{
			ID:                "v_5",
			InventoryQuantity: 123,
			Sku:               "1012",
			Barcode: models.ProductVariantBarcode{
				Value: "0195751059831",
				Type:  "ean",
			},
			Title: "te12",
			Price: models.ProductVariantPrice{
				Currency: "SGD",
				Amount:   "19.6",
			},
			Cost: models.ProductVariantPrice{
				Currency: "SGD",
				Amount:   "19.6",
			},
			ImageURL: "https://cdn.shopify.com/s/files/1/0629/9697/1763/products/sssgid.zone-1703053820-sssgid.zone-1698370622-SNUGGLE_BLOOMING_BOUQUET.png?v=1703053851",
			CompareAtPrice: models.ProductVariantPrice{
				Currency: "SGD",
				Amount:   "0",
			},
			Length: models.ProductVariantShippingSetting{
				Unit:  "123",
				Value: 123,
			},
			Width: models.ProductVariantShippingSetting{
				Unit:  "123",
				Value: 123,
			},
			Height: models.ProductVariantShippingSetting{
				Unit:  "123",
				Value: 123,
			},
			Weight: models.ProductVariantShippingSetting{
				Unit:  "123",
				Value: 123,
			},
			AllowBackorder: false,
			Options: []*models.ProductVariantOption{
				{
					Name:  "Color",
					Value: "Black",
				},
				{
					Name:  "Size",
					Value: "XXXL",
				},
			},
			FulfillmentService: "manual",
			RequiresShipping:   false,
			Position:           5,
		},
	}
	product.Options = []*models.ProductOption{
		{
			Name:     "Color",
			Position: 1,
			Values: []string{
				"Red",
				"Blue",
			},
		},
		{
			Name:     "Size",
			Position: 2,
			Values: []string{
				"XL",
				"L",
			},
		},
	}
	product.Media = []*models.ProductMedia{
		{
			Type:              "image",
			Position:          1,
			URL:               "https://cdn.shopify.com/s/files/1/0629/9697/1763/products/sssgid.zone-1703053820-sssgid.zone-1698370622-SNUGGLE_BLOOMING_BOUQUET.png?v=1703053851",
			MimeType:          "123",
			ExternalVideoHost: "123",
			Thumbnail: models.ProductMediaThumbnail{
				URL: "https://cdn.shopify.com/s/files/1/0629/9697/1763/products/sssgid.zone-1703053820-sssgid.zone-1698370622-SNUGGLE_BLOOMING_BOUQUET.png?v=1703053851",
			},
		},
	}
	return product
}

func generateProductListingSettings() SyncSettings {
	inventorySyncSetting := InventorySyncSetting{
		Preference:   "store",
		LastEffectAt: time.Now().UTC(),
	}
	inventorySyncSetting.Customized = models.InventorySync{
		AutoSync:                 "enabled",
		AvailableQuantityPercent: 1,
	}
	inventorySyncSetting.Customized.LowQuantityThreshold = models.LowQuantityThreshold{
		State: "disabled",
		Value: 0.1,
	}
	inventorySyncSetting.Customized.ActiveWarehouses = []models.ActiveWarehouse{
		{
			State:             "disabled",
			SourceWarehouseId: "36696588354",
		},
		{
			State:             "disabled",
			SourceWarehouseId: "36891590722",
		},
	}
	priceSyncSetting := PriceSyncSetting{
		Preference:   "customized",
		LastEffectAt: time.Now().UTC(),
	}
	priceSyncSetting.Customized = models.PriceSync{
		SourceField: "sale_price",
		AutoSync:    "enabled",
	}
	priceSyncSetting.Customized.Rules = []models.PriceRules{
		{
			ValueType: "percentage",
			Value:     "0",
		},
	}
	productSyncSetting := ProductSyncSetting{
		Preference:   "customized",
		LastEffectAt: time.Now().UTC(),
	}
	productSyncSetting.Customized = models.ProductSync{
		UpdateDetail: models.UpdateDetail{
			Fields:   []string{"title"},
			AutoSync: "enabled",
		},
		UpdateVariants: models.UpdateVariants{
			AutoSync: "enabled",
		},
		LastEffectAt: time.Now().UTC(),
	}
	return SyncSettings{
		InventorySyncSetting: inventorySyncSetting,
		PriceSyncSetting:     priceSyncSetting,
		ProductSyncSetting:   productSyncSetting,
	}
}

func compareProductListing(t *testing.T, expected *ProductListingArgs, actual *ProductListing) {
	require.Equal(t, expected.SalesChannel.StoreKey, actual.SalesChannel.StoreKey)
	require.Equal(t, expected.SalesChannel.Platform, actual.SalesChannel.Platform)
	require.Equal(t, expected.Organization.ID, actual.Organization.ID)
	require.Equal(t, expected.SalesChannelProduct.ID, actual.SalesChannelProduct.ID)
	require.Equal(t, expected.SalesChannelProduct.ConnectorProductID, actual.SalesChannelProduct.ConnectorProductID)
	require.Equal(t, expected.SalesChannelProduct.State, actual.SalesChannelProduct.State)
	require.Equal(t, expected.ProductsCenterProduct.ID, actual.ProductsCenterProduct.ID)
	require.Equal(t, expected.ProductsCenterProduct.ConnectorProductID, actual.ProductsCenterProduct.ConnectorProductID)
	require.Equal(t, expected.ProductsCenterProduct.PublishState, actual.ProductsCenterProduct.PublishState)
	require.Equal(t, expected.ProductsCenterProduct.Source.StoreKey, actual.ProductsCenterProduct.Source.StoreKey)
	require.Equal(t, expected.ProductsCenterProduct.Source.Platform, actual.ProductsCenterProduct.Source.Platform)
	require.Equal(t, expected.ProductsCenterProduct.Source.ID, actual.ProductsCenterProduct.Source.ID)
	require.Equal(t, expected.Settings.InventorySyncSetting.Preference, actual.Settings.InventorySyncSetting.Preference)
	require.Equal(t, expected.Settings.InventorySyncSetting.Customized.AutoSync, actual.Settings.InventorySyncSetting.Customized.AutoSync)
	require.Equal(t, expected.Settings.InventorySyncSetting.Customized.AvailableQuantityPercent, actual.Settings.InventorySyncSetting.Customized.AvailableQuantityPercent)
	require.Equal(t, expected.Settings.InventorySyncSetting.Customized.ActiveWarehouses[0].State, actual.Settings.InventorySyncSetting.Customized.ActiveWarehouses[0].State)
	require.Equal(t, expected.Settings.InventorySyncSetting.Customized.ActiveWarehouses[0].SourceWarehouseId, actual.Settings.InventorySyncSetting.Customized.ActiveWarehouses[0].SourceWarehouseId)
	require.Equal(t, expected.Settings.InventorySyncSetting.LastEffectAt, actual.Settings.InventorySyncSetting.LastEffectAt)
	require.Equal(t, expected.Settings.PriceSyncSetting.Preference, actual.Settings.PriceSyncSetting.Preference)
	require.Equal(t, expected.Settings.PriceSyncSetting.Customized.SourceField, actual.Settings.PriceSyncSetting.Customized.SourceField)
	require.Equal(t, expected.Settings.PriceSyncSetting.Customized.AutoSync, actual.Settings.PriceSyncSetting.Customized.AutoSync)
	require.Equal(t, expected.Settings.PriceSyncSetting.LastEffectAt, actual.Settings.PriceSyncSetting.LastEffectAt)
	require.Equal(t, expected.Settings.ProductSyncSetting.Preference, actual.Settings.ProductSyncSetting.Preference)
	require.Equal(t, expected.Settings.ProductSyncSetting.Customized.UpdateDetail.Fields, actual.Settings.ProductSyncSetting.Customized.UpdateDetail.Fields)
	require.Equal(t, expected.Settings.ProductSyncSetting.Customized.UpdateDetail.AutoSync, actual.Settings.ProductSyncSetting.Customized.UpdateDetail.AutoSync)
	require.Equal(t, expected.Settings.ProductSyncSetting.Customized.UpdateVariants.AutoSync, actual.Settings.ProductSyncSetting.Customized.UpdateVariants.AutoSync)
	require.Equal(t, expected.Settings.ProductSyncSetting.LastEffectAt, actual.Settings.ProductSyncSetting.LastEffectAt)
	require.Equal(t, expected.Product.Title, actual.Product.Title)
	require.Equal(t, expected.Product.ShortDescription, actual.Product.ShortDescription)
	require.Equal(t, expected.Product.Categories[0].SalesChannelID, actual.Product.Categories[0].SalesChannelID)
	require.Equal(t, expected.Product.Categories[0].Name, actual.Product.Categories[0].Name)
	require.Equal(t, expected.Product.Tags, actual.Product.Tags)
	require.Equal(t, expected.Product.Brand.SalesChannelID, actual.Product.Brand.SalesChannelID)
	require.Equal(t, expected.Product.Brand.Name, actual.Product.Brand.Name)
	require.Equal(t, expected.Product.Vendor, actual.Product.Vendor)
	require.Equal(t, expected.Product.Description, actual.Product.Description)
	require.Equal(t, expected.Product.SizeChart.Images[0].SalesChannelID, actual.Product.SizeChart.Images[0].SalesChannelID)
	require.Equal(t, expected.Product.SizeChart.Images[0].URL, actual.Product.SizeChart.Images[0].URL)
	require.Equal(t, expected.Product.Certifications[0].SalesChannelID, actual.Product.Certifications[0].SalesChannelID)
	require.Equal(t, expected.Product.Certifications[0].Files[0].SalesChannelID, actual.Product.Certifications[0].Files[0].SalesChannelID)
	require.Equal(t, expected.Product.Certifications[0].Files[0].URL, actual.Product.Certifications[0].Files[0].URL)
	require.Equal(t, expected.Product.Certifications[0].Images[0].SalesChannelID, actual.Product.Certifications[0].Images[0].SalesChannelID)
	require.Equal(t, expected.Product.Certifications[0].Images[0].URL, actual.Product.Certifications[0].Images[0].URL)
	require.Equal(t, expected.Product.Attributes[0].SalesChannelID, actual.Product.Attributes[0].SalesChannelID)
	require.Equal(t, expected.Product.Attributes[0].Name, actual.Product.Attributes[0].Name)
	require.Equal(t, expected.Product.Attributes[0].Values[0].SalesChannelID, actual.Product.Attributes[0].Values[0].SalesChannelID)
	require.Equal(t, expected.Product.Attributes[0].Values[0].Name, actual.Product.Attributes[0].Values[0].Name)
	require.Equal(t, expected.Product.Variants[0].InventoryQuantity, actual.Product.Variants[0].InventoryQuantity)
	require.Equal(t, expected.Product.Variants[0].Sku, actual.Product.Variants[0].Sku)
	require.Equal(t, expected.Product.Variants[0].Barcode.Value, actual.Product.Variants[0].Barcode.Value)
	require.Equal(t, expected.Product.Variants[0].Barcode.Type, actual.Product.Variants[0].Barcode.Type)
	require.Equal(t, expected.Product.Variants[0].Title, actual.Product.Variants[0].Title)
	require.Equal(t, expected.Product.Variants[0].Price.Currency, actual.Product.Variants[0].Price.Currency)
	require.Equal(t, expected.Product.Variants[0].Price.Amount, actual.Product.Variants[0].Price.Amount)
	require.Equal(t, expected.Product.Variants[0].Cost, actual.Product.Variants[0].Cost)
	require.Equal(t, expected.Product.Variants[0].ImageURL, actual.Product.Variants[0].ImageURL)
	require.Equal(t, expected.Product.Variants[0].CompareAtPrice.Currency, actual.Product.Variants[0].CompareAtPrice.Currency)
	require.Equal(t, expected.Product.Variants[0].CompareAtPrice.Amount, actual.Product.Variants[0].CompareAtPrice.Amount)
	require.Equal(t, expected.Product.Variants[0].Length.Unit, actual.Product.Variants[0].Length.Unit)
	require.Equal(t, expected.Product.Variants[0].Length.Value, actual.Product.Variants[0].Length.Value)
	require.Equal(t, expected.Product.Variants[0].Width.Unit, actual.Product.Variants[0].Width.Unit)
	require.Equal(t, expected.Product.Variants[0].Width.Value, actual.Product.Variants[0].Width.Value)
	require.Equal(t, expected.Product.Variants[0].Height.Unit, actual.Product.Variants[0].Height.Unit)
	require.Equal(t, expected.Product.Variants[0].Height.Value, actual.Product.Variants[0].Height.Value)
	require.Equal(t, expected.Product.Variants[0].Weight.Unit, actual.Product.Variants[0].Weight.Unit)
	require.Equal(t, expected.Product.Variants[0].Weight.Value, actual.Product.Variants[0].Weight.Value)
	require.Equal(t, expected.Product.Variants[0].AllowBackorder, actual.Product.Variants[0].AllowBackorder)
	require.Equal(t, expected.Product.Variants[0].Options[0].Name, actual.Product.Variants[0].Options[0].Name)
	require.Equal(t, expected.Product.Variants[0].Options[0].Value, actual.Product.Variants[0].Options[0].Value)
	require.Equal(t, expected.Product.Variants[0].Options[1].Name, actual.Product.Variants[0].Options[1].Name)
	require.Equal(t, expected.Product.Variants[0].Options[1].Value, actual.Product.Variants[0].Options[1].Value)
	require.Equal(t, expected.Product.Variants[0].FulfillmentService, actual.Product.Variants[0].FulfillmentService)
	require.Equal(t, expected.Product.Variants[0].RequiresShipping, actual.Product.Variants[0].RequiresShipping)
	require.Equal(t, expected.Product.Variants[0].Position, actual.Product.Variants[0].Position)
	require.Equal(t, expected.Product.Options[0].Name, actual.Product.Options[0].Name)
	require.Equal(t, expected.Product.Options[0].Position, actual.Product.Options[0].Position)
	require.Equal(t, expected.Product.Options[0].Values, actual.Product.Options[0].Values)
	require.Equal(t, expected.Product.Media[0].Type, actual.Product.Media[0].Type)
	require.Equal(t, expected.Product.Media[0].Position, actual.Product.Media[0].Position)
	require.Equal(t, expected.Product.Media[0].Thumbnail.URL, actual.Product.Media[0].Thumbnail.URL)
	require.Equal(t, expected.Product.Media[0].URL, actual.Product.Media[0].URL)
	require.Equal(t, expected.Product.Media[0].MimeType, actual.Product.Media[0].MimeType)
	require.Equal(t, expected.Product.Media[0].ExternalVideoHost, actual.Product.Media[0].ExternalVideoHost)
}

func Test_ProductListingServiceImpl_listByIDs(t *testing.T) {
	service := initService(t)
	require.NotNil(t, service)
	productListingArgs := make(map[string]*ProductListingArgs)
	searchIds := make([]string, 0)
	for i := 0; i < 10; i++ {
		args := generateProductListingArgs()
		pl, err := service.Create(context.Background(), &args)
		require.NoError(t, err)
		require.NotNil(t, pl)
		productListingArgs[pl.ID] = &args
		if i%2 == 0 {
			searchIds = append(searchIds, pl.ID)
		}
	}
	// test case 1 get list by IDs
	searchProductListings, err := service.listByIDs(context.Background(), searchIds)
	require.NoError(t, err)
	require.Equal(t, len(searchIds), len(searchProductListings))
	for _, pl := range searchProductListings {
		if _, ok := productListingArgs[pl.ID]; !ok {
			compareProductListing(t, productListingArgs[pl.ID], pl)
		}
	}

	// test case 2 get list by IDs with not found
	searchIds = append(searchIds, "not found")
	searchProductListings, err = service.listByIDs(context.Background(), searchIds)
	require.NoError(t, err)
	require.Equal(t, len(searchIds)-1, len(searchProductListings))
	for _, pl := range searchProductListings {
		if _, ok := productListingArgs[pl.ID]; !ok {
			compareProductListing(t, productListingArgs[pl.ID], pl)
		}
	}
}

func Test_ProductListingServiceImpl_GetAuditVersionList(t *testing.T) {
	service := initService(t)
	require.NotNil(t, service)
	createArgs := generateProductListingArgs()

	pl, err := service.Create(context.Background(), &createArgs)
	require.NoError(t, err)
	require.NotNil(t, pl)
	for i := 0; i < 10; i++ {
		model := auditVersionDBModel{
			ProductListingID: pl.ID,
			AuditData:        AuditData{ProductListing: pl},
		}
		err = service.repo.plAuditVersionRepo.create(context.Background(), &model)
		require.NoError(t, err)
	}
	// test case 1 get list by ProductListingID
	args := ListAuditVersionsArgs{
		ProductListingID: pl.ID,
		Page:             1,
		Limit:            1,
	}
	auditVersions, err := service.ListAuditVersions(context.Background(), &args)
	require.NoError(t, err)
	require.Equal(t, 1, len(auditVersions))
	compareProductListing(t, &createArgs, &auditVersions[0].ProductListing)

	// test case 1 get list by ProductListingID with invalid page and limit
	args = ListAuditVersionsArgs{
		ProductListingID: pl.ID,
		Page:             -1,
		Limit:            -1,
	}
	auditVersions, err = service.ListAuditVersions(context.Background(), &args)
	require.NoError(t, err)
	require.Equal(t, 0, len(auditVersions))

	// test case 3 get list by ProductListingID with page 2
	args = ListAuditVersionsArgs{
		ProductListingID: pl.ID,
		Page:             2,
		Limit:            1,
	}
	auditVersions, err = service.ListAuditVersions(context.Background(), &args)
	require.NoError(t, err)
	require.Equal(t, 1, len(auditVersions))
	compareProductListing(t, &createArgs, &auditVersions[0].ProductListing)

	// test case 4 get list by ProductListingID with limit 5
	args = ListAuditVersionsArgs{
		ProductListingID: pl.ID,
		Page:             1,
		Limit:            5,
	}
	auditVersions, err = service.ListAuditVersions(context.Background(), &args)
	require.NoError(t, err)
	require.Equal(t, 5, len(auditVersions))

	// test case 5 get list by ProductListingID with not found
	args = ListAuditVersionsArgs{
		ProductListingID: "not found",
		Page:             1,
		Limit:            5,
	}
	auditVersions, err = service.ListAuditVersions(context.Background(), &args)
	require.NoError(t, err)
	require.Equal(t, 0, len(auditVersions))
}

func Test_ProductListingServiceImpl_ListRelations(t *testing.T) {
	service := initService(t)
	require.NotNil(t, service)
	productListings := make([]*ProductListing, 0)
	for i := 0; i < 10; i++ {
		createArgs := generateProductListingArgs()
		pl, err := service.Create(context.Background(), &createArgs)
		require.NoError(t, err)
		require.NotNil(t, pl)
		productListings = append(productListings, &pl)
	}
	// test case 1 search by organization id
	args := ListRelationsArgs{
		OrganizationID: productListings[0].Organization.ID,
		Page:           1,
		Limit:          10,
	}
	relations, err := service.ListRelations(context.Background(), &args)
	require.NoError(t, err)
	require.Equal(t, 2, len(relations))
	require.Contains(t, []string{productListings[0].Relations[0].ProductListingVariantID, productListings[0].Relations[1].ProductListingVariantID}, relations[0].ProductListingVariantID)
	require.Contains(t, []string{productListings[0].Relations[0].ProductsCenterVariant.ID, productListings[0].Relations[1].ProductsCenterVariant.ID}, relations[0].ProductsCenterVariant.ID)
	require.Contains(t, []string{productListings[0].Relations[0].ProductsCenterVariant.ProductID, productListings[0].Relations[1].ProductsCenterVariant.ProductID}, relations[0].ProductsCenterVariant.ProductID)
	require.Contains(t, []string{productListings[0].Relations[0].SalesChannelVariant.ID, productListings[0].Relations[1].SalesChannelVariant.ID}, relations[0].SalesChannelVariant.ID)
	require.Contains(t, []string{productListings[0].Relations[0].SalesChannelVariant.ProductID, productListings[0].Relations[1].SalesChannelVariant.ProductID}, relations[0].SalesChannelVariant.ProductID)
	require.Contains(t, []string{productListings[0].Relations[0].SalesChannelVariant.ConnectorProductID, productListings[0].Relations[1].SalesChannelVariant.ConnectorProductID}, relations[0].SalesChannelVariant.ConnectorProductID)
	require.Contains(t, []string{productListings[0].Relations[0].SalesChannelVariant.Sku, productListings[0].Relations[1].SalesChannelVariant.Sku}, relations[0].SalesChannelVariant.Sku)
	require.Contains(t, []string{productListings[0].Relations[0].ProductsCenterVariant.ConnectorProductID, productListings[0].Relations[1].ProductsCenterVariant.ConnectorProductID}, relations[0].ProductsCenterVariant.ConnectorProductID)

	// test case 2 search by sales channel store key
	args = ListRelationsArgs{
		OrganizationID:       productListings[0].Organization.ID,
		SalesChannelStoreKey: productListings[1].SalesChannel.StoreKey,
		Page:                 1,
		Limit:                10,
	}
	relations, err = service.ListRelations(context.Background(), &args)
	require.NoError(t, err)
	require.Equal(t, 2, len(relations))

	// test case 3 search by sales channel store key with page 2
	args = ListRelationsArgs{
		OrganizationID:       productListings[0].Organization.ID,
		SalesChannelStoreKey: productListings[1].SalesChannel.StoreKey,
		Page:                 2,
		Limit:                1,
	}
	relations, err = service.ListRelations(context.Background(), &args)
	require.NoError(t, err)
	require.Equal(t, 1, len(relations))

	// test case 4 search by sales channel platform
	args = ListRelationsArgs{
		OrganizationID:       productListings[0].Organization.ID,
		SalesChannelPlatform: productListings[0].SalesChannel.Platform,
		Page:                 1,
		Limit:                10,
	}
	relations, err = service.ListRelations(context.Background(), &args)
	require.NoError(t, err)
	require.Equal(t, 2, len(relations))

	// test case 5 search by sales channel product id
	args = ListRelationsArgs{
		OrganizationID:        productListings[0].Organization.ID,
		SalesChannelProductID: productListings[0].SalesChannelProduct.ID,
		Page:                  1,
		Limit:                 2,
	}
	relations, err = service.ListRelations(context.Background(), &args)
	require.NoError(t, err)
	require.Equal(t, 2, len(relations))

	// test case 6 search by sales channel variant id
	args = ListRelationsArgs{
		OrganizationID:        productListings[0].Organization.ID,
		SalesChannelVariantID: productListings[0].Relations[0].SalesChannelVariant.ID,
		Page:                  1,
		Limit:                 10,
	}
	relations, err = service.ListRelations(context.Background(), &args)
	require.NoError(t, err)
	require.Equal(t, 1, len(relations))

	// test case 7 search by products center variant id
	args = ListRelationsArgs{
		OrganizationID:          productListings[0].Organization.ID,
		ProductsCenterVariantID: productListings[0].Relations[0].ProductsCenterVariant.ID,
		Page:                    1,
		Limit:                   10,
	}
	relations, err = service.ListRelations(context.Background(), &args)
	require.NoError(t, err)
	require.Equal(t, 1, len(relations))

	// test case 8 search by products center product id
	args = ListRelationsArgs{
		OrganizationID:          productListings[0].Organization.ID,
		ProductsCenterProductID: productListings[0].ProductsCenterProduct.ID,
		Page:                    1,
		Limit:                   10,
	}
	relations, err = service.ListRelations(context.Background(), &args)
	require.NoError(t, err)
	require.Equal(t, 2, len(relations))

	// test case 9 search without organization id
	args = ListRelationsArgs{
		Page:  1,
		Limit: 10,
	}
	relations, err = service.ListRelations(context.Background(), &args)
	require.Error(t, err)
	require.Equal(t, 0, len(relations))
}

func Test_ProductListingServiceImpl_Duplicate(t *testing.T) {
	service := initService(t)
	require.NotNil(t, service)
	createArgs := generateProductListingArgs()
	pl, err := service.Create(context.Background(), &createArgs)
	require.NoError(t, err)
	require.NotNil(t, pl)

	// test case 1 duplicate exist product_listing
	duplicateProductListing, err := service.Duplicate(context.Background(), pl.ID)
	require.NoError(t, err)
	require.NotNil(t, duplicateProductListing)
	require.Equal(t, consts.ProductListingProductStatePending, duplicateProductListing.State)
	require.Equal(t, consts.SyncStatusUnsync, duplicateProductListing.SyncStatus)
	// 测试用例中 ready字段为空，预期为一致，但是会重新执行 category check 流程
	// require.Equal(t, pl.Ready.Status, duplicateProductListing.Ready.Status)
	require.Equal(t, pl.LinkStatus, duplicateProductListing.LinkStatus)
	require.Equal(t, len(pl.Relations), len(duplicateProductListing.Relations))
	for _, v := range duplicateProductListing.Relations {
		require.Equal(t, consts.SyncStatusUnsync, v.SyncStatus)
	}

	// test case 2 duplicate not exist product_Listing
	duplicateProductListing, err = service.Duplicate(context.Background(), "not found")
	require.Error(t, err)
}

func Test_ProductListingServiceImpl_setTiktokState(t *testing.T) {
	tests := []struct {
		name string
		pl   *ProductListing
		want consts.ProductListingProductState
	}{
		{"SalesChannelProductStateLive",
			&ProductListing{SalesChannelProduct: SalesChannelProduct{State: consts.SalesChannelProductStateLive}},
			consts.ProductListingProductStateActive},
		{"SalesChannelProductStatePending",
			&ProductListing{SalesChannelProduct: SalesChannelProduct{State: consts.SalesChannelProductStatePending}},
			consts.ProductListingProductStateReviewing},
		{"SalesChannelProductStateSellerDeactivate",
			&ProductListing{SalesChannelProduct: SalesChannelProduct{State: consts.SalesChannelProductStateSellerDeactivate}},
			consts.ProductListingProductStateInactive},
		{"SalesChannelProductStatePlatformDeactivated",
			&ProductListing{SalesChannelProduct: SalesChannelProduct{State: consts.SalesChannelProductStatePlatformDeactivated}},
			consts.ProductListingProductStateInactive},
		{"SalesChannelProductStateFailed",
			&ProductListing{SalesChannelProduct: SalesChannelProduct{State: consts.SalesChannelProductStateFailed}},
			consts.ProductListingProductStateSuspended},
		{"SalesChannelProductStateFreeze",
			&ProductListing{SalesChannelProduct: SalesChannelProduct{State: consts.SalesChannelProductStateFreeze}},
			consts.ProductListingProductStateSuspended},
		{"SalesChannelProductStateDeleted",
			&ProductListing{SalesChannelProduct: SalesChannelProduct{State: consts.SalesChannelProductStateDeleted}},
			consts.ProductListingProductStateDeleted},
		{"default",
			&ProductListing{SalesChannelProduct: SalesChannelProduct{State: "default"}},
			consts.ProductListingProductStatePending},
		{"empty",
			&ProductListing{SalesChannelProduct: SalesChannelProduct{State: "empty"}},
			consts.ProductListingProductStatePending},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.pl.SetTiktokState()
			require.Equal(t, tt.pl.State, tt.want)
		})
	}
}

func TestSetSheInState(t *testing.T) {
	tests := []struct {
		name     string
		product  models.Product
		expected consts.ProductListingProductState
	}{
		{
			name: "No options",
			product: models.Product{
				Options: []*models.ProductOption{},
			},
			expected: consts.ProductListingProductStatePending,
		},
		{
			name: "No value details",
			product: models.Product{
				Options: []*models.ProductOption{
					{
						ValueDetails: []models.ProductOptionValueDetail{},
					},
				},
			},
			expected: consts.ProductListingProductStatePending,
		},
		{
			name: "Single pending state",
			product: models.Product{
				Options: []*models.ProductOption{
					{
						ValueDetails: []models.ProductOptionValueDetail{
							{State: consts.ProductOptionValueStatePending},
						},
					},
				},
			},
			expected: consts.ProductListingProductStatePending,
		},
		{
			name: "multi pending state",
			product: models.Product{
				Options: []*models.ProductOption{
					{
						ValueDetails: []models.ProductOptionValueDetail{
							{State: consts.ProductOptionValueStatePending},
							{State: consts.ProductOptionValueStatePending},
						},
					},
				},
			},
			expected: consts.ProductListingProductStatePending,
		},
		{
			name: "Single active state",
			product: models.Product{
				Options: []*models.ProductOption{
					{
						ValueDetails: []models.ProductOptionValueDetail{
							{State: consts.ProductOptionValueStateActive},
						},
					},
				},
			},
			expected: consts.ProductListingProductStateActive,
		},
		{
			name: "multi active state",
			product: models.Product{
				Options: []*models.ProductOption{
					{
						ValueDetails: []models.ProductOptionValueDetail{
							{State: consts.ProductOptionValueStateActive},
							{State: consts.ProductOptionValueStateActive},
						},
					},
				},
			},
			expected: consts.ProductListingProductStateActive,
		},
		{
			name: "Single inactive state",
			product: models.Product{
				Options: []*models.ProductOption{
					{
						ValueDetails: []models.ProductOptionValueDetail{
							{State: consts.ProductOptionValueStateInactive},
						},
					},
				},
			},
			expected: consts.ProductListingProductStateInactive,
		},
		{
			name: "multi inactive state",
			product: models.Product{
				Options: []*models.ProductOption{
					{
						ValueDetails: []models.ProductOptionValueDetail{
							{State: consts.ProductOptionValueStateInactive},
							{State: consts.ProductOptionValueStateInactive},
						},
					},
				},
			},
			expected: consts.ProductListingProductStateInactive,
		},
		{
			name: "Single suspend state",
			product: models.Product{
				Options: []*models.ProductOption{
					{
						ValueDetails: []models.ProductOptionValueDetail{
							{State: consts.ProductOptionValueStateSuspend},
						},
					},
				},
			},
			expected: consts.ProductListingProductStateSuspended,
		},
		{
			name: "multi suspend state",
			product: models.Product{
				Options: []*models.ProductOption{
					{
						ValueDetails: []models.ProductOptionValueDetail{
							{State: consts.ProductOptionValueStateSuspend},
							{State: consts.ProductOptionValueStateSuspend},
						},
					},
				},
			},
			expected: consts.ProductListingProductStateSuspended,
		},
		{
			name: "Single reviewing state",
			product: models.Product{
				Options: []*models.ProductOption{
					{
						ValueDetails: []models.ProductOptionValueDetail{
							{State: consts.ProductOptionValueStateReviewing},
						},
					},
				},
			},
			expected: consts.ProductListingProductStateReviewing,
		},
		{
			name: "multi reviewing state",
			product: models.Product{
				Options: []*models.ProductOption{
					{
						ValueDetails: []models.ProductOptionValueDetail{
							{State: consts.ProductOptionValueStateReviewing},
							{State: consts.ProductOptionValueStateReviewing},
						},
					},
				},
			},
			expected: consts.ProductListingProductStateReviewing,
		},
		{
			name: "Mixed states",
			product: models.Product{
				Options: []*models.ProductOption{
					{
						ValueDetails: []models.ProductOptionValueDetail{
							{State: consts.ProductOptionValueStateActive},
							{State: consts.ProductOptionValueStateInactive},
						},
					},
				},
			},
			expected: consts.ProductListingProductStatePartiallyActive,
		},
		{
			name: "Mixed states",
			product: models.Product{
				Options: []*models.ProductOption{
					{
						ValueDetails: []models.ProductOptionValueDetail{
							{State: consts.ProductOptionValueStateActive},
							{State: consts.ProductOptionValueStateInactive},
							{State: consts.ProductOptionValueStateReviewing},
							{State: consts.ProductOptionValueStateSuspend},
						},
					},
				},
			},
			expected: consts.ProductListingProductStatePartiallyActive,
		},
		{
			name: "Mixed states for reviewing",
			product: models.Product{
				Options: []*models.ProductOption{
					{
						ValueDetails: []models.ProductOptionValueDetail{
							{State: consts.ProductOptionValueStateReviewing},
							{State: consts.ProductOptionValueStateSuspend},
						},
					},
				},
			},
			expected: consts.ProductListingProductStatePartiallyReviewing,
		},
		{
			name: "Mixed states for suspend",
			product: models.Product{
				Options: []*models.ProductOption{
					{
						ValueDetails: []models.ProductOptionValueDetail{
							{State: consts.ProductOptionValueStateSuspend},
							{State: consts.ProductOptionValueStateInactive},
						},
					},
				},
			},
			expected: consts.ProductListingProductStatePartiallySuspend,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pl := &ProductListing{
				Product: tt.product,
			}
			pl.SetSheinState()
			require.Equal(t, tt.expected, pl.State)
		})
	}
}

func Test_ProductListingServiceImpl_setSyncStatus(t *testing.T) {
	tests := []struct {
		name string
		pl   *ProductListing
		want consts.SyncStatus
	}{
		{
			"SyncStatusPartialSynced",
			&ProductListing{
				Relations: []*ProductListingRelation{
					{SyncStatus: consts.SyncStatusUnsync},
					{SyncStatus: consts.SyncStatusSynced},
				},
			},
			consts.SyncStatusPartialSynced,
		},
		{
			"SyncStatusUnsync",
			&ProductListing{
				Relations: []*ProductListingRelation{
					{SyncStatus: consts.SyncStatusUnsync},
					{SyncStatus: consts.SyncStatusUnsync},
				},
			},
			consts.SyncStatusUnsync,
		},
		{
			"SyncStatusSynced",
			&ProductListing{
				Relations: []*ProductListingRelation{
					{SyncStatus: consts.SyncStatusSynced},
					{SyncStatus: consts.SyncStatusSynced},
				},
			},
			consts.SyncStatusSynced,
		},
		{
			"SyncStatusUnsync",
			&ProductListing{},
			consts.SyncStatusUnsync,
		},
		{
			"SyncStatusSynced",
			&ProductListing{
				Relations: []*ProductListingRelation{
					{SyncStatus: consts.SyncStatusSynced},
				},
			},
			consts.SyncStatusSynced,
		},
		{
			"SyncStatusUnsync",
			&ProductListing{
				Relations: []*ProductListingRelation{
					{SyncStatus: consts.SyncStatusUnsync},
				},
			},
			consts.SyncStatusUnsync,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.pl.SetSyncStatus()
			require.Equal(t, tt.pl.SyncStatus, tt.want)
		})
	}
}

func Test_ProductListingServiceImpl_setLinkStatus(t *testing.T) {
	tests := []struct {
		name string
		pl   *ProductListing
		want consts.LinkStatus
	}{
		{
			"LinkStatusPartialSynced",
			&ProductListing{
				Relations: []*ProductListingRelation{
					{LinkStatus: consts.LinkStatusLinked},
					{LinkStatus: consts.LinkStatusUnlink},
				},
			},
			consts.LinkStatusPartialLinked,
		},
		{
			"LinkStatusUnlink",
			&ProductListing{
				Relations: []*ProductListingRelation{
					{LinkStatus: consts.LinkStatusUnlink},
					{LinkStatus: consts.LinkStatusUnlink},
				},
			},
			consts.LinkStatusUnlink,
		},
		{
			"LinkStatusLinked",
			&ProductListing{
				Relations: []*ProductListingRelation{
					{LinkStatus: consts.LinkStatusLinked},
					{LinkStatus: consts.LinkStatusLinked},
				},
			},
			consts.LinkStatusLinked,
		},
		{
			"LinkStatusUnlink",
			&ProductListing{},
			consts.LinkStatusUnlink,
		},
		{
			"LinkStatusLinked",
			&ProductListing{
				Relations: []*ProductListingRelation{
					{LinkStatus: consts.LinkStatusLinked},
				},
			},
			consts.LinkStatusLinked,
		},
		{
			"LinkStatusUnlink",
			&ProductListing{
				Relations: []*ProductListingRelation{
					{LinkStatus: consts.LinkStatusUnlink},
				},
			},
			consts.LinkStatusUnlink,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.pl.SetLinkStatus()
			require.Equal(t, tt.pl.LinkStatus, tt.want)
		})
	}
}

func Test_ProductListingServiceImpl_setRelationSyncStatus(t *testing.T) {
	tests := []struct {
		name                   string
		productListingRelation *ProductListingRelation
		lastSyncStatus         consts.SyncStatus
		wantSyncStatus         consts.SyncStatus
		wantLastSyncedAtIsNull bool
	}{
		{
			"unsynced to synced",
			&ProductListingRelation{
				SalesChannelVariant: SalesChannelVariant{ID: "123", ConnectorProductID: "123"},
			},
			consts.SyncStatusUnsync,
			consts.SyncStatusSynced,
			false,
		},
		{
			"synced to synced",
			&ProductListingRelation{
				SalesChannelVariant: SalesChannelVariant{ID: "123", ConnectorProductID: "123"},
				LastSyncedAt:        types.MakeDatetime(time.Now()),
			},
			consts.SyncStatusSynced,
			consts.SyncStatusSynced,
			false,
		},
		{
			"synced to unsynced",
			&ProductListingRelation{},
			consts.SyncStatusSynced,
			consts.SyncStatusUnsync,
			true,
		},
		{
			"unsynced to unsynced",
			&ProductListingRelation{
				LastSyncedAt: types.NullDatetime,
			},
			consts.SyncStatusUnsync,
			consts.SyncStatusUnsync,
			true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.productListingRelation.SetRelationSyncStatus()
			require.Equal(t, tt.wantSyncStatus, tt.productListingRelation.SyncStatus)
		})
	}
}

func Test_ProductListingRelation_setRelationLinkStatus(t *testing.T) {
	tests := []struct {
		name                   string
		productListingRelation *ProductListingRelation
		lastLinkStatus         consts.LinkStatus
		wantLinkStatus         consts.LinkStatus
		wantLastLinkedAtIsNull bool
	}{
		{
			"unlink to linked",
			&ProductListingRelation{
				ProductsCenterVariant: ProductsCenterVariant{ID: "123"},
			},
			consts.LinkStatusUnlink,
			consts.LinkStatusLinked,
			false,
		},
		{
			"linked to linked",
			&ProductListingRelation{
				ProductsCenterVariant: ProductsCenterVariant{ID: "123"},
				LastLinkedAt:          types.MakeDatetime(time.Now()),
			},
			consts.LinkStatusLinked,
			consts.LinkStatusLinked,
			false,
		},
		{
			"linked to unlinked",
			&ProductListingRelation{},
			consts.LinkStatusLinked,
			consts.LinkStatusUnlink,
			true,
		},
		{
			"unlink to unlink",
			&ProductListingRelation{
				LastLinkedAt: types.NullDatetime,
			},
			consts.LinkStatusUnlink,
			consts.LinkStatusUnlink,
			true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.productListingRelation.SetRelationLinkStatus()
			require.Equal(t, tt.wantLinkStatus, tt.productListingRelation.LinkStatus)
		})
	}
}

func Test_ProductListingRelation_setVariantIDIfNeeded(t *testing.T) {
	tests := []struct {
		name           string
		productListing *ProductListing
	}{
		{
			"empty",
			&ProductListing{Product: models.Product{Variants: []*models.ProductVariant{}}},
		},
		{
			"not empty",
			&ProductListing{Product: models.Product{Variants: []*models.ProductVariant{{ID: "test"}}}},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.productListing.SetVariantIDIfNeeded()
			for _, variant := range tt.productListing.Product.Variants {
				require.NotEqual(t, variant.ID, "")
			}
		})
	}
}

func TestCanUpdate(t *testing.T) {
	tests := []struct {
		name     string
		listing  *ProductListing
		expected bool
	}{
		{
			name: "can update when state is not running",
			listing: &ProductListing{
				Publish: Publish{
					State: consts.PublishStatePending,
				},
			},
			expected: true,
		},
		{
			name: "cannot update when state is running",
			listing: &ProductListing{
				Publish: Publish{
					State: consts.PublishStateRunning,
				},
			},
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tt.listing.canUpdate()
			require.Equal(t, tt.expected, result)
		})
	}
}

func Test_serviceImpl_GeneratePreview(t *testing.T) {
	mockClient := products_center.NewMockProductAPICollection(t)
	mockSettingService := new(settings.MockSettingService)

	service := &serviceImpl{
		logger: logger.Get(),
		productsCenterClient: &products_center.Client{
			Product: mockClient,
		},
		settingService: mockSettingService,
		validate:       validator.New(),
	}

	type args struct {
		ctx context.Context
		pl  *ProductListing
	}
	tests := []struct {
		name  string
		args  args
		mock  func()
		check func(pl *ProductListing, err error)
	}{
		// TODO: Add test cases.
		{
			name: "Case1 product not match",
			args: args{
				ctx: context.Background(),
				pl:  &ProductListing{},
			},
			mock: func() {},
			check: func(pl *ProductListing, err error) {
				require.NoError(t, err)
				require.Equal(t, &ProductListing{}, pl)
			},
		},

		{
			name: "Case 2 products center product not found",
			args: args{
				ctx: context.Background(),
				pl: &ProductListing{
					ProductsCenterProduct: ProductsCenterProduct{
						ID: "9b1442894e2246f997206b17cf703de4",
					},
				},
			},
			mock: func() {
				mockClient.On("GetByID", mock.Anything, mock.Anything).Return(&products_center.Product{}, errors.New("not found")).Once()
			},
			check: func(pl *ProductListing, err error) {
				require.Error(t, err)
			},
		},

		{
			name: "Case 3 get store setting error",
			args: args{
				ctx: context.Background(),
				pl: &ProductListing{
					ProductsCenterProduct: ProductsCenterProduct{
						ID: "9b1442894e2246f997206b17cf703de4",
					},
				},
			},
			mock: func() {
				mockClient.On("GetByID", mock.Anything, mock.Anything).Return(&products_center.Product{}, nil).Once()
				mockSettingService.On("List", mock.Anything, mock.Anything).Return([]*settings.Setting{}, errors.New("not found")).Once()
			},
			check: func(pl *ProductListing, err error) {
				require.Error(t, err)
			},
		},

		{
			name: "Case 4 store setting not found",
			args: args{
				ctx: context.Background(),
				pl: &ProductListing{
					ProductsCenterProduct: ProductsCenterProduct{
						ID: "9b1442894e2246f997206b17cf703de4",
					},
				},
			},
			mock: func() {
				mockClient.On("GetByID", mock.Anything, mock.Anything).Return(&products_center.Product{}, nil).Once()
				mockSettingService.On("List", mock.Anything, mock.Anything).Return([]*settings.Setting{}, nil).Once()
			},
			check: func(pl *ProductListing, err error) {
				require.ErrorIs(t, err, ErrSettingNotFound)
			},
		},
		{
			name: "Case 5 product_listing is nil in args",
			args: args{
				ctx: context.Background(),
				pl:  nil,
			},
			mock: func() {},
			check: func(pl *ProductListing, err error) {
				require.Error(t, err)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.mock()
			got, err := service.GeneratePreview(tt.args.ctx, tt.args.pl)
			tt.check(got, err)
		})
	}
}

func Test_serviceImpl_AutoLink_1(t *testing.T) {
	mockClient := products_center.NewMockProductAPICollection(t)
	mockSettingService := new(settings.MockSettingService)
	mockConnectorService := new(connectors.MockService)
	searchableProductService := new(searchable_products.MockService)
	mockSupportFeature := new(feed.MockSupportFeature)

	mockSupportFeature.On("GetSupportFeatures", mock.Anything, mock.Anything).
		Return([]support_features.GetInternalSupportFeaturesDataSupportFeatures{
			{
				Name:   types.MakeString("auto_link_v2"),
				Status: types.MakeString("disabled"),
			},
		}, nil).Once()
	mockClient.On("List", mock.Anything, mock.Anything).
		Return([]*products_center.Product{
			{
				ID: "4847dcb098264b5b97d5f46d9670c400",
				Variants: []products_center.Variant{
					{
						ID:  "4847dcb098264b5b97d5f46d9670c488",
						Sku: "sku_1",
					},
					{
						ID: "4847dcb098264b5b97d5f46d9670c433",
					},
				},
			},
		}, nil)
	mockSettingService.On("List", mock.Anything, mock.Anything).
		Return([]*settings.Setting{{AutoLink: models.AutoLink{State: consts.StateEnabled}}}, nil)
	mockConnectorService.On("GetBothConnections", mock.Anything, mock.Anything).
		Return(connectors.BothConnections{
			OrganizationID: "organization_id",
			App: models.App{
				Platform: "shopify",
				Key:      "s_1",
			},
			Channels: []models.App{
				{
					Platform: "tiktok-shop",
					Key:      "store_key",
				},
			},
		}, nil).Once()
	searchableProductService.On("Search", mock.Anything, mock.Anything).
		Return([]*searchable_products.SearchableProduct{
			{
				ID:               "id",
				ProductsCenterID: "4847dcb098264b5b97d5f46d9670c400",
				Variants: []searchable_products.Variant{
					{
						ID:                      "4847dcb098264b5b97d5f46d9670c477",
						ProductsCenterVariantID: "4847dcb098264b5b97d5f46d9670c488",
						SourceVariantID:         "45472437993799",
						Sku:                     "1008",
					},
					{
						ID:                      "4847dcb098264b5b97d5f46d9670c422",
						ProductsCenterVariantID: "4847dcb098264b5b97d5f46d9670c433",
						SourceVariantID:         "45472437993744",
						Sku:                     "1009",
					},
				},
			},
		}, models.Pagination{HasNextPage: false}, nil)
	taskMock := new(task.MockTaskService)
	taskMock.On("Create", mock.Anything, mock.Anything).Return(models.Task{}, nil)

	createArg := generateAutoLinkProductListingArgs()
	// 没有开启 variant 自动对齐, 可以进行 link
	createArg.Settings.ProductSyncSetting.Customized.UpdateVariants.AutoSync = "disabled"
	service := initService(t)
	require.NotNil(t, service)
	pl, err := service.Create(context.Background(), &createArg)
	require.NoError(t, err)
	require.NotNil(t, pl)
	service.logger = logger.Get()
	service.productsCenterClient = &products_center.Client{
		Product: mockClient,
	}
	service.settingService = mockSettingService
	service.connectorService = mockConnectorService
	service.searchableProductService = searchableProductService
	service.taskService = taskMock
	service.feedCliV2 = &feed.ClientV2{
		SupportFeature: mockSupportFeature,
	}

	result, err := service.AutoLink(context.Background(), &AutoLinkArg{ID: pl.ID})
	require.NoError(t, err)
	require.Equal(t, result.Relations[0].ProductsCenterVariant.ID, "4847dcb098264b5b97d5f46d9670c488")
	require.Equal(t, result.Relations[1].ProductsCenterVariant.ID, "4847dcb098264b5b97d5f46d9670c433")
}

func TestBuildLinkStatusFiltersQuery(t *testing.T) {
	tests := []struct {
		name              string
		linkStatusFilters []string
		expectedQueries   []elastic.Query
	}{
		{
			name:              "Empty Filters",
			linkStatusFilters: []string{},
			expectedQueries:   []elastic.Query{},
		},
		{
			name:              "Linked Status",
			linkStatusFilters: []string{consts.LinkStatusLinked.String()},
			expectedQueries:   []elastic.Query{elastic.NewTermQuery("link_status", consts.LinkStatusLinked)},
		},
		{
			name:              "Partial Linked Status",
			linkStatusFilters: []string{consts.LinkStatusPartialLinked.String()},
			expectedQueries:   []elastic.Query{elastic.NewTermQuery("link_status", consts.LinkStatusPartialLinked)},
		},
		{
			name:              "Unlink Status",
			linkStatusFilters: []string{consts.LinkStatusUnlink.String()},
			expectedQueries:   []elastic.Query{elastic.NewTermQuery("link_status", consts.LinkStatusUnlink)},
		},
		{
			name:              "Multiple Statuses",
			linkStatusFilters: []string{consts.LinkStatusLinked.String(), consts.LinkStatusUnlink.String()},
			expectedQueries: []elastic.Query{
				elastic.NewTermQuery("link_status", consts.LinkStatusLinked),
				elastic.NewTermQuery("link_status", consts.LinkStatusUnlink),
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			args := &SearchProductListingArgs{
				LinkStatusFilters: tt.linkStatusFilters,
			}
			queries := args.buildLinkStatusFiltersQuery()
			require.Equal(t, len(tt.expectedQueries), len(queries))
			for i, query := range queries {
				require.Equal(t, tt.expectedQueries[i], query)
			}
		})
	}
}

func TestBuildSyncStatusFiltersQuery(t *testing.T) {
	tests := []struct {
		name              string
		syncStatusFilters []string
		expectedQueries   []elastic.Query
	}{
		{
			name:              "Empty Filters",
			syncStatusFilters: []string{},
			expectedQueries:   []elastic.Query{},
		},
		{
			name:              "Sync Status",
			syncStatusFilters: []string{consts.SyncStatusSynced.String()},
			expectedQueries:   []elastic.Query{elastic.NewTermQuery("sync_status", consts.SyncStatusSynced)},
		},
		{
			name:              "Partial Sync Status",
			syncStatusFilters: []string{consts.SyncStatusPartialSynced.String()},
			expectedQueries:   []elastic.Query{elastic.NewTermQuery("sync_status", consts.SyncStatusPartialSynced)},
		},
		{
			name:              "Unsync Status",
			syncStatusFilters: []string{consts.SyncStatusUnsync.String()},
			expectedQueries:   []elastic.Query{elastic.NewTermQuery("sync_status", consts.SyncStatusUnsync)},
		},
		{
			name:              "Multiple Statuses",
			syncStatusFilters: []string{consts.SyncStatusSynced.String(), consts.SyncStatusUnsync.String()},
			expectedQueries: []elastic.Query{
				elastic.NewTermQuery("sync_status", consts.SyncStatusSynced),
				elastic.NewTermQuery("sync_status", consts.SyncStatusUnsync),
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			args := &SearchProductListingArgs{
				SyncStatusFilters: tt.syncStatusFilters,
			}
			queries := args.buildSyncStatusFiltersQuery()
			require.Equal(t, len(tt.expectedQueries), len(queries))
			for i, query := range queries {
				require.Equal(t, tt.expectedQueries[i], query)
			}
		})
	}
}

func Test_serviceImpl_AutoLink_2(t *testing.T) {
	mockClient := products_center.NewMockProductAPICollection(t)
	mockSettingService := new(settings.MockSettingService)
	mockConnectorService := new(connectors.MockService)
	searchableProductService := new(searchable_products.MockService)
	mockSupportFeature := new(feed.MockSupportFeature)

	mockSupportFeature.On("GetSupportFeatures", mock.Anything, mock.Anything).
		Return([]support_features.GetInternalSupportFeaturesDataSupportFeatures{
			{
				Name:   types.MakeString("auto_link_v2"),
				Status: types.MakeString("disabled"),
			},
		}, nil)
	mockConnectorService.On("GetProductByID", mock.Anything, mock.Anything, mock.Anything).
		Return(&products.ModelsResponseProduct{ExternalReferenceID: types.MakeString("")}, nil)
	mockClient.On("List", mock.Anything, mock.Anything).
		Return([]*products_center.Product{
			{
				ID: "4847dcb098264b5b97d5f46d9670c400",
				Variants: []products_center.Variant{
					{
						ID:  "4847dcb098264b5b97d5f46d9670c488",
						Sku: "sku_1",
					},
					{
						ID: "4847dcb098264b5b97d5f46d9670c433",
					},
				},
			},
		}, nil)
	mockSettingService.On("List", mock.Anything, mock.Anything).
		Return([]*settings.Setting{{AutoLink: models.AutoLink{State: consts.StateEnabled}}}, nil)
	mockConnectorService.On("GetBothConnections", mock.Anything, mock.Anything).
		Return(connectors.BothConnections{
			OrganizationID: "organization_id",
			App: models.App{
				Platform: "shopify",
				Key:      "s_1",
			},
			Channels: []models.App{
				{
					Platform: "tiktok-shop",
					Key:      "store_key",
				},
			},
		}, nil)
	searchableProductService.On("Search", mock.Anything, mock.Anything).
		Return([]*searchable_products.SearchableProduct{
			{
				ID:               "id",
				ProductsCenterID: "4847dcb098264b5b97d5f46d9670c400",
				Variants: []searchable_products.Variant{
					{
						ID:                      "4847dcb098264b5b97d5f46d9670c477",
						ProductsCenterVariantID: "4847dcb098264b5b97d5f46d9670c488",
						SourceVariantID:         "45472437993799",
						Sku:                     "1008",
					},
					{
						ID:                      "4847dcb098264b5b97d5f46d9670c422",
						ProductsCenterVariantID: "4847dcb098264b5b97d5f46d9670c433",
						SourceVariantID:         "45472437993744",
						Sku:                     "1009",
					},
				},
			},
		}, models.Pagination{HasNextPage: false}, nil)
	taskMock := new(task.MockTaskService)
	taskMock.On("Create", mock.Anything, mock.Anything).Return(models.Task{}, nil)

	service := initService(t)
	require.NotNil(t, service)

	// case1: can auto link: 没有开启 variant 自动对齐, 可以进行 link
	createArgC1 := generateAutoLinkProductListingArgs()
	createArgC1.Settings.ProductSyncSetting.Customized.UpdateVariants.AutoSync = "disabled"
	plC1, errC1 := service.Create(context.Background(), &createArgC1)
	require.NoError(t, errC1)
	require.NotNil(t, plC1)

	// case2: can auto link: 没有 match, 可以进行 link
	createArgC2 := generateAutoLinkProductListingArgs()
	createArgC2.ProductsCenterProduct.ID = ""
	plC2, errC1 := service.Create(context.Background(), &createArgC2)
	require.NoError(t, errC1)
	require.NotNil(t, plC2)

	// case2: can auto link: 没有 match && 没有开启 variant, 可以进行 link
	createArgC3 := generateAutoLinkProductListingArgs()
	createArgC3.ProductsCenterProduct.ID = ""
	createArgC1.Settings.ProductSyncSetting.Customized.UpdateVariants.AutoSync = "disabled"
	plC3, errC1 := service.Create(context.Background(), &createArgC3)
	require.NoError(t, errC1)
	require.NotNil(t, plC3)

	// case4: can't auto link: 有 match && 开启了 variant 对齐, 不可以进行 link
	createArgC4 := generateAutoLinkProductListingArgs()
	plC4, errC1 := service.Create(context.Background(), &createArgC4)
	require.NoError(t, errC1)
	require.NotNil(t, plC4)

	service.logger = logger.Get()
	service.productsCenterClient = &products_center.Client{
		Product: mockClient,
	}
	service.settingService = mockSettingService
	service.connectorService = mockConnectorService
	service.searchableProductService = searchableProductService
	service.taskService = taskMock
	service.feedCliV2 = &feed.ClientV2{
		SupportFeature: mockSupportFeature,
	}

	resultC1, errC1 := service.AutoLink(context.Background(), &AutoLinkArg{
		ID:               plC1.ID,
		TargetVariantIDs: []string{"001_id"},
	})
	require.NoError(t, errC1)
	require.Equal(t, resultC1.Relations[0].ProductsCenterVariant.ID, "4847dcb098264b5b97d5f46d9670c488")
	require.Equal(t, resultC1.Relations[1].ProductsCenterVariant.ID, "")

	resultC2, errC2 := service.AutoLink(context.Background(), &AutoLinkArg{
		ID:               plC2.ID,
		TargetVariantIDs: []string{"001_id"},
	})
	require.NoError(t, errC2)
	require.Equal(t, resultC2.Relations[0].ProductsCenterVariant.ID, "4847dcb098264b5b97d5f46d9670c488")
	require.Equal(t, resultC2.Relations[1].ProductsCenterVariant.ID, "")

	resultC3, errC3 := service.AutoLink(context.Background(), &AutoLinkArg{
		ID:               plC3.ID,
		TargetVariantIDs: []string{"001_id"},
	})
	require.NoError(t, errC3)
	require.Equal(t, resultC3.Relations[0].ProductsCenterVariant.ID, "4847dcb098264b5b97d5f46d9670c488")
	require.Equal(t, resultC3.Relations[1].ProductsCenterVariant.ID, "")

	_, errC4 := service.AutoLink(context.Background(), &AutoLinkArg{
		ID:               plC4.ID,
		TargetVariantIDs: []string{"001_id"},
	})
	require.ErrorIs(t, errC4, ErrProductListingNotAllowLink)
}

/**
[auto link 单元测试]

pre_check 检测:
1. 开启了 variant 对齐, 不可以进行 link
2. 未开启 auto_link, 不可以进行 link

未关联的 listings:
1. 有 title 相同的 pc product, 且能完全 match 上
	- sku match
	- option match
	- singer product match
2. 没有 title 相同的 pc product, sku 被全局 link 上了
3. 没有 title 相同的 pc product, 部分 SKU 都被 link 了, 剩余 SKU 被全局 link 上了, 但是有个 sku 因 same_sku_name 没有被 link

有关联的 listings:
1. [一对多]有 title 相同的 pc product, 部分 SKU 先被 title相等的 抢去 link 上, 其他 SKU 也会被 link 上了
2. [一对多]没有 title 相等的 pc product, 部分 SKU 被 link 上了, 另外部分 SKU 因 option 相等所以被 link 上了, 剩余部分 SKU 被全局 link 上了
*/

/*
pre_check 检测:
1. 开启了 variant 对齐, 不可以进行 link
2. 未开启 auto_link, 不可以进行 link
*/
func Test_serviceImpl_AutoLink_pre_check(t *testing.T) {
	mockClient := products_center.NewMockProductAPICollection(t)
	mockSettingService := new(settings.MockSettingService)
	mockConnectorService := new(connectors.MockService)
	searchableProductService := new(searchable_products.MockService)
	mockSupportFeature := new(feed.MockSupportFeature)

	mockSupportFeature.On("GetSupportFeatures", mock.Anything, mock.Anything).
		Return([]support_features.GetInternalSupportFeaturesDataSupportFeatures{
			{
				Name:   types.MakeString("auto_link_v2"),
				Status: types.MakeString("enabled"),
			},
		}, nil).Once()
	mockSettingService.On("List", mock.Anything, mock.Anything).
		Return([]*settings.Setting{{AutoLink: models.AutoLink{State: consts.StateEnabled}}}, nil)
	mockConnectorService.On("GetBothConnections", mock.Anything, mock.Anything).
		Return(connectors.BothConnections{
			OrganizationID: "organization_id",
			App: models.App{
				Platform: "shopify",
				Key:      "s_1",
			},
			Channels: []models.App{
				{
					Platform: "tiktok-shop",
					Key:      "store_key",
				},
			},
		}, nil)
	searchableProductService.On("Search", mock.Anything, mock.Anything).
		Return([]*searchable_products.SearchableProduct{
			{
				ID:               "id",
				ProductsCenterID: "4847dcb098264b5b97d5f46d9670c400",
				Variants: []searchable_products.Variant{
					{
						ID:                      "4847dcb098264b5b97d5f46d9670c477",
						ProductsCenterVariantID: "4847dcb098264b5b97d5f46d9670c488",
						SourceVariantID:         "45472437993799",
						Sku:                     "1008",
					},
					{
						ID:                      "4847dcb098264b5b97d5f46d9670c422",
						ProductsCenterVariantID: "4847dcb098264b5b97d5f46d9670c433",
						SourceVariantID:         "45472437993744",
						Sku:                     "1009",
					},
				},
			},
		}, models.Pagination{HasNextPage: false}, nil)
	taskMock := new(task.MockTaskService)
	taskMock.On("Create", mock.Anything, mock.Anything).Return(models.Task{}, nil)

	service := initService(t)
	require.NotNil(t, service)

	service.logger = logger.Get()
	service.productsCenterClient = &products_center.Client{
		Product: mockClient,
	}
	service.settingService = mockSettingService
	service.connectorService = mockConnectorService
	service.searchableProductService = searchableProductService
	service.taskService = taskMock
	service.feedCliV2 = &feed.ClientV2{
		SupportFeature: mockSupportFeature,
	}
	// case: can't auto link: 开启了 variant 对齐, 不可以进行 link
	createArgC4 := generateAutoLinkProductListingArgs()
	plC4, errC1 := service.Create(context.Background(), &createArgC4)
	require.NoError(t, errC1)
	require.NotNil(t, plC4)
	_, errC4 := service.AutoLink(context.Background(), &AutoLinkArg{
		ID:               plC4.ID,
		TargetVariantIDs: []string{"001_id"},
	})
	require.ErrorIs(t, errC4, ErrProductListingNotAllowLink)
}

/*
未关联的 listings: 有 title 相同的 pc product, 且能完全 match 上
  - sku match
*/
func Test_serviceImpl_AutoLink_all_sku_match(t *testing.T) {

	mockClient := products_center.NewMockProductAPICollection(t)
	mockSettingService := new(settings.MockSettingService)
	mockConnectorService := new(connectors.MockService)
	searchableProductService := new(searchable_products.MockService)
	mockSupportFeature := new(feed.MockSupportFeature)

	mockSupportFeature.On("GetSupportFeatures", mock.Anything, mock.Anything).
		Return([]support_features.GetInternalSupportFeaturesDataSupportFeatures{
			{
				Name:   types.MakeString("auto_link_v2"),
				Status: types.MakeString("enabled"),
			},
		}, nil).Once()
	mockClient.On("List", mock.Anything, mock.Anything).
		Return([]*products_center.Product{
			{
				ID:    "pc_id_1",
				Title: "auto-link-title-1",
				Variants: []products_center.Variant{
					{
						ID:  "pc_id_1_01",
						Sku: "1008",
					},
					{
						ID:  "pc_id_1_02",
						Sku: "1009",
					},
				},
			},
		}, nil)
	mockClient.On("GetByID", mock.Anything, "pc_id_1").
		Return(&products_center.Product{

			ID:    "pc_id_1",
			Title: "auto-link-title-1",
			Variants: []products_center.Variant{
				{
					ID:  "pc_id_1_01",
					Sku: "1008",
				},
				{
					ID:  "pc_id_1_02",
					Sku: "1009",
				},
			},
		}, nil).Once()
	mockClient.On("GetByID", mock.Anything, "pc_id_2").
		Return(&products_center.Product{
			ID:    "pc_id_2",
			Title: "auto-link-title-2",
			Variants: []products_center.Variant{
				{
					ID:  "pc_id_2_01",
					Sku: "1008_1",
				},
				{
					ID:  "pc_id_2_02",
					Sku: "1009",
				},
			},
		}, nil).Once()
	mockSettingService.On("List", mock.Anything, mock.Anything).
		Return([]*settings.Setting{{AutoLink: models.AutoLink{State: consts.StateEnabled}}}, nil)
	mockConnectorService.On("GetBothConnections", mock.Anything, mock.Anything).
		Return(connectors.BothConnections{
			OrganizationID: "organization_id",
			App: models.App{
				Platform: "shopify",
				Key:      "s_1",
			},
			Channels: []models.App{
				{
					Platform: "tiktok-shop",
					Key:      "store_key",
				},
			},
		}, nil).Once()
	searchableProductService.On("Search", mock.Anything, mock.Anything).
		// "pc_id_2", "pc_id_1"
		Return([]*searchable_products.SearchableProduct{
			{
				ID:               "pc_id_2",
				ProductsCenterID: "pc_id_2",
				Title:            "Snuggle Blooming Bouquet Fabric Conditioner 4L + Snuggle Bear Plushie",
			},
			{
				ID:               "pc_id_1",
				ProductsCenterID: "pc_id_1",
				Title:            "Snuggle Blooming Bouquet Fabric Conditioner 4L + Snuggle Bear Plushie",
			},
		}, models.Pagination{HasNextPage: false}, nil)
	taskMock := new(task.MockTaskService)
	taskMock.On("Create", mock.Anything, mock.Anything).Return(models.Task{}, nil)

	createArg := generateAutoLinkProductListingArgs_no_relation()
	// 没有开启 variant 自动对齐, 可以进行 link
	createArg.Settings.ProductSyncSetting.Customized.UpdateVariants.AutoSync = "disabled"
	service := initService(t)
	require.NotNil(t, service)
	pl, err := service.Create(context.Background(), &createArg)
	require.NoError(t, err)
	require.NotNil(t, pl)
	service.logger = logger.Get()
	service.productsCenterClient = &products_center.Client{
		Product: mockClient,
	}
	service.settingService = mockSettingService
	service.connectorService = mockConnectorService
	service.searchableProductService = searchableProductService
	service.taskService = taskMock
	service.feedCliV2 = &feed.ClientV2{
		SupportFeature: mockSupportFeature,
	}

	result, err := service.AutoLink(context.Background(), &AutoLinkArg{ID: pl.ID})
	require.NoError(t, err)
	require.Equal(t, result.Relations[0].ProductsCenterVariant.ProductID, "pc_id_1")
	require.Equal(t, result.Relations[0].ProductsCenterVariant.ID, "pc_id_1_01")
	require.Equal(t, result.Relations[1].ProductsCenterVariant.ProductID, "pc_id_1")
	require.Equal(t, result.Relations[1].ProductsCenterVariant.ID, "pc_id_1_02")

}

/*
未关联的 listings: 有 title 相同的 pc product, 但 title 不同
*/
func Test_serviceImpl_AutoLink_all_sku_match_but_product_title_no_eq(t *testing.T) {

	mockClient := products_center.NewMockProductAPICollection(t)
	mockSettingService := new(settings.MockSettingService)
	mockConnectorService := new(connectors.MockService)
	searchableProductService := new(searchable_products.MockService)
	mockSupportFeature := new(feed.MockSupportFeature)

	mockSupportFeature.On("GetSupportFeatures", mock.Anything, mock.Anything).
		Return([]support_features.GetInternalSupportFeaturesDataSupportFeatures{
			{
				Name:   types.MakeString("auto_link_v2"),
				Status: types.MakeString("enabled"),
			},
		}, nil).Once()
	mockClient.On("List", mock.Anything, mock.Anything).
		Return([]*products_center.Product{
			{
				ID:    "pc_id_1",
				Title: "auto-link-title-1",
				Variants: []products_center.Variant{
					{
						ID:  "pc_id_1_01",
						Sku: "1008",
					},
					{
						ID:  "pc_id_1_02",
						Sku: "1009",
					},
				},
			},
		}, nil).Maybe()
	mockClient.On("GetByID", mock.Anything, "pc_id_1").
		Return(&products_center.Product{

			ID:    "pc_id_1",
			Title: "auto-link-title-1",
			Variants: []products_center.Variant{
				{
					ID:  "pc_id_1_01",
					Sku: "1008",
				},
				{
					ID:  "pc_id_1_02",
					Sku: "1009",
				},
			},
		}, nil).Maybe()
	mockConnectorService.On("GetProductByID", mock.Anything, mock.Anything, mock.Anything).
		Return(&products.ModelsResponseProduct{ExternalReferenceID: types.MakeString("")}, nil)
	mockClient.On("GetByID", mock.Anything, "pc_id_2").
		Return(&products_center.Product{
			ID:    "pc_id_2",
			Title: "auto-link-title-2",
			Variants: []products_center.Variant{
				{
					ID:  "pc_id_2_01",
					Sku: "1008_1",
				},
				{
					ID:  "pc_id_2_02",
					Sku: "1009",
				},
			},
		}, nil).Maybe()
	mockSettingService.On("List", mock.Anything, mock.Anything).
		Return([]*settings.Setting{{AutoLink: models.AutoLink{State: consts.StateEnabled}}}, nil).Maybe()
	mockConnectorService.On("GetBothConnections", mock.Anything, mock.Anything).
		Return(connectors.BothConnections{
			OrganizationID: "organization_id",
			App: models.App{
				Platform: "shopify",
				Key:      "s_1",
			},
			Channels: []models.App{
				{
					Platform: "tiktok-shop",
					Key:      "store_key",
				},
			},
		}, nil).Once()
	searchableProductService.On("Search", mock.Anything, mock.Anything).
		// "pc_id_2", "pc_id_1"
		Return([]*searchable_products.SearchableProduct{
			{
				ID:               "pc_id_2",
				ProductsCenterID: "pc_id_2",
				Title:            "Snuggle Blooming Bouquet Fabric Conditioner 4L + Snuggle Bear Plushie - no -eq",
			},
			{
				ID:               "pc_id_1",
				ProductsCenterID: "pc_id_1",
				Title:            "Snuggle Blooming Bouquet Fabric Conditioner 4L + Snuggle Bear Plushie - no -eq",
			},
		}, models.Pagination{HasNextPage: false}, nil)
	taskMock := new(task.MockTaskService)
	taskMock.On("Create", mock.Anything, mock.Anything).Return(models.Task{}, nil)

	createArg := generateAutoLinkProductListingArgs_no_relation()
	// 没有开启 variant 自动对齐, 可以进行 link
	createArg.Settings.ProductSyncSetting.Customized.UpdateVariants.AutoSync = "disabled"
	service := initService(t)
	require.NotNil(t, service)
	pl, err := service.Create(context.Background(), &createArg)
	require.NoError(t, err)
	require.NotNil(t, pl)
	service.logger = logger.Get()
	service.productsCenterClient = &products_center.Client{
		Product: mockClient,
	}
	service.settingService = mockSettingService
	service.connectorService = mockConnectorService
	service.searchableProductService = searchableProductService
	service.taskService = taskMock
	service.feedCliV2 = &feed.ClientV2{
		SupportFeature: mockSupportFeature,
	}

	_, err = service.AutoLink(context.Background(), &AutoLinkArg{ID: pl.ID})
	require.ErrorIs(t, err, ErrNoValidVariantsToAutoLink)

}

/*
未关联的 listings: 有 title 相同的 pc product, 且能完全 match 上
  - options match
*/
func Test_serviceImpl_AutoLink_all_options_match(t *testing.T) {

	mockClient := products_center.NewMockProductAPICollection(t)
	mockSettingService := new(settings.MockSettingService)
	mockConnectorService := new(connectors.MockService)
	searchableProductService := new(searchable_products.MockService)
	mockSupportFeature := new(feed.MockSupportFeature)

	createArg := generateAutoLinkProductListingArgs_no_relation()

	mockSupportFeature.On("GetSupportFeatures", mock.Anything, mock.Anything).
		Return([]support_features.GetInternalSupportFeaturesDataSupportFeatures{
			{
				Name:   types.MakeString("auto_link_v2"),
				Status: types.MakeString("enabled"),
			},
		}, nil).Once()
	mockClient.On("List", mock.Anything, mock.Anything).
		Return([]*products_center.Product{
			{
				ID:    "pc_id_1",
				Title: "auto-link-title-1",
				Variants: []products_center.Variant{
					{
						ID: "pc_id_1_01",
					},
					{
						ID: "pc_id_1_02",
					},
				},
			},
		}, nil)
	mockClient.On("GetByID", mock.Anything, "pc_id_1").
		Return(&products_center.Product{
			ID:    "pc_id_1",
			Title: "auto-link-title-1",
			Variants: []products_center.Variant{
				{
					ID:  "pc_id_1_01",
					Sku: "1008_xx",
					Options: []products_center.VariantOption{
						{
							Name:  "color",
							Value: "black",
						},
						{
							Name:  "size",
							Value: "xl",
						},
					},
				},
				{
					ID:  "pc_id_1_02",
					Sku: "1009",
					Options: []products_center.VariantOption{
						{
							Name:  "color",
							Value: "black",
						},
						{
							Name:  "size",
							Value: "l",
						},
					},
				},
			},
		}, nil).Once()
	mockSettingService.On("List", mock.Anything, mock.Anything).
		Return([]*settings.Setting{{AutoLink: models.AutoLink{State: consts.StateEnabled}}}, nil)
	mockConnectorService.On("GetBothConnections", mock.Anything, mock.Anything).
		Return(connectors.BothConnections{
			OrganizationID: "organization_id",
			App: models.App{
				Platform: "shopify",
				Key:      "s_1",
			},
			Channels: []models.App{
				{
					Platform: "tiktok-shop",
					Key:      "store_key",
				},
			},
		}, nil).Once()
	searchableProductService.On("Search", mock.Anything, mock.Anything).
		Return([]*searchable_products.SearchableProduct{
			{
				ID:               "pc_id_2",
				ProductsCenterID: "pc_id_2",
				Variants: []searchable_products.Variant{
					{
						ProductsCenterVariantID: "pc_id_2_01",
						Sku:                     "1008",
					},
				},
			},
			{
				ID:               "pc_id_1",
				ProductsCenterID: "pc_id_1",
				Title:            createArg.Product.Title,
				Variants: []searchable_products.Variant{
					{
						ProductsCenterVariantID: "pc_id_1_01",
						Sku:                     "1009",
					},
				},
			},
		}, models.Pagination{HasNextPage: false}, nil)
	taskMock := new(task.MockTaskService)
	taskMock.On("Create", mock.Anything, mock.Anything).Return(models.Task{}, nil)

	// 没有开启 variant 自动对齐, 可以进行 link
	createArg.Settings.ProductSyncSetting.Customized.UpdateVariants.AutoSync = "disabled"
	service := initService(t)
	require.NotNil(t, service)
	pl, err := service.Create(context.Background(), &createArg)
	require.NoError(t, err)
	require.NotNil(t, pl)
	service.logger = logger.Get()
	service.productsCenterClient = &products_center.Client{
		Product: mockClient,
	}
	service.settingService = mockSettingService
	service.connectorService = mockConnectorService
	service.searchableProductService = searchableProductService
	service.taskService = taskMock
	service.feedCliV2 = &feed.ClientV2{
		SupportFeature: mockSupportFeature,
	}

	result, err := service.AutoLink(context.Background(), &AutoLinkArg{ID: pl.ID})
	require.NoError(t, err)
	require.Equal(t, result.Relations[0].ProductsCenterVariant.ProductID, "pc_id_1")
	require.Equal(t, result.Relations[0].ProductsCenterVariant.ID, "pc_id_1_01")
	require.Equal(t, result.Relations[1].ProductsCenterVariant.ProductID, "pc_id_1")
	require.Equal(t, result.Relations[1].ProductsCenterVariant.ID, "pc_id_1_02")

}

/*
未关联的 listings: 有 title 相同的 pc product, 且能完全 match 上
  - singer product match
*/
func Test_serviceImpl_AutoLink_singer_product_match(t *testing.T) {

	mockClient := products_center.NewMockProductAPICollection(t)
	mockSettingService := new(settings.MockSettingService)
	mockConnectorService := new(connectors.MockService)
	searchableProductService := new(searchable_products.MockService)
	mockSupportFeature := new(feed.MockSupportFeature)

	createArg := generateAutoLinkProductListingArgs_no_relation_singer_product()

	mockSupportFeature.On("GetSupportFeatures", mock.Anything, mock.Anything).
		Return([]support_features.GetInternalSupportFeaturesDataSupportFeatures{
			{
				Name:   types.MakeString("auto_link_v2"),
				Status: types.MakeString("enabled"),
			},
		}, nil).Once()
	mockClient.On("List", mock.Anything, mock.Anything).
		Return([]*products_center.Product{
			{
				ID:    "pc_id_1",
				Title: "auto-link-title-1",
				Variants: []products_center.Variant{
					{
						ID: "pc_id_1_01",
					},
					{
						ID: "pc_id_1_02",
					},
				},
			},
		}, nil)
	mockClient.On("GetByID", mock.Anything, "pc_id_1").
		Return(&products_center.Product{

			ID:    "pc_id_1",
			Title: "auto-link-title-1",
			Variants: []products_center.Variant{
				{
					ID:      "pc_id_1_01",
					Sku:     "1008_xx",
					Options: []products_center.VariantOption{},
				},
			},
		}, nil).Once()
	mockClient.On("GetByID", mock.Anything, "pc_id_2").
		Return(&products_center.Product{
			ID:    "pc_id_2",
			Title: "auto-link-title-2",
			Variants: []products_center.Variant{
				{
					ID:  "pc_id_2_01",
					Sku: "1008_1_op",
				},
				{
					ID:  "pc_id_2_02",
					Sku: "1009",
				},
			},
		}, nil).Once()
	mockSettingService.On("List", mock.Anything, mock.Anything).
		Return([]*settings.Setting{{AutoLink: models.AutoLink{State: consts.StateEnabled}}}, nil)
	mockConnectorService.On("GetBothConnections", mock.Anything, mock.Anything).
		Return(connectors.BothConnections{
			OrganizationID: "organization_id",
			App: models.App{
				Platform: "shopify",
				Key:      "s_1",
			},
			Channels: []models.App{
				{
					Platform: "tiktok-shop",
					Key:      "store_key",
				},
			},
		}, nil).Once()
	searchableProductService.On("Search", mock.Anything, mock.Anything).
		Return([]*searchable_products.SearchableProduct{
			{
				ID:               "pc_id_2",
				ProductsCenterID: "pc_id_2",
				Title:            createArg.Product.Title,
				Variants: []searchable_products.Variant{
					{
						ProductsCenterVariantID: "pc_id_2_01",
						Sku:                     "1008",
					},
				},
			},
			{
				ID:               "pc_id_1",
				ProductsCenterID: "pc_id_1",
				Title:            createArg.Product.Title,
				Variants: []searchable_products.Variant{
					{
						ProductsCenterVariantID: "pc_id_1_01",
						Sku:                     "1009",
					},
				},
			},
		}, models.Pagination{HasNextPage: false}, nil)
	taskMock := new(task.MockTaskService)
	taskMock.On("Create", mock.Anything, mock.Anything).Return(models.Task{}, nil)

	// 没有开启 variant 自动对齐, 可以进行 link
	createArg.Settings.ProductSyncSetting.Customized.UpdateVariants.AutoSync = "disabled"
	service := initService(t)
	require.NotNil(t, service)
	pl, err := service.Create(context.Background(), &createArg)
	require.NoError(t, err)
	require.NotNil(t, pl)
	service.logger = logger.Get()
	service.productsCenterClient = &products_center.Client{
		Product: mockClient,
	}
	service.settingService = mockSettingService
	service.connectorService = mockConnectorService
	service.searchableProductService = searchableProductService
	service.taskService = taskMock
	service.feedCliV2 = &feed.ClientV2{
		SupportFeature: mockSupportFeature,
	}

	result, err := service.AutoLink(context.Background(), &AutoLinkArg{ID: pl.ID})
	require.NoError(t, err)
	require.Equal(t, result.Relations[0].ProductsCenterVariant.ProductID, "pc_id_1")
	require.Equal(t, result.Relations[0].ProductsCenterVariant.ID, "pc_id_1_01")
}

/*
未关联的 listings: 没有 title 相同的 pc product, sku 被全局 link 上了
*/
func Test_serviceImpl_AutoLink_global_sku_link(t *testing.T) {

	mockClient := products_center.NewMockProductAPICollection(t)
	mockSettingService := new(settings.MockSettingService)
	mockConnectorService := new(connectors.MockService)
	searchableProductService := new(searchable_products.MockService)
	mockSupportFeature := new(feed.MockSupportFeature)

	mockSupportFeature.On("GetSupportFeatures", mock.Anything, mock.Anything).
		Return([]support_features.GetInternalSupportFeaturesDataSupportFeatures{
			{
				Name:   types.MakeString("auto_link_v2"),
				Status: types.MakeString("enabled"),
			},
		}, nil).Once()
	mockConnectorService.On("GetProductByID", mock.Anything, mock.Anything, mock.Anything).
		Return(&products.ModelsResponseProduct{}, nil)
	mockClient.On("List", mock.Anything, mock.Anything).
		Return([]*products_center.Product{
			{
				ID:    "pc_id_1",
				Title: "auto-link-title-1-2-3",
				Variants: []products_center.Variant{
					{
						ID:  "pc_id_1_01",
						Sku: "1009",
					},
				},
			},
			{
				ID:    "pc_id_2",
				Title: "auto-link-title-1-2-9",
				Variants: []products_center.Variant{
					{
						ID:  "pc_id_2_01",
						Sku: "1008",
					},
				},
			},
		}, nil)
	mockSettingService.On("List", mock.Anything, mock.Anything).
		Return([]*settings.Setting{{AutoLink: models.AutoLink{State: consts.StateEnabled}}}, nil)
	mockConnectorService.On("GetBothConnections", mock.Anything, mock.Anything).
		Return(connectors.BothConnections{
			OrganizationID: "organization_id",
			App: models.App{
				Platform: "shopify",
				Key:      "s_1",
			},
			Channels: []models.App{
				{
					Platform: "tiktok-shop",
					Key:      "store_key",
				},
			},
		}, nil).Once()
	searchableProductService.On("Search", mock.Anything, mock.Anything).
		Return([]*searchable_products.SearchableProduct{
			{
				ID:               "pc_id_2",
				ProductsCenterID: "pc_id_2",
				Variants: []searchable_products.Variant{
					{
						ProductsCenterVariantID: "pc_id_2_01",
						Sku:                     "1008",
					},
				},
			},
			{
				ID:               "pc_id_1",
				ProductsCenterID: "pc_id_1",
				Variants: []searchable_products.Variant{
					{
						ProductsCenterVariantID: "pc_id_1_01",
						Sku:                     "1009",
					},
				},
			},
		}, models.Pagination{HasNextPage: false}, nil)
	taskMock := new(task.MockTaskService)
	taskMock.On("Create", mock.Anything, mock.Anything).Return(models.Task{}, nil)

	createArg := generateAutoLinkProductListingArgs_no_relation()
	// 没有开启 variant 自动对齐, 可以进行 link
	createArg.Settings.ProductSyncSetting.Customized.UpdateVariants.AutoSync = "disabled"
	service := initService(t)
	require.NotNil(t, service)
	pl, err := service.Create(context.Background(), &createArg)
	require.NoError(t, err)
	require.NotNil(t, pl)
	service.logger = logger.Get()
	service.productsCenterClient = &products_center.Client{
		Product: mockClient,
	}
	service.settingService = mockSettingService
	service.connectorService = mockConnectorService
	service.searchableProductService = searchableProductService
	service.taskService = taskMock
	service.feedCliV2 = &feed.ClientV2{
		SupportFeature: mockSupportFeature,
	}

	result, err := service.AutoLink(context.Background(), &AutoLinkArg{ID: pl.ID})
	require.NoError(t, err)
	require.Equal(t, result.Relations[0].ProductsCenterVariant.ProductID, "pc_id_2")
	require.Equal(t, result.Relations[0].ProductsCenterVariant.ID, "pc_id_2_01")
	require.Equal(t, result.Relations[1].ProductsCenterVariant.ProductID, "pc_id_1")
	require.Equal(t, result.Relations[1].ProductsCenterVariant.ID, "pc_id_1_01")

}

/*
未关联的 listings: 没有 title 相同的 pc product, sku 被全局 link 上了, 但是有个 sku 因 same_sku_name 没有被 link
*/
func Test_serviceImpl_AutoLink_global_sku_link_but_one_same_sku(t *testing.T) {

	mockClient := products_center.NewMockProductAPICollection(t)
	mockSettingService := new(settings.MockSettingService)
	mockConnectorService := new(connectors.MockService)
	searchableProductService := new(searchable_products.MockService)
	mockSupportFeature := new(feed.MockSupportFeature)

	mockSupportFeature.On("GetSupportFeatures", mock.Anything, mock.Anything).
		Return([]support_features.GetInternalSupportFeaturesDataSupportFeatures{
			{
				Name:   types.MakeString("auto_link_v2"),
				Status: types.MakeString("enabled"),
			},
		}, nil).Once()
	mockClient.On("List", mock.Anything, mock.Anything).
		Return([]*products_center.Product{
			{
				ID:    "pc_id_1",
				Title: "auto-link-title-1-2-3",
				Variants: []products_center.Variant{
					{
						ID:  "pc_id_1_01",
						Sku: "1009",
					},
				},
			},
			{
				ID:    "pc_id_2",
				Title: "auto-link-title-1-2-9",
				Variants: []products_center.Variant{
					{
						ID:  "pc_id_2_01",
						Sku: "1008",
					},
				},
			},
			{
				ID:    "pc_id_3",
				Title: "auto-link-title-1-2-9",
				Variants: []products_center.Variant{
					{
						ID:  "pc_id_3_01",
						Sku: "1008",
					},
				},
			},
		}, nil)
	mockSettingService.On("List", mock.Anything, mock.Anything).
		Return([]*settings.Setting{{AutoLink: models.AutoLink{State: consts.StateEnabled}}}, nil)
	mockConnectorService.On("GetBothConnections", mock.Anything, mock.Anything).
		Return(connectors.BothConnections{
			OrganizationID: "organization_id",
			App: models.App{
				Platform: "shopify",
				Key:      "s_1",
			},
			Channels: []models.App{
				{
					Platform: "tiktok-shop",
					Key:      "store_key",
				},
			},
		}, nil).Once()
	mockConnectorService.On("GetProductByID", mock.Anything, mock.Anything, mock.Anything).
		Return(&products.ModelsResponseProduct{}, nil)
	searchableProductService.On("Search", mock.Anything, mock.Anything).
		Return([]*searchable_products.SearchableProduct{
			{
				ID:               "pc_id_3",
				ProductsCenterID: "pc_id_3",
				Variants: []searchable_products.Variant{
					{
						ProductsCenterVariantID: "pc_id_3_01",
						Sku:                     "1008",
					},
				},
			},
			{
				ID:               "pc_id_2",
				ProductsCenterID: "pc_id_2",
				Variants: []searchable_products.Variant{
					{
						ProductsCenterVariantID: "pc_id_2_01",
						Sku:                     "1008",
					},
				},
			},
			{
				ID:               "pc_id_1",
				ProductsCenterID: "pc_id_1",
				Variants: []searchable_products.Variant{
					{
						ProductsCenterVariantID: "pc_id_1_01",
						Sku:                     "1009",
					},
				},
			},
		}, models.Pagination{HasNextPage: false}, nil)
	taskMock := new(task.MockTaskService)
	taskMock.On("Create", mock.Anything, mock.Anything).Return(models.Task{}, nil)

	createArg := generateAutoLinkProductListingArgs_no_relation()
	// 没有开启 variant 自动对齐, 可以进行 link
	createArg.Settings.ProductSyncSetting.Customized.UpdateVariants.AutoSync = "disabled"
	service := initService(t)
	require.NotNil(t, service)
	pl, err := service.Create(context.Background(), &createArg)
	require.NoError(t, err)
	require.NotNil(t, pl)
	service.logger = logger.Get()
	service.productsCenterClient = &products_center.Client{
		Product: mockClient,
	}
	service.settingService = mockSettingService
	service.connectorService = mockConnectorService
	service.searchableProductService = searchableProductService
	service.taskService = taskMock
	service.feedCliV2 = &feed.ClientV2{
		SupportFeature: mockSupportFeature,
	}

	result, err := service.AutoLink(context.Background(), &AutoLinkArg{ID: pl.ID})
	require.NoError(t, err)
	require.Equal(t, result.Relations[0].ProductsCenterVariant.ProductID, "")
	require.Equal(t, result.Relations[0].ProductsCenterVariant.ID, "")
	require.Equal(t, result.Relations[1].ProductsCenterVariant.ProductID, "pc_id_1")
	require.Equal(t, result.Relations[1].ProductsCenterVariant.ID, "pc_id_1_01")

}

/*
有关联的 listings: [一对多]有 title 相同的 pc product, 部分 SKU 先被 title相等的 抢去 link 上, 其他 SKU 也会被 link 上了
*/
// nolint:maintidx
func Test_serviceImpl_AutoLink_multiple_relation_case_1(t *testing.T) {

	mockClient := products_center.NewMockProductAPICollection(t)
	mockSettingService := new(settings.MockSettingService)
	mockConnectorService := new(connectors.MockService)
	searchableProductService := new(searchable_products.MockService)
	mockSupportFeature := new(feed.MockSupportFeature)

	mockSupportFeature.On("GetSupportFeatures", mock.Anything, mock.Anything).
		Return([]support_features.GetInternalSupportFeaturesDataSupportFeatures{
			{
				Name:   types.MakeString("auto_link_v2"),
				Status: types.MakeString("enabled"),
			},
		}, nil).Once()
	mockClient.On("List", mock.Anything, mock.MatchedBy(func(arg *products_center.GetProductsArgs) bool {
		return arg.IDs == "pc_1,pc_id_1,pc_id_2"
	})).
		Return([]*products_center.Product{
			{
				ID:    "pc_1",
				Title: "auto-link-title-3",
				Source: products_center.Source{
					App: products_center.SourceApp{
						Platform: "shopify",
						Key:      "s_1",
					},
				},
				Variants: []products_center.Variant{
					{
						ID:  "pc_1_01",
						Sku: "001",
					},
				},
			},
			{
				ID:    "pc_id_1",
				Title: "auto-link-title-2",
				Source: products_center.Source{
					App: products_center.SourceApp{
						Platform: "shopify",
						Key:      "s_1",
					},
				},
				Variants: []products_center.Variant{
					{
						ID:  "pc_id_1_01",
						Sku: "1008",
					},
					{
						ID:  "pc_id_1_02",
						Sku: "1010",
					},
				},
			},
			{
				ID:    "pc_id_2",
				Title: "auto-link-title-1",
				Source: products_center.Source{
					App: products_center.SourceApp{
						Platform: "shopify",
						Key:      "s_1",
					},
				},
				Variants: []products_center.Variant{
					{
						ID:  "pc_id_2_01",
						Sku: "1008",
					},
					{
						ID:  "pc_id_2_02",
						Sku: "1010", // title 相等, 被优先 link
					},
				},
			},
		}, nil)
	mockClient.On("List", mock.Anything, mock.MatchedBy(func(arg *products_center.GetProductsArgs) bool {
		return strings.Contains(arg.IDs, "pc_id_2") && strings.Contains(arg.IDs, "pc_id_4") && strings.Contains(arg.IDs, "pc_id_5")
	})).
		Return([]*products_center.Product{
			{
				ID:    "pc_id_2",
				Title: "auto-link-title-1",
				Source: products_center.Source{
					App: products_center.SourceApp{
						Platform: "shopify",
						Key:      "s_1",
					},
				},
				Variants: []products_center.Variant{
					{
						ID:  "pc_id_2_01",
						Sku: "1008",
					},
					{
						ID:  "pc_id_2_02",
						Sku: "1010", // title 相等, 被优先 link
					},
				},
			},
			{
				ID:    "pc_id_4",
				Title: "auto-link-title-2",
				Source: products_center.Source{
					App: products_center.SourceApp{
						Platform: "shopify",
						Key:      "s_1",
					},
				},
				Variants: []products_center.Variant{
					{
						ID:  "pc_id_4_01",
						Sku: "1011",
					},
				},
			},
			{
				ID:    "pc_id_5",
				Title: "auto-link-title-1",
				Source: products_center.Source{
					App: products_center.SourceApp{
						Platform: "shopify",
						Key:      "s_1",
					},
				},
				Variants: []products_center.Variant{
					{
						ID:  "pc_id_5_01",
						Sku: "1012",
					},
				},
			},
		}, nil)
	searchableProductService.On("Search", mock.Anything, mock.Anything).
		Return([]*searchable_products.SearchableProduct{
			{
				ID:               "pc_id_4",
				ProductsCenterID: "pc_id_4",
				Variants: []searchable_products.Variant{
					{
						ProductsCenterVariantID: "pc_id_4_01",
						Sku:                     "1011",
					},
				},
			},
			{
				ID:               "pc_id_5",
				ProductsCenterID: "pc_id_5",
				Variants: []searchable_products.Variant{
					{
						ProductsCenterVariantID: "pc_id_5_01",
						Sku:                     "1012",
					},
				},
			},
		}, models.Pagination{HasNextPage: false}, nil)
	mockSettingService.On("List", mock.Anything, mock.Anything).
		Return([]*settings.Setting{{AutoLink: models.AutoLink{State: consts.StateEnabled}}}, nil)
	mockConnectorService.On("GetBothConnections", mock.Anything, mock.Anything).
		Return(connectors.BothConnections{
			OrganizationID: "organization_id",
			App: models.App{
				Platform: "shopify",
				Key:      "s_1",
			},
			Channels: []models.App{
				{
					Platform: "tiktok-shop",
					Key:      "store_key",
				},
			},
		}, nil).Once()
	taskMock := new(task.MockTaskService)
	taskMock.On("Create", mock.Anything, mock.Anything).Return(models.Task{}, nil)

	createArg := generateAutoLinkProductListingArgs_multiple_relations()
	// 没有开启 variant 自动对齐, 可以进行 link
	createArg.Settings.ProductSyncSetting.Customized.UpdateVariants.AutoSync = "disabled"
	service := initService(t)
	require.NotNil(t, service)
	pl, err := service.Create(context.Background(), &createArg)
	require.NoError(t, err)
	require.NotNil(t, pl)
	service.logger = logger.Get()
	service.productsCenterClient = &products_center.Client{
		Product: mockClient,
	}
	service.settingService = mockSettingService
	service.connectorService = mockConnectorService
	service.searchableProductService = searchableProductService
	service.taskService = taskMock
	service.feedCliV2 = &feed.ClientV2{
		SupportFeature: mockSupportFeature,
	}

	result, err := service.AutoLink(context.Background(), &AutoLinkArg{ID: pl.ID})
	require.NoError(t, err)
	require.Equal(t, result.Relations[0].ProductsCenterVariant.ProductID, "pc_id_1")
	require.Equal(t, result.Relations[0].ProductsCenterVariant.ID, "pc_id_1_01")
	require.Equal(t, result.Relations[1].ProductsCenterVariant.ProductID, "pc_id_2")
	require.Equal(t, result.Relations[1].ProductsCenterVariant.ID, "pc_id_2_01")
	require.Equal(t, result.Relations[2].ProductsCenterVariant.ProductID, "pc_id_2")
	require.Equal(t, result.Relations[2].ProductsCenterVariant.ID, "pc_id_2_02")
	require.Equal(t, result.Relations[3].ProductsCenterVariant.ProductID, "pc_id_4")
	require.Equal(t, result.Relations[3].ProductsCenterVariant.ID, "pc_id_4_01")
	require.Equal(t, result.Relations[4].ProductsCenterVariant.ProductID, "pc_id_5")
	require.Equal(t, result.Relations[4].ProductsCenterVariant.ID, "pc_id_5_01")
}

/*
有关联的 listings: [一对多]没有 title 相等的 pc product, 部分 SKU 因 option 相等所以被 link 上了, 剩余部分 SKU 被全局 link 上了
*/
// nolint:maintidx
func Test_serviceImpl_AutoLink_multiple_relation_case_2(t *testing.T) {

	mockClient := products_center.NewMockProductAPICollection(t)
	mockSettingService := new(settings.MockSettingService)
	mockConnectorService := new(connectors.MockService)
	searchableProductService := new(searchable_products.MockService)
	mockSupportFeature := new(feed.MockSupportFeature)

	mockSupportFeature.On("GetSupportFeatures", mock.Anything, mock.Anything).
		Return([]support_features.GetInternalSupportFeaturesDataSupportFeatures{
			{
				Name:   types.MakeString("auto_link_v2"),
				Status: types.MakeString("enabled"),
			},
		}, nil).Once()
	mockClient.On("List", mock.Anything, mock.MatchedBy(func(arg *products_center.GetProductsArgs) bool {
		return arg.IDs == "pc_1,pc_id_1,pc_id_2"
	})).
		Return([]*products_center.Product{
			{
				ID:    "pc_1",
				Title: "auto-link-title-3",
				Source: products_center.Source{
					App: products_center.SourceApp{
						Platform: "shopify",
						Key:      "s_1",
					},
				},
				Variants: []products_center.Variant{
					{
						ID:  "pc_1_01",
						Sku: "001",
					},
				},
			},
			{
				ID:    "pc_id_1",
				Title: "auto-link-title-2-xx",
				Source: products_center.Source{
					App: products_center.SourceApp{
						Platform: "shopify",
						Key:      "s_1",
					},
				},
				Variants: []products_center.Variant{
					{
						ID:  "pc_id_1_01",
						Sku: "1008",
					},
					{
						ID:  "pc_id_1_02",
						Sku: "0909",
					},
				},
			},
			{
				ID:    "pc_id_2",
				Title: "auto-link-title-1-xx",
				Source: products_center.Source{
					App: products_center.SourceApp{
						Platform: "shopify",
						Key:      "s_1",
					},
				},
				Variants: []products_center.Variant{
					{
						ID:  "pc_id_2_01",
						Sku: "1008",
						Options: []products_center.VariantOption{
							{
								Name:  "color",
								Value: "black",
							},
							{
								Name:  "size",
								Value: "xxl-a", // 因为 option 相等被 link
							},
						},
					},
					{
						ID:  "pc_id_2_02",
						Sku: "xxx",
						Options: []products_center.VariantOption{
							{
								Name:  "color",
								Value: "black",
							},
							{
								Name:  "size",
								Value: "xxl", // 因为 option 相等被 link
							},
						},
					},
				},
			},
		}, nil)
	mockClient.On("List", mock.Anything, mock.MatchedBy(func(arg *products_center.GetProductsArgs) bool {
		return strings.Contains(arg.IDs, "pc_id_2") && strings.Contains(arg.IDs, "pc_id_4") && strings.Contains(arg.IDs, "pc_id_5")
	})).
		Return([]*products_center.Product{
			{
				ID:    "pc_id_2",
				Title: "auto-link-title-1",
				Source: products_center.Source{
					App: products_center.SourceApp{
						Platform: "shopify",
						Key:      "s_1",
					},
				},
				Variants: []products_center.Variant{
					{
						ID:  "pc_id_2_01",
						Sku: "1008",
					},
					{
						ID:  "pc_id_2_02",
						Sku: "1010",
					},
				},
			},
			{
				ID:    "pc_id_4",
				Title: "auto-link-title-2",
				Source: products_center.Source{
					App: products_center.SourceApp{
						Platform: "shopify",
						Key:      "s_1",
					},
				},
				Variants: []products_center.Variant{
					{
						ID:  "pc_id_4_01",
						Sku: "1011",
					},
				},
			},
			{
				ID:    "pc_id_5",
				Title: "auto-link-title-1",
				Source: products_center.Source{
					App: products_center.SourceApp{
						Platform: "shopify",
						Key:      "s_1",
					},
				},
				Variants: []products_center.Variant{
					{
						ID:  "pc_id_5_01",
						Sku: "1012",
					},
				},
			},
		}, nil)
	searchableProductService.On("Search", mock.Anything, mock.Anything).
		Return([]*searchable_products.SearchableProduct{
			{
				ID:               "pc_id_4",
				ProductsCenterID: "pc_id_4",
				Variants: []searchable_products.Variant{
					{
						ProductsCenterVariantID: "pc_id_4_01",
						Sku:                     "1011",
					},
				},
			},
			{
				ID:               "pc_id_5",
				ProductsCenterID: "pc_id_5",
				Variants: []searchable_products.Variant{
					{
						ProductsCenterVariantID: "pc_id_5_01",
						Sku:                     "1012",
					},
				},
			},
		}, models.Pagination{HasNextPage: false}, nil)
	mockSettingService.On("List", mock.Anything, mock.Anything).
		Return([]*settings.Setting{{AutoLink: models.AutoLink{State: consts.StateEnabled}}}, nil)
	mockConnectorService.On("GetBothConnections", mock.Anything, mock.Anything).
		Return(connectors.BothConnections{
			OrganizationID: "organization_id",
			App: models.App{
				Platform: "shopify",
				Key:      "s_1",
			},
			Channels: []models.App{
				{
					Platform: "tiktok-shop",
					Key:      "store_key",
				},
			},
		}, nil).Once()
	taskMock := new(task.MockTaskService)
	taskMock.On("Create", mock.Anything, mock.Anything).Return(models.Task{}, nil)

	createArg := generateAutoLinkProductListingArgs_multiple_relations()
	// 没有开启 variant 自动对齐, 可以进行 link
	createArg.Settings.ProductSyncSetting.Customized.UpdateVariants.AutoSync = "disabled"
	service := initService(t)
	require.NotNil(t, service)
	pl, err := service.Create(context.Background(), &createArg)
	require.NoError(t, err)
	require.NotNil(t, pl)
	service.logger = logger.Get()
	service.productsCenterClient = &products_center.Client{
		Product: mockClient,
	}
	service.settingService = mockSettingService
	service.connectorService = mockConnectorService
	service.searchableProductService = searchableProductService
	service.taskService = taskMock
	service.feedCliV2 = &feed.ClientV2{
		SupportFeature: mockSupportFeature,
	}

	result, err := service.AutoLink(context.Background(), &AutoLinkArg{ID: pl.ID})
	require.NoError(t, err)
	require.Equal(t, result.Relations[0].ProductsCenterVariant.ProductID, "pc_id_1")
	require.Equal(t, result.Relations[0].ProductsCenterVariant.ID, "pc_id_1_01")
	require.Equal(t, result.Relations[1].ProductsCenterVariant.ProductID, "pc_id_2")
	require.Equal(t, result.Relations[1].ProductsCenterVariant.ID, "pc_id_2_01")
	require.Equal(t, result.Relations[2].ProductsCenterVariant.ProductID, "pc_id_2")
	require.Equal(t, result.Relations[2].ProductsCenterVariant.ID, "pc_id_2_02")
	require.Equal(t, result.Relations[3].ProductsCenterVariant.ProductID, "pc_id_4")
	require.Equal(t, result.Relations[3].ProductsCenterVariant.ID, "pc_id_4_01")
	require.Equal(t, result.Relations[4].ProductsCenterVariant.ProductID, "pc_id_5")
	require.Equal(t, result.Relations[4].ProductsCenterVariant.ID, "pc_id_5_01")
}

func Test_serviceImpl_AutoLink_reference_id_success(t *testing.T) {

	mockClient := products_center.NewMockProductAPICollection(t)
	mockSettingService := new(settings.MockSettingService)
	mockConnectorService := new(connectors.MockService)
	searchableProductService := new(searchable_products.MockService)
	mockSupportFeature := new(feed.MockSupportFeature)
	// mockProductCenterService := new(products_center.MockProductAPICollection)

	mockSupportFeature.On("GetSupportFeatures", mock.Anything, mock.Anything).
		Return([]support_features.GetInternalSupportFeaturesDataSupportFeatures{
			{
				Name:   types.MakeString("auto_link_v2"),
				Status: types.MakeString("enabled"),
			},
		}, nil).Once()
	mockSettingService.On("List", mock.Anything, mock.Anything).
		Return([]*settings.Setting{{AutoLink: models.AutoLink{State: consts.StateEnabled}}}, nil)
	mockConnectorService.On("GetBothConnections", mock.Anything, mock.Anything).
		Return(connectors.BothConnections{
			OrganizationID: "organization_id",
			App: models.App{
				Platform: "shopify",
				Key:      "s_1",
			},
			Channels: []models.App{
				{
					Platform: "tiktok-shop",
					Key:      "store_key",
				},
			},
		}, nil).Once()
	mockConnectorService.On("GetProductByID", mock.Anything, mock.Anything, mock.Anything).
		Return(&products.ModelsResponseProduct{
			ExternalReferenceID: types.MakeString("ecommerce_product_id_1"),
			Variants: []products.ModelsResponseProductVariant{
				{
					ExternalID:          types.MakeString("variant_1"),
					ExternalReferenceID: types.MakeString("ecommerce_variant_id_1"),
				},
				{
					ExternalID:          types.MakeString("variant_2"),
					ExternalReferenceID: types.MakeString("ecommerce_variant_id_2"),
				},
			},
		}, nil)
	mockConnectorService.On("GetProducts", mock.Anything, mock.Anything).
		Return([]products.ModelsResponseProduct{
			{
				ExternalID: types.MakeString("ecommerce_product_id_1"),
				Variants: []products.ModelsResponseProductVariant{
					{

						ExternalID: types.MakeString("ecommerce_variant_id_1"),
					},
				},
			},
		}, nil)
	mockClient.On("List", mock.Anything, mock.Anything).
		Return([]*products_center.Product{
			{
				ID: "pc_id_1",
				Variants: []products_center.Variant{
					{
						ID:              "pc_variant_id_1",
						SourceVariantID: "ecommerce_variant_id_1",
					},
				},
			},
		}, nil)
	searchableProductService.On("Search", mock.Anything, mock.Anything).
		Return([]*searchable_products.SearchableProduct{}, models.Pagination{HasNextPage: false}, nil)
	searchableProductService.On("Search", mock.Anything, mock.Anything).
		Return([]*searchable_products.SearchableProduct{}, models.Pagination{HasNextPage: false}, nil)
	taskMock := new(task.MockTaskService)
	taskMock.On("Create", mock.Anything, mock.Anything).Return(models.Task{}, nil)

	createArg := generateAutoLinkProductListingArgs_reference_id()
	// 没有开启 variant 自动对齐, 可以进行 link
	createArg.Settings.ProductSyncSetting.Customized.UpdateVariants.AutoSync = "disabled"
	service := initService(t)
	require.NotNil(t, service)
	pl, err := service.Create(context.Background(), &createArg)
	require.NoError(t, err)
	require.NotNil(t, pl)
	service.logger = logger.Get()
	service.productsCenterClient = &products_center.Client{
		Product: mockClient,
	}
	service.settingService = mockSettingService
	service.connectorService = mockConnectorService
	service.searchableProductService = searchableProductService
	service.taskService = taskMock
	service.feedCliV2 = &feed.ClientV2{
		SupportFeature: mockSupportFeature,
	}

	result, err := service.AutoLink(context.Background(), &AutoLinkArg{ID: pl.ID})
	require.NoError(t, err)
	require.Equal(t, result.Relations[0].ProductsCenterVariant.ProductID, "pc_id_1")
	require.Equal(t, result.Relations[0].ProductsCenterVariant.ID, "pc_variant_id_1")
	require.Equal(t, result.Relations[1].ProductsCenterVariant.ProductID, "")
	require.Equal(t, result.Relations[1].ProductsCenterVariant.ID, "")

}

func Test_serviceImpl_AutoLink_reference_id_failed(t *testing.T) {

	mockClient := products_center.NewMockProductAPICollection(t)
	mockSettingService := new(settings.MockSettingService)
	mockConnectorService := new(connectors.MockService)
	searchableProductService := new(searchable_products.MockService)
	mockSupportFeature := new(feed.MockSupportFeature)
	// mockProductCenterService := new(products_center.MockProductAPICollection)

	mockSupportFeature.On("GetSupportFeatures", mock.Anything, mock.Anything).
		Return([]support_features.GetInternalSupportFeaturesDataSupportFeatures{
			{
				Name:   types.MakeString("auto_link_v2"),
				Status: types.MakeString("enabled"),
			},
		}, nil).Once()
	mockSettingService.On("List", mock.Anything, mock.Anything).
		Return([]*settings.Setting{{AutoLink: models.AutoLink{State: consts.StateEnabled}}}, nil)
	mockConnectorService.On("GetBothConnections", mock.Anything, mock.Anything).
		Return(connectors.BothConnections{
			OrganizationID: "organization_id",
			App: models.App{
				Platform: "shopify",
				Key:      "s_1",
			},
			Channels: []models.App{
				{
					Platform: "tiktok-shop",
					Key:      "store_key",
				},
			},
		}, nil).Once()
	mockConnectorService.On("GetProductByID", mock.Anything, mock.Anything, mock.Anything).
		Return(&products.ModelsResponseProduct{
			ExternalReferenceID: types.MakeString("ecommerce_product_id_1"),
			Variants: []products.ModelsResponseProductVariant{
				{
					ExternalID:          types.MakeString("variant_1"),
					ExternalReferenceID: types.MakeString("ecommerce_variant_id_1"),
				},
				{
					ExternalID:          types.MakeString("variant_2"),
					ExternalReferenceID: types.MakeString("ecommerce_variant_id_2"),
				},
			},
		}, nil)
	mockConnectorService.On("GetProducts", mock.Anything, mock.Anything).
		Return([]products.ModelsResponseProduct{
			{
				ExternalID: types.MakeString("ecommerce_product_id_1"),
				Variants: []products.ModelsResponseProductVariant{
					{

						ExternalID: types.MakeString("ecommerce_variant_id_99"), // no match
					},
				},
			},
		}, nil)
	searchableProductService.On("Search", mock.Anything, mock.Anything).
		Return([]*searchable_products.SearchableProduct{}, models.Pagination{HasNextPage: false}, nil)
	searchableProductService.On("Search", mock.Anything, mock.Anything).
		Return([]*searchable_products.SearchableProduct{}, models.Pagination{HasNextPage: false}, nil)
	taskMock := new(task.MockTaskService)
	taskMock.On("Create", mock.Anything, mock.Anything).Return(models.Task{}, nil)

	createArg := generateAutoLinkProductListingArgs_reference_id()
	// 没有开启 variant 自动对齐, 可以进行 link
	createArg.Settings.ProductSyncSetting.Customized.UpdateVariants.AutoSync = "disabled"
	service := initService(t)
	require.NotNil(t, service)
	pl, err := service.Create(context.Background(), &createArg)
	require.NoError(t, err)
	require.NotNil(t, pl)
	service.logger = logger.Get()
	service.productsCenterClient = &products_center.Client{
		Product: mockClient,
	}
	service.settingService = mockSettingService
	service.connectorService = mockConnectorService
	service.searchableProductService = searchableProductService
	service.taskService = taskMock
	service.feedCliV2 = &feed.ClientV2{
		SupportFeature: mockSupportFeature,
	}

	_, err = service.AutoLink(context.Background(), &AutoLinkArg{ID: pl.ID})
	require.ErrorIs(t, err, ErrNoValidVariantsToAutoLink)

}

func Test_serviceImpl_AutoLink_pc_product_id_success(t *testing.T) {

	mockClient := products_center.NewMockProductAPICollection(t)
	mockSettingService := new(settings.MockSettingService)
	mockConnectorService := new(connectors.MockService)
	searchableProductService := new(searchable_products.MockService)

	mockClient.On("GetByID", mock.Anything, mock.Anything).
		Return(&products_center.Product{
			ID: "pc_id_1",
			Source: products_center.Source{
				App: products_center.SourceApp{
					Platform: "shopify",
					Key:      "s_1",
				},
			},
			Status: string(consts.ProductsCenterProductPublishStateActive),
			Variants: []products_center.Variant{
				{
					ID:  "pc_id_v_01",
					Sku: "1008",
				},
				{
					ID:  "pc_id_v_02",
					Sku: "1009",
				},
			},
		}, nil)
	mockSettingService.On("List", mock.Anything, mock.Anything).
		Return([]*settings.Setting{{AutoLink: models.AutoLink{State: consts.StateEnabled}}}, nil)
	mockConnectorService.On("GetBothConnections", mock.Anything, mock.Anything).
		Return(connectors.BothConnections{
			OrganizationID: "organization_id",
			App: models.App{
				Platform: "shopify",
				Key:      "s_1",
			},
			Channels: []models.App{
				{
					Platform: "tiktok-shop",
					Key:      "store_key",
				},
			},
		}, nil).Once()
	mockClient.On("List", mock.Anything, mock.Anything).
		Return([]*products_center.Product{
			{
				ID: "pc_id_1",
				Variants: []products_center.Variant{
					{
						ID: "pc_id_v_01",
					},
					{
						ID: "pc_id_v_02",
					},
				},
			},
		}, nil)
	mockConnectorService.On("GetProductByID", mock.Anything, mock.Anything, mock.Anything).
		Return(&products.ModelsResponseProduct{ExternalReferenceID: types.MakeString("")}, nil)
	searchableProductService.On("Search", mock.Anything, mock.Anything).
		Return([]*searchable_products.SearchableProduct{}, models.Pagination{HasNextPage: false}, nil)
	searchableProductService.On("Search", mock.Anything, mock.Anything).
		Return([]*searchable_products.SearchableProduct{}, models.Pagination{HasNextPage: false}, nil)
	taskMock := new(task.MockTaskService)
	taskMock.On("Create", mock.Anything, mock.Anything).Return(models.Task{}, nil)

	createArg := generateAutoLinkProductListingArgs_pcpID()
	// 没有开启 variant 自动对齐, 可以进行 link
	createArg.Settings.ProductSyncSetting.Customized.UpdateVariants.AutoSync = "disabled"
	service := initService(t)
	require.NotNil(t, service)
	pl, err := service.Create(context.Background(), &createArg)
	require.NoError(t, err)
	require.NotNil(t, pl)
	service.logger = logger.Get()
	service.productsCenterClient = &products_center.Client{
		Product: mockClient,
	}
	service.settingService = mockSettingService
	service.connectorService = mockConnectorService
	service.searchableProductService = searchableProductService
	service.taskService = taskMock
	service.feedCliV2 = &feed.ClientV2{}

	result, err := service.AutoLink(context.Background(), &AutoLinkArg{ID: pl.ID, RecommendedPCProductID: "pc_id_1"})
	require.NoError(t, err)
	require.Equal(t, result.Relations[0].ProductsCenterVariant.ProductID, "pc_id_1")
	require.Equal(t, result.Relations[0].ProductsCenterVariant.ID, "pc_id_v_01")
	require.Equal(t, result.Relations[1].ProductsCenterVariant.ProductID, "pc_id_1")
	require.Equal(t, result.Relations[1].ProductsCenterVariant.ID, "pc_id_v_02")

}

func Test_serviceImpl_AutoLink_pc_product_id_partial_success(t *testing.T) {

	mockClient := products_center.NewMockProductAPICollection(t)
	mockSettingService := new(settings.MockSettingService)
	mockConnectorService := new(connectors.MockService)
	searchableProductService := new(searchable_products.MockService)

	mockClient.On("GetByID", mock.Anything, mock.Anything).
		Return(&products_center.Product{
			ID: "pc_id_1",
			Source: products_center.Source{
				App: products_center.SourceApp{
					Platform: "shopify",
					Key:      "s_1",
				},
			},
			Status: string(consts.ProductsCenterProductPublishStateActive),
			Variants: []products_center.Variant{
				{
					ID:  "pc_id_v_01",
					Sku: "1008",
				},
				{
					ID:  "pc_id_v_02",
					Sku: "1009_01",
				},
			},
		}, nil)
	mockSettingService.On("List", mock.Anything, mock.Anything).
		Return([]*settings.Setting{{AutoLink: models.AutoLink{State: consts.StateEnabled}}}, nil)
	mockConnectorService.On("GetBothConnections", mock.Anything, mock.Anything).
		Return(connectors.BothConnections{
			OrganizationID: "organization_id",
			App: models.App{
				Platform: "shopify",
				Key:      "s_1",
			},
			Channels: []models.App{
				{
					Platform: "tiktok-shop",
					Key:      "store_key",
				},
			},
		}, nil).Once()
	mockClient.On("List", mock.Anything, mock.Anything).
		Return([]*products_center.Product{
			{
				ID: "pc_id_1",
				Variants: []products_center.Variant{
					{
						ID: "pc_id_v_01",
					},
					{
						ID: "pc_id_v_02",
					},
				},
			},
		}, nil)
	mockConnectorService.On("GetProductByID", mock.Anything, mock.Anything, mock.Anything).
		Return(&products.ModelsResponseProduct{ExternalReferenceID: types.MakeString("")}, nil)
	searchableProductService.On("Search", mock.Anything, mock.Anything).
		Return([]*searchable_products.SearchableProduct{}, models.Pagination{HasNextPage: false}, nil)
	searchableProductService.On("Search", mock.Anything, mock.Anything).
		Return([]*searchable_products.SearchableProduct{}, models.Pagination{HasNextPage: false}, nil)
	taskMock := new(task.MockTaskService)
	taskMock.On("Create", mock.Anything, mock.Anything).Return(models.Task{}, nil)

	createArg := generateAutoLinkProductListingArgs_pcpID()
	// 没有开启 variant 自动对齐, 可以进行 link
	createArg.Settings.ProductSyncSetting.Customized.UpdateVariants.AutoSync = "disabled"
	service := initService(t)
	require.NotNil(t, service)
	pl, err := service.Create(context.Background(), &createArg)
	require.NoError(t, err)
	require.NotNil(t, pl)
	service.logger = logger.Get()
	service.productsCenterClient = &products_center.Client{
		Product: mockClient,
	}
	service.settingService = mockSettingService
	service.connectorService = mockConnectorService
	service.searchableProductService = searchableProductService
	service.taskService = taskMock
	service.feedCliV2 = &feed.ClientV2{}

	result, err := service.AutoLink(context.Background(), &AutoLinkArg{ID: pl.ID, RecommendedPCProductID: "pc_id_1"})
	require.NoError(t, err)
	require.Equal(t, result.Relations[0].ProductsCenterVariant.ProductID, "pc_id_1")
	require.Equal(t, result.Relations[0].ProductsCenterVariant.ID, "pc_id_v_01")
	require.Equal(t, result.Relations[1].ProductsCenterVariant.ProductID, "")
	require.Equal(t, result.Relations[1].ProductsCenterVariant.ID, "")

}

func Test_overWriteAttributes(t *testing.T) {

	tests := []struct {
		name               string
		categoryAttributes *category.AttributesOutput
		productAttributes  []*models.ProductAttribute
		inputAttributes    []*models.ProductAttribute
		want               []*models.ProductAttribute
	}{
		{
			name: "required attributes check",
			categoryAttributes: &category.AttributesOutput{
				Attributes: []category.Attribute{
					{
						IsRequired: true,
						ID:         "id_1",
						Type:       consts.AttributeTypeProductProperty,
					},
					{
						IsRequired: false,
						ID:         "id_2",
						Type:       consts.AttributeTypeProductProperty,
					},
					{
						IsRequired: true,
						ID:         "id_3",
						Type:       consts.AttributeTypeProductProperty,
					},
				},
			},
			productAttributes: []*models.ProductAttribute{
				{
					SalesChannelID: "id_3",
					Values: []models.SalesChannelResource{
						{
							SalesChannelID: "value_3_origin",
						},
					},
				},
			},
			inputAttributes: []*models.ProductAttribute{
				{
					SalesChannelID: "id_1",
					Values: []models.SalesChannelResource{
						{
							SalesChannelID: "value_1",
						},
					},
				},
				{
					SalesChannelID: "id_2",
					Values: []models.SalesChannelResource{
						{
							SalesChannelID: "value_2",
						},
					},
				},
				{
					SalesChannelID: "id_3",
					Values: []models.SalesChannelResource{
						{
							SalesChannelID: "value_3",
						},
					},
				},
			},
			want: []*models.ProductAttribute{
				{
					SalesChannelID: "id_1",
					Values: []models.SalesChannelResource{
						{
							SalesChannelID: "value_1",
						},
					},
				},
				{
					SalesChannelID: "id_3",
					Values: []models.SalesChannelResource{
						{
							SalesChannelID: "value_3",
						},
					},
				},
			},
		},
		{
			name: "condition attributes check - 1",
			categoryAttributes: &category.AttributesOutput{
				Attributes: []category.Attribute{
					{
						IsRequired: true,
						ID:         "id_1",
						Type:       consts.AttributeTypeProductProperty,
					},
					{
						IsRequired: false,
						ID:         "id_2",
						Type:       consts.AttributeTypeProductProperty,
						RequirementConditions: []category.AttributeRequirementCondition{
							{
								AttributeID:      "id_3",
								AttributeValueID: "value_3_origin",
							},
						},
					},
					{
						IsRequired: true,
						ID:         "id_3",
						Type:       consts.AttributeTypeProductProperty,
					},
					{
						IsRequired: false,
						ID:         "id_4",
						Type:       consts.AttributeTypeProductProperty,
						RequirementConditions: []category.AttributeRequirementCondition{
							{
								AttributeID:      "id_3",
								AttributeValueID: "value_3",
							},
						},
					},
				},
			},
			productAttributes: []*models.ProductAttribute{
				{
					SalesChannelID: "id_3",
					Values: []models.SalesChannelResource{
						{
							SalesChannelID: "value_3_origin",
						},
					},
				},
			},
			inputAttributes: []*models.ProductAttribute{
				{
					SalesChannelID: "id_1",
					Values: []models.SalesChannelResource{
						{
							SalesChannelID: "value_1",
						},
					},
				},
				{
					SalesChannelID: "id_2",
					Values: []models.SalesChannelResource{
						{
							SalesChannelID: "value_2",
						},
					},
				},
				{
					SalesChannelID: "id_3",
					Values: []models.SalesChannelResource{
						{
							SalesChannelID: "value_3",
						},
					},
				},
				{
					SalesChannelID: "id_4",
					Values: []models.SalesChannelResource{
						{
							SalesChannelID: "value_4",
						},
					},
				},
			},
			want: []*models.ProductAttribute{
				{
					SalesChannelID: "id_1",
					Values: []models.SalesChannelResource{
						{
							SalesChannelID: "value_1",
						},
					},
				},
				{
					SalesChannelID: "id_3",
					Values: []models.SalesChannelResource{
						{
							SalesChannelID: "value_3",
						},
					},
				},
				{
					SalesChannelID: "id_4",
					Values: []models.SalesChannelResource{
						{
							SalesChannelID: "value_4",
						},
					},
				},
			},
		},
		{
			name: "condition attributes check - 2",
			categoryAttributes: &category.AttributesOutput{
				Attributes: []category.Attribute{
					{
						IsRequired: false,
						ID:         "id_1",
						Type:       consts.AttributeTypeProductProperty,
					},
					{
						IsRequired: false,
						ID:         "id_2",
						Type:       consts.AttributeTypeProductProperty,
						RequirementConditions: []category.AttributeRequirementCondition{
							{
								AttributeID:      "id_3",
								AttributeValueID: "value_3_origin",
							},
						},
					},
					{
						IsRequired: true,
						ID:         "id_3",
						Type:       consts.AttributeTypeProductProperty,
					},
					{
						IsRequired: false,
						ID:         "id_4",
						Type:       consts.AttributeTypeProductProperty,
						RequirementConditions: []category.AttributeRequirementCondition{
							{
								AttributeID:      "id_3",
								AttributeValueID: "value_3",
							},
						},
					},
				},
			},
			productAttributes: []*models.ProductAttribute{
				{
					SalesChannelID: "id_1",
					Values: []models.SalesChannelResource{
						{
							SalesChannelID: "value_1_origin",
						},
					},
				},
				{
					SalesChannelID: "id_3",
					Values: []models.SalesChannelResource{
						{
							SalesChannelID: "value_3_origin",
						},
					},
				},
			},
			inputAttributes: []*models.ProductAttribute{
				{
					SalesChannelID: "id_1",
					Values: []models.SalesChannelResource{
						{
							SalesChannelID: "value_1",
						},
					},
				},
				{
					SalesChannelID: "id_2",
					Values: []models.SalesChannelResource{
						{
							SalesChannelID: "value_2",
						},
					},
				},
				{
					SalesChannelID: "id_3",
					Values: []models.SalesChannelResource{
						{
							SalesChannelID: "value_3",
						},
					},
				},
				{
					SalesChannelID: "id_4",
					Values: []models.SalesChannelResource{
						{
							SalesChannelID: "value_4",
						},
					},
				},
			},
			want: []*models.ProductAttribute{
				{
					SalesChannelID: "id_1",
					Values: []models.SalesChannelResource{
						{
							SalesChannelID: "value_1_origin",
						},
					},
				},
				{
					SalesChannelID: "id_3",
					Values: []models.SalesChannelResource{
						{
							SalesChannelID: "value_3",
						},
					},
				},
				{
					SalesChannelID: "id_4",
					Values: []models.SalesChannelResource{
						{
							SalesChannelID: "value_4",
						},
					},
				},
			},
		},
		{
			name: "condition attributes check - 3",
			categoryAttributes: &category.AttributesOutput{
				Attributes: []category.Attribute{
					{
						IsRequired: false,
						ID:         "id_1",
						Type:       consts.AttributeTypeProductProperty,
						RequirementConditions: []category.AttributeRequirementCondition{
							{
								AttributeID:      "id_3",
								AttributeValueID: "value_3_origin",
							},
						},
					},
					{
						IsRequired: false,
						ID:         "id_2",
						Type:       consts.AttributeTypeProductProperty,
						RequirementConditions: []category.AttributeRequirementCondition{
							{
								AttributeID:      "id_3",
								AttributeValueID: "value_3_origin",
							},
						},
					},
					{
						IsRequired: true,
						ID:         "id_3",
						Type:       consts.AttributeTypeProductProperty,
					},
					{
						IsRequired: false,
						ID:         "id_4",
						Type:       consts.AttributeTypeProductProperty,
						RequirementConditions: []category.AttributeRequirementCondition{
							{
								AttributeID:      "id_3",
								AttributeValueID: "value_3",
							},
						},
					},
				},
			},
			productAttributes: []*models.ProductAttribute{
				{
					SalesChannelID: "id_1",
					Values: []models.SalesChannelResource{
						{
							SalesChannelID: "value_1_origin",
						},
					},
				},
				{
					SalesChannelID: "id_3",
					Values: []models.SalesChannelResource{
						{
							SalesChannelID: "value_3_origin",
						},
					},
				},
			},
			inputAttributes: []*models.ProductAttribute{
				{
					SalesChannelID: "id_2",
					Values: []models.SalesChannelResource{
						{
							SalesChannelID: "value_2",
						},
					},
				},
				{
					SalesChannelID: "id_3",
					Values: []models.SalesChannelResource{
						{
							SalesChannelID: "value_3",
						},
					},
				},
				{
					SalesChannelID: "id_4",
					Values: []models.SalesChannelResource{
						{
							SalesChannelID: "value_4",
						},
					},
				},
			},
			want: []*models.ProductAttribute{
				{
					SalesChannelID: "id_3",
					Values: []models.SalesChannelResource{
						{
							SalesChannelID: "value_3",
						},
					},
				},
				{
					SalesChannelID: "id_4",
					Values: []models.SalesChannelResource{
						{
							SalesChannelID: "value_4",
						},
					},
				},
			},
		},
		{
			name: "condition attributes check - 4",
			categoryAttributes: &category.AttributesOutput{
				Attributes: []category.Attribute{
					{
						IsRequired: false,
						ID:         "id_1",
						Type:       consts.AttributeTypeProductProperty,
						RequirementConditions: []category.AttributeRequirementCondition{
							{
								AttributeID:      "id_3",
								AttributeValueID: "value_3_origin",
							},
						},
					},
					{
						IsRequired: false,
						ID:         "id_2",
						Type:       consts.AttributeTypeProductProperty,
						RequirementConditions: []category.AttributeRequirementCondition{
							{
								AttributeID:      "id_3",
								AttributeValueID: "value_3_origin",
							},
						},
					},
					{
						IsRequired: true,
						ID:         "id_3",
						Type:       consts.AttributeTypeProductProperty,
					},
					{
						IsRequired: false,
						ID:         "id_4",
						Type:       consts.AttributeTypeProductProperty,
						RequirementConditions: []category.AttributeRequirementCondition{
							{
								AttributeID:      "id_3",
								AttributeValueID: "value_3",
							},
						},
					},
					{
						IsRequired: false,
						ID:         "id_5",
						Type:       consts.AttributeTypeProductProperty,
					},
				},
			},
			productAttributes: []*models.ProductAttribute{
				{
					SalesChannelID: "id_1",
					Values: []models.SalesChannelResource{
						{
							SalesChannelID: "value_1_origin",
						},
					},
				},
				{
					SalesChannelID: "id_2",
					Values: []models.SalesChannelResource{
						{
							SalesChannelID: "value_2_origin",
						},
					},
				},
				{
					SalesChannelID: "id_3",
					Values: []models.SalesChannelResource{
						{
							SalesChannelID: "value_3_origin",
						},
					},
				},
				{
					SalesChannelID: "id_5",
					Values: []models.SalesChannelResource{
						{
							SalesChannelID: "value_5_origin",
						},
					},
				},
			},
			inputAttributes: []*models.ProductAttribute{
				{
					SalesChannelID: "id_3",
					Values: []models.SalesChannelResource{
						{
							SalesChannelID: "value_3",
						},
					},
				},
				{
					SalesChannelID: "id_4",
					Values: []models.SalesChannelResource{
						{
							SalesChannelID: "value_4",
						},
					},
				},
			},
			want: []*models.ProductAttribute{
				{
					SalesChannelID: "id_3",
					Values: []models.SalesChannelResource{
						{
							SalesChannelID: "value_3",
						},
					},
				},
				{
					SalesChannelID: "id_4",
					Values: []models.SalesChannelResource{
						{
							SalesChannelID: "value_4",
						},
					},
				},
				{
					SalesChannelID: "id_5",
					Values: []models.SalesChannelResource{
						{
							SalesChannelID: "value_5_origin",
						},
					},
				},
			},
		},
		{
			name: "condition attributes check - 5",
			categoryAttributes: &category.AttributesOutput{
				Attributes: []category.Attribute{
					{
						IsRequired: false,
						ID:         "id_1",
						Type:       consts.AttributeTypeProductProperty,
						RequirementConditions: []category.AttributeRequirementCondition{
							{
								AttributeID:      "id_3",
								AttributeValueID: "value_3_origin",
							},
						},
					},
					{
						IsRequired: false,
						ID:         "id_2",
						Type:       consts.AttributeTypeProductProperty,
						RequirementConditions: []category.AttributeRequirementCondition{
							{
								AttributeID:      "id_3",
								AttributeValueID: "value_3_origin",
							},
						},
					},
					{
						IsRequired: true,
						ID:         "id_3",
						Type:       consts.AttributeTypeProductProperty,
					},
					{
						IsRequired: false,
						ID:         "id_4",
						Type:       consts.AttributeTypeProductProperty,
						RequirementConditions: []category.AttributeRequirementCondition{
							{
								AttributeID:      "id_3",
								AttributeValueID: "value_3",
							},
						},
					},
					{
						IsRequired: false,
						ID:         "id_5",
						Type:       consts.AttributeTypeProductProperty,
					},
					{
						IsRequired: false,
						ID:         "id_6",
						Type:       consts.AttributeTypeProductProperty,
						RequirementConditions: []category.AttributeRequirementCondition{
							{
								AttributeID:      "id_4",
								AttributeValueID: "value_origin_4",
							},
						},
					},
				},
			},
			productAttributes: []*models.ProductAttribute{
				{
					SalesChannelID: "id_1",
					Values: []models.SalesChannelResource{
						{
							SalesChannelID: "value_1_origin",
						},
					},
				},
				{
					SalesChannelID: "id_2",
					Values: []models.SalesChannelResource{
						{
							SalesChannelID: "value_2_origin",
						},
					},
				},
				{
					SalesChannelID: "id_3",
					Values: []models.SalesChannelResource{
						{
							SalesChannelID: "value_3_origin",
						},
					},
				},
				{
					SalesChannelID: "id_4",
					Values: []models.SalesChannelResource{
						{
							SalesChannelID: "value_4_origin",
						},
					},
				},
				{
					SalesChannelID: "id_5",
					Values: []models.SalesChannelResource{
						{
							SalesChannelID: "value_5_origin",
						},
					},
				},
				{
					SalesChannelID: "id_6",
					Values: []models.SalesChannelResource{
						{
							SalesChannelID: "value_6_origin",
						},
					},
				},
			},
			inputAttributes: []*models.ProductAttribute{
				{
					SalesChannelID: "id_3",
					Values: []models.SalesChannelResource{
						{
							SalesChannelID: "value_3",
						},
					},
				},
				{
					SalesChannelID: "id_4",
					Values: []models.SalesChannelResource{
						{
							SalesChannelID: "value_4",
						},
					},
				},
			},
			want: []*models.ProductAttribute{
				{
					SalesChannelID: "id_3",
					Values: []models.SalesChannelResource{
						{
							SalesChannelID: "value_3",
						},
					},
				},
				{
					SalesChannelID: "id_4",
					Values: []models.SalesChannelResource{
						{
							SalesChannelID: "value_4",
						},
					},
				},
				{
					SalesChannelID: "id_5",
					Values: []models.SalesChannelResource{
						{
							SalesChannelID: "value_5_origin",
						},
					},
				},
			},
		},
	}
	for _, test := range tests {
		obj := EditAttributesArgs{
			Attributes: test.inputAttributes,
		}
		t.Run(test.name, func(t *testing.T) {
			listing := &ProductListing{
				Product: models.Product{
					Attributes: test.productAttributes,
				},
			}
			obj.overWriteAttributes(listing, test.categoryAttributes)
			result := listing.Product.Attributes
			resultMap := make(map[string]*models.ProductAttribute)
			for _, cur := range result {
				resultMap[cur.SalesChannelID] = cur
			}
			if len(test.want) != len(result) {
				t.Errorf("want: %v, got: %v", len(test.want), len(result))
			}
			for _, want := range test.want {
				require.Equal(t, resultMap[want.SalesChannelID].Values, want.Values)
			}
		})
	}

}

func TestAutoLinkMappingResult_FilterTargetFailed(t *testing.T) {
	// Initialize test data
	relations := []*ProductListingRelation{
		{
			ProductListingVariantID: "variant_1",
			LinkStatus:              "linked",
		},
		{
			ProductListingVariantID: "variant_2",
			LinkStatus:              "linked",
		},
	}
	autoLinkFailedReason := "some_reason"

	// Initialize autoLinkMappingResult
	result := &autoLinkMappingResult{
		ListingsVariantIDsLinkFailedMapping: map[string]string{
			"variant_2": autoLinkFailedReason,
		},
	}

	// Call the method
	filteredRelations := result.filterTargetFailed(relations, autoLinkFailedReason)

	// Assertions
	require.Equal(t, 1, len(filteredRelations))
	require.Equal(t, "variant_2", filteredRelations[0].ProductListingVariantID)
}

func Test_serviceImpl_EditAttributes(t *testing.T) {
	loadConfig(t)
	// Mock dependencies
	esCli := datastore.Get().ESClient
	err := elasticsearch.CreateTestIndexWithAlias(esCli, "pd_product_listings_2024", searchIndex, "product_listings_mapping.json")
	require.NoError(t, err)

	categoryService := &category.MockCategoryService{}
	categoryService.On("GetCategoryAttributes", mock.Anything, mock.Anything).Return(category.AttributesOutput{
		Attributes: []category.Attribute{
			{
				ID:         "1",
				IsRequired: false,
				Type:       consts.AttributeTypeProductProperty,
			},
			{
				ID:         "2",
				IsRequired: true,
				Type:       consts.AttributeTypeSalesProperty,
			},
		},
	}, nil)
	categoryService.On("GetCategoryRules", mock.Anything, mock.Anything).Return(category.RulesOutput{
		Rule: category.Rule{},
	}, nil)

	service := initService(t)
	require.NotNil(t, service)

	// Define test cases
	tests := []struct {
		name          string
		args          *EditAttributesArgs
		listingModel  productListingDBModel
		mockFunc      func()
		expectedError bool
	}{
		{
			name: "t-0",
			listingModel: productListingDBModel{
				ProductListingID:          uuid.GenerateUUIDV4(),
				State:                     consts.ProductListingProductStateActive,
				OrganizationID:            uuid.GenerateUUIDV4(),
				SalesChannelPlatform:      "tiktok-shop",
				SalesChannelStoreKey:      "test",
				SalesChannelCountryRegion: "US",
				// 2024 year
				CreatedAt: time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC),
			},
			args: &EditAttributesArgs{
				Length: models.ProductVariantShippingSetting{
					Unit:  "cm",
					Value: 10,
				},
				CategorySourceID: "10023",
			},
			mockFunc: func() {
				taskMock := new(task.MockTaskService)
				taskMock.On("Create", mock.Anything, mock.Anything).Return(models.Task{}, nil)
				service.taskService = taskMock
			},
			expectedError: false,
		},
		{
			name: "t-1",
			listingModel: productListingDBModel{
				ProductListingID:          uuid.GenerateUUIDV4(),
				State:                     consts.ProductListingProductStateActive,
				OrganizationID:            uuid.GenerateUUIDV4(),
				SalesChannelPlatform:      "tiktok-shop",
				SalesChannelStoreKey:      "test",
				SalesChannelCountryRegion: "US",
				// 2024 year
				CreatedAt: time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC),
			},
			args: &EditAttributesArgs{
				Length: models.ProductVariantShippingSetting{
					Unit:  "cm",
					Value: 10,
				},
			},
			mockFunc: func() {
				taskMock := new(task.MockTaskService)
				taskMock.On("Create", mock.Anything, mock.Anything).Return(models.Task{}, nil)
				service.taskService = taskMock
			},
			expectedError: false,
		},
		{
			name: "t-2",
			listingModel: productListingDBModel{
				ProductListingID:          uuid.GenerateUUIDV4(),
				State:                     consts.ProductListingProductStateActive,
				OrganizationID:            uuid.GenerateUUIDV4(),
				SalesChannelPlatform:      "tiktok-shop",
				SalesChannelStoreKey:      "test",
				SalesChannelCountryRegion: "US",
				// 2024 year
				CreatedAt: time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC),
			},
			args: &EditAttributesArgs{
				Length: models.ProductVariantShippingSetting{
					Unit:  "cm",
					Value: 10,
				},
			},
			mockFunc: func() {
				taskMock := new(task.MockTaskService)
				taskMock.On("Create", mock.Anything, mock.Anything).Return(models.Task{}, errors.New("task creation error"))
				service.taskService = taskMock
			},
			expectedError: true,
		},
		{
			name: "t-3",
			listingModel: productListingDBModel{
				ProductListingID:          uuid.GenerateUUIDV4(),
				State:                     consts.ProductListingProductStatePending,
				OrganizationID:            uuid.GenerateUUIDV4(),
				SalesChannelPlatform:      "tiktok-shop",
				SalesChannelStoreKey:      "test",
				SalesChannelCountryRegion: "US",
				// 2024 year
				CreatedAt: time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC),
			},
			args: &EditAttributesArgs{
				Length: models.ProductVariantShippingSetting{
					Unit:  "cm",
					Value: 10,
				},
			},
			mockFunc:      func() {},
			expectedError: false,
		},
		{
			name: "t-4",
			listingModel: productListingDBModel{
				ProductListingID:          uuid.GenerateUUIDV4(),
				State:                     consts.ProductListingProductStatePending,
				OrganizationID:            uuid.GenerateUUIDV4(),
				SalesChannelPlatform:      "tiktok-shop",
				SalesChannelStoreKey:      "test",
				SalesChannelCountryRegion: "US",
				// 2024 year
				CreatedAt: time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC),
			},
			args: &EditAttributesArgs{
				Length: models.ProductVariantShippingSetting{
					Unit:  "cm",
					Value: 10,
				},
			},
			mockFunc:      func() {},
			expectedError: false,
		},
		{
			name: "t-5",
			listingModel: productListingDBModel{
				ProductListingID:          uuid.GenerateUUIDV4(),
				State:                     consts.ProductListingProductStateSuspended,
				OrganizationID:            uuid.GenerateUUIDV4(),
				SalesChannelPlatform:      "tiktok-shop",
				SalesChannelStoreKey:      "test",
				SalesChannelCountryRegion: "US",
				// 2024 year
				CreatedAt: time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC),
			},
			args: &EditAttributesArgs{
				Length: models.ProductVariantShippingSetting{
					Unit:  "cm",
					Value: 10,
				},
			},
			mockFunc:      func() {},
			expectedError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {

			tt.mockFunc()
			err = service.repo.plRepo.create(context.Background(), &tt.listingModel)
			require.NoError(t, err)
			// Call the function
			_, err := service.EditAttributes(context.Background(), tt.listingModel.ProductListingID, tt.args)

			// Assert the result
			if tt.expectedError {
				require.Error(t, err)
			} else {
				require.NoError(t, err)
			}
		})
	}
}

func Test_serviceImpl_EditAttributesForActive(t *testing.T) {
	loadConfig(t)
	// Mock dependencies
	esCli := datastore.Get().ESClient
	err := elasticsearch.CreateTestIndexWithAlias(esCli, "pd_product_listings_2024", searchIndex, "product_listings_mapping.json")
	require.NoError(t, err)

	service := initService(t)
	require.NotNil(t, service)

	// Define test cases
	tests := []struct {
		name          string
		args          *EditAttributesArgs
		listingModel  productListingDBModel
		mockFunc      func()
		expectedError bool
	}{
		{
			name: "t-0",
			listingModel: productListingDBModel{
				ProductListingID:          uuid.GenerateUUIDV4(),
				State:                     consts.ProductListingProductStateActive,
				OrganizationID:            uuid.GenerateUUIDV4(),
				SalesChannelPlatform:      "tiktok-shop",
				SalesChannelStoreKey:      "test",
				SalesChannelCountryRegion: "US",
				// 2024 year
				CreatedAt: time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC),
			},
			args: &EditAttributesArgs{
				Length: models.ProductVariantShippingSetting{
					Unit:  "cm",
					Value: 10,
				},
				CategorySourceID: "10023",
			},
			mockFunc: func() {
				categoryService := &category.MockCategoryService{}
				categoryService.On("GetCategoryRules", mock.Anything, mock.Anything).Return(category.RulesOutput{
					Rule: category.Rule{},
				}, nil)
				categoryService.On("GetCategoryAttributes", mock.Anything, mock.Anything).Return(category.AttributesOutput{}, errors.New("category attributes error"))
				service.categoryService = categoryService
			},
			expectedError: true,
		},
		{
			name: "t-1",
			listingModel: productListingDBModel{
				ProductListingID:          uuid.GenerateUUIDV4(),
				State:                     consts.ProductListingProductStateActive,
				OrganizationID:            uuid.GenerateUUIDV4(),
				SalesChannelPlatform:      "tiktok-shop",
				SalesChannelStoreKey:      "test",
				SalesChannelCountryRegion: "US",
				// 2024 year
				CreatedAt: time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC),
			},
			args: &EditAttributesArgs{
				Length: models.ProductVariantShippingSetting{
					Unit:  "cm",
					Value: 10,
				},
				CategorySourceID: "10023",
			},
			mockFunc: func() {
				categoryService := &category.MockCategoryService{}
				categoryService.On("GetCategoryRules", mock.Anything, mock.Anything).Return(category.RulesOutput{}, errors.New("category attributes error"))
				service.categoryService = categoryService
			},
			expectedError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {

			tt.mockFunc()
			err = service.repo.plRepo.create(context.Background(), &tt.listingModel)
			require.NoError(t, err)
			listingModel, err := service.GetByID(context.Background(), tt.listingModel.ProductListingID)
			require.NoError(t, err)
			// Call the function
			_, err = service.editAttributesForActive(context.Background(), listingModel, tt.args)

			// Assert the result
			if tt.expectedError {
				require.Error(t, err)
			} else {
				require.NoError(t, err)
			}

			// add test coverage
			cancelCtx, cf := context.WithTimeout(context.Background(), 1*time.Microsecond)
			cf()
			_, err = service.editAttributesForActive(cancelCtx, listingModel, nil)
			require.Error(t, err)
		})
	}
}

func Test_serviceImpl_acquireListingLock(t *testing.T) {
	loadConfig(t)
	service := initService(t)
	ctx := context.Background()
	ctx, cf := context.WithTimeout(ctx, 1*time.Microsecond)
	cf()
	_, err := service.acquireListingLock(ctx, "id_1")
	require.Error(t, err)
}

func Test_serviceImpl_releaseListingLock_1(t *testing.T) {
	loadConfig(t)
	service := initService(t)
	ctx := context.Background()
	service.releaseListingLock(ctx, nil)
}

func Test_serviceImpl_releaseListingLock_2(t *testing.T) {
	loadConfig(t)
	service := initService(t)
	ctx := context.Background()
	mutex, err := service.acquireListingLock(ctx, "id_1")
	require.NoError(t, err)
	ctx, cf := context.WithTimeout(ctx, 1*time.Microsecond)
	cf()
	service.releaseListingLock(ctx, mutex)
}

func Test_serviceImpl_checkAndFixBlockingPublishListings(t *testing.T) {
	ctx := context.Background()
	// 初始化服务
	loadConfig(t)
	service := initService(t)
	require.NotNil(t, service)

	taskMock := new(task.MockTaskService)
	taskMock.On("Create", mock.Anything, mock.Anything).Return(models.Task{}, nil)
	service.taskService = taskMock

	esCli := datastore.Get().ESClient
	err := elasticsearch.CreateTestIndexWithAlias(esCli, "pd_product_listings_2024", searchIndex, "product_listings_mapping.json")
	require.NoError(t, err)
	err = elasticsearch.CreateTestIndexWithAlias(esCli, "pd_product_listings_2025", searchIndex, "product_listings_mapping.json")
	require.NoError(t, err)

	// 定义测试用例
	tests := []struct {
		name      string
		readyFunc func() string
		checkFunc func(id string) error
		wantErr   bool
	}{
		{
			name: "running -> failed",
			readyFunc: func() string {
				createArg := generateProductListingArgs()
				pl, err := service.Create(ctx, &createArg)
				require.NoError(t, err)
				require.NotNil(t, pl)
				publishArg := pl.Publish
				publishArg.State = consts.PublishStateRunning
				pl, err = service.UpdatePublishState(context.Background(), pl.ID, &publishArg)
				require.NoError(t, err)
				require.NotNil(t, pl)
				esCli.Refresh().Index(searchIndex).Do(context.Background())
				_, err = datastore.Get().SpannerCli.Apply(ctx, []*spanner.Mutation{
					spanner.Update("product_listings", []string{"product_listing_id", "state", "updated_at"}, []interface{}{pl.ID, "active", time.Now().Add(-2 * time.Hour)}),
				})
				require.NoError(t, err)
				return pl.ID
			},
			checkFunc: func(id string) error {
				listing, err := service.GetByID(ctx, id)
				if err != nil {
					return err
				}
				if listing.Publish.State != consts.PublishStateFailed {
					return fmt.Errorf("expected publish state to be %s, got %s", consts.PublishStateFailed, listing.Publish.State)
				}
				return nil
			},
		},
		{
			name: "维持 running 状态, 因为修改时间未达到",
			readyFunc: func() string {
				createArg := generateProductListingArgs()
				pl, err := service.Create(ctx, &createArg)
				require.NoError(t, err)
				require.NotNil(t, pl)
				publishArg := pl.Publish
				publishArg.State = consts.PublishStateRunning
				pl, err = service.UpdatePublishState(context.Background(), pl.ID, &publishArg)
				require.NoError(t, err)
				require.NotNil(t, pl)
				esCli.Refresh().Index(searchIndex).Do(context.Background())
				_, err = datastore.Get().SpannerCli.Apply(ctx, []*spanner.Mutation{
					spanner.Update("product_listings", []string{"product_listing_id", "state", "updated_at"}, []interface{}{pl.ID, "active", time.Now().Add(-time.Second)}),
				})
				require.NoError(t, err)
				return pl.ID
			},
			checkFunc: func(id string) error {
				listing, err := service.GetByID(ctx, id)
				if err != nil {
					return err
				}
				if listing.Publish.State != consts.PublishStateRunning {
					return fmt.Errorf("expected publish state to be %s, got %s", consts.PublishStateRunning, listing.Publish.State)
				}
				return nil
			},
		},
		{
			name: "维持原非 running 状态 - pending",
			readyFunc: func() string {
				createArg := generateProductListingArgs()
				pl, err := service.Create(ctx, &createArg)
				require.NoError(t, err)
				require.NotNil(t, pl)
				publishArg := pl.Publish
				publishArg.State = consts.PublishStatePending
				pl, err = service.UpdatePublishState(context.Background(), pl.ID, &publishArg)
				require.NoError(t, err)
				require.NotNil(t, pl)
				esCli.Refresh().Index(searchIndex).Do(context.Background())
				return pl.ID
			},
			checkFunc: func(id string) error {
				listing, err := service.GetByID(ctx, id)
				if err != nil {
					return err
				}
				if listing.Publish.State != consts.PublishStatePending {
					return fmt.Errorf("expected publish state to be %s, got %s", consts.PublishStatePending, listing.Publish.State)
				}
				return nil
			},
		},
		{
			name: "维持原非 running 状态 - NULL",
			readyFunc: func() string {
				createArg := generateProductListingArgs()
				pl, err := service.Create(ctx, &createArg)
				require.NoError(t, err)
				require.NotNil(t, pl)
				return pl.ID
			},
			checkFunc: func(id string) error {
				listing, err := service.GetByID(ctx, id)
				if err != nil {
					return err
				}
				if listing.Publish.State != "" {
					return fmt.Errorf("expected publish state to be %s, got %s", "NULL", listing.Publish.State)
				}
				return nil
			},
		},
		{
			name: "parent-coverage",
			readyFunc: func() string {
				return ""
			},
			checkFunc: func(id string) error {
				return nil
			},
		},
	}

	// 执行测试用例
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {

			if tt.name == "parent-coverage" {
				err = service.CheckAndFixStatus(ctx, false, 0)
				require.NoError(t, err, "expected no error but got one")
				return
			}

			id := tt.readyFunc()
			service.checkAndFixBlockingPublishListings(context.Background(), false, 0)
			err = tt.checkFunc(id)
			if tt.wantErr {
				require.Error(t, err, "expected an error but got none")
			} else {
				require.NoError(t, err, "expected no error but got one")
			}
		})
	}
}

func Test_serviceImpl_matchProduct(t *testing.T) {
	ctx := context.Background()
	// 初始化服务
	loadConfig(t)
	service := initService(t)
	require.NotNil(t, service)

	mockCalculatorService := new(calculators.MockCalculatorsService)
	mockCalculatorService.On("CalculatePrices", mock.Anything, mock.Anything).Return(
		&calculators.CalculatePricesOutput{
			ProductsCenterProduct: &calculators.CalculatePriceProductsCenterProductOutput{
				Variants: []calculators.CalculatePricesVariantOutput{
					{
						ID:                        "pc_id_variant_1",
						Status:                    consts.CalculateSuccess,
						CalculatedPrice:           decimal.NewFromFloat(18.00),
						CalculatedComparedAtPrice: decimal.NewFromFloat(18.00),
					},
					{
						ID:                        "pc_id_variant_2",
						Status:                    consts.CalculateSuccess,
						CalculatedPrice:           decimal.NewFromFloat(18.00),
						CalculatedComparedAtPrice: decimal.NewFromFloat(18.00),
					},
				},
			},
		}, nil).Once()

	mockClient := products_center.NewMockProductAPICollection(t)

	service.productsCenterClient = &products_center.Client{
		Product: mockClient,
	}
	service.calculatorService = mockCalculatorService

	// 定义测试用例
	tests := []struct {
		name      string
		readyFunc func() string
		checkFunc func(id string) error
		wantErr   bool
	}{
		{
			name: "match success",
			readyFunc: func() string {
				createArg := generateMatchProductListingArgs()
				pl, err := service.Create(ctx, &createArg)
				require.NoError(t, err)
				if pl.matched() {
					t.Fatal("expected listing to not be matched initially")
				}

				mockClient.On("GetByID", mock.Anything, mock.Anything).
					Return(&products_center.Product{
						ID: "pc_id_1",
						Variants: []products_center.Variant{
							{
								ID: "pc_id_variant_1",
								Price: products_center.Price{
									Currency: "USD",
									Amount:   "18",
								},
							},
							{
								ID: "pc_id_variant_2",
								Price: products_center.Price{
									Currency: "USD",
									Amount:   "18",
								},
							},
						},
					}, nil)

				return pl.ID
			},
			checkFunc: func(id string) error {
				listing, err := service.GetByID(ctx, id)
				if err != nil {
					return err
				}
				if !listing.matched() {
					return errors.New("expected listing to be matched but it is not")
				}
				return nil
			},
		},
		{
			name: "already match",
			readyFunc: func() string {
				createArg := generateProductListingArgs()
				pl, err := service.Create(ctx, &createArg)
				require.NoError(t, err)
				return pl.ID
			},
			wantErr: true,
		},
		{
			name: "already match",
			readyFunc: func() string {
				createArg := generateProductListingArgs()
				pl, err := service.Create(ctx, &createArg)
				require.NoError(t, err)
				return pl.ID
			},
			wantErr: true,
		},
		{
			name: "relations not all  sync",
			readyFunc: func() string {
				createArg := generateProductListingArgs()
				createArg.Relations[0].SalesChannel = models.SalesChannel{}
				createArg.Relations[0].SyncStatus = consts.SyncStatusUnsync
				pl, err := service.Create(ctx, &createArg)
				require.NoError(t, err)
				return pl.ID
			},
			wantErr: true,
		},
		{
			name: "relations not link same product",
			readyFunc: func() string {
				createArg := generateProductListingArgs()
				createArg.Relations[0].ProductsCenterVariant.ProductID = "pc_id_2"
				pl, err := service.Create(ctx, &createArg)
				require.NoError(t, err)
				return pl.ID
			},
			wantErr: true,
		},
	}

	// 执行测试用例
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			id := tt.readyFunc()
			_, err := service.MatchProduct(ctx, id)
			if tt.wantErr {
				require.Error(t, err, "expected an error but got none")
				return
			}
			require.NoError(t, err, "expected no error but got one")
			err = tt.checkFunc(id)
			require.NoError(t, err, "expected no error but got one")
		})
	}

}

func Test_serviceImpl_mergePriceResults(t *testing.T) {
	s := &serviceImpl{}

	tests := []struct {
		name   string
		target *calculators.CalculatePricesOutput
		source *calculators.CalculatePricesOutput
		want   *calculators.CalculatePricesOutput
	}{
		{
			name: "source为nil时不合并",
			target: &calculators.CalculatePricesOutput{
				ProductsCenterProduct: &calculators.CalculatePriceProductsCenterProductOutput{
					Variants: []calculators.CalculatePricesVariantOutput{
						{ID: "variant1"},
					},
				},
			},
			source: nil,
			want: &calculators.CalculatePricesOutput{
				ProductsCenterProduct: &calculators.CalculatePriceProductsCenterProductOutput{
					Variants: []calculators.CalculatePricesVariantOutput{
						{ID: "variant1"},
					},
				},
			},
		},
		{
			name: "source.ProductsCenterProduct为nil时不合并",
			target: &calculators.CalculatePricesOutput{
				ProductsCenterProduct: &calculators.CalculatePriceProductsCenterProductOutput{
					Variants: []calculators.CalculatePricesVariantOutput{
						{ID: "variant1"},
					},
				},
			},
			source: &calculators.CalculatePricesOutput{
				ProductsCenterProduct: nil,
			},
			want: &calculators.CalculatePricesOutput{
				ProductsCenterProduct: &calculators.CalculatePriceProductsCenterProductOutput{
					Variants: []calculators.CalculatePricesVariantOutput{
						{ID: "variant1"},
					},
				},
			},
		},
		{
			name: "source.Variants为空时不合并",
			target: &calculators.CalculatePricesOutput{
				ProductsCenterProduct: &calculators.CalculatePriceProductsCenterProductOutput{
					Variants: []calculators.CalculatePricesVariantOutput{
						{ID: "variant1"},
					},
				},
			},
			source: &calculators.CalculatePricesOutput{
				ProductsCenterProduct: &calculators.CalculatePriceProductsCenterProductOutput{
					Variants: []calculators.CalculatePricesVariantOutput{},
				},
			},
			want: &calculators.CalculatePricesOutput{
				ProductsCenterProduct: &calculators.CalculatePriceProductsCenterProductOutput{
					Variants: []calculators.CalculatePricesVariantOutput{
						{ID: "variant1"},
					},
				},
			},
		},
		{
			name: "正常合并variants",
			target: &calculators.CalculatePricesOutput{
				ProductsCenterProduct: &calculators.CalculatePriceProductsCenterProductOutput{
					Variants: []calculators.CalculatePricesVariantOutput{
						{ID: "variant1"},
					},
				},
			},
			source: &calculators.CalculatePricesOutput{
				ProductsCenterProduct: &calculators.CalculatePriceProductsCenterProductOutput{
					Variants: []calculators.CalculatePricesVariantOutput{
						{ID: "variant2"},
						{ID: "variant3"},
					},
				},
			},
			want: &calculators.CalculatePricesOutput{
				ProductsCenterProduct: &calculators.CalculatePriceProductsCenterProductOutput{
					Variants: []calculators.CalculatePricesVariantOutput{
						{ID: "variant1"},
						{ID: "variant2"},
						{ID: "variant3"},
					},
				},
			},
		},
		{
			name: "target.Variants为空时添加source.Variants",
			target: &calculators.CalculatePricesOutput{
				ProductsCenterProduct: &calculators.CalculatePriceProductsCenterProductOutput{
					Variants: []calculators.CalculatePricesVariantOutput{},
				},
			},
			source: &calculators.CalculatePricesOutput{
				ProductsCenterProduct: &calculators.CalculatePriceProductsCenterProductOutput{
					Variants: []calculators.CalculatePricesVariantOutput{
						{ID: "variant1"},
					},
				},
			},
			want: &calculators.CalculatePricesOutput{
				ProductsCenterProduct: &calculators.CalculatePriceProductsCenterProductOutput{
					Variants: []calculators.CalculatePricesVariantOutput{
						{ID: "variant1"},
					},
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建target的深拷贝以避免测试间的相互影响
			targetCopy := &calculators.CalculatePricesOutput{}
			if tt.target != nil {
				*targetCopy = *tt.target
				if tt.target.ProductsCenterProduct != nil {
					targetCopy.ProductsCenterProduct = &calculators.CalculatePriceProductsCenterProductOutput{
						Variants: make([]calculators.CalculatePricesVariantOutput, len(tt.target.ProductsCenterProduct.Variants)),
					}
					copy(targetCopy.ProductsCenterProduct.Variants, tt.target.ProductsCenterProduct.Variants)
				}
			}

			s.mergePriceResults(targetCopy, tt.source)

			require.Equal(t, len(tt.want.ProductsCenterProduct.Variants), len(targetCopy.ProductsCenterProduct.Variants))
			for i, wantVariant := range tt.want.ProductsCenterProduct.Variants {
				require.Equal(t, wantVariant.ID, targetCopy.ProductsCenterProduct.Variants[i].ID)
			}
		})
	}
}
