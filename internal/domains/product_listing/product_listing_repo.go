package product_listing

import (
	"context"

	"cloud.google.com/go/spanner"

	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/gopkg/storage/spannerx"
	"github.com/AfterShip/gopkg/storage/spannerx/sqlbuilder"
	"github.com/AfterShip/gopkg/uuid"

	spanner_util "github.com/AfterShip/pltf-pd-product-listings/internal/utils/spannerx"
)

type productListingRepo struct {
	cli *spannerx.Client
}

func (r *productListingRepo) list(ctx context.Context, arg *repoListArgs) ([]*productListingDBModel, error) {
	query := sqlbuilder.Model(&productListingDBModel{})
	queryParams := map[string]interface{}{}
	if arg.ProductsCenterProductIDs != nil {
		query = query.Where(sqlbuilder.InArray("products_center_product_id", "@products_center_product_ids"))
		queryParams["products_center_product_ids"] = arg.ProductsCenterProductIDs
	}
	if arg.SalesChannelProductIDs != nil {
		query = query.Where(sqlbuilder.InArray("sales_channel_product_id", "@sales_channel_product_ids"))
		query = query.Where(sqlbuilder.NotNull("sales_channel_product_id"))
		queryParams["sales_channel_product_ids"] = arg.SalesChannelProductIDs
	}
	if arg.OrganizationID != "" {
		query = query.Where(sqlbuilder.Eq("organization_id", "@organization_id"))
		queryParams["organization_id"] = arg.OrganizationID
	}
	if arg.SalesChannelStoreKey != "" {
		query = query.Where(sqlbuilder.Eq("sales_channel_store_key", "@sales_channel_store_key"))
		queryParams["sales_channel_store_key"] = arg.SalesChannelStoreKey
	}
	if arg.SalesChannelPlatform != "" {
		query = query.Where(sqlbuilder.Eq("sales_channel_platform", "@sales_channel_platform"))
		queryParams["sales_channel_platform"] = arg.SalesChannelPlatform
	}

	if index := fetchListIndex(arg); index != "" {
		query = query.ForceIndex(index)
	}

	sql := query.
		Limit(arg.Limit).
		Offset((arg.Page - 1) * arg.Limit).
		OrderAsc(tableFieldCreatedAt).
		MustToSQL()

	txn := r.cli.Single()
	defer txn.Close()

	stmt := spanner.Statement{
		SQL:    sql,
		Params: queryParams,
	}
	listings := make([]*productListingDBModel, 0)
	err := txn.Query(ctx, stmt).Do(func(r *spanner.Row) error {
		model := productListingDBModel{}
		defer func() { listings = append(listings, &model) }()
		return r.ToStruct(&model)
	})

	return listings, err
}

func (r *productListingRepo) getByID(ctx context.Context, id string) (productListingDBModel, error) {
	pl := productListingDBModel{}
	cols, err := spanner_util.ParseColumns(&pl)
	if err != nil {
		return pl, err
	}

	txn := r.cli.Single()
	defer txn.Close()

	row, err := txn.ReadRow(ctx, tableProductListing, spanner.Key{id}, cols)
	if err != nil {
		if spannerx.IsNotFoundErr(err) {
			return pl, ErrNotFound
		}
		return pl, err
	}

	return pl, row.ToStruct(&pl)
}

func (r *productListingRepo) listByIDs(ctx context.Context, ids []string) ([]*productListingDBModel, error) {
	sql := sqlbuilder.Model(&productListingDBModel{}).
		Where(sqlbuilder.InArray("product_listing_id", "@product_listing_ids")).
		OrderAsc(tableFieldCreatedAt).
		MustToSQL()

	txn := r.cli.Single()
	defer txn.Close()

	stmt := spanner.Statement{
		SQL:    sql,
		Params: map[string]interface{}{"product_listing_ids": ids},
	}
	listings := make([]*productListingDBModel, 0)
	err := txn.Query(ctx, stmt).Do(func(r *spanner.Row) error {
		model := productListingDBModel{}
		defer func() { listings = append(listings, &model) }()
		return r.ToStruct(&model)
	})

	return listings, err
}

func (r *productListingRepo) create(ctx context.Context, model *productListingDBModel) error {
	mut, err := r.generateCreateMutation(model)
	if err != nil {
		return err
	}
	_, err = r.cli.Apply(ctx, []*spanner.Mutation{mut})
	return err
}

func (r *productListingRepo) generateCreateMutation(model *productListingDBModel) (*spanner.Mutation, error) {
	if model.ProductListingID == "" {
		model.ProductListingID = uuid.GenerateUUIDV4()
	}
	if model.SalesChannelProductID.Empty() || model.SalesChannelProductID.String() == "" {
		model.SalesChannelProductID = types.NullString
	}
	model.PendingDeletedAt = types.NullDatetime
	model.DeletedAt = types.NullDatetime
	model.CreatedAt = spanner.CommitTimestamp
	model.UpdatedAt = spanner.CommitTimestamp
	return spanner.InsertStruct(tableProductListing, model)
}

func (r *productListingRepo) update(ctx context.Context, model *productListingDBModel) error {
	mut, err := r.generateUpdateMutation(model)
	if err != nil {
		return err
	}
	_, err = r.cli.Apply(ctx, []*spanner.Mutation{mut})
	return err
}

func (r *productListingRepo) generateUpdateMutation(model *productListingDBModel) (*spanner.Mutation, error) {
	if model.SalesChannelProductID.Empty() || model.SalesChannelProductID.String() == "" {
		model.SalesChannelProductID = types.NullString
	}
	model.UpdatedAt = spanner.CommitTimestamp
	return spannerx.UpdateStruct(tableProductListing, model)
}

func (r *productListingRepo) delete(ctx context.Context, id string) error {
	mut := r.generateDeleteMutation(id)
	_, err := r.cli.Apply(ctx, []*spanner.Mutation{mut})
	return err
}

func (r *productListingRepo) generateDeleteMutation(id string) *spanner.Mutation {
	data := map[string]interface{}{
		tableFieldProductListingID: id,
		"deleted_at":               types.MakeDatetime(spanner.CommitTimestamp),
		"updated_at":               types.MakeDatetime(spanner.CommitTimestamp),
	}
	return spanner.UpdateMap(tableProductListing, data)
}

func (r *productListingRepo) forceDelete(ctx context.Context, id string) error {
	mut := r.generateForceDeleteMutation(id)
	_, err := r.cli.Apply(ctx, []*spanner.Mutation{mut})
	return err
}

func (r *productListingRepo) generateForceDeleteMutation(id string) *spanner.Mutation {
	return spanner.Delete(tableProductListing, spanner.Key{id})
}

func fetchListIndex(arg *repoListArgs) string {
	if arg.ProductsCenterProductIDs != nil {
		return productsCenterProductIDIndex
	}
	if arg.SalesChannelProductIDs != nil {
		return salesChannelProductIDIndex
	}
	return ""
}
