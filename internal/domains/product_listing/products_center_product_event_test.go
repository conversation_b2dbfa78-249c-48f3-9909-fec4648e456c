package product_listing

import (
	"context"
	"encoding/json"
	"os"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/AfterShip/pltf-pd-product-listings/internal/config"
	"github.com/AfterShip/pltf-pd-product-listings/internal/consts"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
)

func Test_serviceImpl_ProductsCenterProductEvent_match_unsync(t *testing.T) {
	basePath := "./testdata/products_center_product_event/match_unsync/not_auto_sync"
	s := initTestListingUpdateService(t, false, basePath)
	listing := createProductsCenterProductEventTestBaseListing(t, s, basePath+"/base_listing.json")
	lastListing := listing

	// test case 1 add variant
	arg := readProductsCenterProductEventArg(t, basePath+"/add_variant_arg.json")
	listing, err := s.ProductsCenterProductEvent(context.Background(), listing.ID, arg)
	require.NoError(t, err)
	require.NotEmpty(t, listing.ID)
	require.Equal(t, consts.SyncStatusUnsync, listing.SyncStatus)
	require.Equal(t, consts.LinkStatusLinked, listing.LinkStatus)
	compareProductsCenterProduct(t, arg.ProductsCenterProduct, listing.ProductsCenterProduct)
	require.Equal(t, len(lastListing.Product.Variants), len(listing.Product.Variants))
	require.Equal(t, len(lastListing.Relations), len(listing.Relations))
	for i, variant := range lastListing.Product.Variants {
		require.Equal(t, variant.ID, listing.Product.Variants[i].ID)
		require.Equal(t, variant.Title, listing.Product.Variants[i].Title)
		require.Equal(t, variant.Price.Amount, listing.Product.Variants[i].Price.Amount)
		require.Equal(t, variant.CompareAtPrice.Amount, listing.Product.Variants[i].CompareAtPrice.Amount)
	}

	for i, relation := range lastListing.Relations {
		require.Equal(t, relation.ID, listing.Relations[i].ID)
		require.Equal(t, relation.ProductListingVariantID, listing.Relations[i].ProductListingVariantID)
		require.Equal(t, relation.SyncStatus, listing.Relations[i].SyncStatus)
	}
	require.Equal(t, lastListing.Product.Title, listing.Product.Title)
	require.Equal(t, lastListing.Product.Description, listing.Product.Description)
	require.Equal(t, len(lastListing.Product.Media), len(listing.Product.Media))

	// test case 2 delete variant
	arg = readProductsCenterProductEventArg(t, basePath+"/delete_variant_arg.json")
	listing, err = s.ProductsCenterProductEvent(context.Background(), listing.ID, arg)
	require.NoError(t, err)
	require.NotEmpty(t, listing.ID)
	require.Equal(t, consts.SyncStatusUnsync, listing.SyncStatus)
	require.Equal(t, consts.LinkStatusPartialLinked, listing.LinkStatus)
	require.Equal(t, len(lastListing.Product.Variants), len(listing.Product.Variants))
	require.Equal(t, len(lastListing.Relations), len(listing.Relations))
	compareProductsCenterProduct(t, arg.ProductsCenterProduct, listing.ProductsCenterProduct)
	for i, variant := range lastListing.Product.Variants {
		require.Equal(t, variant.ID, listing.Product.Variants[i].ID)
		require.Equal(t, variant.Title, listing.Product.Variants[i].Title)
		require.Equal(t, variant.Price.Amount, listing.Product.Variants[i].Price.Amount)
		require.Equal(t, variant.CompareAtPrice.Amount, listing.Product.Variants[i].CompareAtPrice.Amount)
	}

	for i, relation := range lastListing.Relations {
		if i == 0 {
			require.Equal(t, consts.SyncStatusUnsync, listing.Relations[i].SyncStatus)
			require.Equal(t, consts.LinkStatusUnlink, listing.Relations[i].LinkStatus)
		} else {
			require.Equal(t, consts.SyncStatusUnsync, listing.Relations[i].SyncStatus)
			require.Equal(t, consts.LinkStatusLinked, listing.Relations[i].LinkStatus)
		}
		require.Equal(t, relation.ID, listing.Relations[i].ID)
		require.Equal(t, relation.ProductListingVariantID, listing.Relations[i].ProductListingVariantID)
		require.Equal(t, relation.SyncStatus, listing.Relations[i].SyncStatus)
	}
	require.Equal(t, lastListing.Product.Title, listing.Product.Title)
	require.Equal(t, lastListing.Product.Description, listing.Product.Description)
	require.Equal(t, len(lastListing.Product.Media), len(listing.Product.Media))

	// test case 3 update variant
	arg = readProductsCenterProductEventArg(t, basePath+"/update_variant_arg.json")
	listing, err = s.ProductsCenterProductEvent(context.Background(), listing.ID, arg)
	require.NoError(t, err)
	require.NotEmpty(t, listing.ID)
	require.Equal(t, consts.SyncStatusUnsync, listing.SyncStatus)
	require.Equal(t, consts.LinkStatusPartialLinked, listing.LinkStatus)
	compareProductsCenterProduct(t, arg.ProductsCenterProduct, listing.ProductsCenterProduct)
	require.Equal(t, len(lastListing.Product.Variants), len(listing.Product.Variants))
	require.Equal(t, len(lastListing.Relations), len(listing.Relations))
	for i, variant := range lastListing.Product.Variants {
		require.Equal(t, variant.ID, listing.Product.Variants[i].ID)
		require.Equal(t, variant.Title, listing.Product.Variants[i].Title)
		require.Equal(t, variant.Price.Amount, listing.Product.Variants[i].Price.Amount)
		require.Equal(t, variant.CompareAtPrice.Amount, listing.Product.Variants[i].CompareAtPrice.Amount)
	}

	for i, relation := range lastListing.Relations {
		if i == 0 {
			require.Equal(t, consts.SyncStatusUnsync, listing.Relations[i].SyncStatus)
			require.Equal(t, consts.LinkStatusUnlink, listing.Relations[i].LinkStatus)
		} else {
			require.Equal(t, consts.SyncStatusUnsync, listing.Relations[i].SyncStatus)
			require.Equal(t, consts.LinkStatusLinked, listing.Relations[i].LinkStatus)
		}
		require.Equal(t, relation.ID, listing.Relations[i].ID)
		require.Equal(t, relation.ProductListingVariantID, listing.Relations[i].ProductListingVariantID)
		require.Equal(t, relation.SyncStatus, listing.Relations[i].SyncStatus)
	}
	require.Equal(t, lastListing.Product.Title, listing.Product.Title)
	require.Equal(t, lastListing.Product.Description, listing.Product.Description)
	require.Equal(t, len(lastListing.Product.Media), len(listing.Product.Media))

	// test case 4 update product
	arg = readProductsCenterProductEventArg(t, basePath+"/modify_product_arg.json")
	listing, err = s.ProductsCenterProductEvent(context.Background(), listing.ID, arg)
	require.NoError(t, err)
	require.NotEmpty(t, listing.ID)
	require.Equal(t, consts.SyncStatusUnsync, listing.SyncStatus)
	require.Equal(t, consts.LinkStatusPartialLinked, listing.LinkStatus)
	compareProductsCenterProduct(t, arg.ProductsCenterProduct, listing.ProductsCenterProduct)
	require.Equal(t, len(lastListing.Product.Variants), len(listing.Product.Variants))
	require.Equal(t, len(lastListing.Relations), len(listing.Relations))
	for i, variant := range lastListing.Product.Variants {
		require.Equal(t, variant.ID, listing.Product.Variants[i].ID)
		require.Equal(t, variant.Title, listing.Product.Variants[i].Title)
		require.Equal(t, variant.Price.Amount, listing.Product.Variants[i].Price.Amount)
		require.Equal(t, variant.CompareAtPrice.Amount, listing.Product.Variants[i].CompareAtPrice.Amount)
	}

	for i, relation := range lastListing.Relations {
		if i == 0 {
			require.Equal(t, consts.SyncStatusUnsync, listing.Relations[i].SyncStatus)
			require.Equal(t, consts.LinkStatusUnlink, listing.Relations[i].LinkStatus)
		} else {
			require.Equal(t, consts.SyncStatusUnsync, listing.Relations[i].SyncStatus)
			require.Equal(t, consts.LinkStatusLinked, listing.Relations[i].LinkStatus)
		}
		require.Equal(t, relation.ID, listing.Relations[i].ID)
		require.Equal(t, relation.ProductListingVariantID, listing.Relations[i].ProductListingVariantID)
		require.Equal(t, relation.SyncStatus, listing.Relations[i].SyncStatus)
	}
	require.Equal(t, lastListing.Product.Title, listing.Product.Title)
	require.Equal(t, lastListing.Product.Description, listing.Product.Description)
	require.Equal(t, len(lastListing.Product.Media), len(listing.Product.Media))
}

func Test_serviceImpl_ProductsCenterProductEvent_match_unsync_auto_sync_field(t *testing.T) {
	basePath := "./testdata/products_center_product_event/match_unsync/auto_sync_field"
	s := initTestListingUpdateService(t, false, basePath)
	listing := createProductsCenterProductEventTestBaseListing(t, s, basePath+"/base_listing.json")
	lastListing := listing

	// test case 1 add variant
	arg := readProductsCenterProductEventArg(t, basePath+"/add_variant_arg.json")
	listing, err := s.ProductsCenterProductEvent(context.Background(), listing.ID, arg)
	require.NoError(t, err)
	require.NotEmpty(t, listing.ID)
	require.Equal(t, consts.SyncStatusUnsync, listing.SyncStatus)
	require.Equal(t, consts.LinkStatusLinked, listing.LinkStatus)
	compareProductsCenterProduct(t, arg.ProductsCenterProduct, listing.ProductsCenterProduct)
	require.Equal(t, len(lastListing.Product.Variants), len(listing.Product.Variants))
	require.Equal(t, len(lastListing.Relations), len(listing.Relations))
	for i, variant := range lastListing.Product.Variants {
		require.Equal(t, variant.ID, listing.Product.Variants[i].ID)
		require.Equal(t, variant.Title, listing.Product.Variants[i].Title)
		require.Equal(t, variant.Price.Amount, listing.Product.Variants[i].Price.Amount)
		require.Equal(t, variant.CompareAtPrice.Amount, listing.Product.Variants[i].CompareAtPrice.Amount)
	}

	for i, relation := range lastListing.Relations {
		require.Equal(t, consts.LinkStatusLinked, listing.Relations[i].LinkStatus)
		require.Equal(t, consts.SyncStatusUnsync, listing.Relations[i].SyncStatus)
		require.Equal(t, relation.ID, listing.Relations[i].ID)
		require.Equal(t, relation.ProductListingVariantID, listing.Relations[i].ProductListingVariantID)
		require.Equal(t, relation.SyncStatus, listing.Relations[i].SyncStatus)
	}
	require.Equal(t, arg.Product.Title, listing.Product.Title)
	require.Equal(t, arg.Product.Description, listing.Product.Description)
	require.Equal(t, len(arg.Product.Media), len(listing.Product.Media))

	// test case 2 delete variant
	arg = readProductsCenterProductEventArg(t, basePath+"/delete_variant_arg.json")
	listing, err = s.ProductsCenterProductEvent(context.Background(), listing.ID, arg)
	require.NoError(t, err)
	require.NotEmpty(t, listing.ID)
	require.Equal(t, consts.SyncStatusUnsync, listing.SyncStatus)
	require.Equal(t, consts.LinkStatusPartialLinked, listing.LinkStatus)
	compareProductsCenterProduct(t, arg.ProductsCenterProduct, listing.ProductsCenterProduct)
	require.Equal(t, len(lastListing.Product.Variants), len(listing.Product.Variants))
	require.Equal(t, len(lastListing.Relations), len(listing.Relations))
	for i, variant := range lastListing.Product.Variants {
		require.Equal(t, variant.ID, listing.Product.Variants[i].ID)
		require.Equal(t, variant.Title, listing.Product.Variants[i].Title)
		require.Equal(t, variant.Price.Amount, listing.Product.Variants[i].Price.Amount)
		require.Equal(t, variant.CompareAtPrice.Amount, listing.Product.Variants[i].CompareAtPrice.Amount)
	}

	for i, relation := range lastListing.Relations {
		if i == 0 {
			require.Equal(t, consts.SyncStatusUnsync, listing.Relations[i].SyncStatus)
			require.Equal(t, consts.LinkStatusUnlink, listing.Relations[i].LinkStatus)
		} else {
			require.Equal(t, consts.SyncStatusUnsync, listing.Relations[i].SyncStatus)
			require.Equal(t, consts.LinkStatusLinked, listing.Relations[i].LinkStatus)
		}
		require.Equal(t, relation.ID, listing.Relations[i].ID)
		require.Equal(t, relation.ProductListingVariantID, listing.Relations[i].ProductListingVariantID)
		require.Equal(t, relation.SyncStatus, listing.Relations[i].SyncStatus)
	}
	require.Equal(t, arg.Product.Title, listing.Product.Title)
	require.Equal(t, arg.Product.Description, listing.Product.Description)
	require.Equal(t, len(arg.Product.Media), len(listing.Product.Media))

	// test case 3 update variant
	arg = readProductsCenterProductEventArg(t, basePath+"/update_variant_arg.json")
	listing, err = s.ProductsCenterProductEvent(context.Background(), listing.ID, arg)
	require.NoError(t, err)
	require.NotEmpty(t, listing.ID)
	require.Equal(t, consts.SyncStatusUnsync, listing.SyncStatus)
	require.Equal(t, consts.LinkStatusPartialLinked, listing.LinkStatus)
	compareProductsCenterProduct(t, arg.ProductsCenterProduct, listing.ProductsCenterProduct)
	require.Equal(t, len(lastListing.Product.Variants), len(listing.Product.Variants))
	require.Equal(t, len(lastListing.Relations), len(listing.Relations))
	for i, variant := range lastListing.Product.Variants {
		require.Equal(t, variant.ID, listing.Product.Variants[i].ID)
		require.Equal(t, variant.Title, listing.Product.Variants[i].Title)
		require.Equal(t, variant.Price.Amount, listing.Product.Variants[i].Price.Amount)
		require.Equal(t, variant.CompareAtPrice.Amount, listing.Product.Variants[i].CompareAtPrice.Amount)
	}

	for i, relation := range lastListing.Relations {
		if i == 0 {
			require.Equal(t, consts.SyncStatusUnsync, listing.Relations[i].SyncStatus)
			require.Equal(t, consts.LinkStatusUnlink, listing.Relations[i].LinkStatus)
		} else {
			require.Equal(t, consts.SyncStatusUnsync, listing.Relations[i].SyncStatus)
			require.Equal(t, consts.LinkStatusLinked, listing.Relations[i].LinkStatus)
		}
		require.Equal(t, relation.ID, listing.Relations[i].ID)
		require.Equal(t, relation.ProductListingVariantID, listing.Relations[i].ProductListingVariantID)
		require.Equal(t, relation.SyncStatus, listing.Relations[i].SyncStatus)
	}
	require.Equal(t, arg.Product.Title, listing.Product.Title)
	require.Equal(t, arg.Product.Description, listing.Product.Description)
	require.Equal(t, len(arg.Product.Media), len(listing.Product.Media))

	// test case 4 update product
	arg = readProductsCenterProductEventArg(t, basePath+"/modify_product_arg.json")
	listing, err = s.ProductsCenterProductEvent(context.Background(), listing.ID, arg)
	require.NoError(t, err)
	require.NotEmpty(t, listing.ID)
	require.Equal(t, consts.SyncStatusUnsync, listing.SyncStatus)
	require.Equal(t, consts.LinkStatusPartialLinked, listing.LinkStatus)
	compareProductsCenterProduct(t, arg.ProductsCenterProduct, listing.ProductsCenterProduct)
	require.Equal(t, len(lastListing.Product.Variants), len(listing.Product.Variants))
	require.Equal(t, len(lastListing.Relations), len(listing.Relations))
	for i, variant := range lastListing.Product.Variants {
		require.Equal(t, variant.ID, listing.Product.Variants[i].ID)
		require.Equal(t, variant.Title, listing.Product.Variants[i].Title)
		require.Equal(t, variant.Price.Amount, listing.Product.Variants[i].Price.Amount)
		require.Equal(t, variant.CompareAtPrice.Amount, listing.Product.Variants[i].CompareAtPrice.Amount)
	}

	for i, relation := range lastListing.Relations {
		if i == 0 {
			require.Equal(t, consts.SyncStatusUnsync, listing.Relations[i].SyncStatus)
			require.Equal(t, consts.LinkStatusUnlink, listing.Relations[i].LinkStatus)
		} else {
			require.Equal(t, consts.SyncStatusUnsync, listing.Relations[i].SyncStatus)
			require.Equal(t, consts.LinkStatusLinked, listing.Relations[i].LinkStatus)
		}
		require.Equal(t, relation.ID, listing.Relations[i].ID)
		require.Equal(t, relation.ProductListingVariantID, listing.Relations[i].ProductListingVariantID)
		require.Equal(t, relation.SyncStatus, listing.Relations[i].SyncStatus)
	}
	require.Equal(t, arg.Product.Title, listing.Product.Title)
	require.Equal(t, arg.Product.Description, listing.Product.Description)
	require.Equal(t, len(arg.Product.Media), len(listing.Product.Media))
}

func Test_serviceImpl_ProductsCenterProductEvent_match_unsync_auto_sync_variant(t *testing.T) {
	basePath := "./testdata/products_center_product_event/match_unsync/auto_sync_variant"
	s := initTestListingUpdateService(t, false, basePath)
	listing := createProductsCenterProductEventTestBaseListing(t, s, basePath+"/base_listing.json")
	lastListing := listing

	// test case 1 add variant
	arg := readProductsCenterProductEventArg(t, basePath+"/add_variant_arg.json")
	listing, err := s.ProductsCenterProductEvent(context.Background(), listing.ID, arg)
	require.NoError(t, err)
	require.NotEmpty(t, listing.ID)
	require.Equal(t, consts.SyncStatusUnsync, listing.SyncStatus)
	require.Equal(t, consts.LinkStatusLinked, listing.LinkStatus)
	compareProductsCenterProduct(t, arg.ProductsCenterProduct, listing.ProductsCenterProduct)
	require.Equal(t, len(arg.Product.Variants), len(listing.Product.Variants))
	require.Equal(t, len(arg.Relations), len(listing.Relations))
	for i, variant := range arg.Product.Variants {
		require.Equal(t, variant.ID, listing.Product.Variants[i].ID)
		require.Equal(t, variant.Title, listing.Product.Variants[i].Title)
		require.Equal(t, variant.Price.Amount, listing.Product.Variants[i].Price.Amount)
		require.Equal(t, variant.CompareAtPrice.Amount, listing.Product.Variants[i].CompareAtPrice.Amount)
	}

	for i, relation := range arg.Relations {
		require.Equal(t, relation.ProductListingVariantID, listing.Relations[i].ProductListingVariantID)
		require.Equal(t, consts.SyncStatusUnsync, listing.Relations[i].SyncStatus)
		require.Equal(t, consts.LinkStatusLinked, listing.Relations[i].LinkStatus)
	}
	require.Equal(t, lastListing.Product.Title, listing.Product.Title)
	require.Equal(t, lastListing.Product.Description, listing.Product.Description)
	require.Equal(t, len(lastListing.Product.Media), len(listing.Product.Media))

	// test case 2 delete variant
	arg = readProductsCenterProductEventArg(t, basePath+"/delete_variant_arg.json")
	listing, err = s.ProductsCenterProductEvent(context.Background(), listing.ID, arg)
	require.NoError(t, err)
	require.NotEmpty(t, listing.ID)
	require.Equal(t, consts.SyncStatusUnsync, listing.SyncStatus)
	require.Equal(t, consts.LinkStatusLinked, listing.LinkStatus)
	compareProductsCenterProduct(t, arg.ProductsCenterProduct, listing.ProductsCenterProduct)
	require.Equal(t, len(arg.Product.Variants), len(listing.Product.Variants))
	require.Equal(t, len(arg.Relations), len(listing.Relations))
	for i, variant := range arg.Product.Variants {
		require.Equal(t, variant.ID, listing.Product.Variants[i].ID)
		require.Equal(t, variant.Title, listing.Product.Variants[i].Title)
		require.Equal(t, variant.Price.Amount, listing.Product.Variants[i].Price.Amount)
		require.Equal(t, variant.CompareAtPrice.Amount, listing.Product.Variants[i].CompareAtPrice.Amount)
	}

	for i, relation := range arg.Relations {
		require.Equal(t, relation.ProductListingVariantID, listing.Relations[i].ProductListingVariantID)
		require.Equal(t, consts.SyncStatusUnsync, listing.Relations[i].SyncStatus)
		require.Equal(t, consts.LinkStatusLinked, listing.Relations[i].LinkStatus)
	}
	require.Equal(t, lastListing.Product.Title, listing.Product.Title)
	require.Equal(t, lastListing.Product.Description, listing.Product.Description)
	require.Equal(t, len(lastListing.Product.Media), len(listing.Product.Media))

	// test case 3 update variant
	arg = readProductsCenterProductEventArg(t, basePath+"/update_variant_arg.json")
	listing, err = s.ProductsCenterProductEvent(context.Background(), listing.ID, arg)
	require.NoError(t, err)
	require.NotEmpty(t, listing.ID)
	require.Equal(t, consts.SyncStatusUnsync, listing.SyncStatus)
	require.Equal(t, consts.LinkStatusLinked, listing.LinkStatus)
	compareProductsCenterProduct(t, arg.ProductsCenterProduct, listing.ProductsCenterProduct)
	require.Equal(t, len(arg.Product.Variants), len(listing.Product.Variants))
	require.Equal(t, len(arg.Relations), len(listing.Relations))
	for i, variant := range arg.Product.Variants {
		require.Equal(t, variant.ID, listing.Product.Variants[i].ID)
		require.Equal(t, variant.Title, listing.Product.Variants[i].Title)
		require.Equal(t, variant.Price.Amount, listing.Product.Variants[i].Price.Amount)
		require.Equal(t, variant.CompareAtPrice.Amount, listing.Product.Variants[i].CompareAtPrice.Amount)
	}

	for i, relation := range arg.Relations {
		require.Equal(t, relation.ProductListingVariantID, listing.Relations[i].ProductListingVariantID)
		require.Equal(t, consts.SyncStatusUnsync, listing.Relations[i].SyncStatus)
		require.Equal(t, consts.LinkStatusLinked, listing.Relations[i].LinkStatus)
	}
	require.Equal(t, lastListing.Product.Title, listing.Product.Title)
	require.Equal(t, lastListing.Product.Description, listing.Product.Description)
	require.Equal(t, len(lastListing.Product.Media), len(listing.Product.Media))

	// test case 4 update product
	arg = readProductsCenterProductEventArg(t, basePath+"/modify_product_arg.json")
	listing, err = s.ProductsCenterProductEvent(context.Background(), listing.ID, arg)
	require.NoError(t, err)
	require.NotEmpty(t, listing.ID)
	require.Equal(t, consts.SyncStatusUnsync, listing.SyncStatus)
	require.Equal(t, consts.LinkStatusLinked, listing.LinkStatus)
	compareProductsCenterProduct(t, arg.ProductsCenterProduct, listing.ProductsCenterProduct)
	require.Equal(t, len(arg.Product.Variants), len(listing.Product.Variants))
	require.Equal(t, len(arg.Relations), len(listing.Relations))
	for i, variant := range arg.Product.Variants {
		require.Equal(t, variant.ID, listing.Product.Variants[i].ID)
		require.Equal(t, variant.Title, listing.Product.Variants[i].Title)
		require.Equal(t, variant.Price.Amount, listing.Product.Variants[i].Price.Amount)
		require.Equal(t, variant.CompareAtPrice.Amount, listing.Product.Variants[i].CompareAtPrice.Amount)
	}

	for i, relation := range arg.Relations {
		require.Equal(t, relation.ProductListingVariantID, listing.Relations[i].ProductListingVariantID)
		require.Equal(t, consts.SyncStatusUnsync, listing.Relations[i].SyncStatus)
		require.Equal(t, consts.LinkStatusLinked, listing.Relations[i].LinkStatus)
	}
	require.Equal(t, lastListing.Product.Title, listing.Product.Title)
	require.Equal(t, lastListing.Product.Description, listing.Product.Description)
	require.Equal(t, len(lastListing.Product.Media), len(listing.Product.Media))
}

func Test_serviceImpl_ProductsCenterProductEvent_match_synced(t *testing.T) {
	basePath := "./testdata/products_center_product_event/match_synced/not_auto_sync"
	s := initTestListingUpdateService(t, false, basePath)
	listing := createProductsCenterProductEventTestBaseListing(t, s, basePath+"/base_listing.json")
	lastListing := listing

	// test case 1 add variant
	arg := readProductsCenterProductEventArg(t, basePath+"/add_variant_arg.json")
	listing, err := s.ProductsCenterProductEvent(context.Background(), listing.ID, arg)
	require.NoError(t, err)
	require.NotEmpty(t, listing.ID)
	require.Equal(t, consts.SyncStatusSynced, listing.SyncStatus)
	require.Equal(t, consts.LinkStatusLinked, listing.LinkStatus)
	compareProductsCenterProduct(t, arg.ProductsCenterProduct, listing.ProductsCenterProduct)
	require.Equal(t, len(lastListing.Product.Variants), len(listing.Product.Variants))
	require.Equal(t, len(lastListing.Relations), len(listing.Relations))
	for i, variant := range lastListing.Product.Variants {
		require.Equal(t, variant.ID, listing.Product.Variants[i].ID)
		require.Equal(t, variant.Title, listing.Product.Variants[i].Title)
		require.Equal(t, variant.Price.Amount, listing.Product.Variants[i].Price.Amount)
		require.Equal(t, variant.CompareAtPrice.Amount, listing.Product.Variants[i].CompareAtPrice.Amount)
	}

	for i, relation := range lastListing.Relations {
		require.Equal(t, consts.SyncStatusSynced, listing.Relations[i].SyncStatus)
		require.Equal(t, consts.LinkStatusLinked, listing.Relations[i].LinkStatus)
		require.Equal(t, relation.ID, listing.Relations[i].ID)
		require.Equal(t, relation.ProductListingVariantID, listing.Relations[i].ProductListingVariantID)
		require.Equal(t, relation.SyncStatus, listing.Relations[i].SyncStatus)
		require.Equal(t, relation.LinkStatus, listing.Relations[i].LinkStatus)
	}
	require.Equal(t, lastListing.Product.Title, listing.Product.Title)
	require.Equal(t, lastListing.Product.Description, listing.Product.Description)
	require.Equal(t, len(lastListing.Product.Media), len(listing.Product.Media))

	// test case 2 delete variant
	arg = readProductsCenterProductEventArg(t, basePath+"/delete_variant_arg.json")
	listing, err = s.LinkOrUnlink(context.Background(), &LinkArg{
		ProductListingID: listing.ID,
		LinkedVariants: []LinkedVariantArg{
			{
				ProductListingVariantID:        listing.Product.Variants[0].ID,
				ProductsCenterProductID:        "",
				ProductsCenterProductVariantID: "",
			},
		},
	})
	require.NoError(t, err)
	listing, err = s.ProductsCenterProductEvent(context.Background(), listing.ID, arg)
	require.NoError(t, err)
	require.NotEmpty(t, listing.ID)
	require.Equal(t, consts.SyncStatusSynced, listing.SyncStatus)
	require.Equal(t, consts.LinkStatusPartialLinked, listing.LinkStatus)
	compareProductsCenterProduct(t, arg.ProductsCenterProduct, listing.ProductsCenterProduct)
	require.Equal(t, len(lastListing.Product.Variants), len(listing.Product.Variants))
	require.Equal(t, len(lastListing.Relations), len(listing.Relations))
	for i, variant := range lastListing.Product.Variants {
		require.Equal(t, variant.ID, listing.Product.Variants[i].ID)
		require.Equal(t, variant.Title, listing.Product.Variants[i].Title)
		require.Equal(t, variant.Price.Amount, listing.Product.Variants[i].Price.Amount)
		require.Equal(t, variant.CompareAtPrice.Amount, listing.Product.Variants[i].CompareAtPrice.Amount)
	}

	for i, relation := range lastListing.Relations {
		if i == 0 {
			require.Equal(t, consts.SyncStatusSynced, listing.Relations[i].SyncStatus)
			require.Equal(t, consts.LinkStatusUnlink, listing.Relations[i].LinkStatus)
		} else {
			require.Equal(t, consts.SyncStatusSynced, listing.Relations[i].SyncStatus)
			require.Equal(t, consts.LinkStatusLinked, listing.Relations[i].LinkStatus)
		}
		require.Equal(t, relation.ID, listing.Relations[i].ID)
		require.Equal(t, relation.ProductListingVariantID, listing.Relations[i].ProductListingVariantID)
		require.Equal(t, relation.SyncStatus, listing.Relations[i].SyncStatus)
	}
	require.Equal(t, lastListing.Product.Title, listing.Product.Title)
	require.Equal(t, lastListing.Product.Description, listing.Product.Description)
	require.Equal(t, len(lastListing.Product.Media), len(listing.Product.Media))

	// test case 3 update variant
	arg = readProductsCenterProductEventArg(t, basePath+"/update_variant_arg.json")
	listing, err = s.ProductsCenterProductEvent(context.Background(), listing.ID, arg)
	require.NoError(t, err)
	require.NotEmpty(t, listing.ID)
	require.Equal(t, consts.SyncStatusSynced, listing.SyncStatus)
	require.Equal(t, consts.LinkStatusPartialLinked, listing.LinkStatus)
	compareProductsCenterProduct(t, arg.ProductsCenterProduct, listing.ProductsCenterProduct)
	require.Equal(t, len(lastListing.Product.Variants), len(listing.Product.Variants))
	require.Equal(t, len(lastListing.Relations), len(listing.Relations))
	for i, variant := range lastListing.Product.Variants {
		require.Equal(t, variant.ID, listing.Product.Variants[i].ID)
		require.Equal(t, variant.Title, listing.Product.Variants[i].Title)
		require.Equal(t, variant.Price.Amount, listing.Product.Variants[i].Price.Amount)
		require.Equal(t, variant.CompareAtPrice.Amount, listing.Product.Variants[i].CompareAtPrice.Amount)
	}

	for i, relation := range lastListing.Relations {
		if i == 0 {
			require.Equal(t, consts.SyncStatusSynced, listing.Relations[i].SyncStatus)
			require.Equal(t, consts.LinkStatusUnlink, listing.Relations[i].LinkStatus)
		} else {
			require.Equal(t, consts.SyncStatusSynced, listing.Relations[i].SyncStatus)
			require.Equal(t, consts.LinkStatusLinked, listing.Relations[i].LinkStatus)
		}
		require.Equal(t, relation.ID, listing.Relations[i].ID)
		require.Equal(t, relation.ProductListingVariantID, listing.Relations[i].ProductListingVariantID)
		require.Equal(t, relation.SyncStatus, listing.Relations[i].SyncStatus)
	}
	require.Equal(t, lastListing.Product.Title, listing.Product.Title)
	require.Equal(t, lastListing.Product.Description, listing.Product.Description)
	require.Equal(t, len(lastListing.Product.Media), len(listing.Product.Media))

	// test case 4 update product
	arg = readProductsCenterProductEventArg(t, basePath+"/modify_product_arg.json")
	listing, err = s.ProductsCenterProductEvent(context.Background(), listing.ID, arg)
	require.NoError(t, err)
	require.NotEmpty(t, listing.ID)
	require.Equal(t, consts.SyncStatusSynced, listing.SyncStatus)
	require.Equal(t, consts.LinkStatusPartialLinked, listing.LinkStatus)
	compareProductsCenterProduct(t, arg.ProductsCenterProduct, listing.ProductsCenterProduct)
	require.Equal(t, len(lastListing.Product.Variants), len(listing.Product.Variants))
	require.Equal(t, len(lastListing.Relations), len(listing.Relations))
	for i, variant := range lastListing.Product.Variants {
		require.Equal(t, variant.ID, listing.Product.Variants[i].ID)
		require.Equal(t, variant.Title, listing.Product.Variants[i].Title)
		require.Equal(t, variant.Price.Amount, listing.Product.Variants[i].Price.Amount)
		require.Equal(t, variant.CompareAtPrice.Amount, listing.Product.Variants[i].CompareAtPrice.Amount)
	}

	for i, relation := range lastListing.Relations {
		if i == 0 {
			require.Equal(t, consts.SyncStatusSynced, listing.Relations[i].SyncStatus)
			require.Equal(t, consts.LinkStatusUnlink, listing.Relations[i].LinkStatus)
		} else {
			require.Equal(t, consts.SyncStatusSynced, listing.Relations[i].SyncStatus)
			require.Equal(t, consts.LinkStatusLinked, listing.Relations[i].LinkStatus)
		}
		require.Equal(t, relation.ID, listing.Relations[i].ID)
		require.Equal(t, relation.ProductListingVariantID, listing.Relations[i].ProductListingVariantID)
		require.Equal(t, relation.SyncStatus, listing.Relations[i].SyncStatus)
	}
	require.Equal(t, lastListing.Product.Title, listing.Product.Title)
	require.Equal(t, lastListing.Product.Description, listing.Product.Description)
	require.Equal(t, len(lastListing.Product.Media), len(listing.Product.Media))
}

// nolint:maintidx
func Test_serviceImpl_ProductsCenterProductEvent_match_synced_auto_sync_field(t *testing.T) {
	basePath := "./testdata/products_center_product_event/match_synced/auto_sync_field"
	s := initTestListingUpdateService(t, false, basePath)
	listing := createProductsCenterProductEventTestBaseListing(t, s, basePath+"/base_listing.json")
	lastListing := listing

	// test case 1 add variant
	arg := readProductsCenterProductEventArg(t, basePath+"/add_variant_arg.json")
	listing, err := s.ProductsCenterProductEvent(context.Background(), listing.ID, arg)
	require.NoError(t, err)
	require.NotEmpty(t, listing.ID)
	require.Equal(t, consts.SyncStatusSynced, listing.SyncStatus)
	require.Equal(t, consts.LinkStatusLinked, listing.LinkStatus)
	compareProductsCenterProduct(t, arg.ProductsCenterProduct, listing.ProductsCenterProduct)
	require.Equal(t, len(lastListing.Product.Variants), len(listing.Product.Variants))
	require.Equal(t, len(lastListing.Relations), len(listing.Relations))
	for i, variant := range lastListing.Product.Variants {
		require.Equal(t, variant.ID, listing.Product.Variants[i].ID)
		require.Equal(t, variant.Title, listing.Product.Variants[i].Title)
		require.Equal(t, variant.Price.Amount, listing.Product.Variants[i].Price.Amount)
		require.Equal(t, variant.CompareAtPrice.Amount, listing.Product.Variants[i].CompareAtPrice.Amount)
	}

	for i, relation := range lastListing.Relations {
		require.Equal(t, consts.SyncStatusSynced, listing.Relations[i].SyncStatus)
		require.Equal(t, consts.LinkStatusLinked, listing.Relations[i].LinkStatus)
		require.Equal(t, relation.ID, listing.Relations[i].ID)
		require.Equal(t, relation.ProductListingVariantID, listing.Relations[i].ProductListingVariantID)
		require.Equal(t, relation.SyncStatus, listing.Relations[i].SyncStatus)
		require.Equal(t, relation.LinkStatus, listing.Relations[i].LinkStatus)
	}
	require.Equal(t, lastListing.Product.Title, listing.Product.Title)
	require.Equal(t, lastListing.Product.Description, listing.Product.Description)
	require.Equal(t, len(lastListing.Product.Media), len(listing.Product.Media))

	// test case 2 delete variant
	arg = readProductsCenterProductEventArg(t, basePath+"/delete_variant_arg.json")
	listing, err = s.LinkOrUnlink(context.Background(), &LinkArg{
		ProductListingID: listing.ID,
		LinkedVariants: []LinkedVariantArg{
			{
				ProductListingVariantID:        listing.Product.Variants[0].ID,
				ProductsCenterProductID:        "",
				ProductsCenterProductVariantID: "",
			},
		},
	})
	require.NoError(t, err)
	listing, err = s.ProductsCenterProductEvent(context.Background(), listing.ID, arg)
	require.NoError(t, err)
	require.NotEmpty(t, listing.ID)
	require.Equal(t, consts.SyncStatusSynced, listing.SyncStatus)
	require.Equal(t, consts.LinkStatusPartialLinked, listing.LinkStatus)
	compareProductsCenterProduct(t, arg.ProductsCenterProduct, listing.ProductsCenterProduct)
	require.Equal(t, len(lastListing.Product.Variants), len(listing.Product.Variants))
	require.Equal(t, len(lastListing.Relations), len(listing.Relations))
	for i, variant := range lastListing.Product.Variants {
		require.Equal(t, variant.ID, listing.Product.Variants[i].ID)
		require.Equal(t, variant.Title, listing.Product.Variants[i].Title)
		require.Equal(t, variant.Price.Amount, listing.Product.Variants[i].Price.Amount)
		require.Equal(t, variant.CompareAtPrice.Amount, listing.Product.Variants[i].CompareAtPrice.Amount)
	}

	for i, relation := range lastListing.Relations {
		if i == 0 {
			require.Equal(t, consts.SyncStatusSynced, listing.Relations[i].SyncStatus)
			require.Equal(t, consts.LinkStatusUnlink, listing.Relations[i].LinkStatus)
		} else {
			require.Equal(t, consts.SyncStatusSynced, listing.Relations[i].SyncStatus)
			require.Equal(t, consts.LinkStatusLinked, listing.Relations[i].LinkStatus)
		}
		require.Equal(t, relation.ID, listing.Relations[i].ID)
		require.Equal(t, relation.ProductListingVariantID, listing.Relations[i].ProductListingVariantID)
		require.Equal(t, relation.SyncStatus, listing.Relations[i].SyncStatus)
	}
	require.Equal(t, lastListing.Product.Title, listing.Product.Title)
	require.Equal(t, lastListing.Product.Description, listing.Product.Description)
	require.Equal(t, len(lastListing.Product.Media), len(listing.Product.Media))

	// test case 3 update variant
	arg = readProductsCenterProductEventArg(t, basePath+"/update_variant_arg.json")
	listing, err = s.ProductsCenterProductEvent(context.Background(), listing.ID, arg)
	require.NoError(t, err)
	require.NotEmpty(t, listing.ID)
	require.Equal(t, consts.SyncStatusSynced, listing.SyncStatus)
	require.Equal(t, consts.LinkStatusPartialLinked, listing.LinkStatus)
	compareProductsCenterProduct(t, arg.ProductsCenterProduct, listing.ProductsCenterProduct)
	require.Equal(t, len(lastListing.Product.Variants), len(listing.Product.Variants))
	require.Equal(t, len(lastListing.Relations), len(listing.Relations))
	for i, variant := range lastListing.Product.Variants {
		require.Equal(t, variant.ID, listing.Product.Variants[i].ID)
		require.Equal(t, variant.Title, listing.Product.Variants[i].Title)
		require.Equal(t, variant.Price.Amount, listing.Product.Variants[i].Price.Amount)
		require.Equal(t, variant.CompareAtPrice.Amount, listing.Product.Variants[i].CompareAtPrice.Amount)
	}

	for i, relation := range lastListing.Relations {
		if i == 0 {
			require.Equal(t, consts.SyncStatusSynced, listing.Relations[i].SyncStatus)
			require.Equal(t, consts.LinkStatusUnlink, listing.Relations[i].LinkStatus)
		} else {
			require.Equal(t, consts.SyncStatusSynced, listing.Relations[i].SyncStatus)
			require.Equal(t, consts.LinkStatusLinked, listing.Relations[i].LinkStatus)
		}
		require.Equal(t, relation.ID, listing.Relations[i].ID)
		require.Equal(t, relation.ProductListingVariantID, listing.Relations[i].ProductListingVariantID)
		require.Equal(t, relation.SyncStatus, listing.Relations[i].SyncStatus)
	}
	require.Equal(t, lastListing.Product.Title, listing.Product.Title)
	require.Equal(t, lastListing.Product.Description, listing.Product.Description)
	require.Equal(t, len(lastListing.Product.Media), len(listing.Product.Media))

	// test case 4 update product
	arg = readProductsCenterProductEventArg(t, basePath+"/modify_product_arg.json")
	listing, err = s.ProductsCenterProductEvent(context.Background(), listing.ID, arg)
	require.NoError(t, err)
	require.NotEmpty(t, listing.ID)
	require.Equal(t, consts.SyncStatusSynced, listing.SyncStatus)
	require.Equal(t, consts.LinkStatusPartialLinked, listing.LinkStatus)
	compareProductsCenterProduct(t, arg.ProductsCenterProduct, listing.ProductsCenterProduct)
	require.Equal(t, len(lastListing.Product.Variants), len(listing.Product.Variants))
	require.Equal(t, len(lastListing.Relations), len(listing.Relations))
	for i, variant := range lastListing.Product.Variants {
		require.Equal(t, variant.ID, listing.Product.Variants[i].ID)
		require.Equal(t, variant.Title, listing.Product.Variants[i].Title)
		require.Equal(t, variant.Price.Amount, listing.Product.Variants[i].Price.Amount)
		require.Equal(t, variant.CompareAtPrice.Amount, listing.Product.Variants[i].CompareAtPrice.Amount)
	}

	for i, relation := range lastListing.Relations {
		if i == 0 {
			require.Equal(t, consts.SyncStatusSynced, listing.Relations[i].SyncStatus)
			require.Equal(t, consts.LinkStatusUnlink, listing.Relations[i].LinkStatus)
		} else {
			require.Equal(t, consts.SyncStatusSynced, listing.Relations[i].SyncStatus)
			require.Equal(t, consts.LinkStatusLinked, listing.Relations[i].LinkStatus)
		}
		require.Equal(t, relation.ID, listing.Relations[i].ID)
		require.Equal(t, relation.ProductListingVariantID, listing.Relations[i].ProductListingVariantID)
		require.Equal(t, relation.SyncStatus, listing.Relations[i].SyncStatus)
	}
	require.Equal(t, lastListing.Product.Title, listing.Product.Title)
	require.Equal(t, lastListing.Product.Description, listing.Product.Description)
	require.Equal(t, len(lastListing.Product.Media), len(listing.Product.Media))

	lastAuditVersion, err := s.latestAuditVersion(context.Background(), listing.ID)
	require.NoError(t, err)
	require.NotEmpty(t, lastAuditVersion)
	require.Equal(t, lastAuditVersion.ProductListing.ID, listing.ID)
	require.Equal(t, lastAuditVersion.ProductListing.Product.Title, arg.Product.Title)
	require.Equal(t, lastAuditVersion.ProductListing.Product.Description, arg.Product.Description)
	require.Equal(t, len(lastAuditVersion.ProductListing.Product.Media), len(arg.Product.Media))
	require.Equal(t, len(lastAuditVersion.ProductListing.Product.Variants), len(lastListing.Product.Variants))
	for i, variant := range lastAuditVersion.ProductListing.Product.Variants {
		require.Equal(t, variant.ID, lastListing.Product.Variants[i].ID)
		require.Equal(t, variant.Title, lastListing.Product.Variants[i].Title)
		require.Equal(t, variant.Price.Amount, lastListing.Product.Variants[i].Price.Amount)
		require.Equal(t, variant.CompareAtPrice.Amount, lastListing.Product.Variants[i].CompareAtPrice.Amount)
	}
	for i, relation := range lastAuditVersion.ProductListing.Relations {
		if i == 0 {
			require.Equal(t, consts.SyncStatusSynced, relation.SyncStatus)
			require.Equal(t, consts.LinkStatusUnlink, relation.LinkStatus)
		} else {
			require.Equal(t, consts.SyncStatusSynced, relation.SyncStatus)
			require.Equal(t, consts.LinkStatusLinked, relation.LinkStatus)
		}
		require.Equal(t, relation.ProductListingVariantID, lastListing.Relations[i].ProductListingVariantID)
	}
}

func Test_serviceImpl_ProductsCenterProductEvent_match_synced_auto_sync_variant_add_variant(t *testing.T) {
	basePath := "./testdata/products_center_product_event/match_synced/auto_sync_variant/add_variant"
	s := initTestListingUpdateService(t, false, basePath)
	listing := createProductsCenterProductEventTestBaseListing(t, s, basePath+"/base_listing.json")
	lastListing := listing

	// test case 1 add variant
	arg := readProductsCenterProductEventArg(t, basePath+"/arg.json")
	listing, err := s.ProductsCenterProductEvent(context.Background(), listing.ID, arg)
	require.NoError(t, err)
	require.NotEmpty(t, listing.ID)
	require.Equal(t, consts.SyncStatusPartialSynced, listing.SyncStatus)
	require.Equal(t, consts.LinkStatusLinked, listing.LinkStatus)
	compareProductsCenterProduct(t, arg.ProductsCenterProduct, listing.ProductsCenterProduct)
	require.Equal(t, len(arg.Product.Variants), len(listing.Product.Variants))
	require.Equal(t, len(arg.Relations), len(listing.Relations))
	for i, variant := range arg.Product.Variants {
		require.Equal(t, variant.ID, listing.Product.Variants[i].ID)
		require.Equal(t, variant.Title, listing.Product.Variants[i].Title)
		require.Equal(t, variant.Price.Amount, listing.Product.Variants[i].Price.Amount)
		require.Equal(t, variant.CompareAtPrice.Amount, listing.Product.Variants[i].CompareAtPrice.Amount)
	}

	for i, relation := range listing.Relations {
		if i == 2 {
			require.Equal(t, consts.SyncStatusUnsync, relation.SyncStatus)
		} else {
			require.Equal(t, consts.SyncStatusSynced, relation.SyncStatus)
		}
		require.Equal(t, consts.LinkStatusLinked, relation.LinkStatus)
		require.Equal(t, relation.ProductListingVariantID, arg.Product.Variants[i].ID)
		require.Equal(t, relation.ProductsCenterVariant.ProductID, arg.Relations[i].ProductsCenterVariant.ProductID)
		require.Equal(t, relation.ProductsCenterVariant.ConnectorProductID, arg.Relations[i].ProductsCenterVariant.ConnectorProductID)
		require.Equal(t, relation.ProductsCenterVariant.Source.StoreKey, arg.Relations[i].ProductsCenterVariant.Source.StoreKey)
		require.Equal(t, relation.ProductsCenterVariant.Source.Platform, arg.Relations[i].ProductsCenterVariant.Source.Platform)
		require.Equal(t, relation.ProductsCenterVariant.Source.ID, arg.Relations[i].ProductsCenterVariant.Source.ID)
		require.Equal(t, relation.ProductsCenterVariant.Source.ProductID, arg.Relations[i].ProductsCenterVariant.Source.ProductID)
		require.Equal(t, relation.ProductsCenterVariant.Source.Sku, arg.Relations[i].ProductsCenterVariant.Source.Sku)
	}
	require.Equal(t, lastListing.Product.Title, listing.Product.Title)
	require.Equal(t, lastListing.Product.Description, listing.Product.Description)
	require.Equal(t, len(lastListing.Product.Media), len(listing.Product.Media))

	lastAuditVersion, err := s.latestAuditVersion(context.Background(), listing.ID)
	require.NoError(t, err)
	require.NotEmpty(t, lastAuditVersion)
	require.Equal(t, lastAuditVersion.ProductListing.ID, listing.ID)
	require.Equal(t, lastAuditVersion.ProductListing.Product.Title, lastListing.Product.Title)
	require.Equal(t, lastAuditVersion.ProductListing.Product.Description, lastListing.Product.Description)
	require.Equal(t, len(lastAuditVersion.ProductListing.Product.Media), len(lastListing.Product.Media))
	require.Equal(t, len(arg.Product.Variants), len(lastAuditVersion.ProductListing.Product.Variants))
	require.Equal(t, len(arg.Relations), len(lastAuditVersion.ProductListing.Relations))
	for i, variant := range lastAuditVersion.ProductListing.Product.Variants {
		require.Equal(t, variant.ID, arg.Product.Variants[i].ID)
		require.Equal(t, variant.Title, arg.Product.Variants[i].Title)
		require.Equal(t, variant.Price.Amount, arg.Product.Variants[i].Price.Amount)
		require.Equal(t, variant.CompareAtPrice.Amount, arg.Product.Variants[i].CompareAtPrice.Amount)
	}

	for i, relation := range lastAuditVersion.ProductListing.Relations {
		if i == 2 {
			require.Equal(t, consts.SyncStatusUnsync, relation.SyncStatus)
		} else {
			require.Equal(t, consts.SyncStatusSynced, relation.SyncStatus)
		}
		require.Equal(t, consts.LinkStatusLinked, relation.LinkStatus)
		require.Equal(t, relation.ProductListingVariantID, arg.Product.Variants[i].ID)
		// require.Equal(t, relation.ProductsCenterVariant.ProductID, arg.Relations[i].ProductsCenterVariant.ProductID)
		// require.Equal(t, relation.ProductsCenterVariant.ConnectorProductID, arg.Relations[i].ProductsCenterVariant.ConnectorProductID)
		// require.Equal(t, relation.ProductsCenterVariant.Source.StoreKey, arg.Relations[i].ProductsCenterVariant.Source.StoreKey)
		// require.Equal(t, relation.ProductsCenterVariant.Source.Platform, arg.Relations[i].ProductsCenterVariant.Source.Platform)
		// require.Equal(t, relation.ProductsCenterVariant.Source.ID, arg.Relations[i].ProductsCenterVariant.Source.ID)
		// require.Equal(t, relation.ProductsCenterVariant.Source.ProductID, arg.Relations[i].ProductsCenterVariant.Source.ProductID)
		// require.Equal(t, relation.ProductsCenterVariant.Source.Sku, arg.Relations[i].ProductsCenterVariant.Source.Sku)
	}
}

func Test_serviceImpl_ProductsCenterProductEvent_match_synced_auto_sync_variant_delete_variant(t *testing.T) {
	basePath := "./testdata/products_center_product_event/match_synced/auto_sync_variant/delete_variant"
	s := initTestListingUpdateService(t, false, basePath)
	listing := createProductsCenterProductEventTestBaseListing(t, s, basePath+"/base_listing.json")
	lastListing := listing

	// test case 1 delete variant
	arg := readProductsCenterProductEventArg(t, basePath+"/arg.json")
	listing, err := s.LinkOrUnlink(context.Background(), &LinkArg{
		ProductListingID: listing.ID,
		LinkedVariants: []LinkedVariantArg{
			{
				ProductListingVariantID:        listing.Product.Variants[0].ID,
				ProductsCenterProductID:        "",
				ProductsCenterProductVariantID: "",
			},
		},
	})
	require.NoError(t, err)
	listing, err = s.ProductsCenterProductEvent(context.Background(), listing.ID, arg)
	require.NoError(t, err)
	require.NotEmpty(t, listing.ID)
	require.Equal(t, consts.SyncStatusSynced, listing.SyncStatus)
	require.Equal(t, consts.LinkStatusPartialLinked, listing.LinkStatus)
	compareProductsCenterProduct(t, arg.ProductsCenterProduct, listing.ProductsCenterProduct)
	require.Equal(t, len(lastListing.Product.Variants), len(listing.Product.Variants))
	require.Equal(t, len(lastListing.Relations), len(listing.Relations))
	for i, variant := range lastListing.Product.Variants {
		require.Equal(t, variant.ID, listing.Product.Variants[i].ID)
		require.Equal(t, variant.Title, listing.Product.Variants[i].Title)
		require.Equal(t, variant.Price.Amount, listing.Product.Variants[i].Price.Amount)
		require.Equal(t, variant.CompareAtPrice.Amount, listing.Product.Variants[i].CompareAtPrice.Amount)
	}

	for i, relation := range listing.Relations {
		require.Equal(t, consts.SyncStatusSynced, relation.SyncStatus)
		if i == 0 {
			require.Equal(t, consts.LinkStatusUnlink, relation.LinkStatus)
		} else {
			require.Equal(t, consts.LinkStatusLinked, relation.LinkStatus)
			require.Equal(t, relation.ProductsCenterVariant.ProductID, lastListing.Relations[i].ProductsCenterVariant.ProductID)
			require.Equal(t, relation.ProductsCenterVariant.ConnectorProductID, lastListing.Relations[i].ProductsCenterVariant.ConnectorProductID)
			require.Equal(t, relation.ProductsCenterVariant.Source.StoreKey, lastListing.Relations[i].ProductsCenterVariant.Source.StoreKey)
			require.Equal(t, relation.ProductsCenterVariant.Source.Platform, lastListing.Relations[i].ProductsCenterVariant.Source.Platform)
			require.Equal(t, relation.ProductsCenterVariant.Source.ID, lastListing.Relations[i].ProductsCenterVariant.Source.ID)
			require.Equal(t, relation.ProductsCenterVariant.Source.ProductID, lastListing.Relations[i].ProductsCenterVariant.Source.ProductID)
			require.Equal(t, relation.ProductsCenterVariant.Source.Sku, lastListing.Relations[i].ProductsCenterVariant.Source.Sku)
		}
		require.Equal(t, relation.ProductListingVariantID, lastListing.Product.Variants[i].ID)
	}
	require.Equal(t, lastListing.Product.Title, listing.Product.Title)
	require.Equal(t, lastListing.Product.Description, listing.Product.Description)
	require.Equal(t, len(lastListing.Product.Media), len(listing.Product.Media))

	lastAuditVersion, err := s.latestAuditVersion(context.Background(), listing.ID)
	require.NoError(t, err)
	require.NotEmpty(t, lastAuditVersion)
	require.Equal(t, lastAuditVersion.ProductListing.ID, listing.ID)
	require.Equal(t, lastAuditVersion.ProductListing.Product.Title, lastListing.Product.Title)
	require.Equal(t, lastAuditVersion.ProductListing.Product.Description, lastListing.Product.Description)
	require.Equal(t, len(lastAuditVersion.ProductListing.Product.Media), len(lastListing.Product.Media))
	require.Equal(t, len(arg.Product.Variants), len(lastAuditVersion.ProductListing.Product.Variants))
	require.Equal(t, len(arg.Relations), len(lastAuditVersion.ProductListing.Relations))
	require.Equal(t, len(arg.Product.Variants), len(lastAuditVersion.ProductListing.Product.Variants))
	for i, variant := range lastAuditVersion.ProductListing.Product.Variants {
		require.Equal(t, variant.ID, arg.Product.Variants[i].ID)
		require.Equal(t, variant.Title, arg.Product.Variants[i].Title)
		require.Equal(t, variant.Price.Amount, arg.Product.Variants[i].Price.Amount)
		require.Equal(t, variant.CompareAtPrice.Amount, arg.Product.Variants[i].CompareAtPrice.Amount)
	}

	require.Equal(t, len(arg.Relations), len(lastAuditVersion.ProductListing.Relations))
	for i, relation := range lastAuditVersion.ProductListing.Relations {
		require.Equal(t, consts.SyncStatusSynced, relation.SyncStatus)
		require.Equal(t, consts.LinkStatusLinked, relation.LinkStatus)
		require.Equal(t, relation.ProductListingVariantID, arg.Product.Variants[i].ID)
		// require.Equal(t, relation.ProductsCenterVariant.ProductID, arg.Relations[i].ProductsCenterVariant.ProductID)
		// require.Equal(t, relation.ProductsCenterVariant.ConnectorProductID, arg.Relations[i].ProductsCenterVariant.ConnectorProductID)
		// require.Equal(t, relation.ProductsCenterVariant.Source.StoreKey, arg.Relations[i].ProductsCenterVariant.Source.StoreKey)
		// require.Equal(t, relation.ProductsCenterVariant.Source.Platform, arg.Relations[i].ProductsCenterVariant.Source.Platform)
		// require.Equal(t, relation.ProductsCenterVariant.Source.ID, arg.Relations[i].ProductsCenterVariant.Source.ID)
		// require.Equal(t, relation.ProductsCenterVariant.Source.ProductID, arg.Relations[i].ProductsCenterVariant.Source.ProductID)
		// require.Equal(t, relation.ProductsCenterVariant.Source.Sku, arg.Relations[i].ProductsCenterVariant.Source.Sku)
	}
}

func Test_serviceImpl_ProductsCenterProductEvent_match_synced_auto_sync_variant_update_variant(t *testing.T) {
	basePath := "./testdata/products_center_product_event/match_synced/auto_sync_variant/update_variant"
	s := initTestListingUpdateService(t, false, basePath)
	listing := createProductsCenterProductEventTestBaseListing(t, s, basePath+"/base_listing.json")
	lastListing := listing

	// test case 1 update variant
	arg := readProductsCenterProductEventArg(t, basePath+"/arg.json")
	listing, err := s.ProductsCenterProductEvent(context.Background(), listing.ID, arg)
	require.NoError(t, err)
	require.NotEmpty(t, listing.ID)
	require.Equal(t, consts.SyncStatusSynced, listing.SyncStatus)
	require.Equal(t, consts.LinkStatusLinked, listing.LinkStatus)
	compareProductsCenterProduct(t, arg.ProductsCenterProduct, listing.ProductsCenterProduct)
	require.Equal(t, len(lastListing.Product.Variants), len(listing.Product.Variants))
	require.Equal(t, len(lastListing.Relations), len(listing.Relations))
	for i, variant := range lastListing.Product.Variants {
		require.Equal(t, variant.ID, listing.Product.Variants[i].ID)
		require.Equal(t, variant.Title, listing.Product.Variants[i].Title)
		require.Equal(t, variant.Price.Amount, listing.Product.Variants[i].Price.Amount)
		require.Equal(t, variant.CompareAtPrice.Amount, listing.Product.Variants[i].CompareAtPrice.Amount)
	}

	for i, relation := range listing.Relations {
		require.Equal(t, consts.SyncStatusSynced, relation.SyncStatus)
		require.Equal(t, consts.LinkStatusLinked, relation.LinkStatus)
		require.Equal(t, relation.ProductListingVariantID, lastListing.Product.Variants[i].ID)
		require.Equal(t, relation.ProductsCenterVariant.ProductID, lastListing.Relations[i].ProductsCenterVariant.ProductID)
		require.Equal(t, relation.ProductsCenterVariant.ConnectorProductID, lastListing.Relations[i].ProductsCenterVariant.ConnectorProductID)
		require.Equal(t, relation.ProductsCenterVariant.Source.StoreKey, lastListing.Relations[i].ProductsCenterVariant.Source.StoreKey)
		require.Equal(t, relation.ProductsCenterVariant.Source.Platform, lastListing.Relations[i].ProductsCenterVariant.Source.Platform)
		require.Equal(t, relation.ProductsCenterVariant.Source.ID, lastListing.Relations[i].ProductsCenterVariant.Source.ID)
		require.Equal(t, relation.ProductsCenterVariant.Source.ProductID, lastListing.Relations[i].ProductsCenterVariant.Source.ProductID)
		require.Equal(t, relation.ProductsCenterVariant.Source.Sku, lastListing.Relations[i].ProductsCenterVariant.Source.Sku)
	}
	require.Equal(t, lastListing.Product.Title, listing.Product.Title)
	require.Equal(t, lastListing.Product.Description, listing.Product.Description)
	require.Equal(t, len(lastListing.Product.Media), len(listing.Product.Media))

	lastAuditVersion, err := s.latestAuditVersion(context.Background(), listing.ID)
	require.NoError(t, err)
	require.NotEmpty(t, lastAuditVersion)
	require.Equal(t, lastAuditVersion.ProductListing.ID, listing.ID)
	require.Equal(t, lastAuditVersion.ProductListing.Product.Title, lastListing.Product.Title)
	require.Equal(t, lastAuditVersion.ProductListing.Product.Description, lastListing.Product.Description)
	require.Equal(t, len(lastAuditVersion.ProductListing.Product.Media), len(lastListing.Product.Media))
	require.Equal(t, len(lastListing.Product.Variants), len(lastAuditVersion.ProductListing.Product.Variants))
	require.Equal(t, len(lastListing.Relations), len(lastAuditVersion.ProductListing.Relations))
	for i, variant := range lastAuditVersion.ProductListing.Product.Variants {
		require.Equal(t, variant.ID, arg.Product.Variants[i].ID)
		require.Equal(t, variant.Title, arg.Product.Variants[i].Title)
		require.Equal(t, variant.Price.Amount, arg.Product.Variants[i].Price.Amount)
		require.Equal(t, variant.CompareAtPrice.Amount, arg.Product.Variants[i].CompareAtPrice.Amount)
	}

	for i, relation := range lastAuditVersion.ProductListing.Relations {
		require.Equal(t, consts.SyncStatusSynced, relation.SyncStatus)
		require.Equal(t, consts.LinkStatusLinked, relation.LinkStatus)
		require.Equal(t, relation.ProductListingVariantID, arg.Product.Variants[i].ID)
		// require.Equal(t, relation.ProductsCenterVariant.ProductID, arg.Relations[i].ProductsCenterVariant.ProductID)
		// require.Equal(t, relation.ProductsCenterVariant.ConnectorProductID, arg.Relations[i].ProductsCenterVariant.ConnectorProductID)
		// require.Equal(t, relation.ProductsCenterVariant.Source.StoreKey, arg.Relations[i].ProductsCenterVariant.Source.StoreKey)
		// require.Equal(t, relation.ProductsCenterVariant.Source.Platform, arg.Relations[i].ProductsCenterVariant.Source.Platform)
		// require.Equal(t, relation.ProductsCenterVariant.Source.ID, arg.Relations[i].ProductsCenterVariant.Source.ID)
		// require.Equal(t, relation.ProductsCenterVariant.Source.ProductID, arg.Relations[i].ProductsCenterVariant.Source.ProductID)
		// require.Equal(t, relation.ProductsCenterVariant.Source.Sku, arg.Relations[i].ProductsCenterVariant.Source.Sku)
	}

	// same params do not publish
	// arg = readProductsCenterProductEventArg(t, basePath+"/arg.json")
	// listing, err = s.ProductsCenterProductEvent(context.Background(), listing.ID, arg)
	// require.NoError(t, err)
	// newLastAuditVersion, err := s.latestAuditVersion(context.Background(), listing.ID)
	// require.NoError(t, err)
	// require.NotEqual(t, newLastAuditVersion.ID, lastAuditVersion.ID)
}

func compareProductsCenterProduct(t *testing.T, expect, actual ProductsCenterProduct) {
	require.NotEmpty(t, actual.ConnectorProductID)
	require.NotEmpty(t, actual.Source.StoreKey)
	require.NotEmpty(t, actual.Source.Platform)
	require.NotEmpty(t, actual.Source.ID)
	require.Equal(t, expect.ID, actual.ID)
}

func readProductsCenterProductEventArg(t *testing.T, path string) *ProductsCenterProductEventArg {
	bytes, err := os.ReadFile(path)
	require.NoError(t, err)
	outputs := ProductsCenterProductEventArg{}
	err = json.Unmarshal(bytes, &outputs)
	require.NoError(t, err)
	return &outputs
}

func createProductsCenterProductEventTestBaseListing(t *testing.T, s *serviceImpl, path string) ProductListing {
	bytes, err := os.ReadFile(path)
	require.NoError(t, err)
	createArg := ProductListingArgs{}
	err = json.Unmarshal(bytes, &createArg)
	require.NoError(t, err)
	ctx := context.Background()
	listing, err := s.Create(ctx, &createArg)
	require.NoError(t, err)
	require.NotEmpty(t, listing.ID)
	s.esRepo.RefreshESIndex(ctx)
	return listing
}

func Test_productsCenterEventModel_overWritePrice(t *testing.T) {
	// 准备测试数据
	tests := []struct {
		name                string
		unionSetting        *storeProductListingSetting
		lastListingVariants []*models.ProductVariant
		argVariants         []*models.ProductVariant
		inputVariants       []*models.ProductVariant
		expectedVariants    []*models.ProductVariant
	}{
		{
			name: "同步开启时应使用最新的价格",
			unionSetting: &storeProductListingSetting{
				productListingSetting: SyncSettings{
					PriceSyncSetting: PriceSyncSetting{
						Preference: consts.SettingPreferenceCustomized,
						Customized: models.PriceSync{
							AutoSync: consts.StateEnabled,
						},
					},
				},
			},
			lastListingVariants: []*models.ProductVariant{
				{
					ID: "v1",
					Price: models.ProductVariantPrice{
						Amount:   "100",
						Currency: "USD",
					},
					CompareAtPrice: models.ProductVariantPrice{
						Amount:   "120",
						Currency: "USD",
					},
				},
			},
			argVariants: []*models.ProductVariant{
				{
					ID: "v1",
					Price: models.ProductVariantPrice{
						Amount:   "150",
						Currency: "USD",
					},
					CompareAtPrice: models.ProductVariantPrice{
						Amount:   "180",
						Currency: "USD",
					},
				},
			},
			inputVariants: []*models.ProductVariant{
				{
					ID: "v1",
					Price: models.ProductVariantPrice{
						Amount:   "100",
						Currency: "USD",
					},
					CompareAtPrice: models.ProductVariantPrice{
						Amount:   "120",
						Currency: "USD",
					},
				},
			},
			expectedVariants: []*models.ProductVariant{
				{
					ID: "v1",
					Price: models.ProductVariantPrice{
						Amount:   "150",
						Currency: "USD",
					},
					CompareAtPrice: models.ProductVariantPrice{
						Amount:   "180",
						Currency: "USD",
					},
				},
			},
		},
		{
			name: "同步关闭时应使用旧的价格",
			unionSetting: &storeProductListingSetting{
				productListingSetting: SyncSettings{
					PriceSyncSetting: PriceSyncSetting{
						Preference: consts.SettingPreferenceCustomized,
						Customized: models.PriceSync{
							AutoSync: consts.StateEnabled,
						},
					},
				},
			},
			lastListingVariants: []*models.ProductVariant{
				{
					ID: "v1",
					Price: models.ProductVariantPrice{
						Amount:   "100",
						Currency: "USD",
					},
					CompareAtPrice: models.ProductVariantPrice{
						Amount:   "120",
						Currency: "USD",
					},
				},
			},
			argVariants: []*models.ProductVariant{
				{
					ID: "v1",
					Price: models.ProductVariantPrice{
						Amount:   "150",
						Currency: "USD",
					},
					CompareAtPrice: models.ProductVariantPrice{
						Amount:   "180",
						Currency: "USD",
					},
				},
			},
			inputVariants: []*models.ProductVariant{
				{
					ID: "v1",
					Price: models.ProductVariantPrice{
						Amount:   "150",
						Currency: "USD",
					},
					CompareAtPrice: models.ProductVariantPrice{
						Amount:   "180",
						Currency: "USD",
					},
				},
			},
			expectedVariants: []*models.ProductVariant{
				{
					ID: "v1",
					Price: models.ProductVariantPrice{
						Amount:   "150",
						Currency: "USD",
					},
					CompareAtPrice: models.ProductVariantPrice{
						Amount:   "180",
						Currency: "USD",
					},
				},
			},
		},
		{
			name: "处理多个变体",
			unionSetting: &storeProductListingSetting{
				productListingSetting: SyncSettings{
					PriceSyncSetting: PriceSyncSetting{
						Preference: consts.SettingPreferenceCustomized,
						Customized: models.PriceSync{
							AutoSync: consts.StateEnabled,
						},
					},
				},
			},
			lastListingVariants: []*models.ProductVariant{
				{
					ID: "v1",
					Price: models.ProductVariantPrice{
						Amount:   "100",
						Currency: "USD",
					},
					CompareAtPrice: models.ProductVariantPrice{
						Amount:   "120",
						Currency: "USD",
					},
				},
				{
					ID: "v2",
					Price: models.ProductVariantPrice{
						Amount:   "200",
						Currency: "USD",
					},
					CompareAtPrice: models.ProductVariantPrice{
						Amount:   "220",
						Currency: "USD",
					},
				},
			},
			argVariants: []*models.ProductVariant{
				{
					ID: "v1",
					Price: models.ProductVariantPrice{
						Amount:   "150",
						Currency: "USD",
					},
					CompareAtPrice: models.ProductVariantPrice{
						Amount:   "180",
						Currency: "USD",
					},
				},
				{
					ID: "v2",
					Price: models.ProductVariantPrice{
						Amount:   "250",
						Currency: "USD",
					},
					CompareAtPrice: models.ProductVariantPrice{
						Amount:   "280",
						Currency: "USD",
					},
				},
			},
			inputVariants: []*models.ProductVariant{
				{
					ID: "v1",
					Price: models.ProductVariantPrice{
						Amount:   "100",
						Currency: "USD",
					},
					CompareAtPrice: models.ProductVariantPrice{
						Amount:   "120",
						Currency: "USD",
					},
				},
				{
					ID: "v2",
					Price: models.ProductVariantPrice{
						Amount:   "200",
						Currency: "USD",
					},
					CompareAtPrice: models.ProductVariantPrice{
						Amount:   "220",
						Currency: "USD",
					},
				},
			},
			expectedVariants: []*models.ProductVariant{
				{
					ID: "v1",
					Price: models.ProductVariantPrice{
						Amount:   "150",
						Currency: "USD",
					},
					CompareAtPrice: models.ProductVariantPrice{
						Amount:   "180",
						Currency: "USD",
					},
				},
				{
					ID: "v2",
					Price: models.ProductVariantPrice{
						Amount:   "250",
						Currency: "USD",
					},
					CompareAtPrice: models.ProductVariantPrice{
						Amount:   "280",
						Currency: "USD",
					},
				},
			},
		},
		{
			name: "变体ID不匹配时不改变价格",
			unionSetting: &storeProductListingSetting{
				productListingSetting: SyncSettings{
					PriceSyncSetting: PriceSyncSetting{
						Preference: consts.SettingPreferenceCustomized,
						Customized: models.PriceSync{
							AutoSync: consts.StateEnabled,
						},
					},
				},
			},
			lastListingVariants: []*models.ProductVariant{
				{
					ID: "v1",
					Price: models.ProductVariantPrice{
						Amount:   "100",
						Currency: "USD",
					},
					CompareAtPrice: models.ProductVariantPrice{
						Amount:   "120",
						Currency: "USD",
					},
				},
			},
			argVariants: []*models.ProductVariant{
				{
					ID: "v2", // 不同的ID
					Price: models.ProductVariantPrice{
						Amount:   "150",
						Currency: "USD",
					},
					CompareAtPrice: models.ProductVariantPrice{
						Amount:   "180",
						Currency: "USD",
					},
				},
			},
			inputVariants: []*models.ProductVariant{
				{
					ID: "v1",
					Price: models.ProductVariantPrice{
						Amount:   "100",
						Currency: "USD",
					},
					CompareAtPrice: models.ProductVariantPrice{
						Amount:   "120",
						Currency: "USD",
					},
				},
			},
			expectedVariants: []*models.ProductVariant{
				{
					ID: "v1",
					Price: models.ProductVariantPrice{
						Amount:   "100",
						Currency: "USD",
					},
					CompareAtPrice: models.ProductVariantPrice{
						Amount:   "120",
						Currency: "USD",
					},
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建模型
			m := &productsCenterEventModel{
				unionSetting: tt.unionSetting,
				lastListing: &ProductListing{
					Product: models.Product{
						Variants: tt.lastListingVariants,
					},
				},
				arg: &ProductListingArgs{
					Product: models.Product{
						Variants: tt.argVariants,
					},
				},
			}

			// 复制输入变体以避免修改原始数据
			testVariants := make([]*models.ProductVariant, len(tt.inputVariants))
			for i, v := range tt.inputVariants {
				copied := *v
				testVariants[i] = &copied
			}

			// 执行测试
			m.overWritePrice(testVariants)

			// 验证结果
			assert.Equal(t, len(tt.expectedVariants), len(testVariants), "变体数量应相同")
			for i, expected := range tt.expectedVariants {
				assert.Equal(t, expected.ID, testVariants[i].ID, "变体ID应相同")
				assert.Equal(t, expected.Price.Amount, testVariants[i].Price.Amount,
					"价格应相同，期望 %s，实际 %s", expected.Price.Amount, testVariants[i].Price.Amount)
				assert.Equal(t, expected.Price.Currency, testVariants[i].Price.Currency, "货币应相同")
				assert.Equal(t, expected.CompareAtPrice.Amount, testVariants[i].CompareAtPrice.Amount,
					"比较价格应相同，期望 %s，实际 %s", expected.CompareAtPrice.Amount, testVariants[i].CompareAtPrice.Amount)
				assert.Equal(t, expected.CompareAtPrice.Currency, testVariants[i].CompareAtPrice.Currency, "比较价格货币应相同")
			}
		})
	}
}

func Test_shouldRemoveVariantsImagesForEvent(t *testing.T) {
	tests := []struct {
		name    string
		orgID   string
		product models.Product
		config  *config.TikTokSyncConfig
		want    bool
	}{
		{
			name:  "配置为nil时应返回false",
			orgID: "org123",
			product: models.Product{
				Variants: []*models.ProductVariant{
					{
						Options: []*models.ProductVariantOption{
							{Name: "颜色", Value: "红色"},
						},
					},
				},
			},
			config: nil,
			want:   false,
		},
		{
			name:  "移除条件为空时应返回false",
			orgID: "org123",
			product: models.Product{
				Variants: []*models.ProductVariant{
					{
						Options: []*models.ProductVariantOption{
							{Name: "颜色", Value: "红色"},
						},
					},
				},
			},
			config: &config.TikTokSyncConfig{
				SkuImageRemovalConditions: []config.SkuImageRemovalConditions{},
			},
			want: false,
		},
		{
			name:  "产品没有变体时应返回false",
			orgID: "org123",
			product: models.Product{
				Variants: []*models.ProductVariant{},
			},
			config: &config.TikTokSyncConfig{
				SkuImageRemovalConditions: []config.SkuImageRemovalConditions{
					{
						OrganizationID:  "org123",
						RequiredOptions: []string{"颜色", "尺寸"},
					},
				},
			},
			want: false,
		},
		{
			name:  "产品没有选项时应返回false",
			orgID: "org123",
			product: models.Product{
				Variants: []*models.ProductVariant{
					{
						Options: []*models.ProductVariantOption{},
					},
				},
			},
			config: &config.TikTokSyncConfig{
				SkuImageRemovalConditions: []config.SkuImageRemovalConditions{
					{
						OrganizationID:  "org123",
						RequiredOptions: []string{"颜色", "尺寸"},
					},
				},
			},
			want: false,
		},
		{
			name:  "产品选项名为空时应返回false",
			orgID: "org123",
			product: models.Product{
				Variants: []*models.ProductVariant{
					{
						Options: []*models.ProductVariantOption{
							{Name: "", Value: "红色"},
						},
					},
				},
			},
			config: &config.TikTokSyncConfig{
				SkuImageRemovalConditions: []config.SkuImageRemovalConditions{
					{
						OrganizationID:  "org123",
						RequiredOptions: []string{"颜色", "尺寸"},
					},
				},
			},
			want: false,
		},
		{
			name:  "从多个变体中收集选项名并匹配条件",
			orgID: "org123",
			product: models.Product{
				Variants: []*models.ProductVariant{
					{
						Options: []*models.ProductVariantOption{
							{Name: "颜色", Value: "红色"},
						},
					},
					{
						Options: []*models.ProductVariantOption{
							{Name: "颜色", Value: "蓝色"},
							{Name: "尺寸", Value: "M"},
						},
					},
				},
			},
			config: &config.TikTokSyncConfig{
				SkuImageRemovalConditions: []config.SkuImageRemovalConditions{
					{
						OrganizationID:  "org123",
						RequiredOptions: []string{"颜色", "尺寸"},
					},
				},
			},
			want: true,
		},
		{
			name:  "重复的选项名应该去重",
			orgID: "org123",
			product: models.Product{
				Variants: []*models.ProductVariant{
					{
						Options: []*models.ProductVariantOption{
							{Name: "颜色", Value: "红色"},
							{Name: "颜色", Value: "蓝色"},
						},
					},
					{
						Options: []*models.ProductVariantOption{
							{Name: "尺寸", Value: "M"},
						},
					},
				},
			},
			config: &config.TikTokSyncConfig{
				SkuImageRemovalConditions: []config.SkuImageRemovalConditions{
					{
						OrganizationID:  "org123",
						RequiredOptions: []string{"颜色", "尺寸"},
					},
				},
			},
			want: true,
		},
		{
			name:  "混合空选项名和有效选项名",
			orgID: "org123",
			product: models.Product{
				Variants: []*models.ProductVariant{
					{
						Options: []*models.ProductVariantOption{
							{Name: "", Value: "红色"},
							{Name: "颜色", Value: "蓝色"},
						},
					},
					{
						Options: []*models.ProductVariantOption{
							{Name: "尺寸", Value: "M"},
						},
					},
				},
			},
			config: &config.TikTokSyncConfig{
				SkuImageRemovalConditions: []config.SkuImageRemovalConditions{
					{
						OrganizationID:  "org123",
						RequiredOptions: []string{"颜色", "尺寸"},
					},
				},
			},
			want: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := shouldRemoveVariantsImagesForEvent(tt.orgID, tt.product, tt.config)
			assert.Equal(t, tt.want, got)
		})
	}
}
