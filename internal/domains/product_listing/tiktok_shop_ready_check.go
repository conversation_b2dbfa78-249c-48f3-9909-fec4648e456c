package product_listing

import (
	"fmt"
	"regexp"

	errors_sdk "github.com/AfterShip/connectors-errors-sdk-go"
	"github.com/AfterShip/gopkg/facility/set"
	"github.com/AfterShip/pltf-pd-product-listings/internal/consts"

	"github.com/AfterShip/pltf-pd-product-listings/internal/config"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/category"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
	"github.com/AfterShip/pltf-pd-product-listings/internal/utils/image"
)

const (
	readyCheckPackageWeight           = "package_weight"
	readyCheckPackageDimensionsLength = "package_dimensions_length"
	readyCheckPackageDimensionsWidth  = "package_dimensions_width"
	readyCheckPackageDimensionsHeight = "package_dimensions_height"
	readyCheckMainImage               = "main_image"
	readyCheckDescription             = "description"
	readyCheckTitle                   = "title"
	readyCheckImage                   = "image"
	readyCheckPrice                   = "price"
	readyCheckVariant                 = "variant"
	readyCheckCertifications          = "certifications"
	readyCheckSizeChart               = "size_chart"
	readyCheckCategory                = "category"
	readyCheckAttributes              = "attributes"
	readyCheckCompliance              = "compliance"
)

type tiktokShopReadyCheckModel struct {
	product             *models.Product
	rules               *category.RulesOutput
	attributes          *category.AttributesOutput
	regionConfig        *config.ChannelRegionConfig
	ttsCdnDomainRegExps []*regexp.Regexp
}

func newTikTokShopReadyCheckModel(product *models.Product, rules *category.RulesOutput,
	attributes *category.AttributesOutput, regionConfig *config.ChannelRegionConfig, ttsCdnDomainRegExps []*regexp.Regexp) *tiktokShopReadyCheckModel {
	return &tiktokShopReadyCheckModel{
		product:             product,
		rules:               rules,
		attributes:          attributes,
		regionConfig:        regionConfig,
		ttsCdnDomainRegExps: ttsCdnDomainRegExps,
	}
}

// nolint:gocyclo
func (model *tiktokShopReadyCheckModel) Check() []tiktokReadyCheck {
	result := make([]tiktokReadyCheck, 0)

	if len(model.product.Variants) == 0 {
		result = append(result, &variantIsEmpty{})
	} else {
		result = append(result, model.packageCheck()...)
		result = append(result, model.variantMediaCheck()...)
		result = append(result, model.variantOptionAndSKUCheck()...)
		result = append(result, model.variantPriceCheck()...)
		result = append(result, model.variantMediaEmptyCheck()...)
	}

	if len(model.product.Categories) == 0 {
		result = append(result, &categoryIsEmpty{})
	} else {
		// product certification check
		result = append(result, model.productCertificationCheck()...)
		// size chart check
		result = append(result, model.sizeChartCheck()...)
		// attributes
		result = append(result, model.attributesCheck()...)
	}

	result = append(result, model.complianceCheck()...)

	result = append(result, model.mediaCheck()...)
	result = append(result, model.descriptionCheck()...)
	result = append(result, model.titleCheck()...)

	return result
}

func (model *tiktokShopReadyCheckModel) productCertificationCheck() []tiktokReadyCheck {
	if model.rules == nil {
		return nil
	}
	if model.rules.ExternalCategoryID == "" {
		return nil
	}

	result := make([]tiktokReadyCheck, 0)
	// build product certification map
	productCertificationMap := make(map[string]*models.ProductCertification)
	for _, certification := range model.product.Certifications {
		productCertificationMap[certification.SalesChannelID] = certification
	}

	// 收集所有必填的 product certification
	requiredCertifications := model.collectRequiredCertifications()

	// check category rule product certification
	for _, certification := range requiredCertifications {
		// check certification
		if certificationInfo, ok := productCertificationMap[certification.ExternalId]; !ok ||
			(len(certificationInfo.Files) == 0 && len(certificationInfo.Images) == 0) {
			result = append(result, &certificationsIsEmpty{
				Name: certification.ExternalName,
			})
		}
	}

	return result
}

func (model *tiktokShopReadyCheckModel) collectRequiredCertifications() []*category.ProductCertification {
	if model.rules == nil || len(model.rules.Rule.ProductCertifications) == 0 {
		return nil
	}

	result := make([]*category.ProductCertification, 0)

	productAttributes := make(map[string][]models.SalesChannelResource)
	for _, attr := range model.product.Attributes {
		if attr == nil || attr.SalesChannelID == "" {
			continue
		}
		productAttributes[attr.SalesChannelID] = attr.Values
	}

	for _, cur := range model.rules.Rule.ProductCertifications {
		if cur.IsRequired {
			result = append(result, cur)
			continue
		}
		// condition 判断
		conditionRequired := false
		for _, condition := range cur.RequirementConditions {
			values, ok := productAttributes[condition.AttributeID]
			if !ok {
				continue
			}
			for _, value := range values {
				if value.SalesChannelID == condition.AttributeValueID {
					conditionRequired = true
					break
				}
			}
		}
		if conditionRequired {
			result = append(result, cur)
		}
	}

	return result
}

func (model *tiktokShopReadyCheckModel) sizeChartCheck() []tiktokReadyCheck {
	if model.rules == nil {
		return nil
	}
	if model.rules.ExternalCategoryID == "" {
		return nil
	}

	if model.rules.Rule.SizeChart == nil || !model.rules.Rule.SizeChart.IsRequired {
		return nil
	}
	if model.product.SizeChartFilled() {
		return nil
	}

	result := make([]tiktokReadyCheck, 0)
	result = append(result, &sizeChartIsEmpty{})
	return result
}

func (model *tiktokShopReadyCheckModel) packageCheck() []tiktokReadyCheck {
	if len(model.product.Variants) == 0 {
		return nil
	}

	variant := model.product.Variants[0]
	result := make([]tiktokReadyCheck, 0)
	if !variant.HeightFilled() {
		result = append(result, &packageDimensionsHeightIsEmpty{})
	}
	if !variant.WidthFilled() {
		result = append(result, &packageDimensionsWidthIsEmpty{})
	}
	if !variant.LengthFilled() {
		result = append(result, &packageDimensionsLengthIsEmpty{})
	}
	if !variant.WeightFilled() {
		result = append(result, &packageWeightIsEmpty{})
	}

	if variant.LengthExceedTTSMaxValue() {
		result = append(result, &packageDimensionsLengthIsExceedMaxValue{})
	}

	return result
}

func (model *tiktokShopReadyCheckModel) mediaCheck() []tiktokReadyCheck {
	result := make([]tiktokReadyCheck, 0)
	if len(model.product.Media) == 0 {
		result = append(result, &mainImageIsEmpty{})
	} else {
		for i := range model.product.Media {
			if _, ok := image.IsSalesChannelURL(model.product.Media[i].URL, model.ttsCdnDomainRegExps); !ok {
				result = append(result, &mainImageInvalid{
					position: i,
				})
			}
		}
	}

	return result
}

func (model *tiktokShopReadyCheckModel) descriptionCheck() []tiktokReadyCheck {
	result := make([]tiktokReadyCheck, 0)
	if model.product.Description == "" {
		result = append(result, &descriptionIsEmpty{})
	}

	if model.regionConfig != nil && model.regionConfig.Region != consts.RegionJP {
		// 汉字校验
		for _, c := range model.product.Description {
			if c >= 0x4e00 && c <= 0x9fa5 {
				result = append(result, &descriptionContainChinese{})
				break
			}
		}
	}

	return result
}

func (model *tiktokShopReadyCheckModel) titleCheck() []tiktokReadyCheck {
	result := make([]tiktokReadyCheck, 0)

	if model.regionConfig != nil && model.regionConfig.Region != consts.RegionJP {
		// 汉字校验
		for _, c := range model.product.Title {
			if c >= 0x4e00 && c <= 0x9fa5 {
				result = append(result, &titleContainChinese{})
				break
			}
		}
	}

	return result
}

func (model *tiktokShopReadyCheckModel) variantMediaCheck() []tiktokReadyCheck {
	result := make([]tiktokReadyCheck, 0)
	for i := range model.product.Variants {
		if model.product.Variants[i].ImageURL == "" {
			continue
		}

		if _, ok := image.IsSalesChannelURL(model.product.Variants[i].ImageURL, model.ttsCdnDomainRegExps); !ok {
			result = append(result, &variantImageInvalid{position: i})
		}
	}
	return result
}

func (model *tiktokShopReadyCheckModel) variantMediaEmptyCheck() []tiktokReadyCheck {
	result := make([]tiktokReadyCheck, 0)

	needCheck := false
	for i := range model.product.Variants {
		if model.product.Variants[i].ImageURL != "" {
			needCheck = true
			break
		}
	}

	if needCheck {
		for i := range model.product.Variants {
			if model.product.Variants[i].ImageURL == "" {
				result = append(result, &variantImageIsEmpty{position: i})
			}
		}
	}

	return result
}

func (model *tiktokShopReadyCheckModel) variantOptionAndSKUCheck() []tiktokReadyCheck {
	result := make([]tiktokReadyCheck, 0)
	optionNameSet := set.NewStringSet()
	for i := range model.product.Variants {
		if len(model.product.Variants[i].Sku) > 50 {
			result = append(result, &skuTooLong{Position: i})
		}

		if model.product.Variants[i].IsSingleVariant() {
			continue
		}

		for j := range model.product.Variants[i].Options {
			optionNameSet.Add(model.product.Variants[i].Options[j].Name)
			if len(model.product.Variants[i].Options[j].Value) > 50 {
				result = append(result, &optionValueTooLong{PositionX: i, PositionY: j})
			}
		}
	}

	options := optionNameSet.ToList()
	for i := range options {
		if len(options[i]) > 20 {
			result = append(result, &optionNameTooLong{Position: i, Name: options[i]})
		}
	}
	return result
}

func (model *tiktokShopReadyCheckModel) variantPriceCheck() []tiktokReadyCheck {
	result := make([]tiktokReadyCheck, 0)
	for i := range model.product.Variants {
		if model.product.Variants[i].Price.Amount == "0" || model.product.Variants[i].Price.Amount == "" {
			result = append(result, &variantPriceInvalid{position: i})
		}
	}

	return result
}

func (model *tiktokShopReadyCheckModel) attributesCheck() []tiktokReadyCheck {
	if model.attributes == nil {
		return nil
	}
	if model.attributes.ExternalCategoryID == "" {
		return nil
	}

	result := make([]tiktokReadyCheck, 0)

	// 收集已经填写的 attributes
	productAttributes := model.product.Attributes
	productAttributesMap := make(map[string][]models.SalesChannelResource)
	for _, attr := range productAttributes {
		if attr == nil || attr.SalesChannelID == "" {
			continue
		}
		productAttributesMap[attr.SalesChannelID] = attr.Values
	}

	// 收集全部必填的 attributes
	requiredAttributes := model.collectRequiredAttributes()

	// 检查已经必填的 attributes
	for _, requiredAttribute := range requiredAttributes {
		if values, ok := productAttributesMap[requiredAttribute.ID]; !ok || len(values) == 0 {
			result = append(result, &attributesIsEmpty{
				Name: requiredAttribute.Name,
			})
		}
	}

	return result
}

func (model *tiktokShopReadyCheckModel) collectRequiredAttributes() []category.Attribute {

	if model.product == nil || model.attributes == nil {
		return nil
	}

	result := make([]category.Attribute, 0)
	productAttributes := model.product.Attributes
	productAttributeValuesMap := make(map[string][]models.SalesChannelResource)
	for _, attr := range productAttributes {
		if attr == nil || attr.SalesChannelID == "" {
			continue
		}
		productAttributeValuesMap[attr.SalesChannelID] = attr.Values
	}

	for _, attribute := range model.attributes.Attributes {
		if attribute.Type != consts.AttributeTypeProductProperty {
			continue
		}
		// IsRequired 字段判断
		if attribute.IsRequired {
			result = append(result, attribute)
			continue // next attribute
		}
		// condition 判断
		conditionRequired := false
		for _, condition := range attribute.RequirementConditions {
			targetAttributeValues, ok := productAttributeValuesMap[condition.AttributeID]
			if !ok {
				continue
			}
			for _, value := range targetAttributeValues {
				if value.SalesChannelID == condition.AttributeValueID {
					conditionRequired = true
					break
				}
			}
		}
		if conditionRequired {
			result = append(result, attribute)
		}
	}

	return result
}

func (model *tiktokShopReadyCheckModel) complianceCheck() []tiktokReadyCheck {

	result := make([]tiktokReadyCheck, 0)

	responsiblePersonIDs := make([]string, 0)
	for _, cur := range model.product.Compliance.ResponsiblePersons {
		if cur.SalesChannelID != "" {
			responsiblePersonIDs = append(responsiblePersonIDs, cur.SalesChannelID)
		}
	}

	manufacturerIDs := make([]string, 0)
	for _, cur := range model.product.Compliance.Manufacturers {
		if cur.SalesChannelID != "" {
			manufacturerIDs = append(manufacturerIDs, cur.SalesChannelID)
		}
	}

	var requiredResponsiblePerson, requiredManufacturer bool
	if model.rules != nil && model.rules.Rule.Compliance != nil {
		if model.rules.Rule.Compliance.ResponsiblePerson != nil {
			requiredResponsiblePerson = model.rules.Rule.Compliance.ResponsiblePerson.IsRequired
		}
		if model.rules.Rule.Compliance.Manufacturer != nil {
			requiredManufacturer = model.rules.Rule.Compliance.Manufacturer.IsRequired
		}
	}

	if requiredResponsiblePerson && len(responsiblePersonIDs) == 0 {
		result = append(result, &complianceResponsiblePersonEmpty{})
	}
	if requiredManufacturer && len(manufacturerIDs) == 0 {
		result = append(result, &complianceManufacturerEmpty{})
	}

	return result
}

type tiktokReadyCheck interface {
	toFailReason() *ReadyFailedReason
}

type packageWeightIsEmpty struct {
}

func (p *packageWeightIsEmpty) toFailReason() *ReadyFailedReason {
	return &ReadyFailedReason{
		Position:    readyCheckPackageWeight,
		ErrorCodes:  []string{errors_sdk.ProductListingUnreadyPackageWeightIsNotCorrect_6064120001.Code().String()},
		Reasons:     []string{errors_sdk.ProductListingUnreadyPackageWeightIsNotCorrect_6064120001.Error()},
		Suggestions: []string{errors_sdk.ProductListingUnreadyPackageWeightIsNotCorrect_6064120001.Error()},
	}
}

type packageDimensionsLengthIsEmpty struct {
}

func (p *packageDimensionsLengthIsEmpty) toFailReason() *ReadyFailedReason {
	return &ReadyFailedReason{
		Position:    readyCheckPackageDimensionsLength,
		ErrorCodes:  []string{errors_sdk.ProductListingUnreadyPackageLengthIsNotCorrect_6064120002.Code().String()},
		Reasons:     []string{errors_sdk.ProductListingUnreadyPackageLengthIsNotCorrect_6064120002.Error()},
		Suggestions: []string{errors_sdk.ProductListingUnreadyPackageLengthIsNotCorrect_6064120002.Error()},
	}
}

type packageDimensionsLengthIsExceedMaxValue struct {
}

func (p *packageDimensionsLengthIsExceedMaxValue) toFailReason() *ReadyFailedReason {
	return &ReadyFailedReason{
		Position:    readyCheckPackageDimensionsLength,
		ErrorCodes:  []string{errors_sdk.ProductListingUnreadyPackageLengthIsNotCorrect_6064120002.Code().String()},
		Reasons:     []string{errors_sdk.ProductListingUnreadyPackageLengthIsNotCorrect_6064120002.Error()},
		Suggestions: []string{errors_sdk.ProductListingUnreadyPackageLengthIsNotCorrect_6064120002.Error()},
	}
}

type packageDimensionsWidthIsEmpty struct {
}

func (p *packageDimensionsWidthIsEmpty) toFailReason() *ReadyFailedReason {
	return &ReadyFailedReason{
		Position:    readyCheckPackageDimensionsWidth,
		ErrorCodes:  []string{errors_sdk.ProductListingUnreadyPackageWidthIsNotCorrect_6064120010.Code().String()},
		Reasons:     []string{errors_sdk.ProductListingUnreadyPackageWidthIsNotCorrect_6064120010.Error()},
		Suggestions: []string{errors_sdk.ProductListingUnreadyPackageWidthIsNotCorrect_6064120010.Error()},
	}
}

type packageDimensionsHeightIsEmpty struct {
}

func (p *packageDimensionsHeightIsEmpty) toFailReason() *ReadyFailedReason {
	return &ReadyFailedReason{
		Position:    readyCheckPackageDimensionsHeight,
		ErrorCodes:  []string{errors_sdk.ProductListingUnreadyPackageHeightIsNotCorrect_6064120003.Code().String()},
		Reasons:     []string{errors_sdk.ProductListingUnreadyPackageHeightIsNotCorrect_6064120003.Error()},
		Suggestions: []string{errors_sdk.ProductListingUnreadyPackageHeightIsNotCorrect_6064120003.Error()},
	}
}

type variantImageInvalid struct {
	position int
}

func (v *variantImageInvalid) toFailReason() *ReadyFailedReason {
	return &ReadyFailedReason{
		Position:    fmt.Sprint(readyCheckVariant, "[", v.position, "].", readyCheckImage),
		ErrorCodes:  []string{errors_sdk.ProductListingUnreadyVariantImageIsInvalid_6064120005.Code().String()},
		Reasons:     []string{errors_sdk.ProductListingUnreadyVariantImageIsInvalid_6064120005.Error()},
		Suggestions: []string{errors_sdk.ProductListingUnreadyVariantImageIsInvalid_6064120005.Error()},
	}
}

type mainImageIsEmpty struct {
}

func (m *mainImageIsEmpty) toFailReason() *ReadyFailedReason {
	return &ReadyFailedReason{
		Position:    fmt.Sprint(readyCheckMainImage),
		ErrorCodes:  []string{errors_sdk.ProductListingUnreadyMainImageIsEmpty_6064120014.Code().String()},
		Reasons:     []string{errors_sdk.ProductListingUnreadyMainImageIsEmpty_6064120014.Error()},
		Suggestions: []string{errors_sdk.ProductListingUnreadyMainImageIsEmpty_6064120014.Error()},
	}
}

type titleIsEmpty struct {
}

func (m *titleIsEmpty) toFailReason() *ReadyFailedReason {
	return &ReadyFailedReason{
		Position:    fmt.Sprint(readyCheckTitle),
		ErrorCodes:  []string{errors_sdk.ProductListingUnreadyProductTitleIsEmpty_6064120032.Code().String()},
		Reasons:     []string{errors_sdk.ProductListingUnreadyProductTitleIsEmpty_6064120032.Error()},
		Suggestions: []string{errors_sdk.ProductListingUnreadyProductTitleIsEmpty_6064120032.Error()},
	}
}

type brandIsEmpty struct {
}

func (m *brandIsEmpty) toFailReason() *ReadyFailedReason {
	return &ReadyFailedReason{
		Position:    fmt.Sprint(readyCheckTitle),
		ErrorCodes:  []string{errors_sdk.ProductListingUnreadyProductBrandIsEmpty_6064120033.Code().String()},
		Reasons:     []string{errors_sdk.ProductListingUnreadyProductBrandIsEmpty_6064120033.Error()},
		Suggestions: []string{errors_sdk.ProductListingUnreadyProductBrandIsEmpty_6064120033.Error()},
	}
}

type descriptionIsEmpty struct {
}

func (m *descriptionIsEmpty) toFailReason() *ReadyFailedReason {
	return &ReadyFailedReason{
		Position:    fmt.Sprint(readyCheckDescription),
		ErrorCodes:  []string{errors_sdk.ProductListingUnreadyProductDescriptionIsEmpty_6064120015.Code().String()},
		Reasons:     []string{errors_sdk.ProductListingUnreadyProductDescriptionIsEmpty_6064120015.Error()},
		Suggestions: []string{errors_sdk.ProductListingUnreadyProductDescriptionIsEmpty_6064120015.Error()},
	}
}

type mainImageInvalid struct {
	position int
}

func (m *mainImageInvalid) toFailReason() *ReadyFailedReason {
	return &ReadyFailedReason{
		Position:    fmt.Sprint(readyCheckMainImage, "[", m.position, "]"),
		ErrorCodes:  []string{errors_sdk.ProductListingUnreadyMainImageIsInvalid_6064120006.Code().String()},
		Reasons:     []string{errors_sdk.ProductListingUnreadyMainImageIsInvalid_6064120006.Error()},
		Suggestions: []string{errors_sdk.ProductListingUnreadyMainImageIsInvalid_6064120006.Error()},
	}
}

type variantIsEmpty struct {
}

func (v *variantIsEmpty) toFailReason() *ReadyFailedReason {
	return &ReadyFailedReason{
		Position:    readyCheckVariant,
		ErrorCodes:  []string{errors_sdk.ProductListingUnreadyVariantIsEmpty_6064120007.Code().String()},
		Reasons:     []string{errors_sdk.ProductListingUnreadyVariantIsEmpty_6064120007.Error()},
		Suggestions: []string{errors_sdk.ProductListingUnreadyVariantIsEmpty_6064120007.Error()},
	}
}

type certificationsIsEmpty struct {
	Name string
}

func (c *certificationsIsEmpty) toFailReason() *ReadyFailedReason {
	return &ReadyFailedReason{
		Position:    c.Name,
		ErrorCodes:  []string{errors_sdk.ProductListingUnreadyCertificationsAreMissing_6064120008.Code().String()},
		Reasons:     []string{errors_sdk.ProductListingUnreadyCertificationsAreMissing_6064120008.Error()},
		Suggestions: []string{errors_sdk.ProductListingUnreadyCertificationsAreMissing_6064120008.Error()},
	}
}

type sizeChartIsEmpty struct {
}

func (s *sizeChartIsEmpty) toFailReason() *ReadyFailedReason {
	return &ReadyFailedReason{
		Position:    readyCheckSizeChart,
		ErrorCodes:  []string{errors_sdk.ProductListingUnreadySizeChartIsEmpty_6064120009.Code().String()},
		Reasons:     []string{errors_sdk.ProductListingUnreadySizeChartIsEmpty_6064120009.Error()},
		Suggestions: []string{errors_sdk.ProductListingUnreadySizeChartIsEmpty_6064120009.Error()},
	}
}

type categoryIsEmpty struct {
}

func (v *categoryIsEmpty) toFailReason() *ReadyFailedReason {
	return &ReadyFailedReason{
		Position:    readyCheckCategory,
		ErrorCodes:  []string{errors_sdk.ProductListingSHEINUnreadyCategoryIsEmpty_6064120034.Code().String()},
		Reasons:     []string{errors_sdk.ProductListingSHEINUnreadyCategoryIsEmpty_6064120034.Error()},
		Suggestions: []string{errors_sdk.ProductListingSHEINUnreadyCategoryIsEmpty_6064120034.Error()},
	}
}

type attributesIsEmpty struct {
	Name string
}

func (a *attributesIsEmpty) toFailReason() *ReadyFailedReason {
	return &ReadyFailedReason{
		Position:    fmt.Sprint(readyCheckAttributes, ": ", a.Name),
		ErrorCodes:  []string{errors_sdk.ProductListingUnreadyAttributesIsEmpty_6064120013.Code().String()},
		Reasons:     []string{errors_sdk.ProductListingUnreadyAttributesIsEmpty_6064120013.Error()},
		Suggestions: []string{fmt.Sprintf("Fill in the required attributes: %s", a.Name)},
	}
}

type optionNameTooLong struct {
	Name     string
	Position int
}

func (a *optionNameTooLong) toFailReason() *ReadyFailedReason {
	return &ReadyFailedReason{
		Position:    fmt.Sprint(readyCheckAttributes, "[", a.Position, "]"),
		ErrorCodes:  []string{errors_sdk.ProductListingUnreadyAttributeNameTooLong_6064120016.Code().String()},
		Reasons:     []string{errors_sdk.ProductListingUnreadyAttributeNameTooLong_6064120016.Error()},
		Suggestions: []string{fmt.Sprintf(`Option Name "%s" should less than 20 characters`, a.Name)},
	}
}

type optionValueTooLong struct {
	PositionX int
	PositionY int
}

func (a *optionValueTooLong) toFailReason() *ReadyFailedReason {
	return &ReadyFailedReason{
		Position:    fmt.Sprint(readyCheckAttributes, "[", a.PositionX, "][", a.PositionY, "]"),
		ErrorCodes:  []string{errors_sdk.ProductListingUnreadyAttributeValueTooLong_6064120017.Code().String()},
		Reasons:     []string{errors_sdk.ProductListingUnreadyAttributeValueTooLong_6064120017.Error()},
		Suggestions: []string{errors_sdk.ProductListingUnreadyAttributeValueTooLong_6064120017.Error()},
	}
}

type skuTooLong struct {
	Position int
}

func (a *skuTooLong) toFailReason() *ReadyFailedReason {
	return &ReadyFailedReason{
		Position:    fmt.Sprint(readyCheckVariant, "[", a.Position, "]"),
		ErrorCodes:  []string{errors_sdk.ProductListingUnreadySKUTooLong_6064120018.Code().String()},
		Reasons:     []string{errors_sdk.ProductListingUnreadySKUTooLong_6064120018.Error()},
		Suggestions: []string{errors_sdk.ProductListingUnreadySKUTooLong_6064120018.Error()},
	}
}

type variantPriceInvalid struct {
	position int
}

func (v *variantPriceInvalid) toFailReason() *ReadyFailedReason {
	return &ReadyFailedReason{
		Position:    fmt.Sprint(readyCheckVariant, "[", v.position, "].price", readyCheckPrice),
		ErrorCodes:  []string{errors_sdk.ProductListingUnreadyVariantPriceIsInvalid_6064120011.Code().String()},
		Reasons:     []string{errors_sdk.ProductListingUnreadyVariantPriceIsInvalid_6064120011.Error()},
		Suggestions: []string{errors_sdk.ProductListingUnreadyVariantPriceIsInvalid_6064120011.Error()},
	}
}

type variantImageIsEmpty struct {
	position int
}

func (v *variantImageIsEmpty) toFailReason() *ReadyFailedReason {
	return &ReadyFailedReason{
		Position:    fmt.Sprint(readyCheckVariant, "[", v.position, "].", readyCheckImage),
		ErrorCodes:  []string{errors_sdk.ProductListingUnreadyVariantImageIsEmpty_6064120019.Code().String()},
		Reasons:     []string{errors_sdk.ProductListingUnreadyVariantImageIsEmpty_6064120019.Error()},
		Suggestions: []string{errors_sdk.ProductListingUnreadyVariantImageIsEmpty_6064120019.Error()},
	}
}

type complianceManufacturerEmpty struct {
}

func (v *complianceManufacturerEmpty) toFailReason() *ReadyFailedReason {
	return &ReadyFailedReason{
		Position:    fmt.Sprint(readyCheckCompliance, ": ", consts.ComplianceManufacturer),
		ErrorCodes:  []string{errors_sdk.ProductListingUnreadyComplianceManufacturerEmpty_6064120023.Code().String()},
		Reasons:     []string{errors_sdk.ProductListingUnreadyComplianceManufacturerEmpty_6064120023.Error()},
		Suggestions: []string{errors_sdk.ProductListingUnreadyComplianceManufacturerEmpty_6064120023.Error()},
	}
}

type complianceResponsiblePersonEmpty struct {
}

func (v *complianceResponsiblePersonEmpty) toFailReason() *ReadyFailedReason {
	return &ReadyFailedReason{
		Position:    fmt.Sprint(readyCheckCompliance, ": ", consts.ComplianceResponsiblePerson),
		ErrorCodes:  []string{errors_sdk.ProductListingUnreadyComplianceResponsiblePersonEmpty_6064120024.Code().String()},
		Reasons:     []string{errors_sdk.ProductListingUnreadyComplianceResponsiblePersonEmpty_6064120024.Error()},
		Suggestions: []string{errors_sdk.ProductListingUnreadyComplianceResponsiblePersonEmpty_6064120024.Error()},
	}
}

type descriptionContainChinese struct{}

func (m *descriptionContainChinese) toFailReason() *ReadyFailedReason {
	return &ReadyFailedReason{
		Position:    fmt.Sprint(readyCheckDescription),
		ErrorCodes:  []string{errors_sdk.WFPTTSCreateProductDescContainChinese_603422780.Code().String()},
		Reasons:     []string{"Product descriptions cannot contain Chinese characters"},
		Suggestions: []string{"Product descriptions cannot contain Chinese characters"},
	}
}

type titleContainChinese struct{}

func (m *titleContainChinese) toFailReason() *ReadyFailedReason {
	return &ReadyFailedReason{
		Position:    fmt.Sprint(readyCheckTitle),
		ErrorCodes:  []string{errors_sdk.WFPTTSCreateProductTitleContainChinese_603422795.Code().String()},
		Reasons:     []string{"Product title cannot contain Chinese characters"},
		Suggestions: []string{"Product title cannot contain Chinese characters"},
	}
}
