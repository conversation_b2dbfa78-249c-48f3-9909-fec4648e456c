package product_listing

import (
	"context"
	"errors"
	"math"
	"sort"
	"strings"

	"github.com/go-playground/validator/v10"
	"go.uber.org/zap"

	"github.com/AfterShip/connectors-library/sdks/products_center"
	"github.com/AfterShip/gopkg/facility/set"
	"github.com/AfterShip/gopkg/log"
	"github.com/AfterShip/pltf-pd-product-listings/internal/config"
	"github.com/AfterShip/pltf-pd-product-listings/internal/utils/toolbox"

	"github.com/AfterShip/pltf-pd-product-listings/internal/consts"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/common/calculators"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/convert"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/settings"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
	"github.com/AfterShip/pltf-pd-product-listings/internal/utils/slicex"
)

type previewModel struct {
	service   *serviceImpl
	validator *validator.Validate

	// Data dependency
	originPL            *ProductListing
	pcp                 *products_center.Product
	setting             *storeProductListingSetting
	removeVariantsImage bool // 特定白名单客户不组装 SKU 图

	// 中间变量
	linkedRelations             []*ProductListingRelation
	linkedProductsCenterProduct map[string]*products_center.Product

	// Result
	preview *ProductListing

	amazonOptionNameMapping map[string]string
}

func (s *serviceImpl) newPreviewModel(originPL *ProductListing, storeSetting *settings.Setting,
	pcp *products_center.Product, optionNameMapping map[string]string) *previewModel {
	removeVariantsImage := shouldRemoveVariantsImagesForPreview(pcp, s.conf.DynamicConfigs.TikTokSyncConfig)
	return &previewModel{
		service:                     s,
		validator:                   validator.New(),
		originPL:                    originPL,
		pcp:                         pcp,
		setting:                     newStoreProductListingSetting(*storeSetting, originPL.Settings),
		preview:                     originPL.DeepCopy(),
		linkedProductsCenterProduct: make(map[string]*products_center.Product, 0),
		linkedRelations:             make([]*ProductListingRelation, 0),
		removeVariantsImage:         removeVariantsImage,
		amazonOptionNameMapping:     optionNameMapping,
	}
}

func (p *previewModel) generatePreview(ctx context.Context) (*ProductListing, error) {
	// Note that the order of the following functions is important.

	if p.originPL.matched() {
		matchHandles := []func(ctx context.Context) error{
			p.overWriteProductTitle,
			p.overWriteProductDescription,
			p.overWriteProductMainMedia,
			p.overWriteVariantAndRelation,
			p.overWriteVariantDetail,
			p.overWriteProductOptions,
		}

		for _, handle := range matchHandles {
			if err := handle(ctx); err != nil {
				return nil, err
			}
		}
	}

	priceAndInventoryHandles := []func(ctx context.Context) error{
		p.preOverWriteVariantPriceAndInventory,
		p.overWriteVariantPrice,
		p.overWriteVariantInventory,
	}

	for _, handle := range priceAndInventoryHandles {
		if err := handle(ctx); err != nil {
			return nil, err
		}
	}

	return p.preview, nil
}

func (p *previewModel) overWriteProductTitle(ctx context.Context) error {
	if !p.setting.autoSyncProductDetailField(consts.ProductDetailFieldTitle) {
		return nil
	}

	p.preview.Product.Title = p.pcp.Title
	return nil
}

func (p *previewModel) overWriteProductOptions(ctx context.Context) error {
	p.preview.Product.Options = models.ConvertOptionsWithNameMapping(p.preview.Product.Options, p.amazonOptionNameMapping)
	return nil
}

func (p *previewModel) overWriteProductDescription(ctx context.Context) error {
	if !p.setting.autoSyncProductDetailField(consts.ProductDetailFieldDescription) {
		return nil
	}

	data, err := p.service.convertService.ConvertDescription(ctx, &convert.DescriptionArg{
		Organization: &p.originPL.Organization,
		SalesChannel: &p.originPL.SalesChannel,
		SourceStore: &models.App{
			Key:      p.pcp.Source.App.Key,
			Platform: p.pcp.Source.App.Platform,
		},
		Description:      p.pcp.Description,
		ShortDescription: p.pcp.ShortDescription,
		IgnoreCache:      false,
	})
	if err != nil {
		return err
	}

	p.preview.Product.Description = data.Description
	return nil
}

func (p *previewModel) overWriteProductMainMedia(ctx context.Context) error {
	if !p.setting.autoSyncProductDetailField(consts.ProductDetailFieldMedia) {
		return nil
	}

	mainMedia := make([]*models.ProductMedia, 0)
	for _, media := range p.pcp.Media {
		// Convert image
		data, err := p.service.convertService.ConvertImage(ctx, &convert.ImageArg{
			Organization:    &p.originPL.Organization,
			SalesChannel:    &p.originPL.SalesChannel,
			ImageUseCase:    convert.ImageUseCaseMainImage,
			ImageName:       "",
			ImageType:       "",
			ImageBytes:      nil,
			ImageOriginURL:  media.Url,
			IgnoreCache:     false,
			IgnoreTransform: false,
		})
		if err != nil {
			// If the image is a video or gateway 413 error, skip
			if errors.Is(err, convert.ErrorUnsupportedVideoURL) ||
				errors.Is(err, convert.ErrorConvertImageService413Error) ||
				errors.Is(err, convert.ErrorDownLoadFileFromEcommerce4XX) {
				log.GlobalLogger().WarnCtx(ctx, "convert image error",
					zap.String("image_url", media.Url),
					zap.String("listing_id", p.originPL.ID),
					zap.String("organization_id", p.originPL.Organization.ID),
					zap.Error(err))
				continue
			}
			return err
		}

		mainMedia = append(mainMedia, &models.ProductMedia{
			SalesChannelID: data.ImageURI,
			Type:           media.Type,
			Position:       media.Position,
			Thumbnail: models.ProductMediaThumbnail{
				URL: data.ImageURL,
			},
			URL:               data.ImageURL,
			MimeType:          media.MimeType,
			ExternalVideoHost: media.SourceVideoHost,
		})
	}

	p.preview.Product.Media = mainMedia
	return nil
}

func (p *previewModel) overWriteVariantAndRelation(ctx context.Context) error {
	if !p.setting.autoSyncVariant() {
		return nil
	}

	// If auto sync variant is enabled, get the latest variants and relation data from DB.
	conductorProductListing, err := p.service.repo.getByID(ctx, p.originPL.ID)
	if err != nil {
		return err
	}
	data := convertToProductListing(&conductorProductListing)
	// If the product listing is unmatched, skip
	// Match product situation, trust the front-end data, prevent delete all SKU in sales_channel
	if !p.originPL.matched() {
		return nil
	}

	variants, relations, err := p.getVariantsPreview(ctx, getVariantsPreviewArgs{
		productListingID: p.originPL.ID,
		variants:         data.Product.Variants,
		relations:        data.Relations,
		pcp:              p.pcp,
	})
	if err != nil {
		return err
	}

	// overwrite variant barcode by origin product_listing
	for i := range p.originPL.Product.Variants {
		if p.originPL.Product.Variants[i].Barcode.Value == "" && p.originPL.Product.Variants[i].Barcode.Type == "" {
			continue
		}

		for j := range variants {
			if variants[j].ID == p.originPL.Product.Variants[i].ID {
				variants[j].Barcode = p.originPL.Product.Variants[i].Barcode
			}
		}
	}

	p.preview.Product.Variants = variants
	p.preview.Relations = relations

	// maintenance link and sync status
	p.preview.ModifyStateAndStatus()
	return nil
}

func (p *previewModel) overWriteVariantDetail(ctx context.Context) error {
	if !p.setting.autoSyncVariant() {
		return nil
	}

	// product_center_variant_id -> product_center_variant
	pcpVariants := make(map[string]products_center.Variant)
	for i := range p.pcp.Variants {
		variant := p.pcp.Variants[i]
		pcpVariants[variant.ID] = p.pcp.Variants[i]
	}

	// Overwrite variant fields
	for i, variant := range p.preview.Product.Variants {
		relation, ok := getRelationWithProductCenterVariantID(p.preview.Relations, variant)
		if !ok {
			return ErrNoProductsCenterVariantID
		}

		if pcpVariant, ok := pcpVariants[relation.ProductsCenterVariant.ID]; ok {
			// Convert image
			if pcpVariant.ImageUrl != "" && !p.removeVariantsImage {
				data, err := p.service.convertService.ConvertImage(ctx, &convert.ImageArg{
					Organization:    &p.originPL.Organization,
					SalesChannel:    &p.originPL.SalesChannel,
					ImageUseCase:    convert.ImageUseCaseMainImage,
					ImageName:       "",
					ImageType:       "",
					ImageBytes:      nil,
					ImageOriginURL:  pcpVariant.ImageUrl,
					IgnoreCache:     false,
					IgnoreTransform: false,
				})
				if err != nil {
					// If the image is a video or gateway 413 error, skip
					if !errors.Is(err, convert.ErrorUnsupportedVideoURL) &&
						!errors.Is(err, convert.ErrorConvertImageService413Error) {
						return err
					}
				}

				// So far only Image or option changes will be synced to sales_channel
				if data.ImageURL != "" {
					p.preview.Product.Variants[i].ImageURL = data.ImageURL
				}
			}

			p.preview.Product.Variants[i].Options = convertOptions(pcpVariant.Options)
			// Dimensions are consistent with origin product_listing
			p.preview.Product.Variants[i].Length = p.originPL.Product.GetLength()
			p.preview.Product.Variants[i].Width = p.originPL.Product.GetWidth()
			p.preview.Product.Variants[i].Height = p.originPL.Product.GetHeight()
			p.preview.Product.Variants[i].Weight = p.originPL.Product.GetWeight()

			p.preview.Product.Variants[i].Sku = pcpVariant.Sku
			p.preview.Product.Variants[i].AllowBackorder = pcpVariant.AllowBackorder
			p.preview.Product.Variants[i].FulfillmentService = pcpVariant.FulfillmentService
			p.preview.Product.Variants[i].RequiresShipping = pcpVariant.RequiresShipping
		}

		// 将 option name 改为映射中的值
		p.preview.Product.Variants[i].Options = models.ConvertVariantOptionsWithNameMapping(
			p.preview.Product.Variants[i].Options,
			p.amazonOptionNameMapping)
	}

	return nil
}

// nolint:gocyclo
func (p *previewModel) preOverWriteVariantPriceAndInventory(ctx context.Context) error {
	linkedProductsCenterProductIDs := make([]string, 0)
	for i := range p.preview.Relations {
		relation := p.preview.Relations[i]
		if relation.ProductsCenterVariant.ID != "" {
			p.linkedRelations = append(p.linkedRelations, p.preview.Relations[i])

			if !slicex.ContainsString(linkedProductsCenterProductIDs, relation.ProductsCenterVariant.ProductID) {
				linkedProductsCenterProductIDs = append(linkedProductsCenterProductIDs, relation.ProductsCenterVariant.ProductID)
			}
		}
	}

	if len(linkedProductsCenterProductIDs) == 0 {
		return nil
	}

	if p.pcp != nil &&
		len(linkedProductsCenterProductIDs) == 1 &&
		linkedProductsCenterProductIDs[0] == p.pcp.ID {
		p.linkedProductsCenterProduct[p.pcp.ID] = p.pcp
		return nil
	}

	// Get all linked products center product
	products, err := p.service.productsCenterClient.Product.List(ctx, &products_center.GetProductsArgs{
		OrganizationID: p.preview.Organization.ID,
		IDs:            strings.Join(linkedProductsCenterProductIDs, ","),
	})
	if err != nil {
		return err
	}

	if len(products) == 0 {
		return ErrProductNotFound
	}

	if len(products) != len(linkedProductsCenterProductIDs) {
		return ErrProductNotFound
	}

	for i := range products {
		p.linkedProductsCenterProduct[products[i].ID] = products[i]
	}

	return nil
}

// nolint:gocyclo
func (p *previewModel) overWriteVariantPrice(ctx context.Context) error {
	priceSyncSetting := p.setting.getPriceSyncSetting()

	handleVariantIDs := make([]string, 0)
	if priceSyncSetting.AutoSync == consts.StateEnabled {
		for i := range p.preview.Product.Variants {
			handleVariantIDs = append(handleVariantIDs, p.preview.Product.Variants[i].ID)
		}
	}

	if priceSyncSetting.AutoSync == consts.StateDisabled {
		for i := range p.preview.Product.Variants {
			newVariant := true
			for j := range p.originPL.Product.Variants {
				if p.preview.Product.Variants[i].ID == p.originPL.Product.Variants[j].ID &&
					p.originPL.Product.Variants[j].Price.Currency != "" &&
					p.originPL.Product.Variants[j].Price.Amount != "" {
					newVariant = false
					p.preview.Product.Variants[i].Price = p.originPL.Product.Variants[j].Price
					p.preview.Product.Variants[i].CompareAtPrice = p.originPL.Product.Variants[j].CompareAtPrice
				}
			}
			if newVariant {
				handleVariantIDs = append(handleVariantIDs, p.preview.Product.Variants[i].ID)
			}
		}
	}

	if len(handleVariantIDs) == 0 || len(p.linkedRelations) == 0 {
		return nil
	}

	currency := p.originPL.Product.Variants[0].Price.Currency
	// Calculate all linked products center product prices
	calculatePricesOutputs := make(map[string]*calculators.CalculatePricesOutput, 0)
	for _, pcp := range p.linkedProductsCenterProduct {
		if pcp == nil {
			return errors.New("product is nil")
		}

		// 基于 ID 拿一次最新数据，同时完成价格的特殊处理
		latestProduct, err := p.service.getLatestProduct(ctx, pcp.ID)
		if err != nil {
			p.service.logger.WarnCtx(ctx, "get latest product with price from meta field failed",
				zap.String("products_center_id", pcp.ID), zap.Error(err))
		} else {
			pcp = latestProduct
		}

		output, err := p.service.calculatePrices(ctx,
			p.preview.Organization,
			p.preview.SalesChannel,
			currency,
			pcp,
			&priceSyncSetting)
		if err != nil {
			return err
		}
		calculatePricesOutputs[pcp.ID] = output
	}

	for i, variant := range p.preview.Product.Variants {
		if !slicex.ContainsString(handleVariantIDs, variant.ID) { // gocover:ignore
			continue
		}

		relation, ok := getRelationWithProductCenterVariantID(p.preview.Relations, variant)
		if !ok {
			// Variant may be unlinked, skip
			continue
		}

		if relation.ProductsCenterVariant.ID == "" {
			continue
		}

		calculatePricesOutput, ok := calculatePricesOutputs[relation.ProductsCenterVariant.ProductID]
		if !ok {
			return ErrCalculateVariantPriceNotFound
		}

		price, compareAtPrice, err := calculatePricesOutput.GetPrice(relation.ProductsCenterVariant.ID)
		if err != nil {
			return err
		}
		p.preview.Product.Variants[i].Price = price
		p.preview.Product.Variants[i].CompareAtPrice = compareAtPrice
	}

	return nil
}

func (p *previewModel) overWriteVariantInventory(ctx context.Context) error {
	inventorySyncSetting := p.setting.getInventorySyncSetting()
	handleVariantIDs := make([]string, 0)
	if inventorySyncSetting.AutoSync == consts.StateEnabled {
		for i := range p.preview.Product.Variants {
			handleVariantIDs = append(handleVariantIDs, p.preview.Product.Variants[i].ID)
		}
	}

	if inventorySyncSetting.AutoSync == consts.StateDisabled {
		for i := range p.preview.Product.Variants {
			newVariant := true
			for j := range p.originPL.Product.Variants {
				if p.preview.Product.Variants[i].ID == p.originPL.Product.Variants[j].ID {
					newVariant = false
					p.preview.Product.Variants[i].InventoryQuantity = p.originPL.Product.Variants[j].InventoryQuantity
				}
			}
			if newVariant {
				handleVariantIDs = append(handleVariantIDs, p.preview.Product.Variants[i].ID)
			}
		}
	}

	if len(handleVariantIDs) == 0 || len(p.linkedRelations) == 0 {
		return nil
	}

	// Calculate all linked products center product available quantities
	calculateAvailableQuantitiesOutputs := make(map[string]*calculators.CalculateAvailableQuantitiesOutput, 0)
	for i := range p.linkedProductsCenterProduct {
		output, err := p.service.calculateQuantities(ctx,
			p.preview.Organization,
			p.preview.SalesChannel,
			p.linkedProductsCenterProduct[i],
			&inventorySyncSetting)
		if err != nil {
			return err
		}
		calculateAvailableQuantitiesOutputs[p.linkedProductsCenterProduct[i].ID] = output
	}

	for i, variant := range p.preview.Product.Variants {
		if !slicex.ContainsString(handleVariantIDs, variant.ID) {
			continue
		}

		relation, ok := getRelationWithProductCenterVariantID(p.preview.Relations, variant)
		if !ok {
			// Variant may be unlinked, skip
			continue
		}
		if relation.ProductsCenterVariant.ID == "" {
			continue
		}

		calculateAvailableQuantitiesOutput, ok := calculateAvailableQuantitiesOutputs[relation.ProductsCenterVariant.ProductID]
		if !ok {
			return ErrCalculateVariantInventoryNotFound
		}

		quantity, err := calculateAvailableQuantitiesOutput.GetAvailableQuantity(relation.ProductsCenterVariant.ID)
		if err != nil {
			return err
		}

		if quantity > consts.TikTokMaxInventory {
			quantity = consts.TikTokMaxInventory
		}
		p.preview.Product.Variants[i].InventoryQuantity = quantity
	}

	return nil
}

type getVariantsPreviewArgs struct {
	productListingID string                    `validate:"required"`
	variants         []*models.ProductVariant  `validate:"required"`
	relations        []*ProductListingRelation `validate:"required"`
	pcp              *products_center.Product  `validate:"required"`
}

func (p *previewModel) getVariantsPreview(ctx context.Context, args getVariantsPreviewArgs) ([]*models.ProductVariant, []*ProductListingRelation, error) {
	if err := p.validator.StructCtx(ctx, &args); err != nil {
		return nil, nil, err
	}
	variants := make([]*models.ProductVariant, 0)
	relations := make([]*ProductListingRelation, 0)

	// product_center_variant_id -> product_variant
	variantsMap := make(map[string]*models.ProductVariant)
	// product_center_variant_id -> product_listing_relation
	relationsMap := make(map[string]*ProductListingRelation)
	for i := range args.variants {
		relation, ok := getRelationWithProductCenterVariantID(args.relations, args.variants[i])
		// If not found product center variant ID, it means the variant is not come from product center, should be removed.
		if !ok {
			continue
		}
		variantsMap[relation.ProductsCenterVariant.ID] = args.variants[i]
		relationsMap[relation.ProductsCenterVariant.ID] = relation
	}

	positionSet := set.NewIntSet()
	for i := range args.pcp.Variants {
		pcpVariant := args.pcp.Variants[i]
		plVariant, ok1 := variantsMap[pcpVariant.ID]
		relation, ok2 := relationsMap[pcpVariant.ID]

		// position is unique
		position := pcpVariant.Position
		for positionSet.Contains(position) {
			position++
		}
		positionSet.Add(position)

		if ok1 && ok2 {
			// Reset variant position
			plVariant.Position = position
			relation.VariantPosition = position
		} else {
			// Add variant
			newVariant, newRelation, err := p.createVariantFromProductCenterVariant(ctx, createVariantFromProductCenterVariantArgs{
				position:   position,
				plID:       args.productListingID,
				pcp:        args.pcp,
				pcpVariant: &pcpVariant,
			})
			if err != nil {
				return nil, nil, err
			}

			plVariant = newVariant
			relation = newRelation
		}

		// AFD-10082
		overWriteRelationProductIDs(relation, pcpVariant, args.pcp)

		// 基于白名单移除指定客户的 SKU 图，白名单纬度为 org_id + options，只有商品现有的全部 options 跟白名单内配置的完全符合，才会被移除
		if p.removeVariantsImage {
			plVariant.ImageURL = ""
		}
		variants = append(variants, plVariant)
		relations = append(relations, relation)
	}

	// Sort variants and relations by position
	sort.Slice(variants, func(i, j int) bool {
		return variants[i].Position < variants[j].Position
	})
	sort.Slice(relations, func(i, j int) bool {
		return relations[i].VariantPosition < relations[j].VariantPosition
	})

	return variants, relations, nil
}

// overWriteRelationProductIDs 设置 relation 关联的产品 ID 信息
func overWriteRelationProductIDs(relation *ProductListingRelation, pcpVariant products_center.Variant, pcp *products_center.Product) {
	if relation == nil || pcp == nil {
		return
	}

	// 设置源产品ID
	sourceProductID := pcp.Source.ID
	if pcpVariant.SourceProductID != "" {
		sourceProductID = pcpVariant.SourceProductID
	}
	relation.ProductsCenterVariant.Source.ProductID = sourceProductID

	// 设置连接器产品ID
	connectorProductID := pcp.ConnectorsProductID
	if pcpVariant.ConnectorsProductID != "" {
		connectorProductID = pcpVariant.ConnectorsProductID
	}
	relation.ProductsCenterVariant.ConnectorProductID = connectorProductID
}

// TODO: common function
type createVariantFromProductCenterVariantArgs struct {
	position int `validate:"required"`
	// plID may be empty if the product listing is not created yet.
	plID       string
	pcp        *products_center.Product `validate:"required"`
	pcpVariant *products_center.Variant `validate:"required"`
}

func (p *previewModel) createVariantFromProductCenterVariant(ctx context.Context,
	args createVariantFromProductCenterVariantArgs) (*models.ProductVariant, *ProductListingRelation, error) {
	if err := p.validator.StructCtx(ctx, &args); err != nil {
		return nil, nil, err
	}

	variant := &models.ProductVariant{
		ID:                "",
		Position:          args.position,
		InventoryQuantity: args.pcpVariant.InventoryQuantity,
		Sku:               args.pcpVariant.Sku,
		Barcode: models.ProductVariantBarcode{
			Value: args.pcpVariant.Barcode,
			Type:  toolbox.BarcodeType(args.pcpVariant.Barcode),
		},
		Title: args.pcpVariant.Title,
		Price: models.ProductVariantPrice{
			Currency: args.pcpVariant.Price.Currency,
			Amount:   args.pcpVariant.Price.Amount,
		},
		Cost: models.ProductVariantPrice{
			Currency: args.pcpVariant.Cost.Currency,
			Amount:   args.pcpVariant.Cost.Amount,
		},
		ImageURL: args.pcpVariant.ImageUrl,
		CompareAtPrice: models.ProductVariantPrice{
			Currency: args.pcpVariant.CompareAtPrice.Currency,
			Amount:   args.pcpVariant.CompareAtPrice.Amount,
		},
		Length: models.ProductVariantShippingSetting{
			Unit:  args.pcpVariant.Length.Unit,
			Value: math.Ceil(args.pcpVariant.Length.Value),
		},
		Width: models.ProductVariantShippingSetting{
			Unit:  args.pcpVariant.Width.Unit,
			Value: math.Ceil(args.pcpVariant.Width.Value),
		},
		Height: models.ProductVariantShippingSetting{
			Unit:  args.pcpVariant.Height.Unit,
			Value: math.Ceil(args.pcpVariant.Height.Value),
		},
		Weight: models.ProductVariantShippingSetting{
			Unit:  args.pcpVariant.Weight.Unit,
			Value: args.pcpVariant.Weight.Value,
		},
		AllowBackorder:     args.pcpVariant.AllowBackorder,
		Options:            convertOptions(args.pcpVariant.Options),
		FulfillmentService: args.pcpVariant.FulfillmentService,
		RequiresShipping:   args.pcpVariant.RequiresShipping,
	}

	relation := &ProductListingRelation{
		ID: "",
		Organization: models.Organization{
			ID: args.pcp.Organization.ID,
		},
		SalesChannel: models.SalesChannel{
			StoreKey:      "",
			Platform:      "",
			CountryRegion: "",
		},
		ProductListingID:        args.plID,
		VariantPosition:         args.position,
		ProductListingVariantID: "",
		SalesChannelVariant: SalesChannelVariant{
			ID:                 "",
			ConnectorProductID: "",
			ProductID:          "",
			Sku:                "",
		},
		ProductsCenterVariant: ProductsCenterVariant{
			ID:                 args.pcpVariant.ID,
			ProductID:          args.pcp.ID,
			ConnectorProductID: args.pcp.ConnectorsProductID,
			Source: ProductsCenterVariantSource{
				StoreKey:  args.pcp.Source.App.Key,
				Platform:  args.pcp.Source.App.Platform,
				ID:        args.pcpVariant.SourceVariantID,
				ProductID: args.pcp.Source.ID,
				Sku:       args.pcpVariant.Sku,
			},
		},
		SyncStatus: consts.SyncStatusUnsync,
		LinkStatus: consts.LinkStatusLinked,
		AllowSync:  consts.AllowSyncEnabled,
	}

	return variant, relation, nil
}

func convertOptions(pcpVariantOptions []products_center.VariantOption) []*models.ProductVariantOption {
	options := make([]*models.ProductVariantOption, 0)
	for _, option := range pcpVariantOptions {
		options = append(options, &models.ProductVariantOption{
			Name:  option.Name,
			Value: option.Value,
		})
	}

	return options
}

func getRelationWithProductCenterVariantID(relations []*ProductListingRelation, variant *models.ProductVariant) (*ProductListingRelation, bool) {
	for _, relation := range relations {
		// If relation not link, skip
		// if relation.LinkStatus != consts.LinkStatusLinked {
		//	continue
		// }

		// If variant ID is not empty, it means variants and relations have been previously saved in DataBase.
		// Therefore, a relation with the same variant ID must exist.
		if len(variant.ID) != 0 {
			if relation.ProductListingVariantID == variant.ID &&
				len(relation.ProductsCenterVariant.ID) != 0 {
				return relation, true
			}
		}

		// If variant ID is empty, it means the variant is newly added.
		if variant.Position != 0 && variant.Position == relation.VariantPosition && len(relation.ProductsCenterVariant.ID) != 0 {
			return relation, true
		}
	}

	return nil, false
}

func shouldRemoveVariantsImagesForPreview(product *products_center.Product, config *config.TikTokSyncConfig) bool {
	if config == nil || len(config.SkuImageRemovalConditions) == 0 {
		return false
	}

	optionNames := set.NewStringSet()
	for _, variant := range product.Variants {
		for _, vop := range variant.Options {
			if vop.Name != "" {
				optionNames.Add(vop.Name)
			}
		}
	}
	if optionNames.Card() == 0 {
		return false
	}

	return config.ShouldRemoveSkusImage(product.Organization.ID, optionNames.ToList())
}
