package product_listing

import (
	"context"

	"cloud.google.com/go/spanner"

	"github.com/AfterShip/gopkg/storage/spannerx"
	"github.com/AfterShip/gopkg/storage/spannerx/sqlbuilder"
	"github.com/AfterShip/gopkg/uuid"

	spanner_util "github.com/AfterShip/pltf-pd-product-listings/internal/utils/spannerx"
)

type auditVersionRepo struct {
	cli *spannerx.Client
}

func (r *auditVersionRepo) list(ctx context.Context, args *repoListAuditVersionArgs) ([]*auditVersionDBModel, error) {
	// sql
	query := sqlbuilder.Model(&auditVersionDBModel{})
	queryParams := map[string]interface{}{}
	if args.ProductListingID != "" {
		query = query.Where(sqlbuilder.Eq("product_listing_id", "@product_listing_id"))
		queryParams["product_listing_id"] = args.ProductListingID
	}
	sql, err := query.Limit(args.Limit).Offset((args.Page - 1) * args.Limit).OrderDesc(tableFieldCreatedAt).ToSQL()
	if err != nil {
		return nil, err
	}

	// do query
	txn := r.cli.Single()
	defer txn.Close()

	stmt := spanner.Statement{
		SQL:    sql,
		Params: queryParams,
	}
	auditVersions := make([]*auditVersionDBModel, 0)
	err = txn.Query(ctx, stmt).Do(func(r *spanner.Row) error {
		model := auditVersionDBModel{}
		defer func() { auditVersions = append(auditVersions, &model) }()
		return r.ToStruct(&model)
	})

	return auditVersions, err
}

func (r *auditVersionRepo) getByID(ctx context.Context, id string) (auditVersionDBModel, error) {
	auditVersion := auditVersionDBModel{}
	cols, err := spanner_util.ParseColumns(&auditVersion)
	if err != nil {
		return auditVersion, err
	}

	txn := r.cli.Single()
	defer txn.Close()

	row, err := txn.ReadRow(ctx, auditVersion.SpannerTable(), spanner.Key{id}, cols)
	if err != nil {
		return auditVersion, err
	}

	return auditVersion, row.ToStruct(&auditVersion)
}

func (r *auditVersionRepo) create(ctx context.Context, model *auditVersionDBModel) error {
	mut, err := r.generateCreateMutation(model)
	if err != nil {
		return err
	}
	_, err = r.cli.Apply(ctx, []*spanner.Mutation{mut})
	return err
}

func (r *auditVersionRepo) generateCreateMutation(model *auditVersionDBModel) (*spanner.Mutation, error) {
	if model.ProductListingAuditVersionID == "" {
		model.ProductListingAuditVersionID = uuid.GenerateUUIDV4()
	}
	model.CreatedAt = spanner.CommitTimestamp
	return spanner.InsertStruct(tableAuditVersion, model)
}

func (r *auditVersionRepo) forceDelete(ctx context.Context, id string) error {
	mut := r.generateForceDeleteMutation(id)
	_, err := r.cli.Apply(ctx, []*spanner.Mutation{mut})
	return err
}

func (r *auditVersionRepo) generateForceDeleteMutation(id string) *spanner.Mutation {
	return spanner.Delete(tableAuditVersion, spanner.Key{id})
}
