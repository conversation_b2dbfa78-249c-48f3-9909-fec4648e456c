package product_listing

import (
	"time"
)

const (
	tableAuditVersion = "product_listing_audit_versions"
)

type auditVersionDBModel struct {
	ProductListingAuditVersionID string    `spanner:"product_listing_audit_version_id"`
	ProductListingID             string    `spanner:"product_listing_id"`
	AuditData                    AuditData `spanner:"audit_data"`
	CreatedAt                    time.Time `spanner:"created_at"`
}

func (model *auditVersionDBModel) SpannerTable() string {
	return tableAuditVersion
}

func (model *auditVersionDBModel) toAuditVersion() AuditVersion {
	return AuditVersion{
		ID:             model.ProductListingAuditVersionID,
		CreatedAt:      model.CreatedAt,
		ProductListing: model.AuditData.ProductListing,
	}
}
