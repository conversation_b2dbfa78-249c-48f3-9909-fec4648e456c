package product_listing

import (
	"context"
	"fmt"
	"regexp"
	"sort"
	"strings"
	"time"

	redsync "github.com/go-redsync/redsync/v4"
	jsoniter "github.com/json-iterator/go"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"

	standard_error "github.com/AfterShip/connectors-errors-sdk-go"
	"github.com/AfterShip/connectors-library/httpx"
	product_listings_api_v1 "github.com/AfterShip/connectors-library/sdks/product_listings"
	"github.com/AfterShip/connectors-library/sdks/products_center"
	connector_lib_utils "github.com/AfterShip/connectors-library/utils"
	"github.com/AfterShip/connectors-library/utils/sets"
	"github.com/AfterShip/connectors-sdk-go/v2/products"
	"github.com/AfterShip/feed-sdk-go/business_events_collector"
	"github.com/AfterShip/feed-sdk-go/events"
	events_sdk "github.com/AfterShip/feed-sdk-go/v1/events"
	"github.com/AfterShip/gopkg/facility/set"
	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/gopkg/log"
	"github.com/AfterShip/gopkg/routine"
	"github.com/AfterShip/pltf-pd-product-listings/internal/third_party/feed"

	"github.com/AfterShip/pltf-pd-product-listings/internal/consts"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/category"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/common/calculators"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/common/databus"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/organization_settings"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/searchable_products"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/settings"
	"github.com/AfterShip/pltf-pd-product-listings/internal/logger"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
	"github.com/AfterShip/pltf-pd-product-listings/internal/third_party/bme"
	products_center_internal "github.com/AfterShip/pltf-pd-product-listings/internal/third_party/products_center"
)

func (s *serviceImpl) GetByID(ctx context.Context, id string) (ProductListing, error) {
	conductorProductListing, err := s.repo.getByID(ctx, id)
	if err != nil {
		return ProductListing{}, err
	}
	listing := convertToProductListing(&conductorProductListing)
	s.overWriteListingVariantPriceAndInventory(ctx, &listing)
	return listing, nil
}

func (s *serviceImpl) Create(ctx context.Context, args *ProductListingArgs) (ProductListing, error) {
	var err error

	if err = s.validate.Struct(args); err != nil {
		return ProductListing{}, err
	}

	pl := ProductListing{}
	pl.SalesChannel = args.SalesChannel
	pl.Organization = args.Organization
	pl.SalesChannelProduct = args.SalesChannelProduct
	pl.ProductsCenterProduct = args.ProductsCenterProduct
	pl.Settings = args.Settings
	pl.Product = args.Product
	pl.Relations = args.Relations
	pl.Version = args.Version
	pl.FeedCustomizationParams = args.FeedCustomizationParams
	pl.ModifyStateAndStatus()

	if pl.SyncStatus == consts.SyncStatusUnsync {
		s.setReadyStatus(ctx, &pl)
	} else {
		pl.ClearReadyStatus()
	}

	removeVariantsImage := shouldRemoveVariantsImagesForEvent(pl.Organization.ID, pl.Product, s.conf.DynamicConfigs.TikTokSyncConfig)
	// 指定白名单内的客户需要清空 variants 的图片
	if removeVariantsImage {
		for i := range pl.Product.Variants {
			pl.Product.Variants[i].ImageURL = ""
		}
	}

	conductorPL := convertToConductorProductListing(&pl)
	err = s.repo.create(ctx, &conductorPL)
	if err != nil {
		return ProductListing{}, err
	}

	pl, err = s.GetByID(ctx, conductorPL.ProductListingDBModel.ProductListingID)
	if err != nil {
		return ProductListing{}, err
	}

	if err = s.esRepo.BatchUpsertProductListings(ctx, []*ProductListing{&pl}); err != nil {
		s.logger.With(zap.String("Id", pl.ID)).WarnCtx(ctx, "Failed to upsert listing data to ES", zap.Error(err))
	}

	return pl, nil
}

func (s *serviceImpl) setReadyStatus(ctx context.Context, pl *ProductListing) error {
	categoryRules := &category.RulesOutput{}
	categoryAttributes := &category.AttributesOutput{}
	var err error
	if len(pl.Product.Categories) > 0 && pl.Product.Categories[0].SalesChannelID != "" {
		categoryRules, err = s.getCategoryRules(ctx, pl.Organization, pl.SalesChannel, pl.Product.Categories)
		if err != nil {
			logger.Get().WarnCtx(ctx, "setReadyStatus failed to get category rules", zap.Error(err))
			return err
		}
		categoryAttributes, err = s.getCategoryAttributes(ctx, pl.Organization, pl.SalesChannel, pl.Product.Categories)
		if err != nil {
			logger.Get().WarnCtx(ctx, "setReadyStatus failed to get category attributes", zap.Error(err))
			return err
		}
	}
	switch pl.SalesChannel.Platform {
	case consts.TikTokShop:
		salesChannelRegionConfig, err := s.conf.LoadChannelRegionConfig(pl.SalesChannel.Platform, pl.SalesChannel.CountryRegion)
		if err != nil {
			return err
		}
		ttsCDNDomainRegExps := s.getTiktokCDNDomainRegexps()
		pl.SetReadyStatus(categoryRules, categoryAttributes, salesChannelRegionConfig, ttsCDNDomainRegExps)
	case consts.Shein:
		pl.SetReadyStatus(categoryRules, categoryAttributes, nil, nil)
	}

	return nil
}

func (s *serviceImpl) Search(ctx context.Context, args *SearchProductListingArgs) ([]*ProductListing, *models.Pagination, error) {
	ids, pagination, err := s.SearchIDs(ctx, args)
	if err != nil {
		return nil, nil, errors.WithStack(err)
	}
	productListings, err := s.listByIDs(ctx, ids)
	if err != nil {
		return nil, nil, errors.WithStack(err)
	}
	return productListings, pagination, nil
}

func (s *serviceImpl) SearchIDs(ctx context.Context, args *SearchProductListingArgs) ([]string, *models.Pagination, error) {
	if err := s.validate.Struct(args); err != nil {
		return nil, nil, errors.WithStack(err)
	}
	if err := args.CustomValidate(); err != nil {
		return nil, nil, err
	}
	if len(args.IDs) > 0 {
		return args.IDs, args.buildPaginationSearchByIDs(), nil
	}

	ids, pagination, err := s.esRepo.SearchProductListingsIDs(ctx, args)
	if err != nil {
		return nil, nil, errors.WithStack(err)
	}
	return ids, pagination, nil
}

func (s *serviceImpl) Count(ctx context.Context, args *SearchProductListingArgs) (int64, error) {
	if err := s.validate.Struct(args); err != nil {
		return 0, errors.WithStack(err)
	}

	count, err := s.esRepo.Count(ctx, args)
	if err != nil {
		return 0, errors.WithStack(err)
	}

	return count, nil
}

func (s *serviceImpl) UpdatePublishState(ctx context.Context, id string, publishArgs *Publish) (ProductListing, error) {
	mutex, err := s.acquireListingLock(ctx, id)
	if err != nil {
		return ProductListing{}, errors.WithStack(err)
	}
	defer func() {
		s.releaseListingLock(ctx, mutex)
	}()

	currentProductListing, err := s.GetByID(ctx, id)
	if err != nil {
		return ProductListing{}, err
	}

	if !s.validatePublishState(currentProductListing.Publish.State, publishArgs.State) {
		return ProductListing{}, ErrInvalidPublishState
	}
	if currentProductListing.Publish.State == consts.PublishStateRunning &&
		currentProductListing.Publish.LastReferenceID != publishArgs.LastReferenceID {
		return ProductListing{}, ErrUpdatePublicationByRefID
	}

	// get old product listing db model
	plDBModel, err := s.repo.plRepo.getByID(ctx, id)
	if err != nil {
		return ProductListing{}, err
	}

	// set new data to product listing db modef
	plDBModel.PublishState = publishArgs.State
	plDBModel.PublishLastReferenceID = publishArgs.LastReferenceID
	plDBModel.PublishErrorCode = publishArgs.Error.Code
	plDBModel.PublishErrorMsg = publishArgs.Error.Msg

	if publishArgs.State == consts.PublishStateFailed {
		plDBModel.PublishLastFailedAt = time.Now()
	}

	err = s.repo.plRepo.update(ctx, &plDBModel)
	if err != nil {
		return ProductListing{}, err
	}

	pl, err := s.GetByID(ctx, id)
	if err != nil {
		return ProductListing{}, err
	}

	if err = s.esRepo.BatchUpsertProductListings(ctx, []*ProductListing{&pl}); err != nil {
		s.logger.With(zap.String("Id", pl.ID)).WarnCtx(ctx, "save es error", zap.Error(err))
	}

	return pl, nil
}

func (s *serviceImpl) List(ctx context.Context, arg *ListArg) ([]*ProductListing, error) {
	var (
		conductorProductListings []*conductorProductListing
		err                      error
	)

	if arg.IDs != "" {
		conductorProductListings, err = s.repo.listByIDs(ctx, strings.Split(arg.IDs, ","))
	} else {
		conductorProductListings, err = s.repo.list(ctx, arg.buildRepoListArgs())
	}
	if err != nil {
		return nil, err
	}
	productListings := make([]*ProductListing, 0, len(conductorProductListings))
	for _, repo := range conductorProductListings {
		productListing := convertToProductListing(repo)
		s.overWriteListingVariantPriceAndInventory(ctx, &productListing)
		productListings = append(productListings, &productListing)
	}

	return productListings, nil
}

func (s *serviceImpl) listByIDs(ctx context.Context, ids []string) ([]*ProductListing, error) {
	conductorProductListings, err := s.repo.listByIDs(ctx, ids)
	if err != nil {
		return nil, err
	}
	// Create a map where the key is the product listing ID and the value is the product listing
	productListingMap := make(map[string]*ProductListing)
	for _, listing := range conductorProductListings {
		pl := convertToProductListing(listing)
		s.overWriteListingVariantPriceAndInventory(ctx, &pl)
		productListingMap[pl.ID] = &pl
	}

	// Create the result slice
	productListings := make([]*ProductListing, 0, len(conductorProductListings))

	// Iterate over the queried ids and append the corresponding product listing from the map to the result
	for _, id := range ids {
		if pl, ok := productListingMap[id]; ok {
			productListings = append(productListings, pl)
		}
	}

	return productListings, nil
}

func (s *serviceImpl) UpdateAuditState(ctx context.Context, id string, args *Audit) (ProductListing, error) {
	lastProductListing, err := s.GetByID(ctx, id)
	if err != nil {
		return ProductListing{}, err
	}

	lastProductListing.Audit = *args
	if lastProductListing.Audit.State == consts.AuditStateReviewing &&
		lastProductListing.State == consts.ProductListingProductStatePending {
		lastProductListing.State = consts.ProductListingProductStateReviewing
	}

	plDBModel := convertToProductListingDBModel(&lastProductListing)
	err = s.repo.plRepo.update(ctx, &plDBModel)
	if err != nil {
		return ProductListing{}, err
	}

	pl, err := s.GetByID(ctx, id)
	if err != nil {
		return ProductListing{}, err
	}

	// 记录 listing 日志
	s.saveModifyActivityLog(ctx, &lastProductListing, &pl)

	if err = s.esRepo.BatchUpsertProductListings(ctx, []*ProductListing{&pl}); err != nil {
		s.logger.With(zap.String("Id", pl.ID)).WarnCtx(ctx, "save es error", zap.Error(err))
	}

	return pl, nil
}

func (s *serviceImpl) ListAuditVersions(ctx context.Context, args *ListAuditVersionsArgs) ([]*AuditVersion, error) {
	repos, err := s.repo.plAuditVersionRepo.list(ctx, &repoListAuditVersionArgs{
		ProductListingID: args.ProductListingID,
		Page:             args.Page,
		Limit:            args.Limit,
	})
	if err != nil {
		return nil, err
	}

	auditVersions := make([]*AuditVersion, 0)
	for _, repo := range repos {
		auditVersion := repo.toAuditVersion()
		auditVersions = append(auditVersions, &auditVersion)
	}

	return auditVersions, nil
}

func (s *serviceImpl) ListRelations(ctx context.Context, args *ListRelationsArgs) ([]*ProductListingRelation, error) {
	if err := s.validate.Struct(args); err != nil {
		return nil, err
	}
	repos, err := s.repo.plRelationRepo.list(ctx, &repoListRelationArgs{
		OrganizationID:                    args.OrganizationID,
		SalesChannelPlatform:              args.SalesChannelPlatform,
		SalesChannelStoreKey:              args.SalesChannelStoreKey,
		SourcePlatform:                    args.SourcePlatform,
		SourceStoreKey:                    args.SourceStoreKey,
		SalesChannelProductID:             args.SalesChannelProductID,
		SalesChannelProductIDs:            args.SalesChannelProductIDs,
		SalesChannelVariantID:             args.SalesChannelVariantID,
		SalesChannelVariantIDs:            args.SalesChannelVariantIDs,
		ProductsCenterProductID:           args.ProductsCenterProductID,
		ProductsCenterVariantID:           args.ProductsCenterVariantID,
		ProductsCenterConnectorProductIDs: args.ProductsCenterConnectorProductIDs,
		IncludeDeleted:                    args.IncludeDeleted,
		LinkStatus:                        args.LinkStatus,
		SyncStatus:                        args.SyncStatus,
		Page:                              args.Page,
		Limit:                             args.Limit,
	})
	if err != nil {
		return nil, err
	}
	relations := make([]*ProductListingRelation, 0)
	for _, repo := range repos {
		relation := repo.toProductListingRelation()
		relations = append(relations, &relation)
	}

	return relations, nil
}

func (s *serviceImpl) Duplicate(ctx context.Context, id string) (ProductListing, error) {
	pl, err := s.GetByID(ctx, id)
	if err != nil {
		return ProductListing{}, err
	}

	args := convertToProductListingArgsForDuplicate(&pl)
	// create new product listing
	listing, err := s.Create(ctx, &args)
	if err != nil {
		return ProductListing{}, err
	}

	// refresh the product listing es data
	s.esRepo.RefreshESIndex(ctx)

	return listing, nil
}

func (s *serviceImpl) Delete(ctx context.Context, id string, force bool) error {
	pl, err := s.GetByID(ctx, id)
	if err != nil {
		return err
	}
	return s.delete(ctx, &pl, force)
}

func (s *serviceImpl) delete(ctx context.Context, pl *ProductListing, force bool) error {
	if !pl.canDelete() && !force {
		return ErrStateInDelete
	}

	err := s.repo.forceDelete(ctx, pl.ID)
	if err != nil {
		return err
	}

	// delete the product listing es data
	if err = s.esRepo.DeleteProductListings(ctx, []*ProductListing{pl}); err != nil {
		s.logger.With(zap.String("Id", pl.ID)).WarnCtx(ctx, "save es error", zap.Error(err))
	}

	if err = s.notifySalesChannelUpsertEvent(ctx, pl); err != nil {
		s.logger.WarnCtx(ctx, "send relation upsert to pub/sub failed",
			zap.String("connector_product_id", pl.ProductsCenterProduct.ConnectorProductID),
			zap.Error(err))
	}

	if force {
		s.logger.InfoCtx(ctx, "force delete product listing",
			zap.String("product_listing_id", pl.ID),
			zap.String("listing_model", connector_lib_utils.GetJsonIndent(pl)),
		)
	}

	return nil
}

func (s *serviceImpl) validatePublishState(current, target consts.PublishState) bool {
	if current == "" || current == target {
		return true
	}

	targetStates := map[consts.PublishState]struct{}{}
	switch current {
	case consts.PublishStatePending:
		targetStates[consts.PublishStateBlocked] = struct{}{}
		targetStates[consts.PublishStateRunning] = struct{}{}
		targetStates[consts.PublishStateIgnored] = struct{}{}
	case consts.PublishStateRunning:
		targetStates[consts.PublishStateFailed] = struct{}{}
		targetStates[consts.PublishStateSucceeded] = struct{}{}
		targetStates[consts.PublishStateBlocked] = struct{}{}
		targetStates[consts.PublishStateIgnored] = struct{}{}
	case consts.PublishStateFailed:
		targetStates[consts.PublishStatePending] = struct{}{}
		targetStates[consts.PublishStateRunning] = struct{}{}
	case consts.PublishStateSucceeded:
		targetStates[consts.PublishStatePending] = struct{}{}
		targetStates[consts.PublishStateRunning] = struct{}{}
	case consts.PublishStateIgnored:
		targetStates[consts.PublishStatePending] = struct{}{}
		targetStates[consts.PublishStateRunning] = struct{}{}
	case consts.PublishStateBlocked:
		targetStates[consts.PublishStatePending] = struct{}{}
		targetStates[consts.PublishStateRunning] = struct{}{}
	}

	_, ok := targetStates[target]
	return ok
}

func (s *serviceImpl) getStoreSetting(ctx context.Context, args *settings.SearchSettingArgs) (*settings.Setting, error) {
	storeSettings, err := s.settingService.List(ctx, args)
	if err != nil {
		return nil, err
	}

	if len(storeSettings) == 0 {
		return nil, ErrSettingNotFound
	}

	return storeSettings[0], nil
}

func (s *serviceImpl) getUnionSettingByListing(ctx context.Context, listing *ProductListing) (*storeProductListingSetting, error) {
	arg := &settings.SearchSettingArgs{
		OrganizationID:       listing.Organization.ID,
		SalesChannelPlatform: listing.SalesChannel.Platform,
		SalesChannelStoreKey: listing.SalesChannel.StoreKey,
	}
	if listing.ProductsCenterProduct.Source.Platform != "" && listing.ProductsCenterProduct.Source.StoreKey != "" {
		arg.SourceAppPlatform = listing.ProductsCenterProduct.Source.Platform
		arg.SourceAppKey = listing.ProductsCenterProduct.Source.StoreKey
	}
	setting, err := s.getStoreSetting(ctx, arg)

	if err != nil {
		return nil, err
	}

	return newStoreProductListingSetting(*setting, listing.Settings), nil
}

// nolint:dupl
func (s *serviceImpl) getCategoryRules(ctx context.Context,
	organization models.Organization, salesChannel models.SalesChannel, categories []*models.SalesChannelResource) (*category.RulesOutput, error) {
	if len(categories) == 0 {
		return nil, ErrListingProductCategoryNotSet
	}
	if categories[0].SalesChannelID == "" {
		return nil, ErrListingProductCategoryNotSet
	}
	rules, err := s.categoryService.GetCategoryRules(ctx, &category.GetRulesArg{
		OrganizationID:       organization.ID,
		SalesChannelPlatform: salesChannel.Platform,
		SalesChannelStoreKey: salesChannel.StoreKey,
		ExternalCategoryID:   categories[0].SalesChannelID,
		SalesChannelRegion:   salesChannel.CountryRegion,
	})
	return &rules, err
}

// nolint:dupl
func (s *serviceImpl) getCategoryAttributes(ctx context.Context,
	organization models.Organization, salesChannel models.SalesChannel, categories []*models.SalesChannelResource) (*category.AttributesOutput, error) {
	if len(categories) == 0 {
		return nil, ErrListingProductCategoryNotSet
	}
	if categories[0].SalesChannelID == "" {
		return nil, ErrListingProductCategoryNotSet
	}
	attributes, err := s.categoryService.GetCategoryAttributes(ctx, &category.GetAttributesArg{
		OrganizationID:       organization.ID,
		SalesChannelPlatform: salesChannel.Platform,
		SalesChannelStoreKey: salesChannel.StoreKey,
		ExternalCategoryID:   categories[0].SalesChannelID,
		AllType:              true,
	})

	return &attributes, err
}

func (s *serviceImpl) latestAuditVersion(ctx context.Context, id string) (*AuditVersion, error) {
	auditVersions, err := s.ListAuditVersions(ctx, &ListAuditVersionsArgs{
		ProductListingID: id,
		Page:             1,
		Limit:            1,
	})
	if err != nil {
		return nil, err
	}
	if len(auditVersions) == 0 {
		return nil, models.ErrResourceNotFound
	}

	auditVersion := auditVersions[0]
	// 增加 products center product exist validate
	productIDs := sets.StringSet{}
	if auditVersion.ProductListing.ProductsCenterProduct.ID != "" {
		productIDs.Add(auditVersion.ProductListing.ProductsCenterProduct.ID)
	}
	for i := range auditVersion.ProductListing.Relations {
		if auditVersion.ProductListing.Relations[i].ProductsCenterVariant.ProductID != "" {
			productIDs.Add(auditVersion.ProductListing.Relations[i].ProductsCenterVariant.ProductID)
		}
	}

	if len(productIDs.ToList()) > 0 {
		products, err := s.productsCenterClient.Product.List(ctx, &products_center.GetProductsArgs{
			OrganizationID: auditVersion.ProductListing.Organization.ID,
			IDs:            strings.Join(productIDs.ToList(), ","),
			Page:           1,
			Limit:          200,
		})

		if err != nil {
			return nil, err
		}

		productMaps := make(map[string]set.StringSet)
		for i := range products {
			variantIDs := set.StringSet{}
			for j := range products[i].Variants {
				variantIDs.Add(products[i].Variants[j].ID)
			}
			productMaps[products[i].ID] = variantIDs
		}

		// 处理 match
		if auditVersion.ProductListing.ProductsCenterProduct.ID != "" {
			// 如果 products center product 不存在，清空 product listing 中 match 关系
			if _, ok := productMaps[auditVersion.ProductListing.ProductsCenterProduct.ID]; !ok {
				s.logger.InfoCtx(ctx, "products center product not found from get last audit version",
					zap.String("productID", auditVersion.ProductListing.ProductsCenterProduct.ID))
				auditVersion.ProductListing.ProductsCenterProduct = ProductsCenterProduct{}
			}
		}

		// 处理 relation
		for i := range auditVersion.ProductListing.Relations {
			if auditVersion.ProductListing.Relations[i].ProductsCenterVariant.ProductID == "" {
				continue
			}

			variantIDs, ok := productMaps[auditVersion.ProductListing.Relations[i].ProductsCenterVariant.ProductID]
			// 如果 products center product 不存在，清空 relation
			if !ok {
				s.logger.InfoCtx(ctx, "products center product not found from get last audit version",
					zap.String("productID", auditVersion.ProductListing.Relations[i].ProductsCenterVariant.ProductID),
				)
				auditVersion.ProductListing.Relations[i].ProductsCenterVariant = ProductsCenterVariant{}
				continue
			}

			// 如果 products center variant 不存在，清空 relation
			if !variantIDs.Contains(auditVersion.ProductListing.Relations[i].ProductsCenterVariant.ID) {
				s.logger.InfoCtx(ctx, "products center product variant not found from get last audit version",
					zap.String("productID", auditVersion.ProductListing.Relations[i].ProductsCenterVariant.ProductID),
					zap.String("variantID", auditVersion.ProductListing.Relations[i].ProductsCenterVariant.ID),
				)
				auditVersion.ProductListing.Relations[i].ProductsCenterVariant = ProductsCenterVariant{}
			}
		}
	}

	return auditVersion, nil
}

func (s *serviceImpl) createAuditVersion(ctx context.Context, id string, arg *AuditVersion, needCompare bool) error {
	model := arg.convertToAuditVersionDBModel(id)

	if needCompare {
		lastAuditVersion, err := s.latestAuditVersion(ctx, id)
		if err != nil &&
			!errors.Is(err, models.ErrResourceNotFound) {
			return err
		}

		if !errors.Is(err, models.ErrResourceNotFound) {
			// 比较当前 auditVersion 和最新的 auditVersion 是否一致，不需要创建新的 auditVersion 发起刊登任务
			listingCompareModel := newCompareModel(&lastAuditVersion.ProductListing, &model.AuditData.ProductListing,
				compareSetting{}, s.getTiktokCDNDomainRegexps())
			if !listingCompareModel.needPublish() {
				return ErrSameAuditVersion
			}
		}
	}

	// check audit version relation id is empty, if empty return error
	for i := range model.AuditData.ProductListing.Relations {
		if model.AuditData.ProductListing.Relations[i].ID == "" {
			return ErrAuditVersionRelationIDIsEmpty
		}
	}

	return s.repo.plAuditVersionRepo.create(ctx, &model)
}

func (s *serviceImpl) GeneratePreview(ctx context.Context, pl *ProductListing) (*ProductListing, error) {
	var err error
	if err = s.validate.Struct(pl); err != nil {
		return nil, err
	}

	pcp := new(products_center.Product)
	if pl.matched() {
		// Get the matched products center product
		pcp, err = s.productsCenterClient.Product.GetByID(ctx, pl.ProductsCenterProduct.ID)
		if err != nil {
			apiErr := &httpx.APIError{}
			if errors.As(err, &apiErr) && apiErr.MetaCode == 40400 {
				return nil, errors.Wrap(ErrProductsCenterProductNotFound, err.Error())
			}
			return nil, err
		}
	}

	storeSetting, err := s.getStoreSettingByListing(ctx, pl)
	if err != nil {
		if errors.Is(err, ErrNotMatchedAndLinkedProduct) {
			return pl, nil
		}
		return nil, err
	}

	return s.newPreviewModel(pl, storeSetting, pcp, s.conf.DynamicConfigs.AmazonOptionNameMapping).generatePreview(ctx)
}

func (s *serviceImpl) EditAttributes(ctx context.Context, id string, args *EditAttributesArgs) (ProductListing, error) {
	pl, err := s.GetByID(ctx, id)
	if err != nil {
		return ProductListing{}, err
	}

	switch pl.State {
	case consts.ProductListingProductStatePending:
		return s.editAttributesForPending(ctx, pl, args)
	case consts.ProductListingProductStateActive, consts.ProductListingProductStatePartiallyActive:
		return s.editAttributesForActive(ctx, pl, args)
	}

	return ProductListing{}, errors.Wrap(ErrEditAttributesNotAllowUpdate, fmt.Sprintf("invalid product listing state: %s", pl.State))
}

func (s *serviceImpl) editAttributesForPending(ctx context.Context, pl ProductListing, args *EditAttributesArgs) (ProductListing, error) {
	// modify product listings category
	if args.CategorySourceID != "" {
		pl.Product.Categories = []*models.SalesChannelResource{
			{
				SalesChannelID: args.CategorySourceID,
			},
		}
	}

	var err error
	categoryRules := &category.RulesOutput{}
	categoryAttributes := &category.AttributesOutput{}
	if len(pl.Product.Categories) > 0 && pl.Product.Categories[0].SalesChannelID != "" {
		categoryRules, err = s.getCategoryRules(ctx, pl.Organization, pl.SalesChannel, pl.Product.Categories)
		if err != nil {
			return ProductListing{}, err
		}

		categoryAttributes, err = s.getCategoryAttributes(ctx, pl.Organization, pl.SalesChannel, pl.Product.Categories)
		if err != nil {
			return ProductListing{}, err
		}
	}

	salesChannelRegionConfig, err := s.conf.LoadChannelRegionConfig(pl.SalesChannel.Platform, pl.SalesChannel.CountryRegion)
	if err != nil {
		return ProductListing{}, err
	}

	if err = s.repo.update(ctx, args.buildConductorUpdateArgs(&pl,
		categoryRules, categoryAttributes, salesChannelRegionConfig, s.getTiktokCDNDomainRegexps())); err != nil {
		return ProductListing{}, err
	}

	pl, err = s.GetByID(ctx, pl.ID)
	if err != nil {
		return ProductListing{}, err
	}

	if err = s.esRepo.BatchUpsertProductListings(ctx, []*ProductListing{&pl}); err != nil {
		s.logger.With(zap.String("Id", pl.ID)).WarnCtx(ctx, "save es error", zap.Error(err))
	}

	return pl, nil
}

// editAttributesForActive 不会直接修改 db model, 仅会创建 一个新的 audit version 和 刊登任务
func (s *serviceImpl) editAttributesForActive(ctx context.Context, pl ProductListing, args *EditAttributesArgs) (ProductListing, error) {
	mutex, err := s.acquireListingLock(ctx, pl.ID)
	if err != nil {
		return ProductListing{}, err
	}
	defer s.releaseListingLock(ctx, mutex)

	oldListing := pl.DeepCopy()

	if args.CategorySourceID != "" {
		pl.Product.Categories = []*models.SalesChannelResource{
			{
				SalesChannelID: args.CategorySourceID,
			},
		}
	}

	categoryRules := &category.RulesOutput{}
	categoryAttributes := &category.AttributesOutput{}
	if len(pl.Product.Categories) > 0 && pl.Product.Categories[0].SalesChannelID != "" {
		categoryRules, err = s.getCategoryRules(ctx, pl.Organization, pl.SalesChannel, pl.Product.Categories)
		if err != nil {
			return ProductListing{}, err
		}

		categoryAttributes, err = s.getCategoryAttributes(ctx, pl.Organization, pl.SalesChannel, pl.Product.Categories)
		if err != nil {
			return ProductListing{}, err
		}
	}

	salesChannelRegionConfig, err := s.conf.LoadChannelRegionConfig(pl.SalesChannel.Platform, pl.SalesChannel.CountryRegion)
	if err != nil {
		return ProductListing{}, err
	}
	newListing := args.buildUpdateListing(&pl, categoryRules, categoryAttributes, salesChannelRegionConfig, s.getTiktokCDNDomainRegexps())

	listingCompareModel := newCompareModel(newListing, oldListing, compareSetting{
		ignoreCompareTitle:        true,
		ignoreCompareMedia:        true,
		ignoreCompareDescription:  true,
		ignoreCompareVariant:      true,
		ignoreCompareVariantImage: true,
		ignoreCompareBarcode:      true,
	}, s.getTiktokCDNDomainRegexps())

	auditVersion := listingCompareModel.buildAuditVersion()
	if err := s.createAuditVersion(ctx, newListing.ID, auditVersion, false); err != nil {
		return ProductListing{}, err
	}

	if err := s.createPublishTask(ctx, newListing); err != nil {
		return ProductListing{}, err
	}

	return *oldListing, nil
}

// 刊登任务
func (s *serviceImpl) createPublishTask(ctx context.Context, listing *ProductListing) error {
	singlePublishTask := SinglePublishTask{
		Logger:    s.logger,
		Validator: s.validate,
	}

	arg, err := singlePublishTask.BuildTaskArgs(ctx, &PublishTaskInput{
		OrganizationID:    listing.Organization.ID,
		ProductListingIDs: []string{listing.ID},
		SalesChannel:      listing.SalesChannel,
	})

	if err != nil {
		return err
	}

	result, err := s.taskService.Create(ctx, &arg)
	if err != nil {
		return err
	}

	s.logger.InfoCtx(ctx, "Create publish task success",
		zap.String("product_listing_id", listing.ID),
		zap.String("task_id", result.ID))

	return nil
}

// nolint:dupl
func (s *serviceImpl) createDeleteTask(ctx context.Context, listing *ProductListing) error {
	task := StatusTask{
		Logger:    s.logger,
		Validator: s.validate,
	}

	arg, err := task.BuildTaskArgs(ctx, &StatusTaskInput{
		OrganizationID:    listing.Organization.ID,
		ProductListingIDs: []string{listing.ID},
		SalesChannel:      listing.SalesChannel,
		GroupName:         consts.DeleteProductListings,
	})

	if err != nil {
		return err
	}

	result, err := s.taskService.Create(ctx, &arg)
	if err != nil {
		return err
	}

	s.logger.InfoCtx(ctx, "Create delete task success",
		zap.String("product_listing_id", listing.ID),
		zap.String("task_id", result.ID))

	return nil
}

// 刊登价格任务
func (s *serviceImpl) createPublishPricesTask(ctx context.Context, arg *priceTaskArg) error {
	// 自定义设置，并且不同步价格
	if arg.Listing.Settings.PriceSyncSetting.Preference == consts.SettingPreferenceCustomized &&
		arg.Listing.Settings.PriceSyncSetting.Customized.AutoSync == consts.AllowSyncDisabled.String() {
		return nil
	}

	if !arg.Listing.canSyncPrice() {
		s.logger.InfoCtx(ctx, "listing state is not active, skip publish prices task",
			zap.String("from_event", arg.FromEvent), zap.String("product_listing_id", arg.Listing.ID))
		return nil
	}

	task := PublishPricesTask{
		Logger: s.logger,
	}

	taskInput := arg.BuildTaskInput()

	if len(taskInput.ProductListingRelations) == 0 {
		return nil
	}

	createTaskArg, err := task.BuildTaskArgs(ctx, taskInput)

	if err != nil {
		return err
	}

	result, err := s.taskService.Create(ctx, &createTaskArg)
	if err != nil {
		return err
	}
	s.logger.InfoCtx(ctx, "Create publish prices task success",
		zap.String("product_listing_id", arg.Listing.ID),
		zap.String("task_id", result.ID),
		zap.String("from event", arg.FromEvent),
	)

	return nil
}

func (s *serviceImpl) createPublishInventoriesTask(ctx context.Context, arg *InventoriesTaskArg) error {
	// 自定义设置，并且不同步库存
	if arg.Listing.Settings.InventorySyncSetting.Preference == consts.SettingPreferenceCustomized &&
		arg.Listing.Settings.InventorySyncSetting.Customized.AutoSync == consts.AllowSyncDisabled.String() {
		return nil
	}

	if !arg.Listing.canSyncInventory() {
		s.logger.InfoCtx(ctx, "listing state is deleted, skip publish inventories task",
			zap.String("from_event", arg.FromEvent), zap.String("product_listing_id", arg.Listing.ID))
		return nil
	}

	task := PublishInventoriesTask{
		Logger: s.logger,
	}

	taskInput := arg.BuildTaskInput()

	if len(taskInput.ProductListingRelations) == 0 {
		return nil
	}

	createTaskArg, err := task.BuildTaskArgs(ctx, taskInput)

	if err != nil {
		return err
	}

	result, err := s.taskService.Create(ctx, &createTaskArg)
	if err != nil {
		return err
	}

	s.logger.InfoCtx(ctx, "Create publish inventories task success",
		zap.String("product_listing_id", arg.Listing.ID),
		zap.Strings("clear_stock_relation_ids", arg.ClearRelationIDs),
		zap.String("from event", arg.FromEvent),
		zap.String("task_id", result.ID),
		zap.String("task_input", connector_lib_utils.GetJsonIndent(taskInput)))

	return nil
}

func (s *serviceImpl) AggregateCategoryIDs(ctx context.Context, args *SearchProductListingArgs) ([]string, error) {
	return s.esRepo.AggregateCategoryIDs(ctx, args)
}

// nolint:funlen,gocyclo
func (s *serviceImpl) AutoLink(ctx context.Context, arg *AutoLinkArg) (ProductListing, error) {
	// v2 dry_run, v1&v2都会返回 relations, v1会真正执行, 要比对 v1/v2 的结果
	if err := s.validate.Struct(arg); err != nil {
		return ProductListing{}, errors.WithStack(err)
	}
	productListing, err := s.GetByID(ctx, arg.ID)
	if err != nil {
		return ProductListing{}, errors.WithStack(err)
	}

	if productListing.SalesChannel.Platform != consts.TikTokShop {
		return ProductListing{}, errors.New("auto link only support tiktok shop")
	}

	if lo.Contains(productListing.Product.ProductTypes, consts.ProductTypeBundles) {
		return ProductListing{}, ErrorBundlesProductNotAllowAutoLink
	}

	// 验证状态是否可以更新
	if !productListing.canUpdate() {
		return ProductListing{}, ErrPublishStateInUpdate
	}

	bothConnections, err := s.connectorService.GetBothConnections(ctx, productListing.Organization.ID)
	if err != nil {
		return ProductListing{}, errors.WithStack(err)
	}
	if !bothConnections.IsBothConnectionsWithSaleChannel(productListing.SalesChannel) {
		return ProductListing{}, ErrBothConnectionIsNotFound
	}

	setting, err := s.getStoreSetting(ctx, &settings.SearchSettingArgs{
		OrganizationID:       productListing.Organization.ID,
		SalesChannelStoreKey: productListing.SalesChannel.StoreKey,
		SalesChannelPlatform: productListing.SalesChannel.Platform,
		SourceAppKey:         bothConnections.App.Key,
		SourceAppPlatform:    bothConnections.App.Platform,
	})
	if err != nil {
		return ProductListing{}, errors.WithStack(err)
	}
	if !setting.AutoLink.Enabled() {
		return ProductListing{}, ErrAutoLinkIsDisabled
	}
	unionSetting := newStoreProductListingSetting(*setting, productListing.Settings)
	if productListing.matched() && unionSetting.autoSyncVariant() {
		return ProductListing{}, ErrProductListingNotAllowLink
	}

	result, err := s.autoLinkV2(ctx, &autoLinkListing{
		app:                    bothConnections.App,
		productListing:         productListing,
		targetVariantIDs:       arg.TargetVariantIDs,
		recommendedPCProductID: arg.RecommendedPCProductID,
	})
	if err != nil {
		return ProductListing{}, errors.WithStack(err)
	}
	return result, nil
}

// autoLinkV2 更多的匹配方式, 不仅仅是全局sku搜索匹配
// nolint:funlen,gocyclo
func (s *serviceImpl) autoLinkV2(ctx context.Context, arg *autoLinkListing) (ProductListing, error) {
	/**
	1. 准备好需要 auto link 的 relations
	*/
	sortProductsCenterIDs := make([]string, 0)
	relationProductsCenterIDsSet := set.NewStringSet()
	if arg.productListing.ProductsCenterProduct.ID != "" {
		relationProductsCenterIDsSet.Add(arg.productListing.ProductsCenterProduct.ID)
		sortProductsCenterIDs = append(sortProductsCenterIDs, arg.productListing.ProductsCenterProduct.ID)
	}
	// 最早关联的 product 优先进行 auto link
	sort.Slice(arg.productListing.Relations, func(i, j int) bool {
		return arg.productListing.Relations[i].CreatedAt.Before(arg.productListing.Relations[j].CreatedAt)
	})
	needAutoLinkVariantIDsSet := set.NewStringSet()
	needAutoLinkRelations := make([]*ProductListingRelation, 0)
	for _, relation := range arg.productListing.Relations {
		if relation.IsDeleted() {
			continue
		}
		if relation.ProductsCenterVariant.ProductID != "" && !relationProductsCenterIDsSet.Contains(relation.ProductsCenterVariant.ProductID) {
			sortProductsCenterIDs = append(sortProductsCenterIDs, relation.ProductsCenterVariant.ProductID)
		}
		relationProductsCenterIDsSet.Add(relation.ProductsCenterVariant.ProductID)
		if relation.LinkStatus == consts.LinkStatusLinked || relation.SyncStatus == consts.SyncStatusUnsync {
			continue
		}
		if arg.isOnlyTarget() && !arg.isTargetVariant(relation.ProductListingVariantID) {
			continue
		}
		needAutoLinkRelations = append(needAutoLinkRelations, relation)
		needAutoLinkVariantIDsSet.Add(relation.ProductListingVariantID)
	}

	if len(needAutoLinkRelations) == 0 {
		return ProductListing{}, ErrNoValidVariantsToAutoLink
	}

	/**
	2. 小范围内进行 auto link 流程
		- 未关联的 product, 搜索一批 title 相同的 product 进行 match auto link
		- 有关联的 product, 从 relation-products 中进行 auto link
	*/
	var result *autoLinkMappingResult
	var err error
	if len(sortProductsCenterIDs) == 0 {
		// 未关联, 进行 match 流程
		result, err = s.autoLinkByNoRelatedProduct(ctx, arg.app, &arg.productListing)
	} else {
		// 有关联, 进行 scope products link 流程
		result, err = s.autoLinkByHaveRelateProducts(ctx, arg.app, &arg.productListing, sortProductsCenterIDs)
	}
	if err != nil {
		return ProductListing{}, errors.WithStack(err)
	}

	/**
	3. 小范围内 auto link 完成后, 对剩余未 auto link 的 sku 进行 全局sku auto link
	*/
	unmappingRelations := result.filterUsefulMappingAndReturnUnmappingRelations(needAutoLinkRelations)
	if len(unmappingRelations) > 0 {
		// global sku_name auto link
		globalResult, err := s.globalSKUNameAutoLink(ctx, arg.app, &arg.productListing, unmappingRelations)
		if err != nil {
			return ProductListing{}, errors.WithStack(err)
		}
		result.mergeResult(globalResult)
	}

	/**
	4. external_id 进行 auto-link
	*/
	unmappingRelations = result.filterUsefulMappingAndReturnUnmappingRelations(needAutoLinkRelations)
	if len(unmappingRelations) > 0 {
		externalIDResult, err := s.referenceIDAutoLink(ctx, arg.app, &arg.productListing, unmappingRelations)
		if err != nil {
			return ProductListing{}, errors.WithStack(err)
		}

		// referenceID 匹配成功, 但不能直接应用, 需要灰度验证通过后才能应用
		if externalIDResult != nil && len(externalIDResult.ListingsVariantIDsMapping) > 0 {
			result.mergeResult(externalIDResult)
			s.logger.InfoCtx(ctx, "external_id auto link result",
				zap.String("unmapping_relations", connector_lib_utils.GetJsonIndent(unmappingRelations)),
				zap.String("external_id_result", connector_lib_utils.GetJsonIndent(externalIDResult)),
				zap.String("product_listing_id", arg.productListing.ID),
			)
		}
	}

	/**
	5. 参数有传入 pc_product_id, 会对该 product 进行 auto link [来源 pcp_event, 避免 ES 写入延迟加入的步骤]
		这一步只能处理 events.AutoLinkFailedByNotFindSKU 的 relations
	*/
	unmappingRelations = result.filterTargetFailed(needAutoLinkRelations, events.AutoLinkFailedByNotFindSKU)
	if len(unmappingRelations) > 0 && arg.recommendedPCProductID != "" {
		pcProductIDAutoLinkResult, err := s.pcProductIDAutoLink(ctx, arg.recommendedPCProductID, arg.app, &arg.productListing, unmappingRelations)
		if err != nil {
			return ProductListing{}, errors.WithStack(err)
		}
		result.mergeResult(pcProductIDAutoLinkResult)
	}

	linkFailed := false
	defer func() {
		if linkFailed || arg.productListing.SalesChannelProduct.State != consts.SalesChannelProductStateLive {
			return
		}
		s.collectAutoLinkBusinessMonitorData(ctx, &arg.productListing, needAutoLinkVariantIDsSet.ToList(), result)
	}()

	// auto link 结果为空
	if len(result.ListingsVariantIDsMapping) == 0 {
		return ProductListing{}, ErrNoValidVariantsToAutoLink
	}

	// 通过 result 更新 productListing, 然后进行 update-link
	linkArg := LinkArg{
		ProductListingID: arg.productListing.ID,
	}
	for listingVariantID, pcVariant := range result.ListingsVariantIDsMapping {
		if !needAutoLinkVariantIDsSet.Contains(listingVariantID) {
			continue
		}
		linkArg.LinkedVariants = append(linkArg.LinkedVariants, LinkedVariantArg{
			ProductListingVariantID:        listingVariantID,
			ProductsCenterProductID:        pcVariant.ProductCenterProductID,
			ProductsCenterProductVariantID: pcVariant.ProductCenterProductVariantID,
		})
	}

	ctx = context.WithValue(ctx, consts.AutoLinkPassKey, true)

	newListing, err := s.LinkOrUnlink(ctx, &linkArg)
	if err != nil {
		s.logger.InfoCtx(ctx, "auto link error",
			zap.String("organization_id", arg.productListing.Organization.ID),
			zap.String("product_listing_id", arg.productListing.ID),
			zap.String("link_arg", connector_lib_utils.GetJsonIndent(arg)),
			zap.Error(err))
		linkFailed = true
		return ProductListing{}, errors.WithStack(err)
	}

	return newListing, nil
}

// autoLinkByNoRelatedProduct 未关联的 listing 进行 auto link
func (s *serviceImpl) autoLinkByNoRelatedProduct(ctx context.Context, app models.App, productListing *ProductListing) (*autoLinkMappingResult, error) {
	result := &autoLinkMappingResult{
		ListingsVariantIDsMapping:           make(map[string]linkPcProductVariant),
		ListingsVariantIDsLinkFailedMapping: make(map[string]string),
	}

	if productListing.Product.Title == "" {
		return result, nil
	}

	searchableProducts, _, err := s.searchableProductService.Search(ctx, &searchable_products.SearchArgs{
		OrganizationID: productListing.Organization.ID,
		SourcePlatform: app.Platform,
		SourceStoreKey: app.Key,
		ProductTitle:   productListing.Product.Title,
		Status:         []string{string(consts.ProductsCenterProductPublishStateActive)},
		SourceTypes:    []string{consts.ProductSourceTypeIntegration},
		Page:           1,
		Limit:          10,
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}

	for _, product := range searchableProducts {
		if product.Title != productListing.Product.Title { // must eq
			continue
		}
		pcProduct, err := s.productsCenterClient.Product.GetByID(ctx, product.ID)
		if err != nil {
			apiErr := &httpx.APIError{}
			if errors.As(err, &apiErr) && apiErr.MetaCode == 40400 {
				continue
			}
			return nil, err
		}

		// filter inactive variants
		filterProducts := s.whitelistFilterProductsInActiveVariants(ctx, []*products_center.Product{pcProduct})
		pcProduct = filterProducts[0] // sure index

		// 检测当前 product 是否能 match 上 listingProduct
		variantsMatchResult, ok := productListing.autoLinkAllSKUMatchCheck(pcProduct)
		if !ok {
			continue
		}
		// all sku match
		for listingVariantID, pcVariantID := range variantsMatchResult {
			result.ListingsVariantIDsMapping[listingVariantID] = linkPcProductVariant{
				ProductCenterProductID:        pcProduct.ID,
				ProductCenterProductVariantID: pcVariantID,
				AutoLinkSuccessReason:         events.AutoLinkSuccessByNoRelationAllMatch,
			}
		}
		break
	}

	return result, nil
}

// autoLinkByHaveRelateProducts 有关联的 listing 进行 auto link
// nolint:funlen,gocyclo
func (s *serviceImpl) autoLinkByHaveRelateProducts(ctx context.Context, app models.App,
	productListing *ProductListing, relationProductsCenterProductIDs []string) (*autoLinkMappingResult, error) {

	result := &autoLinkMappingResult{
		ListingsVariantIDsMapping:           make(map[string]linkPcProductVariant),
		ListingsVariantIDsLinkFailedMapping: make(map[string]string),
	}

	products, err := s.productsCenterClient.Product.List(ctx, &products_center.GetProductsArgs{
		OrganizationID: productListing.Organization.ID,
		IDs:            strings.Join(relationProductsCenterProductIDs, ","),
		Page:           1,
		Limit:          len(relationProductsCenterProductIDs),
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}

	// filter inactive variants
	products = s.whitelistFilterProductsInActiveVariants(ctx, products)

	// title 和 productListing.title 相等则排在前面，如果不相等就用原排序
	productsIndexMap := make(map[string]int)
	for index, id := range relationProductsCenterProductIDs {
		productsIndexMap[id] = index
	}
	sort.Slice(products, func(i, j int) bool {
		if products[i].Title == productListing.Product.Title {
			return true
		}
		if products[j].Title == productListing.Product.Title {
			return false
		}
		return productsIndexMap[products[i].ID] < productsIndexMap[products[j].ID]
	})

	// sku auto link
	for _, pcProduct := range products {
		if pcProduct.Source.App.Platform != app.Platform || pcProduct.Source.App.Key != app.Key {
			continue
		}
		matchResult, ok := productListing.autoLinkSKUMatchCheck(pcProduct)
		if !ok {
			continue
		}
		for listingVariantID, pcVariantID := range matchResult {
			if _, alreadyMapping := result.ListingsVariantIDsMapping[listingVariantID]; !alreadyMapping {
				result.ListingsVariantIDsMapping[listingVariantID] = linkPcProductVariant{
					ProductCenterProductID:        pcProduct.ID,
					ProductCenterProductVariantID: pcVariantID,
					AutoLinkSuccessReason:         events.AutoLinkSuccessByRelationSKUMatch,
				}
			}
		}
	}
	// option auto link
	for _, pcProduct := range products {
		if pcProduct.Source.App.Platform != app.Platform || pcProduct.Source.App.Key != app.Key {
			continue
		}
		matchResult, ok := productListing.autoLinkOptionsMatchCheck(pcProduct)
		if !ok {
			continue
		}
		for listingVariantID, pcVariantID := range matchResult {
			if _, alreadyMapping := result.ListingsVariantIDsMapping[listingVariantID]; !alreadyMapping {
				result.ListingsVariantIDsMapping[listingVariantID] = linkPcProductVariant{
					ProductCenterProductID:        pcProduct.ID,
					ProductCenterProductVariantID: pcVariantID,
					AutoLinkSuccessReason:         events.AutoLinkSuccessByRelationOptionMatch,
				}
			}
		}
	}

	return result, nil
}

// globalSKUNameAutoLink 全局 sku_name auto link
// nolint:funlen,gocyclo
func (s *serviceImpl) globalSKUNameAutoLink(ctx context.Context, app models.App,
	productListing *ProductListing, relations []*ProductListingRelation) (*autoLinkMappingResult, error) {

	result := &autoLinkMappingResult{
		ListingsVariantIDsMapping:           make(map[string]linkPcProductVariant),
		ListingsVariantIDsLinkFailedMapping: make(map[string]string),
	}

	targetVariantIDSet := set.NewStringSet()
	for _, relation := range relations {
		targetVariantIDSet.Add(relation.ProductListingVariantID)
	}

	channelSKUsSet := set.NewStringSet()
	sameChannelSKUs := productListing.CollectSameSKUCodes()
	channelSKURelationVariantID := make(map[string]string)    // sku -> variantID
	channelSKURelationVariantIDs := make(map[string][]string) // sku -> variantIDs [only self same sku check]
	for _, listingVariant := range productListing.Product.Variants {
		if !targetVariantIDSet.Contains(listingVariant.ID) {
			continue
		}
		if listingVariant.Sku == "" {
			result.ListingsVariantIDsLinkFailedMapping[listingVariant.ID] = events.AutoLinkFailedByEmptySKU
			continue
		}
		channelSKUsSet.Add(listingVariant.Sku)
		channelSKURelationVariantID[listingVariant.Sku] = listingVariant.ID
		channelSKURelationVariantIDs[listingVariant.Sku] = append(channelSKURelationVariantIDs[listingVariant.Sku], listingVariant.ID)
	}
	needToAutoLinkChannelSKUsSet := channelSKUsSet.Diff(set.NewStringSet(sameChannelSKUs...))

	for _, sameSKU := range sameChannelSKUs {
		for _, variantID := range channelSKURelationVariantIDs[sameSKU] {
			result.ListingsVariantIDsLinkFailedMapping[variantID] = events.AutoLinkFailedBySelfSameSKU
		}
	}

	if needToAutoLinkChannelSKUsSet.Card() == 0 {
		return result, nil
	}

	channelSKURelationEcommerceVariantInfo, err := s.getEcommerceVariantsBySKU(ctx, getEcommerceVariants{
		OrganizationID: productListing.Organization.ID,
		AppPlatform:    app.Platform,
		AppKey:         app.Key,
		SKUs:           needToAutoLinkChannelSKUsSet.ToList(),
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}

	for _, channelSKU := range needToAutoLinkChannelSKUsSet.ToList() {
		if channelSKURelationEcommerceVariantInfo[channelSKU].hitCount == 0 {
			result.ListingsVariantIDsLinkFailedMapping[channelSKURelationVariantID[channelSKU]] = events.AutoLinkFailedByNotFindSKU
			continue
		}
		if channelSKURelationEcommerceVariantInfo[channelSKU].hitCount > 1 {
			result.ListingsVariantIDsLinkFailedMapping[channelSKURelationVariantID[channelSKU]] = events.AutoLinkFailedByFindMultipleSKU
			continue
		}
		ecommerceVariantInfo := channelSKURelationEcommerceVariantInfo[channelSKU]
		result.ListingsVariantIDsMapping[channelSKURelationVariantID[channelSKU]] = linkPcProductVariant{
			ProductCenterProductID:        ecommerceVariantInfo.productsCenterProductID,
			ProductCenterProductVariantID: ecommerceVariantInfo.ProductsCenterVariantID,
			AutoLinkSuccessReason:         events.AutoLinkSuccessByGlobalSKUMatch,
		}
	}

	return result, nil
}

func (s *serviceImpl) referenceIDAutoLink(ctx context.Context, app models.App,
	productListing *ProductListing, relations []*ProductListingRelation) (*autoLinkMappingResult, error) {

	result := &autoLinkMappingResult{
		ListingsVariantIDsMapping: make(map[string]linkPcProductVariant),
	}

	if productListing.SalesChannel.Platform != consts.TikTokShop {
		return result, nil
	}

	channelConnectorProduct, err := s.connectorService.GetProductByID(ctx, productListing.SalesChannelProduct.ConnectorProductID, "")
	if err != nil {
		return nil, errors.WithStack(err)
	}

	// 是否拥有有效的 variant referenceID
	isValidReferenceID := false
	for _, variant := range channelConnectorProduct.Variants {
		if variant.ExternalReferenceID.String() != "" {
			isValidReferenceID = true
		}
	}
	// 没有 ReferenceID || ReferenceID已被listings覆盖
	if !isValidReferenceID ||
		channelConnectorProduct.ExternalReferenceID.String() == "" ||
		channelConnectorProduct.ExternalReferenceID.String() == productListing.ID {
		return result, nil
	}

	// 使用 connector 搜索, products_center 没有存储 external_product_id
	sourceCNTProducts, err := s.connectorService.GetProducts(ctx, &products.GetProductsParams{
		OrganizationID: productListing.Organization.ID,
		AppPlatform:    app.Platform,
		AppKey:         app.Key,
		ExternalIDs:    channelConnectorProduct.ExternalReferenceID.String(),
		Published:      "true",
		Page:           1,
		Limit:          1,
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if len(sourceCNTProducts) == 0 {
		return result, nil
	}

	// 准备 source product 侧数据
	matchSourceCNTProduct := sourceCNTProducts[0]
	matchSourceCNTProductVariants := make(map[string]products.ModelsResponseProductVariant)
	for index := range matchSourceCNTProduct.Variants {
		matchSourceCNTProductVariants[matchSourceCNTProduct.Variants[index].ExternalID.String()] =
			matchSourceCNTProduct.Variants[index]
	}

	// 收集 channel product variant id 对应的 source product variant id
	channelVariantMatchSourceCNTVariant := make(map[string]products.ModelsResponseProductVariant)
	for _, variant := range channelConnectorProduct.Variants {
		if variant.ExternalReferenceID.String() == "" {
			continue
		}
		if matchSourceVariant, ok := matchSourceCNTProductVariants[variant.ExternalReferenceID.String()]; ok {
			channelVariantMatchSourceCNTVariant[variant.ExternalID.String()] = matchSourceVariant
		}
	}

	if len(channelVariantMatchSourceCNTVariant) == 0 {
		return result, nil
	}

	// 准备 pc_product
	pcProducts, err := s.productsCenterClient.Product.List(ctx, &products_center.GetProductsArgs{
		OrganizationID:       productListing.Organization.ID,
		AppPlatform:          app.Platform,
		AppKey:               app.Key,
		ConnectorsProductIDs: matchSourceCNTProduct.ID.String(),
		Page:                 1,
		Limit:                1,
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if len(pcProducts) == 0 {
		s.logger.ErrorCtx(ctx, "connector product not found in products center", zap.String("connector_product_id", channelConnectorProduct.ID.String()))
		return result, nil
	}

	// filter inactive variants
	filterProducts := s.whitelistFilterProductsInActiveVariants(ctx, pcProducts)
	pcProduct := filterProducts[0] // sure index

	for _, relation := range relations {
		matchCNTVariant, ok := channelVariantMatchSourceCNTVariant[relation.SalesChannelVariant.ID]
		if !ok {
			continue
		}
		var matchPcVariant *products_center.Variant
		for index := range pcProduct.Variants {
			if pcProduct.Variants[index].SourceVariantID == matchCNTVariant.ExternalID.String() {
				matchPcVariant = &pcProduct.Variants[index]
				break
			}
		}
		if matchPcVariant != nil {
			result.ListingsVariantIDsMapping[relation.ProductListingVariantID] = linkPcProductVariant{
				ProductCenterProductID:        pcProduct.ID,
				ProductCenterProductVariantID: matchPcVariant.ID,
				AutoLinkSuccessReason:         events.AutoLinkSuccessByReferenceID,
			}
		} else {
			// cnt variant 找不到对应的 pc variant, error log
			s.logger.ErrorCtx(ctx, "cnt variant not found in products center",
				zap.String("products_center_product_id", pcProduct.ID),
				zap.String("connector_product_id", matchSourceCNTProduct.ID.String()),
				zap.String("connector_variant_id", matchCNTVariant.ID.String()),
			)
		}
	}

	return result, nil
}

// pcProductIDAutoLink source_product_event 触发的 auto link, 指定了 pc_product_id
func (s *serviceImpl) pcProductIDAutoLink(ctx context.Context, pcProductID string, app models.App,
	productListing *ProductListing, relations []*ProductListingRelation) (*autoLinkMappingResult, error) {

	pcProduct, err := s.productsCenterClient.Product.GetByID(ctx, pcProductID)
	if err != nil {
		apiErr := &httpx.APIError{}
		if errors.As(err, &apiErr) && apiErr.MetaCode == 40400 {
			return nil, nil
		} else {
			return nil, errors.WithStack(err)
		}
	}

	if pcProduct.Source.App.Platform != app.Platform || pcProduct.Source.App.Key != app.Key {
		return nil, nil
	}

	if pcProduct.Status != string(consts.ProductsCenterProductPublishStateActive) {
		return nil, nil
	}

	// sure index
	pcProduct = s.whitelistFilterProductsInActiveVariants(ctx, []*products_center.Product{pcProduct})[0]

	pcProductSKUVariantMap := make(map[string]products_center.Variant)
	for _, variant := range pcProduct.Variants {
		if variant.Sku == "" {
			continue
		}
		if _, ok := pcProductSKUVariantMap[variant.Sku]; ok {
			return nil, nil // source_product 有重复 sku 不进行 auto link
		}
		pcProductSKUVariantMap[variant.Sku] = variant
	}

	targetVariantIDSet := set.NewStringSet()
	for _, relation := range relations {
		targetVariantIDSet.Add(relation.ProductListingVariantID)
	}

	result := &autoLinkMappingResult{
		ListingsVariantIDsMapping: make(map[string]linkPcProductVariant),
	}

	if len(pcProductSKUVariantMap) == 0 || targetVariantIDSet.Card() == 0 {
		return nil, nil
	}

	someSKUCodesSet := set.NewStringSet(productListing.CollectSameSKUCodes()...)
	for _, variant := range productListing.Product.Variants {
		if !targetVariantIDSet.Contains(variant.ID) {
			continue
		}
		if variant.Sku == "" || someSKUCodesSet.Contains(variant.Sku) {
			continue
		}
		pcVariant, ok := pcProductSKUVariantMap[variant.Sku]
		if !ok {
			continue
		}
		result.ListingsVariantIDsMapping[variant.ID] = linkPcProductVariant{
			ProductCenterProductID:        pcProduct.ID,
			ProductCenterProductVariantID: pcVariant.ID,
			AutoLinkSuccessReason:         events.AutoLinkSuccessBySourceProductEvent,
		}
	}

	return result, nil
}

// collectAutoLinkBusinessMonitorData 收集业务监控数据
//
//nolint:gocyclo
func (s *serviceImpl) collectAutoLinkBusinessMonitorData(ctx context.Context, linkPreviousListing *ProductListing,
	wantLinkVariantIDs []string, linkResult *autoLinkMappingResult) {

	if linkPreviousListing == nil || len(wantLinkVariantIDs) == 0 || linkResult == nil || linkResult.ListingsVariantIDsMapping == nil {
		return
	}

	var autoLinkFrom string
	if value, _ := ctx.Value(consts.AutoLinkFromKey).(string); value == consts.AutoLinkFromSettingBatchTask {
		autoLinkFrom = events.AutoLinkTriggerSourceByAPI
	} else if value == consts.AutoLinkFromSourceProductEvent {
		autoLinkFrom = events.AutoLinkTriggerSourceBySourceProductEvent
	} else if value == consts.AutoLinkFromBlockedOrderCompensation {
		autoLinkFrom = events.AutoLinkTriggerBlockedOrderCompensation
	} else {
		autoLinkFrom = events.AutoLinkTriggerSourceByChannelProductEvent
	}

	reportArg := autoLinkResultReportArg{
		listing:        linkPreviousListing,
		triggerSource:  autoLinkFrom,
		variantResults: make(map[string]autoLinkVariantResult, len(wantLinkVariantIDs)),
	}
	for _, variantID := range wantLinkVariantIDs {
		if result, ok := linkResult.ListingsVariantIDsMapping[variantID]; ok {
			reportArg.variantResults[variantID] = autoLinkVariantResult{
				success: true,
				reason:  result.AutoLinkSuccessReason,
			}
		} else {
			autoLinkFailedReason := linkResult.ListingsVariantIDsLinkFailedMapping[variantID]
			if autoLinkFailedReason == "" {
				autoLinkFailedReason = events.AutoLinkFailedByUnknown
			}
			reportArg.variantResults[variantID] = autoLinkVariantResult{
				success: false,
				reason:  autoLinkFailedReason,
			}
		}
	}

	// 上报数据
	s.saveAutoLinkActivityLog(ctx, reportArg)
}

func (s *serviceImpl) saveAutoLinkActivityLog(ctx context.Context, reportArg autoLinkResultReportArg) {

	// 上报 activity log & clickhouse
	for variantID, variantResult := range reportArg.variantResults {
		bizLinkEventInput := events.ListingAutoLinkEvent{
			CreateEventBaseInfo: events.CreateEventBaseInfo{
				ProductCode:      "product-listings",
				OrganizationID:   reportArg.listing.Organization.ID,
				AppPlatform:      reportArg.listing.ProductsCenterProduct.Source.Platform,
				AppKey:           reportArg.listing.ProductsCenterProduct.Source.StoreKey,
				ChannelPlatform:  reportArg.listing.SalesChannel.Platform,
				ChannelKey:       reportArg.listing.SalesChannel.StoreKey,
				ResourceID:       variantID,
				EventTimestamp:   time.Now(),
				MerchantVisible:  false,
				ExecutionSucceed: variantResult.success,
				ExpiredDays:      180,
			},
			TriggerSource:    reportArg.triggerSource,
			Reason:           variantResult.reason,
			ProductListingID: reportArg.listing.ID,
		}
		if err := s.feedEventsService.ListingLinkEventsService.AutoLinkEvent(ctx, bizLinkEventInput); err != nil {
			s.logger.ErrorCtx(ctx, "auto link failed to collect activity log", zap.Error(err))
		}

		var autoLinkStatus consts.BizStatus
		if variantResult.success {
			autoLinkStatus = "succeeded"
		} else {
			autoLinkStatus = "failed"
		}

		s.businessMonitoringExporter.RecordService.Recording(bme.Logs{
			Organization: product_listings_api_v1.Organization{
				ID: reportArg.listing.Organization.ID,
			},
			SalesChannel: product_listings_api_v1.SalesChannel{
				StoreKey: reportArg.listing.SalesChannel.StoreKey,
				Platform: reportArg.listing.SalesChannel.Platform,
			},
			Source: product_listings_api_v1.Source{
				App: product_listings_api_v1.App{
					Key:      reportArg.listing.ProductsCenterProduct.Source.StoreKey,
					Platform: reportArg.listing.ProductsCenterProduct.Source.Platform,
				},
			},
			Biz: bme.Biz{
				SourceID:  reportArg.listing.ID + "#" + variantID,
				Event:     consts.BizEvent(business_events_collector.AutoLinkResult.String()),
				StageName: consts.BizStageName(reportArg.triggerSource),
				Status:    autoLinkStatus,
				Message:   consts.BizMessage(variantResult.reason),
			},
		})

		var businessResult business_events_collector.BizResult
		if variantResult.success {
			businessResult = business_events_collector.Succeeded
		} else {
			businessResult = business_events_collector.Failed
		}
		if err := s.businessMonitoringExporter.RecordService.CollectBusinessEvents(&business_events_collector.BizEventInput{
			Type: business_events_collector.AutoLinkResult,
			BizPayloads: business_events_collector.BizPayloads{
				LinkRateEvent: &business_events_collector.LinkRateEventReq{
					OrganizationID:       reportArg.listing.Organization.ID,
					AppPlatform:          reportArg.listing.ProductsCenterProduct.Source.Platform,
					AppKey:               reportArg.listing.ProductsCenterProduct.Source.StoreKey,
					SalesChannelPlatform: reportArg.listing.SalesChannel.Platform,
					SalesChannelStoreKey: reportArg.listing.SalesChannel.StoreKey,
					ResourceID:           reportArg.listing.ID + "#" + variantID,
					Result:               businessResult,
					AutoLinkRecordExtensions: &business_events_collector.AutoLinkRecordExtensions{
						ReasonFroResult: variantResult.reason,
						From:            reportArg.triggerSource,
					},
				},
			},
		}); err != nil {
			s.logger.ErrorCtx(ctx, "failed to report auto link data event", zap.Error(err))
		}
	}
}

// nolint:funlen,gocyclo,maintidx
func (s *serviceImpl) LinkOrUnlink(ctx context.Context, arg *LinkArg) (ProductListing, error) {
	if err := s.validate.Struct(arg); err != nil {
		return ProductListing{}, errors.WithStack(err)
	}
	toUpdateListings, err := s.GetByID(ctx, arg.ProductListingID)
	if err != nil {
		return ProductListing{}, errors.WithStack(err)
	}
	lastListing := toUpdateListings.DeepCopy()
	// 验证状态是否可以更新
	if !toUpdateListings.canUpdate() {
		return ProductListing{}, ErrPublishStateInUpdate
	}
	copyOldProductListing := toUpdateListings.DeepCopy()

	productsCenterProductIDsSet := set.NewStringSet()
	productListingVariantIDRelationLinkArgMap := make(map[string]LinkedVariantArg)
	for _, cur := range arg.LinkedVariants {
		if cur.DoLink() {
			productsCenterProductIDsSet.Add(cur.ProductsCenterProductID)
		}
		productListingVariantIDRelationLinkArgMap[cur.ProductListingVariantID] = cur
	}

	productsCenterProductMap := make(map[string]*products_center.Product)
	if productsCenterProductIDsSet.Card() > 0 {
		productsCenterProducts, err := s.productsCenterClient.Product.List(ctx, &products_center.GetProductsArgs{
			OrganizationID: toUpdateListings.Organization.ID,
			IDs:            strings.Join(productsCenterProductIDsSet.ToList(), ","),
		})
		if err != nil {
			return ProductListing{}, errors.WithStack(err)
		}
		if len(productsCenterProducts) == 0 {
			return ProductListing{}, errors.WithStack(ErrProductsCenterProductNotFound)
		}

		for index := range productsCenterProducts {
			product := productsCenterProducts[index]
			productsCenterProductMap[product.ID] = product
		}
	}

	unLinkRelationIDs := make([]string, 0)
	haveNewLinkRelations := false // 是否有新的关联关系
	// 构建 relation 关系
	for index := range toUpdateListings.Relations {
		// 调用者传入的数据中没有这个 variant_id, 不处理
		linkArg, ok := productListingVariantIDRelationLinkArgMap[toUpdateListings.Relations[index].ProductListingVariantID]
		if !ok {
			continue
		}
		if linkArg.DoUnlink() {
			if toUpdateListings.Relations[index].IsLinkedAndSynced() {
				unLinkRelationIDs = append(unLinkRelationIDs, toUpdateListings.Relations[index].ID)
			}
			toUpdateListings.Relations[index].Unlink()
			continue
		}
		// 已经 linked 不报错, 可以进行覆盖 link
		pcProduct, ok := productsCenterProductMap[linkArg.ProductsCenterProductID]
		if !ok {
			return ProductListing{}, errors.WithStack(errors.Wrapf(ErrProductsCenterProductNotFound, "pc_product_id: %s", linkArg.ProductsCenterProductID))
		}

		var variant *products_center.Variant
		for inIndex := range pcProduct.Variants {
			if pcProduct.Variants[inIndex].ID == linkArg.ProductsCenterProductVariantID {
				variant = &pcProduct.Variants[inIndex]
				break
			}
		}

		if variant == nil {
			return ProductListing{}, errors.WithStack(errors.Wrapf(ErrNoProductsCenterVariantID,
				"pc_product_id: %s ;pc_product_variant_id: %s", linkArg.ProductsCenterProductID, linkArg.ProductsCenterProductVariantID))
		}

		// relations 没发生变化, 就不去更新
		if linkArg.ProductsCenterProductID == toUpdateListings.Relations[index].ProductsCenterVariant.ID &&
			linkArg.ProductsCenterProductVariantID == toUpdateListings.Relations[index].ProductsCenterVariant.Source.ID {
			continue
		}
		haveNewLinkRelations = true

		sourceProductID := pcProduct.Source.ID
		if variant.SourceProductID != "" {
			sourceProductID = variant.SourceProductID
		}
		toUpdateListings.Relations[index].ProductsCenterVariant = ProductsCenterVariant{
			ProductID:          linkArg.ProductsCenterProductID,
			ConnectorProductID: pcProduct.ConnectorsProductID,
			ID:                 linkArg.ProductsCenterProductVariantID,
			Source: ProductsCenterVariantSource{
				StoreKey:  pcProduct.Source.App.Key,
				Platform:  pcProduct.Source.App.Platform,
				ID:        variant.SourceVariantID,
				ProductID: sourceProductID,
				Sku:       variant.Sku,
			},
		}
	}

	toUpdateListings.ModifyStateAndStatus()

	updateArg := convertToConductorUpdateArgs(&toUpdateListings, copyOldProductListing)

	// listings 是 match && enabled_auto_sync_variants, variants 会自行对齐
	setting, err := s.getUnionSettingByListing(ctx, lastListing)
	if err != nil {
		return ProductListing{}, errors.WithStack(err)
	}
	if haveNewLinkRelations && lastListing.matched() && setting.autoSyncVariant() {
		return ProductListing{}, ErrProductListingNotAllowLink
	}

	err = s.repo.update(ctx, &updateArg)
	if err != nil {
		return ProductListing{}, errors.WithStack(err)
	}
	newListing, err := s.GetByID(ctx, arg.ProductListingID)
	if err != nil {
		return ProductListing{}, errors.WithStack(err)
	}

	if err = s.esRepo.BatchUpsertProductListings(ctx, []*ProductListing{&newListing}); err != nil {
		s.logger.With(zap.String("Id", newListing.ID)).WarnCtx(ctx, "save es error", zap.Error(err))
	}

	if haveNewLinkRelations {
		if err = s.createPublishPricesTask(ctx, &priceTaskArg{
			Listing:   &newListing,
			FromEvent: FromEventListingLinkVariant,
		}); err != nil {
			s.logger.ErrorCtx(ctx, "Create publish prices task error", zap.Error(err))
		}

		if err = s.createPublishInventoriesTask(ctx, &InventoriesTaskArg{
			Listing:   &newListing,
			FromEvent: FromEventListingLinkVariant,
			Enforced:  true,
		}); err != nil {
			s.logger.ErrorCtx(ctx, "Create publish inventories task error", zap.Error(err))
		}
	}

	if len(unLinkRelationIDs) > 0 {
		s.logger.InfoCtx(ctx, "unlink relations",
			zap.String("product_listing_id", newListing.ID),
			zap.String("organization_id", newListing.Organization.ID),
			zap.String("relation_ids", strings.Join(unLinkRelationIDs, ",")))
	}

	// 记录 listing 日志
	s.saveVariantActivityLog(ctx, lastListing, &newListing)

	return newListing, nil
}

// 获取 channel_sku 对应的 ecommerce_product
func (s *serviceImpl) getEcommerceVariantsBySKU(ctx context.Context, arg getEcommerceVariants) (map[string]linkEcommerceVariantInfo, error) {
	if err := s.validate.Struct(arg); err != nil {
		return nil, errors.WithStack(err)
	}

	needSKUsSet := set.NewStringSet(arg.SKUs...)
	channelSKURelationEcommerceVariantInfo := make(map[string]linkEcommerceVariantInfo)
	searchSearchableProductArg := &searchable_products.SearchArgs{
		OrganizationID: arg.OrganizationID,
		SourceStoreKey: arg.AppKey,
		SourcePlatform: arg.AppPlatform,
		SourceTypes:    []string{consts.ProductSourceTypeIntegration},
		SKUs:           arg.SKUs,
		Status:         []string{string(consts.ProductsCenterProductPublishStateActive)},
		Page:           1,
		Limit:          100,
	}
	for loop := 1; loop <= 5; loop++ {
		searchableProducts, pagination, err := s.searchableProductService.Search(ctx, searchSearchableProductArg)
		if err != nil {
			return nil, errors.WithStack(err)
		}

		// filter inactive variants
		searchableProducts = s.whitelistFilterSearchableInActiveVariants(ctx, searchableProducts)

		for outIndex := range searchableProducts {
			for index := range searchableProducts[outIndex].Variants {
				variant := searchableProducts[outIndex].Variants[index]
				if needSKUsSet.Contains(variant.Sku) {
					value := channelSKURelationEcommerceVariantInfo[variant.Sku]
					value.hitCount++
					value.productsCenterProductID = searchableProducts[outIndex].ProductsCenterID
					value.ProductsCenterVariantID = variant.ProductsCenterVariantID
					channelSKURelationEcommerceVariantInfo[variant.Sku] = value
				}
			}
		}

		if !pagination.HasNextPage {
			break
		}
		searchSearchableProductArg.Cursor = pagination.NextCursor
	}

	return channelSKURelationEcommerceVariantInfo, nil
}

func (s *serviceImpl) saveEventActivityLog(ctx context.Context, listing *ProductListing, fromEvent string, err error) {
	if listing == nil || listing.ID == "" {
		return
	}

	event := events.ListingCommonEvent{
		CreateEventBaseInfo: events.CreateEventBaseInfo{
			ProductCode:      "product-listings",
			OrganizationID:   listing.Organization.ID,
			AppPlatform:      listing.ProductsCenterProduct.Source.Platform,
			AppKey:           listing.ProductsCenterProduct.Source.StoreKey,
			ChannelPlatform:  listing.SalesChannel.Platform,
			ChannelKey:       listing.SalesChannel.StoreKey,
			ResourceID:       listing.ID,
			EventTimestamp:   time.Now(),
			MerchantVisible:  false,
			ExpiredDays:      60,
			ExecutionSucceed: true,
		},
		Type: fromEvent,
	}

	if err != nil {
		event.ExecutionSucceed = false
		standardErr := &standard_error.Error{}
		if ok := errors.As(err, &standardErr); ok {
			event.FailedReasonMessageCode = fmt.Sprint(standardErr.StandardErrCode())
		} else {
			event.FailedReasonMessageCode = err.Error()
		}
	}

	if err := s.feedEventsService.ListingEventsService.CommonEvent(ctx, event); err != nil {
		s.logger.ErrorCtx(ctx, "save event activity log error", zap.Error(err))
	}
}

// nolint:gocyclo
func (s *serviceImpl) saveModifyActivityLog(ctx context.Context, lastListing, listing *ProductListing) {
	if lastListing == nil || listing == nil || listing.ID == "" || lastListing.ID == "" {
		return
	}

	// 首次 review 通过, audit 是 success 并且 sales channel id 为空，或者 sales channel product state 是 pending
	if s.isFirstReviewPass(ctx, lastListing, listing) {
		if err := s.businessMonitoringExporter.RecordService.CollectBusinessEvents(&business_events_collector.BizEventInput{
			Type: business_events_collector.FirstReviewPass,
			BizPayloads: business_events_collector.BizPayloads{
				AuditProductEvent: &business_events_collector.AuditProductEventReq{
					OrganizationID:       listing.Organization.ID,
					AppPlatform:          listing.ProductsCenterProduct.Source.Platform,
					AppKey:               listing.ProductsCenterProduct.Source.StoreKey,
					SalesChannelPlatform: listing.SalesChannel.Platform,
					SalesChannelStoreKey: listing.SalesChannel.StoreKey,
					ProductListingID:     listing.ID,
					FinishAt:             types.MakeDatetime(time.Now()),
					Result:               business_events_collector.Succeeded,
				},
			},
		}); err != nil {
			s.logger.WarnCtx(ctx, "failed to report audit product event", zap.Error(err))
		}
	}

	modifyEvents := make([]events.ListingModifyStateEvent, 0)
	// review 变更
	if listing.Audit.State != lastListing.Audit.State {
		modifyEvents = append(modifyEvents, events.ListingModifyStateEvent{
			Type:      events.TypeListingReviewModify,
			LastState: lastListing.Audit.State.String(),
			NewState:  listing.Audit.State.String(),
		})
	}

	// match 变更
	if listing.ProductsCenterProduct.ID != lastListing.ProductsCenterProduct.ID {
		event := events.ListingModifyStateEvent{
			Type:      events.TypeListingMatchModify,
			LastState: consts.MatchStateUnmatch.String(),
			NewState:  consts.MatchStateMatched.String(),
		}
		if listing.ProductsCenterProduct.ID == "" {
			event.NewState = consts.MatchStateUnmatch.String()
		}
		if lastListing.ProductsCenterProduct.ID != "" {
			event.LastState = consts.MatchStateMatched.String()
		}
		modifyEvents = append(modifyEvents, event)
	}

	// state 变更
	if listing.State != lastListing.State {
		modifyEvents = append(modifyEvents, events.ListingModifyStateEvent{
			Type:      events.TypeListingStateModify,
			LastState: lastListing.State.String(),
			NewState:  listing.State.String(),
		})
	}

	createEventBaseInfo := events.CreateEventBaseInfo{
		ProductCode:      "product-listings",
		OrganizationID:   listing.Organization.ID,
		AppPlatform:      listing.ProductsCenterProduct.Source.Platform,
		AppKey:           listing.ProductsCenterProduct.Source.StoreKey,
		ChannelPlatform:  listing.SalesChannel.Platform,
		ChannelKey:       listing.SalesChannel.StoreKey,
		ResourceID:       listing.ID,
		EventTimestamp:   time.Now(),
		MerchantVisible:  true,
		ExpiredDays:      60,
		ExecutionSucceed: true,
	}

	for i := range modifyEvents {
		modifyEvents[i].CreateEventBaseInfo = createEventBaseInfo
		if err := s.feedEventsService.ListingEventsService.ModifyStateEvent(ctx, modifyEvents[i]); err != nil {
			s.logger.ErrorCtx(ctx, "save event activity log error", zap.Error(err))
		}
	}
}

func (s *serviceImpl) isFirstReviewPass(ctx context.Context, lastListing, listing *ProductListing) bool {

	if listing == nil || lastListing == nil {
		return false
	}

	switch listing.SalesChannel.Platform {
	case consts.TikTokShop:
		if listing.Audit.State == consts.AuditStateSucceeded &&
			listing.SalesChannelProduct.ID != "" &&
			(lastListing.SalesChannelProduct.ID == "" || lastListing.SalesChannelProduct.State == consts.SalesChannelProductStatePending) {
			return true
		}
		return false
	case consts.Shein:
		// last listings all reviewing
		mainOption, ok := lastListing.Product.GetMainOption()
		if !ok {
			return false
		}
		allReviewing := true
		for _, valueDetail := range mainOption.ValueDetails {
			allReviewing = allReviewing && (valueDetail.State == consts.ProductOptionValueStateReviewing)
		}
		if !allReviewing {
			return false
		}

		return listing.Audit.State == consts.AuditStateSucceeded
	}

	return false
}

// nolint:gocyclo
// saveChannelVariantInitActivityLog 只有 channel create product event 才会走到这里
func (s *serviceImpl) saveChannelVariantInitActivityLog(ctx context.Context, listing *ProductListing) {
	if listing == nil || len(listing.Product.Variants) == 0 || listing.matched() {
		return
	}

	// 上报至 click house && data
	for _, relation := range listing.Relations {
		s.businessMonitoringExporter.RecordService.Recording(bme.Logs{
			Organization: product_listings_api_v1.Organization{
				ID: listing.Organization.ID,
			},
			SalesChannel: product_listings_api_v1.SalesChannel{
				StoreKey: listing.SalesChannel.StoreKey,
				Platform: listing.SalesChannel.Platform,
			},
			Source: product_listings_api_v1.Source{
				App: product_listings_api_v1.App{
					Key:      listing.ProductsCenterProduct.Source.StoreKey,
					Platform: listing.ProductsCenterProduct.Source.Platform,
				},
			},
			Biz: bme.Biz{
				SourceID:  listing.ID + "#" + relation.ProductListingVariantID,
				Event:     consts.BizEvent(business_events_collector.SKULinkRecord.String()),
				CreatedAt: relation.CreatedAt,
				Status:    consts.BizStatus(business_events_collector.SKULinkActionInit),
			},
		})

		if err := s.businessMonitoringExporter.RecordService.CollectBusinessEvents(&business_events_collector.BizEventInput{
			Type: business_events_collector.SKULinkRecord,
			BizPayloads: business_events_collector.BizPayloads{
				LinkRateEvent: &business_events_collector.LinkRateEventReq{
					OrganizationID:       listing.Organization.ID,
					AppPlatform:          listing.ProductsCenterProduct.Source.Platform,
					AppKey:               listing.ProductsCenterProduct.Source.StoreKey,
					SalesChannelPlatform: listing.SalesChannel.Platform,
					SalesChannelStoreKey: listing.SalesChannel.StoreKey,
					ResourceID:           listing.ID + "#" + relation.ProductListingVariantID,
					FinishAt:             types.MakeDatetime(time.Now()),
					Result:               business_events_collector.Succeeded,
					SKULinkRecordExtensions: &business_events_collector.SKULinkRecordExtensions{
						SKUCreatedAt: types.MakeDatetime(relation.CreatedAt),
						Action:       business_events_collector.SKULinkActionInit,
					},
				},
			},
		}); err != nil {
			s.logger.ErrorCtx(ctx, "failed to report new sku link result event", zap.Error(err))
		}
	}

}

// nolint:gocyclo
func (s *serviceImpl) saveVariantActivityLog(ctx context.Context, lastListing, listing *ProductListing) {
	if lastListing == nil || listing == nil || listing.ID == "" || lastListing.ID == "" {
		return
	}

	createEventBaseInfo := events.CreateEventBaseInfo{
		ProductCode:      "product-listings",
		OrganizationID:   listing.Organization.ID,
		AppPlatform:      listing.ProductsCenterProduct.Source.Platform,
		AppKey:           listing.ProductsCenterProduct.Source.StoreKey,
		ChannelPlatform:  listing.SalesChannel.Platform,
		ChannelKey:       listing.SalesChannel.StoreKey,
		ResourceID:       "",
		EventTimestamp:   time.Now(),
		MerchantVisible:  true,
		ExpiredDays:      60,
		ExecutionSucceed: true,
	}

	variantsLinkRecordMap := make(map[string]variantLinkRecord)
	newRelationMap := make(map[string]*ProductListingRelation)
	lastRelationMap := make(map[string]*ProductListingRelation)

	variantEvents := make([]events.ListingModifyStateEvent, 0)
	for i := range listing.Relations {
		newRelationMap[listing.Relations[i].ID] = listing.Relations[i]
	}

	for i := range lastListing.Relations {
		lastRelationMap[lastListing.Relations[i].ID] = lastListing.Relations[i]
	}

	for id, newRelation := range newRelationMap {
		createEventBaseInfo.ResourceID = id
		if lastRelation, ok := lastRelationMap[id]; ok {
			if newRelation.LinkStatus != lastRelation.LinkStatus {
				event := events.ListingModifyStateEvent{
					CreateEventBaseInfo: createEventBaseInfo,
					Type:                events.TypeListingLinkVariant,
					LastState:           lastRelation.LinkStatus.String(),
					NewState:            newRelation.LinkStatus.String(),
				}
				variantEvents = append(variantEvents, event)
				// 只收集已经 synced 的 link / unlink 事件
				if newRelation.SyncStatus == consts.SyncStatusSynced {
					if newRelation.LinkStatus == consts.LinkStatusLinked {
						// link 操作
						variantsLinkRecordMap[newRelation.ProductListingVariantID] = variantLinkRecord{
							variantCreatedAt: newRelation.CreatedAt,
							actions: append(variantsLinkRecordMap[newRelation.ProductListingVariantID].actions,
								business_events_collector.SKULinkActionLinked),
						}
					} else {
						// unlink 操作
						variantsLinkRecordMap[newRelation.ProductListingVariantID] = variantLinkRecord{
							variantCreatedAt: newRelation.CreatedAt,
							actions: append(variantsLinkRecordMap[newRelation.ProductListingVariantID].actions,
								business_events_collector.SKULinkActionUnlinked),
						}
					}
				}
			}
			if newRelation.SyncStatus != lastRelation.SyncStatus {
				event := events.ListingModifyStateEvent{
					CreateEventBaseInfo: createEventBaseInfo,
					Type:                events.TypeListingSyncVariant,
					LastState:           lastRelation.SyncStatus.String(),
					NewState:            newRelation.SyncStatus.String(),
				}
				variantEvents = append(variantEvents, event)
			}
		} else {
			// 新增
			event := events.ListingModifyStateEvent{
				CreateEventBaseInfo: createEventBaseInfo,
				Type:                events.TypeListingLinkVariant,
			}
			variantEvents = append(variantEvents, event)
			if newRelation.SyncStatus == consts.SyncStatusSynced {
				variantsLinkRecordMap[newRelation.ProductListingVariantID] = variantLinkRecord{
					variantCreatedAt: newRelation.CreatedAt,
					actions: append(variantsLinkRecordMap[newRelation.ProductListingVariantID].actions,
						business_events_collector.SKULinkActionInit),
				}

				if newRelation.LinkStatus == consts.LinkStatusLinked {
					variantsLinkRecordMap[newRelation.ProductListingVariantID] = variantLinkRecord{
						variantCreatedAt: newRelation.CreatedAt,
						actions: append(variantsLinkRecordMap[newRelation.ProductListingVariantID].actions,
							business_events_collector.SKULinkActionLinked),
					}
				} else {
					variantsLinkRecordMap[newRelation.ProductListingVariantID] =
						variantLinkRecord{
							variantCreatedAt: newRelation.CreatedAt,
							actions: append(variantsLinkRecordMap[newRelation.ProductListingVariantID].actions,
								business_events_collector.SKULinkActionUnlinked),
						}
				}
			}
		}
	}

	for id := range lastRelationMap {
		if _, ok := newRelationMap[id]; ok {
			continue
		}
		createEventBaseInfo.ResourceID = id
		// 删除
		event := events.ListingModifyStateEvent{
			CreateEventBaseInfo: createEventBaseInfo,
			Type:                events.TypeListingDeleteVariant,
		}
		variantEvents = append(variantEvents, event)
		if lastRelationMap[id].SyncStatus == consts.SyncStatusSynced {
			variantsLinkRecordMap[lastRelationMap[id].ProductListingVariantID] =
				variantLinkRecord{
					variantCreatedAt: lastRelationMap[id].CreatedAt,
					actions: append(variantsLinkRecordMap[lastRelationMap[id].ProductListingVariantID].actions,
						business_events_collector.SKULinkActionDeleted),
				}
		}
	}

	for i := range variantEvents {
		if err := s.feedEventsService.ListingEventsService.ModifyStateEvent(ctx, variantEvents[i]); err != nil {
			s.logger.ErrorCtx(ctx, "save event activity log error", zap.Error(err))
		}
	}

	linkFrom := business_events_collector.SKULinkSourceByManual
	if _, ok := ctx.Value(consts.AutoLinkPassKey).(bool); ok {
		linkFrom = business_events_collector.SKULinkSourceByAutoLink
	}

	unlinkFrom := business_events_collector.SKUUnlinkSourceByEcommerceSKUDeleted
	if name, ok := ctx.Value("consumer_username").(string); ok && name == consts.ConsumerFeed {
		unlinkFrom = business_events_collector.SKUUnlinkSourceByManual
	}

	for variantID, record := range variantsLinkRecordMap {
		// 当前 sku record.actions 如果有包含 init, 就修正 unlinkFrom 为 no_operation
		for _, action := range record.actions {
			if action == business_events_collector.SKULinkActionInit {
				unlinkFrom = business_events_collector.SKUUnlinkSourceByNoOperation
			}
		}
		for _, action := range record.actions {
			// 1. 上报 link 事件至 data
			if err := s.businessMonitoringExporter.RecordService.CollectBusinessEvents(&business_events_collector.BizEventInput{
				Type: business_events_collector.SKULinkRecord,
				BizPayloads: business_events_collector.BizPayloads{
					LinkRateEvent: &business_events_collector.LinkRateEventReq{
						OrganizationID:       listing.Organization.ID,
						AppPlatform:          listing.ProductsCenterProduct.Source.Platform,
						AppKey:               listing.ProductsCenterProduct.Source.StoreKey,
						SalesChannelPlatform: listing.SalesChannel.Platform,
						SalesChannelStoreKey: listing.SalesChannel.StoreKey,
						ResourceID:           listing.ID + "#" + variantID,
						Result:               business_events_collector.Succeeded,
						SKULinkRecordExtensions: &business_events_collector.SKULinkRecordExtensions{
							SKUCreatedAt: types.MakeDatetime(record.variantCreatedAt),
							LinkFrom:     linkFrom,
							UnLinkFrom:   unlinkFrom,
							Action:       action,
						},
					},
				},
			}); err != nil {
				s.logger.ErrorCtx(ctx, "failed to report sku link record event", zap.Error(err))
			}

			var from string
			if action == business_events_collector.SKULinkActionLinked {
				from = string(linkFrom)
			}
			if action == business_events_collector.SKULinkActionUnlinked {
				from = string(unlinkFrom)
			}

			// 2. 上报 link 事件至 clickhouse
			s.businessMonitoringExporter.RecordService.Recording(bme.Logs{
				Organization: product_listings_api_v1.Organization{
					ID: listing.Organization.ID,
				},
				SalesChannel: product_listings_api_v1.SalesChannel{
					StoreKey: listing.SalesChannel.StoreKey,
					Platform: listing.SalesChannel.Platform,
				},
				Source: product_listings_api_v1.Source{
					App: product_listings_api_v1.App{
						Key:      listing.ProductsCenterProduct.Source.StoreKey,
						Platform: listing.ProductsCenterProduct.Source.Platform,
					},
				},
				Biz: bme.Biz{
					SourceID:  listing.ID + "#" + variantID,
					Event:     consts.BizEvent(business_events_collector.SKULinkRecord.String()),
					CreatedAt: record.variantCreatedAt,
					Status:    consts.BizStatus(action),
					StageName: consts.BizStageName(from),
				},
			})

			// 3. 上报 link 事件至 activity_log
			bizLinkEventInput := events.ListingLinkRecordEvent{
				CreateEventBaseInfo: events.CreateEventBaseInfo{
					ProductCode:      "product-listings",
					OrganizationID:   listing.Organization.ID,
					AppPlatform:      listing.ProductsCenterProduct.Source.Platform,
					AppKey:           listing.ProductsCenterProduct.Source.StoreKey,
					ChannelPlatform:  listing.SalesChannel.Platform,
					ChannelKey:       listing.SalesChannel.StoreKey,
					ResourceID:       variantID,
					EventTimestamp:   time.Now(),
					MerchantVisible:  true,
					ExecutionSucceed: true,
					ExpiredDays:      180,
				},
				SKUCreatedAt: types.MakeDatetime(record.variantCreatedAt),
				LinkFrom:     string(linkFrom),
				UnLinkFrom:   string(unlinkFrom),
				Action:       string(action),
			}
			if err := s.feedEventsService.ListingLinkEventsService.LinkRecordEvent(ctx, bizLinkEventInput); err != nil {
				s.logger.ErrorCtx(ctx, "auto link failed to collect activity log", zap.Error(err))
			}
		}
	}

}

func (s *serviceImpl) saveAISuggestionProductActivityLog(ctx context.Context, lastListing *ProductListing, newListing ProductListing) {
	if lastListing == nil {
		return
	}
	if !newListing.matched() {
		return
	}
	// only record: only tts platform ai
	if newListing.SalesChannel.Platform != consts.TikTokShop {
		return
	}
	// only record: reviewing -> active/suspended
	if !(lastListing.State == consts.ProductListingProductStateReviewing &&
		(newListing.State == consts.ProductListingProductStateActive || newListing.State == consts.ProductListingProductStateSuspended)) {
		return
	}

	nowTime := time.Now()
	eventList, err := s.feedCliV1.EventsService.GetEvents(ctx, events_sdk.GetInternalEventsParams{
		AppKey:             newListing.ProductsCenterProduct.Source.StoreKey,
		AppPlatform:        newListing.ProductsCenterProduct.Source.Platform,
		ChannelKey:         newListing.SalesChannel.StoreKey,
		ChannelPlatform:    newListing.SalesChannel.Platform,
		OrganizationID:     newListing.Organization.ID,
		ResourceID:         newListing.ID,
		Type:               consts.FeedEventTypeListingsAI,
		FromEventTimestamp: nowTime.Add(-3 * 24 * time.Hour).UTC().Format(time.RFC3339), // 取最近3天的数据
		ToEventTimestamp:   nowTime.UTC().Format(time.RFC3339),
		Page:               "1",
		Limit:              "3", // 只取3条
	})
	if err != nil {
		s.logger.ErrorCtx(ctx, "Get events error", zap.Error(err))
		return
	}
	if len(eventList) == 0 {
		return
	}

	eventIDs := make([]string, 0)
	fieldsSet := set.NewStringSet()
	for _, event := range eventList {
		if event.Properties == nil || event.Properties.ListingAiRecord == nil {
			continue
		}
		field := event.Properties.ListingAiRecord.Fields.String()
		if field != consts.AISuggestionProductTitle && field != consts.AISuggestionProductDesc {
			continue
		}
		fieldsSet.Add(field)
		eventIDs = append(eventIDs, event.ID.String())
	}

	fieldsArr := fieldsSet.ToList()
	sort.Strings(fieldsArr)
	recordFields := strings.Join(fieldsArr, ",")

	s.businessMonitoringExporter.RecordService.Recording(bme.Logs{
		Organization: product_listings_api_v1.Organization{
			ID: newListing.Organization.ID,
		},
		SalesChannel: product_listings_api_v1.SalesChannel{
			StoreKey: newListing.SalesChannel.StoreKey,
			Platform: newListing.SalesChannel.Platform,
		},
		Source: product_listings_api_v1.Source{
			App: product_listings_api_v1.App{
				Key:      newListing.ProductsCenterProduct.Source.StoreKey,
				Platform: newListing.ProductsCenterProduct.Source.Platform,
			},
		},
		Biz: bme.Biz{
			SourceID:  newListing.ID,
			Event:     consts.EventTypeListingAiRecord,
			StageName: consts.BizStageName(recordFields),              // 推荐字段
			Status:    consts.BizStatus(newListing.State),             // listings 的状态
			Message:   consts.BizMessage(strings.Join(eventIDs, ",")), // 关联的 event id=
		},
	})

}

func (s *serviceImpl) getStoreSettingByListing(ctx context.Context, listing *ProductListing) (*settings.Setting, error) {
	sourceAppKey := listing.ProductsCenterProduct.Source.StoreKey
	sourceAppPlatform := listing.ProductsCenterProduct.Source.Platform

	if !listing.matched() && !listing.linked() {
		return nil, ErrNotMatchedAndLinkedProduct
	}

	if !listing.matched() {
		for i := range listing.Relations {
			if listing.Relations[i].ProductsCenterVariant.Source.StoreKey != "" {
				sourceAppKey = listing.Relations[i].ProductsCenterVariant.Source.StoreKey
				sourceAppPlatform = listing.Relations[i].ProductsCenterVariant.Source.Platform
				break
			}
		}
	}

	return s.getStoreSetting(ctx, &settings.SearchSettingArgs{
		OrganizationID:       listing.Organization.ID,
		SalesChannelPlatform: listing.SalesChannel.Platform,
		SalesChannelStoreKey: listing.SalesChannel.StoreKey,
		SourceAppKey:         sourceAppKey,
		SourceAppPlatform:    sourceAppPlatform,
	})
}

// nolint:funlen,gocyclo,maintidx
func (s *serviceImpl) overWriteListingVariantPriceAndInventory(ctx context.Context, listing *ProductListing) {
	// If listing has been published, do not overwrite price and inventory
	if listing.SalesChannelProduct.ID != "" {
		return
	}

	// If listing customized price and inventory, do not overwrite price and inventory
	if listing.Settings.InventorySyncSetting.Preference == consts.SettingPreferenceCustomized &&
		listing.Settings.InventorySyncSetting.Customized.AutoSync == consts.StateDisabled &&
		listing.Settings.PriceSyncSetting.Preference == consts.SettingPreferenceCustomized &&
		listing.Settings.PriceSyncSetting.Customized.AutoSync == consts.StateDisabled {
		return
	}

	setting, err := s.getUnionSettingByListing(ctx, listing)
	if err != nil {
		s.logger.ErrorCtx(ctx, "Failed to get union setting", zap.Error(err), zap.String("product_listing_id", listing.ID))
		return
	}

	// Get all linked relations
	linkedRelations := make([]*ProductListingRelation, 0)
	linkedProductsCenterProductIDSets := set.NewStringSet()
	for i := range listing.Relations {
		relation := listing.Relations[i]
		if listing.Relations[i].LinkStatus == consts.LinkStatusLinked && relation.ProductsCenterVariant.ProductID != "" {
			linkedRelations = append(linkedRelations, relation)
			linkedProductsCenterProductIDSets.Add(relation.ProductsCenterVariant.ProductID)
		}
	}

	linkedProductsCenterProductIDs := linkedProductsCenterProductIDSets.ToList()
	// If no linked relations, return
	if len(linkedProductsCenterProductIDs) == 0 {
		return
	}

	// Get all linked products center product
	products, err := s.productsCenterClient.Product.List(ctx, &products_center.GetProductsArgs{
		OrganizationID: listing.Organization.ID,
		IDs:            strings.Join(linkedProductsCenterProductIDs, ","),
	})
	if err != nil {
		s.logger.WarnCtx(ctx, "Failed to get products center product",
			zap.Error(err), zap.String("product_listing_id", listing.ID),
			zap.String("linked_products_center_product_ids", strings.Join(linkedProductsCenterProductIDs, ",")),
		)
		return
	}

	if len(products) == 0 {
		s.logger.WarnCtx(ctx, "No products center product found",
			zap.String("product_listing_id", listing.ID),
			zap.String("linked_products_center_product_ids", strings.Join(linkedProductsCenterProductIDs, ",")),
		)
		return
	}

	if len(products) != len(linkedProductsCenterProductIDs) {
		s.logger.WarnCtx(ctx, "Not all products center product found",
			zap.String("product_listing_id", listing.ID),
			zap.String("linked_products_center_product_ids", strings.Join(linkedProductsCenterProductIDs, ",")),
		)
		return
	}

	linkedProductsCenterProduct := make(map[string]*products_center.Product)
	for i := range products {
		linkedProductsCenterProduct[products[i].ID] = products[i]
	}

	// Calculate prices
	priceSyncSetting := setting.getPriceSyncSetting()
	if priceSyncSetting.AutoSync == consts.StateEnabled {
		calculatePricesOutputs := make(map[string]*calculators.CalculatePricesOutput, 0)
		for _, pcp := range linkedProductsCenterProduct { // gocover:ignore
			// 基于 ID 拿一次最新数据，同时完成价格的特殊处理
			latestProduct, err := s.getLatestProduct(ctx, pcp.ID)
			if err != nil {
				s.logger.WarnCtx(ctx, "get latest product with price from meta field failed",
					zap.String("products_center_id", pcp.ID), zap.Error(err))
			} else {
				pcp = latestProduct // gocover:ignore
			}
			output, err := s.calculatePrices(ctx,
				listing.Organization,
				listing.SalesChannel,
				listing.Product.Variants[0].Price.Currency,
				pcp, // gocover:ignore
				&priceSyncSetting)
			if err != nil {
				if errors.Is(err, organization_settings.ErrOrganizationSettingNotFound) ||
					errors.Is(err, organization_settings.ErrSpecificCurrencyConvertorNotFound) {
					s.logger.WarnCtx(ctx, "Failed to calculate prices", zap.Error(err), zap.String("product_listing_id", listing.ID))
				} else {
					s.logger.ErrorCtx(ctx, "Failed to calculate prices", zap.Error(err), zap.String("product_listing_id", listing.ID))
				}
				return
			}
			calculatePricesOutputs[pcp.ID] = output // gocover:ignore
		}

		// Overwrite price
		for i := range linkedRelations {
			for j := range listing.Product.Variants {
				if linkedRelations[i].ProductListingVariantID != listing.Product.Variants[j].ID {
					continue
				}
				calculatePricesOutput, ok := calculatePricesOutputs[linkedRelations[i].ProductsCenterVariant.ProductID]
				if !ok {
					s.logger.ErrorCtx(ctx, "Calculate prices output not found",
						zap.String("product_listing_id", listing.ID),
						zap.Any("relations", linkedRelations[i]),
					)
					continue
				}

				price, compareAtPrice, err := calculatePricesOutput.GetPrice(linkedRelations[i].ProductsCenterVariant.ID)
				if err != nil {
					s.logger.WarnCtx(ctx, "Failed to get price",
						zap.Error(err),
						zap.String("product_listing_id", listing.ID),
						zap.Any("relations", linkedRelations[i]),
					)
					continue
				}
				listing.Product.Variants[j].Price = price
				listing.Product.Variants[j].CompareAtPrice = compareAtPrice
			}
		}
	}

	// Calculate quantities
	inventorySyncSetting := setting.getInventorySyncSetting()
	if inventorySyncSetting.AutoSync == consts.StateEnabled {
		calculateAvailableQuantitiesOutputs := make(map[string]*calculators.CalculateAvailableQuantitiesOutput, 0)
		for i := range linkedProductsCenterProduct {
			output, err := s.calculateQuantities(ctx,
				listing.Organization,
				listing.SalesChannel,
				linkedProductsCenterProduct[i],
				&inventorySyncSetting)
			if err != nil {
				s.logger.ErrorCtx(ctx, "Failed to calculate available quantities",
					zap.String("product_arg", connector_lib_utils.GetJsonIndent(linkedProductsCenterProduct[i])),
					zap.String("product_listing_id", listing.ID), zap.Error(err))
				return
			}
			calculateAvailableQuantitiesOutputs[linkedProductsCenterProduct[i].ID] = output
		}

		// Overwrite inventory
		for i := range linkedRelations {
			for j := range listing.Product.Variants {
				if linkedRelations[i].ProductListingVariantID != listing.Product.Variants[j].ID {
					continue
				}
				calculateAvailableQuantitiesOutput, ok := calculateAvailableQuantitiesOutputs[linkedRelations[i].ProductsCenterVariant.ProductID]
				if !ok {
					s.logger.WarnCtx(ctx, "Calculate available quantities output not found",
						zap.String("product_listing_id", listing.ID),
						zap.Any("relations", linkedRelations[i]),
					)
					continue
				}
				quantity, err := calculateAvailableQuantitiesOutput.GetAvailableQuantity(linkedRelations[i].ProductsCenterVariant.ID)
				if err != nil {
					s.logger.WarnCtx(ctx, "Failed to get available quantity",
						zap.Error(err),
						zap.String("product_listing_id", listing.ID),
						zap.Any("relations", linkedRelations[i]),
					)
					continue
				}
				// If quantity is greater than TikTokMaxInventory, set quantity to TikTokMaxInventory
				if quantity > consts.TikTokMaxInventory {
					quantity = consts.TikTokMaxInventory
				}

				listing.Product.Variants[j].InventoryQuantity = quantity
			}
		}
	}
}

// prepareVariantPriceInputs 将产品变体转换为价格计算输入
func (s *serviceImpl) prepareVariantPriceInputs(variants []products_center.Variant, connectorProductID string) (map[string][]calculators.CalculatePricesVariantInput, error) {
	result := make(map[string][]calculators.CalculatePricesVariantInput, 0)
	for _, variant := range variants {
		priceAmount, err := decimal.NewFromString(variant.Price.Amount)
		if err != nil {
			return nil, err
		}

		var compareAtPriceAmount decimal.Decimal
		if variant.CompareAtPrice.Amount != "" {
			compareAtPriceAmount, err = decimal.NewFromString(variant.CompareAtPrice.Amount)
			if err != nil {
				return nil, err
			}
		}

		key := variant.ConnectorsProductID
		if key == "" {
			key = connectorProductID
		}

		_, ok := result[key]
		if !ok {
			result[key] = make([]calculators.CalculatePricesVariantInput, 0)
		}
		result[key] = append(result[key], calculators.CalculatePricesVariantInput{
			ID:         variant.ID,
			ExternalID: variant.SourceVariantID,
			Price: models.Price{
				Amount:   priceAmount,
				Currency: variant.Price.Currency,
			},
			CompareAtPrice: models.Price{
				Amount:   compareAtPriceAmount,
				Currency: variant.CompareAtPrice.Currency,
			},
		})
	}

	return result, nil
}

// buildPriceCalculationInput 构建价格计算输入请求
func (s *serviceImpl) buildPriceCalculationInput(
	organization models.Organization,
	salesChannel models.SalesChannel,
	salesChannelCurrency string,
	product *products_center.Product,
	connectorsProductID string,
	priceSync *models.PriceSync,
	variantInputs []calculators.CalculatePricesVariantInput,
) *calculators.CalculatePricesInput {
	return &calculators.CalculatePricesInput{
		SalesChannel: salesChannel,
		Organization: organization,
		Source: models.Source{
			Type: product.Source.Type,
			App: models.App{
				Key:      product.Source.App.Key,
				Platform: product.Source.App.Platform,
			},
			ID: product.ID,
		},
		SalesChannelCurrency: salesChannelCurrency,
		PriceSync:            priceSync,
		ProductsCenterProduct: &calculators.CalculatePriceProductsCenterProductInput{
			ID:                 product.ID,
			ConnectorProductID: connectorsProductID,
			Variants:           variantInputs,
		},
	}
}

// calculatePrices 计算产品价格
func (s *serviceImpl) calculatePrices(ctx context.Context,
	organization models.Organization,
	salesChannel models.SalesChannel,
	salesChannelCurrency string,
	product *products_center.Product,
	priceSync *models.PriceSync) (*calculators.CalculatePricesOutput, error) {
	// 1. 准备 variant 价格输入
	variantInputsMap, err := s.prepareVariantPriceInputs(product.Variants, product.ConnectorsProductID)
	if err != nil {
		return nil, err
	}

	// 如果没有 variant 数据，返回空结果
	if len(variantInputsMap) == 0 {
		return &calculators.CalculatePricesOutput{
			ProductsCenterProduct: &calculators.CalculatePriceProductsCenterProductOutput{
				ID:       product.ID,
				Variants: []calculators.CalculatePricesVariantOutput{},
			},
		}, nil
	}

	// 2. 计算并合并所有结果
	return s.calculateAndMergePriceResults(ctx, organization, salesChannel, salesChannelCurrency, product, priceSync, variantInputsMap)
}

func (s *serviceImpl) getLatestProduct(ctx context.Context, id string) (*products_center.Product, error) {
	if s.productsCenterClient == nil || s.productsCenterClient.Product == nil {
		return nil, errors.New("products center client or product service is not initialized")
	}

	latestProduct, err := s.productsCenterClient.Product.GetByID(ctx, id)
	if err != nil {
		return nil, err
	}

	cli := products_center_internal.NewClient(s.conf, s.logger, s.connectorService, s.productsCenterClient)
	latestProduct, err = cli.Product.CoverProductVariantsPriceWithSpecialRules(ctx, latestProduct)
	if err != nil {
		return nil, err
	}

	return latestProduct, nil
}

// calculateAndMergeResults 计算并合并不同产品的价格结果
func (s *serviceImpl) calculateAndMergePriceResults(
	ctx context.Context,
	organization models.Organization,
	salesChannel models.SalesChannel,
	salesChannelCurrency string,
	product *products_center.Product,
	priceSync *models.PriceSync,
	variantInputsMap map[string][]calculators.CalculatePricesVariantInput,
) (*calculators.CalculatePricesOutput, error) {
	var result *calculators.CalculatePricesOutput

	for connectorsProductID, variantInputs := range variantInputsMap {
		// 构建计算请求
		calculationInput := s.buildPriceCalculationInput(
			organization,
			salesChannel,
			salesChannelCurrency,
			product,
			connectorsProductID,
			priceSync,
			variantInputs,
		)

		// 执行计算
		tempResult, err := s.calculatorService.CalculatePrices(ctx, calculationInput)
		if err != nil {
			return nil, err
		}

		if tempResult == nil || tempResult.ProductsCenterProduct == nil {
			continue
		}

		// 初始化或合并结果
		if result == nil {
			result = tempResult
			continue
		}

		s.mergePriceResults(result, tempResult)
	}

	return result, nil
}

// mergePriceResults 合并价格计算结果
func (s *serviceImpl) mergePriceResults(target, source *calculators.CalculatePricesOutput) {
	if source == nil ||
		source.ProductsCenterProduct == nil ||
		len(source.ProductsCenterProduct.Variants) == 0 {
		return
	}

	target.ProductsCenterProduct.Variants = append(
		target.ProductsCenterProduct.Variants,
		source.ProductsCenterProduct.Variants...,
	)
}

// prepareVariantQuantityInputs 将产品 variant 转换为库存计算输入
func (s *serviceImpl) prepareVariantQuantityInputs(variants []products_center.Variant, connectorProductID string) map[string][]calculators.CalculateAvailableQuantitiesVariantInput {
	inputMaps := make(map[string][]calculators.CalculateAvailableQuantitiesVariantInput)
	for _, variant := range variants {
		key := variant.ConnectorsProductID
		if key == "" {
			key = connectorProductID
		}

		_, ok := inputMaps[key]
		if !ok {
			inputMaps[key] = make([]calculators.CalculateAvailableQuantitiesVariantInput, 0)
		}
		inputMaps[key] = append(inputMaps[key], calculators.CalculateAvailableQuantitiesVariantInput{
			ID:                      variant.ID,
			ExternalID:              variant.SourceVariantID,
			AvailableQuantity:       variant.InventoryQuantity,
			AllowBackorder:          variant.AllowBackorder,
			ExternalInventoryItemID: variant.SourceInventoryItemId,
			Status:                  variant.Status,
		})
	}

	return inputMaps
}

// buildQuantityCalculationInput 构建库存计算输入请求
func (s *serviceImpl) buildQuantityCalculationInput(
	organization models.Organization,
	salesChannel models.SalesChannel,
	product *products_center.Product,
	connectorsProductID string,
	inventorySetting *models.InventorySync,
	variantInputs []calculators.CalculateAvailableQuantitiesVariantInput,
) *calculators.CalculateAvailableQuantitiesInput {
	return &calculators.CalculateAvailableQuantitiesInput{
		SalesChannel: salesChannel,
		Organization: organization,
		Source: models.Source{
			Type: product.Source.Type,
			App: models.App{
				Key:      product.Source.App.Key,
				Platform: product.Source.App.Platform,
			},
			ID: product.Source.ID,
		},
		InventorySync: inventorySetting,
		ProductsCenterProduct: &calculators.CalculateAvailableQuantitiesProductsCenterProductInput{
			ID:                 product.ID,
			ConnectorProductID: connectorsProductID,
			Variants:           variantInputs,
		},
	}
}

// mergeQuantityResults 合并库存计算结果
func (s *serviceImpl) mergeQuantityResults(target, source *calculators.CalculateAvailableQuantitiesOutput) {
	if source.ProductsCenterProduct == nil || len(source.ProductsCenterProduct.Variants) == 0 {
		return
	}

	target.ProductsCenterProduct.Variants = append(
		target.ProductsCenterProduct.Variants,
		source.ProductsCenterProduct.Variants...,
	)
}

// calculateAndMergeQuantityResults 计算并合并不同连接器产品的库存结果
func (s *serviceImpl) calculateAndMergeQuantityResults(
	ctx context.Context,
	organization models.Organization,
	salesChannel models.SalesChannel,
	product *products_center.Product,
	inventorySetting *models.InventorySync,
	inputMaps map[string][]calculators.CalculateAvailableQuantitiesVariantInput,
) (*calculators.CalculateAvailableQuantitiesOutput, error) {
	var result *calculators.CalculateAvailableQuantitiesOutput

	for connectorsProductID, input := range inputMaps {
		// 构建计算请求
		calculationInput := s.buildQuantityCalculationInput(
			organization,
			salesChannel,
			product,
			connectorsProductID,
			inventorySetting,
			input,
		)

		// 执行计算
		tempResult, err := s.calculatorService.CalculateAvailableQuantities(ctx, calculationInput)
		if err != nil {
			return nil, err
		}

		if tempResult == nil || tempResult.ProductsCenterProduct == nil {
			continue
		}

		// 初始化或合并结果
		if result == nil {
			result = tempResult
			continue
		}
		s.mergeQuantityResults(result, tempResult)
	}

	return result, nil
}

// calculateQuantities 计算产品库存
func (s *serviceImpl) calculateQuantities(ctx context.Context,
	organization models.Organization,
	salesChannel models.SalesChannel,
	product *products_center.Product,
	inventorySetting *models.InventorySync) (*calculators.CalculateAvailableQuantitiesOutput, error) {
	// 1. 准备变体库存输入
	inputMaps := s.prepareVariantQuantityInputs(product.Variants, product.ConnectorsProductID)

	// 如果没有变体数据，返回空结果
	if len(inputMaps) == 0 {
		return &calculators.CalculateAvailableQuantitiesOutput{
			ProductsCenterProduct: &calculators.ProductsCenterProductOut{
				ID:       product.ID,
				Variants: []calculators.CalculateAvailableQuantitiesVariantOutput{},
			},
		}, nil
	}

	// 2. 计算并合并所有结果
	return s.calculateAndMergeQuantityResults(ctx, organization, salesChannel, product, inventorySetting, inputMaps)
}

// nolint:gocyclo
func (s *serviceImpl) SalesChannelOrderVariantRelation(ctx context.Context, arg *SalesChannelOrderVariantRelationArg) (ProductListingRelation, error) {
	maxPage, limit := 10, 500
	relations := make([]*relationDBModel, 0)
	searchArg := repoListRelationArgs{
		OrganizationID:        arg.OrganizationID,
		SalesChannelPlatform:  arg.SalesChannelPlatform,
		SalesChannelStoreKey:  arg.SalesChannelStoreKey,
		SourcePlatform:        arg.SourcePlatform,
		SourceStoreKey:        arg.SourceStoreKey,
		SalesChannelProductID: arg.SalesChannelProductID,
		SalesChannelVariantID: arg.SalesChannelVariantID,
		IncludeDeleted:        true,
		LinkStatus:            []string{consts.LinkStatusLinked.String()},
		SyncStatus:            []string{consts.SyncStatusSynced.String()},
	}

	for page := 1; page <= maxPage; page++ {
		searchArg.Page = int64(page)
		searchArg.Limit = int64(limit)
		repos, err := s.repo.plRelationRepo.list(ctx, &searchArg)
		if err != nil {
			return ProductListingRelation{}, err
		}
		relations = append(relations, repos...)
		if len(repos) < limit {
			break
		}
	}

	if len(relations) == 0 {
		return ProductListingRelation{}, models.ErrResourceNotFound
	}

	// Sort relations by synced_at and linked_at
	sort.Slice(relations, func(i, j int) bool {
		return relations[i].LastLinkedAt.Datetime().After(relations[j].LastLinkedAt.Datetime())
	})

	relation := relations[0].toProductListingRelation()

	// validate relation e-commerces product and variant is exist
	if relation.ProductsCenterVariant.ProductID == "" || relation.ProductsCenterVariant.ID == "" {
		return ProductListingRelation{}, models.ErrResourceNotFound
	}

	// validate product is exist
	product, err := s.productsCenterClient.Product.GetByID(ctx, relation.ProductsCenterVariant.ProductID)
	if err != nil {
		return ProductListingRelation{}, err
	}
	if product == nil || product.ID == "" {
		return ProductListingRelation{}, models.ErrResourceNotFound
	}

	// validate variant is exist
	variantIsExist := false
	// 校准中台的 sku Name
	sourceSkuName := relation.ProductsCenterVariant.Source.Sku
	for i := range product.Variants {
		if product.Variants[i].ID == relation.ProductsCenterVariant.ID {
			variantIsExist = true
			sourceSkuName = product.Variants[i].Sku
			break
		}
	}
	if !variantIsExist {
		return ProductListingRelation{}, models.ErrResourceNotFound
	}

	relation.ProductsCenterVariant.Source.Sku = sourceSkuName
	return relation, nil
}

func (s *serviceImpl) RefreshES(ctx context.Context, id string, ops ...EsOption) error {
	listing, err := s.GetByID(ctx, id)
	if err != nil {
		return errors.WithStack(err)
	}

	if err = s.esRepo.BatchUpsertProductListings(ctx, []*ProductListing{&listing}, ops...); err != nil {
		s.logger.With(zap.String("Id", listing.ID)).WarnCtx(ctx, "save es error", zap.Error(err))
	}

	return nil
}

func (s *serviceImpl) NotifySearchableProductUpsertEvent(ctx context.Context, productsCenterProductIDs string) error {
	meta := databus.PubSubMeta{}

	ids := strings.Split(productsCenterProductIDs, ",")
	input := make([]notifySalesChannelUpsertEventProduct, 0)

	for index := range ids {
		input = append(input, notifySalesChannelUpsertEventProduct{
			ProductsCenterProductID: ids[index],
		})
	}

	message := notifySalesChannelUpsertEventPubsubMessage{
		Products: input,
	}

	messageBytes, err := jsoniter.Marshal(message)
	if err != nil {
		return errors.WithStack(err)
	}

	return s.databusService.SendToPubSub(ctx, s.conf.PubSubTopics.SearchableProductModifySalesChannel, messageBytes, meta)
}

func (s *serviceImpl) PublishUpdate(ctx context.Context, id string) error {

	listing, err := s.GetByID(ctx, id)
	if err != nil {
		return errors.WithStack(err)
	}
	if listing.State == consts.ProductListingProductStatePending {
		return errors.New("product listing is pending, can not publish")
	}
	if err := s.createPublishTask(ctx, &listing); err != nil {
		return errors.WithStack(err)
	}

	// audit log
	logger.Get().InfoCtx(ctx, "create an publish task for the published product", zap.String("id", id))

	return nil
}

func (s *serviceImpl) getTiktokCDNDomainRegexps() []*regexp.Regexp {
	ttsCDNDomainRegExps := make([]*regexp.Regexp, 0)
	if s.conf != nil &&
		s.conf.DynamicConfigs != nil &&
		s.conf.DynamicConfigs.TTSCDNDomainRegExps != nil {
		ttsCDNDomainRegExps = s.conf.DynamicConfigs.TTSCDNDomainRegExps
	}

	return ttsCDNDomainRegExps
}

func (s *serviceImpl) whitelistFilterProductsInActiveVariants(ctx context.Context, products []*products_center.Product) []*products_center.Product {

	if len(products) == 0 {
		return products
	}

	appPlatform := products[0].Source.App.Platform
	organizationID := products[0].Organization.ID
	if appPlatform != consts.SFCC {
		return products
	}

	supportFeatures, err := s.feedCliV2.SupportFeature.GetSupportFeatures(ctx, organizationID)
	if err != nil {
		s.logger.WarnCtx(ctx, "failed to get support features", zap.Error(err)) // ignore err
		return products
	}

	if !feed.RunInGrayModel(supportFeatures, feed.FeatureCodeVariantStatusInventory) {
		return products
	}

	for index := range products {
		products[index].Variants = removeProductInActiveVariants(products[index].Variants)
	}

	return products
}

func (s *serviceImpl) whitelistFilterSearchableInActiveVariants(ctx context.Context, products []*searchable_products.SearchableProduct) []*searchable_products.SearchableProduct {

	if len(products) == 0 {
		return products
	}

	appPlatform := products[0].Source.App.Platform
	organizationID := products[0].Organization.ID
	if appPlatform != consts.SFCC {
		return products
	}

	supportFeatures, err := s.feedCliV2.SupportFeature.GetSupportFeatures(ctx, organizationID)
	if err != nil {
		s.logger.WarnCtx(ctx, "failed to get support features", zap.Error(err)) // ignore err
		return products
	}

	if !feed.RunInGrayModel(supportFeatures, feed.FeatureCodeVariantStatusInventory) {
		return products
	}

	for index := range products {
		products[index].Variants = removeSearchableInActiveVariants(products[index].Variants)
	}

	return products
}

func (s *serviceImpl) UpdateRelations(ctx context.Context, id string, arg *UpdateRelationsArg) (ProductListing, error) {
	var (
		lastProductListing ProductListing
		newProductListing  ProductListing
		err                error
	)

	lastProductListing, err = s.GetByID(ctx, id)
	if err != nil {
		return ProductListing{}, err
	}

	mutex, err := s.acquireListingLock(ctx, id)
	if err != nil {
		return ProductListing{}, errors.WithStack(err)
	}
	defer func() {
		s.releaseListingLock(ctx, mutex)
		// 记录 listing 日志
		s.saveModifyActivityLog(ctx, &lastProductListing, &newProductListing)
	}()

	productListing := lastProductListing.DeepCopy()
	if err := s.repo.update(ctx, arg.convertToConductorUpdateArgs(productListing)); err != nil {
		return ProductListing{}, err
	}

	newProductListing, err = s.GetByID(ctx, id)
	if err != nil {
		return ProductListing{}, err
	}

	if err = s.esRepo.BatchUpsertProductListings(ctx, []*ProductListing{&newProductListing}); err != nil {
		s.logger.With(zap.String("Id", newProductListing.ID)).WarnCtx(ctx, "save es error", zap.Error(err))
	}

	return newProductListing, nil
}

func (s *serviceImpl) UnMatchProduct(ctx context.Context, id string) (ProductListing, error) {
	listing, err := s.GetByID(ctx, id)
	if err != nil {
		return ProductListing{}, err
	}

	oldListing := listing.DeepCopy()

	previewListing, err := s.GeneratePreview(ctx, &ProductListing{
		ID:                    listing.ID,
		Organization:          listing.Organization,
		SalesChannel:          listing.SalesChannel,
		Product:               listing.Product,
		Relations:             listing.Relations,
		Settings:              listing.Settings,
		ProductsCenterProduct: ProductsCenterProduct{},
	})
	if err != nil {
		return ProductListing{}, err
	}

	// 生成更新 model
	model, err := s.generateUpdateModel(ctx, &listing, previewListing)
	if err != nil {
		return ProductListing{}, err
	}

	// 调用 repo 更新
	if err = s.repo.update(ctx, model.buildUpdateArgs()); err != nil {
		return ProductListing{}, err
	}

	// 获取更新后的 listing
	newListing, err := s.GetByID(ctx, listing.ID)
	if err != nil {
		return ProductListing{}, err
	}

	// 更新 ES
	if err := s.esRepo.BatchUpsertProductListings(ctx, []*ProductListing{&newListing}); err != nil {
		s.logger.With(zap.String("Id", newListing.ID)).WarnCtx(ctx, "Failed to upsert listing data to ES", zap.Error(err))
	}

	// unmatch 之后更新 searchable product sales channel
	if err := s.notifySalesChannelUpsertEvent(ctx, oldListing); err != nil {
		s.logger.WarnCtx(ctx, "send relation upsert to pub/sub failed",
			zap.String("listing_id", oldListing.ID),
			zap.Error(err))
	}

	logger.Get().InfoCtx(ctx, "remove product listing matched products center product", zap.String("id", id))
	return newListing, nil
}

// MatchProduct  所有 sku 都已 link 同个 product, 并且 SKU 全匹配对齐, 才允许 match
// nolint:funlen
func (s *serviceImpl) MatchProduct(ctx context.Context, id string) (ProductListing, error) {
	listing, err := s.GetByID(ctx, id)
	if err != nil {
		return ProductListing{}, err
	}

	if listing.matched() {
		return ProductListing{}, errors.New("product listing is already matched")
	}

	if len(listing.Relations) == 0 {
		return ProductListing{}, errors.New("product listing has no relations")
	}

	linkPcProductID := listing.Relations[0].ProductsCenterVariant.ProductID
	listingLinkPcVariantIDsSet := set.NewStringSet()
	for _, relation := range listing.Relations {
		if !relation.IsLinkedAndSynced() {
			return ProductListing{}, errors.New("product listing has unlinked or unsynced relations")
		}
		if relation.ProductsCenterVariant.ProductID != linkPcProductID {
			return ProductListing{}, errors.New("product listing has relations with different products")
		}
		if listingLinkPcVariantIDsSet.Contains(relation.ProductsCenterVariant.ID) {
			return ProductListing{}, errors.New("product listing has relations with duplicate product variants")
		}
		listingLinkPcVariantIDsSet.Add(relation.ProductsCenterVariant.ID)
	}

	pcProduct, err := s.productsCenterClient.Product.GetByID(ctx, linkPcProductID)
	if err != nil {
		return ProductListing{}, errors.WithStack(err)
	}

	for _, variant := range pcProduct.Variants {
		listingLinkPcVariantIDsSet.Remove(variant.ID)
	}
	if listingLinkPcVariantIDsSet.Card() != 0 {
		return ProductListing{}, errors.New("product listing has relations with different product variants")
	}

	previewListing, err := s.GeneratePreview(ctx, &ProductListing{
		ID:           listing.ID,
		Organization: listing.Organization,
		SalesChannel: listing.SalesChannel,
		Product:      listing.Product,
		Relations:    listing.Relations,
		Settings:     listing.Settings,
		ProductsCenterProduct: ProductsCenterProduct{ // same work convert code
			ID:                 pcProduct.ID,
			ConnectorProductID: pcProduct.ConnectorsProductID,
			PublishState:       consts.ProductsCenterProductPublishState(pcProduct.Status),
			Source: ProductsCenterProductSource{
				StoreKey: pcProduct.Source.App.Key,
				Platform: pcProduct.Source.App.Platform,
				ID:       pcProduct.Source.ID,
			},
		},
	})
	if err != nil {
		return ProductListing{}, err
	}

	// 生成更新 model
	model, err := s.generateUpdateModel(ctx, &listing, previewListing)
	if err != nil {
		return ProductListing{}, err
	}

	// 调用 repo 更新
	if err = s.repo.update(ctx, model.buildUpdateArgs()); err != nil {
		return ProductListing{}, err
	}

	// 获取更新后的 listing
	newListing, err := s.GetByID(ctx, listing.ID)
	if err != nil {
		return ProductListing{}, err
	}

	// 更新 ES
	if err := s.esRepo.BatchUpsertProductListings(ctx, []*ProductListing{&newListing}); err != nil {
		s.logger.With(zap.String("Id", newListing.ID)).WarnCtx(ctx, "Failed to upsert listing data to ES", zap.Error(err))
	}

	if err := s.notifySalesChannelUpsertEvent(ctx, &newListing); err != nil {
		s.logger.WarnCtx(ctx, "send relation upsert to pub/sub failed",
			zap.String("listing_id", newListing.ID),
			zap.Error(err))
	}

	logger.Get().InfoCtx(ctx, "match product listing with products center product", zap.String("id", id))

	return newListing, nil
}

// CheckAndFixStatus 用于纠正异常状态的 listings; 后续有较多检测项时, 可以放到 worker 中执行
func (s *serviceImpl) CheckAndFixStatus(ctx context.Context, dryRun bool, processedLimitCount int) error {

	newCtx := log.CloneLogContext(ctx)

	routine.WithRecover(s.logger, func() {
		goCtx, cF := context.WithTimeout(newCtx, 15*time.Minute)
		defer cF()
		s.checkAndFixBlockingPublishListings(goCtx, dryRun, processedLimitCount)
	})

	return nil
}

func (s *serviceImpl) checkAndFixBlockingPublishListings(ctx context.Context, dryRun bool, processedLimitCount int) {

	ctx = log.AppendFieldsToContext(ctx, zap.String("step", "checkAndFixBlockingPublishListings"))

	s.logger.InfoCtx(ctx, "checkAndFixBlockingPublishListings started")
	var err error
	defer func() {
		if err != nil {
			s.logger.ErrorCtx(ctx, "checkAndFixBlockingPublishListings error", zap.Error(err))
		} else {
			s.logger.InfoCtx(ctx, "checkAndFixBlockingPublishListings completed successfully")
		}
	}()

	publishRunningListingIDs, err := s.collectPublishStateRunningListingIDs(ctx)
	if err != nil {
		return
	}

	if dryRun {
		s.logger.InfoCtx(ctx, "checkAndFixBlockingPublishListings dry run mode enabled",
			zap.Int("count", len(publishRunningListingIDs)))
		return
	}

	if processedLimitCount != 0 && len(publishRunningListingIDs) > processedLimitCount {
		publishRunningListingIDs = publishRunningListingIDs[:processedLimitCount]
	}

	for index, id := range publishRunningListingIDs {
		if err = s.fixPublishStateByID(ctx, id); err != nil {
			s.logger.WarnCtx(ctx, "checkAndFixBlockingPublishListings failed to fix listing",
				zap.String("listing_id", id), zap.Error(err))
			continue
		}
		s.logger.InfoCtx(ctx, "checkAndFixBlockingPublishListings fixed listing",
			zap.Int("index", index), zap.String("listing_id", id))
	}

}

func (s *serviceImpl) collectPublishStateRunningListingIDs(ctx context.Context) ([]string, error) {
	publishRunningListingIDs := make([]string, 0)
	cursor := consts.BeginningCursor
	for {
		ids, pagination, err := s.esRepo.SearchProductListingsIDs(ctx, &SearchProductListingArgs{
			PublishState: string(consts.PublishStateRunning),
			Page:         1,
			Limit:        100,
			Cursor:       cursor,
		})
		if err != nil {
			return nil, err
		}
		if len(ids) == 0 {
			break
		}
		cursor = pagination.NextCursor
		publishRunningListingIDs = append(publishRunningListingIDs, ids...)
	}
	s.logger.InfoCtx(ctx, "collectPublishStateRunningListingIDs completed", zap.Int("count", len(publishRunningListingIDs)))
	return publishRunningListingIDs, nil
}

func (s *serviceImpl) fixPublishStateByID(ctx context.Context, id string) error {
	listing, err := s.GetByID(ctx, id)
	if err != nil {
		// 错误处理1: Spanner Model 不存在了, ES model 对齐删除
		if errors.Is(err, ErrNotFound) {
			if deleteErr := s.esRepo.DeleteProductListingByID(ctx, id); deleteErr != nil {
				s.logger.WarnCtx(ctx, "Failed to delete listing from ES", zap.String("listing_id", id), zap.Error(deleteErr))
			} else {
				s.logger.InfoCtx(ctx, "Deleted listing from ES", zap.String("listing_id", id))
			}
		}
		return err
	}

	if time.Since(listing.UpdatedAt) < time.Hour*1 {
		return nil
	}

	if listing.Publish.State != consts.PublishStateRunning {
		// 错误处理2: Spanner Model 非 running, ES model 对齐状态
		listing.UpdatedAt = listing.UpdatedAt.Add(time.Second * 1)
		if err := s.esRepo.BatchUpsertProductListings(ctx, []*ProductListing{&listing}); err != nil {
			s.logger.With(zap.String("listing_id", listing.ID)).WarnCtx(ctx, "Failed to upsert listing data to ES", zap.Error(err))
		}
		return nil
	}

	// running 状态超1个小时, 需要进行处理
	publishArgs := listing.Publish
	publishArgs.State = consts.PublishStateFailed
	publishArgs.LastFailedAt = time.Now()
	publishArgs.Error.Code = "50001"
	publishArgs.Error.Msg = "Publish task has been running for more than 1 hour, automatically set to failed state"

	newListing, err := s.UpdatePublishState(ctx, id, &publishArgs)
	if err != nil {
		return err
	}

	if newListing.State == consts.ProductListingProductStateActive {
		return s.createPublishTask(ctx, &newListing)
	}

	return nil
}

func (s *serviceImpl) acquireListingLock(ctx context.Context, id string) (*redsync.Mutex, error) {
	mutex := s.locker.NewMutex(
		redisKeyPrefixUpdateProduct+id,
		redsync.WithExpiry(redisLockerDefaultTime),
		redsync.WithTries(redisLockerDefaultTries),
	)
	if err := mutex.LockContext(ctx); err != nil {
		return nil, errors.WithStack(err)
	}
	return mutex, nil
}

func (s *serviceImpl) releaseListingLock(ctx context.Context, mutex *redsync.Mutex) {
	if mutex == nil {
		return
	}
	if _, err := mutex.Unlock(); err != nil {
		logger.Get().ErrorCtx(ctx, "Failed to unlock mutex",
			zap.String("redis_key", mutex.Name()), zap.Error(err))
	}
}
