package product_listing

import (
	"sort"

	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/gopkg/uuid"

	"github.com/AfterShip/pltf-pd-product-listings/internal/consts"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
)

// convertToProductListing converts product listing from repo model to domain model
func convertToProductListing(conductor *conductorProductListing) ProductListing {
	pl := ProductListing{
		ID: conductor.ProductListingDBModel.ProductListingID,
		Organization: models.Organization{
			ID: conductor.ProductListingDBModel.OrganizationID,
		},
		SalesChannel: models.SalesChannel{
			Platform:      conductor.ProductListingDBModel.SalesChannelPlatform,
			StoreKey:      conductor.ProductListingDBModel.SalesChannelStoreKey,
			CountryRegion: conductor.ProductListingDBModel.SalesChannelCountryRegion,
		},
		State:            conductor.ProductListingDBModel.State,
		LinkStatus:       conductor.ProductListingDBModel.LinkStatus,
		SyncStatus:       conductor.ProductListingDBModel.SyncStatus,
		PendingDeletedAt: conductor.ProductListingDBModel.PendingDeletedAt,
		Product:          conductor.ProductListingDBModel.Product,
		Settings:         conductor.ProductListingDBModel.Settings,
		DeletedAt:        conductor.ProductListingDBModel.DeletedAt,
		CreatedAt:        conductor.ProductListingDBModel.CreatedAt,
		UpdatedAt:        conductor.ProductListingDBModel.UpdatedAt,
	}

	pl.SalesChannelProduct = conductor.ProductListingDBModel.toProductListingSalesChannelProduct()
	pl.ProductsCenterProduct = conductor.ProductListingDBModel.toProductListingProductsCenterProduct()
	pl.Publish = conductor.ProductListingDBModel.toProductListingPublish()
	pl.Audit = conductor.ProductListingDBModel.toProductListingAudit()
	pl.Ready = conductor.ProductListingDBModel.toProductListingReady()
	pl.FeedCustomizationParams = FeedCustomizationParams{
		FeedCategoryTemplateID: conductor.ProductListingDBModel.FeedCategoryTemplateID.String(),
	}
	pl.Version = conductor.ProductListingDBModel.Version
	pl.Relations = make([]*ProductListingRelation, 0)
	variantMap := make(map[string]int)
	for i := range pl.Product.Variants {
		variantMap[pl.Product.Variants[i].ID] = pl.Product.Variants[i].Position
	}
	for _, relation := range conductor.Relations {
		r := relation.toProductListingRelation()
		if position, ok := variantMap[r.ProductListingVariantID]; ok {
			r.VariantPosition = position
		}
		pl.Relations = append(pl.Relations, &r)
	}
	sort.Slice(pl.Relations, func(i, j int) bool {
		return pl.Relations[i].VariantPosition < pl.Relations[j].VariantPosition
	})

	return pl
}

func convertToConductorProductListing(p *ProductListing) conductorProductListing {
	if p.ID == "" {
		p.ID = uuid.GenerateUUIDV4()
	}
	result := conductorProductListing{}
	result.ProductListingDBModel = convertToProductListingDBModel(p)
	result.Relations = convertToRelationDBModels(p)
	return result
}

func convertToProductListingDBModel(p *ProductListing) productListingDBModel {
	model := productListingDBModel{}
	model.ProductListingID = p.ID
	model.OrganizationID = p.Organization.ID
	model.SalesChannelPlatform = p.SalesChannel.Platform
	model.SalesChannelStoreKey = p.SalesChannel.StoreKey
	model.SalesChannelCountryRegion = p.SalesChannel.CountryRegion
	model.State = p.State
	model.LinkStatus = p.LinkStatus
	model.SyncStatus = p.SyncStatus
	model.Settings = p.Settings
	model.Product = p.Product
	model.CreatedAt = p.CreatedAt
	model.PendingDeletedAt = p.PendingDeletedAt
	model.Version = p.Version

	setSalesChannelProductToProductListingDBModel(p, &model)
	setProductsCenterProductToProductListingDBModel(p, &model)
	setAuditToProductListingDBModel(p, &model)
	setReadyToProductListingDBModel(p, &model)
	setPublishToProductListingDBModel(p, &model)
	setFeedCustomizationParams(p, &model)
	return model
}

func setSalesChannelProductToProductListingDBModel(p *ProductListing, model *productListingDBModel) {
	model.SalesChannelProductID = types.MakeString(p.SalesChannelProduct.ID)
	model.SalesChannelConnectorProductID = p.SalesChannelProduct.ConnectorProductID
	model.SalesChannelState = p.SalesChannelProduct.State
	model.SalesChannelMetricsCreatedAt = p.SalesChannelProduct.Metrics.CreatedAt
	model.SalesChannelMetricsUpdatedAt = p.SalesChannelProduct.Metrics.UpdatedAt
}

func setProductsCenterProductToProductListingDBModel(p *ProductListing, model *productListingDBModel) {
	model.ProductsCenterProductID = p.ProductsCenterProduct.ID
	model.ProductsCenterConnectorProductID = p.ProductsCenterProduct.ConnectorProductID
	model.ProductsCenterState = p.ProductsCenterProduct.PublishState
	model.ProductsCenterSourceStoreKey = p.ProductsCenterProduct.Source.StoreKey
	model.ProductsCenterSourcePlatform = p.ProductsCenterProduct.Source.Platform
	model.ProductsCenterSourceProductID = p.ProductsCenterProduct.Source.ID
}

func setReadyToProductListingDBModel(p *ProductListing, model *productListingDBModel) {
	model.ReadyStatus = p.Ready.Status
	model.ReadyFailedReasons = p.Ready.FailedReasons
	model.ReadyLastFailedAt = p.Ready.LastFailedAt
}

func setAuditToProductListingDBModel(p *ProductListing, model *productListingDBModel) {
	model.AuditState = p.Audit.State
	model.AuditFailedReasons = p.Audit.FailedReasons
	model.AuditLastFailedAt = p.Audit.LastFailedAt
}

func setPublishToProductListingDBModel(p *ProductListing, model *productListingDBModel) {
	model.PublishLastReferenceID = p.Publish.LastReferenceID
	model.PublishState = p.Publish.State
	model.PublishErrorCode = p.Publish.Error.Code
	model.PublishErrorMsg = p.Publish.Error.Msg
	model.PublishLastFailedAt = p.Publish.LastFailedAt
}

func setFeedCustomizationParams(p *ProductListing, model *productListingDBModel) {
	model.FeedCategoryTemplateID = types.MakeString(p.FeedCustomizationParams.FeedCategoryTemplateID)
}

func convertToRelationDBModels(p *ProductListing) []*relationDBModel {
	relationDBModels := make([]*relationDBModel, 0)
	for _, relation := range p.Relations {
		repoRelation := convertToRelationDBModel(p.ID, relation, &p.Organization, &p.SalesChannel, &p.Product)
		relationDBModels = append(relationDBModels, repoRelation)
	}
	return relationDBModels
}

func convertToRelationDBModel(
	productListingID string,
	relation *ProductListingRelation,
	org *models.Organization,
	salesChannel *models.SalesChannel,
	product *models.Product) *relationDBModel {

	model := &relationDBModel{}
	model.ProductListingRelationID = relation.ID
	model.OrganizationID = org.ID
	model.SalesChannelPlatform = salesChannel.Platform
	model.SalesChannelStoreKey = salesChannel.StoreKey
	model.ProductListingID = productListingID
	model.ProductListingVariantID = relation.ProductListingVariantID
	model.SyncStatus = relation.SyncStatus
	model.LastSyncedAt = relation.LastSyncedAt
	model.LinkStatus = relation.LinkStatus
	model.LastLinkedAt = relation.LastLinkedAt
	model.AllowSync = relation.AllowSync
	model.CreatedAt = relation.CreatedAt

	setSalesChannelVariantToRelationDBModel(relation, model)
	setProductsCenterVariantToRelationDBModel(relation, model)

	// Set product listing variant id
	if model.ProductListingRelationID == "" {
		model.ProductListingRelationID = uuid.GenerateUUIDV4()
	}

	// Set product listing relation variant id
	if model.ProductListingVariantID == "" {
		for _, variant := range product.Variants {
			if variant.Position == relation.VariantPosition {
				model.ProductListingVariantID = variant.ID
				break
			}
		}
	}

	return model
}

func setProductsCenterVariantToRelationDBModel(r *ProductListingRelation, model *relationDBModel) {
	model.ProductsCenterVariantID = r.ProductsCenterVariant.ID
	model.ProductsCenterConnectorProductID = r.ProductsCenterVariant.ConnectorProductID
	model.ProductsCenterProductID = r.ProductsCenterVariant.ProductID
	model.ProductsCenterSourceStoreKey = r.ProductsCenterVariant.Source.StoreKey
	model.ProductsCenterSourcePlatform = r.ProductsCenterVariant.Source.Platform
	model.ProductsCenterSourceVariantID = r.ProductsCenterVariant.Source.ID
	model.ProductsCenterSourceProductID = r.ProductsCenterVariant.Source.ProductID
	model.ProductsCenterSourceSku = r.ProductsCenterVariant.Source.Sku
}

func setSalesChannelVariantToRelationDBModel(r *ProductListingRelation, model *relationDBModel) {
	model.SalesChannelVariantID = r.SalesChannelVariant.ID
	model.SalesChannelConnectorProductID = r.SalesChannelVariant.ConnectorProductID
	model.SalesChannelProductID = r.SalesChannelVariant.ProductID
	model.SalesChannelSku = r.SalesChannelVariant.Sku
}

func convertToProductListingArgsForDuplicate(pl *ProductListing) ProductListingArgs {
	args := ProductListingArgs{
		SalesChannel:          pl.SalesChannel,
		Organization:          pl.Organization,
		ProductsCenterProduct: pl.ProductsCenterProduct,
		Settings:              pl.Settings,
	}
	// set new product listing data
	productListingProduct := pl.Product
	for i, variant := range productListingProduct.Variants {
		for _, relation := range pl.Relations {
			if relation.ProductListingVariantID == variant.ID {
				variant.ID = uuid.GenerateUUIDV4()
				relation.ID = uuid.GenerateUUIDV4()
				relation.ProductListingVariantID = variant.ID
				variant.Position = i
				relation.VariantPosition = i
				relation.SyncStatus = consts.SyncStatusUnsync
				relation.SalesChannelVariant = SalesChannelVariant{}
				args.Relations = append(args.Relations, relation)
				break
			}
		}
	}
	for i, option := range productListingProduct.Options {
		for j, _ := range option.ValueDetails {
			productListingProduct.Options[i].ValueDetails[j].SalesChannelID = ""
			productListingProduct.Options[i].ValueDetails[j].Audit = models.ProductOptionValueDetailAudit{}
			productListingProduct.Options[i].ValueDetails[j].State = consts.ProductOptionValueStatePending
			productListingProduct.Options[i].ValueDetails[j].SyncStatus = consts.SyncStatusUnsync
			// clear media group id
			productListingProduct.Options[i].ValueDetails[j].SalesChannelMediaGroupID = ""

			// clear media sales channel id
			for z, _ := range option.ValueDetails[j].Media {
				productListingProduct.Options[i].ValueDetails[j].Media[z].SalesChannelID = ""
			}
		}
	}
	args.Product = productListingProduct
	return args
}

func convertToConductorUpdateArgs(p, oldProductListing *ProductListing) conductorUpdateArgs {
	result := conductorUpdateArgs{}
	result.ProductListingDBModel = convertToProductListingDBModel(p)
	convertToConductorUpdateRelations(p, oldProductListing, &result)
	return result
}

func convertToConductorUpdateRelations(p, oldProductListing *ProductListing, args *conductorUpdateArgs) {
	for _, relation := range p.Relations {
		// handle create relations
		if relation.ID == "" {
			createRelation := convertToRelationDBModel(p.ID, relation, &p.Organization, &p.SalesChannel, &p.Product)
			args.CreateRelations = append(args.CreateRelations, createRelation)
			continue
		}
		// handle update relations
		for _, oldRelation := range oldProductListing.Relations {
			if oldRelation.ID == relation.ID {
				updateRelation := convertToRelationDBModel(p.ID, relation, &p.Organization, &p.SalesChannel, &p.Product)
				args.UpdateRelations = append(args.UpdateRelations, updateRelation)
			}
		}
	}

	// handle delete relations
	for _, oldRelation := range oldProductListing.Relations {
		exist := false
		for _, relation := range p.Relations {
			if oldRelation.ID == relation.ID {
				exist = true
				break
			}
		}
		if !exist {
			args.DeleteRelationIDs = append(args.DeleteRelationIDs, oldRelation.ID)
		}
	}
}
