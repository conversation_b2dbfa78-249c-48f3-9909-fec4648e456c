package product_listing

type repoListRelationArgs struct {
	OrganizationID                    string   `json:"organization_id"`
	SalesChannelStoreKey              string   `json:"sales_channel_store_key"`
	SalesChannelPlatform              string   `json:"sales_channel_platform"`
	SourceStoreKey                    string   `json:"source_store_key"`
	SourcePlatform                    string   `json:"source_platform"`
	SalesChannelProductID             string   `json:"sales_channel_product_id"`
	SalesChannelProductIDs            []string `json:"sales_channel_product_ids"`
	SalesChannelVariantID             string   `json:"sales_channel_variant_id"`
	SalesChannelVariantIDs            []string `json:"sales_channel_variant_ids"`
	ProductsCenterProductID           string   `json:"products_center_product_id"`
	ProductsCenterVariantID           string   `json:"products_center_variant_id"`
	ProductsCenterConnectorProductIDs []string `json:"products_center_connector_product_ids"`
	LinkStatus                        []string `json:"link_status"`
	SyncStatus                        []string `json:"sync_status"`
	IncludeDeleted                    bool     `json:"include_deleted"`
	Page                              int64    `json:"page"`
	Limit                             int64    `json:"limit"`
}

type repoListAuditVersionArgs struct {
	ProductListingID string `json:"product_listing_id"`
	Page             int64  `json:"page"`
	Limit            int64  `json:"limit"`
}

type conductorProductListing struct {
	ProductListingDBModel productListingDBModel `json:"product_listing"`
	Relations             []*relationDBModel    `json:"relations"`
}

type conductorUpdateArgs struct {
	ProductListingDBModel productListingDBModel
	CreateRelations       []*relationDBModel
	UpdateRelations       []*relationDBModel
	DeleteRelationIDs     []string
}

type repoListArgs struct {
	OrganizationID           string
	SalesChannelPlatform     string
	SalesChannelStoreKey     string
	ProductsCenterProductIDs []string
	SalesChannelProductIDs   []string
	Page                     int64
	Limit                    int64
}
