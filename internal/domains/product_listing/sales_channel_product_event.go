package product_listing

import (
	"context"
	"sort"
	"strings"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/AfterShip/connectors-library/utils/sets"
	"github.com/AfterShip/feed-sdk-go/events"
	"github.com/AfterShip/gopkg/facility/set"
	"github.com/AfterShip/gopkg/log"
	"github.com/AfterShip/gopkg/routine"
	"github.com/AfterShip/pltf-pd-product-listings/internal/consts"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/category"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/organization_settings"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/settings"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
	"github.com/AfterShip/pltf-pd-product-listings/internal/utils/slicex"
)

// SalesChannelProductEvent 销售渠道产品事件
func (s *serviceImpl) SalesChannelProductEvent(ctx context.Context, arg *SalesChannelProductEventArg) (ProductListing, error) {
	ctx = log.AppendFieldsToContext(ctx,
		zap.String("sales_channel_reference_id", arg.ID),
		zap.String("organization_id", arg.Organization.ID),
		zap.String("sales_channel_product_id", arg.SalesChannelProduct.ID),
		zap.String("connector_product_id", arg.SalesChannelProduct.ConnectorProductID),
	)

	s.logger.InfoCtx(ctx, "handler sales channel product event")

	listing, err := s.getProductListingBySalesChannelProduct(ctx, arg)
	if err != nil {
		if errors.Is(err, ErrNotFound) {
			return s.createSalesChannelProductListing(ctx, arg)
		}
		return ProductListing{}, err
	}

	return s.updateSalesChannelProductListing(ctx, listing.ID, arg)
}

func (s *serviceImpl) createSalesChannelProductListing(ctx context.Context, arg *SalesChannelProductEventArg) (ProductListing, error) {
	var (
		err     error
		listing ProductListing
	)

	if err = arg.createPreCheck(); err != nil {
		return ProductListing{}, err
	}

	mutex, err := s.acquireListingLock(ctx, arg.SalesChannelProduct.ConnectorProductID)
	if err != nil {
		return ProductListing{}, errors.WithStack(err)
	}
	defer func() {
		s.releaseListingLock(ctx, mutex)
		s.saveEventActivityLog(ctx, &listing, events.TypeListingSalesChannelEvent, err)
		s.saveModifyActivityLog(ctx, &listing, &listing)
		s.saveChannelVariantInitActivityLog(ctx, &listing)
	}()

	// 拿到锁之后再查询一次防止重复处理
	pl, innerErr := s.getProductListingBySalesChannelProduct(ctx, arg)
	if innerErr != nil && !errors.Is(innerErr, ErrNotFound) {
		return ProductListing{}, innerErr
	}

	if pl.ID != "" {
		s.logger.WarnCtx(ctx, "create sales channel product listing already exist")
		return ProductListing{}, ErrSalesChannelProductIsExist
	}

	productListingArg := arg.convertToProductListingArgs(s.conf.DynamicConfigs.AmazonOptionNameMapping)
	productListingArg.Settings = getDefaultProductListingSetting(arg.SalesChannel.Platform)
	// 新创建的 product listing 不需要 relation ID，防止被外部数据污染
	productListingArg.ID = ""
	for i := range productListingArg.Relations {
		productListingArg.Relations[i].ID = ""
	}

	// 创建新的 product listing
	listing, err = s.Create(ctx, productListingArg)
	if err != nil {
		return ProductListing{}, err
	}

	// auto_link
	tempListing, autoLinkErr := s.AutoLink(ctx, &AutoLinkArg{ID: listing.ID})
	if autoLinkErr == nil {
		listing = tempListing
	}

	return listing, nil
}

// nolint:gocyclo
func (s *serviceImpl) updateSalesChannelProductListing(ctx context.Context, id string, arg *SalesChannelProductEventArg) (ProductListing, error) {
	var (
		err         error
		lastListing *ProductListing
		newListing  ProductListing
	)

	if err = arg.updatePreCheck(); err != nil {
		return ProductListing{}, err
	}

	mutex, err := s.acquireListingLock(ctx, id)
	if err != nil {
		return ProductListing{}, errors.WithStack(err)
	}
	defer func() {
		s.releaseListingLock(ctx, mutex)
		// activity log
		s.saveModifyActivityLog(ctx, lastListing, &newListing)
		s.saveVariantActivityLog(ctx, lastListing, &newListing)
		s.saveEventActivityLog(ctx, &newListing, events.TypeListingSalesChannelEvent, err)
		// takes a long time to build data activity log
		newCtx := log.CloneLogContext(ctx)
		routine.WithRecover(s.logger, func() {
			s.saveAISuggestionProductActivityLog(newCtx, lastListing, newListing)
		})
	}()

	listing, err := s.GetByID(ctx, id)
	if err != nil {
		return ProductListing{}, err
	}

	if !listing.canUpdate() {
		return ProductListing{}, ErrPublishStateInUpdateNotAck
	}

	// 记录日志使用
	lastListing = listing.DeepCopy()

	if arg.Version < listing.Version {
		return ProductListing{}, ErrVersionConflict
	}

	if listing.SalesChannelProduct.ID != "" &&
		listing.SalesChannelProduct.ID != arg.SalesChannelProduct.ID {
		s.logger.ErrorCtx(ctx, "Sales channel product conflict",
			zap.String("product_listing_id", listing.ID),
			zap.String("new sales_channel_product_id", arg.SalesChannelProduct.ID),
			zap.String("current sales_channel_product_id", listing.SalesChannelProduct.ID))
		return ProductListing{}, ErrSalesChannelProductConflict
	}

	productListingArg := arg.convertToProductListingArgs(s.conf.DynamicConfigs.AmazonOptionNameMapping)
	model := newSalesChannelEventModel(productListingArg, &listing)
	// 调用 repo 更新
	if err = s.repo.update(ctx, model.buildUpdateArg()); err != nil {
		s.logger.ErrorCtx(ctx, "Failed to update product listing from sales channel product event", zap.Error(err))
		return ProductListing{}, err
	}

	// 判断是否需要后处理，比如重新刊登，发起价格和库存同步
	if err = s.afterSalesChannelProductEvent(ctx, listing.ID, model); err != nil {
		if !isAfterSalesErrorIgnorable(err) {
			s.logger.WarnCtx(ctx, "Failed to process after sales channel product event", zap.Error(err))
			return ProductListing{}, errors.WithStack(err)
		}
		// 可忽略的错误, 不影响后续流程
		s.logger.InfoCtx(ctx, "Failed to process after sales channel product event", zap.Error(err))
	}

	newListing, err = s.GetByID(ctx, listing.ID)
	if err != nil {
		return ProductListing{}, err
	}

	// 更新 ES
	if err := s.esRepo.BatchUpsertProductListings(ctx, []*ProductListing{&newListing}); err != nil {
		s.logger.With(zap.String("product_listing_id", newListing.ID)).WarnCtx(ctx, "Failed to upsert listing data to ES", zap.Error(err))
	}

	return newListing, nil
}

func (s *serviceImpl) autoLinkAfterSaleChannelProductEvent(ctx context.Context, model *salesChannelEventModel) (*ProductListing, error) {

	if model.lastListing == nil {
		return nil, errors.New("last listing is nil")
	}

	autoLinkArg, err := model.buildAutoLinkArg()
	if err != nil {
		return nil, errors.WithStack(err)
	}

	newProductListing, autoLinkErr := s.AutoLink(ctx, autoLinkArg)
	if autoLinkErr != nil {
		return nil, errors.WithStack(autoLinkErr)
	}
	return &newProductListing, nil
}

// getProductListingBySalesChannelProduct 通过销售渠道产品信息获取 listing 数据
func (s *serviceImpl) getProductListingBySalesChannelProduct(ctx context.Context, arg *SalesChannelProductEventArg) (ProductListing, error) {
	if arg.ID != "" {
		listing, err := s.GetByID(ctx, arg.ID)
		if err != nil && !errors.Is(err, ErrNotFound) {
			return ProductListing{}, err
		}

		// 如果找到了 listing，且 organization 和 sales channel 一致，直接返回
		if listing.ID != "" &&
			listing.Organization.ID == arg.Organization.ID &&
			listing.SalesChannel.StoreKey == arg.SalesChannel.StoreKey &&
			listing.SalesChannel.Platform == arg.SalesChannel.Platform &&
			(listing.SalesChannelProduct.ID == "" || listing.SalesChannelProduct.ID == arg.SalesChannelProduct.ID) {
			return listing, nil
		}

		if listing.ID != "" {
			s.logger.InfoCtx(ctx, "Product listing id or organization or sales channel not match")
		}
	}

	listings, err := s.List(ctx, &ListArg{
		OrganizationID:         arg.Organization.ID,
		SalesChannelStoreKey:   arg.SalesChannel.StoreKey,
		SalesChannelPlatform:   arg.SalesChannel.Platform,
		SalesChannelProductIDs: strings.Join([]string{arg.SalesChannelProduct.ID}, ","),
		Page:                   1,
		Limit:                  1,
	})
	if err != nil {
		return ProductListing{}, err
	}

	if len(listings) > 0 {
		return *listings[0], nil
	}

	return ProductListing{}, ErrNotFound
}

// nolint:gocyclo
func (s *serviceImpl) afterSalesChannelProductEvent(ctx context.Context, id string, model *salesChannelEventModel) error {
	// 获取最新的 listing
	listing, err := s.GetByID(ctx, id)
	if err != nil {
		return err
	}

	// sales channel 的商品状态不是 live 的状态，无需进行后续操作
	if listing.SalesChannelProduct.State != consts.SalesChannelProductStateLive {
		return nil
	}

	// 没有双边链接，无需进行后续操作
	bothConnections, err := s.connectorService.GetBothConnections(ctx, listing.Organization.ID)
	if err != nil {
		return err
	}
	if !bothConnections.IsBothConnections() {
		return nil
	}

	// 任务一: auto-link
	autoLinkNewListing, err := s.autoLinkAfterSaleChannelProductEvent(ctx, model)
	if err == nil {
		listing = *autoLinkNewListing
	}

	lastListing := model.lastListing
	lastListingState := lastListing.State
	auditVersion, err := s.latestAuditVersion(ctx, listing.ID)

	if err != nil {
		if !errors.Is(err, models.ErrResourceNotFound) {
			return errors.WithStack(err)
		}
		if listing.ProductsCenterProduct.ID == "" {
			return nil
		}
	} else {
		lastListing = &auditVersion.ProductListing
		model.lastListing = &auditVersion.ProductListing
		model.lastListing.State = lastListingState //  auditVersion listing 没有维护 state, 这里手动塞入维护
	}

	// 使用 previewListing 进行对比，防止出现状态未更新的情况
	previewListing, err := s.GeneratePreview(ctx, lastListing)
	if err != nil {
		return errors.WithStack(err)
	}
	if previewListing != nil {
		lastListing = previewListing
	}

	productInfoListingCompareModel := newCompareModel(&listing, lastListing, compareSetting{
		ignoreCompareDescription:    true,
		ignoreCompareVariantPackage: true,
		ignoreCompareVariantImage:   true,
		ignoreCompareAttributes:     true,
		ignoreCompareCertification:  true,
		ignoreCompareBrand:          true,
		ignoreCompareSizeChart:      true,
		ignoreCompareCategory:       true,
		ignoreCompareBarcode:        true,
		ignoreCompareMedia:          true,
	}, s.getTiktokCDNDomainRegexps())

	unionSetting, err := s.getUnionSettingByListing(ctx, &listing)
	if err != nil {
		if errors.Is(err, settings.ErrSettingNotFound) {
			s.logger.WarnCtx(ctx, "Union setting not found",
				zap.String("product_listing_id", listing.ID),
				zap.String("organization_id", listing.Organization.ID))
			return nil
		}

		if !errors.Is(err, settings.ErrSettingNotFound) {
			return errors.WithStack(err)
		}
	}

	// 任务二: 价格和库存同步
	priceInventoryCompareModel := newCompareModel(&listing, model.lastListing, compareSetting{
		ignoreCompareDescription:    true,
		ignoreCompareVariantPackage: true,
		ignoreCompareAttributes:     true,
		ignoreCompareCertification:  true,
		ignoreCompareBrand:          true,
		ignoreCompareSizeChart:      true,
		ignoreCompareCategory:       true,
	}, s.getTiktokCDNDomainRegexps())
	if priceInventoryCompareModel.needPublishPriceAndInventory() {
		if err = s.createPublishPricesTask(ctx, &priceTaskArg{
			Listing:   &listing,
			FromEvent: FromEventSalesChannelActiveOrSyncedVariant,
		}); err != nil {
			s.logger.ErrorCtx(ctx, "Create publish prices task error", zap.Error(err))
		}

		if err = s.createPublishInventoriesTask(ctx, &InventoriesTaskArg{
			Listing:   &listing,
			FromEvent: FromEventSalesChannelActiveOrSyncedVariant,
			Enforced:  true,
		}); err != nil {
			s.logger.ErrorCtx(ctx, "Create publish inventories task error", zap.Error(err))
		}
	}

	// 任务三
	// 1.没有 match 时，不需要检测其他商品信息是否有变化
	// 2.审核失败时，不需要检测其他商品信息是否有变化
	if !listing.matched() ||
		listing.Audit.State == consts.AuditStateFailed {
		return nil
	}

	// 只有在已经 match 的情况下，才需要重新刊登
	if productInfoListingCompareModel.needPublishWithSetting(unionSetting) {
		needUpdateListing := productInfoListingCompareModel.isVariantModify() && unionSetting.autoSyncVariant()
		if err = s.afterSalesChannelProductEventPublishListing(ctx, &listing, model, unionSetting, needUpdateListing); err != nil {
			return errors.WithStack(err)
		}
	}

	return nil
}

func (s *serviceImpl) afterSalesChannelProductEventPublishListing(ctx context.Context,
	listing *ProductListing, model *salesChannelEventModel, unionSetting *storeProductListingSetting, needUpdateListing bool) error {
	previewListing, err := s.GeneratePreview(ctx, &ProductListing{
		ID:                    listing.ID,
		Organization:          listing.Organization,
		SalesChannel:          listing.SalesChannel,
		SalesChannelProduct:   listing.SalesChannelProduct,
		ProductsCenterProduct: listing.ProductsCenterProduct,
		Settings:              listing.Settings,
		Product:               model.lastListing.Product,
		Relations:             model.lastListing.Relations,
	})
	if err != nil {
		s.logger.ErrorCtx(ctx, "Failed to generate preview", zap.Error(err))
		return err
	}
	newListing := previewListing.DeepCopy()

	categoryRules, err := s.getCategoryRules(ctx, listing.Organization, listing.SalesChannel, listing.Product.Categories)
	if err != nil {
		return err
	}

	// 有 variant 的变化才需要更新 listing
	if needUpdateListing {
		m := newAfterSalesChannelProductUpdateModel(previewListing, listing, unionSetting, categoryRules)
		// 调用 repo 更新，不需要更新 es 由上层更新
		if err = s.repo.update(ctx, m.buildUpdateArgs()); err != nil {
			return err
		}
		// 如果有更新，需要重新获取最新的 listing
		updateListing, err := s.GetByID(ctx, listing.ID)
		if err != nil {
			return err
		}
		newListing = &updateListing
		// 重新设置 product
		newListing.Product = previewListing.Product
	}

	listingCompareModel := newCompareModel(newListing, listing, compareSetting{}, s.getTiktokCDNDomainRegexps())
	auditVersion := listingCompareModel.buildAuditVersionWithSetting(unionSetting)
	err = s.createAuditVersion(ctx, listing.ID, auditVersion, false)
	if err != nil {
		if errors.Is(err, ErrSameAuditVersion) {
			s.logger.InfoCtx(ctx, "Same audit version, no need to create audit version", zap.String("product_listing_id", listing.ID))
			return nil
		}
		return err
	}

	s.logger.InfoCtx(ctx, "Create audit version success from sales channel product event", zap.String("product_listing_id", listing.ID))
	return s.createPublishTask(ctx, listing)
}

type salesChannelEventModel struct {
	arg         *ProductListingArgs
	lastListing *ProductListing
}

func newSalesChannelEventModel(arg *ProductListingArgs, lastListing *ProductListing) *salesChannelEventModel {
	return &salesChannelEventModel{
		arg:         arg,
		lastListing: lastListing,
	}
}

// ProductStatusReSale 商品从不可售状态变为可售状态
func (m *salesChannelEventModel) isProductStateToLive() bool {
	return m.arg.SalesChannelProduct.State == consts.SalesChannelProductStateLive &&
		m.lastListing.SalesChannelProduct.State != consts.SalesChannelProductStateLive
}

func (m *salesChannelEventModel) collectNeedAutoLinkVariantIDs() []string {
	newVariantIDsSet := set.NewStringSet()
	newFillSKUCodeVariantIDsSet := set.NewStringSet()
	for _, variant := range m.arg.Product.Variants {
		newVariantIDsSet.Add(variant.ID)
		if variant.Sku != "" {
			newFillSKUCodeVariantIDsSet.Add(variant.ID)
		}
	}

	for _, variant := range m.lastListing.Product.Variants {
		newVariantIDsSet.Remove(variant.ID)
		if variant.Sku != "" {
			newFillSKUCodeVariantIDsSet.Remove(variant.ID)
		}
	}

	return newVariantIDsSet.Union(newFillSKUCodeVariantIDsSet).ToList()
}

func (m *salesChannelEventModel) buildSheinUpdateArg(listing *ProductListing) {
	// 如果商品没有 title 表示获取不到 shein 平台数据，需要使用 lastListing product 进行补充
	m.mergeListingProduct(listing)
	// feed 首次刊登的 skc 信息补充信息
	m.mergeListingSKC(listing)
	// 非 feed 刊登的商品删除首次 review failed 的 skc 信息
	m.removeInvalidSKC(listing)
	// 聚合 Skc 的 audit 失败信息到 listing audit 里面
	m.mergeListingAudit(listing)
}

func (m *salesChannelEventModel) mergeListingAudit(listing *ProductListing) {
	mainOption, ok := listing.Product.GetMainOption()
	if !ok {
		return
	}

	audit := Audit{
		State:         consts.AuditStateSucceeded,
		FailedReasons: make(AuditFailedReasons, 0),
	}

	failedReasons := make([]AuditFailedReason, 0)
	inReview := false
	for i := range mainOption.ValueDetails {
		if mainOption.ValueDetails[i].Audit.State == consts.ProductOptionValueAuditStateFailed {
			reasons := make([]string, 0)
			for j := range mainOption.ValueDetails[i].Audit.FailedReasons {
				reasons = append(reasons, mainOption.ValueDetails[i].Audit.FailedReasons[j].Reasons...)
			}
			failedReasons = append(failedReasons, AuditFailedReason{
				Position: mainOption.ValueDetails[i].SalesChannelID,
				Reasons:  reasons,
			})
		}

		if mainOption.ValueDetails[i].Audit.State == consts.ProductOptionValueAuditStateReviewing {
			inReview = true
			break
		}
	}

	if inReview {
		audit.State = consts.AuditStateReviewing
	} else {
		if len(failedReasons) > 0 {
			audit.State = consts.AuditStateFailed
			audit.FailedReasons = failedReasons
		}
	}

	listing.Audit = audit
}

func (m *salesChannelEventModel) mergeListingProduct(listing *ProductListing) {
	if strings.TrimSpace(listing.Product.Title) != "" {
		return
	}

	if len(listing.Product.Variants) > 0 {
		return
	}

	mainOption, ok := listing.Product.GetMainOption()
	if !ok {
		return
	}

	listing.Product = m.lastListing.Product
	listing.Relations = m.lastListing.Relations
	// 补充 audit 数据
	for i := range listing.Product.Options {
		for j := range listing.Product.Options[i].ValueDetails {
			for k := range mainOption.ValueDetails {
				if mainOption.ValueDetails[k].SalesChannelID == listing.Product.Options[i].ValueDetails[j].SalesChannelID {
					listing.Product.Options[i].ValueDetails[j].Audit = mainOption.ValueDetails[k].Audit
				}
			}
		}
	}
}

func (m *salesChannelEventModel) mergeListingSKC(listing *ProductListing) {
	mainOption, ok := listing.Product.GetMainOption()
	if !ok {
		return
	}

	lastMainOption, ok := m.lastListing.Product.GetMainOption()
	if !ok {
		return
	}

	if ok := isSameSKCIDs(*lastMainOption, *mainOption); !ok {
		return
	}

	optionID := lastMainOption.SalesChannelOptionID

	// 如果 product title 为空，表示中台无法拉取到商品信息，使用旧 Listing product 数据
	if strings.TrimSpace(listing.Product.Title) == "" {
		listing.Product.Title = m.lastListing.Product.Title
		listing.Product.Description = m.lastListing.Product.Description
		listing.Product.ShortDescription = m.lastListing.Product.ShortDescription
		listing.Product.Media = m.lastListing.Product.Media
		listing.Product.Categories = m.lastListing.Product.Categories
		listing.Product.Certifications = m.lastListing.Product.Certifications
		listing.Product.Brand = m.lastListing.Product.Brand
		listing.Product.Attributes = m.lastListing.Product.Attributes
		listing.Product.Tags = m.lastListing.Product.Tags
		listing.Product.SizeChart = m.lastListing.Product.SizeChart
		listing.Product.ProductNumber = m.lastListing.Product.ProductNumber
	}

	// 首次同步
	if mainOption.SalesChannelOptionID == "" {
		mainOption.SalesChannelOptionID = optionID
		mainOption.Name = lastMainOption.Name
		mainOption.Values = lastMainOption.Values
	}

	// 补充旧的 SKU 和 relation 信息
	oldSKCsMap := make(map[string]models.ProductOptionValueDetail, 0)
	for i := range lastMainOption.ValueDetails {
		salesChannelID := lastMainOption.ValueDetails[i].SalesChannelID
		if salesChannelID != "" {
			oldSKCsMap[salesChannelID] = lastMainOption.ValueDetails[i]
		}
	}

	for i, skc := range mainOption.ValueDetails {
		// 没有 SKC id
		if skc.SalesChannelID == "" {
			continue
		}

		// 非首次刊登
		if skc.SalesChannelValueID != "" {
			continue
		}

		oldSKC, ok := oldSKCsMap[mainOption.ValueDetails[i].SalesChannelID]
		if !ok {
			continue
		}

		// 使用 listing skc, 保留审核状态
		oldSKC.Audit = skc.Audit
		mainOption.ValueDetails[i] = oldSKC

		variants := m.lastListing.Product.GetVariantsByOption(
			mainOption.SalesChannelOptionID, oldSKC.SalesChannelValueID)

		if len(variants) > 0 {
			variantIDs := make([]string, 0)
			for _, variant := range variants {
				variantIDs = append(variantIDs, variant.ID)
			}

			relations := make([]*ProductListingRelation, 0)
			for _, relation := range m.lastListing.Relations {
				if slicex.ContainsString(variantIDs, relation.ProductListingVariantID) {
					relations = append(relations, relation)
				}
			}
			// 使用 listing variant
			listing.Product.Variants = append(listing.Product.Variants, variants...)
			// 使用 listing relation
			listing.Relations = append(listing.Relations, relations...)
		}
	}
}

func (m *salesChannelEventModel) removeInvalidSKC(listing *ProductListing) {
	// skc 如果没有 option value id，并且 review 是 Failed 表示无效 SKC，直接删除
	mainOption, ok := listing.Product.GetMainOption()
	if !ok {
		return
	}

	validateValueDetails := make([]models.ProductOptionValueDetail, 0)
	for i := range mainOption.ValueDetails {
		if mainOption.ValueDetails[i].SalesChannelValueID == "" &&
			mainOption.ValueDetails[i].Audit.State == consts.ProductOptionValueAuditStateFailed {
			continue
		}
		validateValueDetails = append(validateValueDetails, mainOption.ValueDetails[i])
	}

	mainOption.ValueDetails = validateValueDetails
}

func (m *salesChannelEventModel) buildUpdateArg() *conductorUpdateArgs {
	newListing := m.lastListing.DeepCopy()

	// 重新设置 product listing 的数据
	newListing.Product = m.arg.Product
	newListing.SalesChannelProduct = m.arg.SalesChannelProduct
	newListing.Audit = m.arg.Audit
	newListing.Product.Variants = m.buildVariants()
	newListing.Relations = m.buildRelations()
	newListing.Version = m.arg.Version

	// 补充 shein 的数据
	if m.arg.SalesChannel.Platform == consts.Shein {
		m.buildSheinUpdateArg(newListing)
	}

	// 更新状态和状态
	newListing.ModifyStateAndStatus()
	newListing.ClearReadyStatus()

	// 转换为 conductor update args
	result := convertToConductorUpdateArgs(newListing, m.lastListing)

	return &result
}

func (m *salesChannelEventModel) buildRelationVariantIDMaps() (map[string]string, map[string]string) {
	lastRelationMap := make(map[string]string, len(m.lastListing.Relations))
	for i := range m.lastListing.Relations {
		lastRelationMap[m.lastListing.Relations[i].ID] = m.lastListing.Relations[i].ProductListingVariantID
	}

	lastRelationSalesChannelVariantIDMap := make(map[string]string, len(m.lastListing.Relations))
	for i := range m.lastListing.Relations {
		if m.lastListing.Relations[i].SalesChannelVariant.ID != "" {
			lastRelationSalesChannelVariantIDMap[m.lastListing.Relations[i].SalesChannelVariant.ID] = m.lastListing.Relations[i].ProductListingVariantID
		}
	}

	return lastRelationMap, lastRelationSalesChannelVariantIDMap
}

func (m *salesChannelEventModel) buildRelationMaps() (map[string]*ProductListingRelation, map[string]*ProductListingRelation) {
	lastRelationMap := make(map[string]*ProductListingRelation, len(m.lastListing.Relations))
	for i := range m.lastListing.Relations {
		lastRelationMap[m.lastListing.Relations[i].ID] = m.lastListing.Relations[i]
	}

	lastRelationSalesChannelVariantIDMap := make(map[string]*ProductListingRelation, len(m.lastListing.Relations))
	for i := range m.lastListing.Relations {
		if m.lastListing.Relations[i].SalesChannelVariant.ID != "" {
			lastRelationSalesChannelVariantIDMap[m.lastListing.Relations[i].SalesChannelVariant.ID] = m.lastListing.Relations[i]
		}
	}

	return lastRelationMap, lastRelationSalesChannelVariantIDMap
}

func (m *salesChannelEventModel) buildVariants() []*models.ProductVariant {
	variants := m.arg.Product.Variants
	lastRelationMap, lastRelationSalesChannelVariantIDMap := m.buildRelationVariantIDMaps()
	for i := range variants {
		// 通过 relation ID 匹配，如果有匹配到，直接更新 ProductListingVariantID
		if m.arg.Relations[i].ID != "" {
			if productListingVariantID, ok := lastRelationMap[m.arg.Relations[i].ID]; ok {
				variants[i].ID = productListingVariantID
				continue
			}
		}
		// 通过 sales channel variant ID 匹配，如果有匹配到，直接更新 ProductListingVariantID
		if productListingVariantID, ok := lastRelationSalesChannelVariantIDMap[m.arg.Relations[i].SalesChannelVariant.ID]; ok {
			variants[i].ID = productListingVariantID
			continue
		}

		// 新增的 variant 统一设置为空
		variants[i].ID = ""
	}

	return variants
}

func (m *salesChannelEventModel) buildRelations() []*ProductListingRelation {
	relations := make([]*ProductListingRelation, 0, len(m.arg.Relations))
	lastRelationMap, lastRelationSalesChannelVariantIDMap := m.buildRelationMaps()
	for i := range m.arg.Relations {
		// 通过 relation ID 匹配，如果有匹配到，直接更新 SalesChannelVariant
		if m.arg.Relations[i].ID != "" {
			if lastRelation, ok := lastRelationMap[m.arg.Relations[i].ID]; ok {
				newRelation := lastRelation.DeepCopy()
				newRelation.SalesChannelVariant = m.arg.Relations[i].SalesChannelVariant
				relations = append(relations, newRelation)
				continue
			}
		}
		// 通过 sales channel variant ID 匹配，如果有匹配到，直接更新 ProductListingVariantID
		if lastRelation, ok := lastRelationSalesChannelVariantIDMap[m.arg.Relations[i].SalesChannelVariant.ID]; ok {
			newRelation := lastRelation.DeepCopy()
			newRelation.SalesChannelVariant = m.arg.Relations[i].SalesChannelVariant
			relations = append(relations, newRelation)
			continue
		}
		// 新增的 relation, id 需要设置为空
		m.arg.Relations[i].ID = ""
		relations = append(relations, m.arg.Relations[i])
	}

	return relations
}

func (m *salesChannelEventModel) buildAutoLinkArg() (*AutoLinkArg, error) {
	result := &AutoLinkArg{ID: m.lastListing.ID}
	// 由不可售转为可售, 对所有 SKU 进行一次 auto-link
	if m.isProductStateToLive() {
		return result, nil
	}
	// 有新的 variant || sku由空填充, 进行 auto-link
	if autoLinkVariantIDs := m.collectNeedAutoLinkVariantIDs(); len(autoLinkVariantIDs) > 0 {
		result.TargetVariantIDs = autoLinkVariantIDs
		return result, nil
	}

	// 其他情况不需要 auto-link
	return nil, errors.New("no need to auto link")
}

type afterSalesChannelProductUpdateModel struct {
	newListing    *ProductListing
	lastListing   *ProductListing
	unionSetting  *storeProductListingSetting
	categoryRules *category.RulesOutput
}

func newAfterSalesChannelProductUpdateModel(newListing, lastListing *ProductListing, unionSetting *storeProductListingSetting,
	categoryRules *category.RulesOutput) *afterSalesChannelProductUpdateModel {
	return &afterSalesChannelProductUpdateModel{
		newListing:    newListing,
		lastListing:   lastListing,
		unionSetting:  unionSetting,
		categoryRules: categoryRules,
	}
}

// nolint:dupl
func (m *afterSalesChannelProductUpdateModel) getCreateAndDeleteVariantIDSet() (*sets.StringSet, *sets.StringSet) {
	oldVariantIDs := sets.NewStringSet()
	newVariantIDs := sets.NewStringSet()
	for i := range m.newListing.Product.Variants {
		newVariantIDs.Add(m.newListing.Product.Variants[i].ID)
	}
	for i := range m.lastListing.Product.Variants {
		oldVariantIDs.Add(m.lastListing.Product.Variants[i].ID)
	}
	createVariantIDs := newVariantIDs.Diff(oldVariantIDs)
	deleteVariantIDs := oldVariantIDs.Diff(newVariantIDs)
	return createVariantIDs, deleteVariantIDs
}

func (m *afterSalesChannelProductUpdateModel) buildUpdateArgs() *conductorUpdateArgs {
	newListing := m.lastListing.DeepCopy()
	createVariantIDs, deleteVariantIDs := m.getCreateAndDeleteVariantIDSet()
	newListing.Product.Variants = m.buildVariants(createVariantIDs)
	newListing.Relations = m.buildRelations(createVariantIDs, deleteVariantIDs)
	newListing.ModifyStateAndStatus()
	result := convertToConductorUpdateArgs(newListing, m.lastListing)
	return &result
}

// nolint:dupl
func (m *afterSalesChannelProductUpdateModel) buildVariants(createVariantIDs *sets.StringSet) []*models.ProductVariant {
	variants := m.lastListing.Product.Variants
	if len(createVariantIDs.ToList()) == 0 {
		return variants
	}

	for i := range m.newListing.Product.Variants {
		if createVariantIDs.Contains(m.newListing.Product.Variants[i].ID) {
			variants = append(variants, m.newListing.Product.Variants[i])
		}
	}
	sort.Slice(variants, func(i, j int) bool {
		return variants[i].Position < variants[j].Position
	})

	return variants
}

func (m *afterSalesChannelProductUpdateModel) buildRelations(createVariantIDs *sets.StringSet, deleteVariantIDs *sets.StringSet) []*ProductListingRelation {
	relations := m.lastListing.Relations
	if len(createVariantIDs.ToList()) > 0 {
		for i := range m.newListing.Relations {
			if createVariantIDs.Contains(m.newListing.Relations[i].ProductListingVariantID) {
				// 需要重新刊登的 variant，需要重新生成 relation，并且清空旧 sales channel 的信息
				relation := m.newListing.Relations[i].DeepCopy()
				relation.ID = ""
				relation.SalesChannelVariant = SalesChannelVariant{}
				relations = append(relations, relation)
			}
		}
	}

	if len(deleteVariantIDs.ToList()) > 0 {
		for i := range relations {
			if deleteVariantIDs.Contains(relations[i].ProductListingVariantID) {
				relations[i].ProductsCenterVariant = ProductsCenterVariant{}
			}
		}
	}
	return relations
}

func isAfterSalesErrorIgnorable(err error) bool {
	// channel product 价格发生了改变, 需要发起任务纠正价格, 但过程中遇到了币种转换错误, 可以忽略
	if errors.Is(err, organization_settings.ErrSpecificCurrencyConvertorNotFound) {
		return true
	}
	return false
}

// isSameSKCIDs checks if the SalesChannelID arrays in the ValueDetails of two ProductOption are identical.
func isSameSKCIDs(lastMainOption, newMainOption models.ProductOption) bool {
	if len(lastMainOption.ValueDetails) == 0 ||
		len(lastMainOption.ValueDetails) == 0 ||
		len(lastMainOption.ValueDetails) != len(lastMainOption.ValueDetails) {
		return false
	}

	lastSKCIDs := extractSKCIDs(lastMainOption)
	newSKCIDs := extractSKCIDs(newMainOption)

	if len(lastSKCIDs) == 0 ||
		len(newSKCIDs) == 0 ||
		len(lastSKCIDs) != len(newSKCIDs) {
		return false
	}

	sort.Strings(lastSKCIDs)
	sort.Strings(newSKCIDs)

	for i := range lastSKCIDs {
		if lastSKCIDs[i] != newSKCIDs[i] {
			return false
		}
	}

	return true
}

// extractSKCIDs extracts SalesChannelID from the ValueDetails of a ProductOption.
func extractSKCIDs(option models.ProductOption) []string {
	skcIDs := make([]string, 0)
	for _, valueDetail := range option.ValueDetails {
		if valueDetail.SalesChannelID != "" {
			skcIDs = append(skcIDs, valueDetail.SalesChannelID)
		}
	}
	return skcIDs
}
