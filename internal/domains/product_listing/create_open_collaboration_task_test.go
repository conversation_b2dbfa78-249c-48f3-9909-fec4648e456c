package product_listing

import (
	"testing"

	"github.com/go-playground/validator/v10"
	"github.com/stretchr/testify/assert"

	"github.com/AfterShip/gopkg/log"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
)

func TestCreateOpenCollaborationTask_validate(t *testing.T) {
	tests := []struct {
		name    string
		input   *CreateOpenCollaborationTaskInput
		wantErr error
	}{
		{
			name: "有效输入",
			input: &CreateOpenCollaborationTaskInput{
				OrganizationID: "org123",
				SalesChannel: models.SalesChannel{
					Platform: "tiktok",
					StoreKey: "store123",
				},
				ProductListingIDs: []string{"pl1", "pl2"},
				CommissionRate:    "20",
			},
			wantErr: nil,
		},
		{
			name: "OrganizationID为空",
			input: &CreateOpenCollaborationTaskInput{
				OrganizationID: "",
				SalesChannel: models.SalesChannel{
					Platform: "tiktok",
					StoreKey: "store123",
				},
				ProductListingIDs: []string{"pl1", "pl2"},
				CommissionRate:    "20",
			},
			wantErr: validator.ValidationErrors{},
		},
		{
			name: "ProductListingIDs为空数组",
			input: &CreateOpenCollaborationTaskInput{
				OrganizationID: "org123",
				SalesChannel: models.SalesChannel{
					Platform: "tiktok",
					StoreKey: "store123",
				},
				ProductListingIDs: []string{},
				CommissionRate:    "20",
			},
			wantErr: ErrNoProductListingID,
		},
		{
			name: "ProductListingIDs为nil",
			input: &CreateOpenCollaborationTaskInput{
				OrganizationID: "org123",
				SalesChannel: models.SalesChannel{
					Platform: "tiktok",
					StoreKey: "store123",
				},
				ProductListingIDs: nil,
				CommissionRate:    "20",
			},
			wantErr: validator.ValidationErrors{},
		},
		{
			name: "CommissionRate为空",
			input: &CreateOpenCollaborationTaskInput{
				OrganizationID: "org123",
				SalesChannel: models.SalesChannel{
					Platform: "tiktok",
					StoreKey: "store123",
				},
				ProductListingIDs: []string{"pl1", "pl2"},
				CommissionRate:    "",
			},
			wantErr: validator.ValidationErrors{},
		},
		{
			name: "CommissionRate不是数字",
			input: &CreateOpenCollaborationTaskInput{
				OrganizationID: "org123",
				SalesChannel: models.SalesChannel{
					Platform: "tiktok",
					StoreKey: "store123",
				},
				ProductListingIDs: []string{"pl1", "pl2"},
				CommissionRate:    "abc",
			},
			wantErr: ErrInvalidCommissionRate,
		},
		{
			name: "CommissionRate小于1",
			input: &CreateOpenCollaborationTaskInput{
				OrganizationID: "org123",
				SalesChannel: models.SalesChannel{
					Platform: "tiktok",
					StoreKey: "store123",
				},
				ProductListingIDs: []string{"pl1", "pl2"},
				CommissionRate:    "0.5",
			},
			wantErr: ErrInvalidCommissionRate,
		},
		{
			name: "CommissionRate等于1",
			input: &CreateOpenCollaborationTaskInput{
				OrganizationID: "org123",
				SalesChannel: models.SalesChannel{
					Platform: "tiktok",
					StoreKey: "store123",
				},
				ProductListingIDs: []string{"pl1", "pl2"},
				CommissionRate:    "1",
			},
			wantErr: nil,
		},
		{
			name: "CommissionRate等于80",
			input: &CreateOpenCollaborationTaskInput{
				OrganizationID: "org123",
				SalesChannel: models.SalesChannel{
					Platform: "tiktok",
					StoreKey: "store123",
				},
				ProductListingIDs: []string{"pl1", "pl2"},
				CommissionRate:    "80",
			},
			wantErr: nil,
		},
		{
			name: "CommissionRate大于80",
			input: &CreateOpenCollaborationTaskInput{
				OrganizationID: "org123",
				SalesChannel: models.SalesChannel{
					Platform: "tiktok",
					StoreKey: "store123",
				},
				ProductListingIDs: []string{"pl1", "pl2"},
				CommissionRate:    "81",
			},
			wantErr: ErrInvalidCommissionRate,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			task := &CreateOpenCollaborationTask{
				Logger:    log.GlobalLogger(),
				Validator: validator.New(),
			}
			err := task.validate(tt.input)
			if tt.wantErr != nil {
				if _, ok := tt.wantErr.(validator.ValidationErrors); ok {
					assert.IsType(t, validator.ValidationErrors{}, err)
				} else {
					assert.Equal(t, tt.wantErr, err)
				}
			} else {
				assert.NoError(t, err)
			}
		})
	}
}
