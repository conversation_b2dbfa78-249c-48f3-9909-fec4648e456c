package product_listing

import (
	"context"
	"encoding/json"
	"os"
	"testing"

	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"

	"github.com/AfterShip/connectors-library/sdks/products_center"
	"github.com/AfterShip/feed-sdk-go/events"
	"github.com/AfterShip/pltf-pd-product-listings/internal/consts"
	"github.com/AfterShip/pltf-pd-product-listings/internal/datastore"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/category"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/common/databus"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/settings"
	"github.com/AfterShip/pltf-pd-product-listings/internal/logger"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
	"github.com/AfterShip/pltf-pd-product-listings/internal/third_party/bme"
	"github.com/AfterShip/pltf-pd-product-listings/internal/utils/elasticsearch"
)

func TestPushToChannelModel_BuildArgs(t *testing.T) {
	testCases := []struct {
		name string
		arg  *PushToChannelArg
	}{
		{
			name: "Test case 1",
			arg: &PushToChannelArg{
				SalesChannel: models.SalesChannel{
					Platform:      "platform",
					StoreKey:      "store_key",
					CountryRegion: "country_region",
				},
				Organization: models.Organization{
					ID: "organization_id",
				},
				ProductsCenterProduct: ProductsCenterProduct{
					ID:                 "product_id",
					ConnectorProductID: "connector_product_id",
					PublishState:       consts.ProductsCenterProductPublishStateActive,
					Source: ProductsCenterProductSource{
						StoreKey: "store_key",
						Platform: "platform",
						ID:       "source_id",
					},
				},
				Product: models.Product{
					Title: "title",
					Variants: []*models.ProductVariant{
						{
							ID: "variant_id",
						},
					},
				},
				Relations: []*PushToChannelRelationArg{
					{
						ProductListingVariantID: "variant_id",
					},
				},
				FeedCustomizationParams: FeedCustomizationParams{
					FeedCategoryTemplateID: "feed_category_template_id",
				},
				Ready: Ready{
					FailedReasons: ReadyFailedReasons{
						{
							Position:    "position",
							Reasons:     []string{"reason"},
							Suggestions: []string{"position"},
						},
					},
				},
			},
		},
	}

	for _, tt := range testCases {
		t.Run(tt.name, func(t *testing.T) {
			model := newPushToChannelModel(tt.arg)
			result := model.buildArgs()
			require.Equal(t, tt.arg.Organization.ID, result.Organization.ID)
			require.Equal(t, tt.arg.SalesChannel.Platform, result.SalesChannel.Platform)
			require.Equal(t, tt.arg.SalesChannel.StoreKey, result.SalesChannel.StoreKey)
			require.Equal(t, tt.arg.ProductsCenterProduct.ID, result.ProductsCenterProduct.ID)
			require.Equal(t, tt.arg.Product.Title, result.Product.Title)
			require.Equal(t, tt.arg.Relations[0].ProductListingVariantID, result.Relations[0].ProductListingVariantID)
			require.Equal(t, tt.arg.FeedCustomizationParams.FeedCategoryTemplateID, result.FeedCustomizationParams.FeedCategoryTemplateID)
			require.Equal(t, tt.arg.Ready.FailedReasons[0].Position, result.Ready.FailedReasons[0].Position)
			require.Equal(t, tt.arg.Ready.FailedReasons[0].Reasons, result.Ready.FailedReasons[0].Reasons)
			require.Equal(t, tt.arg.Ready.FailedReasons[0].Suggestions, result.Ready.FailedReasons[0].Suggestions)
			require.Equal(t, consts.SettingPreferenceStore, result.Settings.InventorySyncSetting.Preference)
			require.Equal(t, consts.SettingPreferenceStore, result.Settings.ProductSyncSetting.Preference)
			require.Equal(t, consts.SettingPreferenceStore, result.Settings.PriceSyncSetting.Preference)
			require.Equal(t, "", result.SalesChannelProduct.ID)
		})
	}
}

func TestPushToChannelModel_BuildPushToChannelUniqueKey(t *testing.T) {
	arg := &PushToChannelArg{
		// Fill in the necessary fields for your test case
		Organization: models.Organization{
			ID: "organization_id",
		},
		SalesChannel: models.SalesChannel{
			Platform: "platform",
			StoreKey: "store_key",
		},
		ProductsCenterProduct: ProductsCenterProduct{
			ID: "product_id",
		},
	}

	arg2 := &PushToChannelArg{
		// Fill in the necessary fields for your test case
		Organization: models.Organization{
			ID: "organization_id",
		},
		SalesChannel: models.SalesChannel{
			Platform: "platform",
			StoreKey: "store_key",
		},
		ProductsCenterProduct: ProductsCenterProduct{
			ID: "product_id2",
		},
	}

	model := newPushToChannelModel(arg)
	result := model.buildPushToChannelUniqueKey()
	model2 := newPushToChannelModel(arg2)
	result2 := model2.buildPushToChannelUniqueKey()
	require.NotEqual(t, "", result)
	require.NotEqual(t, "", result2)
	require.NotEqual(t, result, result2)
}

func Test_serviceImpl_PushToChannel(t *testing.T) {
	config := loadConfig(t)

	spannerCli := datastore.Get().SpannerCli
	esCli := datastore.Get().ESClient
	err := elasticsearch.CreateTestIndexWithAlias(esCli, "pd_product_listings_2024", searchIndex, "product_listings_mapping.json")
	esCli.Refresh().Index(searchIndex).Do(context.Background())
	require.NoError(t, err)

	redisCli := datastore.Get().RedisClient
	locker := datastore.Get().RedisLocker
	categoryMock := new(category.MockCategoryService)
	databusMock := new(databus.MockDatabusService)
	listingEventsServiceMock := new(events.MockListingEventsService)
	listingEventsServiceMock.On("CommonEvent", mock.Anything, mock.Anything).Return(nil)
	listingEventsServiceMock.On("ModifyStateEvent", mock.Anything, mock.Anything).Return(nil)
	listingLinkEventsServiceMock := new(events.MockListingLinkEventsService)
	listingLinkEventsServiceMock.On("AutoLinkEvent", mock.Anything, mock.Anything).Return(nil)
	listingLinkEventsServiceMock.On("LinkRecordEvent", mock.Anything, mock.Anything).Return(nil)
	feedEventService := &events.FeedEventsService{
		ListingEventsService:     listingEventsServiceMock,
		ListingLinkEventsService: listingLinkEventsServiceMock,
	}
	mockSettingService := new(settings.MockSettingService)
	mockSettingService.On("List", mock.Anything, mock.Anything).Return([]*settings.Setting{}, nil)
	productsCenterProductMock := new(products_center.MockProductAPICollection)
	productsCenterCli := &products_center.Client{
		Product: productsCenterProductMock,
	}
	productsCenterProductMock.On("GetByID", mock.Anything, mock.Anything).Return(nil, nil)
	productsCenterProductMock.On("List", mock.Anything, mock.Anything).Return([]*products_center.Product{}, nil)

	bmeService := new(bme.MockBmeService)
	bmeService.On("Recording", mock.Anything).Return(nil)
	bmeService.On("CollectBusinessEvents", mock.Anything).Return(nil)
	bmeCli := &bme.Client{
		RecordService: bmeService,
	}

	s := NewService(logger.Get(), spannerCli, esCli, redisCli, locker, productsCenterCli, mockSettingService,
		nil, categoryMock, nil, nil, databusMock, config, nil, nil, feedEventService,
		bmeCli, nil, nil)

	bytes, err := os.ReadFile("./testdata/push_to_channel.json")
	require.NoError(t, err)
	outputs := make([]*PushToChannelArg, 0)
	err = json.Unmarshal(bytes, &outputs)
	require.NoError(t, err)

	testCases := []struct {
		name     string
		arg      *PushToChannelArg
		mock     func()
		hasError bool
	}{
		{
			name: "Test case 1 push success",
			arg:  outputs[0],
			mock: func() {
				categoryMock.On("GetCategoryRules", mock.Anything, mock.Anything).Return(category.RulesOutput{
					ExternalCategoryID: "939272",
					Rule: category.Rule{
						ProductCertifications: []*category.ProductCertification{},
						SizeChart: &category.SizeChart{
							IsRequired:  false,
							IsSupported: false,
						},
						PackageDimension: &category.PackageDimension{
							IsRequired: false,
						},
					},
				}, nil).Once()
				categoryMock.On("GetCategoryAttributes", mock.Anything, mock.Anything).Return(category.AttributesOutput{
					ExternalCategoryID: "939272",
					Attributes: []category.Attribute{
						{
							ID:         "1",
							IsRequired: false,
						},
					},
				}, nil).Once()
				databusMock.On("SendToPubSub", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(nil).Once()
			},
			hasError: false,
		},
		{
			name: "Test case 2 is pushed",
			arg:  outputs[0],
			mock: func() {
				categoryMock.On("GetCategoryRules", mock.Anything, mock.Anything).Return(category.RulesOutput{
					ExternalCategoryID: "939272",
					Rule: category.Rule{
						ProductCertifications: []*category.ProductCertification{},
						SizeChart: &category.SizeChart{
							IsRequired:  false,
							IsSupported: false,
						},
						PackageDimension: &category.PackageDimension{
							IsRequired: false,
						},
					},
				}, nil).Once()
				categoryMock.On("GetCategoryAttributes", mock.Anything, mock.Anything).Return(category.AttributesOutput{
					ExternalCategoryID: "939272",
					Attributes: []category.Attribute{
						{
							ID:         "1",
							IsRequired: false,
						},
					},
				}, nil).Once()
				databusMock.On("SendToPubSub", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(nil).Once()
			},
			hasError: true,
		},
	}
	ctx := context.Background()
	for _, tt := range testCases {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mock != nil {
				tt.mock()
			}
			result, err := s.PushToChannel(ctx, tt.arg)
			if tt.hasError {
				require.Error(t, err)
			} else {
				require.NoError(t, err)
				require.NotEmpty(t, result.ID)
				require.Equal(t, tt.arg.Organization.ID, result.Organization.ID)
				require.Equal(t, tt.arg.SalesChannel.Platform, result.SalesChannel.Platform)
				require.Equal(t, tt.arg.SalesChannel.StoreKey, result.SalesChannel.StoreKey)
				require.Equal(t, tt.arg.ProductsCenterProduct.ID, result.ProductsCenterProduct.ID)
				require.Equal(t, tt.arg.Product.Title, result.Product.Title)
				require.Equal(t, len(tt.arg.Product.Variants), len(result.Product.Variants))
				require.Equal(t, tt.arg.Product.Variants[0].ID, result.Product.Variants[0].ID)
				require.Equal(t, tt.arg.FeedCustomizationParams.FeedCategoryTemplateID, result.FeedCustomizationParams.FeedCategoryTemplateID)
				require.Equal(t, consts.SettingPreferenceStore, result.Settings.InventorySyncSetting.Preference)
				require.Equal(t, consts.SettingPreferenceStore, result.Settings.ProductSyncSetting.Preference)
				require.Equal(t, consts.SettingPreferenceStore, result.Settings.PriceSyncSetting.Preference)
				require.Equal(t, "", result.SalesChannelProduct.ID)
				require.Equal(t, consts.LinkStatusLinked, result.LinkStatus)
				require.Equal(t, consts.SyncStatusUnsync, result.SyncStatus)
				require.Equal(t, consts.ReadyStatusReady, result.Ready.Status)
				require.Equal(t, consts.LinkStatusLinked, result.Relations[0].LinkStatus)
				require.Equal(t, consts.SyncStatusUnsync, result.Relations[0].SyncStatus)
				require.Equal(t, len(tt.arg.Relations), len(result.Relations))
				require.Equal(t, tt.arg.Relations[0].ProductListingVariantID, result.Relations[0].ProductListingVariantID)
				require.Equal(t, tt.arg.Relations[0].ProductsCenterVariant.ID, result.Relations[0].ProductsCenterVariant.ID)
				require.Equal(t, tt.arg.Relations[0].ProductsCenterVariant.ProductID, result.Relations[0].ProductsCenterVariant.ProductID)
			}
		})
	}
}
