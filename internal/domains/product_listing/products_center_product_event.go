package product_listing

import (
	"context"
	"regexp"
	"sort"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	connector_lib_utils "github.com/AfterShip/connectors-library/utils"
	"github.com/AfterShip/connectors-library/utils/sets"
	"github.com/AfterShip/feed-sdk-go/events"
	"github.com/AfterShip/gopkg/facility/set"
	"github.com/AfterShip/gopkg/log"
	"github.com/AfterShip/pltf-pd-product-listings/internal/utils/toolbox"

	"github.com/AfterShip/pltf-pd-product-listings/internal/config"
	"github.com/AfterShip/pltf-pd-product-listings/internal/consts"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/category"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
)

// ProductsCenterProductEvent 源产品中心产品事件
// nolint:gocyclo
func (s *serviceImpl) ProductsCenterProductEvent(ctx context.Context, id string, arg *ProductsCenterProductEventArg) (ProductListing, error) {
	ctx = log.AppendFieldsToContext(ctx,
		zap.String("product_listing_id", id),
		zap.String("products_center_product_id", arg.ProductsCenterProduct.ID))

	var (
		err         error
		lastListing ProductListing
		newListing  ProductListing
	)

	mutex, err := s.acquireListingLock(ctx, id)
	if err != nil {
		s.logger.WarnCtx(ctx, "Failed to acquire version lock for products center product event", zap.Error(err))
		return ProductListing{}, errors.WithStack(err)
	}

	defer func() {
		s.releaseListingLock(ctx, mutex)

		// biz activity log
		s.saveModifyActivityLog(ctx, &newListing, &lastListing)
		s.saveVariantActivityLog(ctx, &newListing, &lastListing)
		s.saveEventActivityLog(ctx, &newListing, events.TypeListingProductsCenterEvent, err)
		logZaps := make([]zap.Field, 0)
		if err != nil {
			logZaps = append(logZaps, zap.String("error", err.Error()))
			logZaps = append(logZaps, zap.String("arg", connector_lib_utils.GetJsonIndent(arg)))
		}
		s.logger.InfoCtx(ctx, "handler products center product event", logZaps...)
	}()

	// 获取 listing
	listing, err := s.GetByID(ctx, id)
	if err != nil {
		return ProductListing{}, err
	}

	// 没有双边链接，无需进行后续操作
	bothConnections, err := s.connectorService.GetBothConnections(ctx, listing.Organization.ID)
	if err != nil {
		return ProductListing{}, err
	}
	if !bothConnections.IsBothConnectionsWithSaleChannel(listing.SalesChannel) {
		return ProductListing{}, ErrBothConnectionIsNotFound
	}

	ctx = log.AppendFieldsToContext(ctx, zap.String("organization_id", listing.Organization.ID))

	// 记录日志使用
	lastListing = *listing.DeepCopy()

	if !listing.canUpdate() ||
		listing.State == consts.ProductListingProductStateReviewing {
		return ProductListing{}, ErrPublishStateInUpdate
	}

	// 如果需要强制更新，不判断商品状态
	if !arg.ForcePublish &&
		listing.SyncStatus == consts.SyncStatusSynced &&
		listing.SalesChannelProduct.State != consts.SalesChannelProductStateLive &&
		listing.SalesChannelProduct.State != consts.SalesChannelProductStatePending {
		return ProductListing{}, ErrNotLiveProduct
	}

	unionSetting, err := s.getUnionSettingByListing(ctx, &listing)
	if err != nil {
		return ProductListing{}, err
	}

	categoryRules := &category.RulesOutput{}
	categoryAttributes := &category.AttributesOutput{}
	if len(listing.Product.Categories) > 0 && listing.Product.Categories[0].SalesChannelID != "" {
		categoryRules, err = s.getCategoryRules(ctx, listing.Organization, listing.SalesChannel, listing.Product.Categories)
		if err != nil {
			return ProductListing{}, err
		}
		categoryAttributes, err = s.getCategoryAttributes(ctx, listing.Organization, listing.SalesChannel, listing.Product.Categories)
		if err != nil {
			return ProductListing{}, err
		}
	}
	salesChannelRegionConfig, err := s.conf.LoadChannelRegionConfig(listing.SalesChannel.Platform, listing.SalesChannel.CountryRegion)
	if err != nil {
		return ProductListing{}, err
	}

	// 构建更新参数
	removeVariantsImage := shouldRemoveVariantsImagesForEvent(listing.Organization.ID, arg.Product, s.conf.DynamicConfigs.TikTokSyncConfig)
	productListingArg := arg.convertToProductListingArgs(&listing, removeVariantsImage, s.conf.DynamicConfigs.AmazonOptionNameMapping)

	model := newProductsCenterEventModel(productListingArg, &listing, unionSetting,
		categoryRules, categoryAttributes, salesChannelRegionConfig, s.getTiktokCDNDomainRegexps())
	// 调用 repo 更新
	if err = s.repo.update(ctx, model.buildUpdateArgs()); err != nil {
		s.logger.ErrorCtx(ctx, "Failed to update product listing from products center product event", zap.Error(err))
		return ProductListing{}, err
	}

	newListing, err = s.GetByID(ctx, listing.ID)
	if err != nil {
		return ProductListing{}, err
	}

	copyNewListing := newListing.DeepCopy()
	err = s.afterProductsCenterProductEvent(ctx, copyNewListing, model, arg.ForcePublish)
	if err != nil {
		s.logger.WarnCtx(ctx, "Failed to process after products center product event", zap.Error(err))
		return ProductListing{}, errors.WithStack(err)
	}

	// 更新 ES，err 不外放
	if err := s.esRepo.BatchUpsertProductListings(ctx, []*ProductListing{&newListing}); err != nil {
		s.logger.With(zap.String("product_listing_id", newListing.ID)).WarnCtx(ctx, "Failed to upsert listing data to ES", zap.Error(err))
	}

	return newListing, nil
}

// ProductsCenterProductDeleteEvent 源产品删除事件，unmatch，如果是 synced 发起 deactivate
func (s *serviceImpl) ProductsCenterProductDeleteEvent(ctx context.Context, id string) (ProductListing, error) {
	var (
		err         error
		lastListing ProductListing
		newListing  ProductListing
	)

	mutex, err := s.acquireListingLock(ctx, id)
	if err != nil {
		return ProductListing{}, errors.WithStack(err)
	}

	defer func() {
		s.releaseListingLock(ctx, mutex)

		// activity log
		s.saveModifyActivityLog(ctx, &newListing, &lastListing)
		s.saveVariantActivityLog(ctx, &newListing, &lastListing)
		s.saveEventActivityLog(ctx, &newListing, events.TypeListingProductsCenterEvent, err)
	}()

	// 获取 listing
	listing, err := s.GetByID(ctx, id)
	if err != nil {
		return ProductListing{}, err
	}

	// 记录日志使用
	lastListing = *listing.DeepCopy()

	// not match
	if listing.ProductsCenterProduct.ID == "" {
		return listing, nil
	}

	// unsync listing 直接删除
	if listing.SyncStatus == consts.SyncStatusUnsync {
		return ProductListing{}, s.delete(ctx, &listing, false)
	}

	newListing, err = s.unMatchSyncedListing(ctx, &listing)

	return newListing, err
}

// nolint:gocyclo
func (s *serviceImpl) unMatchSyncedListing(ctx context.Context, listing *ProductListing) (ProductListing, error) {
	// unmatch
	newListing := listing.DeepCopy()
	newListing.ProductsCenterProduct = ProductsCenterProduct{}
	for i := range newListing.Relations {
		if newListing.Relations[i].ProductsCenterVariant.ProductID == listing.ProductsCenterProduct.ID {
			newListing.Relations[i].ProductsCenterVariant = ProductsCenterVariant{}
		}
	}
	newListing.ModifyStateAndStatus()
	result := convertToConductorUpdateArgs(newListing, listing)
	if err := s.repo.update(ctx, &result); err != nil {
		return ProductListing{}, err
	}

	// unmatch 之后更新 searchable product sales channel
	if err := s.notifySalesChannelUpsertEvent(ctx, listing); err != nil {
		s.logger.WarnCtx(ctx, "send relation upsert to pub/sub failed",
			zap.String("connector_product_id", listing.ProductsCenterProduct.ConnectorProductID),
			zap.Error(err))
	}

	// 只要有 Match 关系，并且是 active 的状态，就发起 delete 事件
	if listing.State == consts.ProductListingProductStateActive {
		if err := s.createDeleteTask(ctx, listing); err != nil {
			return ProductListing{}, err
		}
	}

	pl, err := s.GetByID(ctx, listing.ID)
	if err != nil {
		return ProductListing{}, err
	}

	// 更新 ES
	if err = s.esRepo.BatchUpsertProductListings(ctx, []*ProductListing{&pl}); err != nil {
		s.logger.With(zap.String("product_listing_id", newListing.ID)).WarnCtx(ctx, "Failed to upsert listing data to ES", zap.Error(err))
	}

	return pl, nil
}

func (s *serviceImpl) afterProductsCenterProductEvent(ctx context.Context,
	listing *ProductListing, model *productsCenterEventModel, forcePublish bool) error {

	// unsync listing 不需要做处理
	if listing.SyncStatus == consts.SyncStatusUnsync {
		return nil
	}

	// 需要使用最新的 product 信息来进行比较，因为 product 可能还未写入到 listing, 只是需要写入到 audit version
	listing.Product = model.arg.Product

	listingCompareModel := newCompareModel(listing, model.lastListing, compareSetting{}, s.getTiktokCDNDomainRegexps())
	if forcePublish || listingCompareModel.needPublishWithSetting(model.unionSetting) {
		auditVersion := listingCompareModel.buildAuditVersionWithSetting(model.unionSetting)

		// 判断是否需要比较
		needCompare := true
		if forcePublish {
			needCompare = false
		}

		if err := s.createAuditVersion(ctx, listing.ID, auditVersion, needCompare); err != nil {
			return err
		}
		s.logger.InfoCtx(ctx, "Create audit version success from products center", zap.String("product_listing_id", listing.ID))
		return s.createPublishTask(ctx, listing)
	}
	return nil
}

type productsCenterEventModel struct {
	arg                      *ProductListingArgs
	lastListing              *ProductListing
	unionSetting             *storeProductListingSetting
	categoryRules            *category.RulesOutput
	categoryAttributes       *category.AttributesOutput
	salesChannelRegionConfig *config.ChannelRegionConfig
	ttsCDNDomainRegExps      []*regexp.Regexp
}

func newProductsCenterEventModel(
	arg *ProductListingArgs,
	lastListing *ProductListing,
	unionSetting *storeProductListingSetting,
	categoryRules *category.RulesOutput,
	categoryAttributes *category.AttributesOutput,
	salesChannelRegionConfig *config.ChannelRegionConfig,
	ttsCDNDomainRegExps []*regexp.Regexp,
) *productsCenterEventModel {

	// overwrite arg product barcode, if listing is saved barcode
	for i := range arg.Product.Variants {
		if arg.Product.Variants[i].Barcode.Value != "" && arg.Product.Variants[i].Barcode.Type == "" {
			barcodeType := toolbox.BarcodeType(arg.Product.Variants[i].Barcode.Value)
			if barcodeType != "" {
				arg.Product.Variants[i].Barcode.Type = barcodeType
			} else {
				// 不是有效的 barcode，清空 barcode
				arg.Product.Variants[i].Barcode.Value = ""
			}
		}

		for j := range lastListing.Product.Variants {
			if arg.Product.Variants[i].ID == lastListing.Product.Variants[j].ID {
				if lastListing.Product.Variants[j].Barcode.Type != "" && lastListing.Product.Variants[j].Barcode.Value != "" {
					arg.Product.Variants[i].Barcode = lastListing.Product.Variants[j].Barcode
				}
			}
		}
	}

	return &productsCenterEventModel{
		arg:                      arg,
		lastListing:              lastListing,
		unionSetting:             unionSetting,
		categoryRules:            categoryRules,
		categoryAttributes:       categoryAttributes,
		salesChannelRegionConfig: salesChannelRegionConfig,
		ttsCDNDomainRegExps:      ttsCDNDomainRegExps,
	}
}

// nolint:dupl
func (m *productsCenterEventModel) getCreateAndDeleteVariantIDSet() (*sets.StringSet, *sets.StringSet) {
	oldVariantIDs := sets.NewStringSet()
	newVariantIDs := sets.NewStringSet()
	for i := range m.arg.Product.Variants {
		newVariantIDs.Add(m.arg.Product.Variants[i].ID)
	}
	for i := range m.lastListing.Product.Variants {
		oldVariantIDs.Add(m.lastListing.Product.Variants[i].ID)
	}
	createVariantIDs := newVariantIDs.Diff(oldVariantIDs)
	deleteVariantIDs := oldVariantIDs.Diff(newVariantIDs)
	return createVariantIDs, deleteVariantIDs
}

func (m *productsCenterEventModel) buildProductTitle() string {
	if m.unionSetting.autoSyncProductDetailField(consts.ProductDetailFieldTitle) {
		return m.arg.Product.Title
	}
	return m.lastListing.Product.Title
}

func (m *productsCenterEventModel) buildProductDescription() (string, string) {
	if m.unionSetting.autoSyncProductDetailField(consts.ProductDetailFieldDescription) {
		return m.arg.Product.Description, m.arg.Product.ShortDescription
	}
	return m.lastListing.Product.Description, m.lastListing.Product.ShortDescription
}

func (m *productsCenterEventModel) buildProductMedia() []*models.ProductMedia {
	if m.unionSetting.autoSyncProductDetailField(consts.ProductDetailFieldMedia) {
		return m.arg.Product.Media
	}
	return m.lastListing.Product.Media
}

func (m *productsCenterEventModel) buildPendingUpdateArg() *conductorUpdateArgs {
	listing := m.lastListing.DeepCopy()
	listing.ProductsCenterProduct.PublishState = m.arg.ProductsCenterProduct.PublishState
	listing.Product.Title = m.buildProductTitle()
	listing.Product.Description, listing.Product.ShortDescription = m.buildProductDescription()
	listing.Product.Media = m.buildProductMedia()
	listing.Product.Variants = m.buildPendingVariants()
	listing.Product.Options = m.buildProductOptions()
	listing.Relations = m.buildPendingRelations()
	listing.Ready = m.arg.Ready

	listing.ModifyStateAndStatus()
	listing.SetReadyStatus(m.categoryRules, m.categoryAttributes, m.salesChannelRegionConfig, m.ttsCDNDomainRegExps)
	result := convertToConductorUpdateArgs(listing, m.lastListing)

	return &result
}

func (m *productsCenterEventModel) buildProductOptions() []*models.ProductOption {
	if m.unionSetting.autoSyncVariant() {
		return m.arg.Product.Options
	}
	return m.lastListing.Product.Options
}

func (m *productsCenterEventModel) buildPendingVariants() []*models.ProductVariant {
	variants := m.lastListing.Product.Variants
	if m.unionSetting.autoSyncVariant() {
		variants = m.arg.Product.Variants
	}

	m.overWritePrice(variants)
	m.overWriteInventory(variants)
	m.overWriteBarcode(variants)

	return variants
}

func (m *productsCenterEventModel) overWriteInventory(variants []*models.ProductVariant) {
	inventorySyncSetting := m.unionSetting.getInventorySyncSetting()
	// 开启同步，使用最新的库存
	if inventorySyncSetting.EnabledAutoSync() {
		for i := range variants {
			for j := range m.arg.Product.Variants {
				if variants[i].ID == m.arg.Product.Variants[j].ID {
					variants[i].InventoryQuantity = m.arg.Product.Variants[j].InventoryQuantity
				}
			}
		}
	}
	// 关闭同步，使用旧的库存
	if !inventorySyncSetting.EnabledAutoSync() {
		for i := range variants {
			for j := range m.lastListing.Product.Variants {
				if variants[i].ID == m.lastListing.Product.Variants[j].ID {
					variants[i].InventoryQuantity = m.lastListing.Product.Variants[j].InventoryQuantity
				}
			}
		}
	}
}

func (m *productsCenterEventModel) overWritePrice(variants []*models.ProductVariant) {
	priceSyncSetting := m.unionSetting.getPriceSyncSetting()
	// 开启同步，使用最新的价格
	if priceSyncSetting.AutoSync == consts.StateEnabled {
		for i := range variants {
			for j := range m.arg.Product.Variants {
				if variants[i].ID == m.arg.Product.Variants[j].ID {
					variants[i].Price = m.arg.Product.Variants[j].Price
					variants[i].CompareAtPrice = m.arg.Product.Variants[j].CompareAtPrice
				}
			}
		}
	}
	// 不开启同步，使用旧的价格
	if priceSyncSetting.AutoSync == consts.StateDisabled {
		for i := range variants {
			for j := range m.lastListing.Product.Variants {
				if variants[i].ID == m.lastListing.Product.Variants[j].ID {
					variants[i].Price = m.lastListing.Product.Variants[j].Price
					variants[i].CompareAtPrice = m.lastListing.Product.Variants[j].CompareAtPrice
				}
			}
		}
	}
}

// overWriteBarcode 用于覆盖 barcode，用户填写的需要保存
func (m *productsCenterEventModel) overWriteBarcode(variants []*models.ProductVariant) {
	for i := range variants {
		for j := range m.lastListing.Product.Variants {
			if variants[i].ID == m.lastListing.Product.Variants[j].ID {
				if m.lastListing.Product.Variants[j].Barcode.Type != "" &&
					m.lastListing.Product.Variants[j].Barcode.Value != "" {
					variants[i].Barcode = m.lastListing.Product.Variants[j].Barcode
				}
			}
		}
	}
}

func (m *productsCenterEventModel) buildPendingRelations() []*ProductListingRelation {
	if !m.unionSetting.autoSyncVariant() {
		// 不需要同步 variant，需要对删除的 variant 进行 unlink
		relations := m.lastListing.Relations
		newVariantIDs := sets.NewStringSet()
		for i := range m.arg.Product.Variants {
			newVariantIDs.Add(m.arg.Product.Variants[i].ID)
		}
		for i := range relations {
			if !newVariantIDs.Contains(relations[i].ProductListingVariantID) {
				relations[i].ProductsCenterVariant = ProductsCenterVariant{}
			}
		}
		return relations
	}

	// 需要同步 variant，需要找到旧的 Relations 关系
	relations := make([]*ProductListingRelation, 0, len(m.arg.Relations))
	lastRelationMap := make(map[string]*ProductListingRelation)
	for i := range m.lastListing.Relations {
		lastRelationMap[m.lastListing.Relations[i].ProductListingVariantID] = m.lastListing.Relations[i]
	}
	for i := range m.arg.Relations {
		if lastRelation, ok := lastRelationMap[m.arg.Relations[i].ProductListingVariantID]; ok {
			relation := lastRelation.DeepCopy()
			relation.ProductsCenterVariant = m.arg.Relations[i].ProductsCenterVariant
			relations = append(relations, relation)
			continue
		}
		relations = append(relations, m.arg.Relations[i])
	}

	return relations
}

func (m *productsCenterEventModel) buildUpdateArgs() *conductorUpdateArgs {
	if m.lastListing.State == consts.ProductListingProductStatePending && m.lastListing.Audit.State == "" {
		return m.buildPendingUpdateArg()
	}

	listing := m.lastListing.DeepCopy()
	listing.ProductsCenterProduct.PublishState = m.arg.ProductsCenterProduct.PublishState
	createVariantIDs, deleteVariantIDs := m.getCreateAndDeleteVariantIDSet()
	createVariantIDsList := createVariantIDs.ToList()

	if m.unionSetting.autoSyncVariant() {
		// 如果需要同步 variant，需要更新 variant 和 relation
		if len(createVariantIDsList) > 0 {
			listing.Product.Variants = m.buildVariants(createVariantIDs)
		}
		listing.Relations = m.buildRelations(createVariantIDs, deleteVariantIDs)
	}

	listing.ModifyStateAndStatus()
	result := convertToConductorUpdateArgs(listing, m.lastListing)

	return &result
}

func (m *productsCenterEventModel) buildVariants(createVariantIDs *sets.StringSet) []*models.ProductVariant {
	variants := m.lastListing.Product.Variants
	for i := range m.arg.Product.Variants {
		if createVariantIDs.Contains(m.arg.Product.Variants[i].ID) {
			variants = append(variants, m.arg.Product.Variants[i])
		}
	}
	sort.Slice(variants, func(i, j int) bool {
		return variants[i].Position < variants[j].Position
	})
	return variants
}

func (m *productsCenterEventModel) buildRelations(createVariantIDs *sets.StringSet, deleteVariantIDs *sets.StringSet) []*ProductListingRelation {
	// comment: 此处没有维护因为 e-commerce variant sku 变更场景 relation 中的 sku 信息
	relations := m.lastListing.Relations
	for i := range relations {
		if deleteVariantIDs.Contains(relations[i].ProductListingVariantID) {
			relations[i].ProductsCenterVariant = ProductsCenterVariant{}
			continue
		}
	}
	for i := range m.arg.Relations {
		if createVariantIDs.Contains(m.arg.Relations[i].ProductListingVariantID) {
			relations = append(relations, m.arg.Relations[i])
		}
	}
	return relations
}

func shouldRemoveVariantsImagesForEvent(orgID string, product models.Product, config *config.TikTokSyncConfig) bool {
	if config == nil || len(config.SkuImageRemovalConditions) == 0 {
		return false
	}

	optionNames := set.NewStringSet()
	for _, variant := range product.Variants {
		for _, vop := range variant.Options {
			if vop.Name != "" {
				optionNames.Add(vop.Name)
			}
		}
	}
	if optionNames.Card() == 0 {
		return false
	}

	return config.ShouldRemoveSkusImage(orgID, optionNames.ToList())
}
