package product_listing

import (
	"context"
	"encoding/json"
	"net/http"
	"os"
	"testing"
	"time"

	"github.com/olivere/elastic/v7"
	"github.com/spf13/viper"
	"github.com/stretchr/testify/require"

	"github.com/AfterShip/gopkg/cfg"

	"github.com/AfterShip/pltf-pd-product-listings/internal/config"
	"github.com/AfterShip/pltf-pd-product-listings/internal/consts"
	"github.com/AfterShip/pltf-pd-product-listings/internal/datastore"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
	"github.com/AfterShip/pltf-pd-product-listings/internal/utils/elasticsearch"
)

func buildESClient(t *testing.T) *elastic.Client {
	configs := new(config.Config)
	_, err := cfg.LoadViperConfig(configs, func(v *viper.Viper) { v.AddConfigPath("../../../cmd/apiserver/conf") })
	require.NoError(t, err)

	configs.DynamicConfigs.ElasticsearchAuth = &config.ElasticsearchAuthConfig{
		Host: "http://localhost:9200",
	}
	require.NoError(t, datastore.Init(configs))

	esCli := datastore.Get().ESClient
	res, num, err := esCli.Ping("http://localhost:9200").Do(context.Background())
	require.NoError(t, err)
	require.Equal(t, http.StatusOK, num)
	require.NotNil(t, res)

	return esCli
}

func Test_Create(t *testing.T) {
	cli := buildESClient(t)
	repo := &productListingESRepoImpl{cli: cli}

	err := elasticsearch.CreateTestIndexWithAlias(cli, "pd_product_listings_2024", searchIndex, "product_listings_mapping.json")
	require.NoError(t, err)

	bytes, err := os.ReadFile("./testdata/batch_insert_es.json")
	require.NoError(t, err)
	outputs := make([]*ProductListing, 0)
	err = json.Unmarshal(bytes, &outputs)
	require.NoError(t, err)
	err = repo.BatchUpsertProductListings(context.Background(), outputs)
	require.NoError(t, err)
}

func Test_SearchProductsByState(t *testing.T) {
	cli := buildESClient(t)
	repo := &productListingESRepoImpl{cli: cli}

	err := elasticsearch.CreateTestIndexWithAlias(cli, "pd_product_listings_2024", searchIndex, "product_listings_mapping.json")
	require.NoError(t, err)

	// 构建需要的数据
	bytes, err := os.ReadFile("./testdata/batch_insert_es.json")
	require.NoError(t, err)
	outputs := make([]*ProductListing, 0)
	err = json.Unmarshal(bytes, &outputs)
	require.NoError(t, err)
	err = repo.BatchUpsertProductListings(context.Background(), outputs)
	require.NoError(t, err)
	cli.Refresh().Do(context.Background())

	total := len(outputs)
	tests := []struct {
		name           string
		args           SearchProductListingArgs
		deepSearchAll  bool
		wantPagination *models.Pagination
		wantProductIds []string
		wantErr        bool
	}{
		{
			name: "page search test query state=active",
			args: SearchProductListingArgs{
				OrganizationID:       "1111",
				SalesChannelStoreKey: "gf-test",
				SalesChannelPlatform: "tiktok-shop",
				States:               []string{string(consts.ProductListingProductStateActive)},
				Limit:                1,
				Page:                 1,
			},
			wantPagination: &models.Pagination{
				Page:        1,
				Limit:       1,
				Total:       int64(total),
				HasNextPage: true,
			},
			wantErr: false,
			// 倒序
			wantProductIds: []string{"k"},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			plIDs, pagination, err := repo.SearchProductListingsIDs(context.Background(), &tt.args)
			if tt.wantErr {
				require.Error(t, err)
			} else {
				require.NoError(t, err)
				require.Equal(t, tt.wantProductIds, plIDs)
				require.Equal(t, tt.wantPagination, pagination)
			}
		})
	}
}

func Test_SearchProductsByPredefinedFilters_1(t *testing.T) {
	cli := buildESClient(t)
	repo := &productListingESRepoImpl{cli: cli}

	err := elasticsearch.CreateTestIndexWithAlias(cli, "pd_product_listings_2024", searchIndex, "product_listings_mapping.json")
	require.NoError(t, err)

	// 构建需要的数据
	bytes, err := os.ReadFile("./testdata/batch_insert_es.json")
	require.NoError(t, err)
	outputs := make([]*ProductListing, 0)
	err = json.Unmarshal(bytes, &outputs)
	require.NoError(t, err)
	err = repo.BatchUpsertProductListings(context.Background(), outputs)
	require.NoError(t, err)

	cli.Refresh().Index(searchIndex).Do(context.Background())

	tests := []struct {
		name           string
		args           SearchProductListingArgs
		deepSearchAll  bool
		wantPagination *models.Pagination
		wantProductIds []string
		wantErr        bool
	}{

		{
			name: "page search test query predefined_filters=ready_to_publish",
			args: SearchProductListingArgs{
				OrganizationID:       "1111",
				SalesChannelStoreKey: "gf-test",
				SalesChannelPlatform: "tiktok-shop",
				PredefinedFilters:    []string{PredefinedFilterReadyToPublish},
				Limit:                1,
				Page:                 1,
			},
			wantPagination: &models.Pagination{
				Page:        1,
				Limit:       1,
				Total:       1,
				HasNextPage: false,
			},
			wantErr: false,
			// 倒序查询
			wantProductIds: []string{"b"},
		},
		{
			name: "page search test query predefined_filters=info_incomplete",
			args: SearchProductListingArgs{
				OrganizationID:       "1111",
				SalesChannelStoreKey: "gf-test",
				SalesChannelPlatform: "tiktok-shop",
				PredefinedFilters:    []string{PredefinedFilterInfoIncomplete},
				Limit:                1,
				Page:                 1,
			},
			wantPagination: &models.Pagination{
				Page:        1,
				Limit:       1,
				Total:       1,
				HasNextPage: false,
			},
			wantErr: false,
			// 倒序查询
			wantProductIds: []string{"c"},
		},
		{
			name: "page search test query predefined_filters=published_failed",
			args: SearchProductListingArgs{
				OrganizationID:       "1111",
				SalesChannelStoreKey: "gf-test",
				SalesChannelPlatform: "tiktok-shop",
				PredefinedFilters:    []string{PredefinedFilterPublishedFailed},
				Limit:                1,
				Page:                 1,
			},
			wantPagination: &models.Pagination{
				Page:        1,
				Limit:       1,
				Total:       1,
				HasNextPage: false,
			},
			wantErr: false,
			// 倒序查询
			wantProductIds: []string{"d"},
		},
		{
			name: "page search test query predefined_filters=edits_under_review",
			args: SearchProductListingArgs{
				OrganizationID:       "1111",
				SalesChannelStoreKey: "gf-test",
				SalesChannelPlatform: "tiktok-shop",
				PredefinedFilters:    []string{PredefinedFilterEditsUnderReview},
				Limit:                1,
				Page:                 1,
			},
			wantPagination: &models.Pagination{
				Page:        1,
				Limit:       1,
				Total:       1,
				HasNextPage: false,
			},
			wantErr: false,
			// 倒序查询
			wantProductIds: []string{"e"},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			plIDs, pagination, err := repo.SearchProductListingsIDs(context.Background(), &tt.args)
			if tt.wantErr {
				require.Error(t, err)
			} else {
				require.NoError(t, err)
				require.Equal(t, tt.wantProductIds, plIDs)
				require.Equal(t, tt.wantPagination, pagination)
			}

		})
	}
}

func Test_SearchProductsByPredefinedFilters_2(t *testing.T) {
	cli := buildESClient(t)
	repo := &productListingESRepoImpl{cli: cli}

	err := elasticsearch.CreateTestIndexWithAlias(cli, "pd_product_listings_2024", searchIndex, "product_listings_mapping.json")
	require.NoError(t, err)

	// 构建需要的数据
	bytes, err := os.ReadFile("./testdata/batch_insert_es.json")
	require.NoError(t, err)
	outputs := make([]*ProductListing, 0)
	err = json.Unmarshal(bytes, &outputs)
	require.NoError(t, err)
	err = repo.BatchUpsertProductListings(context.Background(), outputs)
	require.NoError(t, err)
	cli.Refresh().Index(searchIndex).Do(context.Background())

	tests := []struct {
		name           string
		args           SearchProductListingArgs
		deepSearchAll  bool
		wantPagination *models.Pagination
		wantProductIds []string
		wantErr        bool
	}{
		{
			name: "page search test query predefined_filters=edits_not_approved",
			args: SearchProductListingArgs{
				OrganizationID:       "1111",
				SalesChannelStoreKey: "gf-test",
				SalesChannelPlatform: "tiktok-shop",
				PredefinedFilters:    []string{PredefinedFilterEditsNotApproved},
				Limit:                1,
				Page:                 1,
			},
			wantPagination: &models.Pagination{
				Page:        1,
				Limit:       1,
				Total:       1,
				HasNextPage: false,
			},
			wantErr: false,
			// 倒序查询
			wantProductIds: []string{"f"},
		},
		{
			name: "page search test query predefined_filters=review_failed",
			args: SearchProductListingArgs{
				OrganizationID:       "1111",
				SalesChannelStoreKey: "gf-test",
				SalesChannelPlatform: "tiktok-shop",
				PredefinedFilters:    []string{PredefinedFilterReviewFailed},
				Limit:                1,
				Page:                 1,
			},
			wantPagination: &models.Pagination{
				Page:        1,
				Limit:       1,
				Total:       1,
				HasNextPage: false,
			},
			wantErr: false,
			// 倒序查询
			wantProductIds: []string{"g"},
		},
		{
			name: "page search test query predefined_filters=frozen",
			args: SearchProductListingArgs{
				OrganizationID:       "1111",
				SalesChannelStoreKey: "gf-test",
				SalesChannelPlatform: "tiktok-shop",
				PredefinedFilters:    []string{PredefinedFilterFrozen},
				Limit:                1,
				Page:                 1,
			},
			wantPagination: &models.Pagination{
				Page:        1,
				Limit:       1,
				Total:       1,
				HasNextPage: false,
			},
			wantErr: false,
			// 倒序查询
			wantProductIds: []string{"h"},
		},
		{
			name: "page search test query multi predefined_filters",
			args: SearchProductListingArgs{
				OrganizationID:       "1111",
				SalesChannelStoreKey: "gf-test",
				SalesChannelPlatform: "tiktok-shop",
				PredefinedFilters:    []string{PredefinedFilterEditsNotApproved, PredefinedFilterReviewFailed, PredefinedFilterFrozen},
				Limit:                3,
				Page:                 1,
			},
			wantPagination: &models.Pagination{
				Page:        1,
				Limit:       3,
				Total:       3,
				HasNextPage: false,
			},
			wantErr: false,
			// 倒序查询
			wantProductIds: []string{"h", "g", "f"},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			plIDs, pagination, err := repo.SearchProductListingsIDs(context.Background(), &tt.args)
			if tt.wantErr {
				require.Error(t, err)
			} else {
				require.NoError(t, err)
				require.Equal(t, tt.wantProductIds, plIDs)
				require.Equal(t, tt.wantPagination, pagination)
			}

		})
	}
}

func Test_SearchProductsByInventoryFilters(t *testing.T) {
	cli := buildESClient(t)
	repo := &productListingESRepoImpl{cli: cli}

	err := elasticsearch.CreateTestIndexWithAlias(cli, "pd_product_listings_2024", searchIndex, "product_listings_mapping.json")
	require.NoError(t, err)

	// 构建需要的数据
	bytes, err := os.ReadFile("./testdata/batch_insert_es.json")
	require.NoError(t, err)
	outputs := make([]*ProductListing, 0)
	err = json.Unmarshal(bytes, &outputs)
	require.NoError(t, err)
	err = repo.BatchUpsertProductListings(context.Background(), outputs)
	require.NoError(t, err)
	cli.Refresh().Index(searchIndex).Do(context.Background())

	tests := []struct {
		name           string
		args           SearchProductListingArgs
		deepSearchAll  bool
		wantPagination *models.Pagination
		wantProductIds []string
		wantErr        bool
	}{
		{
			name: "page search test query inventory_filters=out_of_stock",
			args: SearchProductListingArgs{
				OrganizationID:       "1111",
				SalesChannelStoreKey: "gf-test",
				SalesChannelPlatform: "tiktok-shop",
				InventoryFilters:     InventoryFilterOutOfStock,
				Limit:                1,
				Page:                 1,
			},
			wantPagination: &models.Pagination{
				Page:        1,
				Limit:       1,
				Total:       1,
				HasNextPage: false,
			},
			wantErr: false,
			// 倒序查询
			wantProductIds: []string{"i"},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			plIDs, pagination, err := repo.SearchProductListingsIDs(context.Background(), &tt.args)
			if tt.wantErr {
				require.Error(t, err)
			} else {
				require.NoError(t, err)
				require.Equal(t, tt.wantProductIds, plIDs)
				require.Equal(t, tt.wantPagination, pagination)
			}

		})
	}
}

func Test_SearchProductsByFuzzy(t *testing.T) {
	cli := buildESClient(t)
	repo := &productListingESRepoImpl{cli: cli}

	err := elasticsearch.CreateTestIndexWithAlias(cli, "pd_product_listings_2024", searchIndex, "product_listings_mapping.json")
	require.NoError(t, err)

	// 构建需要的数据
	bytes, err := os.ReadFile("./testdata/batch_insert_es.json")
	require.NoError(t, err)
	outputs := make([]*ProductListing, 0)
	err = json.Unmarshal(bytes, &outputs)
	require.NoError(t, err)
	err = repo.BatchUpsertProductListings(context.Background(), outputs)
	require.NoError(t, err)
	_, err = cli.Refresh().Index(searchIndex).Do(context.Background())
	require.NoError(t, err)

	total := len(outputs)
	tests := []struct {
		name           string
		args           SearchProductListingArgs
		deepSearchAll  bool
		wantPagination *models.Pagination
		wantProductIds []string
		wantErr        bool
	}{
		{
			name: "page search test query multi fuzzy query = by",
			args: SearchProductListingArgs{
				OrganizationID:       "1111",
				SalesChannelStoreKey: "gf-test",
				SalesChannelPlatform: "tiktok-shop",
				Query:                "by",
				Limit:                2,
				Page:                 1,
			},
			wantPagination: &models.Pagination{
				Page:        1,
				Limit:       2,
				Total:       int64(total),
				HasNextPage: true,
			},
			wantErr: false,
			// 倒序查询
			wantProductIds: []string{"k", "j"},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			plIDs, pagination, err := repo.SearchProductListingsIDs(context.Background(), &tt.args)
			if tt.wantErr {
				require.Error(t, err)
			} else {
				require.NoError(t, err)
				require.Equal(t, tt.wantProductIds, plIDs)
				require.Equal(t, tt.wantPagination, pagination)
			}

		})
	}
}

func Test_SearchProductsByCategory(t *testing.T) {
	cli := buildESClient(t)
	repo := &productListingESRepoImpl{cli: cli}

	err := elasticsearch.CreateTestIndexWithAlias(cli, "pd_product_listings_2024", searchIndex, "product_listings_mapping.json")
	require.NoError(t, err)

	// 构建需要的数据
	bytes, err := os.ReadFile("./testdata/batch_insert_es.json")
	require.NoError(t, err)
	outputs := make([]*ProductListing, 0)
	err = json.Unmarshal(bytes, &outputs)
	require.NoError(t, err)
	err = repo.BatchUpsertProductListings(context.Background(), outputs)
	require.NoError(t, err)
	cli.Refresh().Index(searchIndex).Do(context.Background())

	tests := []struct {
		name           string
		args           SearchProductListingArgs
		deepSearchAll  bool
		wantPagination *models.Pagination
		wantProductIds []string
		wantErr        bool
	}{
		{
			name: "page search uncategoried",
			args: SearchProductListingArgs{
				OrganizationID:       "1111",
				SalesChannelStoreKey: "gf-test",
				SalesChannelPlatform: "tiktok-shop",
				Categories:           []string{UncategoriedValueInEs},
				Limit:                2,
				Page:                 1,
			},
			wantPagination: &models.Pagination{
				Page:        1,
				Limit:       2,
				Total:       1,
				HasNextPage: false,
			},
			wantErr: false,
			// 倒序查询
			wantProductIds: []string{"1"},
		},
		{
			name: "page search categories with uncategoried",
			args: SearchProductListingArgs{
				OrganizationID:       "1111",
				SalesChannelStoreKey: "gf-test",
				SalesChannelPlatform: "tiktok-shop",
				Categories:           []string{UncategoriedValueInEs, "853000"},
				Limit:                2,
				Page:                 1,
			},
			wantPagination: &models.Pagination{
				Page:        1,
				Limit:       2,
				Total:       int64(11),
				HasNextPage: true,
			},
			wantErr: false,
			// 倒序查询
			wantProductIds: []string{"k", "j"},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			plIDs, pagination, err := repo.SearchProductListingsIDs(context.Background(), &tt.args)
			if tt.wantErr {
				require.Error(t, err)
			} else {
				require.NoError(t, err)
				require.Equal(t, tt.wantProductIds, plIDs)
				require.Equal(t, tt.wantPagination, pagination)
			}
		})
	}
}

func Test_SearchProductsByProductsCenterProductIds(t *testing.T) {
	cli := buildESClient(t)
	repo := &productListingESRepoImpl{cli: cli}

	err := elasticsearch.CreateTestIndexWithAlias(cli, "pd_product_listings_2024", searchIndex, "product_listings_mapping.json")
	require.NoError(t, err)
	// 构建需要的数据
	bytes, err := os.ReadFile("./testdata/batch_insert_es.json")
	require.NoError(t, err)
	outputs := make([]*ProductListing, 0)
	err = json.Unmarshal(bytes, &outputs)
	require.NoError(t, err)
	err = repo.BatchUpsertProductListings(context.Background(), outputs)
	require.NoError(t, err)
	cli.Refresh().Index(searchIndex).Do(context.Background())

	tests := []struct {
		name           string
		args           SearchProductListingArgs
		deepSearchAll  bool
		wantPagination *models.Pagination
		wantProductIds []string
		wantErr        bool
	}{
		{
			name: "page search product_center_product_ids",
			args: SearchProductListingArgs{
				OrganizationID:           "1111",
				SalesChannelStoreKey:     "gf-test",
				SalesChannelPlatform:     "tiktok-shop",
				ProductsCenterProductIds: []string{"kk", "jj"},
				Limit:                    2,
				Page:                     1,
			},
			wantPagination: &models.Pagination{
				Page:        1,
				Limit:       2,
				Total:       2,
				HasNextPage: false,
			},
			wantErr: false,
			// 倒序查询
			wantProductIds: []string{"k", "j"},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			plIDs, pagination, err := repo.SearchProductListingsIDs(context.Background(), &tt.args)
			if tt.wantErr {
				require.Error(t, err)
			} else {
				require.NoError(t, err)
				require.Equal(t, tt.wantProductIds, plIDs)
				require.Equal(t, tt.wantPagination, pagination)
			}

		})
	}
}

func Test_SearchProductsDeepSearch(t *testing.T) {
	cli := buildESClient(t)
	repo := &productListingESRepoImpl{cli: cli}

	err := elasticsearch.CreateTestIndexWithAlias(cli, "pd_product_listings_2024", searchIndex, "product_listings_mapping.json")
	require.NoError(t, err)

	// 构建需要的数据
	bytes, err := os.ReadFile("./testdata/batch_insert_es.json")
	require.NoError(t, err)
	outputs := make([]*ProductListing, 0)
	err = json.Unmarshal(bytes, &outputs)
	require.NoError(t, err)
	err = repo.BatchUpsertProductListings(context.Background(), outputs)
	require.NoError(t, err)
	cli.Refresh().Index(searchIndex).Do(context.Background())

	total := len(outputs)
	tests := []struct {
		name           string
		args           SearchProductListingArgs
		deepSearchAll  bool
		wantPagination *models.Pagination
		wantProductIds []string
		wantErr        bool
	}{

		{
			name: "deep search all data  fuzzy query =by, field= product_name",
			args: SearchProductListingArgs{
				OrganizationID:       "1111",
				SalesChannelStoreKey: "gf-test",
				SalesChannelPlatform: "tiktok-shop",
				Query:                "by",
				Limit:                2,
				Cursor:               consts.BeginningCursor,
			},
			deepSearchAll: true,
			wantErr:       false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			allplIDs := make([]string, 0)
			for {
				plIDs, pagination, err := repo.SearchProductListingsIDs(context.Background(), &tt.args)
				if tt.wantErr {
					require.Error(t, err)
				} else if pagination.NextCursor == consts.EndCursor {
					break
				} else {
					tt.args.Cursor = pagination.NextCursor
					allplIDs = append(allplIDs, plIDs...)
				}
			}
			require.Len(t, allplIDs, total)

		})
	}
}

func Test_productListingESRepoImpl_AggregateCategoryCodes(t *testing.T) {
	// Init
	cli := buildESClient(t)
	repo := &productListingESRepoImpl{cli: cli}

	err := elasticsearch.CreateTestIndexWithAlias(cli, "pd_product_listings_2024", searchIndex, "product_listings_mapping.json")
	require.NoError(t, err)

	// 构建需要的数据
	bytes, err := os.ReadFile("./testdata/batch_insert_es.json")
	require.NoError(t, err)
	outputs := make([]*ProductListing, 0)
	err = json.Unmarshal(bytes, &outputs)
	require.NoError(t, err)
	err = repo.BatchUpsertProductListings(context.Background(), outputs)
	require.NoError(t, err)
	cli.Refresh().Index(searchIndex).Do(context.Background())

	type fields struct {
		cli *elastic.Client
	}
	type args struct {
		ctx  context.Context
		args *SearchProductListingArgs
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []string
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "test aggregate category codes",
			args: args{
				ctx: context.Background(),
				args: &SearchProductListingArgs{
					OrganizationID:       "1111",
					SalesChannelStoreKey: "gf-test",
					SalesChannelPlatform: "tiktok-shop",
					States:               []string{string(consts.ProductListingProductStateActive)},
				},
			},
			fields: fields{
				cli: cli,
			},
			want:    []string{"853000", "600002"},
			wantErr: false,
		},
		{
			name: "query product listings by ids",
			args: args{
				ctx: context.Background(),
				args: &SearchProductListingArgs{
					OrganizationID:       "1111",
					SalesChannelStoreKey: "gf-test",
					SalesChannelPlatform: "tiktok-shop",
					ProductListingIDs:    []string{"a", "b"},
					States:               []string{string(consts.ProductListingProductStateActive)},
				},
			},
			fields: fields{
				cli: cli,
			},
			want:    []string{"600002", "853000"},
			wantErr: false,
		},
		{
			name: "no hit product listing",
			args: args{
				ctx: context.Background(),
				args: &SearchProductListingArgs{
					OrganizationID:       "non-exist-org",
					SalesChannelStoreKey: "gf-test",
					SalesChannelPlatform: "tiktok-shop",
				},
			},
			fields: fields{
				cli: cli,
			},
			want:    []string{},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			r := &productListingESRepoImpl{
				cli: tt.fields.cli,
			}
			got, err := r.AggregateCategoryIDs(tt.args.ctx, tt.args.args)
			if (err != nil) != tt.wantErr {
				t.Errorf("AggregateCategoryIDs() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			require.Equal(t, tt.want, got)
		})
	}
}

func Test_productListingESRepoImpl_ESProxyProductListingCount(t *testing.T) {
	// Init
	cli := buildESClient(t)
	repo := &productListingESRepoImpl{cli: cli}

	err := elasticsearch.CreateTestIndexWithAlias(cli, "pd_product_listings_2024", searchIndex, "product_listings_mapping.json")
	require.NoError(t, err)

	// 构建需要的数据
	bytes, err := os.ReadFile("./testdata/batch_insert_es.json")
	require.NoError(t, err)
	outputs := make([]*ProductListing, 0)
	err = json.Unmarshal(bytes, &outputs)
	require.NoError(t, err)
	err = repo.BatchUpsertProductListings(context.Background(), outputs)
	require.NoError(t, err)
	cli.Refresh().Index(searchIndex).Do(context.Background())

	type fields struct {
		cli *elastic.Client
	}
	type args struct {
		ctx   context.Context
		query string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    int64
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "Normal: test es proxy product listing count",
			args: args{
				ctx: context.Background(),
				query: `{
						  "query": {
							"bool": {
							  "filter": [
								{
								  "term": {
									"deleted": false
								  }
								},
								{
								  "term": {
									"organization_id": "1111"
								  }
								},
								{
								  "term":{
									"publish_state": "successes"
								  }
								}
							  ]
							}
						  }
						}`,
			},
			fields: fields{
				cli: cli,
			},
			want:    10,
			wantErr: false,
		},
		{
			name: "Error: invalid query",
			args: args{
				ctx:   context.Background(),
				query: `invalid query`,
			},
			fields: fields{
				cli: cli,
			},
			want:    0,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			r := &productListingESRepoImpl{
				cli: tt.fields.cli,
			}
			got, err := r.ESProxyProductListingCount(tt.args.ctx, tt.args.query)
			if (err != nil) != tt.wantErr {
				t.Errorf("ESProxyProductListingCount() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("ESProxyProductListingCount() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_toOptionEsModel(t *testing.T) {
	// 构建测试数据
	pl := &ProductListing{
		Product: models.Product{
			Options: []*models.ProductOption{
				{
					Name: "Color",
					ValueDetails: []models.ProductOptionValueDetail{
						{
							Value:          "Red",
							SalesChannelID: "123",
							State:          "active",
							SyncStatus:     "synced",
						},
						{
							Value:          "Blue",
							SalesChannelID: "456",
							State:          "inactive",
							SyncStatus:     "not_synced",
						},
					},
				},
			},
		},
	}

	// 调用函数
	optionEsModels := toOptionEsModel(pl)

	// 验证结果
	require.Len(t, optionEsModels, 2)
	require.Equal(t, "Red", optionEsModels[0].Name)
	require.Equal(t, "123", optionEsModels[0].ValueDetailsSalesChannelID)
	require.Equal(t, "active", optionEsModels[0].ValueDetailsState)
	require.Equal(t, "synced", optionEsModels[0].ValueDetailsSyncStatus)

	require.Equal(t, "Blue", optionEsModels[1].Name)
	require.Equal(t, "456", optionEsModels[1].ValueDetailsSalesChannelID)
	require.Equal(t, "inactive", optionEsModels[1].ValueDetailsState)
	require.Equal(t, "not_synced", optionEsModels[1].ValueDetailsSyncStatus)
}

func Test_productListingESRepoImpl_getEsIndexWithEsModel(t *testing.T) {
	// 准备测试数据
	tests := []struct {
		name string
		pl   *productListingsEsModel
		want string
	}{
		{
			name: "2023年创建的产品",
			pl: &productListingsEsModel{
				CreatedAt: time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC),
			},
			want: createIndex + "2023",
		},
		{
			name: "2024年创建的产品",
			pl: &productListingsEsModel{
				CreatedAt: time.Date(2024, 6, 15, 10, 30, 0, 0, time.UTC),
			},
			want: createIndex + "2024",
		},
		{
			name: "2022年创建的产品",
			pl: &productListingsEsModel{
				CreatedAt: time.Date(2022, 12, 31, 23, 59, 59, 0, time.UTC),
			},
			want: createIndex + "2022",
		},
	}

	// 执行测试
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			r := &productListingESRepoImpl{}
			got := r.getEsIndexWithEsModel(tt.pl)

			// 验证结果
			if got != tt.want {
				t.Errorf("getEsIndexWithEsModel() = %v, 期望 %v", got, tt.want)
			}
		})
	}
}
