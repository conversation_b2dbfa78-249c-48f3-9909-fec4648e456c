package product_listing

import (
	"context"
	"encoding/json"
	"errors"
	"os"
	"slices"
	"testing"

	errors_1 "github.com/pkg/errors"

	"github.com/stretchr/testify/require"

	"github.com/AfterShip/gopkg/facility/set"
	"github.com/AfterShip/pltf-pd-product-listings/internal/consts"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/organization_settings"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
)

func Test_serviceImpl_SalesChannelProductEvent_Publish_By_SalesChannel(t *testing.T) {
	basePath := "./testdata/sales_channel_product_event/publish_by_sales_channel"
	s := initTestListingUpdateService(t, false, basePath)

	// test case 1 create new product listing
	ctx := context.Background()
	arg := readSalesChannelProductEventArg(t, basePath+"/test_case_1_create_listing.json")
	listing, err := s.SalesChannelProductEvent(ctx, arg)
	require.NoError(t, err)
	require.NotEmpty(t, listing.ID)
	require.Equal(t, arg.Organization.ID, listing.Organization.ID)
	require.Equal(t, arg.SalesChannel.StoreKey, listing.SalesChannel.StoreKey)
	require.Equal(t, arg.SalesChannel.Platform, listing.SalesChannel.Platform)
	require.Equal(t, consts.SyncStatusSynced, listing.SyncStatus)
	require.Equal(t, consts.LinkStatusUnlink, listing.LinkStatus)
	require.Equal(t, len(arg.Product.Variants), len(listing.Product.Variants))
	require.Equal(t, len(arg.Relations), len(listing.Relations))
	require.Equal(t, len(listing.Product.Variants), len(listing.Relations))
	for i := range listing.Relations {
		// 全部 synced and unlinked
		require.Equal(t, consts.SyncStatusSynced, listing.Relations[i].SyncStatus)
		require.Equal(t, consts.LinkStatusUnlink, listing.Relations[i].LinkStatus)
	}
	compareSalesChannelProduct(t, arg.SalesChannelProduct, listing.SalesChannelProduct)
	compareProduct(t, arg.Product, listing.Product)
	compareRelation(t, arg.Relations, listing.Relations)
	s.esRepo.RefreshESIndex(ctx)

	// test case 2 add variant
	arg = readSalesChannelProductEventArg(t, basePath+"/test_case_2_add_variant.json")
	listing, err = s.SalesChannelProductEvent(ctx, arg)
	require.NoError(t, err)
	require.Equal(t, arg.Organization.ID, listing.Organization.ID)
	require.Equal(t, arg.SalesChannel.StoreKey, listing.SalesChannel.StoreKey)
	require.Equal(t, arg.SalesChannel.Platform, listing.SalesChannel.Platform)
	require.Equal(t, consts.SyncStatusSynced, listing.SyncStatus)
	require.Equal(t, consts.LinkStatusUnlink, listing.LinkStatus)
	require.Equal(t, len(arg.Product.Variants), len(listing.Product.Variants))
	require.Equal(t, len(arg.Relations), len(listing.Relations))
	require.Equal(t, len(listing.Product.Variants), len(listing.Relations))
	for i := range listing.Relations {
		// 全部 synced and unlinked
		require.Equal(t, consts.SyncStatusSynced, listing.Relations[i].SyncStatus)
		require.Equal(t, consts.LinkStatusUnlink, listing.Relations[i].LinkStatus)
	}
	compareSalesChannelProduct(t, arg.SalesChannelProduct, listing.SalesChannelProduct)
	compareProduct(t, arg.Product, listing.Product)
	compareRelation(t, arg.Relations, listing.Relations)

	// test case 3 delete variant
	arg = readSalesChannelProductEventArg(t, basePath+"/test_case_3_delete_variant.json")
	listing, err = s.SalesChannelProductEvent(ctx, arg)
	require.NoError(t, err)
	require.Equal(t, arg.Organization.ID, listing.Organization.ID)
	require.Equal(t, arg.SalesChannel.StoreKey, listing.SalesChannel.StoreKey)
	require.Equal(t, arg.SalesChannel.Platform, listing.SalesChannel.Platform)
	require.Equal(t, consts.SyncStatusSynced, listing.SyncStatus)
	require.Equal(t, consts.LinkStatusUnlink, listing.LinkStatus)
	require.Equal(t, len(arg.Product.Variants), len(listing.Product.Variants))
	require.Equal(t, len(arg.Relations), len(listing.Relations))
	require.Equal(t, len(listing.Product.Variants), len(listing.Relations))
	for i := range listing.Relations {
		// 全部 synced and unlinked
		require.Equal(t, consts.SyncStatusSynced, listing.Relations[i].SyncStatus)
		require.Equal(t, consts.LinkStatusUnlink, listing.Relations[i].LinkStatus)
	}
	compareSalesChannelProduct(t, arg.SalesChannelProduct, listing.SalesChannelProduct)
	compareProduct(t, arg.Product, listing.Product)
	compareRelation(t, arg.Relations, listing.Relations)

	// test case 4 modify product
	arg = readSalesChannelProductEventArg(t, basePath+"/test_case_4_modify_product.json")
	listing, err = s.SalesChannelProductEvent(ctx, arg)
	require.NoError(t, err)
	require.Equal(t, arg.Organization.ID, listing.Organization.ID)
	require.Equal(t, arg.SalesChannel.StoreKey, listing.SalesChannel.StoreKey)
	require.Equal(t, arg.SalesChannel.Platform, listing.SalesChannel.Platform)
	require.Equal(t, consts.SyncStatusSynced, listing.SyncStatus)
	require.Equal(t, consts.LinkStatusUnlink, listing.LinkStatus)
	require.Equal(t, len(arg.Product.Variants), len(listing.Product.Variants))
	require.Equal(t, len(arg.Relations), len(listing.Relations))
	require.Equal(t, len(listing.Product.Variants), len(listing.Relations))
	for i := range listing.Relations {
		// 全部 synced and unlinked
		require.Equal(t, consts.SyncStatusSynced, listing.Relations[i].SyncStatus)
		require.Equal(t, consts.LinkStatusUnlink, listing.Relations[i].LinkStatus)
	}
	compareSalesChannelProduct(t, arg.SalesChannelProduct, listing.SalesChannelProduct)
	compareProduct(t, arg.Product, listing.Product)
	compareRelation(t, arg.Relations, listing.Relations)
}

func Test_serviceImpl_SalesChannelProductEvent_Publish_By_Competitor(t *testing.T) {
	basePath := "./testdata/sales_channel_product_event/publish_by_competitor"
	s := initTestListingUpdateService(t, false, basePath)
	// test case 1 create new product listing
	ctx := context.Background()
	arg := readSalesChannelProductEventArg(t, basePath+"/test_case_1_create_listing.json")
	listing, err := s.SalesChannelProductEvent(ctx, arg)
	require.NoError(t, err)
	require.NotEmpty(t, listing.ID)
	require.Equal(t, arg.Organization.ID, listing.Organization.ID)
	require.Equal(t, arg.SalesChannel.StoreKey, listing.SalesChannel.StoreKey)
	require.Equal(t, arg.SalesChannel.Platform, listing.SalesChannel.Platform)
	require.Equal(t, consts.SyncStatusSynced, listing.SyncStatus)
	require.Equal(t, consts.LinkStatusUnlink, listing.LinkStatus)
	require.Equal(t, len(arg.Product.Variants), len(listing.Product.Variants))
	require.Equal(t, len(arg.Relations), len(listing.Relations))
	require.Equal(t, len(listing.Product.Variants), len(listing.Relations))
	for i := range listing.Relations {
		// 全部 synced and unlinked
		require.Equal(t, consts.SyncStatusSynced, listing.Relations[i].SyncStatus)
		require.Equal(t, consts.LinkStatusUnlink, listing.Relations[i].LinkStatus)
	}
	compareSalesChannelProduct(t, arg.SalesChannelProduct, listing.SalesChannelProduct)
	compareProduct(t, arg.Product, listing.Product)
	compareRelation(t, arg.Relations, listing.Relations)
	s.esRepo.RefreshESIndex(ctx)

	// test case 2 add variant
	arg = readSalesChannelProductEventArg(t, basePath+"/test_case_2_add_variant.json")
	listing, err = s.SalesChannelProductEvent(ctx, arg)
	require.NoError(t, err)
	require.Equal(t, arg.Organization.ID, listing.Organization.ID)
	require.Equal(t, arg.SalesChannel.StoreKey, listing.SalesChannel.StoreKey)
	require.Equal(t, arg.SalesChannel.Platform, listing.SalesChannel.Platform)
	require.Equal(t, consts.SyncStatusSynced, listing.SyncStatus)
	require.Equal(t, consts.LinkStatusUnlink, listing.LinkStatus)
	require.Equal(t, len(arg.Product.Variants), len(listing.Product.Variants))
	require.Equal(t, len(arg.Relations), len(listing.Relations))
	require.Equal(t, len(listing.Product.Variants), len(listing.Relations))
	for i := range listing.Relations {
		// 全部 synced and unlinked
		require.Equal(t, consts.SyncStatusSynced, listing.Relations[i].SyncStatus)
		require.Equal(t, consts.LinkStatusUnlink, listing.Relations[i].LinkStatus)
	}
	compareSalesChannelProduct(t, arg.SalesChannelProduct, listing.SalesChannelProduct)
	compareProduct(t, arg.Product, listing.Product)
	compareRelation(t, arg.Relations, listing.Relations)

	// test case 3 delete variant
	arg = readSalesChannelProductEventArg(t, basePath+"/test_case_3_delete_variant.json")
	listing, err = s.SalesChannelProductEvent(ctx, arg)
	require.NoError(t, err)
	require.Equal(t, arg.Organization.ID, listing.Organization.ID)
	require.Equal(t, arg.SalesChannel.StoreKey, listing.SalesChannel.StoreKey)
	require.Equal(t, arg.SalesChannel.Platform, listing.SalesChannel.Platform)
	require.Equal(t, consts.SyncStatusSynced, listing.SyncStatus)
	require.Equal(t, consts.LinkStatusUnlink, listing.LinkStatus)
	require.Equal(t, len(arg.Product.Variants), len(listing.Product.Variants))
	require.Equal(t, len(arg.Relations), len(listing.Relations))
	require.Equal(t, len(listing.Product.Variants), len(listing.Relations))
	for i := range listing.Relations {
		// 全部 synced and unlinked
		require.Equal(t, consts.SyncStatusSynced, listing.Relations[i].SyncStatus)
		require.Equal(t, consts.LinkStatusUnlink, listing.Relations[i].LinkStatus)
	}
	compareSalesChannelProduct(t, arg.SalesChannelProduct, listing.SalesChannelProduct)
	compareProduct(t, arg.Product, listing.Product)
	compareRelation(t, arg.Relations, listing.Relations)

	// test case 4 modify product
	arg = readSalesChannelProductEventArg(t, basePath+"/test_case_4_modify_product.json")
	listing, err = s.SalesChannelProductEvent(ctx, arg)
	require.NoError(t, err)
	require.Equal(t, arg.Organization.ID, listing.Organization.ID)
	require.Equal(t, arg.SalesChannel.StoreKey, listing.SalesChannel.StoreKey)
	require.Equal(t, arg.SalesChannel.Platform, listing.SalesChannel.Platform)
	require.Equal(t, consts.SyncStatusSynced, listing.SyncStatus)
	require.Equal(t, consts.LinkStatusUnlink, listing.LinkStatus)
	require.Equal(t, len(arg.Product.Variants), len(listing.Product.Variants))
	require.Equal(t, len(arg.Relations), len(listing.Relations))
	require.Equal(t, len(listing.Product.Variants), len(listing.Relations))
	for i := range listing.Relations {
		// 全部 synced and unlinked
		require.Equal(t, consts.SyncStatusSynced, listing.Relations[i].SyncStatus)
		require.Equal(t, consts.LinkStatusUnlink, listing.Relations[i].LinkStatus)
	}
	compareSalesChannelProduct(t, arg.SalesChannelProduct, listing.SalesChannelProduct)
	compareProduct(t, arg.Product, listing.Product)
	compareRelation(t, arg.Relations, listing.Relations)
}

func Test_serviceImpl_SalesChannelProductEvent_Publish_By_Feed(t *testing.T) {
	basePath := "./testdata/sales_channel_product_event/publish_by_feed"
	s := initTestListingUpdateService(t, false, basePath)
	ctx := context.Background()
	listing := createSalesChannelProductEventTestBaseListing(t, s, basePath+"/base_listing.json")

	// test case 1 publish to channel and call back
	arg := readSalesChannelProductEventArg(t, basePath+"/test_case_1_publish_callback.json")
	arg.ID = listing.ID
	listing, err := s.SalesChannelProductEvent(ctx, arg)
	require.NoError(t, err)
	require.NotEmpty(t, listing.ID)
	require.Equal(t, arg.Organization.ID, listing.Organization.ID)
	require.Equal(t, arg.SalesChannel.StoreKey, listing.SalesChannel.StoreKey)
	require.Equal(t, arg.SalesChannel.Platform, listing.SalesChannel.Platform)
	require.Equal(t, consts.SyncStatusSynced, listing.SyncStatus)
	require.Equal(t, consts.LinkStatusLinked, listing.LinkStatus)
	require.Equal(t, len(arg.Product.Variants), len(listing.Product.Variants))
	require.Equal(t, len(arg.Relations), len(listing.Relations))
	require.Equal(t, len(listing.Product.Variants), len(listing.Relations))
	for i := range listing.Relations {
		// 全部 synced and linked
		require.Equal(t, consts.SyncStatusSynced, listing.Relations[i].SyncStatus)
		require.Equal(t, consts.LinkStatusLinked, listing.Relations[i].LinkStatus)
	}
	compareSalesChannelProduct(t, arg.SalesChannelProduct, listing.SalesChannelProduct)
	compareProduct(t, arg.Product, listing.Product)
	compareRelation(t, arg.Relations, listing.Relations)

	// test case 2 add variant
	arg = readSalesChannelProductEventArg(t, basePath+"/test_case_2_add_variant.json")
	arg.ID = listing.ID
	listing, err = s.SalesChannelProductEvent(ctx, arg)
	require.NoError(t, err)
	require.Equal(t, arg.Organization.ID, listing.Organization.ID)
	require.Equal(t, arg.SalesChannel.StoreKey, listing.SalesChannel.StoreKey)
	require.Equal(t, arg.SalesChannel.Platform, listing.SalesChannel.Platform)
	require.Equal(t, consts.SyncStatusSynced, listing.SyncStatus)
	require.Equal(t, consts.LinkStatusPartialLinked, listing.LinkStatus)
	require.Equal(t, len(arg.Product.Variants), len(listing.Product.Variants))
	require.Equal(t, len(arg.Relations), len(listing.Relations))
	require.Equal(t, len(listing.Product.Variants), len(listing.Relations))
	linkStatus := make([]consts.LinkStatus, 0, len(arg.Relations))
	for i := range listing.Relations {
		// 全部 synced
		require.Equal(t, consts.SyncStatusSynced, listing.Relations[i].SyncStatus)
		linkStatus = append(linkStatus, listing.Relations[i].LinkStatus)
	}
	require.Contains(t, linkStatus, consts.LinkStatusLinked)
	require.Contains(t, linkStatus, consts.LinkStatusUnlink)
	compareSalesChannelProduct(t, arg.SalesChannelProduct, listing.SalesChannelProduct)
	compareProduct(t, arg.Product, listing.Product)
	compareRelation(t, arg.Relations, listing.Relations)

	// test case 3 delete variant
	arg = readSalesChannelProductEventArg(t, basePath+"/test_case_3_delete_variant.json")
	arg.ID = listing.ID
	listing, err = s.SalesChannelProductEvent(ctx, arg)
	require.NoError(t, err)
	require.Equal(t, consts.SyncStatusSynced, listing.SyncStatus)
	require.Equal(t, consts.LinkStatusPartialLinked, listing.LinkStatus)
	require.Equal(t, len(arg.Product.Variants), len(listing.Product.Variants))
	require.Equal(t, len(arg.Relations), len(listing.Relations))
	require.Equal(t, len(listing.Product.Variants), len(listing.Relations))
	linkStatus = make([]consts.LinkStatus, 0, len(arg.Relations))
	for i := range listing.Relations {
		// 全部 synced
		require.Equal(t, consts.SyncStatusSynced, listing.Relations[i].SyncStatus)
		linkStatus = append(linkStatus, listing.Relations[i].LinkStatus)
	}
	require.Contains(t, linkStatus, consts.LinkStatusLinked)
	require.Contains(t, linkStatus, consts.LinkStatusUnlink)
	compareSalesChannelProduct(t, arg.SalesChannelProduct, listing.SalesChannelProduct)
	compareProduct(t, arg.Product, listing.Product)
	compareRelation(t, arg.Relations, listing.Relations)

	// test case 4 modify product
	arg = readSalesChannelProductEventArg(t, basePath+"/test_case_4_modify_product.json")
	arg.ID = listing.ID
	listing, err = s.SalesChannelProductEvent(ctx, arg)
	require.NoError(t, err)
	require.Equal(t, consts.SyncStatusSynced, listing.SyncStatus)
	require.Equal(t, consts.LinkStatusPartialLinked, listing.LinkStatus)
	require.Equal(t, len(arg.Product.Variants), len(listing.Product.Variants))
	require.Equal(t, len(arg.Relations), len(listing.Relations))
	require.Equal(t, len(listing.Product.Variants), len(listing.Relations))
	linkStatus = make([]consts.LinkStatus, 0, len(arg.Relations))
	for i := range listing.Relations {
		// 全部 synced
		require.Equal(t, consts.SyncStatusSynced, listing.Relations[i].SyncStatus)
		linkStatus = append(linkStatus, listing.Relations[i].LinkStatus)
	}
	require.Contains(t, linkStatus, consts.LinkStatusLinked)
	require.Contains(t, linkStatus, consts.LinkStatusUnlink)
	compareSalesChannelProduct(t, arg.SalesChannelProduct, listing.SalesChannelProduct)
	compareProduct(t, arg.Product, listing.Product)
	compareRelation(t, arg.Relations, listing.Relations)
}

func Test_serviceImpl_SalesChannelProductEvent_linked_synced(t *testing.T) {
	basePath := "./testdata/sales_channel_product_event/linked_synced"
	s := initTestListingUpdateService(t, false, basePath)
	ctx := context.Background()
	listing := createSalesChannelProductEventTestBaseListing(t, s, basePath+"/base_listing.json")

	// test case 1 add variant
	arg := readSalesChannelProductEventArg(t, basePath+"/add_variant_arg.json")
	arg.ID = listing.ID
	listing, err := s.SalesChannelProductEvent(ctx, arg)
	require.NoError(t, err)
	require.Equal(t, arg.Organization.ID, listing.Organization.ID)
	require.Equal(t, arg.SalesChannel.StoreKey, listing.SalesChannel.StoreKey)
	require.Equal(t, arg.SalesChannel.Platform, listing.SalesChannel.Platform)
	require.Equal(t, consts.SyncStatusSynced, listing.SyncStatus)
	require.Equal(t, consts.LinkStatusPartialLinked, listing.LinkStatus)
	require.Equal(t, len(arg.Product.Variants), len(listing.Product.Variants))
	require.Equal(t, len(arg.Relations), len(listing.Relations))
	require.Equal(t, len(listing.Product.Variants), len(listing.Relations))
	linkStatus := make([]consts.LinkStatus, 0, len(arg.Relations))
	for i := range listing.Relations {
		// 全部 synced
		require.Equal(t, consts.SyncStatusSynced, listing.Relations[i].SyncStatus)
		linkStatus = append(linkStatus, listing.Relations[i].LinkStatus)
	}
	require.Contains(t, linkStatus, consts.LinkStatusLinked)
	require.Contains(t, linkStatus, consts.LinkStatusUnlink)
	compareSalesChannelProduct(t, arg.SalesChannelProduct, listing.SalesChannelProduct)
	compareProduct(t, arg.Product, listing.Product)
	compareRelation(t, arg.Relations, listing.Relations)

	// test case 2 delete variant
	arg = readSalesChannelProductEventArg(t, basePath+"/delete_variant_arg.json")
	arg.ID = listing.ID
	listing, err = s.SalesChannelProductEvent(ctx, arg)
	require.NoError(t, err)
	require.Equal(t, consts.SyncStatusSynced, listing.SyncStatus)
	require.Equal(t, consts.LinkStatusPartialLinked, listing.LinkStatus)
	require.Equal(t, len(arg.Product.Variants), len(listing.Product.Variants))
	require.Equal(t, len(arg.Relations), len(listing.Relations))
	require.Equal(t, len(listing.Product.Variants), len(listing.Relations))
	linkStatus = make([]consts.LinkStatus, 0, len(arg.Relations))
	for i := range listing.Relations {
		// 全部 synced
		require.Equal(t, consts.SyncStatusSynced, listing.Relations[i].SyncStatus)
		linkStatus = append(linkStatus, listing.Relations[i].LinkStatus)
	}
	require.Contains(t, linkStatus, consts.LinkStatusLinked)
	require.Contains(t, linkStatus, consts.LinkStatusUnlink)
	compareSalesChannelProduct(t, arg.SalesChannelProduct, listing.SalesChannelProduct)
	compareProduct(t, arg.Product, listing.Product)
	compareRelation(t, arg.Relations, listing.Relations)

	// test case 3 modify product
	arg = readSalesChannelProductEventArg(t, basePath+"/modify_product_arg.json")
	arg.ID = listing.ID
	listing, err = s.SalesChannelProductEvent(ctx, arg)
	require.NoError(t, err)
	require.Equal(t, consts.SyncStatusSynced, listing.SyncStatus)
	require.Equal(t, consts.LinkStatusPartialLinked, listing.LinkStatus)
	require.Equal(t, len(arg.Product.Variants), len(listing.Product.Variants))
	require.Equal(t, len(arg.Relations), len(listing.Relations))
	require.Equal(t, len(listing.Product.Variants), len(listing.Relations))
	linkStatus = make([]consts.LinkStatus, 0, len(arg.Relations))
	for i := range listing.Relations {
		// 全部 synced
		require.Equal(t, consts.SyncStatusSynced, listing.Relations[i].SyncStatus)
		linkStatus = append(linkStatus, listing.Relations[i].LinkStatus)
	}
	require.Contains(t, linkStatus, consts.LinkStatusLinked)
	require.Contains(t, linkStatus, consts.LinkStatusUnlink)
	compareSalesChannelProduct(t, arg.SalesChannelProduct, listing.SalesChannelProduct)
	compareProduct(t, arg.Product, listing.Product)
	compareRelation(t, arg.Relations, listing.Relations)
}

func Test_serviceImpl_SalesChannelProductEvent_match_synced_auto_sync_field(t *testing.T) {
	basePath := "./testdata/sales_channel_product_event/match_synced/auto_sync_field"
	product := readUpdateEventProductsCenterProduct(t, basePath+"/product.json")
	s := initTestListingUpdateService(t, true, basePath)
	ctx := context.Background()
	listing := createSalesChannelProductEventTestBaseListing(t, s, basePath+"/base_listing.json")

	// test case 1 add variant
	arg := readSalesChannelProductEventArg(t, basePath+"/add_variant_arg.json")
	arg.ID = listing.ID
	listing, err := s.SalesChannelProductEvent(ctx, arg)
	require.NoError(t, err)
	require.Equal(t, arg.Organization.ID, listing.Organization.ID)
	require.Equal(t, arg.SalesChannel.StoreKey, listing.SalesChannel.StoreKey)
	require.Equal(t, arg.SalesChannel.Platform, listing.SalesChannel.Platform)
	require.Equal(t, consts.SyncStatusSynced, listing.SyncStatus)
	require.Equal(t, consts.LinkStatusPartialLinked, listing.LinkStatus)
	require.Equal(t, len(arg.Product.Variants), len(listing.Product.Variants))
	require.Equal(t, len(arg.Relations), len(listing.Relations))
	require.Equal(t, len(listing.Product.Variants), len(listing.Relations))
	linkStatus := make([]consts.LinkStatus, 0, len(arg.Relations))
	for i := range listing.Relations {
		// 全部 synced
		require.Equal(t, consts.SyncStatusSynced, listing.Relations[i].SyncStatus)
		linkStatus = append(linkStatus, listing.Relations[i].LinkStatus)
	}
	require.Contains(t, linkStatus, consts.LinkStatusLinked)
	require.Contains(t, linkStatus, consts.LinkStatusUnlink)
	compareSalesChannelProduct(t, arg.SalesChannelProduct, listing.SalesChannelProduct)
	compareProduct(t, arg.Product, listing.Product)
	compareRelation(t, arg.Relations, listing.Relations)

	// test case 2 delete variant
	arg = readSalesChannelProductEventArg(t, basePath+"/delete_variant_arg.json")
	arg.ID = listing.ID
	listing, err = s.SalesChannelProductEvent(ctx, arg)
	require.NoError(t, err)
	require.Equal(t, consts.SyncStatusSynced, listing.SyncStatus)
	require.Equal(t, consts.LinkStatusPartialLinked, listing.LinkStatus)
	require.Equal(t, len(arg.Product.Variants), len(listing.Product.Variants))
	require.Equal(t, len(arg.Relations), len(listing.Relations))
	require.Equal(t, len(listing.Product.Variants), len(listing.Relations))
	linkStatus = make([]consts.LinkStatus, 0, len(arg.Relations))
	for i := range listing.Relations {
		// 全部 synced
		require.Equal(t, consts.SyncStatusSynced, listing.Relations[i].SyncStatus)
		linkStatus = append(linkStatus, listing.Relations[i].LinkStatus)
	}
	require.Contains(t, linkStatus, consts.LinkStatusLinked)
	require.Contains(t, linkStatus, consts.LinkStatusUnlink)
	compareSalesChannelProduct(t, arg.SalesChannelProduct, listing.SalesChannelProduct)
	compareProduct(t, arg.Product, listing.Product)
	compareRelation(t, arg.Relations, listing.Relations)

	// test case 3 modify product need create audit version
	arg = readSalesChannelProductEventArg(t, basePath+"/modify_product_arg.json")
	arg.ID = listing.ID
	listing, err = s.SalesChannelProductEvent(ctx, arg)
	require.NoError(t, err)
	require.Equal(t, consts.SyncStatusSynced, listing.SyncStatus)
	require.Equal(t, consts.LinkStatusPartialLinked, listing.LinkStatus)
	require.Equal(t, len(arg.Product.Variants), len(listing.Product.Variants))
	require.Equal(t, len(arg.Relations), len(listing.Relations))
	require.Equal(t, len(listing.Product.Variants), len(listing.Relations))
	linkStatus = make([]consts.LinkStatus, 0, len(arg.Relations))
	for i := range listing.Relations {
		// 全部 synced
		require.Equal(t, consts.SyncStatusSynced, listing.Relations[i].SyncStatus)
		linkStatus = append(linkStatus, listing.Relations[i].LinkStatus)
	}
	require.Contains(t, linkStatus, consts.LinkStatusLinked)
	require.Contains(t, linkStatus, consts.LinkStatusUnlink)
	compareSalesChannelProduct(t, arg.SalesChannelProduct, listing.SalesChannelProduct)
	compareProduct(t, arg.Product, listing.Product)
	compareRelation(t, arg.Relations, listing.Relations)

	lastAuditVersion, err := s.latestAuditVersion(ctx, listing.ID)
	require.NoError(t, err)
	require.NotEmpty(t, lastAuditVersion)
	require.Equal(t, lastAuditVersion.ProductListing.ID, listing.ID)
	require.Equal(t, lastAuditVersion.ProductListing.Product.Title, product.Title)
	// require.Equal(t, lastAuditVersion.ProductListing.Product.Description, product.Description)
}

func Test_serviceImpl_SalesChannelProductEvent_match_synced_auto_sync_variant_add_variant(t *testing.T) {
	basePath := "./testdata/sales_channel_product_event/match_synced/auto_sync_variant/add_variant"
	product := readUpdateEventProductsCenterProduct(t, basePath+"/product.json")
	s := initTestListingUpdateService(t, true, basePath)
	ctx := context.Background()
	listing := createSalesChannelProductEventTestBaseListing(t, s, basePath+"/base_listing.json")

	// test case 1 add variant
	arg := readSalesChannelProductEventArg(t, basePath+"/arg.json")
	arg.ID = listing.ID
	listing, err := s.SalesChannelProductEvent(ctx, arg)
	require.NoError(t, err)
	require.Equal(t, arg.Organization.ID, listing.Organization.ID)
	require.Equal(t, arg.SalesChannel.StoreKey, listing.SalesChannel.StoreKey)
	require.Equal(t, arg.SalesChannel.Platform, listing.SalesChannel.Platform)
	require.Equal(t, consts.SyncStatusSynced, listing.SyncStatus)
	require.Equal(t, consts.LinkStatusPartialLinked, listing.LinkStatus)
	require.Equal(t, len(arg.Product.Variants), len(listing.Product.Variants))
	require.Equal(t, len(arg.Relations), len(listing.Relations))
	require.Equal(t, len(listing.Product.Variants), len(listing.Relations))
	for i := range listing.Relations {
		// 全部 synced
		require.Equal(t, consts.SyncStatusSynced, listing.Relations[i].SyncStatus)
		if i == 2 {
			require.Equal(t, consts.LinkStatusUnlink, listing.Relations[i].LinkStatus)
		} else {
			require.Equal(t, consts.LinkStatusLinked, listing.Relations[i].LinkStatus)
		}
	}
	compareSalesChannelProduct(t, arg.SalesChannelProduct, listing.SalesChannelProduct)
	compareProduct(t, arg.Product, listing.Product)
	compareRelation(t, arg.Relations, listing.Relations)
	// validate audit version data
	lastAuditVersion, err := s.latestAuditVersion(ctx, listing.ID)
	require.NoError(t, err)
	require.NotEmpty(t, lastAuditVersion)
	require.Equal(t, len(product.Variants), len(lastAuditVersion.ProductListing.Product.Variants))
	require.Equal(t, len(product.Variants), len(lastAuditVersion.ProductListing.Relations))
	require.Equal(t, len(lastAuditVersion.ProductListing.Product.Variants), len(product.Variants))
	for i := range lastAuditVersion.ProductListing.Product.Variants {
		require.Equal(t, lastAuditVersion.ProductListing.Product.Variants[i].Price.Amount, product.Variants[i].Price.Amount)
		require.Equal(t, lastAuditVersion.ProductListing.Product.Variants[i].Price.Currency, product.Variants[i].Price.Currency)
		require.Equal(t, lastAuditVersion.ProductListing.Product.Variants[i].Sku, product.Variants[i].Sku)
		require.Equal(t, lastAuditVersion.ProductListing.Product.Variants[i].Title, product.Variants[i].Title)
		require.Equal(t, lastAuditVersion.ProductListing.Product.Variants[i].InventoryQuantity, product.Variants[i].InventoryQuantity)
	}
	for i := range lastAuditVersion.ProductListing.Relations {
		require.Equal(t, lastAuditVersion.ProductListing.Relations[i].ProductsCenterVariant.ID, product.Variants[i].ID)
	}
}

func Test_serviceImpl_SalesChannelProductEvent_match_synced_auto_sync_variant_delete_variant(t *testing.T) {
	basePath := "./testdata/sales_channel_product_event/match_synced/auto_sync_variant/delete_variant"
	product := readUpdateEventProductsCenterProduct(t, basePath+"/product.json")
	s := initTestListingUpdateService(t, true, basePath)
	ctx := context.Background()
	listing := createSalesChannelProductEventTestBaseListing(t, s, basePath+"/base_listing.json")
	lastListing := listing

	// test case 1 delete variant
	arg := readSalesChannelProductEventArg(t, basePath+"/arg.json")
	arg.ID = listing.ID
	listing, err := s.SalesChannelProductEvent(ctx, arg)
	require.NoError(t, err)
	require.Equal(t, arg.Organization.ID, listing.Organization.ID)
	require.Equal(t, arg.SalesChannel.StoreKey, listing.SalesChannel.StoreKey)
	require.Equal(t, arg.SalesChannel.Platform, listing.SalesChannel.Platform)
	require.Equal(t, consts.SyncStatusPartialSynced, listing.SyncStatus)
	require.Equal(t, consts.LinkStatusLinked, listing.LinkStatus)
	require.Equal(t, len(product.Variants), len(listing.Product.Variants))
	require.Equal(t, len(product.Variants), len(listing.Relations))
	require.Equal(t, len(listing.Product.Variants), len(listing.Relations))
	for i := range listing.Relations {
		if i == 0 {
			require.Equal(t, consts.SyncStatusUnsync, listing.Relations[i].SyncStatus)
			require.NotEqual(t, listing.Relations[i].ID, lastListing.Relations[i].ID)
		} else {
			require.Equal(t, consts.SyncStatusSynced, listing.Relations[i].SyncStatus)
		}
		require.Equal(t, consts.LinkStatusLinked, listing.Relations[i].LinkStatus)
	}
	compareSalesChannelProduct(t, arg.SalesChannelProduct, listing.SalesChannelProduct)
	// validate audit version data
	lastAuditVersion, err := s.latestAuditVersion(ctx, listing.ID)
	require.NoError(t, err)
	require.NotEmpty(t, lastAuditVersion)
	require.Equal(t, len(product.Variants), len(lastAuditVersion.ProductListing.Product.Variants))
	require.Equal(t, len(product.Variants), len(lastAuditVersion.ProductListing.Relations))
	require.Equal(t, len(lastAuditVersion.ProductListing.Product.Variants), len(product.Variants))
	for i := range lastAuditVersion.ProductListing.Product.Variants {
		require.Equal(t, lastAuditVersion.ProductListing.Product.Variants[i].Price.Amount, product.Variants[i].Price.Amount)
		require.Equal(t, lastAuditVersion.ProductListing.Product.Variants[i].Price.Currency, product.Variants[i].Price.Currency)
		require.Equal(t, lastAuditVersion.ProductListing.Product.Variants[i].Sku, product.Variants[i].Sku)
		require.Equal(t, lastAuditVersion.ProductListing.Product.Variants[i].Title, product.Variants[i].Title)
		require.Equal(t, lastAuditVersion.ProductListing.Product.Variants[i].InventoryQuantity, product.Variants[i].InventoryQuantity)
	}
	for i := range lastAuditVersion.ProductListing.Relations {
		require.Equal(t, lastAuditVersion.ProductListing.Relations[i].ProductsCenterVariant.ID, product.Variants[i].ID)
	}
}

func Test_serviceImpl_SalesChannelProductEvent_match_synced_auto_sync_all(t *testing.T) {
	basePath := "./testdata/sales_channel_product_event/match_synced/auto_sync_all"
	product := readUpdateEventProductsCenterProduct(t, basePath+"/product.json")
	s := initTestListingUpdateService(t, true, basePath)
	ctx := context.Background()
	listing := createSalesChannelProductEventTestBaseListing(t, s, basePath+"/base_listing.json")

	// test case 1 add variant
	arg := readSalesChannelProductEventArg(t, basePath+"/arg.json")
	arg.ID = listing.ID
	listing, err := s.SalesChannelProductEvent(ctx, arg)
	require.NoError(t, err)
	require.Equal(t, arg.Organization.ID, listing.Organization.ID)
	require.Equal(t, arg.SalesChannel.StoreKey, listing.SalesChannel.StoreKey)
	require.Equal(t, arg.SalesChannel.Platform, listing.SalesChannel.Platform)
	require.Equal(t, consts.SyncStatusSynced, listing.SyncStatus)
	require.Equal(t, consts.LinkStatusPartialLinked, listing.LinkStatus)
	require.Equal(t, len(arg.Product.Variants), len(listing.Product.Variants))
	require.Equal(t, len(arg.Relations), len(listing.Relations))
	require.Equal(t, len(listing.Product.Variants), len(listing.Relations))
	for i := range listing.Relations {
		// 全部 synced
		require.Equal(t, consts.SyncStatusSynced, listing.Relations[i].SyncStatus)
		if i == 2 {
			require.Equal(t, consts.LinkStatusUnlink, listing.Relations[i].LinkStatus)
		} else {
			require.Equal(t, consts.LinkStatusLinked, listing.Relations[i].LinkStatus)
		}
	}
	compareSalesChannelProduct(t, arg.SalesChannelProduct, listing.SalesChannelProduct)
	compareProduct(t, arg.Product, listing.Product)
	compareRelation(t, arg.Relations, listing.Relations)
	// validate audit version data
	lastAuditVersion, err := s.latestAuditVersion(ctx, listing.ID)
	require.NoError(t, err)
	require.NotEmpty(t, lastAuditVersion)
	require.Equal(t, len(product.Variants), len(lastAuditVersion.ProductListing.Product.Variants))
	require.Equal(t, len(product.Variants), len(lastAuditVersion.ProductListing.Relations))
	require.Equal(t, len(lastAuditVersion.ProductListing.Product.Variants), len(product.Variants))
	for i := range lastAuditVersion.ProductListing.Product.Variants {
		require.Equal(t, lastAuditVersion.ProductListing.Product.Variants[i].Price.Amount, product.Variants[i].Price.Amount)
		require.Equal(t, lastAuditVersion.ProductListing.Product.Variants[i].Price.Currency, product.Variants[i].Price.Currency)
		require.Equal(t, lastAuditVersion.ProductListing.Product.Variants[i].Sku, product.Variants[i].Sku)
		require.Equal(t, lastAuditVersion.ProductListing.Product.Variants[i].Title, product.Variants[i].Title)
		require.Equal(t, lastAuditVersion.ProductListing.Product.Variants[i].InventoryQuantity, product.Variants[i].InventoryQuantity)
	}
	for i := range lastAuditVersion.ProductListing.Relations {
		require.Equal(t, lastAuditVersion.ProductListing.Relations[i].ProductsCenterVariant.ID, product.Variants[i].ID)
	}
	require.Equal(t, lastAuditVersion.ProductListing.Product.Title, product.Title)
	// require.Equal(t, lastAuditVersion.ProductListing.Product.Description, product.Description)
}

func Test_serviceImpl_SalesChannelProductEvent_VersionValidate(t *testing.T) {
	basePath := "./testdata/sales_channel_product_event/version_validate"
	s := initTestListingUpdateService(t, false, basePath)
	// test case 1 create new product listing
	ctx := context.Background()
	arg := readSalesChannelProductEventArg(t, basePath+"/base_listing.json")
	listing, err := s.SalesChannelProductEvent(ctx, arg)
	require.NoError(t, err)
	require.NotEmpty(t, listing.ID)
	require.Equal(t, arg.Organization.ID, listing.Organization.ID)
	require.Equal(t, arg.SalesChannel.StoreKey, listing.SalesChannel.StoreKey)
	require.Equal(t, arg.SalesChannel.Platform, listing.SalesChannel.Platform)
	require.Equal(t, consts.SyncStatusSynced, listing.SyncStatus)
	require.Equal(t, consts.LinkStatusUnlink, listing.LinkStatus)
	require.Equal(t, len(arg.Product.Variants), len(listing.Product.Variants))
	require.Equal(t, len(arg.Relations), len(listing.Relations))
	require.Equal(t, len(listing.Product.Variants), len(listing.Relations))
	require.Equal(t, arg.Version, listing.Version)
	for i := range listing.Relations {
		// 全部 synced and unlinked
		require.Equal(t, consts.SyncStatusSynced, listing.Relations[i].SyncStatus)
		require.Equal(t, consts.LinkStatusUnlink, listing.Relations[i].LinkStatus)
	}
	compareSalesChannelProduct(t, arg.SalesChannelProduct, listing.SalesChannelProduct)
	compareProduct(t, arg.Product, listing.Product)
	compareRelation(t, arg.Relations, listing.Relations)
	s.esRepo.RefreshESIndex(ctx)

	// test case 2 add variant
	arg = readSalesChannelProductEventArg(t, basePath+"/good_case_arg.json")
	listing, err = s.SalesChannelProductEvent(ctx, arg)
	require.NoError(t, err)
	require.Equal(t, arg.Organization.ID, listing.Organization.ID)
	require.Equal(t, arg.SalesChannel.StoreKey, listing.SalesChannel.StoreKey)
	require.Equal(t, arg.SalesChannel.Platform, listing.SalesChannel.Platform)
	require.Equal(t, consts.SyncStatusSynced, listing.SyncStatus)
	require.Equal(t, consts.LinkStatusUnlink, listing.LinkStatus)
	require.Equal(t, len(arg.Product.Variants), len(listing.Product.Variants))
	require.Equal(t, len(arg.Relations), len(listing.Relations))
	require.Equal(t, len(listing.Product.Variants), len(listing.Relations))
	require.Equal(t, arg.Version, listing.Version)
	for i := range listing.Relations {
		// 全部 synced and unlinked
		require.Equal(t, consts.SyncStatusSynced, listing.Relations[i].SyncStatus)
		require.Equal(t, consts.LinkStatusUnlink, listing.Relations[i].LinkStatus)
	}
	compareSalesChannelProduct(t, arg.SalesChannelProduct, listing.SalesChannelProduct)
	compareProduct(t, arg.Product, listing.Product)
	compareRelation(t, arg.Relations, listing.Relations)

	// test case 3 version conflict
	arg = readSalesChannelProductEventArg(t, basePath+"/bad_case_arg.json")
	listing, err = s.SalesChannelProductEvent(ctx, arg)
	require.Error(t, err)
	require.True(t, errors.Is(err, ErrVersionConflict))
}

func Test_serviceImpl_buildAutoLinkArg(t *testing.T) {

	service := initService(t)
	require.NotNil(t, service)
	tests := []struct {
		name    string
		model   salesChannelEventModel
		want    AutoLinkArg
		wantErr bool
	}{
		{
			name: "ToLive",
			model: salesChannelEventModel{
				arg: &ProductListingArgs{
					ID: "id_1",
					SalesChannelProduct: SalesChannelProduct{
						State: consts.SalesChannelProductStateLive,
					},
				},
				lastListing: &ProductListing{
					ID: "id_1",
					SalesChannelProduct: SalesChannelProduct{
						State: consts.SalesChannelProductStateFailed,
					},
				},
			},
			want: AutoLinkArg{
				ID: "id_1",
			},
			wantErr: false,
		},
		{
			name: "new_variants_and_fill_new_sku_code",
			model: salesChannelEventModel{
				arg: &ProductListingArgs{
					ID: "id_1",
					SalesChannelProduct: SalesChannelProduct{
						State: consts.SalesChannelProductStateLive,
					},
					Product: models.Product{
						Variants: []*models.ProductVariant{
							{
								ID:  "variant_id_1",
								Sku: "sku_1",
							},
							{
								ID:  "variant_id_2",
								Sku: "sku_2",
							},
							{
								ID: "variant_id_3",
							},
						},
					},
				},
				lastListing: &ProductListing{
					ID: "id_1",
					SalesChannelProduct: SalesChannelProduct{
						State: consts.SalesChannelProductStateLive,
					},
					Product: models.Product{
						Variants: []*models.ProductVariant{
							{
								ID:  "variant_id_1",
								Sku: "sku_1",
							},
							{
								ID:  "variant_id_2",
								Sku: "",
							},
						},
					},
				},
			},
			want: AutoLinkArg{
				ID:               "id_1",
				TargetVariantIDs: []string{"variant_id_2", "variant_id_3"},
			},
			wantErr: false,
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			result, err := test.model.buildAutoLinkArg()
			if test.wantErr {
				require.Error(t, err)
			} else {
				require.NoError(t, err)
				require.Equal(t, test.want.ID, result.ID)
				if set.NewStringSet(test.want.TargetVariantIDs...).Diff(set.NewStringSet(result.TargetVariantIDs...)).Card() != 0 {
					t.Errorf("want %v, got %v", test.want.TargetVariantIDs, result.TargetVariantIDs)
				}
			}
		})
	}

}

func compareRelation(t *testing.T, expect []*SalesChannelProductEventRelationArg, actual []*ProductListingRelation) {
	require.Equal(t, expect[0].SalesChannelVariant.ProductID, actual[0].SalesChannelVariant.ProductID)
	require.Equal(t, expect[0].SalesChannelVariant.ConnectorProductID, actual[0].SalesChannelVariant.ConnectorProductID)
	expectedSkus := make([]string, 0, len(expect))
	for i := range expect {
		expectedSkus = append(expectedSkus, expect[i].SalesChannelVariant.Sku)
	}
	slices.Sort(expectedSkus)
	actualSkus := make([]string, 0, len(actual))
	for i := range actual {
		actualSkus = append(actualSkus, actual[i].SalesChannelVariant.Sku)
	}
	slices.Sort(actualSkus)
	require.Equal(t, expectedSkus, actualSkus)
	expectedIDs := make([]string, 0, len(expect))
	for i := range expect {
		expectedIDs = append(expectedIDs, expect[i].SalesChannelVariant.ID)
	}
	slices.Sort(expectedIDs)
	actualIDs := make([]string, 0, len(actual))
	for i := range actual {
		actualIDs = append(actualIDs, actual[i].SalesChannelVariant.ID)
	}
	slices.Sort(actualIDs)
	require.Equal(t, expectedIDs, actualIDs)
}

func compareProduct(t *testing.T, expect, actual models.Product) {
	require.Equal(t, expect.Title, actual.Title)
	require.Equal(t, len(expect.Variants), len(actual.Variants))
	require.Equal(t, expect.Variants[0].Price, actual.Variants[0].Price)
}

func compareSalesChannelProduct(t *testing.T, expect, actual SalesChannelProduct) {
	require.Equal(t, expect.ConnectorProductID, actual.ConnectorProductID)
	require.Equal(t, expect.State, actual.State)
	require.Equal(t, expect.ID, actual.ID)
}

func readSalesChannelProductEventArg(t *testing.T, path string) *SalesChannelProductEventArg {
	bytes, err := os.ReadFile(path)
	require.NoError(t, err)
	outputs := SalesChannelProductEventArg{}
	err = json.Unmarshal(bytes, &outputs)
	require.NoError(t, err)
	return &outputs
}

func createSalesChannelProductEventTestBaseListing(t *testing.T, s *serviceImpl, path string) ProductListing {
	bytes, err := os.ReadFile(path)
	require.NoError(t, err)
	createArg := ProductListingArgs{}
	err = json.Unmarshal(bytes, &createArg)
	require.NoError(t, err)
	ctx := context.Background()
	listing, err := s.Create(ctx, &createArg)
	require.NoError(t, err)
	require.NotEmpty(t, listing.ID)
	s.esRepo.RefreshESIndex(ctx)
	return listing
}

func TestMergeFirstSyncSheinSKC(t *testing.T) {
	tests := []struct {
		name         string
		listing      *ProductListing
		model        *salesChannelEventModel
		expectedFunc func(listing *ProductListing)
	}{
		{
			name: "No main option in listing",
			listing: &ProductListing{
				Product: models.Product{
					Options: []*models.ProductOption{},
					Variants: []*models.ProductVariant{
						{
							ID:  "1",
							Sku: "sku_1",
						},
					},
				},
				Relations: []*ProductListingRelation{
					{
						ProductListingVariantID: "1",
					},
				},
			},
			model: &salesChannelEventModel{
				lastListing: &ProductListing{
					Product: models.Product{
						Options: []*models.ProductOption{},
						Variants: []*models.ProductVariant{
							{
								ID:  "1",
								Sku: "sku_1",
							},
						},
					},
					Relations: []*ProductListingRelation{
						{
							ProductListingVariantID: "1",
						},
					},
				},
			},
			expectedFunc: func(listing *ProductListing) {
				require.Equal(t, len(listing.Relations), 1)
				require.Equal(t, len(listing.Product.Variants), 1)
			},
		},
		{
			name: "No main option in last listing",
			listing: &ProductListing{
				Product: models.Product{
					Options: []*models.ProductOption{
						{
							Position: 1,
							ValueDetails: []models.ProductOptionValueDetail{
								{SalesChannelID: "1"},
							},
						},
					},
					Variants: []*models.ProductVariant{
						{
							ID:  "1",
							Sku: "sku_1",
						},
					},
				},
				Relations: []*ProductListingRelation{
					{
						ProductListingVariantID: "1",
					},
				},
			},
			model: &salesChannelEventModel{
				lastListing: &ProductListing{
					Product: models.Product{
						Options: []*models.ProductOption{},
						Variants: []*models.ProductVariant{
							{
								ID:  "1",
								Sku: "sku_1",
							},
						},
					},
					Relations: []*ProductListingRelation{
						{
							ProductListingVariantID: "1",
						},
					},
				},
			},
			expectedFunc: func(listing *ProductListing) {
				require.Equal(t, len(listing.Relations), 1)
				require.Equal(t, len(listing.Product.Variants), 1)
			},
		},
		{
			name: "Patch SKC from last listing",
			listing: &ProductListing{
				Product: models.Product{
					Options: []*models.ProductOption{
						{
							Position: 1,
							ValueDetails: []models.ProductOptionValueDetail{
								{
									SalesChannelID: "1",
								},
							},
						},
					},
					Variants: []*models.ProductVariant{
						{
							ID: "2",
							Options: []*models.ProductVariantOption{
								{
									SalesChannelOptionID: "1",
									SalesChannelValueID:  "2",
								},
							},
						},
					},
				},
				Relations: []*ProductListingRelation{
					{
						ProductListingVariantID: "2",
					},
				},
			},
			model: &salesChannelEventModel{
				lastListing: &ProductListing{
					Product: models.Product{
						Options: []*models.ProductOption{
							{
								Position:             1,
								SalesChannelOptionID: "1",
								ValueDetails: []models.ProductOptionValueDetail{
									{
										SalesChannelID:      "1",
										SalesChannelValueID: "100",
									},
								},
							},
						},
						Variants: []*models.ProductVariant{
							{
								ID: "1",
								Options: []*models.ProductVariantOption{
									{
										SalesChannelOptionID: "1",
										SalesChannelValueID:  "100",
									},
								},
							},
						},
					},
					Relations: []*ProductListingRelation{
						{
							ProductListingVariantID: "1",
						},
					},
				},
			},
			expectedFunc: func(listing *ProductListing) {
				require.Equal(t, len(listing.Relations), 2)
				require.Equal(t, len(listing.Product.Variants), 2)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.model.mergeListingSKC(tt.listing)
			tt.expectedFunc(tt.listing)
		})
	}
}

func TestIsSameSKCIDs(t *testing.T) {
	tests := []struct {
		name     string
		option1  models.ProductOption
		option2  models.ProductOption
		expected bool
	}{
		{
			name: "Both options have no ValueDetails",
			option1: models.ProductOption{
				ValueDetails: []models.ProductOptionValueDetail{},
			},
			option2: models.ProductOption{
				ValueDetails: []models.ProductOptionValueDetail{},
			},
			expected: false,
		},
		{
			name: "One option has ValueDetails, the other does not",
			option1: models.ProductOption{
				ValueDetails: []models.ProductOptionValueDetail{
					{SalesChannelID: "ID1"},
				},
			},
			option2: models.ProductOption{
				ValueDetails: []models.ProductOptionValueDetail{},
			},
			expected: false,
		},
		{
			name: "Both options have different SalesChannelIDs",
			option1: models.ProductOption{
				ValueDetails: []models.ProductOptionValueDetail{
					{SalesChannelID: "ID1"},
				},
			},
			option2: models.ProductOption{
				ValueDetails: []models.ProductOptionValueDetail{
					{SalesChannelID: "ID2"},
				},
			},
			expected: false,
		},
		{
			name: "Both options have the same SalesChannelIDs",
			option1: models.ProductOption{
				ValueDetails: []models.ProductOptionValueDetail{
					{SalesChannelID: "ID1"},
					{SalesChannelID: "ID2"},
				},
			},
			option2: models.ProductOption{
				ValueDetails: []models.ProductOptionValueDetail{
					{SalesChannelID: "ID2"},
					{SalesChannelID: "ID1"},
				},
			},
			expected: true,
		},
		{
			name: "Both options have mixed SalesChannelIDs",
			option1: models.ProductOption{
				ValueDetails: []models.ProductOptionValueDetail{
					{SalesChannelID: "ID1"},
					{SalesChannelID: ""},
					{SalesChannelID: "ID2"},
				},
			},
			option2: models.ProductOption{
				ValueDetails: []models.ProductOptionValueDetail{
					{SalesChannelID: "ID2"},
					{SalesChannelID: ""},
					{SalesChannelID: "ID1"},
				},
			},
			expected: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := isSameSKCIDs(tt.option1, tt.option2)
			require.Equal(t, tt.expected, result)
		})
	}
}

func TestExtractSKCIDs(t *testing.T) {
	tests := []struct {
		name     string
		option   models.ProductOption
		expected []string
	}{
		{
			name: "No ValueDetails",
			option: models.ProductOption{
				ValueDetails: []models.ProductOptionValueDetail{},
			},
			expected: []string{},
		},
		{
			name: "ValueDetails with empty SalesChannelID",
			option: models.ProductOption{
				ValueDetails: []models.ProductOptionValueDetail{
					{SalesChannelID: ""},
					{SalesChannelID: ""},
				},
			},
			expected: []string{},
		},
		{
			name: "ValueDetails with SalesChannelIDs",
			option: models.ProductOption{
				ValueDetails: []models.ProductOptionValueDetail{
					{SalesChannelID: "ID1"},
					{SalesChannelID: "ID2"},
				},
			},
			expected: []string{"ID1", "ID2"},
		},
		{
			name: "Mixed ValueDetails",
			option: models.ProductOption{
				ValueDetails: []models.ProductOptionValueDetail{
					{SalesChannelID: "ID1"},
					{SalesChannelID: ""},
					{SalesChannelID: "ID2"},
				},
			},
			expected: []string{"ID1", "ID2"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := extractSKCIDs(tt.option)
			require.Equal(t, tt.expected, result)
		})
	}
}

func TestRemoveInvalidSKC(t *testing.T) {
	tests := []struct {
		name     string
		listing  *ProductListing
		expected []models.ProductOptionValueDetail
	}{
		{
			name: "No ValueDetails",
			listing: &ProductListing{
				Product: models.Product{
					Options: []*models.ProductOption{
						{
							ValueDetails: []models.ProductOptionValueDetail{},
						},
					},
				},
			},
			expected: []models.ProductOptionValueDetail{},
		},
		{
			name: "ValueDetails with valid and invalid SKCs",
			listing: &ProductListing{
				Product: models.Product{
					Options: []*models.ProductOption{
						{
							ValueDetails: []models.ProductOptionValueDetail{
								{SalesChannelValueID: "ID1", Audit: models.ProductOptionValueDetailAudit{State: consts.ProductOptionValueAuditStateFailed}},
								{SalesChannelValueID: "", Audit: models.ProductOptionValueDetailAudit{State: consts.ProductOptionValueAuditStateFailed}},
								{SalesChannelValueID: "ID2", Audit: models.ProductOptionValueDetailAudit{State: consts.ProductOptionValueAuditStateSucceeded}},
							},
						},
					},
				},
			},
			expected: []models.ProductOptionValueDetail{
				{SalesChannelValueID: "ID1", Audit: models.ProductOptionValueDetailAudit{State: consts.ProductOptionValueAuditStateFailed}},
				{SalesChannelValueID: "ID2", Audit: models.ProductOptionValueDetailAudit{State: consts.ProductOptionValueAuditStateSucceeded}},
			},
		},
		{
			name: "All ValueDetails are invalid",
			listing: &ProductListing{
				Product: models.Product{
					Options: []*models.ProductOption{
						{
							ValueDetails: []models.ProductOptionValueDetail{
								{SalesChannelValueID: "", Audit: models.ProductOptionValueDetailAudit{State: consts.ProductOptionValueAuditStateFailed}},
								{SalesChannelValueID: "", Audit: models.ProductOptionValueDetailAudit{State: consts.ProductOptionValueAuditStateFailed}},
							},
						},
					},
				},
			},
			expected: []models.ProductOptionValueDetail{},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			model := &salesChannelEventModel{}
			model.removeInvalidSKC(tt.listing)
			mainOption, _ := tt.listing.Product.GetMainOption()
			require.Equal(t, tt.expected, mainOption.ValueDetails)
		})
	}
}

func Test_serviceImpl_SalesChannelProductEvent_shein_first_synced_review_failed(t *testing.T) {
	basePath := "./testdata/shein_sales_channel_product_event/shein"
	arg := readSalesChannelProductEventArg(t, basePath+"/first_synced_review_failed.json")
	s := initTestListingUpdateService(t, false, basePath)
	ctx := context.Background()

	_, err := s.SalesChannelProductEvent(ctx, arg)
	require.Error(t, err)
	require.Equal(t, err, ErrSheinProductIsInvalid)
}

func Test_serviceImpl_SalesChannelProductEvent_shein_first_synced_reviewing(t *testing.T) {
	basePath := "./testdata/shein_sales_channel_product_event/shein"
	arg := readSalesChannelProductEventArg(t, basePath+"/first_synced_reviewing.json")
	s := initTestListingUpdateService(t, false, basePath)
	ctx := context.Background()

	_, err := s.SalesChannelProductEvent(ctx, arg)
	require.Error(t, err)
	require.Equal(t, err, ErrSheinProductIsInvalid)
}

func Test_serviceImpl_SalesChannelProductEvent_shein_first_synced_review_succeeded(t *testing.T) {
	basePath := "./testdata/shein_sales_channel_product_event/shein"
	arg := readSalesChannelProductEventArg(t, basePath+"/first_synced_review_succeeded.json")
	s := initTestListingUpdateService(t, false, basePath)
	ctx := context.Background()

	listing, err := s.SalesChannelProductEvent(ctx, arg)
	require.NoError(t, err)
	require.Equal(t, listing.State, consts.ProductListingProductStateActive)
	require.Equal(t, listing.Product.Title, arg.Product.Title)
	require.Equal(t, listing.Product.Description, arg.Product.Description)
	require.Equal(t, len(listing.Product.Variants), len(arg.Product.Variants))
	require.Equal(t, len(listing.Product.Options), len(arg.Product.Options))
	require.Equal(t, len(listing.Product.Options[0].ValueDetails), len(arg.Product.Options[0].ValueDetails))
	require.Equal(t, listing.Product.Options[0].SalesChannelOptionID, arg.Product.Options[0].SalesChannelOptionID)
	require.Equal(t, listing.Product.Options[0].Name, arg.Product.Options[0].Name)
	require.Equal(t, listing.Product.Options[0].ValueDetails[0].SalesChannelID, arg.Product.Options[0].ValueDetails[0].SalesChannelID)
	require.Equal(t, listing.Product.Options[0].ValueDetails[0].SalesChannelValueID, arg.Product.Options[0].ValueDetails[0].SalesChannelValueID)
	require.Equal(t, len(listing.Product.Options[0].ValueDetails[0].Media), len(arg.Product.Options[0].ValueDetails[0].Media))
	require.Equal(t, len(listing.Relations), len(arg.Relations))
}

func Test_serviceImpl_SalesChannelProductEvent_shein_add_skc_review_failed(t *testing.T) {
	basePath := "./testdata/shein_sales_channel_product_event/shein/add_skc_review_failed"
	s := initTestListingUpdateService(t, false, basePath)
	ctx := context.Background()
	listing := createSalesChannelProductEventTestBaseListing(t, s, basePath+"/base_listing.json")

	arg := readSalesChannelProductEventArg(t, basePath+"/update_arg.json")
	newListing, err := s.SalesChannelProductEvent(ctx, arg)
	require.NoError(t, err)
	require.Equal(t, newListing.State, consts.ProductListingProductStateActive)
	require.Equal(t, len(listing.Relations), len(newListing.Relations))
	require.Equal(t, len(listing.Product.Variants), len(newListing.Product.Variants))
	require.Equal(t, len(listing.Product.Options), len(newListing.Product.Options))
	require.Equal(t, len(listing.Product.Options[0].ValueDetails), len(newListing.Product.Options[0].ValueDetails))
}

func Test_serviceImpl_SalesChannelProductEvent_shein_add_skc_reviewing(t *testing.T) {
	basePath := "./testdata/shein_sales_channel_product_event/shein/add_skc_reviewing"
	s := initTestListingUpdateService(t, false, basePath)
	ctx := context.Background()
	_ = createSalesChannelProductEventTestBaseListing(t, s, basePath+"/base_listing.json")

	arg := readSalesChannelProductEventArg(t, basePath+"/update_arg.json")
	newListing, err := s.SalesChannelProductEvent(ctx, arg)
	require.NoError(t, err)
	require.Equal(t, newListing.State, consts.ProductListingProductStatePartiallyActive)
	require.Equal(t, len(arg.Relations), len(newListing.Relations))
	require.Equal(t, len(arg.Product.Variants), len(newListing.Product.Variants))
	require.Equal(t, len(arg.Product.Options), len(newListing.Product.Options))
	require.Equal(t, len(arg.Product.Options[0].ValueDetails), len(newListing.Product.Options[0].ValueDetails))
}

func Test_serviceImpl_SalesChannelProductEvent_shein_add_skc_succeeded(t *testing.T) {
	basePath := "./testdata/shein_sales_channel_product_event/shein/add_skc_review_succeeded"
	s := initTestListingUpdateService(t, false, basePath)
	ctx := context.Background()
	_ = createSalesChannelProductEventTestBaseListing(t, s, basePath+"/base_listing.json")

	arg := readSalesChannelProductEventArg(t, basePath+"/update_arg.json")
	newListing, err := s.SalesChannelProductEvent(ctx, arg)
	require.NoError(t, err)
	require.Equal(t, newListing.State, consts.ProductListingProductStateActive)
	require.Equal(t, len(arg.Relations), len(newListing.Relations))
	require.Equal(t, len(arg.Product.Variants), len(newListing.Product.Variants))
	require.Equal(t, len(arg.Product.Options), len(newListing.Product.Options))
	require.Equal(t, len(arg.Product.Options[0].ValueDetails), len(newListing.Product.Options[0].ValueDetails))
}

func Test_serviceImpl_SalesChannelProductEvent_shein_feed_first_sync_review_failed(t *testing.T) {
	basePath := "./testdata/shein_sales_channel_product_event/feed/first_sync_review_failed"
	s := initTestListingUpdateService(t, false, basePath)
	ctx := context.Background()
	listing := createSalesChannelProductEventTestBaseListing(t, s, basePath+"/base_listing.json")

	arg := readSalesChannelProductEventArg(t, basePath+"/update_arg.json")
	newListing, err := s.SalesChannelProductEvent(ctx, arg)
	require.NoError(t, err)
	require.Equal(t, newListing.State, consts.ProductListingProductStateSuspended)
	require.Equal(t, len(listing.Relations), len(newListing.Relations))
	require.Equal(t, len(listing.Product.Variants), len(newListing.Product.Variants))
	require.Equal(t, len(listing.Product.Options), len(newListing.Product.Options))
	require.Equal(t, len(listing.Product.Options[0].ValueDetails), len(newListing.Product.Options[0].ValueDetails))
}

func Test_serviceImpl_SalesChannelProductEvent_shein_feed_first_sync_reviewing(t *testing.T) {
	basePath := "./testdata/shein_sales_channel_product_event/feed/first_sync_reviewing"
	s := initTestListingUpdateService(t, false, basePath)
	ctx := context.Background()
	_ = createSalesChannelProductEventTestBaseListing(t, s, basePath+"/base_listing.json")

	arg := readSalesChannelProductEventArg(t, basePath+"/update_arg.json")
	_, err := s.SalesChannelProductEvent(ctx, arg)
	require.Error(t, err)
	require.Equal(t, err, ErrSheinProductIsInvalid)
}

func Test_serviceImpl_SalesChannelProductEvent_shein_feed_first_sync_partial_reviewing(t *testing.T) {
	basePath := "./testdata/shein_sales_channel_product_event/feed/first_sync_partial_reviewing"
	s := initTestListingUpdateService(t, false, basePath)
	ctx := context.Background()
	listing := createSalesChannelProductEventTestBaseListing(t, s, basePath+"/base_listing.json")

	arg := readSalesChannelProductEventArg(t, basePath+"/update_arg.json")
	newListing, err := s.SalesChannelProductEvent(ctx, arg)

	require.NoError(t, err)
	require.Equal(t, newListing.State, consts.ProductListingProductStatePartiallyActive)
	require.Equal(t, len(listing.Relations), len(newListing.Relations))
	require.Equal(t, len(listing.Product.Variants), len(newListing.Product.Variants))
	require.Equal(t, len(listing.Product.Options), len(newListing.Product.Options))
	require.Equal(t, len(listing.Product.Options[0].ValueDetails), len(newListing.Product.Options[0].ValueDetails))
}

func Test_serviceImpl_SalesChannelProductEvent_shein_feed_first_sync_partial_failed(t *testing.T) {
	basePath := "./testdata/shein_sales_channel_product_event/feed/first_sync_partial_failed"
	s := initTestListingUpdateService(t, false, basePath)
	ctx := context.Background()
	listing := createSalesChannelProductEventTestBaseListing(t, s, basePath+"/base_listing.json")

	arg := readSalesChannelProductEventArg(t, basePath+"/update_arg.json")
	newListing, err := s.SalesChannelProductEvent(ctx, arg)

	require.NoError(t, err)
	require.Equal(t, newListing.State, consts.ProductListingProductStatePartiallyActive)
	require.Equal(t, len(listing.Relations), len(newListing.Relations))
	require.Equal(t, len(listing.Product.Variants), len(newListing.Product.Variants))
	require.Equal(t, len(listing.Product.Options), len(newListing.Product.Options))
	require.Equal(t, len(listing.Product.Options[0].ValueDetails), len(newListing.Product.Options[0].ValueDetails))
}

func Test_serviceImpl_SalesChannelProductEvent_shein_feed_update_skc_reviewing(t *testing.T) {
	basePath := "./testdata/shein_sales_channel_product_event/feed/update_skc_reviewing"
	s := initTestListingUpdateService(t, false, basePath)
	ctx := context.Background()
	listing := createSalesChannelProductEventTestBaseListing(t, s, basePath+"/base_listing.json")

	arg := readSalesChannelProductEventArg(t, basePath+"/update_arg.json")
	newListing, err := s.SalesChannelProductEvent(ctx, arg)

	require.NoError(t, err)
	require.Equal(t, newListing.State, consts.ProductListingProductStateActive)
	require.Equal(t, len(listing.Relations), len(newListing.Relations))
	require.Equal(t, len(listing.Product.Variants), len(newListing.Product.Variants))
	require.Equal(t, len(listing.Product.Options), len(newListing.Product.Options))
	require.Equal(t, len(listing.Product.Options[0].ValueDetails), len(newListing.Product.Options[0].ValueDetails))

	mainOption, ok := newListing.Product.GetMainOption()
	require.Equal(t, ok, true)
	for i := range mainOption.ValueDetails {
		require.Equal(t, mainOption.ValueDetails[i].State, consts.ProductOptionValueStateActive)
		if i == 1 {
			require.Equal(t, mainOption.ValueDetails[i].Audit.State, consts.ProductOptionValueAuditStateReviewing)
		} else {
			require.Equal(t, mainOption.ValueDetails[i].Audit.State, consts.ProductOptionValueAuditStateSucceeded)
		}
	}
}

func Test_serviceImpl_SalesChannelProductEvent_shein_feed_update_skc_review_failed(t *testing.T) {
	basePath := "./testdata/shein_sales_channel_product_event/feed/update_skc_review_failed"
	s := initTestListingUpdateService(t, false, basePath)
	ctx := context.Background()
	listing := createSalesChannelProductEventTestBaseListing(t, s, basePath+"/base_listing.json")

	arg := readSalesChannelProductEventArg(t, basePath+"/update_arg.json")
	newListing, err := s.SalesChannelProductEvent(ctx, arg)

	require.NoError(t, err)
	require.Equal(t, newListing.State, consts.ProductListingProductStateActive)
	require.Equal(t, len(listing.Relations), len(newListing.Relations))
	require.Equal(t, len(listing.Product.Variants), len(newListing.Product.Variants))
	require.Equal(t, len(listing.Product.Options), len(newListing.Product.Options))
	require.Equal(t, len(listing.Product.Options[0].ValueDetails), len(newListing.Product.Options[0].ValueDetails))

	mainOption, ok := newListing.Product.GetMainOption()
	require.Equal(t, ok, true)
	for i := range mainOption.ValueDetails {
		require.Equal(t, mainOption.ValueDetails[i].State, consts.ProductOptionValueStateActive)
		if i == 1 {
			require.Equal(t, mainOption.ValueDetails[i].Audit.State, consts.ProductOptionValueAuditStateFailed)
		} else {
			require.Equal(t, mainOption.ValueDetails[i].Audit.State, consts.ProductOptionValueAuditStateSucceeded)
		}
	}
}

func Test_serviceImpl_SalesChannelProductEvent_shein_feed_update_skc_review_succeeded(t *testing.T) {
	basePath := "./testdata/shein_sales_channel_product_event/feed/update_skc_review_succeeded"
	s := initTestListingUpdateService(t, false, basePath)
	ctx := context.Background()
	listing := createSalesChannelProductEventTestBaseListing(t, s, basePath+"/base_listing.json")

	arg := readSalesChannelProductEventArg(t, basePath+"/update_arg.json")
	newListing, err := s.SalesChannelProductEvent(ctx, arg)

	require.NoError(t, err)
	require.Equal(t, newListing.State, consts.ProductListingProductStateActive)
	require.Equal(t, len(listing.Relations), len(newListing.Relations))
	require.Equal(t, len(listing.Product.Variants), len(newListing.Product.Variants))
	require.Equal(t, len(listing.Product.Options), len(newListing.Product.Options))
	require.Equal(t, len(listing.Product.Options[0].ValueDetails), len(newListing.Product.Options[0].ValueDetails))

	mainOption, ok := newListing.Product.GetMainOption()
	require.Equal(t, ok, true)
	for i := range mainOption.ValueDetails {
		require.Equal(t, mainOption.ValueDetails[i].State, consts.ProductOptionValueStateActive)
		require.Equal(t, mainOption.ValueDetails[i].Audit.State, consts.ProductOptionValueAuditStateSucceeded)
	}
}

func TestSheinPlatformUpdatePreCheck(t *testing.T) {
	tests := []struct {
		name     string
		arg      SalesChannelProductEventArg
		expected error
	}{
		{
			name: "valid product with reviewing state",
			arg: SalesChannelProductEventArg{
				Product: models.Product{
					Options: []*models.ProductOption{
						{
							ValueDetails: []models.ProductOptionValueDetail{
								{Audit: models.ProductOptionValueDetailAudit{State: consts.ProductOptionValueAuditStateReviewing}},
							},
						},
					},
				},
			},
			expected: ErrSheinProductIsInvalid,
		},
		{
			name: "valid product with non-reviewing state",
			arg: SalesChannelProductEventArg{
				Product: models.Product{
					Options: []*models.ProductOption{
						{
							ValueDetails: []models.ProductOptionValueDetail{
								{Audit: models.ProductOptionValueDetailAudit{State: consts.ProductOptionValueAuditStateFailed}},
							},
						},
					},
				},
			},
			expected: nil,
		},
		{
			name: "valid product with non-reviewing state",
			arg: SalesChannelProductEventArg{
				Product: models.Product{
					Options: []*models.ProductOption{
						{
							ValueDetails: []models.ProductOptionValueDetail{
								{Audit: models.ProductOptionValueDetailAudit{State: consts.ProductOptionValueAuditStateSucceeded}},
							},
						},
					},
				},
			},
			expected: nil,
		},
		{
			name: "invalid product with no main option",
			arg: SalesChannelProductEventArg{
				Product: models.Product{},
			},
			expected: ErrSheinProductIsInvalid,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.arg.sheinPlatformUpdatePreCheck()
			require.Equal(t, tt.expected, err)
		})
	}
}

func TestMergeListingAudit(t *testing.T) {
	tests := []struct {
		name     string
		listing  *ProductListing
		expected Audit
	}{
		{
			name: "All ValueDetails are succeeded",
			listing: &ProductListing{
				Product: models.Product{
					Options: []*models.ProductOption{
						{
							ValueDetails: []models.ProductOptionValueDetail{
								{Audit: models.ProductOptionValueDetailAudit{State: consts.ProductOptionValueAuditStateSucceeded}},
								{Audit: models.ProductOptionValueDetailAudit{State: consts.ProductOptionValueAuditStateSucceeded}},
							},
						},
					},
				},
			},
			expected: Audit{State: consts.AuditStateSucceeded, FailedReasons: []AuditFailedReason{}},
		},
		{
			name: "Some ValueDetails are failed",
			listing: &ProductListing{
				Product: models.Product{
					Options: []*models.ProductOption{
						{
							ValueDetails: []models.ProductOptionValueDetail{
								{
									SalesChannelValueID: "1",
									Audit: models.ProductOptionValueDetailAudit{
										State: consts.ProductOptionValueAuditStateFailed,
										FailedReasons: []models.ProductOptionValueDetailAuditFailedReason{
											{
												Reasons: []string{"reason1"},
											},
										},
									},
								},
								{Audit: models.ProductOptionValueDetailAudit{State: consts.ProductOptionValueAuditStateSucceeded}},
							},
						},
					},
				},
			},
			expected: Audit{
				State: consts.AuditStateFailed,
				FailedReasons: []AuditFailedReason{
					{Position: "1", Reasons: []string{"reason1"}},
				},
			},
		},
		{
			name: "Some ValueDetails are reviewing",
			listing: &ProductListing{
				Product: models.Product{
					Options: []*models.ProductOption{
						{
							ValueDetails: []models.ProductOptionValueDetail{
								{Audit: models.ProductOptionValueDetailAudit{State: consts.ProductOptionValueAuditStateReviewing}},
								{Audit: models.ProductOptionValueDetailAudit{State: consts.ProductOptionValueAuditStateSucceeded}},
							},
						},
					},
				},
			},
			expected: Audit{State: consts.AuditStateReviewing, FailedReasons: []AuditFailedReason{}},
		},
		{
			name: "mix ValueDetails are reviewing",
			listing: &ProductListing{
				Product: models.Product{
					Options: []*models.ProductOption{
						{
							ValueDetails: []models.ProductOptionValueDetail{
								{Audit: models.ProductOptionValueDetailAudit{State: consts.ProductOptionValueAuditStateReviewing}},
								{Audit: models.ProductOptionValueDetailAudit{State: consts.ProductOptionValueAuditStateSucceeded}},
								{
									Audit: models.ProductOptionValueDetailAudit{
										State: consts.ProductOptionValueAuditStateFailed,
										FailedReasons: []models.ProductOptionValueDetailAuditFailedReason{
											{
												Reasons: []string{"reason1"},
											},
										},
									},
								},
							},
						},
					},
				},
			},
			expected: Audit{State: consts.AuditStateReviewing, FailedReasons: []AuditFailedReason{}},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			model := &salesChannelEventModel{}
			model.mergeListingAudit(tt.listing)
			require.Equal(t, tt.expected.State, tt.listing.Audit.State)
			require.Equal(t, len(tt.expected.FailedReasons), len(tt.listing.Audit.FailedReasons))
		})
	}
}

func Test_mergeListingProduct(t *testing.T) {
	// Initialize a `salesChannelEventModel` instance with necessary fields
	lastListing := &ProductListing{
		Product: models.Product{
			Title: "Last Listing Title",
			Options: []*models.ProductOption{
				{
					ValueDetails: []models.ProductOptionValueDetail{
						{SalesChannelID: "1"},
					},
				},
			},
		},
	}
	model := &salesChannelEventModel{
		lastListing: lastListing,
	}

	// Initialize a `ProductListing` instance with necessary fields
	listing := &ProductListing{
		Product: models.Product{
			Title: "",
			Options: []*models.ProductOption{
				{
					ValueDetails: []models.ProductOptionValueDetail{
						{SalesChannelID: "1", Audit: models.ProductOptionValueDetailAudit{State: consts.ProductOptionValueAuditStateFailed}},
					},
				},
			},
		},
	}

	// Call the `mergeListingProduct` method
	model.mergeListingProduct(listing)

	// Verify the expected behavior using assertions
	require.Equal(t, "Last Listing Title", listing.Product.Title)
	require.Equal(t, "1", listing.Product.Options[0].ValueDetails[0].SalesChannelID)
	require.Equal(t, consts.ProductOptionValueAuditStateFailed, listing.Product.Options[0].ValueDetails[0].Audit.State)
}

func Test_isAfterSalesErrorIgnorable(t *testing.T) {
	tests := []struct {
		name string
		err  error
		want bool
	}{
		{
			name: "SpecificCurrencyConvertorNotFound错误应该可忽略",
			err:  organization_settings.ErrSpecificCurrencyConvertorNotFound,
			want: true,
		},
		{
			name: "包装后的SpecificCurrencyConvertorNotFound错误应该可忽略",
			err:  errors_1.Wrap(organization_settings.ErrSpecificCurrencyConvertorNotFound, "wrapped error"),
			want: true,
		},
		{
			name: "普通错误不应该可忽略",
			err:  errors.New("some error"),
			want: false,
		},
		{
			name: "ErrNotFound错误不应该可忽略",
			err:  ErrNotFound,
			want: false,
		},
		{
			name: "ErrVersionConflict错误不应该可忽略",
			err:  ErrVersionConflict,
			want: false,
		},
		{
			name: "nil错误不应该可忽略",
			err:  nil,
			want: false,
		},
		{
			name: "其他organization_settings错误不应该可忽略",
			err:  errors.New("other organization settings error"),
			want: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := isAfterSalesErrorIgnorable(tt.err)
			require.Equal(t, tt.want, got)
		})
	}
}
