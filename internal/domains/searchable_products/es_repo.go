package searchable_products

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"

	elastic "github.com/olivere/elastic/v7"
	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/AfterShip/gopkg/log"
	"github.com/AfterShip/pltf-pd-product-listings/internal/consts"
	"github.com/AfterShip/pltf-pd-product-listings/internal/logger"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
	"github.com/AfterShip/pltf-pd-product-listings/internal/utils/elasticsearch"
)

type esRepo interface {
	getByID(ctx context.Context, id string) (*searchableProductESModel, error)
	search(ctx context.Context, args *searchArgs) ([]*searchableProductESModel, models.Pagination, error)
	searchIDs(ctx context.Context, args *searchArgs) ([]string, models.Pagination, error)
	searchSourceIDs(ctx context.Context, args *searchArgs) ([]string, models.Pagination, error)
	batchUpsert(ctx context.Context, esProducts []*searchableProductESModel) error
	refreshESIndex(ctx context.Context)
	optionNamesAggregation(ctx context.Context, args *optionNamesAggregationArgs) ([]string, error)

	// Internal use only
	eSProxyCount(ctx context.Context, index string, queryBody json.RawMessage) (int64, error)
	eSProxySearch(ctx context.Context, index string, queryBody json.RawMessage) (*elastic.SearchResult, error)
}

type esRepoImpl struct {
	logger *log.Logger
	cli    *elastic.Client
}

func newESRepo(logger *log.Logger, cli *elastic.Client) *esRepoImpl {
	return &esRepoImpl{
		logger: logger,
		cli:    cli,
	}
}

func (impl *esRepoImpl) getByID(ctx context.Context, id string) (*searchableProductESModel, error) {
	if id == "" {
		return nil, errors.WithMessage(models.ErrMissingRequiredField, "id is empty")
	}

	resultData, pagination, err := impl.search(ctx, &searchArgs{IDs: []string{id}, Page: 1, Limit: 1})
	if err != nil {
		return nil, err
	}

	if len(resultData) == 0 {
		return nil, errors.WithMessage(models.ErrResourceNotFound, "id: "+id)
	}

	// 多条数据时, 仅返回最早的一条
	if pagination.Total != 1 {
		impl.logger.WarnCtx(ctx, "multiple searchable products found by id", zap.String("id", id))
	}

	return resultData[0], nil
}

// nolint: funlen
func (impl *esRepoImpl) search(
	ctx context.Context, args *searchArgs,
) ([]*searchableProductESModel, models.Pagination, error) {
	if err := args.validate(); err != nil {
		return nil, models.Pagination{}, errors.WithMessage(models.ErrMissingRequiredField, err.Error())
	}

	query := args.buildESQueryByArgs()
	search := impl.cli.Search(IndexAliasV2).Query(query).Sort("created_at", false)
	fillSearchPageAndLimitByArgs(search, args)
	err := fillSearchCursorAndLimitByArgs(search, args)
	if err != nil {
		return nil, models.Pagination{}, errors.WithStack(err)
	}

	result, err := search.Do(ctx)
	if err != nil {
		if elastic.IsStatusCode(err, http.StatusTooManyRequests) {
			return nil, models.Pagination{}, errors.WithStack(elasticsearch.ErrTooManyRequest)
		}
		return nil, models.Pagination{}, errors.WithStack(err)
	}

	pagination := models.Pagination{
		Page:  int64(args.Page),
		Limit: int64(args.Limit),
		Total: result.TotalHits(),
	}
	if args.Cursor != "" {
		pagination.PreviousCursor = args.Cursor
		pagination.NextCursor = consts.EndCursor
		pagination.HasNextPage = len(result.Hits.Hits) == args.Limit
	} else {
		pagination.HasNextPage = int(result.TotalHits()) > args.Page*args.Limit
	}

	if len(result.Hits.Hits) == 0 {
		return nil, pagination, nil
	}

	resultData := make([]*searchableProductESModel, 0, len(result.Hits.Hits))
	for _, hits := range result.Hits.Hits {
		sp := &searchableProductESModel{}
		if err = json.Unmarshal(hits.Source, sp); err != nil {
			return nil, models.Pagination{}, errors.WithStack(err)
		}
		resultData = append(resultData, sp)
	}

	lastSort := result.Hits.Hits[len(result.Hits.Hits)-1].Sort
	nextCursor, err := elasticsearch.BuildNextCursor(lastSort)
	if err != nil {
		return nil, models.Pagination{}, errors.WithStack(err)
	}
	pagination.NextCursor = nextCursor

	return resultData, pagination, nil
}

func (impl *esRepoImpl) searchIDs(ctx context.Context, args *searchArgs) ([]string, models.Pagination, error) {
	if err := args.validate(); err != nil {
		return nil, models.Pagination{}, errors.WithMessage(models.ErrMissingRequiredField, err.Error())
	}

	query := args.buildESQueryByArgs()
	search := impl.cli.Search(IndexAliasV2).Query(query).FetchSource(false).Sort("updated_at", false)
	fillSearchPageAndLimitByArgs(search, args)
	err := fillSearchCursorAndLimitByArgs(search, args)
	if err != nil {
		return nil, models.Pagination{}, errors.WithStack(err)
	}

	result, err := search.Do(ctx)
	if err != nil {
		if elastic.IsStatusCode(err, http.StatusTooManyRequests) {
			return nil, models.Pagination{}, errors.WithStack(elasticsearch.ErrTooManyRequest)
		}
		return nil, models.Pagination{}, errors.WithStack(err)
	}

	pagination := models.Pagination{
		Page:  int64(args.Page),
		Limit: int64(args.Limit),
		Total: result.TotalHits(),
	}
	if args.Cursor != "" {
		pagination.PreviousCursor = args.Cursor
		pagination.NextCursor = consts.EndCursor
		pagination.HasNextPage = len(result.Hits.Hits) == args.Limit
	} else {
		pagination.HasNextPage = int(result.TotalHits()) > args.Page*args.Limit
	}

	if len(result.Hits.Hits) == 0 {
		return nil, pagination, nil
	}

	ids := make([]string, 0, len(result.Hits.Hits))
	for _, hit := range result.Hits.Hits {
		ids = append(ids, hit.Id)
	}

	lastSort := result.Hits.Hits[len(result.Hits.Hits)-1].Sort
	nextCursor, err := elasticsearch.BuildNextCursor(lastSort)
	if err != nil {
		return nil, models.Pagination{}, errors.WithStack(err)
	}
	pagination.NextCursor = nextCursor

	return ids, pagination, nil
}

func (impl *esRepoImpl) searchSourceIDs(ctx context.Context, args *searchArgs) ([]string, models.Pagination, error) {
	if err := args.validate(); err != nil {
		return nil, models.Pagination{}, errors.WithMessage(models.ErrMissingRequiredField, err.Error())
	}

	query := args.buildESQueryByArgs()
	search := impl.cli.Search(IndexAliasV2).Query(query).
		Sort("updated_at", false).
		FetchSourceContext(elastic.NewFetchSourceContext(true).Include("source.id"))

	fillSearchPageAndLimitByArgs(search, args)
	err := fillSearchCursorAndLimitByArgs(search, args)
	if err != nil {
		return nil, models.Pagination{}, errors.WithStack(err)
	}

	result, err := search.Do(ctx)
	if err != nil {
		if elastic.IsStatusCode(err, http.StatusTooManyRequests) {
			return nil, models.Pagination{}, errors.WithStack(elasticsearch.ErrTooManyRequest)
		}
		return nil, models.Pagination{}, errors.WithStack(err)
	}

	pagination := models.Pagination{
		Page:  int64(args.Page),
		Limit: int64(args.Limit),
		Total: result.TotalHits(),
	}
	if args.Cursor != "" {
		pagination.PreviousCursor = args.Cursor
		pagination.NextCursor = consts.EndCursor
		pagination.HasNextPage = len(result.Hits.Hits) == args.Limit
	} else {
		pagination.HasNextPage = int(result.TotalHits()) > args.Page*args.Limit
	}

	if len(result.Hits.Hits) == 0 {
		return nil, pagination, nil
	}

	type SourceIDObj struct {
		Source struct {
			ID string `json:"id"`
		} `json:"source"`
	}

	sourceIDs := make([]string, 0, len(result.Hits.Hits))
	for _, hit := range result.Hits.Hits {
		// Unmarshal the source into a map to extract source_id
		var sourceIDObj SourceIDObj
		if err := json.Unmarshal(hit.Source, &sourceIDObj); err != nil {
			return nil, models.Pagination{}, errors.WithStack(err)
		}

		sourceIDs = append(sourceIDs, sourceIDObj.Source.ID)
	}

	lastSort := result.Hits.Hits[len(result.Hits.Hits)-1].Sort
	nextCursor, err := elasticsearch.BuildNextCursor(lastSort)
	if err != nil {
		return nil, models.Pagination{}, errors.WithStack(err)
	}
	pagination.NextCursor = nextCursor

	return sourceIDs, pagination, nil
}

func (impl *esRepoImpl) batchUpsert(ctx context.Context, esProducts []*searchableProductESModel) error {
	bulk := impl.cli.Bulk()

	for _, product := range esProducts {
		index := product.esIndex()
		req := elastic.NewBulkIndexRequest().
			Index(index).
			Id(product.ID).
			Version(product.Version). // document version control
			VersionType("external").
			Doc(product)
		bulk.Add(req)
	}

	resp, err := bulk.Do(ctx)
	if err != nil {
		if elastic.IsStatusCode(err, http.StatusTooManyRequests) {
			return errors.WithStack(elasticsearch.ErrTooManyRequest)
		}
		return errors.WithStack(err)
	}

	for _, failedItem := range resp.Failed() {
		if failedItem.Status == 409 { // same version, 丢掉了
			continue
		}
		cause := fmt.Sprintf("unknown, status: %d", failedItem.Status)
		if failedItem.Error != nil {
			cause = failedItem.Error.Reason
		}
		return errors.WithMessage(elasticsearch.ErrBatchUpsertES, cause)
	}

	return nil
}

func (impl *esRepoImpl) eSProxyCount(ctx context.Context, index string, queryBody json.RawMessage) (int64, error) {
	if index != IndexAlias && index != IndexAliasV2 {
		return 0, errors.WithStack(errors.New("index is invalid"))
	}
	return impl.cli.Count(index).BodyJson(queryBody).Do(ctx)
}

func (impl *esRepoImpl) eSProxySearch(ctx context.Context, index string, queryBody json.RawMessage) (*elastic.SearchResult, error) {
	if index != IndexAlias && index != IndexAliasV2 {
		return nil, errors.WithStack(errors.New("index is invalid"))
	}
	return impl.cli.Search(index).Source(queryBody).Do(ctx)
}

func (impl *esRepoImpl) refreshESIndex(ctx context.Context) {
	_, err := impl.cli.Refresh().Index(IndexAliasV2).Do(ctx)
	if err != nil {
		logger.Get().ErrorCtx(ctx, "Elasticsearch index refresh failed", zap.Error(err))
	}
}

func (impl *esRepoImpl) optionNamesAggregation(ctx context.Context, args *optionNamesAggregationArgs) ([]string, error) {

	query := elastic.NewBoolQuery()

	elasticsearch.BuildTermQuery(query, "organization.id", args.OrganizationID)
	elasticsearch.BuildTermQuery(query, "source.app.key", args.SourceStoreKey)
	elasticsearch.BuildTermQuery(query, "source.app.platform", args.SourcePlatform)
	elasticsearch.BuildTermsQuery(query, "id", args.IDs)
	elasticsearch.BuildTermsQuery(query, "source.id", args.SourceIDs)

	outAggName := "out_agg_options_name"
	inAggName := "in_agg_options_name_nested"
	agg := elastic.NewNestedAggregation().
		Path("options").SubAggregation(inAggName, elastic.NewTermsAggregation().
		Field("options.name").Size(10))

	esResult, err := impl.cli.Search(IndexAliasV2).Query(query).Size(0).Aggregation(outAggName, agg).Do(ctx)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	// 处理聚合结果
	aggOutResult, found := esResult.Aggregations.Nested(outAggName)
	if !found {
		return nil, errors.WithStack(errors.New("could not find out nested aggregation"))
	}

	inOutResult, found := aggOutResult.Terms(inAggName)
	if !found {
		return nil, errors.WithStack(errors.New("could not find in terms aggregation"))
	}

	result := make([]string, 0)
	for _, bucket := range inOutResult.Buckets {
		value := bucket.Key.(string)
		if value == "" {
			continue
		}
		result = append(result, value)
	}
	return result, nil
}
