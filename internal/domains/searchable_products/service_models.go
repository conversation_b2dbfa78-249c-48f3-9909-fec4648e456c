package searchable_products

import (
	"encoding/json"
	"time"

	"github.com/pkg/errors"

	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
)

type SearchArgs struct {
	OrganizationID             string
	SourceStoreKey             string
	SourcePlatform             string
	SourceIDs                  []string
	SourceTypes                []string
	Vendor                     string
	ProductTitle               string
	FulfillmentServices        []string
	Status                     []string
	VariantStatus              []string
	IDs                        []string
	ProductsCenterVariantIDs   []string
	Categories                 []string
	Tags                       []string
	ProductTypes               []string
	SourceSalesChannelNames    []string
	ConnectorsProductIDs       []string
	SKUs                       []string
	IncludedDeletedProduct     bool
	IncludedSalesChannelStores []models.SalesChannel
	ExcludedSalesChannelStores []models.SalesChannel
	SourceInventoryItemID      string
	CombinedListingID          string
	CombinedStatus             string
	Query                      string
	Cursor                     string
	Limit                      int
	Page                       int
}

func (args *SearchArgs) validate() error {
	if (args.Page == 0 && args.Cursor == "") || args.Page < 0 || args.Limit <= 0 {
		return errors.WithMessagef(models.ErrMissingRequiredField, "input invalidate page、cursor or limit")
	}

	return nil
}

func (args *SearchArgs) toESSearchableProduct() *searchArgs {
	esSearchArgs := &searchArgs{
		OrganizationID:             args.OrganizationID,
		SourceStoreKey:             args.SourceStoreKey,
		SourcePlatform:             args.SourcePlatform,
		SourceTypes:                args.SourceTypes,
		SourceIDs:                  args.SourceIDs,
		FulfillmentServices:        args.FulfillmentServices,
		Vendor:                     args.Vendor,
		ProductTitle:               args.ProductTitle,
		VariantStatus:              args.VariantStatus,
		Status:                     args.Status,
		IDs:                        args.IDs,
		ProductsCenterVariantIDs:   args.ProductsCenterVariantIDs,
		CategorySourceIDs:          args.Categories,
		Tags:                       args.Tags,
		ProductTypes:               args.ProductTypes,
		SourceSalesChannelNames:    args.SourceSalesChannelNames,
		SKUs:                       args.SKUs,
		ConnectorsProductIDs:       args.ConnectorsProductIDs,
		IncludedDeletedProduct:     args.IncludedDeletedProduct,
		IncludedSalesChannelStores: args.IncludedSalesChannelStores,
		ExcludedSalesChannelStores: args.ExcludedSalesChannelStores,
		SourceInventoryItemID:      args.SourceInventoryItemID,
		CombinedListingID:          args.CombinedListingID,
		CombinedStatus:             args.CombinedStatus,
		Query:                      args.Query,
		Cursor:                     args.Cursor,
		Limit:                      args.Limit,
		Page:                       args.Page,
	}

	return esSearchArgs
}

type UpsertSearchableProductRequest struct {
	ID                  string                `json:"id" validate:"required"`
	ProductsCenterID    string                `json:"products_center_id" validate:"required"`
	ConnectorsProductID string                `json:"connectors_product_id"`
	Title               string                `json:"title"`
	Status              string                `json:"status"`
	Vendor              string                `json:"vendor"`
	Media               []Media               `json:"media"`
	Source              models.Source         `json:"source"`
	Organization        models.Organization   `json:"organization"`
	SalesChannels       []models.SalesChannel `json:"sales_channels"`
	SourceSalesChannels []SourceSalesChannel  `json:"source_sales_channels"`
	Categories          []Category            `json:"categories"`
	Tags                []string              `json:"tags"`
	ProductTypes        []string              `json:"product_types"`
	Attributes          []Attribute           `json:"attributes"`
	Variants            []Variant             `json:"variants" validate:"required,dive"`
	Combined            Combined              `json:"combined"`
	Version             int64                 `json:"version" validate:"required"`
	Options             []Option              `json:"options"`
	IsDeleted           bool                  `json:"is_deleted"`
}

func (req *UpsertSearchableProductRequest) toESSearchableProduct() *searchableProductESModel {
	esModel := &searchableProductESModel{
		ID:                  req.ID,
		ProductsCenterID:    req.ProductsCenterID,
		ConnectorsProductID: req.ConnectorsProductID,
		Title:               req.Title,
		Status:              req.Status,
		Vendor:              req.Vendor,
		Organization:        req.Organization,
		Source:              req.Source,
		Categories:          req.Categories,
		Tags:                req.Tags,
		ProductTypes:        req.ProductTypes,
		SalesChannels:       req.SalesChannels,
		SourceSalesChannels: req.SourceSalesChannels,
		Attributes:          req.Attributes,
		Variants:            req.Variants,
		Options:             req.Options,
		Combined:            req.Combined,
		Version:             req.Version,
		Deleted:             req.IsDeleted,
	}

	if len(req.Media) > 0 {
		mediaJsonBody, _ := json.Marshal(&req.Media) // nolint: errchkjson
		esModel.Media = string(mediaJsonBody)
	}

	return esModel
}

type SearchableProduct struct {
	ID                  string                `json:"id"`
	ProductsCenterID    string                `json:"products_center_id"`
	ConnectorsProductID string                `json:"connectors_product_id"`
	Title               string                `json:"title"`
	Status              string                `json:"status"`
	Vendor              string                `json:"vendor"`
	Media               []Media               `json:"media"`
	Source              models.Source         `json:"source"`
	Organization        models.Organization   `json:"organization"`
	SalesChannels       []models.SalesChannel `json:"sales_channels"`
	SourceSalesChannels []SourceSalesChannel  `json:"source_sales_channels"`
	Categories          []Category            `json:"categories"`
	Tags                []string              `json:"tags"`
	ProductTypes        []string              `json:"product_types"`
	Attributes          []Attribute           `json:"attributes"`
	Variants            []Variant             `json:"variants"`
	Options             []Option              `json:"options"`
	Combined            Combined              `json:"combined"`
	Version             int64                 `json:"version" validate:"required"`
	CreatedAt           time.Time             `json:"created_at"`
	UpdatedAt           time.Time             `json:"updated_at"`
	Deleted             bool                  `json:"deleted"`
}

type Media struct {
	Type      string `json:"type"`
	Position  int    `json:"position"`
	Thumbnail struct {
		Url string `json:"url"`
	} `json:"thumbnail"`
	Url             string `json:"url"`
	MimeType        string `json:"mime_type"`
	SourceVideoHost string `json:"source_video_host"`
}

func toSearchableProductMediaModel(esMedia string) []Media {
	if esMedia == "" {
		return nil
	}

	var serviceMedia []Media
	_ = json.Unmarshal([]byte(esMedia), &serviceMedia) // nolint: errchkjson

	return serviceMedia
}

type Combined struct {
	ListingID  string   `json:"listing_id"`
	ErrorCodes []string `json:"error_codes"`
	Status     string   `json:"status"`
}

type Category struct {
	SourceID string `json:"source_id"`
	Name     string `json:"name"`
	Type     string `json:"type"`
	ID       string `json:"id"`
}

type Attribute struct {
	SourceID string           `json:"source_id"`
	Name     string           `json:"name"`
	Values   []AttributeValue `json:"values"`
}

type AttributeValue struct {
	SourceID string `json:"source_id"`
	Type     string `json:"type"`
	Value    string `json:"value"`
}

type Variant struct {
	ID                      string  `json:"id" validate:"required"`
	ProductsCenterVariantID string  `json:"products_center_variant_id" validate:"required"`
	ConnectorsProductID     string  `json:"connectors_product_id"`
	SourceProductID         string  `json:"source_product_id"`
	SourceVariantID         string  `json:"source_variant_id"`
	SourceInventoryItemId   string  `json:"source_inventory_item_id"`
	Sku                     string  `json:"sku"`
	Title                   string  `json:"title"`
	ImageURL                string  `json:"image_url"`
	FulfillmentService      string  `json:"fulfillment_service"`
	InventoryQuantity       float64 `json:"inventory_quantity"`
	Price                   Price   `json:"price"`
	Status                  string  `json:"status"`
}

type Option struct {
	Name   string   `json:"name"`
	Values []string `json:"values"`
}

type Price struct {
	Currency string `json:"currency"`
	Amount   string `json:"amount"`
}

type OptionsAggregationArgs struct {
	SourceIDs        []string
	PredefinedFilter string
	OrganizationID   string
	SourceStoreKey   string
	SourcePlatform   string
}

type OptionsAggregationResult struct {
	OptionNames  []string
	ProductCount int
}

func (args *OptionsAggregationArgs) validator() error {
	if len(args.SourceIDs) == 0 {
		return errors.New("either IDs or SourceIDs must be provided")
	}

	if len(args.SourceIDs) > 1000 {
		return errors.New("IDs and SourceIDs cannot exceed 1000 items each")
	}
	return nil
}

type SourceSalesChannel struct {
	ID   string `json:"id"`
	Name string `json:"name"`
}
