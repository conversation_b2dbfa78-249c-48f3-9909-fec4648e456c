package searchable_products

import (
	"context"
	"encoding/json"
	"errors"

	"github.com/go-playground/validator/v10"
	"go.uber.org/zap"

	"github.com/AfterShip/gopkg/log"
	"github.com/AfterShip/pltf-pd-product-listings/internal/utils/slicex"

	"github.com/AfterShip/pltf-pd-product-listings/internal/consts"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
)

type PushToChannelTaskInput struct {
	OrganizationID string                                 `json:"organization_id" validate:"required"`
	SourceStoreKey string                                 `json:"source_store_key" validate:"required"`
	SourcePlatform string                                 `json:"source_platform" validate:"required"`
	IDs            []string                               `json:"ids" validate:"required"`
	SalesChannels  []*PushToChannelTaskInputSalesChannels `json:"sales_channels" validate:"required,dive"`
	GroupName      consts.TaskGroupName                   `json:"group_name"`
}

type PushToChannelTaskInputSalesChannels struct {
	StoreKey                string                                 `json:"store_key" validate:"required"`
	Platform                string                                 `json:"platform" validate:"required"`
	CategorySourceID        string                                 `json:"category_source_id"`
	RecommendCategory       bool                                   `json:"recommend_category"`
	Certifications          []models.ProductCertification          `json:"certifications"`
	Attributes              []models.ProductAttribute              `json:"attributes"`
	SizeChart               models.ProductSizeChart                `json:"size_chart"`
	Length                  TaskInputProductVariantShippingSetting `json:"length"`
	Width                   TaskInputProductVariantShippingSetting `json:"width"`
	Height                  TaskInputProductVariantShippingSetting `json:"height"`
	Weight                  TaskInputProductVariantShippingSetting `json:"weight"`
	Brand                   models.SalesChannelResource            `json:"brand"`
	Compliance              models.ProductCompliance               `json:"compliance"`
	AutoPublish             bool                                   `json:"auto_publish"`
	FeedCustomizationParams FeedCustomizationParams                `json:"feed_customization_params"`
}

type FeedCustomizationParams struct {
	FeedCategoryTemplateID string `json:"feed_category_template_id"`
}

type TaskInputProductVariantShippingSetting struct {
	models.ProductVariantShippingSetting
	Fixed bool `json:"fixed"`
}

type PushToChannelTaskOutput struct {
	ProductTotalCount int                                `json:"product_total_count"`
	TaskFinishedCount int                                `json:"task_finished_count"`
	TaskTotalCount    int                                `json:"task_total_count"`
	Results           []*PushToChannelTaskOutputResponse `json:"results"`
}

type PushToChannelTaskOutputResult struct {
	StoreKey             string `json:"store_key"`
	Platform             string `json:"platform"`
	TotalCount           int    `json:"total_count"`
	SucceededCount       int    `json:"succeeded_count"`
	FailedCount          int    `json:"failed_count"` // included recommend_failed_count
	ExistCount           int    `json:"exist_count"`
	RecommendFailedCount int    `json:"recommend_failed_count"`
}

type PushToChannelTaskOutputResponse struct {
	SalesChannel         models.SalesChannel `json:"sales_channel"`
	TotalCount           int                 `json:"total_count"`
	SucceededCount       int                 `json:"succeeded_count"`
	FailedCount          int                 `json:"failed_count"` // included recommend_failed_count
	ExistCount           int                 `json:"exist_count"`
	RecommendFailedCount int                 `json:"recommend_failed_count"`
}

type PushToChannelTask struct {
	Logger    *log.Logger
	Validator *validator.Validate
}

func (t *PushToChannelTask) validate(input *PushToChannelTaskInput) error {
	if err := t.Validator.Struct(input); err != nil {
		return err
	}
	return nil
}

func (t *PushToChannelTask) BuildTaskArgs(ctx context.Context, input models.TaskInput) (models.TaskArgs, error) {
	args, ok := input.(*PushToChannelTaskInput)
	if !ok {
		t.Logger.WarnCtx(ctx, "Failed to parse push to channel task input", zap.Any("input", input))
		return models.TaskArgs{}, errors.New("invalid input type")
	}

	if err := t.validate(args); err != nil {
		return models.TaskArgs{}, err
	}

	if len(args.IDs) == 0 {
		return models.TaskArgs{}, errors.New("product center IDs is empty")
	}

	inputs := make([]models.TaskInput, 0)

	splitIDs := slicex.SplitSlice(args.IDs, 50)
	for i := range splitIDs {
		inputs = append(inputs, &PushToChannelTaskInput{
			OrganizationID: args.OrganizationID,
			SourceStoreKey: args.SourceStoreKey,
			SourcePlatform: args.SourcePlatform,
			IDs:            splitIDs[i],
			SalesChannels:  args.SalesChannels,
		})
	}

	taskArgs := models.TaskArgs{
		GroupName:      args.GroupName,
		Type:           consts.BatchTaskType,
		ConcurrencyKey: args.OrganizationID,
		ResourceID:     args.OrganizationID,
		Inputs:         inputs,
	}

	return taskArgs, nil
}

func (t *PushToChannelTask) ParseOutput(ctx context.Context, task *models.Task) models.TaskOutput {
	output := PushToChannelTaskOutput{}
	resultMap := map[string]*PushToChannelTaskOutputResponse{}
	for i := range task.ChildTasks {
		// get total count
		input := &PushToChannelTaskInput{}
		if task.ChildTasks[i].Inputs == "" {
			continue
		}
		if err := json.Unmarshal([]byte(task.ChildTasks[i].Inputs), input); err != nil {
			t.Logger.With(zap.String("Id", task.ChildTasks[i].ID)).WarnCtx(ctx, "Failed to parse push to channel task input", zap.Error(err))
			continue
		}
		output.ProductTotalCount += len(input.IDs)
		output.TaskTotalCount += len(input.SalesChannels)

		if task.ChildTasks[i].Outputs.Data == "" {
			continue
		}

		// get child task output
		childTaskOutput := make([]*PushToChannelTaskOutputResult, 0)
		if err := json.Unmarshal([]byte(task.ChildTasks[i].Outputs.Data), &childTaskOutput); err != nil {
			t.Logger.With(zap.String("Id", task.ChildTasks[i].ID)).WarnCtx(ctx, "Failed to parse push to channel task output", zap.Error(err))
			continue
		}
		// calculate finished count
		output.TaskFinishedCount += len(childTaskOutput)

		// merge the same sales channel result
		for i := range childTaskOutput {
			key := childTaskOutput[i].StoreKey + childTaskOutput[i].Platform
			if _, ok := resultMap[key]; !ok {
				resultMap[key] = &PushToChannelTaskOutputResponse{
					SalesChannel: models.SalesChannel{
						StoreKey: childTaskOutput[i].StoreKey,
						Platform: childTaskOutput[i].Platform,
					},
					TotalCount:           childTaskOutput[i].TotalCount,
					SucceededCount:       childTaskOutput[i].SucceededCount,
					FailedCount:          childTaskOutput[i].FailedCount,
					ExistCount:           childTaskOutput[i].ExistCount,
					RecommendFailedCount: childTaskOutput[i].RecommendFailedCount,
				}
			} else {
				resultMap[key].TotalCount += childTaskOutput[i].TotalCount
				resultMap[key].SucceededCount += childTaskOutput[i].SucceededCount
				resultMap[key].FailedCount += childTaskOutput[i].FailedCount
				resultMap[key].ExistCount += childTaskOutput[i].ExistCount
				resultMap[key].RecommendFailedCount += childTaskOutput[i].RecommendFailedCount
			}
		}
	}

	// calculate finished count
	for i := range resultMap {
		output.Results = append(output.Results, resultMap[i])
	}

	return output
}
