package searchable_products // nolint

import (
	"context"
	"encoding/json"

	validator "github.com/go-playground/validator/v10"
	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/AfterShip/connectors-library/sdks/products_center"
	"github.com/AfterShip/gopkg/log"
	"github.com/AfterShip/pltf-pd-product-listings/internal/consts"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
)

type BulkEditCombinedListingsTaskInput struct {
	Organization         models.Organization `json:"organization" validate:"required"`
	SourceApp            models.App          `json:"source_app" validate:"required"`
	SearchableProductIDs []string            `json:"searchable_product_ids" validate:"required"`
	CombinedSetting      CombinedSetting     `json:"combined_setting" validate:"required"`
}

type CombinedSetting struct {
	Title          products_center.FieldCombined           `json:"title"`
	Description    products_center.FieldCombined           `json:"description"`
	Media          products_center.FieldCombined           `json:"media"`
	VariantOptions []products_center.VariantOptionCombined `json:"variant_options"`
}

type BulkEditCombinedListingsTaskOutput struct {
	TotalCount     int `json:"total_count"`
	SucceededCount int `json:"succeeded_count"`
	FailedCount    int `json:"failed_count"`
}

type BulkEditCombinedListingsTask struct {
	Logger    *log.Logger
	Validator *validator.Validate
}

func (t *BulkEditCombinedListingsTask) validate(input *BulkEditCombinedListingsTaskInput) error {
	if err := t.Validator.Struct(input); err != nil {
		return err
	}

	if len(input.SearchableProductIDs) == 0 {
		return errors.New("searchable product IDs is empty")
	}

	return nil
}

func (t *BulkEditCombinedListingsTask) BuildTaskArgs(ctx context.Context, input models.TaskInput) (models.TaskArgs, error) {

	args, ok := input.(*BulkEditCombinedListingsTaskInput)
	if !ok {
		return models.TaskArgs{}, errors.New("invalid input type")
	}

	if err := t.validate(args); err != nil {
		return models.TaskArgs{}, err
	}

	taskArgs := models.TaskArgs{
		GroupName:  consts.BulkEditCombinedListings,
		ResourceID: args.Organization.ID,
		StoreKey:   args.SourceApp.Key,
		Platform:   args.SourceApp.Platform,
		Type:       consts.SingleTaskType,
		Inputs:     args,
	}

	return taskArgs, nil
}

func (t *BulkEditCombinedListingsTask) ParseOutput(ctx context.Context, task *models.Task) models.TaskOutput {
	outputs := BulkEditCombinedListingsTaskOutput{}
	if err := json.Unmarshal([]byte(task.Outputs.Data), &outputs); err != nil {
		t.Logger.With(zap.String("Id", task.ID)).WarnCtx(ctx, "Failed to parse bulk edit combined listings task output", zap.Error(err))
	}

	if outputs.TotalCount == 0 {
		input := BulkEditCombinedListingsTaskInput{}
		if err := json.Unmarshal([]byte(task.Inputs), &input); err != nil {
			t.Logger.With(zap.String("Id", task.ID)).WarnCtx(ctx, "Failed to parse bulk edit combined listings task input", zap.Error(err))
		}

		outputs.TotalCount = len(input.SearchableProductIDs)
	}

	return outputs
}
