package searchable_products

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
)

func Test_searchableProductESModel_toSearchableProductModel(t *testing.T) {
	type fields struct {
		ID                  string
		ProductsCenterID    string
		ConnectorsProductID string
		Title               string
		Status              string
		Vendor              string
		Media               string
		Source              models.Source
		Organization        models.Organization
		SalesChannels       []models.SalesChannel
		Categories          []Category
		Tags                []string
		Attributes          []Attribute
		Variants            []Variant
		Version             int64
		CreatedAt           time.Time
		UpdatedAt           time.Time
		Deleted             bool
	}
	tests := []struct {
		name   string
		fields fields
		want   *SearchableProduct
	}{
		// TODO: Add test cases.
		{
			name: "normal",
			fields: fields{
				ID:               "f5011075f2614fd4817daecc78bbe21b",
				ProductsCenterID: "f5011075f2614fd4817daecc78bbe21a",
				Status:           "active",
			},
			want: &SearchableProduct{
				ID:               "f5011075f2614fd4817daecc78bbe21b",
				ProductsCenterID: "f5011075f2614fd4817daecc78bbe21a",
				Status:           "active",
			},
		},
		{
			name: "status is empty",
			fields: fields{
				ID:               "f5011075f2614fd4817daecc78bbe21b",
				ProductsCenterID: "f5011075f2614fd4817daecc78bbe21a",
				Status:           "",
			},
			want: &SearchableProduct{
				ID:               "f5011075f2614fd4817daecc78bbe21b",
				ProductsCenterID: "f5011075f2614fd4817daecc78bbe21a",
				Status:           "inactive",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := &searchableProductESModel{
				ID:                  tt.fields.ID,
				ProductsCenterID:    tt.fields.ProductsCenterID,
				ConnectorsProductID: tt.fields.ConnectorsProductID,
				Title:               tt.fields.Title,
				Status:              tt.fields.Status,
				Vendor:              tt.fields.Vendor,
				Media:               tt.fields.Media,
				Source:              tt.fields.Source,
				Organization:        tt.fields.Organization,
				SalesChannels:       tt.fields.SalesChannels,
				Categories:          tt.fields.Categories,
				Tags:                tt.fields.Tags,
				Attributes:          tt.fields.Attributes,
				Variants:            tt.fields.Variants,
				Version:             tt.fields.Version,
				CreatedAt:           tt.fields.CreatedAt,
				UpdatedAt:           tt.fields.UpdatedAt,
				Deleted:             tt.fields.Deleted,
			}
			assert.Equalf(t, tt.want, p.toSearchableProductModel(), "toSearchableProductModel()")
		})
	}
}
