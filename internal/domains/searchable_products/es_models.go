package searchable_products

import (
	"fmt"
	"strconv"
	"time"

	elastic "github.com/olivere/elastic/v7"
	"github.com/pkg/errors"

	"github.com/AfterShip/pltf-pd-product-listings/internal/consts"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
	"github.com/AfterShip/pltf-pd-product-listings/internal/utils/elasticsearch"
)

type searchArgs struct {
	OrganizationID             string
	SourceStoreKey             string
	SourcePlatform             string
	SourceTypes                []string
	SourceIDs                  []string
	Vendor                     string
	ProductTitle               string
	FulfillmentServices        []string
	Status                     []string
	VariantStatus              []string
	IDs                        []string
	ProductsCenterVariantIDs   []string
	ConnectorsProductIDs       []string
	CategorySourceIDs          []string
	Tags                       []string
	ProductTypes               []string
	SourceSalesChannelNames    []string
	SKUs                       []string
	IncludedDeletedProduct     bool
	IncludedSalesChannelStores []models.SalesChannel
	ExcludedSalesChannelStores []models.SalesChannel
	SourceInventoryItemID      string
	CombinedListingID          string
	CombinedStatus             string
	Query                      string
	Cursor                     string
	Limit                      int
	Page                       int
}

func (args *searchArgs) validate() error {
	if (args.Page == 0 && args.Cursor == "") || args.Page < 0 || args.Limit <= 0 {
		return errors.WithMessagef(models.ErrMissingRequiredField, "input invalidate page、cursor or limit")
	}

	return nil
}

func (args *searchArgs) buildESQueryByArgs() *elastic.BoolQuery {
	query := elastic.NewBoolQuery()
	// ---- add term query ----
	elasticsearch.BuildTermQuery(query, "organization.id", args.OrganizationID)
	elasticsearch.BuildTermQuery(query, "source.app.key", args.SourceStoreKey)
	elasticsearch.BuildTermQuery(query, "source.app.platform", args.SourcePlatform)
	elasticsearch.BuildTermQuery(query, "vendor", args.Vendor)
	elasticsearch.BuildTermQuery(query, "combined.listing_id", args.CombinedListingID)
	elasticsearch.BuildTermQuery(query, "combined.status", args.CombinedStatus)
	elasticsearch.BuildTermsQuery(query, "source_sales_channels.name", args.SourceSalesChannelNames)

	// ---- add terms query ----
	elasticsearch.BuildTermsQuery(query, "status", args.Status)
	elasticsearch.BuildTermsQuery(query, "id", args.IDs)
	elasticsearch.BuildTermsQuery(query, "tags", args.Tags)
	elasticsearch.BuildTermsQuery(query, "product_types", args.ProductTypes)
	elasticsearch.BuildTermsQuery(query, "connectors_product_id", args.ConnectorsProductIDs)
	elasticsearch.BuildTermsQuery(query, "categories.source_id", args.CategorySourceIDs)
	elasticsearch.BuildTermsQuery(query, "source.id", args.SourceIDs)
	elasticsearch.BuildTermsQuery(query, "source.type", args.SourceTypes)

	// -- add match phrase query --
	addTitleMatchPhraseQueryFilter(query, args.ProductTitle)

	// ---- add special query ----
	fulfillmentServicesQuery := elastic.NewBoolQuery()
	for _, v := range args.FulfillmentServices {
		addVariantAnyOfFilter(fulfillmentServicesQuery, "fulfillment_service", v)
	}
	query.Filter(fulfillmentServicesQuery)

	addVariantEQFilter(query, "source_inventory_item_id", args.SourceInventoryItemID)

	addVariantINFilter(query, "products_center_variant_id", args.ProductsCenterVariantIDs)

	addVariantStatusFilter(query, args.VariantStatus)
	addVariantSkusFilter(query, args.SKUs)
	addSalesChannelStoresNotInFilter(query, args.ExcludedSalesChannelStores)
	addSalesChannelStoresAnyOfFilter(query, args.IncludedSalesChannelStores)
	addQueryWithQueryString(query, args.Query)

	if !args.IncludedDeletedProduct {
		query.Filter(elastic.NewTermQuery("deleted", false))
	}

	return query
}

func fillSearchCursorAndLimitByArgs(search *elastic.SearchService, args *searchArgs) error {
	if args.Cursor == "" {
		return nil
	}
	cursor, err := elasticsearch.ParseCursor(args.Cursor)
	if err != nil {
		return errors.WithMessage(elasticsearch.ErrorInvalidCursor, err.Error())
	}
	search.Size(args.Limit).SearchAfter(cursor...)
	return nil
}

func fillSearchPageAndLimitByArgs(search *elastic.SearchService, args *searchArgs) {
	if args.Page == 0 || args.Cursor != "" {
		return
	}
	search.Size(args.Limit).From((args.Page - 1) * args.Limit)
}

// addSalesChannelStoresNotInFilter 有 saleChannelStores 里面的任何一个 store，就过滤掉
func addSalesChannelStoresNotInFilter(query *elastic.BoolQuery, salesChannelStores []models.SalesChannel) {
	if len(salesChannelStores) == 0 {
		return
	}

	shouldQueries := elastic.NewBoolQuery()
	for _, store := range salesChannelStores {
		shouldQueries.Should(elastic.NewBoolQuery().
			Filter(elastic.NewTermQuery("sales_channels.store_key", store.StoreKey)).
			Filter(elastic.NewTermQuery("sales_channels.platform", store.Platform)))
	}

	query.MustNot(shouldQueries)
}

// addSalesChannelStoresAnyOfFilter 必须有 saleChannelStores 里面的任意一个 store
func addSalesChannelStoresAnyOfFilter(query *elastic.BoolQuery, salesChannelStores []models.SalesChannel) {
	if len(salesChannelStores) == 0 {
		return
	}

	shouldQueries := elastic.NewBoolQuery()
	for _, store := range salesChannelStores {
		shouldQueries.Should(elastic.NewBoolQuery().
			Filter(elastic.NewTermQuery("sales_channels.store_key", store.StoreKey)).
			Filter(elastic.NewTermQuery("sales_channels.platform", store.Platform)))
	}

	query.Must(shouldQueries)
}

// addQueryWithQueryString 处理 query 字段，会对 title、asin、variants.sku 进行匹配
func addQueryWithQueryString(query *elastic.BoolQuery, queryString string) {
	if len(queryString) == 0 {
		return
	}

	shouldQueries := elastic.NewBoolQuery()

	// search title
	fuzzySearchTitleQuery := elastic.NewMatchPhraseQuery("title", queryString)

	// search asin, 只有 Amazon 会有 attributes.name = 'asin' 的数据
	searchASINQuery := elastic.NewBoolQuery().Must(elastic.NewBoolQuery().
		Filter(elastic.NewTermsQuery("attributes.name", "asin")).
		Filter(elastic.NewTermsQuery("attributes.values.value", queryString)))

	// search variants SKU
	searchVariantsSKUQuery := elastic.NewNestedQuery(
		"variants", elastic.NewMatchPhraseQuery("variants.sku", queryString),
	)

	// search source product id
	searchProductIDQuery := elastic.NewTermQuery("source.id", queryString)

	shouldQueries.Should(fuzzySearchTitleQuery).Should(searchASINQuery).Should(searchVariantsSKUQuery).Should(searchProductIDQuery)
	query.Filter(shouldQueries)
}

// addVariantAnyOfFilter 处理 variants 内的字段查询条件
func addVariantAnyOfFilter(query *elastic.BoolQuery, field, value string) {
	if query == nil || field == "" || value == "" {
		return
	}

	query.Should(elastic.NewNestedQuery(
		"variants", elastic.NewMatchPhraseQuery("variants."+field, value),
	))
}

func addTitleMatchPhraseQueryFilter(query *elastic.BoolQuery, value string) {
	if value == "" {
		return
	}
	query.Filter(elastic.NewMatchPhraseQuery("title", value))
}

func addVariantEQFilter(query *elastic.BoolQuery, field, value string) {
	if query == nil || field == "" || value == "" {
		return
	}

	query.Filter(elastic.NewNestedQuery(
		"variants", elastic.NewMatchPhraseQuery("variants."+field, value),
	))
}

func addVariantStatusFilter(query *elastic.BoolQuery, values []string) {
	if query == nil || len(values) == 0 {
		return
	}

	termsValues := make([]interface{}, len(values))
	for i, v := range values {
		termsValues[i] = v
	}

	query.Filter(elastic.NewNestedQuery(
		"variants", elastic.NewTermsQuery("variants.status", termsValues...),
	))
}

func addVariantSkusFilter(query *elastic.BoolQuery, values []string) {
	if query == nil || len(values) == 0 {
		return
	}

	termsValues := make([]interface{}, len(values))
	for i, v := range values {
		termsValues[i] = v
	}

	// .keyword
	query.Filter(elastic.NewNestedQuery(
		"variants", elastic.NewTermsQuery("variants.sku.keyword", termsValues...),
	))
}

func addVariantINFilter(query *elastic.BoolQuery, field string, values []string) {
	if query == nil || field == "" || len(values) == 0 {
		return
	}

	termsValues := make([]interface{}, len(values))
	for i, v := range values {
		termsValues[i] = v
	}

	query.Filter(elastic.NewNestedQuery(
		"variants", elastic.NewTermsQuery("variants."+field, termsValues...),
	))
}

type searchableProductESModel struct {
	ID                  string                `json:"id"`
	ProductsCenterID    string                `json:"products_center_id"`
	ConnectorsProductID string                `json:"connectors_product_id"`
	Title               string                `json:"title"`
	Status              string                `json:"status"`
	Vendor              string                `json:"vendor"`
	Media               string                `json:"media"`
	Source              models.Source         `json:"source"`
	Organization        models.Organization   `json:"organization"`
	SalesChannels       []models.SalesChannel `json:"sales_channels"`
	SourceSalesChannels []SourceSalesChannel  `json:"source_sales_channels"`
	Categories          []Category            `json:"categories"`
	Tags                []string              `json:"tags"`
	ProductTypes        []string              `json:"product_types"`
	Attributes          []Attribute           `json:"attributes"`
	Variants            []Variant             `json:"variants"`
	Combined            Combined              `json:"combined"`
	Options             []Option              `json:"options"`
	Version             int64                 `json:"version" validate:"required"`
	CreatedAt           time.Time             `json:"created_at"`
	UpdatedAt           time.Time             `json:"updated_at"`
	Deleted             bool                  `json:"deleted"`
}

func (p *searchableProductESModel) esIndex() string {
	return fmt.Sprintf(IndexNameTemplateV2, strconv.Itoa(p.CreatedAt.Year()))
}

func (p *searchableProductESModel) fillCreatedAndUpdatedAtTime(oldProduct *searchableProductESModel) {
	p.UpdatedAt = time.Now()
	if oldProduct == nil {
		p.CreatedAt = time.Now()
	} else {
		p.CreatedAt = oldProduct.CreatedAt
	}
}

func (p *searchableProductESModel) compareWithOldProduct(oldProduct *searchableProductESModel) error {
	if oldProduct == nil {
		return nil
	}

	if p.Version > oldProduct.Version {
		return nil
	}

	return errors.Wrapf(models.ErrVersionConflict, "id: %s, old_version: %d, new_version: %d",
		oldProduct.ID, oldProduct.Version, p.Version)
}

func (p *searchableProductESModel) toSearchableProductModel() *SearchableProduct {
	if p == nil {
		return nil
	}
	serviceModel := &SearchableProduct{
		ID:                  p.ID,
		ProductsCenterID:    p.ProductsCenterID,
		ConnectorsProductID: p.ConnectorsProductID,
		Title:               p.Title,
		Status:              p.Status,
		Vendor:              p.Vendor,
		Organization:        p.Organization,
		Source:              p.Source,
		Categories:          p.Categories,
		Tags:                p.Tags,
		ProductTypes:        p.ProductTypes,
		SalesChannels:       p.SalesChannels,
		SourceSalesChannels: p.SourceSalesChannels,
		Attributes:          p.Attributes,
		Variants:            p.Variants,
		Options:             p.Options,
		Combined:            p.Combined,
		Version:             p.Version,
		CreatedAt:           p.CreatedAt,
		UpdatedAt:           p.UpdatedAt,
		Media:               toSearchableProductMediaModel(p.Media),
		Deleted:             p.Deleted,
	}

	// status 为空时，设置为 inactive
	if serviceModel.Status == "" {
		// logger.Get().Warn("searchableProductESModel toSearchableProductModel status is empty",
		// 	zap.String("id", p.ID),
		// 	zap.String("connectors_product_id", p.ConnectorsProductID))
		serviceModel.Status = string(consts.ProductsCenterProductPublishStateInactive)
	}

	// variant.status 为空时，设置为 active
	for index := range serviceModel.Variants {
		if serviceModel.Variants[index].Status == "" {
			serviceModel.Variants[index].Status = consts.ProductsCenterVariantStatusActive
		}
	}

	return serviceModel
}

type optionNamesAggregationArgs struct {
	OrganizationID string
	SourceStoreKey string
	SourcePlatform string
	IDs            []string
	SourceIDs      []string
}
