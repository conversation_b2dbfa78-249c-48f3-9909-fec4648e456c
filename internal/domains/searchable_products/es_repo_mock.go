package searchable_products

import (
	"context"
	"encoding/json"

	"github.com/olivere/elastic/v7"
	"github.com/stretchr/testify/mock"

	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
)

type mockRepo struct {
	mock.Mock
}

func (m *mockRepo) getByID(_ context.Context, id string) (*searchableProductESModel, error) {
	arguments := m.Called(id)
	result, _ := arguments.Get(0).(*searchableProductESModel)
	err := arguments.Error(1)
	return result, err
}

func (m *mockRepo) search(_ context.Context, args *searchArgs) ([]*searchableProductESModel, models.Pagination, error) {
	arguments := m.Called(args)
	result, _ := arguments.Get(0).([]*searchableProductESModel)
	pagination, _ := arguments.Get(1).(models.Pagination)
	err := arguments.Error(2)
	return result, pagination, err
}

func (m *mockRepo) searchIDs(_ context.Context, args *searchArgs) ([]string, models.Pagination, error) {
	arguments := m.Called(args)
	result, _ := arguments.Get(0).([]string)
	pagination, _ := arguments.Get(1).(models.Pagination)
	err := arguments.Error(2)
	return result, pagination, err
}

func (m *mockRepo) searchSourceIDs(_ context.Context, args *searchArgs) ([]string, models.Pagination, error) {
	arguments := m.Called(args)
	result, _ := arguments.Get(0).([]string)
	pagination, _ := arguments.Get(1).(models.Pagination)
	err := arguments.Error(2)
	return result, pagination, err
}

func (m *mockRepo) batchUpsert(_ context.Context, esProducts []*searchableProductESModel) error {
	arguments := m.Called(esProducts)
	return arguments.Error(0)
}

func (m *mockRepo) eSProxyCount(ctx context.Context, index string, queryBody json.RawMessage) (int64, error) {
	arguments := m.Called(index, queryBody)
	result, _ := arguments.Get(0).(int64)
	err := arguments.Error(1)
	return result, err
}

func (m *mockRepo) eSProxySearch(ctx context.Context, index string, queryBody json.RawMessage) (*elastic.SearchResult, error) {
	arguments := m.Called(index, queryBody)
	result, _ := arguments.Get(0).(*elastic.SearchResult)
	err := arguments.Error(1)
	return result, err
}

func (m *mockRepo) refreshESIndex(ctx context.Context) {
	m.Called(ctx)
}

func (m *mockRepo) optionNamesAggregation(ctx context.Context, args *optionNamesAggregationArgs) ([]string, error) {
	arguments := m.Called(args)
	result, _ := arguments.Get(0).([]string)
	err := arguments.Error(1)
	return result, err
}
