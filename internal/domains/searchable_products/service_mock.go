package searchable_products

import (
	"context"
	"encoding/json"

	"github.com/olivere/elastic/v7"
	"github.com/stretchr/testify/mock"

	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
)

type MockService struct {
	mock.Mock
}

func (m *MockService) GetByID(_ context.Context, id string) (*SearchableProduct, error) {
	arguments := m.Called(id)
	result, _ := arguments.Get(0).(*SearchableProduct)
	err := arguments.Error(1)
	return result, err
}

func (m *MockService) Search(_ context.Context, args *SearchArgs) ([]*SearchableProduct, models.Pagination, error) {
	arguments := m.Called(args)
	result, _ := arguments.Get(0).([]*SearchableProduct)
	pagination, _ := arguments.Get(1).(models.Pagination)
	err := arguments.Error(2)
	return result, pagination, err
}

func (m *MockService) SearchIDs(_ context.Context, args *SearchArgs) ([]string, models.Pagination, error) {
	arguments := m.Called(args)
	result, _ := arguments.Get(0).([]string)
	pagination, _ := arguments.Get(1).(models.Pagination)
	err := arguments.Error(2)
	return result, pagination, err
}

func (m *MockService) Upsert(_ context.Context, req *UpsertSearchableProductRequest) (*SearchableProduct, error) {
	arguments := m.Called(req)
	result, _ := arguments.Get(0).(*SearchableProduct)
	err := arguments.Error(1)
	return result, err
}

func (m *MockService) ESProxyProductListingCount(ctx context.Context, index string, queryBody json.RawMessage) (int64, error) {
	arguments := m.Called(index, queryBody)
	result, _ := arguments.Get(0).(int64)
	err := arguments.Error(1)
	return result, err
}

func (m *MockService) ESProxyProductListingSearch(ctx context.Context, index string, queryBody json.RawMessage) (*elastic.SearchResult, error) {
	arguments := m.Called(index, queryBody)
	result, _ := arguments.Get(0).(*elastic.SearchResult)
	err := arguments.Error(1)
	return result, err
}

func (m *MockService) DeleteByID(_ context.Context, id string) error {
	arguments := m.Called(id)
	return arguments.Error(0)
}

func (m *MockService) OptionsAggregation(ctx context.Context, args *OptionsAggregationArgs) (*OptionsAggregationResult, error) {
	arguments := m.Called(args)
	result, _ := arguments.Get(0).(*OptionsAggregationResult)
	err := arguments.Error(1)
	return result, err
}
