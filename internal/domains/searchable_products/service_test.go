package searchable_products

import (
	"context"
	"testing"
	"time"

	"github.com/go-playground/validator/v10"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"

	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
	"github.com/AfterShip/pltf-pd-product-listings/internal/utils/elasticsearch"
)

func TestServiceImpl_GetByID(t *testing.T) {
	mockRepo := new(mockRepo)

	testcases := []struct {
		name   string
		input  string
		expErr error
		mock   func()
	}{
		{
			name:   "Error case",
			input:  "",
			expErr: models.ErrMissingRequiredField,
			mock: func() {
				mockRepo.On("getByID", mock.Anything, mock.Anything).
					Return(nil, models.ErrMissingRequiredField).Once()
			},
		},
		{
			name:   "Good case",
			input:  "14e2203a9acf42468fa75d24c44b527e",
			expErr: nil,
			mock: func() {
				mockRepo.On("getByID", mock.Anything, mock.Anything).
					Return(&searchableProductESModel{}, nil).Once()
			},
		},
	}

	for _, tt := range testcases {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mock != nil {
				tt.mock()
			}

			svc := buildTestService(mockRepo)
			_, err := svc.GetByID(context.Background(), tt.input)
			require.ErrorIs(t, err, tt.expErr)
		})
	}
}

func TestServiceImpl_Search(t *testing.T) {
	mockRepo := new(mockRepo)

	type exp struct {
		result     []*SearchableProduct
		pagination models.Pagination
		err        error
	}

	testcases := []struct {
		name  string
		input *SearchArgs
		exp   exp
		mock  func()
	}{
		{
			name:  "Not input page or cursor case",
			input: &SearchArgs{},
			exp: exp{
				err: models.ErrMissingRequiredField,
			},
		},
		{
			name: "Not input limit case",
			input: &SearchArgs{
				Page: 1,
			},
			exp: exp{
				err: models.ErrMissingRequiredField,
			},
		},
		{
			name: "Input invalid limit case",
			input: &SearchArgs{
				Page:  1,
				Limit: -1,
			},
			exp: exp{
				err: models.ErrMissingRequiredField,
			},
		},
		{
			name: "Input invalid page case",
			input: &SearchArgs{
				Page:  -1,
				Limit: 1,
			},
			exp: exp{
				err: models.ErrMissingRequiredField,
			},
		},
		{
			name: "Good case with page and limit input",
			input: &SearchArgs{
				Page:  1,
				Limit: 1,
			},
			exp: exp{
				result: []*SearchableProduct{},
				pagination: models.Pagination{
					Page:       1,
					Limit:      10,
					Total:      0,
					NextCursor: "TTTwMzgxNzg2ODAwOC==",
				},
			},
			mock: func() {
				mockRepo.On("search", mock.Anything, mock.Anything).
					Return([]*SearchableProduct{}, models.Pagination{
						Page:       1,
						Limit:      10,
						Total:      0,
						NextCursor: "TTTwMzgxNzg2ODAwOC==",
					}, nil).Once()
			},
		},
		{
			name: "Good case with cursor and limit input",
			input: &SearchArgs{
				Cursor: "MTcwMzgxNzg2ODAwMA==",
				Limit:  1,
			},
			exp: exp{
				result: []*SearchableProduct{},
				pagination: models.Pagination{
					Limit:          10,
					Total:          0,
					PreviousCursor: "BBBwMzgxNzg2ODAwDA==",
					NextCursor:     "TTTwMzgxNzg2ODAwOC==",
				},
			},
			mock: func() {
				mockRepo.On("search", mock.Anything, mock.Anything).
					Return([]*SearchableProduct{}, models.Pagination{
						Limit:          10,
						Total:          0,
						PreviousCursor: "BBBwMzgxNzg2ODAwDA==",
						NextCursor:     "TTTwMzgxNzg2ODAwOC==",
					}, nil).Once()
			},
		},
		{
			name: "Fail case",
			input: &SearchArgs{
				Cursor: "MTcwMzgxNzg2ODAwMA==",
				Limit:  1,
			},
			exp: exp{
				pagination: models.Pagination{},
				err:        elasticsearch.ErrTooManyRequest,
			},
			mock: func() {
				mockRepo.On("search", mock.Anything, mock.Anything).
					Return(nil, models.Pagination{}, elasticsearch.ErrTooManyRequest).Once()
			},
		},
	}

	for _, tt := range testcases {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mock != nil {
				tt.mock()
			}

			svc := buildTestService(mockRepo)
			result, pagination, err := svc.Search(context.Background(), tt.input)
			require.ErrorIs(t, err, tt.exp.err)
			require.Equal(t, result, tt.exp.result)
			require.Equal(t, pagination, tt.exp.pagination)
		})
	}
}

func TestServiceImpl_SearchIDs(t *testing.T) {
	mockRepo := new(mockRepo)

	type exp struct {
		ids        []string
		pagination models.Pagination
		err        error
	}

	testcases := []struct {
		name  string
		input *SearchArgs
		exp   exp
		mock  func()
	}{
		{
			name:  "Not input page or cursor case",
			input: &SearchArgs{},
			exp: exp{
				err: models.ErrMissingRequiredField,
			},
		},
		{
			name: "Not input limit case",
			input: &SearchArgs{
				Page: 1,
			},
			exp: exp{
				err: models.ErrMissingRequiredField,
			},
		},
		{
			name: "Input invalid limit case",
			input: &SearchArgs{
				Page:  1,
				Limit: -1,
			},
			exp: exp{
				err: models.ErrMissingRequiredField,
			},
		},
		{
			name: "Input invalid page case",
			input: &SearchArgs{
				Page:  -1,
				Limit: 1,
			},
			exp: exp{
				err: models.ErrMissingRequiredField,
			},
		},
		{
			name: "Good case with page and limit input",
			input: &SearchArgs{
				Page:  1,
				Limit: 1,
			},
			exp: exp{
				ids: []string{"1", "2", "3"},
				pagination: models.Pagination{
					Page:       1,
					Limit:      10,
					Total:      3,
					NextCursor: "TTTwMzgxNzg2ODAwOC==",
				},
			},
			mock: func() {
				mockRepo.On("searchIDs", mock.Anything, mock.Anything).
					Return([]string{"1", "2", "3"}, models.Pagination{
						Page:       1,
						Limit:      10,
						Total:      3,
						NextCursor: "TTTwMzgxNzg2ODAwOC==",
					}, nil).Once()
			},
		},
		{
			name: "Good case with cursor and limit input",
			input: &SearchArgs{
				Cursor: "MTcwMzgxNzg2ODAwMA==",
				Limit:  1,
			},
			exp: exp{
				ids: []string{"1", "2", "3"},
				pagination: models.Pagination{
					Limit:          10,
					Total:          3,
					PreviousCursor: "BBBwMzgxNzg2ODAwDA==",
					NextCursor:     "TTTwMzgxNzg2ODAwOC==",
				},
			},
			mock: func() {
				mockRepo.On("searchIDs", mock.Anything, mock.Anything).
					Return([]string{"1", "2", "3"}, models.Pagination{
						Limit:          10,
						Total:          3,
						PreviousCursor: "BBBwMzgxNzg2ODAwDA==",
						NextCursor:     "TTTwMzgxNzg2ODAwOC==",
					}, nil).Once()
			},
		},
		{
			name: "Fail case",
			input: &SearchArgs{
				Cursor: "MTcwMzgxNzg2ODAwMA==",
				Limit:  1,
			},
			exp: exp{
				pagination: models.Pagination{},
				err:        elasticsearch.ErrTooManyRequest,
			},
			mock: func() {
				mockRepo.On("searchIDs", mock.Anything, mock.Anything).
					Return(nil, models.Pagination{}, elasticsearch.ErrTooManyRequest).Once()
			},
		},
	}

	for _, tt := range testcases {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mock != nil {
				tt.mock()
			}

			svc := buildTestService(mockRepo)
			result, pagination, err := svc.SearchIDs(context.Background(), tt.input)
			require.ErrorIs(t, err, tt.exp.err)
			require.Equal(t, result, tt.exp.ids)
			require.Equal(t, pagination, tt.exp.pagination)
		})
	}
}

func TestServiceImpl_Upsert(t *testing.T) {
	mockRepo := new(mockRepo)

	testcases := []struct {
		name   string
		input  *UpsertSearchableProductRequest
		expErr error
		mock   func()
	}{
		{
			name:   "Version required case",
			input:  &UpsertSearchableProductRequest{},
			expErr: models.ErrMissingRequiredField,
		},
		{
			name: "ID required case",
			input: &UpsertSearchableProductRequest{
				Version:          time.Now().UnixNano(),
				ProductsCenterID: "",
			},
			expErr: models.ErrMissingRequiredField,
		},
		{
			name: "Version conflict case",
			input: &UpsertSearchableProductRequest{
				Version:          1709201655299183000,
				ID:               "50425643adc146e7b2963ef3c70b925d",
				ProductsCenterID: "50425643adc146e7b2963ef3c70b925d",
				Variants: []Variant{
					{
						ID:                      "50425643adc146e7b2963ef3c70b925d",
						ProductsCenterVariantID: "50425643adc146e7b2963ef3c70b925d",
					},
				},
			},
			mock: func() {
				mockRepo.On("getByID", mock.Anything, mock.Anything).
					Return(&searchableProductESModel{
						Version:          1709201655299183000,
						ID:               "50425643adc146e7b2963ef3c70b925d",
						ProductsCenterID: "50425643adc146e7b2963ef3c70b925d",
					}, nil).Once()
			},
			expErr: models.ErrVersionConflict,
		},
		{
			name: "Good case",
			input: &UpsertSearchableProductRequest{
				Version:          time.Now().UnixNano(),
				ProductsCenterID: "e723abbce0c34650921f82cb3e70d03f",
				ID:               "e723abbce0c34650921f82cb3e70d03f",
				Variants: []Variant{
					{
						ID:                      "50425643adc146e7b2963ef3c70b925d",
						ProductsCenterVariantID: "50425643adc146e7b2963ef3c70b925d",
					},
				},
			},
			expErr: nil,
			mock: func() {
				mockRepo.On("getByID", mock.Anything, mock.Anything).
					Return(nil, nil).Once()
				mockRepo.On("batchUpsert", mock.Anything, mock.Anything).
					Return(nil).Once()
			},
		},
	}

	for _, tt := range testcases {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mock != nil {
				tt.mock()
			}

			svc := buildTestService(mockRepo)
			_, err := svc.Upsert(context.Background(), tt.input)
			require.ErrorIs(t, err, tt.expErr)
		})
	}
}

func buildTestService(mockRepo esRepo) *ServiceImpl {
	return &ServiceImpl{
		repo:      mockRepo,
		validator: validator.New(),
	}
}
