package searchable_products

import (
	"context"
	"encoding/json"
	"os"
	"testing"
	"time"

	"github.com/pkg/errors"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/AfterShip/pltf-pd-product-listings/internal/logger"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
	"github.com/AfterShip/pltf-pd-product-listings/internal/utils/elasticsearch"
)

const (
	esRefreshInterval = 2 * time.Second
)

func TestProductSearchRepoImpl_GetByID(t *testing.T) {
	sp, esRepo := initTestProductSearchRepo(t)

	ctx := context.Background()

	// case1: 数据不存在
	_, err := esRepo.getByID(ctx, sp.ID)
	require.ErrorIs(t, err, models.ErrResourceNotFound)

	addTestDocIntoES(ctx, t, sp, esRepo)

	// case2: 数据存在
	result, err := esRepo.getByID(ctx, sp.ID)
	require.NoError(t, err)

	assert.Equal(t, result, sp)
}

func TestProductSearchRepoImpl_Search(t *testing.T) { // nolint: maintidx
	sp, esRepo := initTestProductSearchRepo(t)

	ctx := context.Background()

	addTestDocIntoES(ctx, t, sp, esRepo)

	testCases := []struct {
		name         string
		inputParams  searchArgs
		expectOutput []*searchableProductESModel
		expectError  error
	}{
		{
			name: "search by id with invalid page",
			inputParams: searchArgs{
				IDs:   []string{"50425643adc146e7b2963ef3c70b925d"},
				Page:  -1,
				Limit: 10,
			},
			expectOutput: nil,
			expectError:  models.ErrMissingRequiredField,
		},
		{
			name: "search by id with invalid limit",
			inputParams: searchArgs{
				IDs:   []string{"50425643adc146e7b2963ef3c70b925d"},
				Page:  10,
				Limit: -1,
			},
			expectOutput: nil,
			expectError:  models.ErrMissingRequiredField,
		},
		{
			name: "search by id",
			inputParams: searchArgs{
				IDs:   []string{"50425643adc146e7b2963ef3c70b925d"},
				Page:  1,
				Limit: 10,
			},
			expectOutput: []*searchableProductESModel{sp},
		},
		{
			name: "search by connector_product_id",
			inputParams: searchArgs{
				ConnectorsProductIDs: []string{"f2f3d2da86354d1dbe6c6466fccfc499"},
				Page:                 1,
				Limit:                10,
			},
			expectOutput: []*searchableProductESModel{sp},
		},
		{
			name: "search by organization_id",
			inputParams: searchArgs{
				OrganizationID: "09c3167998f54009ad66822375c34384",
				Page:           1,
				Limit:          10,
			},
			expectOutput: []*searchableProductESModel{sp},
		},
		{
			name: "search by source_app_key and source_app_platform",
			inputParams: searchArgs{
				SourceStoreKey: "miamodaebuy",
				SourcePlatform: "shopify",
				Page:           1,
				Limit:          10,
			},
			expectOutput: []*searchableProductESModel{sp},
		},
		{
			name: "search by fulfillment_services",
			inputParams: searchArgs{
				FulfillmentServices: []string{"manual"},
				Page:                1,
				Limit:               10,
			},
			expectOutput: []*searchableProductESModel{sp},
		},
		{
			name: "search by tags",
			inputParams: searchArgs{
				Tags:  []string{"AHC"},
				Page:  1,
				Limit: 10,
			},
			expectOutput: []*searchableProductESModel{sp},
		},
		{
			name: "search title with 1 characters",
			inputParams: searchArgs{
				Query: "a",
				Page:  1,
				Limit: 10,
			},
			expectOutput: nil,
		},
		{
			name: "search title with min 2 consecutive characters",
			inputParams: searchArgs{
				Query: "ai",
				Page:  1,
				Limit: 10,
			},
			expectOutput: []*searchableProductESModel{sp},
		},
		{
			name: "search title with max 3 consecutive characters",
			inputParams: searchArgs{
				Query: "tyl",
				Page:  1,
				Limit: 10,
			},
			expectOutput: []*searchableProductESModel{sp},
		},
		{
			name: "search title with 3 consecutive characters",
			inputParams: searchArgs{
				Query: "Hair",
				Page:  1,
				Limit: 10,
			},
			expectOutput: []*searchableProductESModel{sp},
		},
		{
			name: "search title with more than 3 consecutive characters",
			inputParams: searchArgs{
				Query: "htene",
				Page:  1,
				Limit: 10,
			},
			expectOutput: []*searchableProductESModel{sp},
		},
		{
			name: "search title with a complete word",
			inputParams: searchArgs{
				Query: "Straightener",
				Page:  1,
				Limit: 10,
			},
			expectOutput: []*searchableProductESModel{sp},
		},
		{
			name: "search asin",
			inputParams: searchArgs{
				Query: "aa",
				Page:  1,
				Limit: 10,
			},
			expectOutput: []*searchableProductESModel{sp},
		},
		{
			name: "search variant SKU",
			inputParams: searchArgs{
				Query: "WELL-ADULTCALCIUM-GUMMIES",
				Page:  1,
				Limit: 10,
			},
			expectOutput: []*searchableProductESModel{sp},
		},
		{
			name: "search system categories",
			inputParams: searchArgs{
				CategorySourceIDs: []string{"788904"},
				Page:              1,
				Limit:             10,
			},
			expectOutput: []*searchableProductESModel{sp},
		},
		{
			name: "search custom categories",
			inputParams: searchArgs{
				CategorySourceIDs: []string{"788905"},
				Page:              1,
				Limit:             10,
			},
			expectOutput: []*searchableProductESModel{sp},
		},
		{
			name: "search custom categories in multiple categories",
			inputParams: searchArgs{
				CategorySourceIDs: []string{"788904", "788905"},
				Page:              1,
				Limit:             10,
			},
			expectOutput: []*searchableProductESModel{sp},
		},
		{
			name: "search categories not match",
			inputParams: searchArgs{
				CategorySourceIDs: []string{"xxx", "dddd"},
				Page:              1,
				Limit:             10,
			},
			expectOutput: nil,
		},
		{
			name: "search included sale channels",
			inputParams: searchArgs{
				IncludedSalesChannelStores: []models.SalesChannel{
					{
						StoreKey: "7495328909601442770",
						Platform: "tiktok-shop",
					},
				},
				Page:  1,
				Limit: 10,
			},
			expectOutput: []*searchableProductESModel{sp},
		},
		{
			name: "search excluded sale channels",
			inputParams: searchArgs{
				ExcludedSalesChannelStores: []models.SalesChannel{
					{
						StoreKey: "7495328909601442770",
						Platform: "tiktok-shop",
					},
				},
				Page:  1,
				Limit: 10,
			},
			expectOutput: nil,
		},
		{
			name: "search variant source inventory item id",
			inputParams: searchArgs{
				SourceInventoryItemID: "020d1bcd0e4d4837aaae61036d4ac537",
				Page:                  1,
				Limit:                 10,
			},
			expectOutput: []*searchableProductESModel{sp},
		},
		{
			name: "search variant source inventory item id not exist",
			inputParams: searchArgs{
				SourceInventoryItemID: "123",
				Page:                  1,
				Limit:                 10,
			},
			expectOutput: nil,
		},
		{
			name: "search by status",
			inputParams: searchArgs{
				Status:         []string{"active"},
				OrganizationID: "09c3167998f54009ad66822375c34384",
				Page:           1,
				Limit:          10,
			},
			expectOutput: []*searchableProductESModel{sp},
		},
	}

	for _, tt := range testCases {
		t.Run(tt.name, func(t *testing.T) {
			resultData, _, err := esRepo.search(ctx, &tt.inputParams)
			assert.Equal(t, true, errors.Is(err, tt.expectError))
			assert.Equal(t, tt.expectOutput, resultData)
		})
	}
}

func TestProductSearchRepoImpl_SearchIDs(t *testing.T) {
	sp, esRepo := initTestProductSearchRepo(t)

	ctx := context.Background()

	// case1: 数据不存在
	ids, pagination, err := esRepo.searchIDs(ctx, &searchArgs{
		IDs:   []string{sp.ID},
		Page:  1,
		Limit: 1,
	})
	require.NoError(t, err)
	require.Empty(t, ids)
	require.Equal(t, models.Pagination{
		Page:  1,
		Limit: 1,
		Total: 0,
	}, pagination)

	addTestDocIntoES(ctx, t, sp, esRepo)

	// case2: 数据存在
	ids, _, err = esRepo.searchIDs(ctx, &searchArgs{
		IDs:   []string{sp.ID},
		Page:  1,
		Limit: 1,
	})
	require.NoError(t, err)
	assert.Equal(t, []string{sp.ID}, ids)
}

func TestProductSearchRepoImpl_BatchUpsert(t *testing.T) {
	sp, esRepo := initTestProductSearchRepo(t)

	ctx := context.Background()

	addTestDocIntoES(ctx, t, sp, esRepo)

	// case1: version equal conflict
	err := esRepo.batchUpsert(ctx, []*searchableProductESModel{sp})
	require.NoError(t, err)

	// case2: version lower conflict
	sp.Version = time.Now().In(time.UTC).Add(-10 * time.Minute).UnixNano()
	err = esRepo.batchUpsert(ctx, []*searchableProductESModel{sp})
	require.NoError(t, err)

	// case3: update succeed
	sp.Version = time.Now().In(time.UTC).UnixNano()
	sp.UpdatedAt = time.Now().In(time.UTC)
	sp.Title = "new title"
	err = esRepo.batchUpsert(ctx, []*searchableProductESModel{sp})
	require.NoError(t, err)
	esRepo.cli.Refresh().Index(sp.esIndex()).Do(ctx)

	result, err := esRepo.getByID(ctx, sp.ID)
	require.NoError(t, err)
	assert.Equal(t, result, sp)
}

func initTestSearchableProductData(result interface{}) error {
	bytes, err := os.ReadFile("./testdata/mock_es_searchable_product.json")
	if err != nil {
		return err
	}
	return json.Unmarshal(bytes, result)
}

func initTestProductSearchRepo(t *testing.T) (*searchableProductESModel, *esRepoImpl) {
	sp := &searchableProductESModel{
		Version:   time.Now().UnixNano(),
		CreatedAt: time.Now().In(time.UTC),
		UpdatedAt: time.Now().In(time.UTC),
	}
	err := initTestSearchableProductData(&sp)
	require.NoError(t, err)

	esCli, err := elasticsearch.BuildTestESClient()
	require.NoError(t, err)

	index := sp.esIndex()
	err = elasticsearch.CreateTestIndexWithAlias(esCli, index, IndexAliasV2, "searchable_product_mapping.json")
	require.NoError(t, err)

	return sp, newESRepo(logger.Get(), esCli)
}

func addTestDocIntoES(ctx context.Context, t *testing.T, sp *searchableProductESModel, esRepo *esRepoImpl) {
	// 写入一条数据， id 固定，即使重复写入也不会报错
	err := esRepo.batchUpsert(ctx, []*searchableProductESModel{sp})
	require.NoError(t, err)
	esRepo.cli.Refresh().Index(sp.esIndex()).Do(ctx)
}
