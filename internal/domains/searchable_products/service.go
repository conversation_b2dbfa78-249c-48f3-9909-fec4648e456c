package searchable_products

import (
	"context"
	"encoding/json"
	"sort"

	"github.com/go-playground/validator/v10"
	elastic "github.com/olivere/elastic/v7"
	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/AfterShip/connectors-library/sdks/products_center"
	"github.com/AfterShip/gopkg/log"
	"github.com/AfterShip/pltf-pd-product-listings/internal/consts"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
)

type Service interface {
	GetByID(ctx context.Context, id string) (*SearchableProduct, error)
	Search(ctx context.Context, args *SearchArgs) ([]*SearchableProduct, models.Pagination, error)
	SearchIDs(ctx context.Context, args *SearchArgs) ([]string, models.Pagination, error)
	Upsert(ctx context.Context, req *UpsertSearchableProductRequest) (*SearchableProduct, error)
	DeleteByID(ctx context.Context, id string) error

	OptionsAggregation(ctx context.Context, args *OptionsAggregationArgs) (*OptionsAggregationResult, error)

	// Internal use only
	ESProxyProductListingCount(ctx context.Context, index string, queryBody json.RawMessage) (int64, error)
	ESProxyProductListingSearch(ctx context.Context, index string, queryBody json.RawMessage) (*elastic.SearchResult, error)
}

type ServiceImpl struct {
	logger    *log.Logger
	repo      esRepo
	validator *validator.Validate
}

func NewService(logger *log.Logger, esClient *elastic.Client) *ServiceImpl {
	return &ServiceImpl{
		logger:    logger,
		repo:      newESRepo(logger, esClient),
		validator: validator.New(),
	}
}

func (impl *ServiceImpl) GetByID(ctx context.Context, id string) (*SearchableProduct, error) {
	product, err := impl.repo.getByID(ctx, id)
	if err != nil {
		return nil, err
	}

	return product.toSearchableProductModel(), nil
}

func (impl *ServiceImpl) Search(ctx context.Context, args *SearchArgs) ([]*SearchableProduct, models.Pagination, error) {
	if err := args.validate(); err != nil {
		return nil, models.Pagination{}, errors.WithMessage(models.ErrMissingRequiredField, err.Error())
	}

	resultData, pagination, err := impl.repo.search(ctx, args.toESSearchableProduct())
	if err != nil {
		return nil, models.Pagination{}, err
	}

	searchableProducts := make([]*SearchableProduct, 0, len(resultData))

	for _, product := range resultData {
		searchableProducts = append(searchableProducts, product.toSearchableProductModel())
	}

	return searchableProducts, pagination, nil
}

func (impl *ServiceImpl) SearchIDs(ctx context.Context, args *SearchArgs) ([]string, models.Pagination, error) {
	if err := args.validate(); err != nil {
		return nil, models.Pagination{}, errors.WithMessage(models.ErrMissingRequiredField, err.Error())
	}

	return impl.repo.searchIDs(ctx, args.toESSearchableProduct())
}

func (impl *ServiceImpl) Upsert(ctx context.Context, req *UpsertSearchableProductRequest) (*SearchableProduct, error) {
	if err := impl.validator.Struct(req); err != nil {
		return nil, errors.WithMessage(models.ErrMissingRequiredField, err.Error())
	}

	updatingProduct := req.toESSearchableProduct()

	oldESModel, err := impl.repo.getByID(ctx, updatingProduct.ID)
	if err != nil && !errors.Is(err, models.ErrResourceNotFound) {
		return nil, err
	}

	if err = updatingProduct.compareWithOldProduct(oldESModel); err != nil {
		return nil, err
	}

	updatingProduct.fillCreatedAndUpdatedAtTime(oldESModel)

	if err = impl.repo.batchUpsert(ctx, []*searchableProductESModel{updatingProduct}); err != nil {
		return nil, err
	}

	if oldESModel == nil && updatingProduct.Source.Type == products_center.SourceTypeAdmin {
		impl.repo.refreshESIndex(ctx)
	}

	return updatingProduct.toSearchableProductModel(), nil
}

func (impl *ServiceImpl) DeleteByID(ctx context.Context, id string) error {

	esModel, err := impl.repo.getByID(ctx, id)
	if err != nil {
		return err
	}
	esModel.Deleted = true
	esModel.Version += 1

	if err = impl.repo.batchUpsert(ctx, []*searchableProductESModel{esModel}); err != nil {
		return err
	}

	impl.logger.InfoCtx(ctx, "searchable product deleted", zap.String("id", id))

	// PDP product do refresh
	if esModel.Source.Type == products_center.SourceTypeAdmin {
		impl.repo.refreshESIndex(ctx)
	}

	return nil
}

func (impl *ServiceImpl) ESProxyProductListingCount(ctx context.Context, index string, queryBody json.RawMessage) (int64, error) {
	return impl.repo.eSProxyCount(ctx, index, queryBody)
}

func (impl *ServiceImpl) ESProxyProductListingSearch(ctx context.Context, index string, queryBody json.RawMessage) (*elastic.SearchResult, error) {
	return impl.repo.eSProxySearch(ctx, index, queryBody)
}

func (impl *ServiceImpl) OptionsAggregation(ctx context.Context, args *OptionsAggregationArgs) (*OptionsAggregationResult, error) {

	if err := args.validator(); err != nil {
		return nil, err
	}

	if err := impl.optionsAggregationPredefinedFilter(ctx, args); err != nil {
		return nil, err
	}

	if len(args.SourceIDs) == 0 {
		return &OptionsAggregationResult{OptionNames: []string{}, ProductCount: 0}, nil
	}

	optionNames, err := impl.repo.optionNamesAggregation(ctx, &optionNamesAggregationArgs{
		OrganizationID: args.OrganizationID,
		SourcePlatform: args.SourcePlatform,
		SourceStoreKey: args.SourceStoreKey,
		SourceIDs:      args.SourceIDs,
	})
	if err != nil {
		return nil, err
	}

	return &OptionsAggregationResult{
		OptionNames:  optionNames,
		ProductCount: len(args.SourceIDs),
	}, nil
}

func (impl *ServiceImpl) optionsAggregationPredefinedFilter(ctx context.Context, args *OptionsAggregationArgs) error {
	if args.PredefinedFilter == consts.OptionsAggregationPDPAdmin {
		sourceIDs, _, err := impl.repo.searchSourceIDs(ctx, &searchArgs{
			OrganizationID: args.OrganizationID,
			SourcePlatform: args.SourcePlatform,
			SourceStoreKey: args.SourceStoreKey,
			SourceIDs:      args.SourceIDs,
			Status:         []string{consts.ProductsCenterProductPublishStateActive.String()},
			Page:           1,
			Limit:          len(args.SourceIDs),
		})
		if err != nil {
			return err
		}

		// sort id index
		idIndexMap := make(map[string]int, len(sourceIDs))
		for index, id := range args.SourceIDs {
			idIndexMap[id] = index
		}
		sort.Slice(sourceIDs, func(i, j int) bool {
			return idIndexMap[sourceIDs[i]] < idIndexMap[sourceIDs[j]]
		})

		if len(sourceIDs) > consts.MaxCollectionActiveProductCount {
			sourceIDs = sourceIDs[:consts.MaxCollectionActiveProductCount]
		}
		args.SourceIDs = sourceIDs
	}
	return nil
}
