package resty

import (
	"go.uber.org/zap"

	"github.com/AfterShip/gopkg/log"
)

func NewLogger(prefix string) *Logger {
	return &Logger{
		l:  log.GlobalLogger().GetZapLogger().Sugar().Named(prefix),
		el: log.GlobalLogger().GetZapLogger().Sugar().Named(prefix).With("origin_level", "error"),
	}
}

type Logger struct {
	l  *zap.SugaredLogger
	el *zap.SugaredLogger
}

func (logger Logger) Errorf(f string, v ...interface{}) {
	logger.el.Warnf(f, v...)
}
func (logger Logger) Warnf(f string, v ...interface{}) {
	logger.l.Warnf(f, v...)
}
func (logger Logger) Debugf(f string, v ...interface{}) {
	logger.l.Debugf(f, v...)
}
