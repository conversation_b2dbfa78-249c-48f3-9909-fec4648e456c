package resty

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"go.uber.org/zap"
)

func TestNewLogger(t *testing.T) {
	logger := NewLogger("test-prefix")
	assert.NotNil(t, logger)
	assert.NotNil(t, logger.l)
	assert.NotNil(t, logger.el)
}

func TestLogger_Errorf_Warnf_Debugf(t *testing.T) {
	l := NewLogger("unit-test")
	// 只测试调用不会 panic，实际日志内容需集成测试
	l.<PERSON>("error: %s", "err")
	l.Warnf("warn: %d", 123)
	l.Debugf("debug: %v", struct{}{})
}

func TestLogger_Interface(t *testing.T) {
	var _ interface {
		Errorf(string, ...interface{})
		Warnf(string, ...interface{})
		Debugf(string, ...interface{})
	} = &Logger{l: zap.NewNop().<PERSON>(), el: zap.NewNop().Sugar()}
}
