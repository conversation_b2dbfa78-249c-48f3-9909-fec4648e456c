package redis

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"go.uber.org/zap"
	"go.uber.org/zap/zaptest"
)

type testLogger struct {
	msgs []string
}

func (t *testLogger) Warnf(format string, args ...interface{}) {
	t.msgs = append(t.msgs, format)
}

func TestNewLogger(t *testing.T) {
	logger := NewLogger("test-prefix")
	assert.NotNil(t, logger)
	assert.NotNil(t, logger.l)
}

func TestLoggerPrintf(t *testing.T) {
	// 用 zaptest.NewLogger 创建一个测试 logger
	zl := zaptest.NewLogger(t)
	sugar := zl.Sugar().Named("test")
	l := &Logger{l: sugar}
	ctx := context.Background()
	l.Printf(ctx, "hello %s", "world")
	// 这里只能保证不会 panic，具体日志内容可在集成测试中验证
}

func TestLoggerPrintfWithCustomLogger(t *testing.T) {
	l := &Logger{l: zap.NewNop().Sugar()}
	// 替换 l.l.Warnf 为自定义实现（仅演示，实际 zap.SugaredLogger 不支持直接替换）
	// 这里只能保证调用不会 panic
	ctx := context.Background()
	l.Printf(ctx, "test message: %d", 123)
}
