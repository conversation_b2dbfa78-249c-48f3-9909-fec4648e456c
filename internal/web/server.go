package web

import (
	"go.uber.org/zap"

	api_server "github.com/AfterShip/gopkg/api/server"
	"github.com/AfterShip/gopkg/log"

	"github.com/AfterShip/pltf-pd-product-listings/internal/config"
	"github.com/AfterShip/pltf-pd-product-listings/internal/datastore"
	"github.com/AfterShip/pltf-pd-product-listings/internal/logger"
	"github.com/AfterShip/pltf-pd-product-listings/internal/web/handlers"
)

type Server struct {
	cfg           *config.Config
	logger        *log.Logger
	apiServer     *api_server.Server
	apiHandlers   []handlers.Handler
	adminHandlers []handlers.Handler
}

func NewServer(cfg *config.Config) (*Server, error) {
	if err := logger.Init(cfg.Log.Level); err != nil {
		return nil, err
	}

	apiServer, err := api_server.New(&api_server.Config{
		API:      cfg.API,
		Admin:    cfg.Admin,
		NewRelic: cfg.NewRelic,
		AccessLog: api_server.AccessLogConfig{
			Enabled: cfg.Log.EnableAccessLog,
			Format:  "JSON",
		},
	}, logger.Get())
	if err != nil {
		return nil, err
	}
	apiServer.GetAPIEngine().UseRawPath = true

	return &Server{
		cfg:           cfg,
		logger:        logger.Get(),
		apiHandlers:   make([]handlers.Handler, 0),
		adminHandlers: make([]handlers.Handler, 0),
		apiServer:     apiServer,
	}, nil
}

func (srv *Server) Init() error {
	return datastore.Init(srv.cfg)
}

func (srv *Server) Run() error {
	setupAPIRoutes(srv)

	if err := srv.apiServer.Run(); err != nil {
		return err
	}

	srv.logger.With(
		zap.Any("api", srv.cfg.API),
		zap.Any("admin", srv.cfg.Admin),
	).Info("The server is running")
	return nil
}

func (srv *Server) Shutdown() {
	if err := srv.apiServer.Shutdown(); err != nil {
		srv.logger.Error("Shutdown the server with error", zap.Error(err))
	}
	srv.logger.Info("The server was shutdown normally, bye bye.")
}
