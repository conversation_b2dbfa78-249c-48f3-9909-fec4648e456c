package web

import (
	commerceproxy "github.com/AfterShip/connectors-library/sdks/commerce_proxy"
	"github.com/AfterShip/connectors-library/sdks/jobs/jobs_svc"
	"github.com/AfterShip/feed-sdk-go/events"
	"github.com/AfterShip/pltf-pd-product-listings/internal/web/handlers/product_compliance"
	"github.com/AfterShip/pltf-pd-product-listings/internal/web/handlers/warehouses"

	"github.com/AfterShip/connectors-library/sdks/shein_proxy"
	"github.com/AfterShip/pltf-pd-product-listings/internal/datastore"
	brand_domain "github.com/AfterShip/pltf-pd-product-listings/internal/domains/brand"
	category_domain "github.com/AfterShip/pltf-pd-product-listings/internal/domains/category"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/common/calculators"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/common/databus"
	convert_domain "github.com/AfterShip/pltf-pd-product-listings/internal/domains/convert"
	data_clean_domain "github.com/AfterShip/pltf-pd-product-listings/internal/domains/data_clean"
	organization_setting_domain "github.com/AfterShip/pltf-pd-product-listings/internal/domains/organization_settings"
	product_category_domain "github.com/AfterShip/pltf-pd-product-listings/internal/domains/product_category"
	product_compliance_domain "github.com/AfterShip/pltf-pd-product-listings/internal/domains/product_compliance"
	product_listing_domain "github.com/AfterShip/pltf-pd-product-listings/internal/domains/product_listing"
	searchable_products_domain "github.com/AfterShip/pltf-pd-product-listings/internal/domains/searchable_products"
	setting_domain "github.com/AfterShip/pltf-pd-product-listings/internal/domains/settings"
	task_schedule_domain "github.com/AfterShip/pltf-pd-product-listings/internal/domains/task_schedule"
	task_domain "github.com/AfterShip/pltf-pd-product-listings/internal/domains/task_schedule/task"
	warehouses_domain "github.com/AfterShip/pltf-pd-product-listings/internal/domains/warehouses"
	"github.com/AfterShip/pltf-pd-product-listings/internal/third_party/connectors"
	tiktokapi "github.com/AfterShip/pltf-pd-product-listings/internal/third_party/tiktok_api"
	"github.com/AfterShip/pltf-pd-product-listings/internal/web/handlers/brands"
	"github.com/AfterShip/pltf-pd-product-listings/internal/web/handlers/categories"
	"github.com/AfterShip/pltf-pd-product-listings/internal/web/handlers/convert"
	"github.com/AfterShip/pltf-pd-product-listings/internal/web/handlers/internals/calculator"
	"github.com/AfterShip/pltf-pd-product-listings/internal/web/handlers/internals/data_clean"
	"github.com/AfterShip/pltf-pd-product-listings/internal/web/handlers/organization_settings"
	"github.com/AfterShip/pltf-pd-product-listings/internal/web/handlers/product_listing"
	"github.com/AfterShip/pltf-pd-product-listings/internal/web/handlers/relation"
	"github.com/AfterShip/pltf-pd-product-listings/internal/web/handlers/searchable_products"
	"github.com/AfterShip/pltf-pd-product-listings/internal/web/handlers/settings"
	"github.com/AfterShip/pltf-pd-product-listings/internal/web/handlers/task"
	"github.com/AfterShip/pltf-pd-product-listings/internal/web/middlewares"
)

func setupAPIRoutes(srv *Server) {
	// init base services
	jobService := jobs_svc.NewJobSvc(datastore.Get().ClientStore.CNTJobClient)
	taskService := task_domain.NewService(srv.logger, srv.cfg, &jobService)
	proxy := commerceproxy.NewRestfulProxy(srv.cfg.ConnectorsCommerceProxyAPI.Url)
	sheinAPIService := shein_proxy.NewService(srv.cfg.ConnectorsCommerceProxyAPI.Url)
	tiktokAPIService := tiktokapi.NewService(proxy)
	databusService := databus.NewService(datastore.Get().PubSubClient)

	// init api services
	settingService := setting_domain.NewService(datastore.Get().SpannerCli)
	organizationSettingService := organization_setting_domain.NewService(datastore.Get().SpannerCli)
	searchableProductsService := searchable_products_domain.NewService(srv.logger, datastore.Get().ESClient)
	taskScheduleService := task_schedule_domain.NewService(srv.logger, taskService)
	calculatorsService := calculators.NewService(srv.cfg, srv.logger,
		datastore.Get().SpannerCli,
		datastore.Get().ClientStore.CNTClient,
		datastore.Get().ClientStore.FeedV2Client,
	)
	connectorsService := connectors.NewService(srv.logger, datastore.Get().ClientStore.CNTClient)
	convertService := convert_domain.NewService(srv.cfg, srv.logger, tiktokAPIService, sheinAPIService, datastore.Get().ClientStore.CNTClient, connectorsService)
	categoryService := category_domain.NewService(tiktokAPIService, sheinAPIService, connectorsService, datastore.Get().RedisClient)
	brandService := brand_domain.NewService(sheinAPIService, datastore.Get().RedisClient)
	feedEventService := events.NewService(datastore.Get().PubSubClient)
	productListingService := product_listing_domain.NewService(srv.logger,
		datastore.Get().SpannerCli,
		datastore.Get().ESClient,
		datastore.Get().RedisClient,
		datastore.Get().RedisLocker,
		datastore.Get().ClientStore.ProductsCenterClient,
		settingService,
		calculatorsService,
		categoryService,
		taskService,
		convertService,
		databusService,
		srv.cfg,
		searchableProductsService,
		connectorsService,
		feedEventService,
		datastore.Get().ClientStore.BusinessMonitoringExporter,
		datastore.Get().ClientStore.FeedV1Client,
		datastore.Get().ClientStore.FeedV2Client,
	)
	productCategoryService := product_category_domain.NewService(categoryService, productListingService)
	productComplianceService := product_compliance_domain.NewService(srv.logger, tiktokAPIService)
	warehousesService := warehouses_domain.NewService(srv.logger, sheinAPIService)
	dataCleanService := data_clean_domain.NewService(srv.logger, connectorsService, searchableProductsService, datastore.Get().ClientStore.ProductsCenterClient, datastore.Get().ESClient, datastore.Get().RedisClient)

	srv.apiHandlers = append(srv.apiHandlers,
		settings.NewSetting(srv.logger, settingService),
		organization_settings.NewOrganizationSetting(srv.logger, organizationSettingService),
		searchable_products.NewProduct(srv.logger, datastore.Get().ClientStore.ProductsCenterClient, searchableProductsService),
		task.NewTask(srv.logger, taskScheduleService),
		calculator.NewCalculator(srv.logger, calculatorsService),
		convert.NewConvert(srv.logger, convertService),
		categories.New(srv.logger, categoryService, productCategoryService),
		product_listing.NewProductListing(srv.logger, productListingService),
		relation.NewRelation(srv.logger, productListingService),
		product_compliance.NewComplianceHandler(srv.logger, productComplianceService),
		brands.New(srv.logger, brandService),
		warehouses.NewWarehouses(srv.logger, warehousesService),
		data_clean.NewDataCleanHandler(srv.logger, dataCleanService),
	)

	routeGroup := srv.apiServer.GetAPIRouteGroup()
	routeGroup.Use(middlewares.InjectContext(srv.cfg))

	apiRoute := routeGroup.Group("/api/v1")
	for _, h := range srv.apiHandlers {
		h.RegisterRoutes(apiRoute)
	}
}
