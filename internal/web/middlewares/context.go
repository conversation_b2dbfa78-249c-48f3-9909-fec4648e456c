package middlewares

import (
	"context"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"github.com/AfterShip/gopkg/log"
	"github.com/AfterShip/pltf-pd-product-listings/internal/config"
	"github.com/AfterShip/pltf-pd-product-listings/internal/consts"
)

func InjectContext(cfg *config.Config) gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Set(consts.ContextKeyConfig, cfg)
	}
}

func AddTrackingHandlerFunc() gin.HandlerFunc {
	return func(c *gin.Context) {

		userName := c.GetHeader(consts.HeaderXConsumerUserHeader)
		if len(userName) == 0 {
			userName = "-"
		}

		ctx := c.Request.Context()

		requestAPI := c.Request.Method + " " + c.FullPath()
		ctx = log.AppendFieldsToContext(ctx, zap.String("request_api", requestAPI))
		ctx = log.AppendFieldsToContext(ctx, zap.String("consumer_username", userName))
		ctx = context.WithValue(ctx, "consumer_username", userName)
		c.Request = c.Request.WithContext(ctx)
	}
}
