package settings

import (
	"github.com/gin-gonic/gin"

	"github.com/AfterShip/connectors-library/gin/responder"
	"github.com/AfterShip/gopkg/log"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/settings"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
)

type Setting struct {
	logger    *log.Logger
	service   settings.Service
	responder *responder.Responder
}

func NewSetting(logger *log.Logger, service settings.Service) *Setting {
	s := &Setting{
		logger:    logger,
		service:   service,
		responder: responder.NewResponder(false, models.CommonErrorsMapping),
	}
	s.responder.RegisterDomainError(errorResponseMapping)
	return s
}

func (s *Setting) RegisterRoutes(router *gin.RouterGroup) {
	router.POST("/settings", s.create)
	router.GET("/settings", s.list)

	settingGroup := router.Group("/settings/:id", s.settingValidateMiddleware)
	{
		settingGroup.GET("", s.get)
		settingGroup.PUT("", s.update)
	}

}

func (s *Setting) settingValidateMiddleware(c *gin.Context) {
	args := SettingIDRequest{}
	if err := c.ShouldBindUri(&args); err != nil {
		s.responder.ResponseWithErrorCode(c, models.CodeMissRequiredPathParam, err)
		c.Abort()
	}
	c.Set(ctxKeySettingID, args.ID)
	c.Next()
}
