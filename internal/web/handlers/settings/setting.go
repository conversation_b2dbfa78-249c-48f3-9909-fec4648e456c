package settings

import (
	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/settings"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
)

func (s *Setting) get(c *gin.Context) {
	id := c.GetString(ctxKeySettingID)

	ctx := c.Request.Context()
	result, err := s.service.GetByID(ctx, id)
	if err != nil {
		s.logger.ErrorCtx(ctx, "Get setting by id error",
			zap.String("id", id),
			zap.Error(err))
		s.responder.ResponseWithError(c, err)
		return
	}

	s.responder.ResponseWithOK(c, result)
}

func (s *Setting) list(c *gin.Context) {
	req := ListSettingRequestQuery{}
	if err := c.ShouldBindQuery(&req); err != nil {
		s.responder.ResponseWithErrorCode(c, models.CodeMissRequiredQueryParam, err)
		return
	}

	ctx := c.Request.Context()
	result, err := s.service.List(ctx, &settings.SearchSettingArgs{
		OrganizationID:       req.OrganizationID,
		SourceAppPlatform:    req.SourceAppPlatform,
		SourceAppKey:         req.SourceAppKey,
		SalesChannelPlatform: req.SalesChannelPlatform,
		SalesChannelStoreKey: req.SalesChannelStoreKey,
	})

	if err != nil {
		s.logger.ErrorCtx(ctx, "List setting error",
			zap.String("organization_id", req.OrganizationID),
			zap.String("source_app_platform", req.SourceAppPlatform),
			zap.String("source_app_key", req.SourceAppKey),
			zap.String("sales_channel_platform", req.SalesChannelPlatform),
			zap.String("sales_channel_store_key", req.SalesChannelStoreKey),
			zap.Error(err))
		s.responder.ResponseWithError(c, err)
		return
	}

	response := ListSettingResponseData{
		Settings:        result,
		ParameterString: c.Request.URL.RawQuery,
	}

	s.responder.ResponseWithOK(c, response)
}

func (s *Setting) create(c *gin.Context) {
	var req settings.Setting

	if err := c.ShouldBindJSON(&req); err != nil {
		s.responder.ResponseWithErrorCode(c, models.CodeMissRequiredBodyParam, err)
		return
	}

	ctx := c.Request.Context()
	setting, err := s.service.Create(ctx, &req)
	if err != nil {
		if !errors.Is(err, settings.ErrSettingConflict) {
			s.logger.ErrorCtx(ctx, "Create setting error",
				zap.Error(err))
		}
		s.responder.ResponseWithError(c, err)
		return
	}

	s.responder.ResponseWithCreated(c, setting)
}

func (s *Setting) update(c *gin.Context) {
	id := c.GetString(ctxKeySettingID)

	var req settings.Setting

	if err := c.ShouldBindJSON(&req); err != nil {
		s.responder.ResponseWithErrorCode(c, models.CodeMissRequiredBodyParam, err)
		return
	}

	ctx := c.Request.Context()

	setting, err := s.service.Update(ctx, id, &req)
	if err != nil {
		s.logger.ErrorCtx(ctx, "Update setting by id error",
			zap.String("id", id),
			zap.Error(err))

		s.responder.ResponseWithError(c, err)
		return
	}

	s.responder.ResponseWithOK(c, setting)
}
