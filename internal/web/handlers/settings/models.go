package settings

import (
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/settings"
)

const (
	ctxKeySettingID = "settingID"
)

type SettingIDRequest struct {
	ID string `uri:"id" binding:"required,len=32"`
}

type ListSettingRequestQuery struct {
	OrganizationID       string `form:"organization_id" binding:"required"`
	SourceAppPlatform    string `form:"source_app_platform"`
	SourceAppKey         string `form:"source_app_key"`
	SalesChannelPlatform string `form:"sales_channel_platform"`
	SalesChannelStoreKey string `form:"sales_channel_store_key"`
}

type ListSettingResponseData struct {
	Settings        []*settings.Setting `json:"settings"`
	ParameterString string              `json:"parameter_string"`
}
