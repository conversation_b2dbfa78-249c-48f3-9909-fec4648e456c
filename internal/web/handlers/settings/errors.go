package settings

import (
	"github.com/AfterShip/connectors-library/gin/responder"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/settings"
)

const (
	SettingNotFound responder.MetaCode = 40401
	SettingConflict responder.MetaCode = 40901
)

var errorResponseMapping = map[error]responder.Meta{
	settings.ErrSettingNotFound: responder.Meta{
		Code: SettingNotFound,
		Description: responder.Description{
			Type:    "SettingNotFound",
			Message: "setting not found",
		},
	},

	settings.ErrSettingConflict: responder.Meta{
		Code: SettingConflict,
		Description: responder.Description{
			Type:    "SettingConflict",
			Message: "setting conflict",
		},
	},
}
