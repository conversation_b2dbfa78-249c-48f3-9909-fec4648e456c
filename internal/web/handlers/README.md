## Group API handler implements in this dir with the below hierarchy:
```
internal
 ├── handlers
 │		├── products
 │	    │		├── models.go
 │		│		├── xx_service.go
 │		│		└── xx_repository.go
 │		├── orders
 │		│		├── models.go
 │		│		├── xx_service.go
 │		│		└── xx_repository.go
 |      └── ...
 └── ...
```
### handlers
> 该目录负责封装各个域对外 API 的逻辑
> Handler 主要是负责组装各个 Domain 所需要的数据结构，按业务层次来编排业务逻辑。原则上只会依赖各个 Domain 的 Service.  
 
#### Sub-domains
> 各领域的子域可以通过子目录来划分
* `models.go`: 这里定义 API 所需的 Model，比如：请求参数绑定和校验、请求响应的结构等。这些 Model 可以各自实现方法来适配各个 Domain 的 Model。
* `xx_handler.go`: 实现 API 相关的处理逻辑。Handler 将会按需依赖各个 Domain 的 Service，组装出 API 逻辑。
