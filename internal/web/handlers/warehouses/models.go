package warehouses

import "github.com/AfterShip/pltf-pd-product-listings/internal/domains/warehouses"

type GetWarehousesParams struct {
	OrganizationID string `form:"organization_id" binding:"required"`
	StoreKey       string `form:"store_key" binding:"required"`
	Platform       string `form:"platform" binding:"required"`
}

type GetWarehousesResponseData struct {
	Warehouses      []warehouses.Warehouses `json:"warehouses"`
	ParameterString string                  `json:"parameter_string"`
}
