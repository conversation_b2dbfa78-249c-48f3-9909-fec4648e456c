package warehouses

import (
	"github.com/gin-gonic/gin"

	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/warehouses"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
)

func (w *Warehouses) GetList(c *gin.Context) {
	ctx := c.Request.Context()
	params := GetWarehousesParams{}

	if err := c.ShouldBindQuery(&params); err != nil {
		w.responder.ResponseWithErrorCode(c, models.CodeMissRequiredQueryParam, err)
		return
	}

	warehouseList, err := w.warehousesService.GetWarehouses(ctx, warehouses.GetWarehousesArgs{
		OrganizationID: params.OrganizationID,
		Platform:       params.Platform,
		StoreKey:       params.StoreKey,
	})
	if err != nil {
		w.responder.ResponseWithError(c, err)
		return
	}

	response := GetWarehousesResponseData{
		Warehouses:      warehouseList,
		ParameterString: c.Request.URL.RawQuery,
	}

	w.responder.ResponseWithOK(c, response)
}
