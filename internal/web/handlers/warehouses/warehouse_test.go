package warehouses

import (
	"context"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"github.com/tidwall/gjson"

	warehouses_domain "github.com/AfterShip/pltf-pd-product-listings/internal/domains/warehouses"
	"github.com/AfterShip/pltf-pd-product-listings/internal/logger"
)

func TestWarehouse_list(t *testing.T) {
	mockSvc := new(warehouses_domain.MockWarehousesService)
	testcases := []struct {
		name      string
		path      string
		mock      func()
		expCode   int
		respCheck func(*testing.T, string)
	}{
		{
			name:    "Params validate error",
			path:    "/warehouses?",
			expCode: http.StatusUnprocessableEntity,
		},
		{
			name: "Error case",
			path: "/warehouses?organization_id=123&store_key=123&platform=123",
			mock: func() {
				mockSvc.On("GetWarehouses", mock.Anything, mock.Anything).Return([]warehouses_domain.Warehouses{}, errors.New("some error")).Once()
			},
			expCode: http.StatusInternalServerError,
		},
		{
			name: "Good case",
			path: "/warehouses?organization_id=123&store_key=123&platform=123",
			mock: func() {
				mockSvc.On("GetWarehouses", mock.Anything, mock.Anything).Return([]warehouses_domain.Warehouses{
					{
						SalesChannelID: "1",
					},
				}, nil).Once()
			},
			expCode: http.StatusOK,
			respCheck: func(t *testing.T, resp string) {
				require.NotEmpty(t, gjson.Parse(resp).Get("data").String())
			},
		},
	}
	for _, tt := range testcases {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mock != nil {
				tt.mock()
			}
			w := httptest.NewRecorder()
			gin.SetMode(gin.TestMode)
			_, eng := gin.CreateTestContext(w)

			NewWarehouses(logger.Get(), mockSvc).RegisterRoutes(eng.RouterGroup.Group(""))

			req, _ := http.NewRequestWithContext(context.Background(), http.MethodGet, tt.path, nil)
			eng.ServeHTTP(w, req)
			resp := w.Body.String()
			require.Equal(t, tt.expCode, w.Code, resp)
			if tt.respCheck != nil {
				tt.respCheck(t, resp)
			}
		})
	}
}
