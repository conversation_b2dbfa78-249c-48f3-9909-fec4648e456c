package warehouses

import (
	"github.com/gin-gonic/gin"

	"github.com/AfterShip/connectors-library/gin/responder"
	"github.com/AfterShip/gopkg/log"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/warehouses"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
)

type Warehouses struct {
	logger    *log.Logger
	responder *responder.Responder

	warehousesService warehouses.WarehousesService
}

func NewWarehouses(logger *log.Logger, warehousesService warehouses.WarehousesService) *Warehouses {
	w := &Warehouses{
		logger:            logger,
		responder:         responder.NewResponder(false, models.CommonErrorsMapping),
		warehousesService: warehousesService,
	}
	w.responder.RegisterDomainError(errorResponseMapping)
	return w
}

func (w *Warehouses) RegisterRoutes(router *gin.RouterGroup) {
	router.GET("/warehouses", w.GetList)
}
