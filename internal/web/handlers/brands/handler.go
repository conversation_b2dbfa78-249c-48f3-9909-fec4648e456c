package brands

import (
	"github.com/gin-gonic/gin"
	validator "github.com/go-playground/validator/v10"

	"github.com/AfterShip/connectors-library/gin/responder"
	"github.com/AfterShip/gopkg/log"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/brand"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
)

type Brand struct {
	logger       *log.Logger
	brandService brand.Service
	responder    *responder.Responder
	validator    *validator.Validate
}

func New(logger *log.Logger, brandService brand.Service) *Brand {
	h := &Brand{
		brandService: brandService,
		logger:       logger,
		responder:    responder.NewResponder(true, errorResponseMapping),
	}
	h.responder.RegisterDomainError(errorResponseMapping)
	return h
}

func (h *Brand) RegisterRoutes(router *gin.RouterGroup) {
	apiRouter := router.Group("/brands")

	apiRouter.GET("", h.list)
}

func (h *Brand) list(c *gin.Context) {
	ctx := c.Request.Context()

	req := listRequest{}
	if err := c.ShouldBindQuery(&req); err != nil {
		h.responder.ResponseWithErrorCode(c, models.CodeMissRequiredQueryParam, err)
		return
	}

	resp, err := h.brandService.List(ctx, brand.ListArg{
		OrganizationID:       req.OrganizationID,
		SalesChannelPlatform: req.SalesChannelPlatform,
		SalesChannelStoreKey: req.SalesChannelStoreKey,
		RefreshCache:         req.RefreshCache,
	})
	if err != nil {
		h.responder.ResponseWithError(c, err)
		return
	}

	h.responder.ResponseWithOK(c, resp)
}
