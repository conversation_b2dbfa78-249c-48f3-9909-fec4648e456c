package organization_settings

import (
	"github.com/gin-gonic/gin"

	"github.com/AfterShip/connectors-library/gin/responder"
	"github.com/AfterShip/gopkg/log"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/organization_settings"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
)

type OrganizationSetting struct {
	logger    *log.Logger
	service   organization_settings.Service
	responder *responder.Responder
}

func NewOrganizationSetting(logger *log.Logger, service organization_settings.Service) *OrganizationSetting {
	s := &OrganizationSetting{
		logger:    logger,
		service:   service,
		responder: responder.NewResponder(false, models.CommonErrorsMapping),
	}
	s.responder.RegisterDomainError(errorResponseMapping)
	return s
}

func (s *OrganizationSetting) RegisterRoutes(router *gin.RouterGroup) {
	router.POST("/organization-settings", s.create)
	router.GET("/organization-settings", s.list)

	settingGroup := router.Group("/organization-settings/:id", s.settingValidateMiddleware)
	{
		settingGroup.GET("", s.get)
		settingGroup.PUT("", s.update)
	}

}

func (s *OrganizationSetting) settingValidateMiddleware(c *gin.Context) {
	args := SettingIDRequest{}
	if err := c.ShouldBindUri(&args); err != nil {
		s.responder.ResponseWithErrorCode(c, models.CodeMissRequiredPathParam, err)
		c.Abort()
	}
	c.Set(ctxKeySettingID, args.ID)
	c.Next()
}
