package organization_settings

import (
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/organization_settings"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
)

func (s *OrganizationSetting) get(c *gin.Context) {
	id := c.GetString(ctxKeySettingID)

	ctx := c.Request.Context()
	result, err := s.service.GetByID(ctx, id)
	if err != nil {
		s.logger.ErrorCtx(ctx, "Get setting by id error",
			zap.String("id", id),
			zap.Error(err))
		s.responder.ResponseWithError(c, err)
		return
	}

	s.responder.ResponseWithOK(c, result)
}

func (s *OrganizationSetting) list(c *gin.Context) {
	req := ListOrganizationSettingRequestQuery{}
	if err := c.ShouldBindQuery(&req); err != nil {
		s.responder.ResponseWithErrorCode(c, models.CodeMissRequiredQueryParam, err)
		return
	}

	ctx := c.Request.Context()
	result, err := s.service.List(ctx, &organization_settings.SearchOrganizationSettingArgs{
		OrganizationID: req.OrganizationID,
	})

	if err != nil {
		s.logger.ErrorCtx(ctx, "List organization setting error",
			zap.String("organization_id", req.OrganizationID),
			zap.Error(err))
		s.responder.ResponseWithError(c, err)
		return
	}

	response := ListOrganizationSettingResponseData{
		OrganizationSettings: result,
		ParameterString:      c.Request.URL.RawQuery,
	}

	s.responder.ResponseWithOK(c, response)
}

func (s *OrganizationSetting) create(c *gin.Context) {
	var req organization_settings.OrganizationSetting

	if err := c.ShouldBindJSON(&req); err != nil {
		s.responder.ResponseWithErrorCode(c, models.CodeMissRequiredBodyParam, err)
		return
	}

	ctx := c.Request.Context()
	setting, err := s.service.Create(ctx, &req)
	if err != nil {
		s.logger.ErrorCtx(ctx, "Create setting error",
			zap.Error(err))
		s.responder.ResponseWithError(c, err)
		return
	}

	s.responder.ResponseWithCreated(c, setting)
}

func (s *OrganizationSetting) update(c *gin.Context) {
	id := c.GetString(ctxKeySettingID)

	var req organization_settings.OrganizationSetting

	if err := c.ShouldBindJSON(&req); err != nil {
		s.responder.ResponseWithErrorCode(c, models.CodeMissRequiredBodyParam, err)
		return
	}

	ctx := c.Request.Context()

	setting, err := s.service.Update(ctx, id, &req)
	if err != nil {
		s.logger.ErrorCtx(ctx, "Update setting by id error",
			zap.String("id", id),
			zap.Error(err))

		s.responder.ResponseWithError(c, err)
		return
	}

	s.responder.ResponseWithOK(c, setting)
}
