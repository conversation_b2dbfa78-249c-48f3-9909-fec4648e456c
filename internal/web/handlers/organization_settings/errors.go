package organization_settings

import (
	"github.com/AfterShip/connectors-library/gin/responder"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/organization_settings"
)

const (
	OrganizationSettingNotFound                 responder.MetaCode = 40401
	OrganizationSettingConflict                 responder.MetaCode = 40901
	OrganizationSettingCurrencyConvertorMissing responder.MetaCode = 42204
)

var errorResponseMapping = map[error]responder.Meta{
	organization_settings.ErrOrganizationSettingNotFound: responder.Meta{
		Code: OrganizationSettingNotFound,
		Description: responder.Description{
			Type:    "OrganizationSettingNotFound",
			Message: "organization setting not found",
		},
	},

	organization_settings.ErrOrganizationSettingConflict: responder.Meta{
		Code: OrganizationSettingConflict,
		Description: responder.Description{
			Type:    "OrganizationSettingConflict",
			Message: "organization setting conflict",
		},
	},
	organization_settings.ErrSettingCurrencyConvertorsNotMatch: responder.Meta{
		Code: OrganizationSettingCurrencyConvertorMissing,
		Description: responder.Description{
			Type:    "UnprocessableEntity",
			Message: "currency convertor should match history",
		},
	},
}
