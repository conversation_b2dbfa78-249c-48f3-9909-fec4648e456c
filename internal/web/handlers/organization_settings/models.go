package organization_settings

import (
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/organization_settings"
)

const (
	ctxKeySettingID = "settingID"
)

type SettingIDRequest struct {
	ID string `uri:"id" binding:"required,len=32"`
}

type ListOrganizationSettingRequestQuery struct {
	OrganizationID string `form:"organization_id" binding:"required"`
}

type ListOrganizationSettingResponseData struct {
	OrganizationSettings []*organization_settings.OrganizationSetting `json:"organization_settings"`
	ParameterString      string                                       `json:"parameter_string"`
}
