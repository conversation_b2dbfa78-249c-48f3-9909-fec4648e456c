package organization_settings

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"net/http"
	"net/http/httptest"
	"net/url"
	"strings"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/spf13/viper"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"github.com/tidwall/gjson"

	stdErr "github.com/AfterShip/connectors-errors-sdk-go"
	"github.com/AfterShip/gopkg/cfg"
	"github.com/AfterShip/pltf-pd-product-listings/internal/config"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/organization_settings"

	"github.com/AfterShip/pltf-pd-product-listings/internal/logger"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
)

func TestOrganizationSetting_getByIDHandler(t *testing.T) {
	configs := new(config.Config)
	_, err := cfg.LoadViperConfig(configs, func(v *viper.Viper) { v.AddConfigPath("../../../../cmd/apiserver/conf") })
	require.NoError(t, err)

	mockSvc := new(organization_settings.MockOrganizationSettingService)

	testcases := []struct {
		name      string
		path      string
		mock      func()
		expCode   int
		respCheck func(*testing.T, string)
	}{
		{
			name: "Setting ID Too Short case",
			path: "/organization-settings/123",
			mock: func() {
				mockSvc.On("GetByID", mock.Anything).Return(&organization_settings.OrganizationSetting{}, nil).Once()
			},
			expCode: http.StatusUnprocessableEntity,
		},
		{
			name: "Setting ID Too Long case",
			path: "/organization-settings/" + strings.Repeat("x", 33),
			mock: func() {
				mockSvc.On("GetByID", mock.Anything).Return(&organization_settings.OrganizationSetting{}, nil).Once()
			},
			expCode: http.StatusUnprocessableEntity,
		},
		{
			name: "Good case",
			path: "/organization-settings/" + strings.Repeat("x", 32),
			mock: func() {
				mockSvc.On("GetByID", mock.Anything, mock.Anything).Return(&organization_settings.OrganizationSetting{}, nil).Once()
			},
			expCode: http.StatusOK,
			respCheck: func(t *testing.T, resp string) {
				require.NotEmpty(t, gjson.Parse(resp).Get("data").String())
			},
		},
	}
	for _, tt := range testcases {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mock != nil {
				tt.mock()
			}
			w := httptest.NewRecorder()
			gin.SetMode(gin.TestMode)
			_, eng := gin.CreateTestContext(w)

			NewOrganizationSetting(logger.Get(), mockSvc).RegisterRoutes(eng.RouterGroup.Group(""))

			req, _ := http.NewRequestWithContext(context.Background(), http.MethodGet, tt.path, nil)
			eng.ServeHTTP(w, req)
			resp := w.Body.String()
			require.Equal(t, tt.expCode, w.Code, resp)
			if tt.respCheck != nil {
				tt.respCheck(t, resp)
			}
		})
	}
}

func TestOrganizationSetting_createHandler(t *testing.T) {
	configs := new(config.Config)
	_, err := cfg.LoadViperConfig(configs, func(v *viper.Viper) { v.AddConfigPath("../../../../cmd/apiserver/conf") })
	require.NoError(t, err)

	mockSvc := new(organization_settings.MockOrganizationSettingService)

	testcases := []struct {
		name                string
		path                string
		body                []byte
		mock                func()
		expCode             int
		expMetaCode         int
		expStandardizedCode int
		respCheck           func(*testing.T, string)
	}{
		{
			name:    "Body is nil case",
			path:    "/organization-settings",
			expCode: http.StatusUnprocessableEntity,
		},
		{
			name: "Service error case",
			path: "/organization-settings",
			mock: func() {
				mockSvc.On("Create", mock.Anything, mock.Anything).Return(&organization_settings.OrganizationSetting{}, errors.New("create error")).Once()
			},
			expCode: http.StatusInternalServerError,
			body:    []byte(`{"organization":{"id":"********************************"}}`),
		},
		{
			name: "test Service return std error case",
			path: "/organization-settings",
			mock: func() {
				mockSvc.On("Create", mock.Anything, mock.Anything).Return(&organization_settings.OrganizationSetting{}, stdErr.FeedFulfillmentOrderSyncBlockedForFulfillByMerchant_700412008).Once()
			},
			expCode: http.StatusPreconditionFailed,
			body:    []byte(`{"organization":{"id":"********************************"}}`),
		},
		{
			name: "Good case",
			path: "/organization-settings",
			body: []byte(`{"organization":{"id":"********************************"}}`),
			mock: func() {
				mockSvc.On("Create", mock.Anything, mock.Anything).Return(&organization_settings.OrganizationSetting{
					Organization: models.Organization{
						ID: "********************************",
					}}, nil).Once()
			},
			expCode: http.StatusCreated,
			respCheck: func(t *testing.T, resp string) {
				require.NotEmpty(t, gjson.Parse(resp).Get("data").String())
				require.Equal(t, gjson.Parse(resp).Get("data.organization.id").String(), "********************************")
			},
		},
	}
	for _, tt := range testcases {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mock != nil {
				tt.mock()
			}
			w := httptest.NewRecorder()
			gin.SetMode(gin.TestMode)
			_, eng := gin.CreateTestContext(w)

			NewOrganizationSetting(logger.Get(), mockSvc).RegisterRoutes(eng.RouterGroup.Group(""))

			req, _ := http.NewRequestWithContext(context.Background(), http.MethodPost, tt.path, bytes.NewBuffer(tt.body))
			req.Header.Set("Content-Type", "application/json")

			eng.ServeHTTP(w, req)
			resp := w.Body.String()
			require.Equal(t, tt.expCode, w.Code, resp)

			if tt.respCheck != nil {
				tt.respCheck(t, resp)
			}
		})
	}
}

func TestOrganizationSetting_updateHandler(t *testing.T) {
	configs := new(config.Config)
	_, err := cfg.LoadViperConfig(configs, func(v *viper.Viper) { v.AddConfigPath("../../../../cmd/apiserver/conf") })
	require.NoError(t, err)

	mockSvc := new(organization_settings.MockOrganizationSettingService)

	testcases := []struct {
		name      string
		path      string
		body      []byte
		mock      func()
		expCode   int
		respCheck func(*testing.T, string)
	}{
		{
			name:    "Setting ID Too Short case",
			path:    "/organization-settings/123",
			expCode: http.StatusUnprocessableEntity,
		},
		{
			name:    "Setting ID Too Long case",
			path:    "/organization-settings/" + strings.Repeat("x", 33),
			expCode: http.StatusUnprocessableEntity,
		},
		{
			name: "Service error case",
			path: "/organization-settings/" + strings.Repeat("y", 32),
			mock: func() {
				mockSvc.On("Update", mock.Anything, mock.Anything, mock.Anything).Return(&organization_settings.OrganizationSetting{}, errors.New("create error")).Once()
			},
			expCode: http.StatusInternalServerError,
			body:    []byte(`{}`),
		},
		{
			name: "Good case",
			path: "/organization-settings/" + strings.Repeat("y", 32),
			mock: func() {
				mockSvc.On("Update", mock.Anything, mock.Anything, mock.Anything).Return(&organization_settings.OrganizationSetting{
					Organization: models.Organization{
						ID: "********************************",
					},
				}, nil).Once()
			},
			expCode: http.StatusOK,
			respCheck: func(t *testing.T, resp string) {
				require.NotEmpty(t, gjson.Parse(resp).Get("data").String())
				require.Equal(t, gjson.Parse(resp).Get("data.organization.id").String(), "********************************")
			},
			body: []byte(`{"organization":{"id":"********************************"}}`),
		},
	}
	for _, tt := range testcases {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mock != nil {
				tt.mock()
			}
			w := httptest.NewRecorder()
			gin.SetMode(gin.TestMode)
			_, eng := gin.CreateTestContext(w)

			NewOrganizationSetting(logger.Get(), mockSvc).RegisterRoutes(eng.RouterGroup.Group(""))

			req, _ := http.NewRequestWithContext(context.Background(), http.MethodPut, tt.path, bytes.NewBuffer(tt.body))
			eng.ServeHTTP(w, req)
			resp := w.Body.String()
			require.Equal(t, tt.expCode, w.Code, resp)
			if tt.respCheck != nil {
				tt.respCheck(t, resp)
			}
		})
	}
}

func TestOrganizationSetting_listHandler(t *testing.T) {
	configs := new(config.Config)
	_, err := cfg.LoadViperConfig(configs, func(v *viper.Viper) { v.AddConfigPath("../../../../cmd/apiserver/conf") })
	require.NoError(t, err)

	mockSvc := new(organization_settings.MockOrganizationSettingService)

	testcases := []struct {
		name      string
		path      string
		mock      func()
		args      *organization_settings.SearchOrganizationSettingArgs
		expCode   int
		respCheck func(*testing.T, string)
	}{
		{
			name:    "missing param",
			path:    "/organization-settings",
			args:    &organization_settings.SearchOrganizationSettingArgs{},
			expCode: http.StatusUnprocessableEntity,
		},
		{
			name: "Service error case",
			path: "/organization-settings",
			args: &organization_settings.SearchOrganizationSettingArgs{
				OrganizationID: "11",
			},
			mock: func() {
				mockSvc.On("List", mock.Anything, mock.Anything).Return([]*organization_settings.OrganizationSetting{}, errors.New("list error")).Once()
			},
			expCode: http.StatusInternalServerError,
		},
		{
			name: "Good case",
			path: "/organization-settings",
			args: &organization_settings.SearchOrganizationSettingArgs{
				OrganizationID: "11",
			},
			mock: func() {
				mockSvc.On("List", mock.Anything, mock.Anything).Return([]*organization_settings.OrganizationSetting{}, nil).Once()
			},
			expCode: http.StatusOK,
			respCheck: func(t *testing.T, resp string) {
				require.NotEmpty(t, gjson.Parse(resp).Get("data").String())
			},
		},
	}
	for _, tt := range testcases {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mock != nil {
				tt.mock()
			}
			w := httptest.NewRecorder()
			gin.SetMode(gin.TestMode)
			_, eng := gin.CreateTestContext(w)

			NewOrganizationSetting(logger.Get(), mockSvc).RegisterRoutes(eng.RouterGroup.Group(""))

			path := tt.path
			if tt.args != nil {
				paramsOut, err := json.Marshal(tt.args)
				require.NoError(t, err)

				argsMap := make(map[string]string)
				err = json.Unmarshal(paramsOut, &argsMap)
				require.NoError(t, err)

				totalParams := url.Values{}
				for k, v := range argsMap {
					totalParams.Set(k, v)
				}

				params := totalParams.Encode()
				path += "?" + params
			}

			req, _ := http.NewRequestWithContext(context.Background(), http.MethodGet, path, nil)
			eng.ServeHTTP(w, req)
			resp := w.Body.String()
			require.Equal(t, tt.expCode, w.Code, resp)
			if tt.respCheck != nil {
				tt.respCheck(t, resp)
			}
		})
	}
}
