package product_compliance

import (
	"context"
	"encoding/json"
	"io"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"github.com/tidwall/gjson"

	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/product_compliance"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
)

func Test_handler(t *testing.T) {
	mockService := &product_compliance.MockProductComplianceService{}

	tests := []struct {
		name      string
		method    string
		path      string
		reqBody   interface{}
		mock      func()
		expCode   int
		respCheck func(*testing.T, string)
	}{
		{
			name:      "Case 1: get responsible-persons missing required query param",
			method:    http.MethodGet,
			path:      "/product-compliance/responsible-persons",
			mock:      nil,
			expCode:   422,
			respCheck: func(t *testing.T, resp string) {},
		},
		{
			name:   "Case 2: get responsible-persons Success",
			method: http.MethodGet,
			path:   "/product-compliance/responsible-persons?organization_id=********************************&sales_channel_platform=tiktok-shop&sales_channel_store_key=8106192175294",
			mock: func() {
				mockService.On("GetResponsiblePersons", mock.Anything, mock.Anything).Return(product_compliance.GetResponsiblePersonsOutput{
					Organization: models.Organization{
						ID: "********************************",
					},
					SalesChannel: models.SalesChannel{
						StoreKey: "8106192175294",
						Platform: "tiktok-shop",
					},
					ResponsiblePersons: []product_compliance.ResponsiblePerson{
						{
							SalesChannelID: "t_1",
						},
						{
							SalesChannelID: "t_2",
						},
					},
				}, nil).Once()
			},
			expCode: 200,
			respCheck: func(t *testing.T, resp string) {
				require.NotEmpty(t, gjson.Parse(resp).Get("data").String())
			},
		},
		{
			name:   "Case 3: create responsible-persons Success",
			method: http.MethodPost,
			path:   "/product-compliance/responsible-persons",
			reqBody: createResponsiblePersonReq{
				OrganizationID:       "********************************",
				SalesChannelPlatform: "tiktok-shop",
				SalesChannelStoreKey: "810619217",
				Name:                 "test",
				Email:                "<EMAIL>",
				Phone: models.CompliancePhone{
					CountryCode: "+852",
					Number:      "12345678",
				},
				Address: models.ResponsiblePersonAddress{
					AddressLine1: "test",
					AddressLine2: "test",
					District:     "test",
					City:         "test",
					PostalCode:   "test",
					Province:     "test",
					Country:      "test",
				},
			},
			mock: func() {
				mockService.On("CreateResponsiblePerson", mock.Anything, mock.Anything).Return(product_compliance.CreateResponsiblePersonOutput{
					Organization: models.Organization{
						ID: "********************************",
					},
					SalesChannel: models.SalesChannel{
						StoreKey: "8106192175294",
						Platform: "tiktok-shop",
					},
					ResponsiblePerson: product_compliance.ResponsiblePerson{
						SalesChannelID: "t_1",
					},
				}, nil).Once()
			},
			expCode: 200,
			respCheck: func(t *testing.T, resp string) {
				require.NotEmpty(t, gjson.Parse(resp).Get("data").String())
			},
		},
		{
			name:      "Case 4: get manufacturers missing required query param",
			method:    http.MethodGet,
			path:      "/product-compliance/manufacturers",
			mock:      nil,
			expCode:   422,
			respCheck: func(t *testing.T, resp string) {},
		},
		{
			name:   "Case 5: get manufacturers Success",
			method: http.MethodGet,
			path:   "/product-compliance/manufacturers?organization_id=********************************&sales_channel_platform=tiktok-shop&sales_channel_store_key=8106192175294",
			mock: func() {
				mockService.On("GetManufacturers", mock.Anything, mock.Anything).Return(product_compliance.GetManufacturersOutput{
					Organization: models.Organization{
						ID: "********************************",
					},
					SalesChannel: models.SalesChannel{
						StoreKey: "8106192175294",
						Platform: "tiktok-shop",
					},
					Manufacturers: []product_compliance.Manufacturer{
						{
							SalesChannelID: "t_1",
						},
						{
							SalesChannelID: "t_2",
						},
					},
				}, nil).Once()
			},
			expCode: 200,
			respCheck: func(t *testing.T, resp string) {
				require.NotEmpty(t, gjson.Parse(resp).Get("data").String())
			},
		},
		{
			name:   "Case 6: create manufacturers Success",
			method: http.MethodPost,
			reqBody: createManufacturerReq{
				OrganizationID:       "********************************",
				SalesChannelPlatform: "tiktok-shop",
				SalesChannelStoreKey: "810619217",
				Name:                 "test",
				Email:                "<EMAIL>",
				Phone: models.CompliancePhone{
					CountryCode: "+852",
					Number:      "12345678",
				},
				Address: models.ManufacturerAddress{
					AddressLine1: "test",
				},
			},
			path: "/product-compliance/manufacturers",
			mock: func() {
				mockService.On("CreateManufacturer", mock.Anything, mock.Anything).Return(product_compliance.CreateManufacturerOutput{
					Organization: models.Organization{
						ID: "********************************",
					},
					SalesChannel: models.SalesChannel{
						StoreKey: "8106192175294",
						Platform: "tiktok-shop",
					},
					Manufacturer: product_compliance.Manufacturer{
						SalesChannelID: "t_1",
					},
				}, nil).Once()
			},
			expCode: 200,
			respCheck: func(t *testing.T, resp string) {
				require.NotEmpty(t, gjson.Parse(resp).Get("data").String())
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mock != nil {
				tt.mock()
			}
			w := httptest.NewRecorder()
			gin.SetMode(gin.TestMode)
			_, eng := gin.CreateTestContext(w)
			NewComplianceHandler(nil, mockService).RegisterRoutes(eng.RouterGroup.Group(""))

			var reqBody io.Reader
			if tt.reqBody != "" {
				data, _ := json.Marshal(tt.reqBody)
				reqBody = strings.NewReader(string(data))
			}
			req, _ := http.NewRequestWithContext(context.Background(), tt.method, tt.path, reqBody)
			eng.ServeHTTP(w, req)
			resp := w.Body.String()
			require.Equal(t, tt.expCode, w.Code, resp)
			if tt.respCheck != nil {
				tt.respCheck(t, resp)
			}
		})
	}
}
