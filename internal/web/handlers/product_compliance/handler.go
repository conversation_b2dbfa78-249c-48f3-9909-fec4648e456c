package product_compliance

import (
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"

	tiktok_rest "github.com/AfterShip/connectors-ecommerce-sdk-go/tiktok/rest"
	"github.com/AfterShip/connectors-library/gin/responder"
	"github.com/AfterShip/gopkg/log"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/product_compliance"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
)

type Compliance struct {
	logger    *log.Logger
	service   product_compliance.Service
	responder *responder.Responder
}

func NewComplianceHandler(logger *log.Logger, service product_compliance.Service) *Compliance {
	s := &Compliance{
		logger:    logger,
		service:   service,
		responder: responder.NewResponder(false, models.CommonErrorsMapping),
	}
	s.responder.RegisterDomainError(errorResponseMapping)
	return s
}

func (h *Compliance) RegisterRoutes(router *gin.RouterGroup) {
	apiRouter := router.Group("/product-compliance")

	responsiblePersonsAPIGroup := apiRouter.Group("/responsible-persons")
	{
		responsiblePersonsAPIGroup.GET("", h.searchResponsiblePerson)
		responsiblePersonsAPIGroup.POST("", h.createResponsiblePerson)
	}

	manufacturersAPIGroup := apiRouter.Group("/manufacturers")
	{
		manufacturersAPIGroup.GET("", h.searchManufacturers)
		manufacturersAPIGroup.POST("", h.createManufacturers)
	}

}

func (p *Compliance) searchResponsiblePerson(c *gin.Context) {
	ctx := c.Request.Context()

	var req searchResponsiblePersonsReq
	if err := c.ShouldBindQuery(&req); err != nil {
		p.responder.ResponseWithErrorCode(c, models.CodeMissRequiredQueryParam, err)
		return
	}

	var salesChannelIDs []string
	if req.SalesChannelIDs != "" {
		salesChannelIDs = strings.Split(req.SalesChannelIDs, ",")
	}

	result, err := p.service.GetResponsiblePersons(ctx, &product_compliance.GetResponsiblePersonsArg{
		Organization: models.Organization{
			ID: req.OrganizationID,
		},
		SalesChannel: models.SalesChannel{
			StoreKey: req.SalesChannelStoreKey,
			Platform: req.SalesChannelPlatform,
		},
		SalesChannelIDs: salesChannelIDs,
		Query:           req.Query,
	})
	if err != nil {
		p.responder.ResponseWithError(c, err)
		return
	}

	p.responder.ResponseWithOK(c, result)
}

func (p *Compliance) createResponsiblePerson(c *gin.Context) {
	ctx := c.Request.Context()

	var req createResponsiblePersonReq
	if err := c.ShouldBindJSON(&req); err != nil {
		p.responder.ResponseWithErrorCode(c, models.CodeMissRequiredQueryParam, err)
		return
	}

	result, err := p.service.CreateResponsiblePerson(ctx, &product_compliance.CreateResponsiblePersonArg{
		Organization: models.Organization{
			ID: req.OrganizationID,
		},
		SalesChannel: models.SalesChannel{
			StoreKey: req.SalesChannelStoreKey,
			Platform: req.SalesChannelPlatform,
		},
		Name:    req.Name,
		Email:   req.Email,
		Phone:   req.Phone,
		Address: req.Address,
	})
	if err != nil {
		p.responseCreateComplianceError(c, err)
		return
	}

	p.responder.ResponseWithOK(c, result)
}

func (p *Compliance) searchManufacturers(c *gin.Context) {
	ctx := c.Request.Context()

	var req searchManufacturersReq
	if err := c.ShouldBindQuery(&req); err != nil {
		p.responder.ResponseWithErrorCode(c, models.CodeMissRequiredQueryParam, err)
		return
	}

	var salesChannelIDs []string
	if req.SalesChannelIDs != "" {
		salesChannelIDs = strings.Split(req.SalesChannelIDs, ",")
	}

	result, err := p.service.GetManufacturers(ctx, &product_compliance.GetManufacturersArg{
		Organization: models.Organization{
			ID: req.OrganizationID,
		},
		SalesChannel: models.SalesChannel{
			StoreKey: req.SalesChannelStoreKey,
			Platform: req.SalesChannelPlatform,
		},
		SalesChannelIDs: salesChannelIDs,
		Query:           req.Query,
	})
	if err != nil {
		p.responder.ResponseWithError(c, err)
		return
	}

	p.responder.ResponseWithOK(c, result)
}

func (p *Compliance) createManufacturers(c *gin.Context) {
	ctx := c.Request.Context()

	var req createManufacturerReq
	if err := c.ShouldBindJSON(&req); err != nil {
		p.responder.ResponseWithErrorCode(c, models.CodeMissRequiredQueryParam, err)
		return
	}

	result, err := p.service.CreateManufacturer(ctx, &product_compliance.CreateManufacturerArg{
		Organization: models.Organization{
			ID: req.OrganizationID,
		},
		SalesChannel: models.SalesChannel{
			StoreKey: req.SalesChannelStoreKey,
			Platform: req.SalesChannelPlatform,
		},
		Name:                req.Name,
		RegisteredTradeName: req.RegisteredTradeName,
		Email:               req.Email,
		Phone:               req.Phone,
		Address:             req.Address,
	})
	if err != nil {
		p.responseCreateComplianceError(c, err)
		return
	}

	p.responder.ResponseWithOK(c, result)
}

func (p *Compliance) responseCreateComplianceError(c *gin.Context, err error) {
	// service only support tiktok-shop
	ttsErr := &tiktok_rest.TikTokError{}
	if errors.As(err, &ttsErr) {
		p.responder.ResponseWithErrorCodeAndDesc(c, ChannelCreateComplianceError, responder.Description{
			Type:    "Channel origin err: CreateComplianceError",
			Message: ttsErr.Message.String(),
		}, errors.New(ttsErr.Message.String()))
	} else {
		p.responder.ResponseWithError(c, err)
	}
}
