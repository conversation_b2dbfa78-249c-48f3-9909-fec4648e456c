package product_compliance

import "github.com/AfterShip/pltf-pd-product-listings/internal/models"

type searchResponsiblePersonsReq struct {
	OrganizationID       string `form:"organization_id" binding:"required"`
	SalesChannelPlatform string `form:"sales_channel_platform" binding:"required"`
	SalesChannelStoreKey string `form:"sales_channel_store_key" binding:"required"`
	Query                string `form:"query"`
	SalesChannelIDs      string `form:"sales_channel_ids"`
}

type createResponsiblePersonReq struct {
	OrganizationID       string                          `json:"organization_id" validate:"required"`
	SalesChannelPlatform string                          `json:"sales_channel_platform" validate:"required"`
	SalesChannelStoreKey string                          `json:"sales_channel_store_key" validate:"required"`
	Name                 string                          `json:"name" validate:"required"`
	Email                string                          `json:"email" validate:"required"`
	Phone                models.CompliancePhone          `json:"phone" validate:"required"`
	Address              models.ResponsiblePersonAddress `json:"address" validate:"required"`
}

type searchManufacturersReq struct {
	OrganizationID       string `form:"organization_id" binding:"required"`
	SalesChannelPlatform string `form:"sales_channel_platform" binding:"required"`
	SalesChannelStoreKey string `form:"sales_channel_store_key" binding:"required"`
	Query                string `form:"query"`
	SalesChannelIDs      string `form:"sales_channel_ids"`
}

type createManufacturerReq struct {
	OrganizationID       string                     `json:"organization_id"  validate:"required"`
	SalesChannelPlatform string                     `json:"sales_channel_platform"  validate:"required"`
	SalesChannelStoreKey string                     `json:"sales_channel_store_key"  validate:"required"`
	Name                 string                     `json:"name"  validate:"required"`
	RegisteredTradeName  string                     `json:"registered_trade_name"  validate:"required"`
	Email                string                     `json:"email"  validate:"required"`
	Phone                models.CompliancePhone     `json:"phone"  validate:"required"`
	Address              models.ManufacturerAddress `json:"address"  validate:"required"`
}
