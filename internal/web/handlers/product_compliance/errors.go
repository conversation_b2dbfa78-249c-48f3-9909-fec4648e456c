package product_compliance

import (
	"github.com/AfterShip/connectors-library/gin/responder"
	tiktokapi "github.com/AfterShip/pltf-pd-product-listings/internal/third_party/tiktok_api"
)

var (
	AddressInvalidEUCountry      responder.MetaCode = 41202
	ChannelCreateComplianceError responder.MetaCode = 41203
)

var errorResponseMapping = map[error]responder.Meta{
	tiktokapi.ErrInvalidEUCountry: {
		Code: AddressInvalidEUCountry,
		Description: responder.Description{
			Type:    "AddressInvalidEUCountry",
			Message: "address country is not in EU",
		},
	},
}
