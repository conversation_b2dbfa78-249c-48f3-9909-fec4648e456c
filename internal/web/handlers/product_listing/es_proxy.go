package product_listing

import (
	"encoding/base64"

	"github.com/gin-gonic/gin"
)

type esProxyProductListingReq struct {
	Query string `form:"query" binding:"required"`
}

// esProxyProductListingCount Count of product listings from ES, internal use only
func (p *ProductListing) esProxyProductListingCount(c *gin.Context) {
	var req esProxyProductListingReq
	if err := c.ShouldBind(&req); err != nil {
		p.responder.ResponseWithError(c, err)
		return
	}
	query, err := base64.StdEncoding.DecodeString(req.Query)
	if err != nil {
		p.responder.ResponseWithError(c, err)
		return
	}

	count, err := p.service.ESProxyProductListingCount(c.Request.Context(), string(query))
	if err != nil {
		p.responder.ResponseWithError(c, err)
		return
	}

	p.responder.ResponseWithOK(c, gin.H{"count": count})
}

func (p *ProductListing) esProxyProductListingSearch(c *gin.Context) {
	var req esProxyProductListingReq
	if err := c.ShouldBind(&req); err != nil {
		p.responder.ResponseWithError(c, err)
		return
	}
	query, err := base64.StdEncoding.DecodeString(req.Query)
	if err != nil {
		p.responder.ResponseWithError(c, err)
		return
	}

	listings, err := p.service.ESProxyProductListingSearch(c.Request.Context(), string(query))
	if err != nil {
		p.responder.ResponseWithError(c, err)
		return
	}

	p.responder.ResponseWithOK(c, gin.H{"listings": listings})
}
