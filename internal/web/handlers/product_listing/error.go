package product_listing

import (
	"errors"

	"github.com/AfterShip/connectors-library/gin/responder"
	product_listing_domain "github.com/AfterShip/pltf-pd-product-listings/internal/domains/product_listing"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
	tiktokapi "github.com/AfterShip/pltf-pd-product-listings/internal/third_party/tiktok_api"
)

var (
	errorIDsMaxLimit = errors.New("ids params limit 1000")
	ackError         = errors.New("worker should ack to pubsub")
)

const (
	channelTokenExpired responder.MetaCode = 40101

	notFound                          responder.MetaCode = 40401
	productsCenterProductNotFound     responder.MetaCode = 40402
	productsCenterVariantIDNotFound   responder.MetaCode = 40403
	connectionIsNotFound              responder.MetaCode = 40404
	productNotFound                   responder.MetaCode = 40405
	settingNotFound                   responder.MetaCode = 40406
	noVariant                         responder.MetaCode = 40407
	noProductsCenterVariantID         responder.MetaCode = 40408
	calculateVariantPriceNotFound     responder.MetaCode = 40409
	calculateVariantInventoryNotFound responder.MetaCode = 40410
)

const (
	missPaginationParams        responder.MetaCode = 42201
	missRequiredParam           responder.MetaCode = 42202
	missRequiredQueryParam      responder.MetaCode = 42203
	missRequiredPathParam       responder.MetaCode = 42204
	missRequiredBodyParam       responder.MetaCode = 42205
	missingRequiredField        responder.MetaCode = 42206
	unprocessableEntity         responder.MetaCode = 42207
	missingProductListingID     responder.MetaCode = 42208
	missingProductListing       responder.MetaCode = 42209
	productCategoryInvalid      responder.MetaCode = 42210
	productCategoryNoPermission responder.MetaCode = 42212 // jump 12, same rule api
)

const (
	errorIDsMaxLimitCode          responder.MetaCode = 42221
	ackErrorCode                  responder.MetaCode = 42222
	noValidVariantsToAutoLink     responder.MetaCode = 42223
	autoLinkIsDisabled            responder.MetaCode = 42224
	toManyRequest                 responder.MetaCode = 42225
	eSBatchUpsertErrorCode        responder.MetaCode = 42226
	eSBatchDeleteErrorCode        responder.MetaCode = 42227
	updatePublicationByRefID      responder.MetaCode = 42228
	invalidPublishState           responder.MetaCode = 42229
	productsCenterProductIsPushed responder.MetaCode = 42230
	listingProductCategoryNotSet  responder.MetaCode = 42231
	notLiveProduct                responder.MetaCode = 42232
	versionConflict               responder.MetaCode = 42233
	salesChannelProductConflict   responder.MetaCode = 42234
	stateInDelete                 responder.MetaCode = 42235
	sameAuditVersion              responder.MetaCode = 42236
	notMatchedAndLinkedProduct    responder.MetaCode = 42237
	productListingNotAllowLink    responder.MetaCode = 42238
	commonErrorCode               responder.MetaCode = 42299
)

const (
	productListingStatusConflict       responder.MetaCode = 40901
	productListingAlreadyLinked        responder.MetaCode = 40902
	productListingStatusConflictNotAck responder.MetaCode = 40903
)

var errorResponseMapping = map[error]responder.Meta{
	errorIDsMaxLimit: {
		Code: errorIDsMaxLimitCode,
		Description: responder.Description{
			Type:    "IDsMaxLimit",
			Message: "ids params limit 1000",
		},
	},
	ackError: {
		Code: ackErrorCode,
		Description: responder.Description{
			Type:    "AckError",
			Message: "worker should ack to pubsub",
		},
	},
	models.ErrMissRequiredPathParam: {
		Code: missRequiredPathParam,
		Description: responder.Description{
			Type:    "MissRequiredPathParam",
			Message: "missing required path parameters.",
		},
	},
	models.ErrMissRequiredQueryParam: {
		Code: missRequiredQueryParam,
		Description: responder.Description{
			Type:    "MissRequiredQueryParam",
			Message: "missing required query parameters.",
		},
	},
	models.ErrMissRequiredBodyParam: {
		Code: missRequiredBodyParam,
		Description: responder.Description{
			Type: "UnprocessableEntity",
			Message: "The request body was well-formed but contains semantical errors. " +
				"The response body will provide more details in the errors or error parameters.",
		},
	},
	models.ErrMissingRequiredField: {
		Code: missingRequiredField,
		Description: responder.Description{
			Type:    "MissingRequiredField",
			Message: "Missing required filed in handler process.",
		},
	},
	product_listing_domain.ErrPublishStateInUpdate: {
		Code: productListingStatusConflict,
		Description: responder.Description{
			Type:    "ProductListingStatusConflict",
			Message: "The status of this listing has changed. You’ll need to discard your changes and edit again.",
		},
	},
	product_listing_domain.ErrPublishStateInUpdateNotAck: {
		Code: productListingStatusConflictNotAck,
		Description: responder.Description{
			Type:    "ProductListingStatusConflictNotAck",
			Message: "The status of this listing is in running, and pub/sub message will be retry.",
		},
	},
	product_listing_domain.ErrProductsCenterProductNotFound: {
		Code: productsCenterProductNotFound,
		Description: responder.Description{
			Type:    "ProductsCenterProductNotFound",
			Message: "products center product not found",
		},
	},
	product_listing_domain.ErrAlreadyLinked: {
		Code: productListingAlreadyLinked,
		Description: responder.Description{
			Type:    "ProductListingAlreadyLinked",
			Message: "product listing already linked",
		},
	},
	product_listing_domain.ErrNoProductsCenterVariantID: {
		Code: productsCenterVariantIDNotFound,
		Description: responder.Description{
			Type:    "ProductsCenterVariantNotFound",
			Message: "products center variant not found",
		},
	},
	product_listing_domain.ErrNoValidVariantsToAutoLink: {
		Code: noValidVariantsToAutoLink,
		Description: responder.Description{
			Type:    "NoValidVariantsToAutoLink",
			Message: "no valid variants to auto link",
		},
	},
	product_listing_domain.ErrBothConnectionIsNotFound: {
		Code: connectionIsNotFound,
		Description: responder.Description{
			Type:    "BothConnectionIsNotFound",
			Message: "both connection is not found",
		},
	},
	product_listing_domain.ErrAutoLinkIsDisabled: {
		Code: autoLinkIsDisabled,
		Description: responder.Description{
			Type:    "AutoLinkIsDisabled",
			Message: "auto link is disabled",
		},
	},
	product_listing_domain.ErrNotFound: {
		Code: notFound,
		Description: responder.Description{
			Type:    "ProductListingNotFound",
			Message: "product listing not found",
		},
	},
	product_listing_domain.ErrEmptyPaginationParams: {
		Code: missPaginationParams,
		Description: responder.Description{
			Type:    "EmptyPaginationParams",
			Message: "page and cursor should not both empty",
		},
	},
	product_listing_domain.ErrTooManyRequest: {
		Code: toManyRequest,
		Description: responder.Description{
			Type:    "TooManyRequest",
			Message: "ES server reject request due to too many request",
		},
	},
	product_listing_domain.ErrUnprocessableEntity: {
		Code: unprocessableEntity,
		Description: responder.Description{
			Type:    "UnprocessableEntity",
			Message: "invalid input parameters",
		},
	},
	product_listing_domain.ErrProductNotFound: {
		Code: productNotFound,
		Description: responder.Description{
			Type:    "ProductNotFound",
			Message: "product not found",
		},
	},
	product_listing_domain.ErrESBatchUpsert: {
		Code: eSBatchUpsertErrorCode,
		Description: responder.Description{
			Type:    "ESBatchUpsert",
			Message: "failed to batch upsert to ES",
		},
	},
	product_listing_domain.ErrESBatchDelete: {
		Code: eSBatchDeleteErrorCode,
		Description: responder.Description{
			Type:    "ESBatchDelete",
			Message: "failed to batch delete from ES",
		},
	},
	product_listing_domain.ErrUpdatePublicationByRefID: {
		Code: updatePublicationByRefID,
		Description: responder.Description{
			Type:    "UpdatePublicationByRefID",
			Message: "the publish still in running it can not update by the reference id",
		},
	},
	product_listing_domain.ErrInvalidPublishState: {
		Code: invalidPublishState,
		Description: responder.Description{
			Type:    "InvalidPublishState",
			Message: "invalid publish state",
		},
	},
	product_listing_domain.ErrProductsCenterProductIsPushed: {
		Code: productsCenterProductIsPushed,
		Description: responder.Description{
			Type:    "ProductsCenterProductIsPushed",
			Message: "products center product is pushed",
		},
	},
	product_listing_domain.ErrSettingNotFound: {
		Code: settingNotFound,
		Description: responder.Description{
			Type:    "SettingNotFound",
			Message: "setting resource not found",
		},
	},
	product_listing_domain.ErrListingProductCategoryNotSet: {
		Code: listingProductCategoryNotSet,
		Description: responder.Description{
			Type:    "ListingProductCategoryNotSet",
			Message: "listing product category not set",
		},
	},
	product_listing_domain.ErrNotLiveProduct: {
		Code: notLiveProduct,
		Description: responder.Description{
			Type:    "NotLiveProduct",
			Message: "product listing in sales channel state is not live",
		},
	},
	product_listing_domain.ErrNoVariant: {
		Code: noVariant,
		Description: responder.Description{
			Type:    "NoVariant",
			Message: "no variant",
		},
	},
	product_listing_domain.ErrNoProductsCenterVariantID: {
		Code: noProductsCenterVariantID,
		Description: responder.Description{
			Type:    "NoProductsCenterVariantID",
			Message: "no products center variant ID",
		},
	},
	product_listing_domain.ErrVersionConflict: {
		Code: versionConflict,
		Description: responder.Description{
			Type:    "VersionConflict",
			Message: "version conflict",
		},
	},
	product_listing_domain.ErrSalesChannelProductConflict: {
		Code: salesChannelProductConflict,
		Description: responder.Description{
			Type:    "SalesChannelProductConflict",
			Message: "sales channel product conflict",
		},
	},
	product_listing_domain.ErrStateInDelete: {
		Code: stateInDelete,
		Description: responder.Description{
			Type:    "StateInDelete",
			Message: "product listing state can not delete",
		},
	},
	product_listing_domain.ErrSameAuditVersion: {
		Code: sameAuditVersion,
		Description: responder.Description{
			Type:    "SameAuditVersion",
			Message: "the audit version is the same as the current version",
		},
	},
	product_listing_domain.ErrNotMatchedAndLinkedProduct: {
		Code: notMatchedAndLinkedProduct,
		Description: responder.Description{
			Type:    "NotMatchedAndLinkedProduct",
			Message: "product listing has not been matched and linked",
		},
	},
	product_listing_domain.ErrProductListingNotAllowLink: {
		Code: productListingNotAllowLink,
		Description: responder.Description{
			Type:    "ProductListingNotAllowLink",
			Message: "product listing not allow link",
		},
	},
	product_listing_domain.ErrNoProductListingID: {
		Code: missingProductListingID,
		Description: responder.Description{
			Type:    "NoProductListingID",
			Message: "no product listing ID",
		},
	},
	product_listing_domain.ErrProductListingIsEmpty: {
		Code: missingProductListing,
		Description: responder.Description{
			Type:    "ProductListingIsEmpty",
			Message: "product listing is empty",
		},
	},
	product_listing_domain.ErrCalculateVariantPriceNotFound: {
		Code: calculateVariantPriceNotFound,
		Description: responder.Description{
			Type:    "CalculateVariantPriceNotFound",
			Message: "calculate variant price not found",
		},
	},
	product_listing_domain.ErrCalculateVariantInventoryNotFound: {
		Code: calculateVariantInventoryNotFound,
		Description: responder.Description{
			Type:    "CalculateVariantInventoryNotFound",
			Message: "calculate variant inventory not found",
		},
	},
	tiktokapi.ErrCategoryVersionNotMatch: {
		Code: productCategoryInvalid,
		Description: responder.Description{
			Type:    "CategoryVersionNotMatch",
			Message: "category version not match",
		},
	},
	tiktokapi.ErrCategoryInviteOnly: {
		Code: productCategoryNoPermission,
		Description: responder.Description{
			Type:    "CategoryNoPermission",
			Message: "no permission to access category",
		},
	},
	tiktokapi.ErrCategoryNotAvailableInRegion: {
		Code: productCategoryNoPermission,
		Description: responder.Description{
			Type:    "CategoryNotAvailableInRegion",
			Message: "category not available in this region",
		},
	},
	tiktokapi.ErrAuthorizationExpired: {
		Code: channelTokenExpired,
		Description: responder.Description{
			Type:    "ChannelTokenExpired",
			Message: "channel token expired, please re-authenticate",
		},
	},
}
