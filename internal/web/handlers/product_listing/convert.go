package product_listing

import (
	product_listing_domain "github.com/AfterShip/pltf-pd-product-listings/internal/domains/product_listing"
)

func convertToProductListingResponse(pl *product_listing_domain.ProductListing) productListingResponse {
	response := productListingResponse{}
	response.ID = pl.ID
	response.State = pl.State
	response.LinkStatus = pl.LinkStatus
	response.SyncStatus = pl.SyncStatus
	response.SalesChannel = pl.SalesChannel
	response.Organization = pl.Organization
	response.Product = pl.Product
	response.SalesChannelProduct = convertToSalesChannelProduct(&pl.SalesChannelProduct)
	response.ProductsCenterProduct = convertToProductsCenterProduct(&pl.ProductsCenterProduct)
	response.Ready = convertToReady(&pl.Ready)
	response.Audit = convertToAudit(&pl.Audit)
	response.Publish = convertToPublish(&pl.Publish)
	response.Settings = convertToSettings(&pl.Settings)
	response.FeedCustomizationParams = convertToFeedCustomizationParams(&pl.FeedCustomizationParams)
	response.PendingDeletedAt = pl.PendingDeletedAt
	response.CreatedAt = pl.CreatedAt
	response.UpdatedAt = pl.UpdatedAt
	response.Version = pl.Version
	relations := make([]*productListingRelation, 0, len(pl.Relations))
	for _, relation := range pl.Relations {
		r := convertToRelationResp(relation)
		relations = append(relations, &r)
	}
	response.Relations = relations
	return response
}

func convertToFeedCustomizationParams(f *product_listing_domain.FeedCustomizationParams) feedCustomizationParams {
	return feedCustomizationParams{
		FeedCategoryTemplateID: f.FeedCategoryTemplateID,
	}
}

func convertToSalesChannelProduct(p *product_listing_domain.SalesChannelProduct) salesChannelProduct {
	response := salesChannelProduct{}
	response.ID = p.ID
	response.ConnectorProductID = p.ConnectorProductID
	response.State = p.State
	response.Metrics = salesChannelProductMetrics{
		CreatedAt: p.Metrics.CreatedAt,
		UpdatedAt: p.Metrics.UpdatedAt,
	}
	return response
}

func convertToProductsCenterProduct(p *product_listing_domain.ProductsCenterProduct) productsCenterProduct {
	response := productsCenterProduct{}
	response.ID = p.ID
	response.ConnectorProductID = p.ConnectorProductID
	response.PublishState = p.PublishState
	response.Source = productsCenterProductSource{
		StoreKey: p.Source.StoreKey,
		Platform: p.Source.Platform,
		ID:       p.Source.ID,
	}
	return response
}

func convertToReady(r *product_listing_domain.Ready) ready {
	response := ready{}
	response.Status = r.Status
	response.LastFailedAt = r.LastFailedAt
	response.FailedReasons = make([]readyFailedReason, 0, len(r.FailedReasons))
	for _, reason := range r.FailedReasons {
		response.FailedReasons = append(response.FailedReasons, readyFailedReason{
			Position:    reason.Position,
			Reasons:     reason.Reasons,
			ErrorCodes:  reason.ErrorCodes,
			Suggestions: reason.Suggestions,
		})
	}
	return response
}

func convertToAudit(a *product_listing_domain.Audit) audit {
	response := audit{}
	response.State = a.State
	response.LastFailedAt = a.LastFailedAt
	response.FailedReasons = make([]auditFailedReason, 0, len(a.FailedReasons))
	for _, reason := range a.FailedReasons {
		response.FailedReasons = append(response.FailedReasons, auditFailedReason{
			Position:    reason.Position,
			Reasons:     reason.Reasons,
			Suggestions: reason.Suggestions,
		})
	}
	return response
}

func convertToPublish(p *product_listing_domain.Publish) publish {
	response := publish{}
	response.LastReferenceID = p.LastReferenceID
	response.State = p.State
	response.LastFailedAt = p.LastFailedAt
	response.Error = publishError{
		Code: p.Error.Code,
		Msg:  p.Error.Msg,
	}
	return response
}

func convertToSettings(s *product_listing_domain.SyncSettings) syncSettings {
	response := syncSettings{}
	response.InventorySyncSetting = inventorySyncSetting{
		Preference:   s.InventorySyncSetting.Preference,
		Customized:   s.InventorySyncSetting.Customized,
		LastEffectAt: s.InventorySyncSetting.LastEffectAt,
	}
	response.PriceSyncSetting = priceSyncSetting{
		Preference:   s.PriceSyncSetting.Preference,
		Customized:   s.PriceSyncSetting.Customized,
		LastEffectAt: s.PriceSyncSetting.LastEffectAt,
	}
	response.ProductSyncSetting = productSyncSetting{
		Preference:   s.ProductSyncSetting.Preference,
		Customized:   s.ProductSyncSetting.Customized,
		LastEffectAt: s.ProductSyncSetting.LastEffectAt,
	}
	return response
}

func convertToRelationResp(r *product_listing_domain.ProductListingRelation) productListingRelation {
	response := productListingRelation{}
	response.ID = r.ID
	response.VariantPosition = r.VariantPosition
	response.SalesChannel = r.SalesChannel
	response.ProductListingVariantID = r.ProductListingVariantID
	response.SyncStatus = r.SyncStatus
	response.LinkStatus = r.LinkStatus
	response.AllowSync = r.AllowSync
	response.LastSyncedAt = r.LastSyncedAt
	response.LastLinkedAt = r.LastLinkedAt
	response.DeletedAt = r.DeletedAt
	response.CreatedAt = r.CreatedAt
	response.UpdatedAt = r.UpdatedAt
	response.SalesChannelVariant = convertToSalesChannelVariant(&r.SalesChannelVariant)
	response.ProductsCenterVariant = convertToProductsCenterVariant(&r.ProductsCenterVariant)
	return response
}

func convertToSalesChannelVariant(v *product_listing_domain.SalesChannelVariant) salesChannelVariant {
	response := salesChannelVariant{}
	response.ID = v.ID
	response.ConnectorProductID = v.ConnectorProductID
	response.ProductID = v.ProductID
	response.Sku = v.Sku
	return response
}

func convertToProductsCenterVariant(v *product_listing_domain.ProductsCenterVariant) productsCenterVariant {
	response := productsCenterVariant{}
	response.ID = v.ID
	response.ProductID = v.ProductID
	response.ConnectorProductID = v.ConnectorProductID
	response.Source = productsCenterVariantSource{
		StoreKey:  v.Source.StoreKey,
		Platform:  v.Source.Platform,
		ID:        v.Source.ID,
		ProductID: v.Source.ProductID,
		Sku:       v.Source.Sku,
	}
	return response
}

func convertToDomainPublishArgs(p *publish) product_listing_domain.Publish {
	return product_listing_domain.Publish{
		State:           p.State,
		LastReferenceID: p.LastReferenceID,
		LastFailedAt:    p.LastFailedAt,
		Error: product_listing_domain.PublishError{
			Code: p.Error.Code,
			Msg:  p.Error.Msg,
		},
	}
}

func convertToDomainAuditArgs(a *audit) product_listing_domain.Audit {
	result := product_listing_domain.Audit{
		State:        a.State,
		LastFailedAt: a.LastFailedAt,
	}
	result.FailedReasons = make([]product_listing_domain.AuditFailedReason, 0, len(a.FailedReasons))
	for _, reason := range a.FailedReasons {
		result.FailedReasons = append(result.FailedReasons, product_listing_domain.AuditFailedReason{
			Position:    reason.Position,
			Reasons:     reason.Reasons,
			Suggestions: reason.Suggestions,
		})
	}
	return result
}

func convertToDomainAudit(a *audit) product_listing_domain.Audit {
	result := product_listing_domain.Audit{
		State:        a.State,
		LastFailedAt: a.LastFailedAt,
	}
	result.FailedReasons = make([]product_listing_domain.AuditFailedReason, 0, len(a.FailedReasons))
	for _, reason := range a.FailedReasons {
		result.FailedReasons = append(result.FailedReasons, product_listing_domain.AuditFailedReason{
			Position:    reason.Position,
			Reasons:     reason.Reasons,
			Suggestions: reason.Suggestions,
		})
	}
	return result
}

func convertToDomainSalesChannelProduct(p *salesChannelProduct) product_listing_domain.SalesChannelProduct {
	return product_listing_domain.SalesChannelProduct{
		ID:                 p.ID,
		ConnectorProductID: p.ConnectorProductID,
		State:              p.State,
		Metrics: product_listing_domain.SalesChannelProductMetrics{
			CreatedAt: p.Metrics.CreatedAt,
			UpdatedAt: p.Metrics.UpdatedAt,
		},
	}
}

func convertToDomainProductsCenterProduct(p *productsCenterProduct) product_listing_domain.ProductsCenterProduct {
	return product_listing_domain.ProductsCenterProduct{
		ID:                 p.ID,
		ConnectorProductID: p.ConnectorProductID,
		PublishState:       p.PublishState,
		Source: product_listing_domain.ProductsCenterProductSource{
			StoreKey: p.Source.StoreKey,
			Platform: p.Source.Platform,
			ID:       p.Source.ID,
		},
		UpdatedAt: p.UpdatedAt,
	}
}

func convertToDomainSyncSettings(s *syncSettings) product_listing_domain.SyncSettings {
	return product_listing_domain.SyncSettings{
		InventorySyncSetting: product_listing_domain.InventorySyncSetting{
			Preference:   s.InventorySyncSetting.Preference,
			Customized:   s.InventorySyncSetting.Customized,
			LastEffectAt: s.InventorySyncSetting.LastEffectAt,
		},
		PriceSyncSetting: product_listing_domain.PriceSyncSetting{
			Preference:   s.PriceSyncSetting.Preference,
			Customized:   s.PriceSyncSetting.Customized,
			LastEffectAt: s.PriceSyncSetting.LastEffectAt,
		},
		ProductSyncSetting: product_listing_domain.ProductSyncSetting{
			Preference:   s.ProductSyncSetting.Preference,
			Customized:   s.ProductSyncSetting.Customized,
			LastEffectAt: s.ProductSyncSetting.LastEffectAt,
		},
	}
}

func convertToDomainFeedCustomizationParams(f *feedCustomizationParams) product_listing_domain.FeedCustomizationParams {
	return product_listing_domain.FeedCustomizationParams{
		FeedCategoryTemplateID: f.FeedCategoryTemplateID,
	}
}

func convertToDomainSalesChannelVariant(v *salesChannelVariant) product_listing_domain.SalesChannelVariant {
	return product_listing_domain.SalesChannelVariant{
		ID:                 v.ID,
		ConnectorProductID: v.ConnectorProductID,
		ProductID:          v.ProductID,
		Sku:                v.Sku,
	}
}

func convertToDomainProductsCenterVariant(v *productsCenterVariant) product_listing_domain.ProductsCenterVariant {
	return product_listing_domain.ProductsCenterVariant{
		ID:                 v.ID,
		ProductID:          v.ProductID,
		ConnectorProductID: v.ConnectorProductID,
		Source: product_listing_domain.ProductsCenterVariantSource{
			StoreKey:  v.Source.StoreKey,
			Platform:  v.Source.Platform,
			ID:        v.Source.ID,
			ProductID: v.Source.ProductID,
			Sku:       v.Source.Sku,
		},
	}
}

func convertToDomainReady(r *ready) product_listing_domain.Ready {
	result := product_listing_domain.Ready{
		Status:       r.Status,
		LastFailedAt: r.LastFailedAt,
	}
	result.FailedReasons = make([]product_listing_domain.ReadyFailedReason, 0, len(r.FailedReasons))
	for _, reason := range r.FailedReasons {
		result.FailedReasons = append(result.FailedReasons, product_listing_domain.ReadyFailedReason{
			Position:    reason.Position,
			Reasons:     reason.Reasons,
			Suggestions: reason.Suggestions,
		})
	}
	return result
}
