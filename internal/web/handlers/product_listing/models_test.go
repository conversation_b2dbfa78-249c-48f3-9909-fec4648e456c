package product_listing

import (
	"reflect"
	"testing"

	"github.com/AfterShip/pltf-pd-product-listings/internal/consts"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
)

func TestSearchProductListingRequest_ToSearchProductListingArgs(t *testing.T) {
	req := &SearchProductListingRequest{
		OrganizationID:           "org1",
		SalesChannelStoreKey:     "store1",
		SalesChannelPlatform:     "platform1",
		State:                    consts.ProductListingProductStatePending,
		States:                   "ready,failed",
		PredefinedFilters:        "f1,f2",
		SyncStatusFilters:        "s1,s2",
		LinkStatusFilters:        "l1,l2",
		InventoryFilters:         "i1,i2",
		Categories:               "c1,c2",
		SalesChannelProductIDs:   "scp1,scp2",
		ProductsCenterProductIds: "pcp1,pcp2",
		FeedCategoryTemplateID:   "fctid",
		SKUs:                     "sku1,sku2",
		Page:                     2,
		Limit:                    10,
		Cursor:                   "cursor1",
		IDs:                      "id1,id2",
		Query:                    "q1",
	}
	args := req.ToSearchProductListingArgs()
	if !reflect.DeepEqual(args.States, []string{"ready", "failed"}) {
		t.Errorf("States priority failed: got %v", args.States)
	}
	if !reflect.DeepEqual(args.PredefinedFilters, []string{"f1", "f2"}) {
		t.Errorf("PredefinedFilters failed: got %v", args.PredefinedFilters)
	}
	if !reflect.DeepEqual(args.SyncStatusFilters, []string{"s1", "s2"}) {
		t.Errorf("SyncStatusFilters failed: got %v", args.SyncStatusFilters)
	}
	if !reflect.DeepEqual(args.LinkStatusFilters, []string{"l1", "l2"}) {
		t.Errorf("LinkStatusFilters failed: got %v", args.LinkStatusFilters)
	}
	if !reflect.DeepEqual(args.Categories, []string{"c1", "c2"}) {
		t.Errorf("Categories failed: got %v", args.Categories)
	}
	if !reflect.DeepEqual(args.SalesChannelProductIds, []string{"scp1", "scp2"}) {
		t.Errorf("SalesChannelProductIds failed: got %v", args.SalesChannelProductIds)
	}
	if !reflect.DeepEqual(args.ProductsCenterProductIds, []string{"pcp1", "pcp2"}) {
		t.Errorf("ProductsCenterProductIds failed: got %v", args.ProductsCenterProductIds)
	}
	if args.FeedCategoryTemplateID != "fctid" {
		t.Errorf("FeedCategoryTemplateID failed: got %v", args.FeedCategoryTemplateID)
	}
	if args.Limit != 10 {
		t.Errorf("Limit failed: got %v", args.Limit)
	}
	if !reflect.DeepEqual(args.IDs, []string{"id1", "id2"}) {
		t.Errorf("IDs failed: got %v", args.IDs)
	}
	if !reflect.DeepEqual(args.SKUs, []string{"sku1", "sku2"}) {
		t.Errorf("SKUs failed: got %v", args.SKUs)
	}
	if args.Query != "q1" {
		t.Errorf("Query failed: got %v", args.Query)
	}
	if args.Page != 2 {
		t.Errorf("Page failed: got %v", args.Page)
	}
	if args.Cursor != "cursor1" {
		t.Errorf("Cursor failed: got %v", args.Cursor)
	}

	// Test default limit
	req2 := &SearchProductListingRequest{}
	args2 := req2.ToSearchProductListingArgs()
	if args2.Limit != consts.DefaultLimit {
		t.Errorf("Default limit failed: got %v", args2.Limit)
	}
}

func TestUpdateRequest_convertToDomainUpdateArgs(t *testing.T) {
	req := &updateRequest{
		ID:                    "id1",
		ProductsCenterProduct: productsCenterProduct{ID: "pcp1"},
		Settings:              syncSettings{},
		Product:               models.Product{Title: "p1"},
		Relations:             []*updateRelationRequest{{ID: "rel1", VariantPosition: 1, ProductListingVariantID: "plv1", ProductsCenterVariant: productsCenterVariant{ID: "pcv1"}, AllowSync: consts.AllowSyncEnabled}},
		NeedPublish:           true,
	}
	args := req.convertToDomainUpdateArgs()
	if args.Product.Title != "p1" {
		t.Errorf("Product title failed: got %v", args.Product.Title)
	}
	if args.ProductsCenterProduct.ID != "pcp1" {
		t.Errorf("ProductsCenterProduct ID failed: got %v", args.ProductsCenterProduct.ID)
	}
	if len(args.Relations) != 1 || args.Relations[0].ID != "rel1" {
		t.Errorf("Relations failed: got %+v", args.Relations)
	}
	if !args.NeedPublish {
		t.Errorf("NeedPublish failed: got %v", args.NeedPublish)
	}
}

func TestUpdateRelationRequest_convertToDomainRelation(t *testing.T) {
	req := &updateRelationRequest{
		ID: "rel1", VariantPosition: 2, ProductListingVariantID: "plv1", ProductsCenterVariant: productsCenterVariant{ID: "pcv1"}, AllowSync: consts.AllowSyncEnabled,
	}
	domain := req.convertToDomainRelation()
	if domain.ID != "rel1" || domain.VariantPosition != 2 || domain.ProductListingVariantID != "plv1" || domain.ProductsCenterVariant.ID != "pcv1" || domain.AllowSync != consts.AllowSyncEnabled {
		t.Errorf("convertToDomainRelation failed: got %+v", domain)
	}
}

func TestListAuditVersionsArg_convertToDomainArg(t *testing.T) {
	arg := &listAuditVersionsArg{ProductListingID: "plid1", Page: 2, Limit: 20}
	domain := arg.convertToDomainArg()
	if domain.ProductListingID != "plid1" || domain.Page != 2 || domain.Limit != 20 {
		t.Errorf("convertToDomainArg failed: got %+v", domain)
	}
}

func TestEditAttributesRequest_convertToDomainEditAttributesArgs(t *testing.T) {
	req := &editAttributesRequest{
		CategorySourceID: "cat1",
		SizeChart:        models.ProductSizeChart{Attributes: []models.ProductSizeChartAttribute{{SalesChannelID: "size1"}}},
		Certifications:   []*models.ProductCertification{{SalesChannelID: "cert1"}},
		Attributes:       []*models.ProductAttribute{{SalesChannelID: "attr1"}},
		Length:           models.ProductVariantShippingSetting{Unit: "cm", Value: 1},
		Width:            models.ProductVariantShippingSetting{Unit: "cm", Value: 2},
		Height:           models.ProductVariantShippingSetting{Unit: "cm", Value: 3},
		Weight:           models.ProductVariantShippingSetting{Unit: "kg", Value: 4},
		Brand:            models.SalesChannelResource{SalesChannelID: "brand1"},
		Compliance:       models.ProductCompliance{Manufacturers: []models.ProductComplianceManufacturer{{SalesChannelID: "manu1"}}},
	}
	args := req.convertToDomainEditAttributesArgs()
	if args.CategorySourceID != "cat1" || len(args.Certifications) != 1 || args.Certifications[0].SalesChannelID != "cert1" || len(args.Attributes) != 1 || args.Attributes[0].SalesChannelID != "attr1" || args.Length.Unit != "cm" || args.Length.Value != 1 || args.Width.Value != 2 || args.Height.Value != 3 || args.Weight.Unit != "kg" || args.Brand.SalesChannelID != "brand1" || len(args.Compliance.Manufacturers) != 1 {
		t.Errorf("convertToDomainEditAttributesArgs failed: got %+v", args)
	}
}

func TestLinkRequest_convertToLinkArgs(t *testing.T) {
	req := &linkRequest{
		LinkVariants: []linkVariant{{ProductListingVariantID: "plv1", ProductsCenterProductID: "pcp1", ProductsCenterProductVariantID: "pcpv1"}},
	}
	args := req.convertToLinkArgs("plid1")
	if args.ProductListingID != "plid1" || len(args.LinkedVariants) != 1 || args.LinkedVariants[0].ProductListingVariantID != "plv1" || args.LinkedVariants[0].ProductsCenterProductID != "pcp1" || args.LinkedVariants[0].ProductsCenterProductVariantID != "pcpv1" {
		t.Errorf("convertToLinkArgs failed: got %+v", args)
	}
}
