package product_listing

import (
	"strings"
	"time"

	"github.com/AfterShip/gopkg/facility/types"

	"github.com/AfterShip/pltf-pd-product-listings/internal/consts"
	product_listing_domain "github.com/AfterShip/pltf-pd-product-listings/internal/domains/product_listing"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
)

type SearchProductListingRequest struct {
	OrganizationID       string `form:"organization_id"`
	SalesChannelStoreKey string `form:"sales_channel_store_key"`
	SalesChannelPlatform string `form:"sales_channel_platform"`
	// State deprecated use states
	State                    consts.ProductListingProductState `form:"state"`
	States                   string                            `form:"states"`
	PredefinedFilters        string                            `form:"predefined_filters"`
	SyncStatusFilters        string                            `form:"sync_status_filters"`
	LinkStatusFilters        string                            `form:"link_status_filters"`
	InventoryFilters         string                            `form:"inventory_filters"`
	Categories               string                            `form:"categories"`
	SalesChannelProductIDs   string                            `form:"sales_channel_product_ids"`
	ProductsCenterProductIds string                            `form:"products_center_product_ids"`
	FeedCategoryTemplateID   string                            `form:"feed_category_template_id"`
	SKUs                     string                            `form:"skus"`
	Query                    string                            `form:"query"`
	Page                     int64                             `form:"page"`
	Limit                    int64                             `form:"limit"`
	Cursor                   string                            `form:"cursor"`
	IDs                      string                            `form:"ids"`
}

func (req *SearchProductListingRequest) ToSearchProductListingArgs() *product_listing_domain.SearchProductListingArgs {
	searchDomainInput := product_listing_domain.SearchProductListingArgs{
		OrganizationID:       req.OrganizationID,
		SalesChannelStoreKey: req.SalesChannelStoreKey,
		SalesChannelPlatform: req.SalesChannelPlatform,
		InventoryFilters:     req.InventoryFilters,
		Query:                req.Query,
		Page:                 req.Page,
		Limit:                req.Limit,
		Cursor:               req.Cursor,
	}

	if req.State != "" {
		searchDomainInput.States = []string{string(req.State)}
	}
	if req.States != "" { // states priority
		searchDomainInput.States = strings.Split(req.States, ",")
	}

	if req.PredefinedFilters != "" {
		searchDomainInput.PredefinedFilters = strings.Split(req.PredefinedFilters, ",")
	}

	if req.LinkStatusFilters != "" {
		searchDomainInput.LinkStatusFilters = strings.Split(req.LinkStatusFilters, ",")
	}

	if req.SyncStatusFilters != "" {
		searchDomainInput.SyncStatusFilters = strings.Split(req.SyncStatusFilters, ",")
	}

	if req.Categories != "" {
		searchDomainInput.Categories = strings.Split(req.Categories, ",")
	}

	if req.SalesChannelProductIDs != "" {
		searchDomainInput.SalesChannelProductIds = strings.Split(req.SalesChannelProductIDs, ",")
	}

	if req.ProductsCenterProductIds != "" {
		searchDomainInput.ProductsCenterProductIds = strings.Split(req.ProductsCenterProductIds, ",")
	}

	if req.FeedCategoryTemplateID != "" {
		searchDomainInput.FeedCategoryTemplateID = req.FeedCategoryTemplateID
	}

	if req.Limit == 0 {
		searchDomainInput.Limit = consts.DefaultLimit
	}

	if req.IDs != "" {
		searchDomainInput.IDs = strings.Split(req.IDs, ",")
	}

	if req.SKUs != "" {
		searchDomainInput.SKUs = strings.Split(req.SKUs, ",")
	}

	return &searchDomainInput
}

type searchProductListingResponse struct {
	ProductListings []productListingResponse `json:"product_listings"`
	Pagination      *models.Pagination       `json:"pagination"`
	ParameterString string                   `json:"parameter_string"`
}

type searchProductListingIDsResponse struct {
	ProductListingIDs []string           `json:"product_listing_ids"`
	Pagination        *models.Pagination `json:"pagination"`
	ParameterString   string             `json:"parameter_string"`
}

type countProductListingResponse struct {
	Count int64 `json:"count"`
}

type productListingIDRequest struct {
	ID string `uri:"id" binding:"required,len=32"`
}

type salesChannelProduct struct {
	ID                 string                          `json:"id"`
	ConnectorProductID string                          `json:"connector_product_id"`
	State              consts.SalesChannelProductState `json:"state"`
	Metrics            salesChannelProductMetrics      `json:"metrics"`
}

type salesChannelProductMetrics struct {
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

type productsCenterProduct struct {
	ID                 string                                   `json:"id"`
	ConnectorProductID string                                   `json:"connector_product_id"`
	PublishState       consts.ProductsCenterProductPublishState `json:"publish_state"`
	Source             productsCenterProductSource              `json:"source"`
	UpdatedAt          time.Time                                `json:"updated_at"`
}

type productsCenterProductSource struct {
	StoreKey string `json:"store_key"`
	Platform string `json:"platform"`
	ID       string `json:"id"`
}

type ready struct {
	Status        consts.ReadyStatus  `json:"status"`
	FailedReasons []readyFailedReason `json:"failed_reasons"`
	LastFailedAt  time.Time           `json:"last_failed_at"`
}

type readyFailedReason struct {
	Position    string   `json:"position"`
	Reasons     []string `json:"reasons"`
	ErrorCodes  []string `json:"error_codes"`
	Suggestions []string `json:"suggestions"`
}

type audit struct {
	State         consts.AuditState   `json:"state"`
	FailedReasons []auditFailedReason `json:"failed_reasons"`
	LastFailedAt  time.Time           `json:"last_failed_at"`
}

type auditFailedReason struct {
	Position    string   `json:"position"`
	Reasons     []string `json:"reasons"`
	Suggestions []string `json:"suggestions"`
}

type publish struct {
	LastReferenceID string              `json:"last_reference_id"`
	State           consts.PublishState `json:"state"`
	Error           publishError        `json:"error"`
	LastFailedAt    time.Time           `json:"last_failed_at"`
}

type publishError struct {
	Code string `json:"code"`
	Msg  string `json:"msg"`
}

type inventorySyncSetting struct {
	Preference   string               `json:"preference" validate:"omitempty,oneof='store' 'customized'"`
	Customized   models.InventorySync `json:"customized"`
	LastEffectAt time.Time            `json:"last_effect_at"`
}

type priceSyncSetting struct {
	Preference   string           `json:"preference" validate:"omitempty,oneof='store' 'customized'"`
	Customized   models.PriceSync `json:"customized"`
	LastEffectAt time.Time        `json:"last_effect_at"`
}

type productSyncSetting struct {
	Preference   string             `json:"preference" validate:"omitempty,oneof='store' 'customized'"`
	Customized   models.ProductSync `json:"customized"`
	LastEffectAt time.Time          `json:"last_effect_at"`
}

type syncSettings struct {
	InventorySyncSetting inventorySyncSetting `json:"inventory_sync"`
	PriceSyncSetting     priceSyncSetting     `json:"price_sync"`
	ProductSyncSetting   productSyncSetting   `json:"product_sync"`
}

type productListingResponse struct {
	ID                      string                            `json:"id"`
	SalesChannel            models.SalesChannel               `json:"sales_channel"`
	Organization            models.Organization               `json:"organization"`
	SalesChannelProduct     salesChannelProduct               `json:"sales_channel_product"`
	ProductsCenterProduct   productsCenterProduct             `json:"products_center_product"`
	State                   consts.ProductListingProductState `json:"state"`
	LinkStatus              consts.LinkStatus                 `json:"link_status"`
	SyncStatus              consts.SyncStatus                 `json:"sync_status"`
	Ready                   ready                             `json:"ready"`
	Audit                   audit                             `json:"audit"`
	Publish                 publish                           `json:"publish"`
	Settings                syncSettings                      `json:"settings"`
	Product                 models.Product                    `json:"product"`
	Relations               []*productListingRelation         `json:"relations"`
	FeedCustomizationParams feedCustomizationParams           `json:"feed_customization_params"`
	Version                 int64                             `json:"version"`
	PendingDeletedAt        types.Datetime                    `json:"pending_deleted_at"`
	DeletedAt               types.Datetime                    `json:"deleted_at"`
	CreatedAt               time.Time                         `json:"created_at"`
	UpdatedAt               time.Time                         `json:"updated_at"`
}

type productListingRelation struct {
	ID                      string                 `json:"id"`
	SalesChannel            models.SalesChannel    `json:"sales_channel"`
	VariantPosition         int                    `json:"variant_position"`
	ProductListingVariantID string                 `json:"product_listing_variant_id"`
	SalesChannelVariant     salesChannelVariant    `json:"sales_channel_variant"`
	ProductsCenterVariant   productsCenterVariant  `json:"products_center_variant"`
	SyncStatus              consts.SyncStatus      `json:"sync_status"`
	LinkStatus              consts.LinkStatus      `json:"link_status"`
	AllowSync               consts.AllowSyncStatus `json:"allow_sync"`
	LastSyncedAt            types.Datetime         `json:"last_synced_at"`
	LastLinkedAt            types.Datetime         `json:"last_linked_at"`
	DeletedAt               types.Datetime         `json:"deleted_at"`
	CreatedAt               time.Time              `json:"created_at"`
	UpdatedAt               time.Time              `json:"updated_at"`
}

type salesChannelVariant struct {
	ID                 string `json:"id"`
	ConnectorProductID string `json:"connector_product_id"`
	ProductID          string `json:"product_id"`
	Sku                string `json:"sku"`
}

type productsCenterVariant struct {
	ID                 string                      `json:"id"`
	ProductID          string                      `json:"product_id"`
	ConnectorProductID string                      `json:"connector_product_id"`
	Source             productsCenterVariantSource `json:"source"`
}

type productsCenterVariantSource struct {
	StoreKey  string `json:"store_key"`
	Platform  string `json:"platform"`
	ID        string `json:"id"`
	ProductID string `json:"product_id"`
	Sku       string `json:"sku"`
}

type updateRequest struct {
	ID                    string                   `json:"id"`
	ProductsCenterProduct productsCenterProduct    `json:"products_center_product"`
	Settings              syncSettings             `json:"settings"`
	Product               models.Product           `json:"product"`
	Relations             []*updateRelationRequest `json:"relations" binding:"dive,required"`
	NeedPublish           bool                     `json:"need_publish"`
}

func (r *updateRequest) convertToDomainUpdateArgs() *product_listing_domain.ProductListingArgs {
	pl := &product_listing_domain.ProductListingArgs{}
	pl.Product = r.Product
	pl.Settings = convertToDomainSyncSettings(&r.Settings)
	pl.Relations = make([]*product_listing_domain.ProductListingRelation, 0, len(r.Relations))
	pl.ProductsCenterProduct = convertToDomainProductsCenterProduct(&r.ProductsCenterProduct)
	for _, relation := range r.Relations {
		pl.Relations = append(pl.Relations, relation.convertToDomainRelation())
	}
	pl.NeedPublish = r.NeedPublish

	return pl
}

type updateRelationRequest struct {
	ID                      string                 `json:"id"`
	VariantPosition         int                    `json:"variant_position"`
	ProductListingVariantID string                 `json:"product_listing_variant_id"`
	ProductsCenterVariant   productsCenterVariant  `json:"products_center_variant"`
	AllowSync               consts.AllowSyncStatus `json:"allow_sync"`
}

func (r *updateRelationRequest) convertToDomainRelation() *product_listing_domain.ProductListingRelation {
	domainRelation := &product_listing_domain.ProductListingRelation{}
	domainRelation.ID = r.ID
	domainRelation.VariantPosition = r.VariantPosition
	domainRelation.ProductListingVariantID = r.ProductListingVariantID
	domainRelation.AllowSync = r.AllowSync
	domainRelation.ProductsCenterVariant = convertToDomainProductsCenterVariant(&r.ProductsCenterVariant)
	return domainRelation
}

type listAuditVersionsArg struct {
	ProductListingID string
	Page             int64 `form:"page" binding:"lte=1000"`
	Limit            int64 `form:"limit" binding:"lte=1000"`
}

func (arg *listAuditVersionsArg) convertToDomainArg() *product_listing_domain.ListAuditVersionsArgs {
	return &product_listing_domain.ListAuditVersionsArgs{
		ProductListingID: arg.ProductListingID,
		Limit:            arg.Limit,
		Page:             arg.Page,
	}
}

type listAuditVersionsResponse struct {
	AuditVersions   []*product_listing_domain.AuditVersion `json:"audit_versions"`
	Paginator       *models.Paginator                      `json:"paginator"`
	ParameterString string                                 `json:"parameter_string"`
}

type feedCustomizationParams struct {
	FeedCategoryTemplateID string `json:"feed_category_template_id"`
}

type internalListRequest struct {
	IDs                      string `form:"ids"`
	OrganizationID           string `form:"organization_id"`
	SalesChannelStoreKey     string `form:"sales_channel_store_key"`
	SalesChannelPlatform     string `form:"sales_channel_platform"`
	ProductsCenterProductIDs string `form:"products_center_product_ids"`
	SalesChannelProductIDs   string `form:"sales_channel_product_ids"`
	Page                     int64  `form:"page"`
	Limit                    int64  `form:"limit" validate:"required"`
}

type internalListResponse struct {
	ProductListings []productListingResponse `json:"product_listings"`
	ParameterString string                   `json:"parameter_string"`
	Paginator       *models.Paginator        `json:"paginator"`
}

type pushToChannelRequest struct {
	SalesChannel            models.SalesChannel             `json:"sales_channel"`
	Organization            models.Organization             `json:"organization"`
	ProductsCenterProduct   productsCenterProduct           `json:"products_center_product"`
	Product                 models.Product                  `json:"product"`
	Relations               []*pushToChannelRelationRequest `json:"relations"`
	Ready                   ready                           `json:"ready"`
	FeedCustomizationParams feedCustomizationParams         `json:"feed_customization_params"`
}

func (r *pushToChannelRequest) convertToDomainPushToChannelArgs() *product_listing_domain.PushToChannelArg {
	arg := &product_listing_domain.PushToChannelArg{}
	arg.SalesChannel = r.SalesChannel
	arg.Organization = r.Organization
	arg.Product = r.Product
	arg.ProductsCenterProduct = convertToDomainProductsCenterProduct(&r.ProductsCenterProduct)
	relations := make([]*product_listing_domain.PushToChannelRelationArg, 0, len(r.Relations))
	for i := range r.Relations {
		relations = append(relations, &product_listing_domain.PushToChannelRelationArg{
			ProductListingVariantID: r.Relations[i].ProductListingVariantID,
			ProductsCenterVariant:   convertToDomainProductsCenterVariant(&r.Relations[i].ProductsCenterVariant),
		})
	}
	arg.Relations = relations
	arg.Ready = convertToDomainReady(&r.Ready)
	arg.FeedCustomizationParams = convertToDomainFeedCustomizationParams(&r.FeedCustomizationParams)
	return arg
}

type pushToChannelRelationRequest struct {
	ProductListingVariantID string                `json:"product_listing_variant_id"`
	ProductsCenterVariant   productsCenterVariant `json:"products_center_variant"`
}

type salesChannelProductEventRequest struct {
	ID                  string                                     `json:"id"`
	SalesChannel        models.SalesChannel                        `json:"sales_channel"`
	Organization        models.Organization                        `json:"organization"`
	SalesChannelProduct salesChannelProduct                        `json:"sales_channel_product"`
	Product             models.Product                             `json:"product"`
	Relations           []*salesChannelProductEventRelationRequest `json:"relations"`
	Audit               audit                                      `json:"audit"`
	Version             int64                                      `json:"version"`
}

func (r *salesChannelProductEventRequest) convertToDomainSalesChannelProductEventArg() *product_listing_domain.SalesChannelProductEventArg {
	arg := &product_listing_domain.SalesChannelProductEventArg{}
	arg.ID = r.ID
	arg.SalesChannel = r.SalesChannel
	arg.Organization = r.Organization
	arg.SalesChannelProduct = convertToDomainSalesChannelProduct(&r.SalesChannelProduct)
	arg.Product = r.Product
	relations := make([]*product_listing_domain.SalesChannelProductEventRelationArg, 0, len(r.Relations))
	for i := range r.Relations {
		relations = append(relations, &product_listing_domain.SalesChannelProductEventRelationArg{
			ID:                  r.Relations[i].ID,
			VariantPosition:     r.Relations[i].VariantPosition,
			SalesChannelVariant: convertToDomainSalesChannelVariant(&r.Relations[i].SalesChannelVariant),
		})
	}
	arg.Relations = relations
	arg.Audit = convertToDomainAudit(&r.Audit)
	arg.Version = r.Version
	return arg
}

type salesChannelProductEventRelationRequest struct {
	ID                  string              `json:"id"`
	VariantPosition     int                 `json:"variant_position"`
	SalesChannelVariant salesChannelVariant `json:"sales_channel_variant"`
}

type productsCenterProductEventRequest struct {
	ProductsCenterProduct productsCenterProduct                        `json:"products_center_product"`
	Product               models.Product                               `json:"product"`
	Relations             []*productsCenterProductEventRelationRequest `json:"relations"`
	Ready                 ready                                        `json:"ready"`
	ForcePublish          bool                                         `json:"force_publish"`
}

func (r *productsCenterProductEventRequest) convertToDomainProductsCenterProductEventArg() *product_listing_domain.ProductsCenterProductEventArg {
	arg := &product_listing_domain.ProductsCenterProductEventArg{}
	arg.ProductsCenterProduct = convertToDomainProductsCenterProduct(&r.ProductsCenterProduct)
	arg.Product = r.Product
	arg.ForcePublish = r.ForcePublish
	relations := make([]*product_listing_domain.ProductsCenterProductEventRelationArg, 0, len(r.Relations))
	for i := range r.Relations {
		relations = append(relations, &product_listing_domain.ProductsCenterProductEventRelationArg{
			ProductListingVariantID: r.Relations[i].ProductListingVariantID,
			ProductsCenterVariant:   convertToDomainProductsCenterVariant(&r.Relations[i].ProductsCenterVariant),
		})
	}
	arg.Relations = relations
	arg.Ready = convertToDomainReady(&r.Ready)

	return arg
}

type productsCenterProductEventRelationRequest struct {
	ProductListingVariantID string                `json:"product_listing_variant_id"`
	ProductsCenterVariant   productsCenterVariant `json:"products_center_variant"`
}

type editAttributesRequest struct {
	CategorySourceID string                               `json:"category_source_id"`
	SizeChart        models.ProductSizeChart              `json:"size_chart"`
	Certifications   []*models.ProductCertification       `json:"certifications"`
	Attributes       []*models.ProductAttribute           `json:"attributes"`
	Length           models.ProductVariantShippingSetting `json:"length"`
	Width            models.ProductVariantShippingSetting `json:"width"`
	Height           models.ProductVariantShippingSetting `json:"height"`
	Weight           models.ProductVariantShippingSetting `json:"weight"`
	Brand            models.SalesChannelResource          `json:"brand"`
	Compliance       models.ProductCompliance             `json:"compliance"`
}

func (r *editAttributesRequest) convertToDomainEditAttributesArgs() *product_listing_domain.EditAttributesArgs {
	return &product_listing_domain.EditAttributesArgs{
		CategorySourceID: r.CategorySourceID,
		SizeChart:        r.SizeChart,
		Certifications:   r.Certifications,
		Attributes:       r.Attributes,
		Length:           r.Length,
		Width:            r.Width,
		Height:           r.Height,
		Weight:           r.Weight,
		Brand:            r.Brand,
		Compliance:       r.Compliance,
	}
}

type linkRequest struct {
	LinkVariants []linkVariant `json:"link_variants"`
}

type linkVariant struct {
	ProductListingVariantID        string `json:"product_listing_variant_id"`
	ProductsCenterProductID        string `json:"products_center_product_id"`
	ProductsCenterProductVariantID string `json:"products_center_product_variant_id"`
}

func (r *linkRequest) convertToLinkArgs(productListingID string) *product_listing_domain.LinkArg {
	result := &product_listing_domain.LinkArg{}
	result.ProductListingID = productListingID
	for _, reqLinkVariant := range r.LinkVariants {
		result.LinkedVariants = append(result.LinkedVariants, product_listing_domain.LinkedVariantArg{
			ProductListingVariantID:        reqLinkVariant.ProductListingVariantID,
			ProductsCenterProductID:        reqLinkVariant.ProductsCenterProductID,
			ProductsCenterProductVariantID: reqLinkVariant.ProductsCenterProductVariantID,
		})
	}
	return result
}

type summaryCategoryIDsResponse struct {
	CategoryIDs []string `json:"category_ids"`
}

type autoLinkRequest struct {
	RecommendedPCProductID string   `json:"recommended_pc_product_id"`
	TargetVariantIDs       []string `json:"target_variant_ids"` // option

	AutoLinkBizParams autoLinkBizParams `json:"biz_params"`
}

type autoLinkBizParams struct {
	BizTriggerSource string `json:"biz_trigger_source"`
}

type refreshSearchableProduct struct {
	ProductsCenterProductIDs string `form:"products_center_product_ids"`
}

type refreshESParams struct {
	VersionOffset int  `form:"version_offset"`
	ForceRefresh  bool `form:"force_refresh"`
}
