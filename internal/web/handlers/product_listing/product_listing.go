package product_listing

import (
	"context"
	"errors"
	"net/http"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	redsync "github.com/go-redsync/redsync/v4"
	"go.uber.org/zap"

	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/category"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/convert"

	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/common/calculators"
	"github.com/AfterShip/pltf-pd-product-listings/internal/third_party/connectors"
	tiktokapi "github.com/AfterShip/pltf-pd-product-listings/internal/third_party/tiktok_api"

	"github.com/AfterShip/connectors-library/gin/responder"
	"github.com/AfterShip/gopkg/log"
	"github.com/AfterShip/pltf-pd-product-listings/internal/consts"
	product_listing_domain "github.com/AfterShip/pltf-pd-product-listings/internal/domains/product_listing"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
	"github.com/AfterShip/pltf-pd-product-listings/internal/web/middlewares"
)

const (
	ctxKeyProductListingID = "productListingID"
)

type ProductListing struct {
	logger    *log.Logger
	service   product_listing_domain.Service
	validator *validator.Validate
	responder *responder.Responder
}

func NewProductListing(logger *log.Logger, service product_listing_domain.Service) *ProductListing {
	return &ProductListing{
		logger:    logger,
		service:   service,
		validator: validator.New(),
		responder: responder.NewResponder(true, errorResponseMapping),
	}
}

func (p *ProductListing) validateMiddleware(c *gin.Context) {
	c.Next()
}

func (p *ProductListing) getIDMiddleware(c *gin.Context) {
	args := productListingIDRequest{}
	if err := c.ShouldBindUri(&args); err != nil {
		p.responder.ResponseWithErrorCode(c, missRequiredParam, err)
		c.Abort()
	}
	c.Set(ctxKeyProductListingID, args.ID)
	c.Next()
}

func (p *ProductListing) RegisterRoutes(router *gin.RouterGroup) {
	router.Use(p.validateMiddleware, middlewares.AddTrackingHandlerFunc())
	router.GET("/product-listings", p.search)
	router.GET("/product-listings/count", p.count)
	router.GET("/product-listing-ids", p.searchIDs)
	router.GET("/product-listings/summary-category-ids", p.summaryCategoryIDs)

	listingAPIWithIDGroup := router.Group("/product-listings/:id", p.getIDMiddleware)
	{
		listingAPIWithIDGroup.GET("", p.get)
		listingAPIWithIDGroup.DELETE("", p.delete)
		listingAPIWithIDGroup.PUT("", p.update)
		listingAPIWithIDGroup.POST("/duplicate", p.duplicate)
		listingAPIWithIDGroup.POST("/update-publish-state", p.updatePublishState)
		listingAPIWithIDGroup.POST("/update-audit-state", p.updateAuditState)
		listingAPIWithIDGroup.GET("/audit-versions", p.listAuditVersions)
		listingAPIWithIDGroup.POST("/preview", p.generatePreview)
		listingAPIWithIDGroup.POST("/link", p.link)
	}

	listingInternalAPIGroup := router.Group("/internal/product-listings")
	{
		listingInternalAPIGroup.GET("/", p.internalList)
		listingInternalAPIGroup.POST("/push-to-channel", p.pushToChannel)
		listingInternalAPIGroup.POST("/sales-channel-product-event", p.salesChannelProductEvent)
		// API 作用: 检查并修复 product-listings
		listingInternalAPIGroup.GET("/check-and-fix-status", p.checkAndFixStatus)
	}

	listingInternalAPIWithIDGroup := router.Group("/internal/product-listings/:id", p.getIDMiddleware)
	{
		listingInternalAPIWithIDGroup.POST("/products-center-product-event", p.productsCenterProductEvent)
		listingInternalAPIWithIDGroup.DELETE("/products-center-product-event", p.productsCenterProductDeleteEvent)
		listingInternalAPIWithIDGroup.POST("/edit-attributes", p.editAttributes)
		listingInternalAPIWithIDGroup.POST("/auto-link", p.autoLink)
		listingInternalAPIWithIDGroup.GET("/refresh-es", p.refreshES)
		listingInternalAPIWithIDGroup.POST("/update-relations", p.updateRelations)

		// 运维接口: 后续改为 POST, 并放到 support tool 上
		listingInternalAPIWithIDGroup.GET("/publish-update", p.publishUpdate)
		listingInternalAPIWithIDGroup.GET("/unmatch-product", p.unMatchProduct)
		listingInternalAPIWithIDGroup.GET("/match-product", p.matchProduct)
	}

	esProxyGroup := router.Group("/internal/product-listings/es-proxy")
	{
		esProxyGroup.GET("/count", p.esProxyProductListingCount)
		esProxyGroup.GET("/search", p.esProxyProductListingSearch)
	}

}

func (p *ProductListing) get(c *gin.Context) {
	id := c.GetString(ctxKeyProductListingID)
	result, err := p.service.GetByID(c, id)
	if err != nil {
		if !errors.Is(err, product_listing_domain.ErrNotFound) {
			p.logger.With(zap.String("Id", id)).ErrorCtx(c, "get product listing failed", zap.Error(err))
		}
		p.responder.ResponseWithError(c, err)
		return
	}

	p.responder.ResponseWithOK(c, convertToProductListingResponse(&result))
}

func (p *ProductListing) delete(c *gin.Context) {
	id := c.GetString(ctxKeyProductListingID)
	forceParam, _ := c.GetQuery("force")
	forceDelete := strings.ToLower(forceParam) == "true"
	result, err := p.service.GetByID(c, id)
	if err != nil {
		if !errors.Is(err, product_listing_domain.ErrNotFound) {
			p.logger.With(zap.String("Id", id)).ErrorCtx(c, "get product listing failed", zap.Error(err))
		}
		p.responder.ResponseWithError(c, err)
		return
	}

	err = p.service.Delete(c, result.ID, forceDelete)
	if err != nil {
		p.logger.With(zap.String("Id", id)).Info("delete product listing failed", zap.Error(err))
		p.responder.ResponseWithError(c, err)
		return
	}

	p.responder.ResponseWithOK(c, convertToProductListingResponse(&result))
}

func (p *ProductListing) update(c *gin.Context) {
	id := c.GetString(ctxKeyProductListingID)

	req := updateRequest{}
	if err := c.ShouldBindJSON(&req); err != nil {
		p.responder.ResponseWithErrorCode(c, missRequiredParam, err)
		return
	}

	result, err := p.service.Update(c, id, req.convertToDomainUpdateArgs())
	if err != nil {
		if !needAckError(err) {
			p.logger.With(zap.String("ID", id)).ErrorCtx(c, "update product listing failed", zap.Error(err))
		}
		p.responder.ResponseWithError(c, err)
		return
	}

	p.responder.ResponseWithOK(c, convertToProductListingResponse(&result))
}

func (p *ProductListing) search(c *gin.Context) {
	var req SearchProductListingRequest
	req.Page = 1
	req.Limit = 50
	if err := c.ShouldBindQuery(&req); err != nil {
		p.responder.ResponseWithErrorCode(c, missRequiredParam, err)
		return
	}

	domainData, pagination, err := p.service.Search(c, req.ToSearchProductListingArgs())
	if err != nil {
		p.responder.ResponseWithError(c, err)
		return
	}

	convertedData := make([]productListingResponse, len(domainData))
	for i, v := range domainData {
		convertedData[i] = convertToProductListingResponse(v)
	}

	response := searchProductListingResponse{
		ProductListings: convertedData,
		Pagination:      pagination,
		ParameterString: c.Request.URL.RawQuery,
	}

	p.responder.ResponseWithOK(c, response)
}

func (p *ProductListing) count(c *gin.Context) {
	var req SearchProductListingRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		p.responder.ResponseWithErrorCode(c, missRequiredParam, err)
		return
	}

	count, err := p.service.Count(c, req.ToSearchProductListingArgs())
	if err != nil {
		p.responder.ResponseWithError(c, err)
		return
	}
	response := countProductListingResponse{
		Count: count,
	}

	p.responder.ResponseWithOK(c, response)
}

func (p *ProductListing) searchIDs(c *gin.Context) {
	var req SearchProductListingRequest
	req.Page = 1
	req.Limit = 50
	if err := c.ShouldBindQuery(&req); err != nil {
		p.responder.ResponseWithErrorCode(c, missRequiredParam, err)
		return
	}

	ids, pagination, err := p.service.SearchIDs(c, req.ToSearchProductListingArgs())
	if err != nil {
		p.responder.ResponseWithError(c, err)
		return
	}

	response := searchProductListingIDsResponse{
		ProductListingIDs: ids,
		Pagination:        pagination,
		ParameterString:   c.Request.URL.RawQuery,
	}

	p.responder.ResponseWithOK(c, response)
}

func (p *ProductListing) summaryCategoryIDs(c *gin.Context) {
	var req SearchProductListingRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		p.responder.ResponseWithErrorCode(c, missRequiredParam, err)
		return
	}

	req.Page = 1
	req.Limit = 10000

	if p.validator.Struct(req) != nil {
		p.responder.ResponseWithErrorCode(c, missRequiredParam, errors.New("invalid params"))
		return
	}

	data, err := p.service.AggregateCategoryIDs(c, req.ToSearchProductListingArgs())
	if err != nil {
		p.responder.ResponseWithError(c, err)
		return
	}

	p.responder.ResponseWithOK(c, summaryCategoryIDsResponse{
		CategoryIDs: data,
	})
}

func (p *ProductListing) duplicate(c *gin.Context) {
	id := c.GetString(ctxKeyProductListingID)
	result, err := p.service.Duplicate(c, id)
	if err != nil {
		p.logger.With(zap.String("Id", id)).Info("duplicate product listing failed", zap.Error(err))
		p.responder.ResponseWithError(c, err)
		return
	}

	p.responder.ResponseWithOK(c, convertToProductListingResponse(&result))
}

func (p *ProductListing) updatePublishState(c *gin.Context) {
	var publishArgs publish
	if err := c.ShouldBindJSON(&publishArgs); err != nil {
		p.responder.ResponseWithErrorCode(c, missRequiredParam, err)
		return
	}

	id := c.GetString(ctxKeyProductListingID)
	args := convertToDomainPublishArgs(&publishArgs)
	result, err := p.service.UpdatePublishState(c, id, &args)
	if err != nil {
		if needAckError(err) {
			p.responder.ResponseWithErrorCode(c, ackErrorCode, err)
			return
		}
		p.logger.With(zap.String("Id", id)).ErrorCtx(c, "update product listing publish state failed", zap.Error(err))
		p.responder.ResponseWithError(c, err)
		return
	}

	p.responder.ResponseWithOK(c, convertToProductListingResponse(&result))
}

func (p *ProductListing) updateAuditState(c *gin.Context) {
	id := c.GetString(ctxKeyProductListingID)

	var auditArgs audit
	if err := c.ShouldBindJSON(&auditArgs); err != nil {
		p.responder.ResponseWithErrorCode(c, missRequiredParam, err)
		return
	}

	args := convertToDomainAuditArgs(&auditArgs)
	result, err := p.service.UpdateAuditState(c, id, &args)
	if err != nil {
		if needAckError(err) {
			p.responder.ResponseWithErrorCode(c, ackErrorCode, err)
			return
		}
		p.logger.With(zap.String("Id", id)).ErrorCtx(c, "update product listing audit state failed", zap.Error(err))
		p.responder.ResponseWithError(c, err)
		return
	}

	p.responder.ResponseWithOK(c, convertToProductListingResponse(&result))
}

func (p *ProductListing) listAuditVersions(c *gin.Context) {
	id := c.GetString(ctxKeyProductListingID)
	arg := listAuditVersionsArg{
		Page:  1,
		Limit: 1,
	}
	if err := c.ShouldBindQuery(&arg); err != nil {
		p.responder.ResponseWithErrorCode(c, missRequiredParam, err)
		return
	}
	arg.ProductListingID = id
	result, err := p.service.ListAuditVersions(c, arg.convertToDomainArg())
	if err != nil {
		p.logger.With(zap.String("Id", id)).ErrorCtx(c, "get product listing audit version failed", zap.Error(err))
		p.responder.ResponseWithError(c, err)
		return
	}

	response := listAuditVersionsResponse{
		AuditVersions:   result,
		ParameterString: c.Request.URL.RawQuery,
		Paginator: &models.Paginator{
			Page:        arg.Page,
			Limit:       arg.Limit,
			HasNextPage: len(result) == int(arg.Limit),
		},
	}

	p.responder.ResponseWithOK(c, response)
}

func (p *ProductListing) internalList(c *gin.Context) {
	var req internalListRequest
	req.Limit = 100
	req.Page = 1

	if err := c.ShouldBindQuery(&req); err != nil {
		p.responder.ResponseWithErrorCode(c, missRequiredParam, err)
		return
	}

	if req.IDs == "" && req.OrganizationID == "" {
		p.responder.ResponseWithErrorCode(c, missRequiredParam, errors.New("ids or organization_id is required"))
		return
	}

	IDs := strings.Split(req.IDs, ",")
	if req.IDs != "" {
		if len(IDs) == 0 {
			p.responder.ResponseWithErrorCode(c, missRequiredParam, errors.New("ids is required"))
			return
		}
		if len(IDs) > 1000 {
			p.responder.ResponseWithErrorCode(c, errorIDsMaxLimitCode, errors.New("ids max limit is 1000"))
			return
		}
	}

	arg := &product_listing_domain.ListArg{
		IDs:                      req.IDs,
		OrganizationID:           req.OrganizationID,
		SalesChannelStoreKey:     req.SalesChannelStoreKey,
		SalesChannelPlatform:     req.SalesChannelPlatform,
		ProductsCenterProductIDs: req.ProductsCenterProductIDs,
		SalesChannelProductIDs:   req.SalesChannelProductIDs,
		Limit:                    req.Limit,
		Page:                     req.Page,
	}

	domainData, err := p.service.List(c, arg)
	if err != nil {
		p.responder.ResponseWithError(c, err)
		return
	}

	convertedData := make([]productListingResponse, len(domainData))
	for i, v := range domainData {
		convertedData[i] = convertToProductListingResponse(v)
	}

	response := internalListResponse{
		ProductListings: convertedData,
		ParameterString: c.Request.URL.RawQuery,
		Paginator: &models.Paginator{
			Page:        req.Page,
			Limit:       req.Limit,
			HasNextPage: len(convertedData) == int(req.Limit),
		},
	}

	p.responder.ResponseWithOK(c, response)
}

// nolint:dupl
func (p *ProductListing) pushToChannel(c *gin.Context) {
	var req pushToChannelRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		p.responder.ResponseWithErrorCode(c, missRequiredParam, err)
		return
	}
	if err := p.validator.Struct(req); err != nil {
		p.responder.ResponseWithErrorCode(c, missRequiredParam, err)
		return
	}

	result, err := p.service.PushToChannel(c, req.convertToDomainPushToChannelArgs())
	if err != nil {
		if needAckError(err) {
			p.responder.ResponseWithErrorCode(c, ackErrorCode, err)
			return
		}
		p.logger.With().ErrorCtx(c, "api handle push to channel failed", zap.Error(err))
		p.responder.ResponseWithError(c, err)
		return
	}

	p.responder.ResponseWithOK(c, convertToProductListingResponse(&result))
}

// nolint:gocyclo,dupl
func (p *ProductListing) salesChannelProductEvent(c *gin.Context) {
	ctx := c.Request.Context()
	var req salesChannelProductEventRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		p.responder.ResponseWithErrorCode(c, missRequiredParam, err)
		return
	}
	if err := p.validator.Struct(req); err != nil {
		p.responder.ResponseWithErrorCode(c, missRequiredParam, err)
		return
	}

	if req.SalesChannel.Platform == consts.Shein {
		p.logger.InfoCtx(c, "handler shein sales channel product event", zap.Any("req", req))
	}

	ctx = log.AppendFieldsToContext(ctx,
		zap.String("organization_id", req.Organization.ID),
		zap.String("sales_channel_product_id", req.SalesChannelProduct.ID),
	)

	result, err := p.service.SalesChannelProductEvent(ctx, req.convertToDomainSalesChannelProductEventArg())
	if err != nil {
		if needAckError(err) {
			p.responder.ResponseWithErrorCode(c, ackErrorCode, err)
			return
		}
		if unAckNeedWaring(err) {
			p.logger.With().WarnCtx(ctx, "api handle sales channel product event failed：retry unack", zap.Error(err))
		} else {
			p.logger.With().ErrorCtx(ctx, "api handle sales channel product event failed", zap.Error(err))
		}
		p.responder.ResponseWithError(c, err)
		return
	}
	p.responder.ResponseWithOK(c, convertToProductListingResponse(&result))
}

func (p *ProductListing) productsCenterProductEvent(c *gin.Context) {
	id := c.GetString(ctxKeyProductListingID)

	var req productsCenterProductEventRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		p.responder.ResponseWithErrorCode(c, missRequiredParam, err)
		return
	}
	if err := p.validator.Struct(req); err != nil {
		p.responder.ResponseWithErrorCode(c, missRequiredParam, err)
		return
	}

	result, err := p.service.ProductsCenterProductEvent(c, id, req.convertToDomainProductsCenterProductEventArg())
	if err != nil {
		if needAckError(err) {
			p.responder.ResponseWithErrorCode(c, ackErrorCode, err)
			return
		} else if unAckNeedWaring(err) {
			p.logger.With().WarnCtx(c, "api handle products center product event failed：retry unack", zap.Error(err))
		} else {
			p.logger.With().ErrorCtx(c, "api handle products center product event failed",
				zap.String("productListingID", id),
				zap.String("source_platform", req.ProductsCenterProduct.Source.Platform),
				zap.String("source_store_key", req.ProductsCenterProduct.Source.StoreKey),
				zap.String("source_connector_product_id", req.ProductsCenterProduct.ConnectorProductID),
				zap.Error(err))
		}
		p.responder.ResponseWithError(c, err)
		return
	}
	p.responder.ResponseWithOK(c, convertToProductListingResponse(&result))
}

func (p *ProductListing) productsCenterProductDeleteEvent(c *gin.Context) {

	ctx := c.Request.Context()

	id := c.GetString(ctxKeyProductListingID)

	ctx = log.AppendFieldsToContext(ctx, zap.String("listing_id", id))

	result, err := p.service.ProductsCenterProductDeleteEvent(ctx, id)
	if err != nil {
		if needAckError(err) {
			p.responder.ResponseWithErrorCode(c, ackErrorCode, err)
			return
		} else if unAckNeedWaring(err) {
			p.logger.With().WarnCtx(ctx, "api handle products center product delete event failed：retry unack", zap.Error(err))
		} else {
			p.logger.With().ErrorCtx(ctx, "api handle products center product delete event failed", zap.Error(err))
		}
		p.responder.ResponseWithError(c, err)
		return
	}
	p.responder.ResponseWithOK(c, convertToProductListingResponse(&result))
}

func (p *ProductListing) generatePreview(c *gin.Context) {
	id := c.GetString(ctxKeyProductListingID)

	data := new(product_listing_domain.ProductListing)
	if err := c.ShouldBindJSON(data); err != nil {
		p.responder.ResponseWithErrorCode(c, http.StatusBadRequest, err)
		return
	}

	// Validate
	if id != data.ID {
		p.responder.ResponseWithErrorCode(c, http.StatusUnprocessableEntity, errors.New("id in path and body are not the same"))
		return
	}

	result, err := p.service.GeneratePreview(c, data)
	if err != nil {
		p.logger.With(zap.String("Id", id)).ErrorCtx(c, "get product listing preview failed", zap.Error(err))
		p.responder.ResponseWithError(c, err)
		return
	}

	p.responder.ResponseWithOK(c, result)
}

func (p *ProductListing) editAttributes(c *gin.Context) {
	id := c.GetString(ctxKeyProductListingID)
	req := editAttributesRequest{}
	if err := c.ShouldBindJSON(&req); err != nil {
		p.responder.ResponseWithErrorCode(c, missRequiredParam, err)
		return
	}
	result, err := p.service.EditAttributes(c, id, req.convertToDomainEditAttributesArgs())
	if err != nil {
		if needAckError(err) {
			p.responder.ResponseWithErrorCode(c, ackErrorCode, err)
			return
		}
		p.logger.With(zap.String("Id", id)).ErrorCtx(c, "edit product listing attributes failed", zap.Error(err))
		p.responder.ResponseWithError(c, err)
		return
	}
	p.responder.ResponseWithOK(c, convertToProductListingResponse(&result))
}

func (p *ProductListing) autoLink(c *gin.Context) {
	ctx := c.Request.Context()
	id := c.GetString(ctxKeyProductListingID)

	req := autoLinkRequest{}
	if err := c.ShouldBindJSON(&req); err != nil {
		p.responder.ResponseWithErrorCode(c, missRequiredParam, err)
		return
	}
	ctx = context.WithValue(ctx, consts.AutoLinkFromKey, req.AutoLinkBizParams.BizTriggerSource)

	result, err := p.service.AutoLink(ctx, &product_listing_domain.AutoLinkArg{
		ID:                     id,
		TargetVariantIDs:       req.TargetVariantIDs,
		RecommendedPCProductID: req.RecommendedPCProductID,
	})
	if err != nil {
		p.responder.ResponseWithError(c, err)
		return
	}
	p.responder.ResponseWithOK(c, convertToProductListingResponse(&result))
}

// nolint: dupl
func (p *ProductListing) link(c *gin.Context) {
	ctx := c.Request.Context()
	id := c.GetString(ctxKeyProductListingID)
	req := linkRequest{}
	if err := c.ShouldBindJSON(&req); err != nil {
		p.responder.ResponseWithErrorCode(c, missRequiredParam, err)
		return
	}
	result, err := p.service.LinkOrUnlink(ctx, req.convertToLinkArgs(id))
	if err != nil {
		p.responder.ResponseWithError(c, err)
		return
	}
	p.responder.ResponseWithOK(c, convertToProductListingResponse(&result))
}

func (p *ProductListing) refreshES(c *gin.Context) {
	ctx := c.Request.Context()
	id := c.GetString(ctxKeyProductListingID)

	var params refreshESParams
	if err := c.ShouldBindQuery(&params); err != nil {
		p.responder.ResponseWithErrorCode(c, missRequiredParam, err)
		return
	}

	esOptions := make([]product_listing_domain.EsOption, 0)
	if params.VersionOffset != 0 {
		esOptions = append(esOptions, product_listing_domain.WithVersionOffset(params.VersionOffset))
	}
	if params.ForceRefresh {
		esOptions = append(esOptions, product_listing_domain.WithForceRefresh(true))
	}

	err := p.service.RefreshES(ctx, id, esOptions...)
	if err != nil {
		p.responder.ResponseWithError(c, err)
		return
	}
	p.responder.ResponseWithOK(c, nil)
}

// publishUpdate 对已经刊登的商品进行更新
func (p *ProductListing) publishUpdate(c *gin.Context) {
	ctx := c.Request.Context()
	id := c.GetString(ctxKeyProductListingID)
	err := p.service.PublishUpdate(ctx, id)
	if err != nil {
		p.responder.ResponseWithError(c, err)
		return
	}
	p.responder.ResponseWithOK(c, nil)
}

// unMatch product listing
func (p *ProductListing) unMatchProduct(c *gin.Context) {
	ctx := c.Request.Context()
	id := c.GetString(ctxKeyProductListingID)
	listing, err := p.service.UnMatchProduct(ctx, id)
	if err != nil {
		p.responder.ResponseWithError(c, err)
		return
	}
	p.responder.ResponseWithOK(c, listing)
}

func (p *ProductListing) matchProduct(c *gin.Context) {
	ctx := c.Request.Context()
	id := c.GetString(ctxKeyProductListingID)

	listing, err := p.service.MatchProduct(ctx, id)
	if err != nil {
		p.responder.ResponseWithError(c, err)
		return
	}
	p.responder.ResponseWithOK(c, listing)
}

func (p *ProductListing) updateRelations(c *gin.Context) {
	id := c.GetString(ctxKeyProductListingID)

	req := product_listing_domain.UpdateRelationsArg{}
	if err := c.ShouldBindJSON(&req); err != nil {
		p.responder.ResponseWithErrorCode(c, missRequiredParam, err)
		return
	}

	result, err := p.service.UpdateRelations(c, id, &req)
	if err != nil {
		if needAckError(err) {
			p.responder.ResponseWithErrorCode(c, ackErrorCode, err)
			return
		}
		p.logger.With(zap.String("Id", id)).ErrorCtx(c, "update product listing relations failed", zap.Error(err))
		p.responder.ResponseWithError(c, err)
		return
	}

	p.responder.ResponseWithOK(c, convertToProductListingResponse(&result))
}

func (p *ProductListing) checkAndFixStatus(c *gin.Context) {
	ctx := c.Request.Context()

	dryRunParam, _ := c.GetQuery("dry_run")              // 是否发起处理
	countParam, _ := c.GetQuery("processed_limit_count") // 处理数量限制

	var dryRun bool
	var processedLimitCount int
	if strings.EqualFold(dryRunParam, "true") {
		dryRun = true
	}
	if countParam != "" {
		processedLimitCount, _ = strconv.Atoi(countParam)
	}

	if err := p.service.CheckAndFixStatus(ctx, dryRun, processedLimitCount); err != nil {
		p.logger.With().ErrorCtx(ctx, "check and fix product listing status failed", zap.Error(err))
		p.responder.ResponseWithError(c, err)
		return
	}

	p.responder.ResponseWithOK(c, "ok")
}

// needAckError 仅控制日志打印级别
func needAckError(err error) bool {
	return errors.Is(err, product_listing_domain.ErrNotFound) ||
		errors.Is(err, product_listing_domain.ErrPublishStateInUpdate) ||
		errors.Is(err, product_listing_domain.ErrInvalidPublishState) ||
		errors.Is(err, product_listing_domain.ErrUpdatePublicationByRefID) ||
		errors.Is(err, product_listing_domain.ErrProductsCenterProductIsPushed) ||
		errors.Is(err, product_listing_domain.ErrVersionConflict) ||
		errors.Is(err, product_listing_domain.ErrNotLiveProduct) ||
		errors.Is(err, product_listing_domain.ErrSalesChannelProductConflict) ||
		errors.Is(err, product_listing_domain.ErrSameAuditVersion) ||
		errors.Is(err, connectors.ErrConnectionNotFound) ||
		errors.Is(err, calculators.ErrPriceNotFound) ||
		errors.Is(err, calculators.ErrInventoryNotFound) ||
		errors.Is(err, product_listing_domain.ErrProductsCenterProductNotFound) ||
		errors.Is(err, tiktokapi.ErrCategoryVersionNotMatch) ||
		errors.Is(err, tiktokapi.ErrCategoryInviteOnly) ||
		errors.Is(err, tiktokapi.ErrSellerInactive) ||
		errors.Is(err, convert.ErrorDownLoadFileFromEcommerce4XX) ||
		errors.Is(err, convert.ErrorConvertImageService422Error) ||
		errors.Is(err, product_listing_domain.ErrBothConnectionIsNotFound) ||
		errors.Is(err, product_listing_domain.ErrSheinProductIsInvalid) ||
		errors.Is(err, tiktokapi.ErrCategoryUnavailable) ||
		errors.Is(err, category.ErrCategoryNotFound) ||
		errors.Is(err, tiktokapi.ErrAuthorizationExpired) ||
		errors.Is(err, product_listing_domain.ErrEditAttributesNotAllowUpdate) ||
		errors.Is(err, calculators.ErrMissingShopifyMarketsMapping) ||
		errors.Is(err, product_listing_domain.ErrStateInDelete)
}

func unAckNeedWaring(err error) bool {
	return errors.Is(err, product_listing_domain.ErrPublishStateInUpdateNotAck) ||
		errors.Is(err, redsync.ErrFailed) ||
		errors.Is(err, models.ErrVersionConflict)
}
