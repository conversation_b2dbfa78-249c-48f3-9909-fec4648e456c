package product_listing

import (
	"bytes"
	"context"
	"encoding/base64"
	"fmt"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/go-redsync/redsync/v4"
	"github.com/pkg/errors"
	"github.com/spf13/viper"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"github.com/tidwall/gjson"

	"github.com/AfterShip/gopkg/cfg"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/common/calculators"
	"github.com/AfterShip/pltf-pd-product-listings/internal/third_party/connectors"

	"github.com/AfterShip/pltf-pd-product-listings/internal/config"
	"github.com/AfterShip/pltf-pd-product-listings/internal/datastore"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/product_listing"
	product_listing_domain "github.com/AfterShip/pltf-pd-product-listings/internal/domains/product_listing"
	"github.com/AfterShip/pltf-pd-product-listings/internal/logger"
)

func TestProductListing_get(t *testing.T) {
	loadConfig(t)
	mockSvc := new(product_listing.MockProductListingService)

	testcases := []struct {
		name      string
		path      string
		mock      func()
		expCode   int
		respCheck func(*testing.T, string)
	}{
		{
			name:    "ID short case",
			path:    "/product-listings/123",
			expCode: http.StatusUnprocessableEntity,
		},
		{
			name:    "ID long case",
			path:    "/product-listings/" + strings.Repeat("x", 33),
			expCode: http.StatusUnprocessableEntity,
		},
		{
			name: "Not found case",
			path: "/product-listings/" + strings.Repeat("x", 32),
			mock: func() {
				mockSvc.On("GetByID", mock.Anything, mock.Anything).Return(product_listing.ProductListing{}, product_listing_domain.ErrNotFound).Once()
			},
			expCode: http.StatusNotFound,
		},
		{
			name: "Good case",
			path: "/product-listings/" + strings.Repeat("x", 32),
			mock: func() {
				mockSvc.On("GetByID", mock.Anything, mock.Anything).Return(product_listing.ProductListing{}, nil).Once()
			},
			expCode: http.StatusOK,
			respCheck: func(t *testing.T, resp string) {
				require.NotEmpty(t, gjson.Parse(resp).Get("data").String())
			},
		},
	}
	for _, tt := range testcases {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mock != nil {
				tt.mock()
			}
			w := httptest.NewRecorder()
			gin.SetMode(gin.TestMode)
			_, eng := gin.CreateTestContext(w)

			NewProductListing(logger.Get(), mockSvc).RegisterRoutes(eng.RouterGroup.Group(""))

			req, _ := http.NewRequestWithContext(context.Background(), http.MethodGet, tt.path, nil)
			eng.ServeHTTP(w, req)
			resp := w.Body.String()
			require.Equal(t, tt.expCode, w.Code, resp)
			if tt.respCheck != nil {
				tt.respCheck(t, resp)
			}
		})
	}
}

func TestProductListing_delete(t *testing.T) {
	loadConfig(t)
	mockSvc := new(product_listing.MockProductListingService)

	testcases := []struct {
		name      string
		path      string
		mock      func()
		expCode   int
		respCheck func(*testing.T, string)
	}{
		{
			name:    "ID short case",
			path:    "/product-listings/123",
			expCode: http.StatusUnprocessableEntity,
		},
		{
			name:    "ID long case",
			path:    "/product-listings/" + strings.Repeat("x", 33),
			expCode: http.StatusUnprocessableEntity,
		},
		{
			name:    "Not found case",
			path:    "/product-listings/" + strings.Repeat("x", 32),
			expCode: http.StatusNotFound,
			mock: func() {
				mockSvc.On("GetByID", mock.Anything, mock.Anything).Return(product_listing.ProductListing{}, product_listing_domain.ErrNotFound).Once()
			},
		},
		{
			name:    "Service error case",
			path:    "/product-listings/" + strings.Repeat("x", 32),
			expCode: http.StatusInternalServerError,
			mock: func() {
				mockSvc.On("GetByID", mock.Anything, mock.Anything).Return(product_listing.ProductListing{}, nil).Once()
				mockSvc.On("Delete", mock.Anything, mock.Anything, mock.Anything).Return(errors.New("service error")).Once()
			},
		},
		{
			name: "Good case",
			path: "/product-listings/" + strings.Repeat("x", 32),
			mock: func() {
				mockSvc.On("Delete", mock.Anything, mock.Anything, mock.Anything).Return(nil).Once()
				mockSvc.On("GetByID", mock.Anything, mock.Anything).Return(product_listing.ProductListing{}, nil).Once()
			},
			expCode: http.StatusOK,
			respCheck: func(t *testing.T, resp string) {
				require.NotEmpty(t, gjson.Parse(resp).Get("data").String())
			},
		},
	}
	for _, tt := range testcases {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mock != nil {
				tt.mock()
			}
			w := httptest.NewRecorder()
			gin.SetMode(gin.TestMode)
			_, eng := gin.CreateTestContext(w)

			NewProductListing(logger.Get(), mockSvc).RegisterRoutes(eng.RouterGroup.Group(""))

			req, _ := http.NewRequestWithContext(context.Background(), http.MethodDelete, tt.path, nil)
			eng.ServeHTTP(w, req)
			resp := w.Body.String()
			require.Equal(t, tt.expCode, w.Code, resp)
			if tt.respCheck != nil {
				tt.respCheck(t, resp)
			}
		})
	}
}

func TestProductListing_update(t *testing.T) {
	loadConfig(t)
	mockSvc := new(product_listing.MockProductListingService)

	testcases := []struct {
		name      string
		path      string
		mock      func()
		expCode   int
		body      []byte
		respCheck func(*testing.T, string)
	}{
		{
			name:    "ID short case",
			path:    "/product-listings/123",
			expCode: http.StatusUnprocessableEntity,
		},
		{
			name:    "ID long case",
			path:    "/product-listings/" + strings.Repeat("x", 33),
			expCode: http.StatusUnprocessableEntity,
		},
		{

			name:    "Body is nil case",
			path:    "/product-listings/" + strings.Repeat("x", 32),
			expCode: http.StatusUnprocessableEntity,
			body:    nil,
		},
		{
			name: "Service error case",
			path: "/product-listings/" + strings.Repeat("x", 32),
			mock: func() {
				mockSvc.On("Update", mock.Anything, mock.Anything, mock.Anything).Return(product_listing.ProductListing{}, errors.New("update error")).Once()
			},
			expCode: http.StatusInternalServerError,
			body:    []byte(`{}`),
		},
		{
			name: "Good case",
			path: "/product-listings/" + strings.Repeat("x", 32),
			mock: func() {
				mockSvc.On("Update", mock.Anything, mock.Anything, mock.Anything).Return(product_listing.ProductListing{}, nil).Once()
			},
			expCode: http.StatusOK,
			respCheck: func(t *testing.T, resp string) {
				require.NotEmpty(t, gjson.Parse(resp).Get("data").String())
			},
			body: []byte(`{}`),
		},
	}
	for _, tt := range testcases {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mock != nil {
				tt.mock()
			}
			w := httptest.NewRecorder()
			gin.SetMode(gin.TestMode)
			_, eng := gin.CreateTestContext(w)

			NewProductListing(logger.Get(), mockSvc).RegisterRoutes(eng.RouterGroup.Group(""))

			req, _ := http.NewRequestWithContext(context.Background(), http.MethodPut, tt.path, bytes.NewBuffer(tt.body))
			eng.ServeHTTP(w, req)
			resp := w.Body.String()
			require.Equal(t, tt.expCode, w.Code, resp)
			if tt.respCheck != nil {
				tt.respCheck(t, resp)
			}
		})
	}
}

func TestProductListing_updatePublishState(t *testing.T) {
	loadConfig(t)
	mockSvc := new(product_listing.MockProductListingService)

	testcases := []struct {
		name      string
		path      string
		mock      func()
		expCode   int
		body      []byte
		respCheck func(*testing.T, string)
	}{
		{
			name:    "ID short case",
			path:    "/product-listings/123/update-publish-state",
			expCode: http.StatusUnprocessableEntity,
		},
		{
			name:    "ID long case",
			path:    "/product-listings/" + strings.Repeat("x", 33) + "/update-publish-state",
			expCode: http.StatusUnprocessableEntity,
		},
		{

			name:    "Body is nil case",
			path:    "/product-listings/" + strings.Repeat("x", 32) + "/update-publish-state",
			expCode: http.StatusUnprocessableEntity,
			body:    nil,
		},
		{
			name: "Service error case",
			path: "/product-listings/" + strings.Repeat("x", 32) + "/update-publish-state",
			mock: func() {
				mockSvc.On("UpdatePublishState", mock.Anything, mock.Anything, mock.Anything).Return(product_listing.ProductListing{}, errors.New("service error")).Once()
			},
			expCode: http.StatusInternalServerError,
			body:    []byte(`{}`),
		},
		{
			name: "Good case",
			path: "/product-listings/" + strings.Repeat("x", 32) + "/update-publish-state",
			mock: func() {
				mockSvc.On("UpdatePublishState", mock.Anything, mock.Anything, mock.Anything).Return(product_listing.ProductListing{}, nil).Once()
			},
			expCode: http.StatusOK,
			respCheck: func(t *testing.T, resp string) {
				require.NotEmpty(t, gjson.Parse(resp).Get("data").String())
			},
			body: []byte(`{}`),
		},
	}
	for _, tt := range testcases {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mock != nil {
				tt.mock()
			}
			w := httptest.NewRecorder()
			gin.SetMode(gin.TestMode)
			_, eng := gin.CreateTestContext(w)

			NewProductListing(logger.Get(), mockSvc).RegisterRoutes(eng.RouterGroup.Group(""))

			req, _ := http.NewRequestWithContext(context.Background(), http.MethodPost, tt.path, bytes.NewBuffer(tt.body))
			eng.ServeHTTP(w, req)
			resp := w.Body.String()
			require.Equal(t, tt.expCode, w.Code, resp)
			if tt.respCheck != nil {
				tt.respCheck(t, resp)
			}
		})
	}
}

func TestProductListing_updateAuditState(t *testing.T) {
	loadConfig(t)
	mockSvc := new(product_listing.MockProductListingService)

	testcases := []struct {
		name      string
		path      string
		mock      func()
		expCode   int
		body      []byte
		respCheck func(*testing.T, string)
	}{
		{
			name:    "ID short case",
			path:    "/product-listings/123/update-audit-state",
			expCode: http.StatusUnprocessableEntity,
		},
		{
			name:    "ID long case",
			path:    "/product-listings/" + strings.Repeat("x", 33) + "/update-audit-state",
			expCode: http.StatusUnprocessableEntity,
		},
		{

			name:    "Body is nil case",
			path:    "/product-listings/" + strings.Repeat("x", 32) + "/update-audit-state",
			expCode: http.StatusUnprocessableEntity,
			body:    nil,
		},
		{
			name: "Service error case",
			path: "/product-listings/" + strings.Repeat("x", 32) + "/update-audit-state",
			mock: func() {
				mockSvc.On("UpdateAuditState", mock.Anything, mock.Anything, mock.Anything).Return(product_listing.ProductListing{}, errors.New("service error")).Once()
			},
			expCode: http.StatusInternalServerError,
			body:    []byte(`{}`),
		},
		{
			name: "Good case",
			path: "/product-listings/" + strings.Repeat("x", 32) + "/update-audit-state",
			mock: func() {
				mockSvc.On("UpdateAuditState", mock.Anything, mock.Anything, mock.Anything).Return(product_listing.ProductListing{}, nil).Once()
			},
			expCode: http.StatusOK,
			respCheck: func(t *testing.T, resp string) {
				require.NotEmpty(t, gjson.Parse(resp).Get("data").String())
			},
			body: []byte(`{}`),
		},
	}
	for _, tt := range testcases {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mock != nil {
				tt.mock()
			}
			w := httptest.NewRecorder()
			gin.SetMode(gin.TestMode)
			_, eng := gin.CreateTestContext(w)

			NewProductListing(logger.Get(), mockSvc).RegisterRoutes(eng.RouterGroup.Group(""))

			req, _ := http.NewRequestWithContext(context.Background(), http.MethodPost, tt.path, bytes.NewBuffer(tt.body))
			eng.ServeHTTP(w, req)
			resp := w.Body.String()
			require.Equal(t, tt.expCode, w.Code, resp)
			if tt.respCheck != nil {
				tt.respCheck(t, resp)
			}
		})
	}
}

func TestProductListing_duplicate(t *testing.T) {
	loadConfig(t)
	mockSvc := new(product_listing.MockProductListingService)

	testcases := []struct {
		name      string
		path      string
		mock      func()
		expCode   int
		respCheck func(*testing.T, string)
	}{
		{
			name:    "ID short case",
			path:    "/product-listings/123/duplicate",
			expCode: http.StatusUnprocessableEntity,
		},
		{
			name:    "ID long case",
			path:    "/product-listings/" + strings.Repeat("x", 33) + "/duplicate",
			expCode: http.StatusUnprocessableEntity,
		},
		{
			name: "Service error case",
			path: "/product-listings/" + strings.Repeat("x", 32) + "/duplicate",
			mock: func() {
				mockSvc.On("Duplicate", mock.Anything, mock.Anything, mock.Anything).Return(product_listing.ProductListing{}, errors.New("service error")).Once()
			},
			expCode: http.StatusInternalServerError,
		},
		{
			name: "Good case",
			path: "/product-listings/" + strings.Repeat("x", 32) + "/duplicate",
			mock: func() {
				mockSvc.On("Duplicate", mock.Anything, mock.Anything).Return(product_listing.ProductListing{}, nil).Once()
			},
			expCode: http.StatusOK,
			respCheck: func(t *testing.T, resp string) {
				require.NotEmpty(t, gjson.Parse(resp).Get("data").String())
			},
		},
	}
	for _, tt := range testcases {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mock != nil {
				tt.mock()
			}
			w := httptest.NewRecorder()
			gin.SetMode(gin.TestMode)
			_, eng := gin.CreateTestContext(w)

			NewProductListing(logger.Get(), mockSvc).RegisterRoutes(eng.RouterGroup.Group(""))

			req, _ := http.NewRequestWithContext(context.Background(), http.MethodPost, tt.path, nil)
			eng.ServeHTTP(w, req)
			resp := w.Body.String()
			require.Equal(t, tt.expCode, w.Code, resp)
			if tt.respCheck != nil {
				tt.respCheck(t, resp)
			}
		})
	}
}

func TestProductListing_listAuditVersions(t *testing.T) {
	loadConfig(t)
	mockSvc := new(product_listing.MockProductListingService)

	testcases := []struct {
		name      string
		path      string
		mock      func()
		expCode   int
		respCheck func(*testing.T, string)
	}{
		{
			name:    "ID short case",
			path:    "/product-listings/123/audit-versions",
			expCode: http.StatusUnprocessableEntity,
		},
		{
			name:    "ID long case",
			path:    "/product-listings/" + strings.Repeat("x", 33) + "/audit-versions",
			expCode: http.StatusUnprocessableEntity,
		},
		{
			name: "Service error case",
			path: "/product-listings/" + strings.Repeat("x", 32) + "/audit-versions",
			mock: func() {
				mockSvc.On("ListAuditVersions", mock.Anything, mock.Anything).Return([]*product_listing.AuditVersion{}, errors.New("service error")).Once()
			},
			expCode: http.StatusInternalServerError,
		},
		{
			name: "Good case",
			path: "/product-listings/" + strings.Repeat("x", 32) + "/audit-versions",
			mock: func() {
				mockSvc.On("ListAuditVersions", mock.Anything, mock.Anything).Return([]*product_listing.AuditVersion{}, nil).Once()
			},
			expCode: http.StatusOK,
			respCheck: func(t *testing.T, resp string) {
				require.NotEmpty(t, gjson.Parse(resp).Get("data").String())
			},
		},
	}
	for _, tt := range testcases {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mock != nil {
				tt.mock()
			}
			w := httptest.NewRecorder()
			gin.SetMode(gin.TestMode)
			_, eng := gin.CreateTestContext(w)

			NewProductListing(logger.Get(), mockSvc).RegisterRoutes(eng.RouterGroup.Group(""))

			req, _ := http.NewRequestWithContext(context.Background(), http.MethodGet, tt.path, nil)
			eng.ServeHTTP(w, req)
			resp := w.Body.String()
			require.Equal(t, tt.expCode, w.Code, resp)
			if tt.respCheck != nil {
				tt.respCheck(t, resp)
			}
		})
	}
}

func loadConfig(t *testing.T) {
	configs := new(config.Config)
	_, err := cfg.LoadViperConfig(configs, func(v *viper.Viper) { v.AddConfigPath("../../../../cmd/apiserver/conf") })
	require.NoError(t, err)
	configs.DynamicConfigs.ElasticsearchAuth = &config.ElasticsearchAuthConfig{
		Host: "http://localhost:9200",
	}
	require.NoError(t, datastore.Init(configs))
}

func TestProductListing_generatePreview(t *testing.T) {
	loadConfig(t)
	mockSvc := new(product_listing.MockProductListingService)

	testcases := []struct {
		name      string
		path      string
		mock      func()
		expCode   int
		body      []byte
		respCheck func(*testing.T, string)
	}{
		{
			name:    "ID short case",
			path:    "/product-listings/123/preview",
			expCode: http.StatusUnprocessableEntity,
		},
		{
			name:    "ID long case",
			path:    "/product-listings/" + strings.Repeat("x", 33) + "/preview",
			expCode: http.StatusUnprocessableEntity,
		},
		{
			name:    "Body is nil case",
			path:    "/product-listings/" + strings.Repeat("x", 32) + "/preview",
			expCode: http.StatusBadRequest,
			body:    nil,
		},
		{
			name: "Service error case",
			path: "/product-listings/" + strings.Repeat("x", 32) + "/preview",
			mock: func() {
				mockSvc.On("GeneratePreview", mock.Anything, mock.Anything).Return(&product_listing.ProductListing{
					ProductsCenterProduct: product_listing.ProductsCenterProduct{
						ID: strings.Repeat("x", 32),
					}}, errors.New("service error")).Once()
			},
			expCode: http.StatusInternalServerError,
			body:    []byte(fmt.Sprintf(`{"id": "%s","products_center_product":{"id":"%s"}}`, strings.Repeat("x", 32), strings.Repeat("x", 32))),
		},
		{
			name: "Good case",
			path: "/product-listings/" + strings.Repeat("x", 32) + "/preview",
			mock: func() {
				mockSvc.On("GeneratePreview", mock.Anything, mock.Anything).Return(&product_listing.ProductListing{
					ProductsCenterProduct: product_listing.ProductsCenterProduct{
						ID: strings.Repeat("x", 32),
					},
				}, nil).Once()
			},
			expCode: http.StatusOK,
			respCheck: func(t *testing.T, resp string) {
				require.NotEmpty(t, gjson.Parse(resp).Get("data").String())
			},
			body: []byte(fmt.Sprintf(`{"id": "%s","products_center_product":{"id":"%s"}}`, strings.Repeat("x", 32), strings.Repeat("x", 32))),
		},
	}
	for _, tt := range testcases {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mock != nil {
				tt.mock()
			}
			w := httptest.NewRecorder()
			gin.SetMode(gin.TestMode)
			_, eng := gin.CreateTestContext(w)

			NewProductListing(logger.Get(), mockSvc).RegisterRoutes(eng.RouterGroup.Group(""))
			req, _ := http.NewRequestWithContext(context.Background(), http.MethodPost, tt.path, bytes.NewBuffer(tt.body))
			eng.ServeHTTP(w, req)
			resp := w.Body.String()
			require.Equal(t, tt.expCode, w.Code, resp)
			if tt.respCheck != nil {
				tt.respCheck(t, resp)
			}
		})

	}
}

func TestESProxyProductListingCount(t *testing.T) {
	loadConfig(t)
	mockSvc := new(product_listing.MockProductListingService)

	testcases := []struct {
		name      string
		path      string
		mock      func()
		expCode   int
		body      []byte
		respCheck func(*testing.T, string)
	}{
		{
			name: "Body is nil case",
			path: "/internal/product-listings/es-proxy/count",
			mock: func() {
				mockSvc.On("ESProxyProductListingCount", mock.Anything, mock.Anything).Return(int64(1), nil).Once()
			},
			expCode:   http.StatusUnprocessableEntity,
			respCheck: func(*testing.T, string) {},
		},
		{
			name: "Service error case: not base64 encoded",
			path: "/internal/product-listings/es-proxy/count?query=123",
			mock: func() {
				mockSvc.On("ESProxyProductListingCount", mock.Anything, mock.Anything).Return(int64(1), nil).Once()
			},
			expCode:   http.StatusInternalServerError,
			respCheck: func(*testing.T, string) {},
		},
		{
			name: "Good case",
			path: "/internal/product-listings/es-proxy/count?query=" + base64.StdEncoding.EncodeToString([]byte(`{
			  "query": {
				"bool": {
				  "filter": [
					{
					  "term": {
						"deleted": false
					  }
					},
					{
					  "term": {
						"organization_id": "3ae8df847ca34499b22653ee1581d2a3"
					  }
					},
					{
					  "terms": {
						"sales_channel_product_id": [
						  "1729464683463348469"
						]
					  }
					}
				  ]
				}
			  }
			}`)),
			mock: func() {
				mockSvc.On("ESProxyProductListingCount", mock.Anything, mock.Anything).Return(int64(1), nil).Once()
			},
			expCode: http.StatusOK,
			respCheck: func(t *testing.T, resp string) {
				require.Equal(t, 1, int(gjson.Get(resp, "data.count").Int()))
			},
		},
	}
	for _, tt := range testcases {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mock != nil {
				tt.mock()
			}
			w := httptest.NewRecorder()
			gin.SetMode(gin.TestMode)
			_, eng := gin.CreateTestContext(w)

			NewProductListing(logger.Get(), mockSvc).RegisterRoutes(eng.RouterGroup.Group(""))
			req, _ := http.NewRequestWithContext(context.Background(), http.MethodGet, tt.path, bytes.NewBuffer(tt.body))
			eng.ServeHTTP(w, req)
			resp := w.Body.String()
			require.Equal(t, tt.expCode, w.Code, resp)
			if tt.respCheck != nil {
				tt.respCheck(t, resp)
			}
		})
	}
}

func TestProductListing_matchProduct(t *testing.T) {
	mockSvc := new(product_listing.MockProductListingService)

	testcases := []struct {
		name      string
		path      string
		mock      func()
		expCode   int
		respCheck func(*testing.T, string)
	}{
		{
			name:    "ID short case",
			path:    "/internal/product-listings/123/match-product",
			expCode: http.StatusUnprocessableEntity,
		},
		{
			name:    "ID long case",
			path:    "/internal/product-listings/" + strings.Repeat("x", 33) + "/match-product",
			expCode: http.StatusUnprocessableEntity,
		},
		{
			name: "Service error case",
			path: "/internal/product-listings/" + strings.Repeat("x", 32) + "/match-product",
			mock: func() {
				mockSvc.On("MatchProduct", mock.Anything, mock.Anything).Return(product_listing_domain.ProductListing{}, errors.New("service error")).Once()
			},
			expCode: http.StatusInternalServerError,
		},
		{
			name: "Good case",
			path: "/internal/product-listings/" + strings.Repeat("x", 32) + "/match-product",
			mock: func() {
				mockSvc.On("MatchProduct", mock.Anything, mock.Anything).Return(product_listing_domain.ProductListing{}, nil).Once()
			},
			expCode: http.StatusOK,
			respCheck: func(t *testing.T, resp string) {
				require.NotEmpty(t, gjson.Parse(resp).Get("data").String())
			},
		},
	}

	for _, tt := range testcases {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mock != nil {
				tt.mock()
			}
			w := httptest.NewRecorder()
			gin.SetMode(gin.TestMode)
			_, eng := gin.CreateTestContext(w)

			NewProductListing(logger.Get(), mockSvc).RegisterRoutes(eng.RouterGroup.Group(""))

			req, _ := http.NewRequestWithContext(context.Background(), http.MethodGet, tt.path, nil)
			eng.ServeHTTP(w, req)
			resp := w.Body.String()
			require.Equal(t, tt.expCode, w.Code, resp)
			if tt.respCheck != nil {
				tt.respCheck(t, resp)
			}
		})
	}
}

func TestUnAckNeedWaring(t *testing.T) {
	tests := []struct {
		name     string
		err      error
		expected bool
	}{
		{
			name:     "nil错误",
			err:      nil,
			expected: false,
		},
		{
			name:     "ErrPublishStateInUpdateNotAck错误",
			err:      product_listing_domain.ErrPublishStateInUpdateNotAck,
			expected: true,
		},
		{
			name:     "redsync.ErrFailed错误",
			err:      redsync.ErrFailed,
			expected: true,
		},
		{
			name:     "包装的ErrPublishStateInUpdateNotAck错误",
			err:      errors.Wrap(product_listing_domain.ErrPublishStateInUpdateNotAck, "wrapped error"),
			expected: true,
		},
		{
			name:     "包装的redsync.ErrFailed错误",
			err:      errors.Wrap(redsync.ErrFailed, "wrapped error"),
			expected: true,
		},
		{
			name:     "多层包装的ErrPublishStateInUpdateNotAck错误",
			err:      errors.Wrap(errors.Wrap(product_listing_domain.ErrPublishStateInUpdateNotAck, "inner wrap"), "outer wrap"),
			expected: true,
		},
		{
			name:     "多层包装的redsync.ErrFailed错误",
			err:      errors.Wrap(errors.Wrap(redsync.ErrFailed, "inner wrap"), "outer wrap"),
			expected: true,
		},
		{
			name:     "ErrNotFound错误",
			err:      product_listing_domain.ErrNotFound,
			expected: false,
		},
		{
			name:     "ErrPublishStateInUpdate错误",
			err:      product_listing_domain.ErrPublishStateInUpdate,
			expected: false,
		},
		{
			name:     "ErrInvalidPublishState错误",
			err:      product_listing_domain.ErrInvalidPublishState,
			expected: false,
		},
		{
			name:     "ErrVersionConflict错误",
			err:      product_listing_domain.ErrVersionConflict,
			expected: false,
		},
		{
			name:     "connectors.ErrConnectionNotFound错误",
			err:      connectors.ErrConnectionNotFound,
			expected: false,
		},
		{
			name:     "calculators.ErrPriceNotFound错误",
			err:      calculators.ErrPriceNotFound,
			expected: false,
		},
		{
			name:     "普通错误",
			err:      errors.New("generic error"),
			expected: false,
		},
		{
			name:     "自定义错误",
			err:      errors.New("custom error message"),
			expected: false,
		},
		{
			name:     "包装的普通错误",
			err:      errors.Wrap(errors.New("generic error"), "wrapped"),
			expected: false,
		},
		{
			name:     "context.Canceled错误",
			err:      context.Canceled,
			expected: false,
		},
		{
			name:     "context.DeadlineExceeded错误",
			err:      context.DeadlineExceeded,
			expected: false,
		},
		{
			name:     "fmt.Errorf错误",
			err:      fmt.Errorf("formatted error: %s", "test"),
			expected: false,
		},
		{
			name:     "混合包装错误（包含需要警告的错误）",
			err:      errors.Wrap(product_listing_domain.ErrPublishStateInUpdateNotAck, "additional context"),
			expected: true,
		},
		{
			name:     "混合包装错误（不包含需要警告的错误）",
			err:      errors.Wrap(product_listing_domain.ErrNotFound, "additional context"),
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := unAckNeedWaring(tt.err)
			require.Equal(t, tt.expected, result)
		})
	}
}

func TestProductListing_CheckAndFixStatus(t *testing.T) {
	mockSvc := new(product_listing.MockProductListingService)

	testcases := []struct {
		name      string
		path      string
		mock      func()
		expCode   int
		respCheck func(*testing.T, string)
	}{
		{
			name:    "默认参数执行",
			path:    "/internal/product-listings/check-and-fix-status",
			expCode: http.StatusOK,
			mock: func() {
				mockSvc.On("CheckAndFixStatus", mock.Anything, false, 0).Return(nil).Once()
			},
			respCheck: func(t *testing.T, resp string) {
				require.Equal(t, "ok", gjson.Get(resp, "data").String())
			},
		},
		{
			name:    "dry_run为true",
			path:    "/internal/product-listings/check-and-fix-status?dry_run=true",
			expCode: http.StatusOK,
			mock: func() {
				mockSvc.On("CheckAndFixStatus", mock.Anything, true, 0).Return(nil).Once()
			},
			respCheck: func(t *testing.T, resp string) {
				require.Equal(t, "ok", gjson.Get(resp, "data").String())
			},
		},
		{
			name:    "dry_run为TRUE（大小写不敏感）",
			path:    "/internal/product-listings/check-and-fix-status?dry_run=TRUE",
			expCode: http.StatusOK,
			mock: func() {
				mockSvc.On("CheckAndFixStatus", mock.Anything, true, 0).Return(nil).Once()
			},
			respCheck: func(t *testing.T, resp string) {
				require.Equal(t, "ok", gjson.Get(resp, "data").String())
			},
		},
		{
			name:    "dry_run为false",
			path:    "/internal/product-listings/check-and-fix-status?dry_run=false",
			expCode: http.StatusOK,
			mock: func() {
				mockSvc.On("CheckAndFixStatus", mock.Anything, false, 0).Return(nil).Once()
			},
			respCheck: func(t *testing.T, resp string) {
				require.Equal(t, "ok", gjson.Get(resp, "data").String())
			},
		},
		{
			name:    "设置processed_limit_count为100",
			path:    "/internal/product-listings/check-and-fix-status?processed_limit_count=100",
			expCode: http.StatusOK,
			mock: func() {
				mockSvc.On("CheckAndFixStatus", mock.Anything, false, 100).Return(nil).Once()
			},
			respCheck: func(t *testing.T, resp string) {
				require.Equal(t, "ok", gjson.Get(resp, "data").String())
			},
		},
		{
			name:    "设置processed_limit_count为0",
			path:    "/internal/product-listings/check-and-fix-status?processed_limit_count=0",
			expCode: http.StatusOK,
			mock: func() {
				mockSvc.On("CheckAndFixStatus", mock.Anything, false, 0).Return(nil).Once()
			},
			respCheck: func(t *testing.T, resp string) {
				require.Equal(t, "ok", gjson.Get(resp, "data").String())
			},
		},
		{
			name:    "设置processed_limit_count为负数",
			path:    "/internal/product-listings/check-and-fix-status?processed_limit_count=-10",
			expCode: http.StatusOK,
			mock: func() {
				mockSvc.On("CheckAndFixStatus", mock.Anything, false, -10).Return(nil).Once()
			},
			respCheck: func(t *testing.T, resp string) {
				require.Equal(t, "ok", gjson.Get(resp, "data").String())
			},
		},
		{
			name:    "设置processed_limit_count为非数字",
			path:    "/internal/product-listings/check-and-fix-status?processed_limit_count=abc",
			expCode: http.StatusOK,
			mock: func() {
				mockSvc.On("CheckAndFixStatus", mock.Anything, false, 0).Return(nil).Once()
			},
			respCheck: func(t *testing.T, resp string) {
				require.Equal(t, "ok", gjson.Get(resp, "data").String())
			},
		},
		{
			name:    "同时设置dry_run和processed_limit_count",
			path:    "/internal/product-listings/check-and-fix-status?dry_run=true&processed_limit_count=50",
			expCode: http.StatusOK,
			mock: func() {
				mockSvc.On("CheckAndFixStatus", mock.Anything, true, 50).Return(nil).Once()
			},
			respCheck: func(t *testing.T, resp string) {
				require.Equal(t, "ok", gjson.Get(resp, "data").String())
			},
		},
		{
			name:    "服务层错误",
			path:    "/internal/product-listings/check-and-fix-status",
			expCode: http.StatusInternalServerError,
			mock: func() {
				mockSvc.On("CheckAndFixStatus", mock.Anything, false, 0).Return(errors.New("service error")).Once()
			},
		},
		{
			name:    "设置processed_limit_count为大数值",
			path:    "/internal/product-listings/check-and-fix-status?processed_limit_count=999999",
			expCode: http.StatusOK,
			mock: func() {
				mockSvc.On("CheckAndFixStatus", mock.Anything, false, 999999).Return(nil).Once()
			},
			respCheck: func(t *testing.T, resp string) {
				require.Equal(t, "ok", gjson.Get(resp, "data").String())
			},
		},
		{
			name:    "dry_run为其他值",
			path:    "/internal/product-listings/check-and-fix-status?dry_run=yes",
			expCode: http.StatusOK,
			mock: func() {
				mockSvc.On("CheckAndFixStatus", mock.Anything, false, 0).Return(nil).Once()
			},
			respCheck: func(t *testing.T, resp string) {
				require.Equal(t, "ok", gjson.Get(resp, "data").String())
			},
		},
		{
			name:    "空字符串参数",
			path:    "/internal/product-listings/check-and-fix-status?dry_run=&processed_limit_count=",
			expCode: http.StatusOK,
			mock: func() {
				mockSvc.On("CheckAndFixStatus", mock.Anything, false, 0).Return(nil).Once()
			},
			respCheck: func(t *testing.T, resp string) {
				require.Equal(t, "ok", gjson.Get(resp, "data").String())
			},
		},
		{
			name:    "包含特殊字符的参数",
			path:    "/internal/product-listings/check-and-fix-status?dry_run=true&processed_limit_count=100&extra_param=test%20value",
			expCode: http.StatusOK,
			mock: func() {
				mockSvc.On("CheckAndFixStatus", mock.Anything, true, 100).Return(nil).Once()
			},
			respCheck: func(t *testing.T, resp string) {
				require.Equal(t, "ok", gjson.Get(resp, "data").String())
			},
		},
	}

	for _, tt := range testcases {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mock != nil {
				tt.mock()
			}
			w := httptest.NewRecorder()
			gin.SetMode(gin.TestMode)
			_, eng := gin.CreateTestContext(w)

			NewProductListing(logger.Get(), mockSvc).RegisterRoutes(eng.RouterGroup.Group(""))

			req, _ := http.NewRequestWithContext(context.Background(), http.MethodGet, tt.path, nil)
			eng.ServeHTTP(w, req)
			resp := w.Body.String()
			require.Equal(t, tt.expCode, w.Code, resp)
			if tt.respCheck != nil {
				tt.respCheck(t, resp)
			}
		})
	}
}

func TestProductListing_updateRelations(t *testing.T) {
	loadConfig(t)
	mockSvc := new(product_listing.MockProductListingService)

	testcases := []struct {
		name      string
		path      string
		mock      func()
		expCode   int
		body      []byte
		respCheck func(*testing.T, string)
	}{
		{
			name:    "ID short case",
			path:    "/internal/product-listings/123/update-relations",
			expCode: http.StatusUnprocessableEntity,
		},
		{
			name:    "ID long case",
			path:    "/internal/product-listings/" + strings.Repeat("x", 33) + "/update-relations",
			expCode: http.StatusUnprocessableEntity,
		},
		{
			name:    "Body is nil case",
			path:    "/internal/product-listings/" + strings.Repeat("x", 32) + "/update-relations",
			expCode: http.StatusUnprocessableEntity,
			body:    nil,
		},
		{
			name: "Service error case",
			path: "/internal/product-listings/" + strings.Repeat("x", 32) + "/update-relations",
			mock: func() {
				mockSvc.On("UpdateRelations", mock.Anything, mock.Anything, mock.Anything).Return(product_listing.ProductListing{}, errors.New("service error")).Once()
			},
			expCode: http.StatusInternalServerError,
			body:    []byte(`{}`),
		},
		{
			name: "needAckError case",
			path: "/internal/product-listings/" + strings.Repeat("x", 32) + "/update-relations",
			mock: func() {
				mockSvc.On("UpdateRelations", mock.Anything, mock.Anything, mock.Anything).Return(product_listing.ProductListing{}, product_listing_domain.ErrNotFound).Once()
			},
			expCode: http.StatusUnprocessableEntity,
			body:    []byte(`{}`),
		},
		{
			name: "Good case",
			path: "/internal/product-listings/" + strings.Repeat("x", 32) + "/update-relations",
			mock: func() {
				mockSvc.On("UpdateRelations", mock.Anything, mock.Anything, mock.Anything).Return(product_listing.ProductListing{}, nil).Once()
			},
			expCode: http.StatusOK,
			body:    []byte(`{}`),
			respCheck: func(t *testing.T, resp string) {
				require.NotEmpty(t, gjson.Parse(resp).Get("data").String())
			},
		},
	}
	for _, tt := range testcases {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mock != nil {
				tt.mock()
			}
			w := httptest.NewRecorder()
			gin.SetMode(gin.TestMode)
			_, eng := gin.CreateTestContext(w)

			NewProductListing(logger.Get(), mockSvc).RegisterRoutes(eng.RouterGroup.Group(""))

			req, _ := http.NewRequestWithContext(context.Background(), http.MethodPost, tt.path, bytes.NewBuffer(tt.body))
			eng.ServeHTTP(w, req)
			resp := w.Body.String()
			require.Equal(t, tt.expCode, w.Code, resp)
			if tt.respCheck != nil {
				tt.respCheck(t, resp)
			}
		})
	}
}

func TestProductListing_pushToChannel(t *testing.T) {
	loadConfig(t)
	mockSvc := new(product_listing.MockProductListingService)

	testcases := []struct {
		name      string
		path      string
		mock      func()
		expCode   int
		body      []byte
		respCheck func(*testing.T, string)
	}{
		{
			name:    "Body is nil case",
			path:    "/internal/product-listings/push-to-channel",
			expCode: http.StatusUnprocessableEntity,
			body:    nil,
		},
		{
			name:    "Invalid JSON body case",
			path:    "/internal/product-listings/push-to-channel",
			expCode: http.StatusUnprocessableEntity,
			body:    []byte(`{invalid json}`),
		},
		{
			name: "Service ACK error case",
			path: "/internal/product-listings/push-to-channel",
			mock: func() {
				mockSvc.On("PushToChannel", mock.Anything, mock.Anything).Return(product_listing.ProductListing{}, product_listing_domain.ErrNotFound).Once()
			},
			expCode: http.StatusUnprocessableEntity,
			body:    []byte(`{"sales_channel": {"platform": "shopify", "store_key": "test"}, "organization": {"id": "org123"}, "products_center_product": {"id": "pc123"}, "product": {"id": "prod123"}, "relations": [], "ready": {}, "feed_customization_params": {}}`),
		},
		{
			name: "Service error case",
			path: "/internal/product-listings/push-to-channel",
			mock: func() {
				mockSvc.On("PushToChannel", mock.Anything, mock.Anything).Return(product_listing.ProductListing{}, errors.New("service error")).Once()
			},
			expCode: http.StatusInternalServerError,
			body:    []byte(`{"sales_channel": {"platform": "shopify", "store_key": "test"}, "organization": {"id": "org123"}, "products_center_product": {"id": "pc123"}, "product": {"id": "prod123"}, "relations": [], "ready": {}, "feed_customization_params": {}}`),
		},
		{
			name: "Good case",
			path: "/internal/product-listings/push-to-channel",
			mock: func() {
				mockSvc.On("PushToChannel", mock.Anything, mock.Anything).Return(product_listing.ProductListing{}, nil).Once()
			},
			expCode: http.StatusOK,
			respCheck: func(t *testing.T, resp string) {
				require.NotEmpty(t, gjson.Parse(resp).Get("data").String())
			},
			body: []byte(`{"sales_channel": {"platform": "shopify", "store_key": "test"}, "organization": {"id": "org123"}, "products_center_product": {"id": "pc123"}, "product": {"id": "prod123"}, "relations": [], "ready": {}, "feed_customization_params": {}}`),
		},
	}
	for _, tt := range testcases {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mock != nil {
				tt.mock()
			}
			w := httptest.NewRecorder()
			gin.SetMode(gin.TestMode)
			_, eng := gin.CreateTestContext(w)

			NewProductListing(logger.Get(), mockSvc).RegisterRoutes(eng.RouterGroup.Group(""))

			req, _ := http.NewRequestWithContext(context.Background(), http.MethodPost, tt.path, bytes.NewBuffer(tt.body))
			eng.ServeHTTP(w, req)
			resp := w.Body.String()
			require.Equal(t, tt.expCode, w.Code, resp)
			if tt.respCheck != nil {
				tt.respCheck(t, resp)
			}
		})
	}
}

func TestProductListing_internalList(t *testing.T) {
	loadConfig(t)
	mockSvc := new(product_listing.MockProductListingService)

	testcases := []struct {
		name      string
		path      string
		mock      func()
		expCode   int
		respCheck func(*testing.T, string)
	}{
		{
			name:    "Missing required params case",
			path:    "/internal/product-listings/",
			expCode: http.StatusUnprocessableEntity,
		},
		{
			name:    "Empty IDs and organization_id case",
			path:    "/internal/product-listings/?limit=10",
			expCode: http.StatusUnprocessableEntity,
		},
		{
			name: "Service error case",
			path: "/internal/product-listings/?organization_id=org123&limit=10",
			mock: func() {
				mockSvc.On("List", mock.Anything, mock.Anything).Return([]*product_listing.ProductListing{}, errors.New("service error")).Once()
			},
			expCode: http.StatusInternalServerError,
		},
		{
			name: "Good case with organization_id",
			path: "/internal/product-listings/?organization_id=org123&limit=10",
			mock: func() {
				mockSvc.On("List", mock.Anything, mock.Anything).Return([]*product_listing.ProductListing{}, nil).Once()
			},
			expCode: http.StatusOK,
			respCheck: func(t *testing.T, resp string) {
				require.NotEmpty(t, gjson.Parse(resp).Get("data").String())
			},
		},
		{
			name: "Good case with IDs",
			path: "/internal/product-listings/?ids=id1,id2,id3&limit=10",
			mock: func() {
				mockSvc.On("List", mock.Anything, mock.Anything).Return([]*product_listing.ProductListing{}, nil).Once()
			},
			expCode: http.StatusOK,
			respCheck: func(t *testing.T, resp string) {
				require.NotEmpty(t, gjson.Parse(resp).Get("data").String())
			},
		},
		{
			name: "Good case with sales channel filters",
			path: "/internal/product-listings/?organization_id=org123&sales_channel_store_key=store123&sales_channel_platform=shopify&limit=10",
			mock: func() {
				mockSvc.On("List", mock.Anything, mock.Anything).Return([]*product_listing.ProductListing{}, nil).Once()
			},
			expCode: http.StatusOK,
			respCheck: func(t *testing.T, resp string) {
				require.NotEmpty(t, gjson.Parse(resp).Get("data").String())
			},
		},
	}
	for _, tt := range testcases {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mock != nil {
				tt.mock()
			}
			w := httptest.NewRecorder()
			gin.SetMode(gin.TestMode)
			_, eng := gin.CreateTestContext(w)

			NewProductListing(logger.Get(), mockSvc).RegisterRoutes(eng.RouterGroup.Group(""))

			req, _ := http.NewRequestWithContext(context.Background(), http.MethodGet, tt.path, nil)
			eng.ServeHTTP(w, req)
			resp := w.Body.String()
			require.Equal(t, tt.expCode, w.Code, resp)
			if tt.respCheck != nil {
				tt.respCheck(t, resp)
			}
		})
	}
}

func TestProductListing_salesChannelProductEvent(t *testing.T) {
	loadConfig(t)
	mockSvc := new(product_listing.MockProductListingService)

	testcases := []struct {
		name      string
		path      string
		mock      func()
		expCode   int
		body      []byte
		respCheck func(*testing.T, string)
	}{
		{
			name:    "Body is nil case",
			path:    "/internal/product-listings/sales-channel-product-event",
			expCode: http.StatusUnprocessableEntity,
			body:    nil,
		},
		{
			name:    "Invalid JSON body case",
			path:    "/internal/product-listings/sales-channel-product-event",
			expCode: http.StatusUnprocessableEntity,
			body:    []byte(`{invalid json}`),
		},
		{
			name: "Service ACK error case",
			path: "/internal/product-listings/sales-channel-product-event",
			mock: func() {
				mockSvc.On("SalesChannelProductEvent", mock.Anything, mock.Anything).Return(product_listing.ProductListing{}, product_listing_domain.ErrNotFound).Once()
			},
			expCode: http.StatusUnprocessableEntity,
			body:    []byte(`{"id": "pl123", "sales_channel": {"platform": "shopify", "store_key": "test"}, "organization": {"id": "org123"}, "sales_channel_product": {"id": "scp123"}, "product": {"id": "prod123"}, "relations": [], "audit": {}, "version": 1}`),
		},
		{
			name: "Service ACK error - version conflict case",
			path: "/internal/product-listings/sales-channel-product-event",
			mock: func() {
				mockSvc.On("SalesChannelProductEvent", mock.Anything, mock.Anything).Return(product_listing.ProductListing{}, product_listing_domain.ErrVersionConflict).Once()
			},
			expCode: http.StatusUnprocessableEntity,
			body:    []byte(`{"id": "pl123", "sales_channel": {"platform": "shopify", "store_key": "test"}, "organization": {"id": "org123"}, "sales_channel_product": {"id": "scp123"}, "product": {"id": "prod123"}, "relations": [], "audit": {}, "version": 1}`),
		},
		{
			name: "Service unACK warning error case",
			path: "/internal/product-listings/sales-channel-product-event",
			mock: func() {
				mockSvc.On("SalesChannelProductEvent", mock.Anything, mock.Anything).Return(product_listing.ProductListing{}, product_listing_domain.ErrPublishStateInUpdateNotAck).Once()
			},
			expCode: http.StatusConflict,
			body:    []byte(`{"id": "pl123", "sales_channel": {"platform": "shopify", "store_key": "test"}, "organization": {"id": "org123"}, "sales_channel_product": {"id": "scp123"}, "product": {"id": "prod123"}, "relations": [], "audit": {}, "version": 1}`),
		},
		{
			name: "Service error case",
			path: "/internal/product-listings/sales-channel-product-event",
			mock: func() {
				mockSvc.On("SalesChannelProductEvent", mock.Anything, mock.Anything).Return(product_listing.ProductListing{}, errors.New("service error")).Once()
			},
			expCode: http.StatusInternalServerError,
			body:    []byte(`{"id": "pl123", "sales_channel": {"platform": "shopify", "store_key": "test"}, "organization": {"id": "org123"}, "sales_channel_product": {"id": "scp123"}, "product": {"id": "prod123"}, "relations": [], "audit": {}, "version": 1}`),
		},
		{
			name: "Good case",
			path: "/internal/product-listings/sales-channel-product-event",
			mock: func() {
				mockSvc.On("SalesChannelProductEvent", mock.Anything, mock.Anything).Return(product_listing.ProductListing{}, nil).Once()
			},
			expCode: http.StatusOK,
			respCheck: func(t *testing.T, resp string) {
				require.NotEmpty(t, gjson.Parse(resp).Get("data").String())
			},
			body: []byte(`{"id": "pl123", "sales_channel": {"platform": "shopify", "store_key": "test"}, "organization": {"id": "org123"}, "sales_channel_product": {"id": "scp123"}, "product": {"id": "prod123"}, "relations": [], "audit": {}, "version": 1}`),
		},
		{
			name: "Shein platform case",
			path: "/internal/product-listings/sales-channel-product-event",
			mock: func() {
				mockSvc.On("SalesChannelProductEvent", mock.Anything, mock.Anything).Return(product_listing.ProductListing{}, nil).Once()
			},
			expCode: http.StatusOK,
			respCheck: func(t *testing.T, resp string) {
				require.NotEmpty(t, gjson.Parse(resp).Get("data").String())
			},
			body: []byte(`{"id": "pl123", "sales_channel": {"platform": "shein", "store_key": "test"}, "organization": {"id": "org123"}, "sales_channel_product": {"id": "scp123"}, "product": {"id": "prod123"}, "relations": [], "audit": {}, "version": 1}`),
		},
	}
	for _, tt := range testcases {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mock != nil {
				tt.mock()
			}
			w := httptest.NewRecorder()
			gin.SetMode(gin.TestMode)
			_, eng := gin.CreateTestContext(w)

			NewProductListing(logger.Get(), mockSvc).RegisterRoutes(eng.RouterGroup.Group(""))

			req, _ := http.NewRequestWithContext(context.Background(), http.MethodPost, tt.path, bytes.NewBuffer(tt.body))
			eng.ServeHTTP(w, req)
			resp := w.Body.String()
			require.Equal(t, tt.expCode, w.Code, resp)
			if tt.respCheck != nil {
				tt.respCheck(t, resp)
			}
		})
	}
}

func TestProductListing_productsCenterProductEvent(t *testing.T) {
	loadConfig(t)
	mockSvc := new(product_listing.MockProductListingService)

	testcases := []struct {
		name      string
		path      string
		mock      func()
		expCode   int
		body      []byte
		respCheck func(*testing.T, string)
	}{
		{
			name:    "ID short case",
			path:    "/internal/product-listings/123/products-center-product-event",
			expCode: http.StatusUnprocessableEntity,
		},
		{
			name:    "ID long case",
			path:    "/internal/product-listings/" + strings.Repeat("x", 33) + "/products-center-product-event",
			expCode: http.StatusUnprocessableEntity,
		},
		{
			name:    "Body is nil case",
			path:    "/internal/product-listings/" + strings.Repeat("x", 32) + "/products-center-product-event",
			expCode: http.StatusUnprocessableEntity,
			body:    nil,
		},
		{
			name:    "Invalid JSON body case",
			path:    "/internal/product-listings/" + strings.Repeat("x", 32) + "/products-center-product-event",
			expCode: http.StatusUnprocessableEntity,
			body:    []byte(`{invalid json}`),
		},
		{
			name: "Service ACK error case",
			path: "/internal/product-listings/" + strings.Repeat("x", 32) + "/products-center-product-event",
			mock: func() {
				mockSvc.On("ProductsCenterProductEvent", mock.Anything, mock.Anything, mock.Anything).Return(product_listing.ProductListing{}, product_listing_domain.ErrNotFound).Once()
			},
			expCode: http.StatusUnprocessableEntity,
			body:    []byte(`{"products_center_product": {"id": "pc123", "source": {"platform": "shopify", "store_key": "test"}}, "product": {"id": "prod123"}, "relations": [], "ready": {}, "force_publish": false}`),
		},
		{
			name: "Service unACK warning error case",
			path: "/internal/product-listings/" + strings.Repeat("x", 32) + "/products-center-product-event",
			mock: func() {
				mockSvc.On("ProductsCenterProductEvent", mock.Anything, mock.Anything, mock.Anything).Return(product_listing.ProductListing{}, product_listing_domain.ErrPublishStateInUpdateNotAck).Once()
			},
			expCode: http.StatusConflict,
			body:    []byte(`{"products_center_product": {"id": "pc123", "source": {"platform": "shopify", "store_key": "test"}}, "product": {"id": "prod123"}, "relations": [], "ready": {}, "force_publish": false}`),
		},
		{
			name: "Service error case",
			path: "/internal/product-listings/" + strings.Repeat("x", 32) + "/products-center-product-event",
			mock: func() {
				mockSvc.On("ProductsCenterProductEvent", mock.Anything, mock.Anything, mock.Anything).Return(product_listing.ProductListing{}, errors.New("service error")).Once()
			},
			expCode: http.StatusInternalServerError,
			body:    []byte(`{"products_center_product": {"id": "pc123", "source": {"platform": "shopify", "store_key": "test"}}, "product": {"id": "prod123"}, "relations": [], "ready": {}, "force_publish": false}`),
		},
		{
			name: "Good case",
			path: "/internal/product-listings/" + strings.Repeat("x", 32) + "/products-center-product-event",
			mock: func() {
				mockSvc.On("ProductsCenterProductEvent", mock.Anything, mock.Anything, mock.Anything).Return(product_listing.ProductListing{}, nil).Once()
			},
			expCode: http.StatusOK,
			respCheck: func(t *testing.T, resp string) {
				require.NotEmpty(t, gjson.Parse(resp).Get("data").String())
			},
			body: []byte(`{"products_center_product": {"id": "pc123", "source": {"platform": "shopify", "store_key": "test"}}, "product": {"id": "prod123"}, "relations": [], "ready": {}, "force_publish": false}`),
		},
		{
			name: "Good case with force_publish true",
			path: "/internal/product-listings/" + strings.Repeat("x", 32) + "/products-center-product-event",
			mock: func() {
				mockSvc.On("ProductsCenterProductEvent", mock.Anything, mock.Anything, mock.Anything).Return(product_listing.ProductListing{}, nil).Once()
			},
			expCode: http.StatusOK,
			respCheck: func(t *testing.T, resp string) {
				require.NotEmpty(t, gjson.Parse(resp).Get("data").String())
			},
			body: []byte(`{"products_center_product": {"id": "pc123", "source": {"platform": "shopify", "store_key": "test"}}, "product": {"id": "prod123"}, "relations": [], "ready": {}, "force_publish": true}`),
		},
	}
	for _, tt := range testcases {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mock != nil {
				tt.mock()
			}
			w := httptest.NewRecorder()
			gin.SetMode(gin.TestMode)
			_, eng := gin.CreateTestContext(w)

			NewProductListing(logger.Get(), mockSvc).RegisterRoutes(eng.RouterGroup.Group(""))

			req, _ := http.NewRequestWithContext(context.Background(), http.MethodPost, tt.path, bytes.NewBuffer(tt.body))
			eng.ServeHTTP(w, req)
			resp := w.Body.String()
			require.Equal(t, tt.expCode, w.Code, resp)
			if tt.respCheck != nil {
				tt.respCheck(t, resp)
			}
		})
	}
}
