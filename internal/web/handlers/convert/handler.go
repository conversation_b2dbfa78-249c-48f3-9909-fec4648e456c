package convert

import (
	"io"
	"slices"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/AfterShip/connectors-library/gin/responder"
	connector_lib_utils "github.com/AfterShip/connectors-library/utils"
	"github.com/AfterShip/gopkg/log"
	tiktokapi "github.com/AfterShip/pltf-pd-product-listings/internal/third_party/tiktok_api"

	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/convert"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
)

var (
	supportedImageFormats = []string{"image/jpg", "image/jpeg", "image/png", "image/gif", "image/webp", "image/bmp"}
	supportedFileFormats  = []string{"video/mp4", "application/pdf"}
)

type Convert struct {
	logger         *log.Logger
	convertService convert.Service
	responder      *responder.Responder
	validator      *validator.Validate
}

func NewConvert(logger *log.Logger, convertService convert.Service) *Convert {
	s := &Convert{
		logger:         logger,
		convertService: convertService,
		responder:      responder.NewResponder(true, errorResponseMapping),
		validator:      validator.New(),
	}
	s.responder.RegisterDomainError(errorResponseMapping)
	return s
}

func (s *Convert) RegisterRoutes(router *gin.RouterGroup) {
	uploadRouter := router.Group("/convert")
	uploadRouter.POST("/image", s.convertImage)
	uploadRouter.POST("/file", s.convertFile)
	uploadRouter.POST("/description", s.convertDescription)
}

//nolint:funlen
func (s *Convert) convertImage(c *gin.Context) {
	ctx := c.Request.Context()
	startTime := time.Now()

	request := convertImageRequest{}
	if err := c.ShouldBind(&request); err != nil {
		s.responder.ResponseWithErrorCode(c, models.CodeMissRequiredQueryParam, err)
		return
	}

	if err := s.validator.Struct(request); err != nil {
		s.responder.ResponseWithErrorCode(c, models.CodeMissRequiredQueryParam, err)
		return
	}

	arg := request.convertToImageArg()

	if request.ImageOriginURL == "" {
		file, headers, err := c.Request.FormFile("file")
		if err != nil {
			s.responder.ResponseWithErrorCode(c, models.CodeMissRequiredQueryParam, err)
			return
		}
		defer file.Close()
		imageContentType := headers.Header.Get("Content-Type")
		if err = validateImageContentType(imageContentType); err != nil {
			s.responder.ResponseWithErrorCode(c, models.CodeMissRequiredQueryParam, err)
			return
		}

		var imageBytes []byte
		buf := make([]byte, 1024)
		for {
			n, err := file.Read(buf)
			if err != nil && err != io.EOF {
				s.responder.ResponseWithError(c, err)
				return
			}
			if n == 0 {
				break
			}
			imageBytes = append(imageBytes, buf[:n]...)
		}

		arg.ImageBytes = imageBytes
		arg.ImageType = headers.Header.Get("Content-Type")
		arg.ImageName = headers.Filename
	}

	result, err := s.convertService.ConvertImage(ctx, arg)
	if err != nil {
		arg.ImageBytes = nil

		logZapFields := []zap.Field{
			zap.Int("spent_seconds", int(time.Since(startTime).Seconds())),
			zap.String("arg", connector_lib_utils.GetJsonIndent(arg)),
			zap.Error(err),
		}
		if needAckError(err) {
			s.logger.InfoCtx(ctx, "convertService.ConvertImage ack error", logZapFields...)
		} else if unAckNeedWaring(err) {
			s.logger.WarnCtx(ctx, "convertService.ConvertImage warning", logZapFields...)
		} else {
			s.logger.ErrorCtx(ctx, "convertService.ConvertImage warning", logZapFields...)
		}

		s.responder.ResponseWithError(c, err)
		return
	}

	s.responder.ResponseWithOK(c, convertToImageResponse(arg.Organization, arg.SalesChannel, &result))
}

//nolint:funlen
func (s *Convert) convertFile(c *gin.Context) {
	ctx := c.Request.Context()
	request := convertFileRequest{}
	if err := c.ShouldBind(&request); err != nil {
		s.responder.ResponseWithErrorCode(c, models.CodeMissRequiredQueryParam, err)
		return
	}

	if err := s.validator.Struct(request); err != nil {
		s.responder.ResponseWithErrorCode(c, models.CodeMissRequiredQueryParam, err)
		return
	}

	arg := request.convertToFileArg()

	if request.FileOriginURL == "" {
		file, headers, err := c.Request.FormFile("file")
		if err != nil {
			s.responder.ResponseWithError(c, err)
			return
		}
		defer file.Close()
		fileContentType := headers.Header.Get("Content-Type")
		if err = validateFileContentType(fileContentType); err != nil {
			s.responder.ResponseWithError(c, err)
			return
		}

		var fileBytes []byte
		buf := make([]byte, 1024)
		for {
			n, err := file.Read(buf)
			if err != nil && err != io.EOF {
				s.responder.ResponseWithError(c, err)
				return
			}
			if n == 0 {
				break
			}
			fileBytes = append(fileBytes, buf[:n]...)
		}

		arg.FileBytes = fileBytes
		arg.FileName = headers.Filename
		arg.FileType = headers.Header.Get("Content-Type")
	}

	result, err := s.convertService.ConvertFile(ctx, arg)
	if err != nil {
		// clear fileBytes to avoid log
		arg.FileBytes = nil
		s.logger.ErrorCtx(ctx, "convertService.ConvertFile error", zap.Error(err), zap.Any("arg", arg))
		s.responder.ResponseWithError(c, err)
		return
	}

	s.responder.ResponseWithOK(c, convertToFileResponse(arg.Organization, arg.SalesChannel, &result))
}

func (s *Convert) convertDescription(c *gin.Context) {
	ctx := c.Request.Context()
	request := convertDescriptionRequest{}
	if err := c.ShouldBindJSON(&request); err != nil {
		s.responder.ResponseWithErrorCode(c, models.CodeMissRequiredQueryParam, err)
		return
	}
	if err := s.validator.Struct(request); err != nil {
		s.responder.ResponseWithErrorCode(c, models.CodeMissRequiredQueryParam, err)
		return
	}
	organization := &models.Organization{ID: request.OrganizationID}
	salesChannel := &models.SalesChannel{StoreKey: request.SalesChannelStoreKey, Platform: request.SalesChannelPlatform}
	arg := convert.DescriptionArg{
		Organization:     organization,
		SalesChannel:     salesChannel,
		Description:      request.Description,
		ShortDescription: request.ShortDescription,
		IgnoreCache:      request.IgnoreCache,
		SourceStore: &models.App{
			Key:      request.SourceStoreKey,
			Platform: request.SourcePlatform,
		},
	}
	result, err := s.convertService.ConvertDescription(ctx, &arg)
	if err != nil {
		// clear description to avoid log
		arg.Description = ""
		s.logger.ErrorCtx(ctx, "convertService.ConvertDescription error", zap.Error(err), zap.Any("arg", arg))
		s.responder.ResponseWithError(c, err)
		return
	}
	response := convertDescriptionResponse{
		Organization: &models.Organization{
			ID: request.OrganizationID,
		},
		SalesChannel: &models.SalesChannel{
			StoreKey: request.SalesChannelStoreKey,
			Platform: request.SalesChannelPlatform,
		},
		Description: result.Description,
	}

	s.responder.ResponseWithOK(c, response)
}

func validateImageContentType(imageContentType string) error {
	if !slices.Contains(supportedImageFormats, strings.ToLower(imageContentType)) {
		return errorFileContentType
	}
	return nil
}

func validateFileContentType(fileContentType string) error {
	if !slices.Contains(supportedFileFormats, strings.ToLower(fileContentType)) {
		return errorFileContentType
	}
	return nil
}

func needAckError(err error) bool {
	return errors.Is(err, convert.ErrorConvertImageService413Error) ||
		errors.Is(err, convert.ErrorConvertImageService422Error) ||
		errors.Is(err, convert.ErrorDownLoadFileFromEcommerce4XX) ||
		errors.Is(err, convert.ErrorUnsupportedVideoURL) ||
		errors.Is(err, convert.ErrorOnlyAllowHttps) ||
		errors.Is(err, convert.ErrorNoConnection) ||
		errors.Is(err, convert.ErrorInvalidImageUseCase) ||
		errors.Is(err, convert.ErrorUnsupportedSalesChannel) ||
		errors.Is(err, convert.ErrorUnexpectedLargeError) ||
		errors.Is(err, convert.ErrDownloadFileFromEcommerceFailed) ||
		errors.Is(err, convert.ErrorRequestUploadAPITimeout) ||
		errors.Is(err, convert.ErrorImageBlank) ||
		errors.Is(err, convert.ErrImageNotValidate) ||
		errors.Is(err, convert.ErrorImageType)
}

func unAckNeedWaring(err error) bool {
	return errors.Is(err, convert.ErrorConvertImageService429Error) ||
		errors.Is(err, tiktokapi.ErrAccessTokenInvalid)
}
