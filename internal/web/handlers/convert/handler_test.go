package convert

import (
	"testing"

	"github.com/pkg/errors"
	"github.com/stretchr/testify/assert"

	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/convert"
)

func TestValidateImageContentType(t *testing.T) {
	validImageContentType := "image/jpeg"
	invalidImageContentType := "text/plain"

	err := validateImageContentType(validImageContentType)
	assert.Nil(t, err, "Expected no error for valid image content type")

	err = validateImageContentType(invalidImageContentType)
	assert.NotNil(t, err, "Expected error for invalid image content type")
}

func TestValidateFileContentType(t *testing.T) {
	validFileContentType := "application/pdf"
	invalidFileContentType := "text/plain"

	err := validateFileContentType(validFileContentType)
	assert.Nil(t, err, "Expected no error for valid file content type")

	err = validateFileContentType(invalidFileContentType)
	assert.NotNil(t, err, "Expected error for invalid file content type")
}

func TestNeedAckError(t *testing.T) {
	// Test errors that need acknowledgement
	t.Run("Errors requiring acknowledgement", func(t *testing.T) {
		errorCases := []error{
			convert.ErrorConvertImageService413Error,
			convert.ErrorConvertImageService422Error,
			convert.ErrorDownLoadFileFromEcommerce4XX,
			convert.ErrorUnsupportedVideoURL,
			convert.ErrorOnlyAllowHttps,
			convert.ErrorNoConnection,
			convert.ErrorInvalidImageUseCase,
			convert.ErrorUnsupportedSalesChannel,
			convert.ErrorUnexpectedLargeError,
			convert.ErrDownloadFileFromEcommerceFailed,
			convert.ErrorRequestUploadAPITimeout,
			convert.ErrorImageBlank,
			convert.ErrImageNotValidate,
			convert.ErrorImageType,
		}

		for _, err := range errorCases {
			assert.True(t, needAckError(err), "Expected error to need acknowledgement: %v", err)
		}
	})

	// Test errors that don't need acknowledgement
	t.Run("Errors not requiring acknowledgement", func(t *testing.T) {
		errorCases := []error{
			convert.ErrorConvertImageService429Error, // This error is handled in unAckNeedWaring
			errors.New("random error"),               // Random errors don't need acknowledgement
		}

		for _, err := range errorCases {
			assert.False(t, needAckError(err), "Expected error to not need acknowledgement: %v", err)
		}
	})

	// Test nil error
	t.Run("Nil error", func(t *testing.T) {
		assert.False(t, needAckError(nil), "Nil error should not need acknowledgement")
	})

	// Test wrapped errors
	t.Run("Wrapped errors", func(t *testing.T) {
		wrappedErr := errors.Wrap(convert.ErrorNoConnection, "connection failed")
		assert.True(t, needAckError(wrappedErr), "Wrapped acknowledgeable error should return true")

		randomWrappedErr := errors.Wrap(errors.New("random error"), "wrapped random error")
		assert.False(t, needAckError(randomWrappedErr), "Wrapped random error should not need acknowledgement")
	})
}
