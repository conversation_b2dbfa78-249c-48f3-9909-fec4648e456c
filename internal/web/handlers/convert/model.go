package convert

import (
	convert_domain "github.com/AfterShip/pltf-pd-product-listings/internal/domains/convert"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
)

type convertImageRequest struct {
	OrganizationID       string `form:"organization_id" validate:"required"`
	SalesChannelPlatform string `form:"sales_channel_platform" validate:"required"`
	SalesChannelStoreKey string `form:"sales_channel_store_key" validate:"required"`
	ImageUseCase         string `form:"image_use_case" validate:"required"`
	ImageOriginURL       string `form:"image_origin_url"`
	IgnoreCache          bool   `form:"ignore_cache"`
	IgnoreTransform      bool   `form:"ignore_transform"`
}

func (r *convertImageRequest) convertToImageArg() *convert_domain.ImageArg {
	return &convert_domain.ImageArg{
		Organization: &models.Organization{
			ID: r.OrganizationID,
		},
		SalesChannel: &models.SalesChannel{
			Platform: r.SalesChannelPlatform,
			StoreKey: r.SalesChannel<PERSON>toreKey,
		},
		ImageUseCase:    r.ImageUseCase,
		ImageOriginURL:  r.ImageOriginURL,
		IgnoreCache:     r.Ignore<PERSON>ache,
		IgnoreTransform: r.IgnoreTransform,
	}
}

type convertImageResponse struct {
	Organization     *models.Organization `json:"organization"`
	SalesChannel     *models.SalesChannel `json:"sales_channel"`
	ImageHeight      int                  `json:"image_height"`
	ImageURI         string               `json:"image_uri"`
	ImageURL         string               `json:"image_url"`
	ImageUseCase     string               `json:"image_use_case"`
	ImageWidth       int                  `json:"image_width"`
	FromSalesChannel bool                 `json:"from_sales_channel"`
}

type convertFileRequest struct {
	OrganizationID       string `form:"organization_id" validate:"required"`
	SalesChannelPlatform string `form:"sales_channel_platform" validate:"required"`
	SalesChannelStoreKey string `form:"sales_channel_store_key" validate:"required"`
	FileOriginURL        string `form:"file_origin_url"`
	IgnoreCache          bool   `form:"ignore_cache"`
}

func (r *convertFileRequest) convertToFileArg() *convert_domain.FileArg {
	return &convert_domain.FileArg{
		Organization: &models.Organization{
			ID: r.OrganizationID,
		},
		SalesChannel: &models.SalesChannel{
			Platform: r.SalesChannelPlatform,
			StoreKey: r.SalesChannelStoreKey,
		},
		FileOriginURL: r.FileOriginURL,
		IgnoreCache:   r.IgnoreCache,
	}
}

type convertFileResponse struct {
	Organization *models.Organization `json:"organization"`
	SalesChannel *models.SalesChannel `json:"sales_channel"`
	FileID       string               `json:"file_id"`
	FileURL      string               `json:"file_url"`
	FileName     string               `json:"file_name"`
	FileFormat   string               `json:"file_format"`
}

type convertDescriptionRequest struct {
	OrganizationID       string `json:"organization_id" validate:"required"`
	SalesChannelPlatform string `json:"sales_channel_platform" validate:"required"`
	SalesChannelStoreKey string `json:"sales_channel_store_key" validate:"required"`
	SourceStoreKey       string `json:"source_store_key" validate:"omitempty"`
	SourcePlatform       string `json:"source_platform" validate:"omitempty"`
	Description          string `json:"description"`
	ShortDescription     string `json:"short_description"`
	IgnoreCache          bool   `json:"ignore_cache"`
}

type convertDescriptionResponse struct {
	Organization *models.Organization `json:"organization"`
	SalesChannel *models.SalesChannel `json:"sales_channel"`
	Description  string               `json:"description"`
}
