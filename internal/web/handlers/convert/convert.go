package convert

import (
	convert_domain "github.com/AfterShip/pltf-pd-product-listings/internal/domains/convert"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
)

func convertToImageResponse(organization *models.Organization, salesChannel *models.SalesChannel, result *convert_domain.ImageOutput) *convertImageResponse {
	return &convertImageResponse{
		Organization:     organization,
		SalesChannel:     salesChannel,
		ImageURI:         result.ImageURI,
		ImageURL:         result.ImageURL,
		ImageHeight:      result.ImageHeight,
		ImageWidth:       result.ImageWidth,
		ImageUseCase:     result.ImageUseCase,
		FromSalesChannel: result.FromSalesChannel,
	}
}

func convertToFileResponse(organization *models.Organization, salesChannel *models.SalesChannel, result *convert_domain.FileOutput) *convertFileResponse {
	return &convertFileResponse{
		Organization: organization,
		SalesChannel: salesChannel,
		FileID:       result.FileID,
		FileURL:      result.FileURL,
		FileName:     result.FileName,
		FileFormat:   result.FileFormat,
	}
}
