package convert

import (
	"errors"

	standard_error "github.com/AfterShip/connectors-errors-sdk-go"
	"github.com/AfterShip/connectors-library/gin/responder"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/convert"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
)

var (
	errorImageContentType = errors.New("image content type not supported")
	errorFileContentType  = errors.New("file content type not supported")
)

const (
	ackErrorCode                    responder.MetaCode = 42222
	errorFileContentTypeCode        responder.MetaCode = 42201
	errorFileTooBigCode             responder.MetaCode = 42202
	errorInvalidImageUseCase        responder.MetaCode = 42203
	errorUnsupportedSalesChannel    responder.MetaCode = 42204
	errorUnsupportedVideoURL        responder.MetaCode = 42205
	errorUnexpectedLargeError       responder.MetaCode = 42206
	errorConvertImageServiceError   responder.MetaCode = 42207
	errorUploadAPIReqTimeout        responder.MetaCode = 42208
	errorProxyAPIContextCancelError responder.MetaCode = 42209
)

var errorResponseMapping = map[error]responder.Meta{
	errorImageContentType: responder.Meta{
		Code: errorFileContentTypeCode,
		Description: responder.Description{
			Type:    "ImageContentTypeNotSupported",
			Message: "image content type not supported",
		},
	},
	errorFileContentType: responder.Meta{
		Code: errorFileContentTypeCode,
		Description: responder.Description{
			Type:    "FileContentTypeNotSupported",
			Message: "file content type not supported",
		},
	},
	standard_error.WFPTTSCreateProductMP4TooBig_603422801: {
		Code: errorFileTooBigCode,
		Description: responder.Description{
			Type:    "FileTooBig",
			Message: "file is too big",
		},
	},
	standard_error.WFPTTSCreateProductPDFTooBig_603422800: {
		Code: errorFileTooBigCode,
		Description: responder.Description{
			Type:    "FileTooBig",
			Message: "file is too big",
		},
	},
	standard_error.WFPTTSCreateProductFileFormatNotMatch_603422799: {
		Code: errorFileContentTypeCode,
		Description: responder.Description{
			Type:    "FileContentTypeNotSupported",
			Message: "file content type not supported",
		},
	},
	convert.ErrorConvertImageServiceError: {
		Code: errorConvertImageServiceError,
		Description: responder.Description{
			Type:    "ConvertImageServiceError",
			Message: "convert image service error",
		},
	},
	convert.ErrorConvertImageService413Error: {
		Code: ackErrorCode,
		Description: responder.Description{
			Type:    "ConvertImageService413Error",
			Message: "convert image service 413 error, please try again later",
		},
	},
	convert.ErrorConvertImageService422Error: {
		Code: ackErrorCode,
		Description: responder.Description{
			Type:    "ConvertImageService422Error",
			Message: "convert image service 422 error, please try again later",
		},
	},
	convert.ErrorDownLoadFileFromEcommerce4XX: {
		Code: ackErrorCode,
		Description: responder.Description{
			Type:    "DownloadFileFromEcommerce4XX",
			Message: "download file from ecommerce 4XX error, please try again later",
		},
	},
	convert.ErrorUnsupportedVideoURL: {
		Code: errorUnsupportedVideoURL,
		Description: responder.Description{
			Type:    "UnsupportedVideoURL",
			Message: "unsupported video URL",
		},
	},
	convert.ErrorOnlyAllowHttps: {
		Code: errorUnsupportedVideoURL,
		Description: responder.Description{
			Type:    "OnlyAllowHttps",
			Message: "only allow https",
		},
	},
	convert.ErrorNoConnection: {
		Code: errorUnsupportedVideoURL,
		Description: responder.Description{
			Type:    "NoConnection",
			Message: "organization and sales channel have no connection",
		},
	},
	convert.ErrorInvalidImageUseCase: {
		Code: errorInvalidImageUseCase,
		Description: responder.Description{
			Type:    "InvalidImageUseCase",
			Message: "invalid image use case",
		},
	},
	convert.ErrorUnsupportedSalesChannel: {
		Code: errorUnsupportedSalesChannel,
		Description: responder.Description{
			Type:    "UnsupportedSalesChannel",
			Message: "unsupported sales channel",
		},
	},
	convert.ErrorUnexpectedLargeError: {
		Code: errorUnexpectedLargeError,
		Description: responder.Description{
			Type:    "UnexpectedLargeError",
			Message: "unexpected large error",
		},
	},
	convert.ErrImageNotValidate: {
		Code: ackErrorCode,
		Description: responder.Description{
			Type:    "ImageNotValidate",
			Message: "image not validate, please upload a valid image",
		},
	},
	convert.ErrDownloadFileFromEcommerceFailed: {
		Code: ackErrorCode,
		Description: responder.Description{
			Type:    "DownloadFileFromEcommerceFailed",
			Message: "download file from ecommerce failed, please try again later",
		},
	},
	convert.ErrorRequestUploadAPITimeout: {
		Code: errorUploadAPIReqTimeout,
		Description: responder.Description{
			Type:    "RequestTimeout",
			Message: "request timeout, please try again later",
		},
	},
	convert.ErrorImageBlank: {
		Code: ackErrorCode,
		Description: responder.Description{
			Type:    "ImageBlank",
			Message: "image is blank, please upload a valid image",
		},
	},
	convert.ErrorImageType: {
		Code: ackErrorCode,
		Description: responder.Description{
			Type:    "ImageTypeNotSupported",
			Message: "image type not supported, please upload a valid image type",
		},
	},
	convert.ErrorConvertImageService429Error: {
		Code: models.CodeTooManyRequests,
		Description: responder.Description{
			Type:    "ConvertImageService429Error",
			Message: "convert image service 429 error, please try again later",
		},
	},
	convert.ErrProxyServiceContextCancelError: {
		Code: errorProxyAPIContextCancelError,
		Description: responder.Description{
			Type:    "ProxyServiceContextCancelError",
			Message: "proxy service context cancelled error",
		},
	},
}
