package calculator

import (
	"github.com/AfterShip/connectors-library/gin/responder"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/common/calculators"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/organization_settings"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/settings"
)

const (
	ackErrorCode                 responder.MetaCode = 42222
	CurrencyConvertorNotFound    responder.MetaCode = 40401
	ErrCurrencyEmpty             responder.MetaCode = 40402
	SyncPriceNotFound            responder.MetaCode = 40403
	SettingInventorySyncNotFound responder.MetaCode = 40404
	OrganizationSettingNotFound  responder.MetaCode = 40405
	MissingShopifyMarketsMapping responder.MetaCode = 42210
	PriceIsNegative              responder.MetaCode = 42211
)

var errorResponseMapping = map[error]responder.Meta{
	organization_settings.ErrSpecificCurrencyConvertorNotFound: responder.Meta{
		Code: CurrencyConvertorNotFound,
		Description: responder.Description{
			Type:    "CurrencyConvertorNotFound",
			Message: "specific currency convertor not found",
		},
	},
	calculators.ErrCurrencyEmpty: responder.Meta{
		Code: ErrCurrencyEmpty,
		Description: responder.Description{
			Type:    "PriceCurrencyEmpty",
			Message: "missing price currency",
		},
	},
	settings.ErrSettingInventorySyncNotFound: responder.Meta{
		Code: SettingInventorySyncNotFound,
		Description: responder.Description{
			Type:    "SettingInventorySyncNotFound",
			Message: "missing inventory sync setting",
		},
	},
	settings.ErrSettingSyncPriceNotFound: responder.Meta{
		Code: SyncPriceNotFound,
		Description: responder.Description{
			Type:    "SyncPriceNotFound",
			Message: "missing price sync setting",
		},
	},
	organization_settings.ErrOrganizationSettingNotFound: responder.Meta{
		Code: OrganizationSettingNotFound,
		Description: responder.Description{
			Type:    "OrganizationSettingConflict",
			Message: "organization setting not found",
		},
	},
	calculators.ErrMissingShopifyMarketsMapping: responder.Meta{
		Code: MissingShopifyMarketsMapping,
		Description: responder.Description{
			Type:    "MissingShopifyMarketsMapping",
			Message: "missing shopify markets mapping",
		},
	},
	calculators.ErrPriceIsNegative: responder.Meta{
		Code: PriceIsNegative,
		Description: responder.Description{
			Type:    "PriceIsNegative",
			Message: "price is negative",
		},
	},
}
