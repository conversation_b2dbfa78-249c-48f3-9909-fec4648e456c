package calculator

import (
	"bytes"
	"context"
	"errors"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"github.com/tidwall/gjson"

	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/common/calculators"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/settings"
	"github.com/AfterShip/pltf-pd-product-listings/internal/logger"
)

func Test_calculatePrices(t *testing.T) {
	path := "/internal/calculate-prices"
	mockSvc := new(calculators.MockCalculatorsService)

	testcases := []struct {
		name      string
		body      []byte
		mock      func()
		expCode   int
		respCheck func(*testing.T, string)
	}{
		{
			name:    "Body is nil case",
			expCode: http.StatusUnprocessableEntity,
		},
		{
			name: "Service error case",
			mock: func() {
				mockSvc.On("CalculatePrices", mock.Anything, mock.Anything).Return(&calculators.CalculatePricesOutput{}, errors.New("create error")).Once()
			},
			expCode: http.StatusInternalServerError,
			body:    []byte(`{"products_center_product":{"id":"936f74f23f9b481da79411f7417f2f54"}}`),
		},
		{
			name: "missing currency",
			mock: func() {
				mockSvc.On("CalculatePrices", mock.Anything, mock.Anything).Return(&calculators.CalculatePricesOutput{}, calculators.ErrCurrencyEmpty).Once()
			},
			expCode: http.StatusNotFound,
			body:    []byte(`{"products_center_product":{"id":"936f74f23f9b481da79411f7417f2f54"}}`),
		},
		{
			name: "Good case",
			body: []byte(`{"products_center_product":{"id":"936f74f23f9b481da79411f7417f2f54"}}`),
			mock: func() {
				mockSvc.On("CalculatePrices", mock.Anything, mock.Anything).Return(&calculators.CalculatePricesOutput{
					ProductsCenterProduct: &calculators.CalculatePriceProductsCenterProductOutput{
						ID: "936f74f23f9b481da79411f7417f2f54",
					}}, nil).Once()
			},
			expCode: http.StatusOK,
			respCheck: func(t *testing.T, resp string) {
				require.NotEmpty(t, gjson.Parse(resp).Get("data").String())
				require.Equal(t, gjson.Parse(resp).Get("data.products_center_product.id").String(), "936f74f23f9b481da79411f7417f2f54")
			},
		},
	}

	for _, tt := range testcases {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mock != nil {
				tt.mock()
			}
			w := httptest.NewRecorder()
			gin.SetMode(gin.TestMode)
			_, eng := gin.CreateTestContext(w)

			NewCalculator(logger.Get(), mockSvc).RegisterRoutes(eng.RouterGroup.Group(""))

			req, _ := http.NewRequestWithContext(context.Background(), http.MethodPost, path, bytes.NewBuffer(tt.body))
			req.Header.Set("Content-Type", "application/json")

			eng.ServeHTTP(w, req)
			resp := w.Body.String()
			require.Equal(t, tt.expCode, w.Code, resp)

			if tt.respCheck != nil {
				tt.respCheck(t, resp)
			}
		})
	}
}

func Test_calculateAvailableQuantities(t *testing.T) {
	path := "/internal/calculate-available-quantities"
	mockSvc := new(calculators.MockCalculatorsService)

	testcases := []struct {
		name      string
		body      []byte
		mock      func()
		expCode   int
		respCheck func(*testing.T, string)
	}{
		{
			name:    "Body is nil case",
			expCode: http.StatusUnprocessableEntity,
		},
		{
			name: "Service error case",
			mock: func() {
				mockSvc.On("CalculateAvailableQuantities", mock.Anything, mock.Anything).Return(&calculators.CalculateAvailableQuantitiesOutput{}, errors.New("create error")).Once()
			},
			expCode: http.StatusInternalServerError,
			body:    []byte(`{"products_center_product":{"id":"936f74f23f9b481da79411f7417f2f54"}}`),
		},
		{
			name: "missing currency",
			mock: func() {
				mockSvc.On("CalculateAvailableQuantities", mock.Anything, mock.Anything).Return(&calculators.CalculateAvailableQuantitiesOutput{}, settings.ErrSettingInventorySyncNotFound).Once()
			},
			expCode: http.StatusNotFound,
			body:    []byte(`{"products_center_product":{"id":"936f74f23f9b481da79411f7417f2f54"}}`),
		},
		{
			name: "Good case",
			body: []byte(`{"products_center_product":{"id":"936f74f23f9b481da79411f7417f2f54"}}`),
			mock: func() {
				mockSvc.On("CalculateAvailableQuantities", mock.Anything, mock.Anything).Return(&calculators.CalculateAvailableQuantitiesOutput{
					ProductsCenterProduct: &calculators.ProductsCenterProductOut{
						ID: "936f74f23f9b481da79411f7417f2f54",
					}}, nil).Once()
			},
			expCode: http.StatusOK,
			respCheck: func(t *testing.T, resp string) {
				require.NotEmpty(t, gjson.Parse(resp).Get("data").String())
				require.Equal(t, gjson.Parse(resp).Get("data.products_center_product.id").String(), "936f74f23f9b481da79411f7417f2f54")
			},
		},
	}

	for _, tt := range testcases {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mock != nil {
				tt.mock()
			}
			w := httptest.NewRecorder()
			gin.SetMode(gin.TestMode)
			_, eng := gin.CreateTestContext(w)

			NewCalculator(logger.Get(), mockSvc).RegisterRoutes(eng.RouterGroup.Group(""))

			req, _ := http.NewRequestWithContext(context.Background(), http.MethodPost, path, bytes.NewBuffer(tt.body))
			req.Header.Set("Content-Type", "application/json")

			eng.ServeHTTP(w, req)
			resp := w.Body.String()
			require.Equal(t, tt.expCode, w.Code, resp)

			if tt.respCheck != nil {
				tt.respCheck(t, resp)
			}
		})
	}
}
