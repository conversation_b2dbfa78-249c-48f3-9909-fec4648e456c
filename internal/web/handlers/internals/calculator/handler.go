package calculator

import (
	"github.com/gin-gonic/gin"

	"github.com/AfterShip/gopkg/log"

	"github.com/AfterShip/connectors-library/gin/responder"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/common/calculators"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
)

type Calculator struct {
	logger    *log.Logger
	service   calculators.Service
	responder *responder.Responder
}

func NewCalculator(logger *log.Logger, service calculators.Service) *Calculator {
	s := &Calculator{
		logger:    logger,
		service:   service,
		responder: responder.NewResponder(false, models.CommonErrorsMapping),
	}
	s.responder.RegisterDomainError(errorResponseMapping)
	return s
}

func (c *Calculator) RegisterRoutes(router *gin.RouterGroup) {
	router = router.Group("/internal")
	router.POST("/calculate-prices", c.calculatePrices)
	router.POST("/calculate-available-quantities", c.calculateAvailableQuantities)
}
