package calculator

import (
	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
	"go.uber.org/zap"

	connector_lib_utils "github.com/AfterShip/connectors-library/utils"
	organization_setting_domain "github.com/AfterShip/pltf-pd-product-listings/internal/domains/organization_settings"
	"github.com/AfterShip/pltf-pd-product-listings/internal/third_party/connectors"

	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/common/calculators"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
)

// nolint:dupl
func (s *Calculator) calculatePrices(c *gin.Context) {
	var req calculators.CalculatePricesInput

	if err := c.ShouldBindJSON(&req); err != nil {
		s.responder.ResponseWithErrorCode(c, models.CodeMissRequiredBodyParam, err)
		return
	}

	ctx := c.Request.Context()
	output, err := s.service.CalculatePrices(ctx, &req)
	if err != nil {
		if needAckError(err) {
			s.responder.ResponseWithErrorCode(c, ackErrorCode, err)
			return
		}
		s.logger.ErrorCtx(ctx, "Calculate prices error",
			zap.Error(err))
		s.responder.ResponseWithError(c, err)
		return
	}

	s.responder.ResponseWithOK(c, output)
}

// nolint:dupl
func (s *Calculator) calculateAvailableQuantities(c *gin.Context) {
	var req calculators.CalculateAvailableQuantitiesInput

	if err := c.ShouldBindJSON(&req); err != nil {
		s.responder.ResponseWithErrorCode(c, models.CodeMissRequiredBodyParam, err)
		return
	}

	ctx := c.Request.Context()
	output, err := s.service.CalculateAvailableQuantities(ctx, &req)
	if err != nil {
		s.logger.ErrorCtx(ctx, "Calculate available quantities error",
			zap.String("req", connector_lib_utils.GetJsonIndent(req)), zap.Error(err))
		s.responder.ResponseWithError(c, err)
		return
	}

	s.responder.ResponseWithOK(c, output)
}

func needAckError(err error) bool {
	return errors.Is(err, organization_setting_domain.ErrSpecificCurrencyConvertorNotFound) ||
		errors.Is(err, connectors.ErrConnectionNotFound) ||
		errors.Is(err, organization_setting_domain.ErrOrganizationSettingNotFound)
}
