package data_clean

import (
	"strings"

	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/data_clean/searchable_product_fields"
)

type fillSearchableProductFieldsParams struct {
	OrganizationIDs string `param:"organization_ids" form:"organization_ids" binding:"required"`
	BatchSize       int    `param:"batch_size" form:"batch_size" binding:"required,max=1000"`
	ProducerCount   int    `param:"producer_count" form:"producer_count" binding:"required,min=1,max=50"`
	ConsumerCount   int    `param:"consumer_count" form:"consumer_count" binding:"required,min=1,max=50"`
	ProcessingQPS   int    `param:"processing_qps" form:"processing_qps" binding:"required,min=1,max=50"`
}

func (p *fillSearchableProductFieldsParams) toServiceArgs() searchable_product_fields.SearchableProductFieldsInputArgs {
	return searchable_product_fields.SearchableProductFieldsInputArgs{
		OrganizationIDs: strings.Split(p.OrganizationIDs, ","),
		BatchSize:       p.BatchSize,
		ProducerCount:   p.ProducerCount,
		ConsumerCount:   p.ConsumerCount,
		ProcessingQPS:   p.ProcessingQPS,
	}
}
