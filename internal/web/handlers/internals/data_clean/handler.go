package data_clean

import (
	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"

	"github.com/AfterShip/connectors-library/gin/responder"
	"github.com/AfterShip/gopkg/log"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/data_clean"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
)

type DataCleanHandler struct {
	logger    *log.Logger
	responder *responder.Responder

	service data_clean.Service
}

func NewDataCleanHandler(logger *log.Logger, dataCleanService data_clean.Service) *DataCleanHandler {
	s := &DataCleanHandler{
		logger:    logger,
		service:   dataCleanService,
		responder: responder.NewResponder(false, models.CommonErrorsMapping),
	}
	return s
}

func (c *DataCleanHandler) RegisterRoutes(router *gin.RouterGroup) {
	router = router.Group("/internal/data-clean")

	router.GET("/abort", c.abort)
	router.GET("/clean-searchable-product-fields", c.cleanSearchableProductFields)
}

func (s *DataCleanHandler) abort(c *gin.Context) {
	ctx := c.Request.Context()

	// 获取 params
	scriptName, _ := c.GetQuery("script_name")
	if scriptName == "" {
		s.responder.ResponseWithErrorCode(c, models.CodeUnprocessableEntity, errors.New("script_name is required"))
		return
	}

	err := s.service.Abort(ctx, scriptName)
	if err != nil {
		s.responder.ResponseWithError(c, err)
		return
	}

	s.responder.ResponseWithOK(c, "script aborted")
}

func (s *DataCleanHandler) cleanSearchableProductFields(c *gin.Context) {
	ctx := c.Request.Context()

	params := new(fillSearchableProductFieldsParams)
	if err := c.ShouldBindQuery(params); err != nil {
		s.responder.ResponseWithErrorCode(c, models.CodeUnprocessableEntity, err)
		return
	}

	name, err := s.service.CleanSearchableProductFields(ctx, params.toServiceArgs())
	if err != nil {
		s.responder.ResponseWithError(c, err)
		return
	}

	s.responder.ResponseWithOK(c, "script started: "+name)
}
