package searchable_products

import (
	"strings"
	"time"

	"github.com/AfterShip/pltf-pd-product-listings/internal/consts"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/searchable_products"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
)

type SearchRequestParams struct {
	OrganizationID             string `form:"organization_id"`
	SourceStoreKey             string `form:"source_store_key"`
	SourcePlatform             string `form:"source_platform"`
	SourceIDs                  string `form:"source_ids"`
	SourceTypes                string `form:"source_types"`
	Status                     string `form:"status"` // 不赋值默认同时返回 active/inactive 数据
	VariantStatus              string `form:"variant_status"`
	FulfillmentServices        string `form:"fulfillment_services"`
	Vendor                     string `form:"vendor"`
	IDs                        string `form:"ids"`
	ProductsCenterVariantIDs   string `form:"products_center_variant_ids"`
	Categories                 string `form:"categories"`
	Tags                       string `form:"tags"`
	SKUs                       string `form:"skus"`
	Query                      string `form:"query"`
	ConnectorsProductIDs       string `form:"connectors_product_ids"`
	SourceInventoryItemID      string `form:"source_inventory_item_id"`
	ProductTypes               string `form:"product_types"`
	CombinedListingID          string `form:"combined_listing_id"`
	CombinedStatus             string `form:"combined_status"`
	IncludedDeletedProduct     bool   `form:"included_deleted_product"`
	IncludedSalesChannelStores string `form:"included_sales_channel_stores"`
	ExcludedSalesChannelStores string `form:"excluded_sales_channel_stores"`
	SourceSalesChannelName     string `form:"source_sales_channel_name"`
	Cursor                     string `form:"cursor"`
	Page                       int    `form:"page" binding:"lte=1000"`
	Limit                      int    `form:"limit" binding:"lte=1000"`
}

func (p *SearchRequestParams) toServiceSearchArgs() *searchable_products.SearchArgs {
	args := &searchable_products.SearchArgs{
		OrganizationID:             p.OrganizationID,
		SourcePlatform:             p.SourcePlatform,
		SourceStoreKey:             p.SourceStoreKey,
		SourceIDs:                  splitRequestValue(p.SourceIDs),
		SourceTypes:                splitRequestValue(p.SourceTypes),
		Vendor:                     p.Vendor,
		FulfillmentServices:        splitRequestValue(p.FulfillmentServices),
		IDs:                        splitRequestValue(p.IDs),
		ProductsCenterVariantIDs:   splitRequestValue(p.ProductsCenterVariantIDs),
		Tags:                       splitRequestValue(p.Tags),
		ProductTypes:               splitRequestValue(p.ProductTypes),
		SourceSalesChannelNames:    splitRequestValue(p.SourceSalesChannelName),
		SKUs:                       splitRequestValue(p.SKUs),
		Status:                     splitRequestValue(p.Status),
		VariantStatus:              splitRequestValue(p.VariantStatus),
		ConnectorsProductIDs:       splitRequestValue(p.ConnectorsProductIDs),
		Categories:                 splitRequestValue(p.Categories),
		IncludedDeletedProduct:     p.IncludedDeletedProduct,
		IncludedSalesChannelStores: convertSearchRequestToSaleChannels(p.IncludedSalesChannelStores),
		ExcludedSalesChannelStores: convertSearchRequestToSaleChannels(p.ExcludedSalesChannelStores),
		SourceInventoryItemID:      p.SourceInventoryItemID,
		Query:                      p.Query,
		CombinedListingID:          p.CombinedListingID,
		CombinedStatus:             p.CombinedStatus,
		Cursor:                     p.Cursor,
		Page:                       p.Page,
		Limit:                      p.Limit,
	}

	return args
}

// eg: "shopify:7495328909601442770,shopify:7495328909601442772"
func convertSearchRequestToSaleChannels(req string) []models.SalesChannel {
	reqStores := splitRequestValue(req)

	if reqStores == nil {
		return nil
	}

	stores := make([]models.SalesChannel, 0, len(reqStores))
	for _, store := range reqStores {
		// eg: "shopify:7495328909601442770
		detail := strings.Split(store, ":")
		if len(detail) != 2 {
			continue
		}
		stores = append(stores, models.SalesChannel{
			Platform: detail[0],
			StoreKey: detail[1],
		})
	}

	return stores
}

func splitRequestValue(req string) []string {
	if req == "" {
		return nil
	}

	return strings.Split(req, ",")
}

type SearchResponse struct {
	SearchableProducts []*SearchableProduct `json:"searchable_products"`
	Pagination         models.Pagination    `json:"pagination"`
	ParameterString    string               `json:"parameter_string"`
}

type SearchIDsResponse struct {
	IDs             []string          `json:"searchable_product_ids"`
	Pagination      models.Pagination `json:"pagination"`
	ParameterString string            `json:"parameter_string"`
}

type UpsertSearchableProductRequest struct {
	ProductsCenterID    string                `json:"products_center_id" binding:"required"`
	ConnectorsProductID string                `json:"connectors_product_id"`
	Title               string                `json:"title" binding:"required"`
	Status              string                `json:"status" binding:"required,oneof=active inactive draft"`
	Vendor              string                `json:"vendor"`
	Media               []Media               `json:"media"`
	Source              models.Source         `json:"source" binding:"required"`
	Organization        models.Organization   `json:"organization" binding:"required"`
	SalesChannels       []models.SalesChannel `json:"sales_channels"`
	SourceSalesChannels []SourceSalesChannel  `json:"source_sales_channels"`
	Categories          []Category            `json:"categories"`
	Tags                []string              `json:"tags"`
	ProductTypes        []string              `json:"product_types"`
	Attributes          []Attribute           `json:"attributes"`
	Variants            []Variant             `json:"variants" binding:"required,dive"`
	Options             []Option              `json:"options"`
	Combined            Combined              `json:"combined"`
	Version             int64                 `json:"version" binding:"required"`
	IsDeleted           bool                  `json:"is_deleted"`
}

func (req *UpsertSearchableProductRequest) toServiceSearchableProduct() *searchable_products.UpsertSearchableProductRequest {
	serviceModel := &searchable_products.UpsertSearchableProductRequest{
		ProductsCenterID:    req.ProductsCenterID,
		ConnectorsProductID: req.ConnectorsProductID,
		Title:               req.Title,
		Status:              req.Status,
		Vendor:              req.Vendor,
		Organization:        req.Organization,
		Source:              req.Source,
		Categories:          convertRequestToCategories(req.Categories),
		Tags:                req.Tags,
		ProductTypes:        req.ProductTypes,
		SalesChannels:       req.SalesChannels,
		SourceSalesChannels: convertRequestToSaleChannels(req.SourceSalesChannels),
		Attributes:          convertRequestToAttributes(req.Attributes),
		Variants:            convertRequestToVariants(req.Variants),
		Options:             convertRequestToOptions(req.Options),
		Combined:            convertRequestToCombined(req.Combined),
		Media:               convertRequestToMedia(req.Media),
		Version:             req.Version,
		IsDeleted:           req.IsDeleted,
	}

	serviceModel.ID = serviceModel.ProductsCenterID
	for i := range serviceModel.Variants {
		serviceModel.Variants[i].ID = serviceModel.Variants[i].ProductsCenterVariantID
	}

	return serviceModel
}

type Category struct {
	SourceID string `json:"source_id"`
	Name     string `json:"name"`
	Type     string `json:"type"`
	ID       string `json:"id"`
}

func convertRequestToCategories(categories []Category) []searchable_products.Category {
	if categories == nil {
		return nil
	}

	serviceCategories := make([]searchable_products.Category, 0, len(categories))
	for _, v := range categories {
		serviceCategories = append(serviceCategories, searchable_products.Category{
			SourceID: v.SourceID,
			Name:     v.Name,
			Type:     v.Type,
			ID:       v.ID,
		})
	}

	return serviceCategories
}

type Attribute struct {
	SourceID string           `json:"source_id"`
	Name     string           `json:"name"`
	Values   []AttributeValue `json:"values"`
}

type AttributeValue struct {
	SourceID string `json:"source_id"`
	Type     string `json:"type"`
	Value    string `json:"value"`
}

func convertRequestToAttributes(attributes []Attribute) []searchable_products.Attribute {
	if attributes == nil {
		return nil
	}

	serviceAttributes := make([]searchable_products.Attribute, 0, len(attributes))
	for _, v := range attributes {
		serviceAttributes = append(serviceAttributes, searchable_products.Attribute{
			SourceID: v.SourceID,
			Name:     v.Name,
			Values:   convertRequestToAttributesValue(v.Values),
		})
	}

	return serviceAttributes
}

func convertRequestToAttributesValue(values []AttributeValue) []searchable_products.AttributeValue {
	if values == nil {
		return nil
	}

	serviceAttributeValues := make([]searchable_products.AttributeValue, 0, len(values))
	for _, v := range values {
		serviceAttributeValues = append(serviceAttributeValues, searchable_products.AttributeValue{
			SourceID: v.SourceID,
			Type:     v.Type,
			Value:    v.Value,
		})
	}

	return serviceAttributeValues
}

type Option struct {
	Name   string   `json:"name"`
	Values []string `json:"values"`
}

type Combined struct {
	ListingID  string   `json:"listing_id"`
	ErrorCodes []string `json:"error_codes"`
	Status     string   `json:"status"`
}

type Variant struct {
	ID                      string  `json:"id"`
	ProductsCenterVariantID string  `json:"products_center_variant_id" binding:"required"`
	SourceProductID         string  `json:"source_product_id"`
	SourceVariantID         string  `json:"source_variant_id"`
	ConnectorsProductID     string  `json:"connectors_product_id"`
	SourceInventoryItemId   string  `json:"source_inventory_item_id"`
	Sku                     string  `json:"sku"`
	Title                   string  `json:"title"`
	ImageURL                string  `json:"image_url"`
	FulfillmentService      string  `json:"fulfillment_service"`
	InventoryQuantity       float64 `json:"inventory_quantity"`
	Status                  string  `json:"status"`

	Price Price `json:"price"`
}

func convertRequestToOptions(options []Option) []searchable_products.Option {
	if options == nil {
		return nil
	}

	serviceOptions := make([]searchable_products.Option, 0, len(options))
	for _, v := range options {
		serviceOptions = append(serviceOptions, searchable_products.Option{
			Name:   v.Name,
			Values: v.Values,
		})
	}

	return serviceOptions
}

func convertRequestToCombined(combined Combined) searchable_products.Combined {
	return searchable_products.Combined{
		ListingID:  combined.ListingID,
		ErrorCodes: combined.ErrorCodes,
		Status:     combined.Status,
	}
}

func convertRequestToVariants(variants []Variant) []searchable_products.Variant {
	if variants == nil {
		return nil
	}

	serviceVariants := make([]searchable_products.Variant, 0, len(variants))
	for i := range variants {
		serviceVariants = append(serviceVariants, searchable_products.Variant{
			ProductsCenterVariantID: variants[i].ProductsCenterVariantID,
			ConnectorsProductID:     variants[i].ConnectorsProductID,
			SourceProductID:         variants[i].SourceProductID,
			SourceVariantID:         variants[i].SourceVariantID,
			SourceInventoryItemId:   variants[i].SourceInventoryItemId,
			Sku:                     variants[i].Sku,
			Title:                   variants[i].Title,
			ImageURL:                variants[i].ImageURL,
			FulfillmentService:      variants[i].FulfillmentService,
			InventoryQuantity:       variants[i].InventoryQuantity,
			Price:                   convertRequestToPrice(variants[i].Price),
			Status:                  convertRequestToStatus(variants[i].Status),
		})
	}

	return serviceVariants
}

type Price struct {
	Currency string `json:"currency"`
	Amount   string `json:"amount"`
}

func convertRequestToStatus(status string) string {
	if status == consts.ProductsCenterVariantStatusInActive {
		return consts.ProductsCenterVariantStatusInActive
	}
	return consts.ProductsCenterVariantStatusActive
}

func convertRequestToPrice(price Price) searchable_products.Price {
	return searchable_products.Price{
		Currency: price.Currency,
		Amount:   price.Amount,
	}
}

type Media struct {
	Type      string `json:"type"`
	Position  int    `json:"position"`
	Thumbnail struct {
		Url string `json:"url"`
	} `json:"thumbnail"`
	Url             string `json:"url"`
	MimeType        string `json:"mime_type"`
	SourceVideoHost string `json:"source_video_host"`
}

func convertRequestToMedia(reqMedia []Media) []searchable_products.Media {
	if reqMedia == nil {
		return nil
	}

	serviceMedia := make([]searchable_products.Media, 0, len(reqMedia))
	for _, v := range reqMedia {
		m := searchable_products.Media{
			Type:            v.Type,
			Position:        v.Position,
			Url:             v.Url,
			MimeType:        v.MimeType,
			SourceVideoHost: v.SourceVideoHost,
		}
		m.Thumbnail.Url = v.Thumbnail.Url
		serviceMedia = append(serviceMedia, m)
	}

	return serviceMedia
}

type SearchableProduct struct {
	ID                  string                `json:"id"`
	ProductsCenterID    string                `json:"products_center_id"`
	ConnectorsProductID string                `json:"connectors_product_id"`
	Title               string                `json:"title"`
	Status              string                `json:"status"`
	Vendor              string                `json:"vendor"`
	Media               []Media               `json:"media"`
	Source              models.Source         `json:"source"`
	Organization        models.Organization   `json:"organization"`
	SalesChannels       []models.SalesChannel `json:"sales_channels"`
	SourceSalesChannels []SourceSalesChannel  `json:"source_sales_channels"`
	Categories          []Category            `json:"categories"`
	Tags                []string              `json:"tags"`
	ProductTypes        []string              `json:"product_types"`
	Attributes          []Attribute           `json:"attributes"`
	Variants            []Variant             `json:"variants"`
	Options             []Option              `json:"options"`
	Combined            Combined              `json:"combined"`
	Version             int64                 `json:"version" validate:"required"`
	CreatedAt           time.Time             `json:"created_at"`
	UpdatedAt           time.Time             `json:"updated_at"`
	Deleted             bool                  `json:"deleted"`
}

func convertToSearchableProductsResp(serviceModel []*searchable_products.SearchableProduct) []*SearchableProduct {
	resModel := make([]*SearchableProduct, 0, len(serviceModel))
	for _, v := range serviceModel {
		resModel = append(resModel, convertToSearchableProductResp(v))
	}

	return resModel
}

func convertToSearchableProductResp(serviceModel *searchable_products.SearchableProduct) *SearchableProduct {
	if serviceModel == nil {
		return nil
	}

	resModel := &SearchableProduct{
		ID:                  serviceModel.ID,
		ProductsCenterID:    serviceModel.ProductsCenterID,
		ConnectorsProductID: serviceModel.ConnectorsProductID,
		Title:               serviceModel.Title,
		Status:              serviceModel.Status,
		Vendor:              serviceModel.Vendor,
		Organization:        serviceModel.Organization,
		Source:              serviceModel.Source,
		Media:               convertToMediaResp(serviceModel.Media),
		Categories:          convertToCategoriesResp(serviceModel.Categories),
		Tags:                serviceModel.Tags,
		ProductTypes:        serviceModel.ProductTypes,
		SalesChannels:       serviceModel.SalesChannels,
		SourceSalesChannels: convertToSaleChannelsResp(serviceModel.SourceSalesChannels),
		Attributes:          convertToAttributesResp(serviceModel.Attributes),
		Variants:            convertToVariantsResp(serviceModel.Variants),
		Options:             convertToOptionsResp(serviceModel.Options),
		Combined:            convertToCombinedResp(serviceModel.Combined),
		Version:             serviceModel.Version,
		CreatedAt:           serviceModel.CreatedAt,
		UpdatedAt:           serviceModel.UpdatedAt,
		Deleted:             serviceModel.Deleted,
	}

	return resModel
}

func convertToMediaResp(values []searchable_products.Media) []Media {
	resModel := make([]Media, 0, len(values))
	for _, v := range values {
		resModel = append(resModel, Media{
			Type:     v.Type,
			Position: v.Position,
			Url:      v.Url,
			MimeType: v.MimeType,
			Thumbnail: struct {
				Url string `json:"url"`
			}{
				Url: v.Thumbnail.Url,
			},
			SourceVideoHost: v.SourceVideoHost,
		})
	}

	return resModel
}

func convertToCategoriesResp(serviceCategories []searchable_products.Category) []Category {
	resModel := make([]Category, 0, len(serviceCategories))
	for _, v := range serviceCategories {
		resModel = append(resModel, Category{
			SourceID: v.SourceID,
			Name:     v.Name,
			Type:     v.Type,
			ID:       v.ID,
		})
	}

	return resModel
}

func convertToAttributesResp(serviceAttributes []searchable_products.Attribute) []Attribute {
	resModel := make([]Attribute, 0, len(serviceAttributes))
	for _, v := range serviceAttributes {
		resModel = append(resModel, Attribute{
			SourceID: v.SourceID,
			Name:     v.Name,
			Values:   convertToAttributeValuesResp(v.Values),
		})
	}

	return resModel
}

func convertToAttributeValuesResp(values []searchable_products.AttributeValue) []AttributeValue {
	resModel := make([]AttributeValue, 0, len(values))
	for _, v := range values {
		resModel = append(resModel, AttributeValue{
			SourceID: v.SourceID,
			Type:     v.Type,
			Value:    v.Value,
		})
	}

	return resModel
}

func convertToOptionsResp(serviceOptions []searchable_products.Option) []Option {
	resModel := make([]Option, 0, len(serviceOptions))
	for _, v := range serviceOptions {
		resModel = append(resModel, Option{
			Name:   v.Name,
			Values: v.Values,
		})
	}

	return resModel
}

func convertToCombinedResp(serviceCombined searchable_products.Combined) Combined {
	return Combined{
		ListingID:  serviceCombined.ListingID,
		Status:     serviceCombined.Status,
		ErrorCodes: serviceCombined.ErrorCodes,
	}
}

func convertToVariantsResp(serviceVariants []searchable_products.Variant) []Variant {
	resModel := make([]Variant, 0, len(serviceVariants))
	for i := range serviceVariants {
		resModel = append(resModel, Variant{
			ID:                      serviceVariants[i].ID,
			ProductsCenterVariantID: serviceVariants[i].ProductsCenterVariantID,
			ConnectorsProductID:     serviceVariants[i].ConnectorsProductID,
			SourceProductID:         serviceVariants[i].SourceProductID,
			SourceVariantID:         serviceVariants[i].SourceVariantID,
			SourceInventoryItemId:   serviceVariants[i].SourceInventoryItemId,
			Sku:                     serviceVariants[i].Sku,
			Title:                   serviceVariants[i].Title,
			ImageURL:                serviceVariants[i].ImageURL,
			FulfillmentService:      serviceVariants[i].FulfillmentService,
			InventoryQuantity:       serviceVariants[i].InventoryQuantity,
			Price:                   convertToPriceResp(serviceVariants[i].Price),
			Status:                  serviceVariants[i].Status,
		})
	}

	return resModel
}

func convertToPriceResp(price searchable_products.Price) Price {
	return Price{
		Currency: price.Currency,
		Amount:   price.Amount,
	}
}

type esProxySearchableProductReq struct {
	Query string `form:"query" binding:"required"`
}

type OptionsAggregationRequest struct {
	SourceIDs        []string `json:"source_ids"`
	PredefinedFilter string   `json:"predefined_filter"`
	OrganizationID   string   `json:"organization_id"`
	SourcePlatform   string   `json:"source_platform"`
	SourceStoreKey   string   `json:"source_store_key"`
}
type OptionsAggregationResponse struct {
	OptionNames  []string `json:"option_names"`
	ProductCount int      `json:"product_count"`
}

func (r *OptionsAggregationRequest) toServiceOptionsAggregationArgs() *searchable_products.OptionsAggregationArgs {
	return &searchable_products.OptionsAggregationArgs{
		SourceIDs:        r.SourceIDs,
		PredefinedFilter: r.PredefinedFilter,
		OrganizationID:   r.OrganizationID,
		SourceStoreKey:   r.SourceStoreKey,
		SourcePlatform:   r.SourcePlatform,
	}
}

func convertToOptionsAggregationResponse(serviceModel *searchable_products.OptionsAggregationResult) *OptionsAggregationResponse {
	if serviceModel == nil {
		return nil
	}

	return &OptionsAggregationResponse{
		OptionNames:  serviceModel.OptionNames,
		ProductCount: serviceModel.ProductCount,
	}
}

type SourceSalesChannel struct {
	ID   string `json:"id"`
	Name string `json:"name"`
}

func convertRequestToSaleChannels(salesChannels []SourceSalesChannel) []searchable_products.SourceSalesChannel {
	if salesChannels == nil {
		return nil
	}

	serviceSaleChannels := make([]searchable_products.SourceSalesChannel, 0, len(salesChannels))
	for _, v := range salesChannels {
		serviceSaleChannels = append(serviceSaleChannels, searchable_products.SourceSalesChannel{
			ID:   v.ID,
			Name: v.Name,
		})
	}

	return serviceSaleChannels
}

func convertToSaleChannelsResp(serviceSaleChannels []searchable_products.SourceSalesChannel) []SourceSalesChannel {
	if serviceSaleChannels == nil {
		return nil
	}

	resModel := make([]SourceSalesChannel, 0, len(serviceSaleChannels))
	for _, v := range serviceSaleChannels {
		resModel = append(resModel, SourceSalesChannel{
			ID:   v.ID,
			Name: v.Name,
		})
	}

	return resModel
}
