package searchable_products

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/spf13/viper"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"github.com/tidwall/gjson"

	"github.com/AfterShip/gopkg/cfg"
	"github.com/AfterShip/pltf-pd-product-listings/internal/config"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/searchable_products"
	"github.com/AfterShip/pltf-pd-product-listings/internal/logger"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
)

func TestProduct_GetSearchableProduct(t *testing.T) {
	configs := new(config.Config)
	_, err := cfg.LoadViperConfig(configs, func(v *viper.Viper) { v.AddConfigPath("../../../../cmd/apiserver/conf") })
	require.NoError(t, err)

	mockSvc := new(searchable_products.MockService)

	testcases := []struct {
		name      string
		path      string
		mock      func()
		expCode   int
		respCheck func(*testing.T, string)
	}{
		{
			name:    "Good case",
			path:    "/searchable-products/:1000",
			expCode: http.StatusOK,
			mock: func() {
				mockSvc.On("GetByID", mock.Anything).
					Return(searchable_products.SearchableProduct{}, nil).Once()
			},
		},
	}
	for _, tt := range testcases {
		t.Run(tt.name, func(t *testing.T) {
			w := httptest.NewRecorder()
			gin.SetMode(gin.TestMode)
			_, eng := gin.CreateTestContext(w)

			if tt.mock != nil {
				tt.mock()
			}

			NewProduct(logger.Get(), nil, mockSvc).RegisterRoutes(eng.RouterGroup.Group(""))

			req, _ := http.NewRequestWithContext(context.Background(), http.MethodGet, tt.path, nil)
			eng.ServeHTTP(w, req)
			resp := w.Body.String()
			require.Equal(t, tt.expCode, w.Code, resp)
			if tt.respCheck != nil {
				tt.respCheck(t, resp)
			}
		})
	}
}

func TestProduct_UpsertSearchableProduct(t *testing.T) {
	configs := new(config.Config)
	_, err := cfg.LoadViperConfig(configs, func(v *viper.Viper) { v.AddConfigPath("../../../../cmd/apiserver/conf") })
	require.NoError(t, err)

	mockSvc := new(searchable_products.MockService)

	testcases := []struct {
		name      string
		path      string
		body      string
		mock      func()
		expCode   int
		respCheck func(*testing.T, string)
	}{
		{
			name:    "422 case",
			path:    "/searchable-products",
			body:    `{}`,
			expCode: http.StatusUnprocessableEntity,
		},
		{
			name:    "Good case",
			path:    "/searchable-products",
			body:    buildGoodUpsertSearchableProductRequestBody("81a85a87e08a42fabd2925b3be77663f"),
			expCode: http.StatusOK,
			mock: func() {
				mockSvc.On("Upsert", mock.Anything, mock.Anything).
					Return(searchable_products.SearchableProduct{}, nil).Once()
			},
		},
	}
	for _, tt := range testcases {
		t.Run(tt.name, func(t *testing.T) {
			w := httptest.NewRecorder()
			gin.SetMode(gin.TestMode)
			_, eng := gin.CreateTestContext(w)

			if tt.mock != nil {
				tt.mock()
			}

			NewProduct(logger.Get(), nil, mockSvc).RegisterRoutes(eng.RouterGroup.Group(""))

			body := bytes.NewBufferString(tt.body)

			req, _ := http.NewRequestWithContext(context.Background(), http.MethodPost, "/internal"+tt.path, body)
			eng.ServeHTTP(w, req)
			resp := w.Body.String()
			require.Equal(t, tt.expCode, w.Code, resp)
			if tt.respCheck != nil {
				tt.respCheck(t, resp)
			}
		})
	}
}

func TestProduct_ListSearchableProducts(t *testing.T) {
	configs := new(config.Config)
	_, err := cfg.LoadViperConfig(configs, func(v *viper.Viper) { v.AddConfigPath("../../../../cmd/apiserver/conf") })
	require.NoError(t, err)

	mockSvc := new(searchable_products.MockService)

	testcases := []struct {
		name      string
		path      string
		mock      func()
		expCode   int
		respCheck func(*testing.T, string)
	}{
		{
			name:    "page gte 1000 case",
			path:    "/searchable-products?page=1001",
			expCode: http.StatusUnprocessableEntity,
		},
		{
			name:    "limit gte 1000 case",
			path:    "/searchable-products?limit=1001",
			expCode: http.StatusUnprocessableEntity,
		},
		{
			name:    "Good case",
			path:    "/searchable-products?limit=100",
			expCode: http.StatusOK,
			mock: func() {
				mockSvc.On("Search", mock.Anything, mock.Anything).
					Return([]*searchable_products.SearchableProduct{}, models.Pagination{}, nil).Once()
			},
		},
	}
	for _, tt := range testcases {
		t.Run(tt.name, func(t *testing.T) {
			w := httptest.NewRecorder()
			gin.SetMode(gin.TestMode)
			_, eng := gin.CreateTestContext(w)

			if tt.mock != nil {
				tt.mock()
			}

			NewProduct(logger.Get(), nil, mockSvc).RegisterRoutes(eng.RouterGroup.Group(""))

			req, _ := http.NewRequestWithContext(context.Background(), http.MethodGet, tt.path, nil)
			eng.ServeHTTP(w, req)
			resp := w.Body.String()
			require.Equal(t, tt.expCode, w.Code, resp)
			if tt.respCheck != nil {
				tt.respCheck(t, resp)
			}
		})
	}
}

func buildGoodUpsertSearchableProductRequestBody(productCenterID string) string {
	req := UpsertSearchableProductRequest{
		ProductsCenterID:    productCenterID,
		ConnectorsProductID: "f0fe2ebf0e614770a095e31e84be758b",
		Title:               "test-title",
		Status:              "active",
		Source: models.Source{
			App: models.App{
				Platform: "shopify",
				Key:      "charm",
			},
			ID:   "7173220958347",
			Type: "integration",
		},
		Organization: models.Organization{
			ID: "c7061231a19f4154bd167277f6e65991",
		},
		Variants: []Variant{
			{
				ProductsCenterVariantID: "2c668ef3ee9b486583352417e71aca59",
			},
		},
		Version: time.Now().UnixNano(),
	}
	body, _ := json.Marshal(&req) // nolint: errchkjson
	return string(body)
}

func TestProduct_GetSearchableProductIDs(t *testing.T) {
	configs := new(config.Config)
	_, err := cfg.LoadViperConfig(configs, func(v *viper.Viper) { v.AddConfigPath("../../../../cmd/apiserver/conf") })
	require.NoError(t, err)

	mockSvc := new(searchable_products.MockService)

	testcases := []struct {
		name      string
		path      string
		mock      func()
		expCode   int
		respCheck func(*testing.T, string)
	}{
		{
			name:    "page gte 1000 case",
			path:    "/searchable-product-ids?page=1001",
			expCode: http.StatusUnprocessableEntity,
		},
		{
			name:    "limit gte 1000 case",
			path:    "/searchable-product-ids?limit=1001",
			expCode: http.StatusUnprocessableEntity,
		},
		{
			name:    "Good case",
			path:    "/searchable-product-ids?limit=100",
			expCode: http.StatusOK,
			mock: func() {
				mockSvc.On("SearchIDs", mock.Anything, mock.Anything).
					Return([]string{}, models.Pagination{}, nil).Once()
			},
		},
	}
	for _, tt := range testcases {
		t.Run(tt.name, func(t *testing.T) {
			w := httptest.NewRecorder()
			gin.SetMode(gin.TestMode)
			_, eng := gin.CreateTestContext(w)

			if tt.mock != nil {
				tt.mock()
			}

			NewProduct(logger.Get(), nil, mockSvc).RegisterRoutes(eng.RouterGroup.Group(""))

			req, _ := http.NewRequestWithContext(context.Background(), http.MethodGet, tt.path, nil)
			eng.ServeHTTP(w, req)
			resp := w.Body.String()
			require.Equal(t, tt.expCode, w.Code, resp)
			if tt.respCheck != nil {
				tt.respCheck(t, resp)
			}
		})
	}
}

func TestProduct_OptionsAggregation(t *testing.T) {
	configs := new(config.Config)
	_, err := cfg.LoadViperConfig(configs, func(v *viper.Viper) { v.AddConfigPath("../../../../cmd/apiserver/conf") })
	require.NoError(t, err)

	mockSvc := new(searchable_products.MockService)

	testcases := []struct {
		name      string
		path      string
		body      string
		mock      func()
		expCode   int
		respCheck func(*testing.T, string)
	}{
		{
			name:    "请求体为空时绑定失败",
			path:    "/searchable-products/options/aggregation",
			body:    ``,
			expCode: http.StatusUnprocessableEntity,
		},
		{
			name:    "请求体格式错误时绑定失败",
			path:    "/searchable-products/options/aggregation",
			body:    `{invalid json`,
			expCode: http.StatusUnprocessableEntity,
		},
		{
			name:    "服务层返回错误",
			path:    "/searchable-products/options/aggregation",
			body:    `{"organization_id":"org123","app_keys":["shopify","amazon"]}`,
			expCode: http.StatusInternalServerError,
			mock: func() {
				mockSvc.On("OptionsAggregation", mock.Anything, mock.Anything).Return(searchable_products.OptionsAggregationArgs{}, errors.New("service error")).Once()
			},
		},
		{
			name:    "成功返回空聚合结果",
			path:    "/searchable-products/options/aggregation",
			body:    `{"organization_id":"org123","app_keys":["shopify"]}`,
			expCode: http.StatusOK,
			mock: func() {
				mockSvc.On("OptionsAggregation", mock.Anything, mock.Anything).Return(searchable_products.OptionsAggregationResult{
					OptionNames:  nil,
					ProductCount: 0,
				}, nil).Once()
			},
			respCheck: func(t *testing.T, resp string) {
				data := gjson.Parse(resp).Get("data")
				require.True(t, data.Exists())
				require.Equal(t, 0, len(data.Get("option_names").Array()))
				require.Equal(t, int64(0), data.Get("product_count").Int())
			},
		},
		{
			name:    "成功返回完整聚合结果",
			path:    "/searchable-products/options/aggregation",
			body:    `{"organization_id":"org123","app_keys":["shopify","amazon"]}`,
			expCode: http.StatusOK,
			mock: func() {
				mockSvc.On("OptionsAggregation", mock.Anything, mock.Anything).Return(&searchable_products.OptionsAggregationResult{
					OptionNames:  []string{"Nike", "Adidas", "Puma"},
					ProductCount: 100,
				}, nil).Once()
			},
			respCheck: func(t *testing.T, resp string) {
				data := gjson.Parse(resp).Get("data")
				require.True(t, data.Exists())
				require.Equal(t, 3, len(data.Get("option_names").Array()))
				require.Equal(t, "Nike", data.Get("option_names").Array()[0].String())
				require.Equal(t, int64(100), data.Get("product_count").Int())
			},
		},
	}

	for _, tt := range testcases {
		t.Run(tt.name, func(t *testing.T) {
			w := httptest.NewRecorder()
			gin.SetMode(gin.TestMode)
			_, eng := gin.CreateTestContext(w)

			if tt.mock != nil {
				tt.mock()
			}

			NewProduct(logger.Get(), nil, mockSvc).RegisterRoutes(eng.RouterGroup.Group(""))

			body := bytes.NewBufferString(tt.body)
			req, _ := http.NewRequestWithContext(context.Background(), http.MethodPost, tt.path, body)
			req.Header.Set("Content-Type", "application/json")
			eng.ServeHTTP(w, req)
			resp := w.Body.String()
			require.Equal(t, tt.expCode, w.Code, resp)
			if tt.respCheck != nil {
				tt.respCheck(t, resp)
			}
		})
	}
}

func TestProduct_DeleteSearchableProduct(t *testing.T) {
	configs := new(config.Config)
	_, err := cfg.LoadViperConfig(configs, func(v *viper.Viper) { v.AddConfigPath("../../../../cmd/apiserver/conf") })
	require.NoError(t, err)

	mockSvc := new(searchable_products.MockService)

	testcases := []struct {
		name      string
		path      string
		mock      func()
		expCode   int
		respCheck func(*testing.T, string)
	}{
		{
			name:    "缺少ID参数",
			path:    "/internal/searchable-products/",
			expCode: http.StatusNotFound,
		},
		{
			name:    "服务层返回错误",
			path:    "/internal/searchable-products/product123",
			expCode: http.StatusInternalServerError,
			mock: func() {
				mockSvc.On("DeleteByID", mock.Anything, mock.Anything).Return(errors.New("delete error")).Once()
			},
		},
		{
			name:    "成功删除产品",
			path:    "/internal/searchable-products/product123",
			expCode: http.StatusOK,
			mock: func() {
				mockSvc.On("DeleteByID", mock.Anything, mock.Anything).Return(nil).Once()
			},
			respCheck: func(t *testing.T, resp string) {
				data := gjson.Parse(resp).Get("data")
				require.True(t, data.Exists())
				require.Equal(t, "deleted", data.String())
			},
		},
		{
			name:    "删除包含特殊字符的产品ID",
			path:    "/internal/searchable-products/product-123_test",
			expCode: http.StatusOK,
			mock: func() {
				mockSvc.On("DeleteByID", mock.Anything, mock.Anything).Return(nil).Once()
			},
			respCheck: func(t *testing.T, resp string) {
				data := gjson.Parse(resp).Get("data")
				require.True(t, data.Exists())
				require.Equal(t, "deleted", data.String())
			},
		},
		{
			name:    "删除长ID的产品",
			path:    "/internal/searchable-products/very-long-product-id-12345678901234567890",
			expCode: http.StatusOK,
			mock: func() {
				mockSvc.On("DeleteByID", mock.Anything, mock.Anything).Return(nil).Once()
			},
			respCheck: func(t *testing.T, resp string) {
				data := gjson.Parse(resp).Get("data")
				require.True(t, data.Exists())
				require.Equal(t, "deleted", data.String())
			},
		},
		{
			name:    "删除不存在的产品",
			path:    "/internal/searchable-products/nonexistent-product",
			expCode: http.StatusInternalServerError,
			mock: func() {
				mockSvc.On("DeleteByID", mock.Anything, mock.Anything).Return(errors.New("product not found")).Once()
			},
		},
		{
			name:    "删除包含数字的产品ID",
			path:    "/internal/searchable-products/123456789",
			expCode: http.StatusOK,
			mock: func() {
				mockSvc.On("DeleteByID", mock.Anything, mock.Anything).Return(nil).Once()
			},
			respCheck: func(t *testing.T, resp string) {
				data := gjson.Parse(resp).Get("data")
				require.True(t, data.Exists())
				require.Equal(t, "deleted", data.String())
			},
		},
		{
			name:    "服务层返回特定错误",
			path:    "/internal/searchable-products/product456",
			expCode: http.StatusInternalServerError,
			mock: func() {
				mockSvc.On("DeleteByID", mock.Anything, mock.Anything).Return(errors.New("database connection failed")).Once()
			},
		},
	}

	for _, tt := range testcases {
		t.Run(tt.name, func(t *testing.T) {
			w := httptest.NewRecorder()
			gin.SetMode(gin.TestMode)
			_, eng := gin.CreateTestContext(w)

			if tt.mock != nil {
				tt.mock()
			}

			NewProduct(logger.Get(), nil, mockSvc).RegisterRoutes(eng.RouterGroup.Group(""))

			req, _ := http.NewRequestWithContext(context.Background(), http.MethodDelete, tt.path, nil)
			eng.ServeHTTP(w, req)
			resp := w.Body.String()
			require.Equal(t, tt.expCode, w.Code, resp)
			if tt.respCheck != nil {
				tt.respCheck(t, resp)
			}
		})
	}
}
