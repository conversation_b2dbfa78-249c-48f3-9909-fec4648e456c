package searchable_products

import (
	"github.com/AfterShip/connectors-library/gin/responder"
	"github.com/AfterShip/pltf-pd-product-listings/internal/utils/elasticsearch"
)

const (
	TooManyESRequest    responder.MetaCode = 42901
	BatchUpsertESFailed responder.MetaCode = 50001
	InvalidESCursor     responder.MetaCode = 42206
)

var errorResponseMapping = map[error]responder.Meta{
	elasticsearch.ErrTooManyRequest: {
		Code: TooManyESRequest,
		Description: responder.Description{
			Type:    "TooManyESRequest",
			Message: "Too many requests to Elasticsearch.",
		},
	},
	elasticsearch.ErrBatchUpsertES: {
		Code: BatchUpsertESFailed,
		Description: responder.Description{
			Type:    "BatchUpsertESFailed",
			Message: "Failed to batch upsert to Elasticsearch.",
		},
	},
	elasticsearch.ErrorInvalidCursor: {
		Code: InvalidESCursor,
		Description: responder.Description{
			Type:    "InvalidESCursor",
			Message: "The cursor is invalid.",
		},
	},
}
