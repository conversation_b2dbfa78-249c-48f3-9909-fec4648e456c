package searchable_products

import (
	"encoding/base64"

	"github.com/gin-gonic/gin"

	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/searchable_products"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
)

func (p *Product) esProxySearchableProductCount(c *gin.Context) {
	ctx := c.Request.Context()

	req := new(esProxySearchableProductReq)
	if err := c.ShouldBindQuery(req); err != nil {
		p.responder.ResponseWithErrorCode(c, models.CodeMissRequiredQueryParam, err)
		return
	}

	query, err := base64.StdEncoding.DecodeString(req.Query)
	if err != nil {
		p.responder.ResponseWithError(c, err)
		return
	}

	countV1, err := p.svc.ESProxyProductListingCount(ctx, searchable_products.IndexAlias, query)
	if err != nil {
		p.responder.ResponseWithError(c, err)
		return
	}

	countV2, err := p.svc.ESProxyProductListingCount(ctx, searchable_products.IndexAliasV2, query)
	if err != nil {
		p.responder.ResponseWithError(c, err)
		return
	}

	p.responder.ResponseWithOK(c, gin.H{"count_v1": countV1, "count_v2": countV2})
}

func (p *Product) esProxySearchableProductSearch(c *gin.Context) {
	ctx := c.Request.Context()

	req := new(esProxySearchableProductReq)
	if err := c.ShouldBindQuery(req); err != nil {
		p.responder.ResponseWithErrorCode(c, models.CodeMissRequiredQueryParam, err)
		return
	}

	query, err := base64.StdEncoding.DecodeString(req.Query)
	if err != nil {
		p.responder.ResponseWithError(c, err)
		return
	}

	resultV1, err := p.svc.ESProxyProductListingSearch(ctx, searchable_products.IndexAlias, query)
	if err != nil {
		p.responder.ResponseWithError(c, err)
		return
	}

	resultV2, err := p.svc.ESProxyProductListingSearch(ctx, searchable_products.IndexAliasV2, query)
	if err != nil {
		p.responder.ResponseWithError(c, err)
		return
	}

	p.responder.ResponseWithOK(c, gin.H{"result_v1": resultV1, "result_v2": resultV2})
}
