package searchable_products

import (
	"testing"

	"github.com/stretchr/testify/require"

	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/searchable_products"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
)

func TestConvertToSaleChannelsResp(t *testing.T) {
	tests := []struct {
		name     string
		input    []searchable_products.SourceSalesChannel
		expected []SourceSalesChannel
	}{
		{
			name:     "nil输入",
			input:    nil,
			expected: nil,
		},
		{
			name:     "空数组",
			input:    []searchable_products.SourceSalesChannel{},
			expected: []SourceSalesChannel{},
		},
		{
			name: "单个销售渠道",
			input: []searchable_products.SourceSalesChannel{
				{
					ID:   "channel1",
					Name: "Shopify Store",
				},
			},
			expected: []SourceSalesChannel{
				{
					ID:   "channel1",
					Name: "Shopify Store",
				},
			},
		},
		{
			name: "多个销售渠道",
			input: []searchable_products.SourceSalesChannel{
				{
					ID:   "channel1",
					Name: "Shopify Store",
				},
				{
					ID:   "channel2",
					Name: "Amazon Store",
				},
				{
					ID:   "channel3",
					Name: "eBay Store",
				},
			},
			expected: []SourceSalesChannel{
				{
					ID:   "channel1",
					Name: "Shopify Store",
				},
				{
					ID:   "channel2",
					Name: "Amazon Store",
				},
				{
					ID:   "channel3",
					Name: "eBay Store",
				},
			},
		},
		{
			name: "包含空字符串的销售渠道",
			input: []searchable_products.SourceSalesChannel{
				{
					ID:   "",
					Name: "",
				},
				{
					ID:   "channel1",
					Name: "Valid Store",
				},
			},
			expected: []SourceSalesChannel{
				{
					ID:   "",
					Name: "",
				},
				{
					ID:   "channel1",
					Name: "Valid Store",
				},
			},
		},
		{
			name: "包含特殊字符的销售渠道",
			input: []searchable_products.SourceSalesChannel{
				{
					ID:   "channel-123_test",
					Name: "Store & Co.",
				},
				{
					ID:   "channel@456#test",
					Name: "Store/Shop (Premium)",
				},
			},
			expected: []SourceSalesChannel{
				{
					ID:   "channel-123_test",
					Name: "Store & Co.",
				},
				{
					ID:   "channel@456#test",
					Name: "Store/Shop (Premium)",
				},
			},
		},
		{
			name: "长名称的销售渠道",
			input: []searchable_products.SourceSalesChannel{
				{
					ID:   "very-long-channel-id-12345678901234567890",
					Name: "Very Long Store Name That Contains Many Characters And Symbols",
				},
			},
			expected: []SourceSalesChannel{
				{
					ID:   "very-long-channel-id-12345678901234567890",
					Name: "Very Long Store Name That Contains Many Characters And Symbols",
				},
			},
		},
		{
			name: "包含中文的销售渠道",
			input: []searchable_products.SourceSalesChannel{
				{
					ID:   "channel_cn",
					Name: "中文店铺名称",
				},
			},
			expected: []SourceSalesChannel{
				{
					ID:   "channel_cn",
					Name: "中文店铺名称",
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := convertToSaleChannelsResp(tt.input)

			if tt.expected == nil {
				require.Nil(t, result)
			} else {
				require.NotNil(t, result)
				require.Equal(t, len(tt.expected), len(result))

				for i, expected := range tt.expected {
					require.Equal(t, expected.ID, result[i].ID)
					require.Equal(t, expected.Name, result[i].Name)
				}
			}
		})
	}
}

func TestOptionsAggregationRequest_toServiceOptionsAggregationArgs(t *testing.T) {
	tests := []struct {
		name     string
		request  OptionsAggregationRequest
		expected *searchable_products.OptionsAggregationArgs
	}{
		{
			name: "正常转换所有字段",
			request: OptionsAggregationRequest{
				SourceIDs:        []string{"id1", "id2", "id3"},
				PredefinedFilter: "active",
				OrganizationID:   "org123",
				SourcePlatform:   "shopify",
				SourceStoreKey:   "store456",
			},
			expected: &searchable_products.OptionsAggregationArgs{
				SourceIDs:        []string{"id1", "id2", "id3"},
				PredefinedFilter: "active",
				OrganizationID:   "org123",
				SourcePlatform:   "shopify",
				SourceStoreKey:   "store456",
			},
		},
		{
			name: "空字符串字段的转换",
			request: OptionsAggregationRequest{
				SourceIDs:        []string{},
				PredefinedFilter: "",
				OrganizationID:   "",
				SourcePlatform:   "",
				SourceStoreKey:   "",
			},
			expected: &searchable_products.OptionsAggregationArgs{
				SourceIDs:        []string{},
				PredefinedFilter: "",
				OrganizationID:   "",
				SourcePlatform:   "",
				SourceStoreKey:   "",
			},
		},
		{
			name: "nil SourceIDs的转换",
			request: OptionsAggregationRequest{
				SourceIDs:        nil,
				PredefinedFilter: "inactive",
				OrganizationID:   "org789",
				SourcePlatform:   "amazon",
				SourceStoreKey:   "store999",
			},
			expected: &searchable_products.OptionsAggregationArgs{
				SourceIDs:        nil,
				PredefinedFilter: "inactive",
				OrganizationID:   "org789",
				SourcePlatform:   "amazon",
				SourceStoreKey:   "store999",
			},
		},
		{
			name: "单个SourceID的转换",
			request: OptionsAggregationRequest{
				SourceIDs:        []string{"single_id"},
				PredefinedFilter: "draft",
				OrganizationID:   "org456",
				SourcePlatform:   "ebay",
				SourceStoreKey:   "store123",
			},
			expected: &searchable_products.OptionsAggregationArgs{
				SourceIDs:        []string{"single_id"},
				PredefinedFilter: "draft",
				OrganizationID:   "org456",
				SourcePlatform:   "ebay",
				SourceStoreKey:   "store123",
			},
		},
		{
			name: "包含特殊字符的转换",
			request: OptionsAggregationRequest{
				SourceIDs:        []string{"id-123_test", "id@456#test"},
				PredefinedFilter: "active-status",
				OrganizationID:   "org-123_test",
				SourcePlatform:   "shopify-plus",
				SourceStoreKey:   "store-456_test",
			},
			expected: &searchable_products.OptionsAggregationArgs{
				SourceIDs:        []string{"id-123_test", "id@456#test"},
				PredefinedFilter: "active-status",
				OrganizationID:   "org-123_test",
				SourcePlatform:   "shopify-plus",
				SourceStoreKey:   "store-456_test",
			},
		},
		{
			name: "长字符串的转换",
			request: OptionsAggregationRequest{
				SourceIDs:        []string{"very-long-source-id-12345678901234567890"},
				PredefinedFilter: "very-long-predefined-filter-name",
				OrganizationID:   "very-long-organization-id-12345678901234567890",
				SourcePlatform:   "very-long-platform-name",
				SourceStoreKey:   "very-long-store-key-12345678901234567890",
			},
			expected: &searchable_products.OptionsAggregationArgs{
				SourceIDs:        []string{"very-long-source-id-12345678901234567890"},
				PredefinedFilter: "very-long-predefined-filter-name",
				OrganizationID:   "very-long-organization-id-12345678901234567890",
				SourcePlatform:   "very-long-platform-name",
				SourceStoreKey:   "very-long-store-key-12345678901234567890",
			},
		},
		{
			name: "多个SourceIDs的转换",
			request: OptionsAggregationRequest{
				SourceIDs:        []string{"id1", "id2", "id3", "id4", "id5"},
				PredefinedFilter: "active",
				OrganizationID:   "org123",
				SourcePlatform:   "shopify",
				SourceStoreKey:   "store456",
			},
			expected: &searchable_products.OptionsAggregationArgs{
				SourceIDs:        []string{"id1", "id2", "id3", "id4", "id5"},
				PredefinedFilter: "active",
				OrganizationID:   "org123",
				SourcePlatform:   "shopify",
				SourceStoreKey:   "store456",
			},
		},
		{
			name: "包含空字符串的SourceIDs转换",
			request: OptionsAggregationRequest{
				SourceIDs:        []string{"", "valid_id", ""},
				PredefinedFilter: "active",
				OrganizationID:   "org123",
				SourcePlatform:   "shopify",
				SourceStoreKey:   "store456",
			},
			expected: &searchable_products.OptionsAggregationArgs{
				SourceIDs:        []string{"", "valid_id", ""},
				PredefinedFilter: "active",
				OrganizationID:   "org123",
				SourcePlatform:   "shopify",
				SourceStoreKey:   "store456",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tt.request.toServiceOptionsAggregationArgs()

			require.NotNil(t, result)
			require.Equal(t, tt.expected.PredefinedFilter, result.PredefinedFilter)
			require.Equal(t, tt.expected.OrganizationID, result.OrganizationID)
			require.Equal(t, tt.expected.SourcePlatform, result.SourcePlatform)
			require.Equal(t, tt.expected.SourceStoreKey, result.SourceStoreKey)

			if tt.expected.SourceIDs == nil {
				require.Nil(t, result.SourceIDs)
			} else {
				require.Equal(t, len(tt.expected.SourceIDs), len(result.SourceIDs))
				require.Equal(t, tt.expected.SourceIDs, result.SourceIDs)
			}
		})
	}
}

func TestConvertToPriceResp(t *testing.T) {
	tests := []struct {
		name     string
		input    searchable_products.Price
		expected Price
	}{
		{
			name: "正常价格转换",
			input: searchable_products.Price{
				Currency: "USD",
				Amount:   "10.99",
			},
			expected: Price{
				Currency: "USD",
				Amount:   "10.99",
			},
		},
		{
			name: "空字符串价格转换",
			input: searchable_products.Price{
				Currency: "",
				Amount:   "",
			},
			expected: Price{
				Currency: "",
				Amount:   "",
			},
		},
		{
			name: "不同货币类型转换",
			input: searchable_products.Price{
				Currency: "EUR",
				Amount:   "25.50",
			},
			expected: Price{
				Currency: "EUR",
				Amount:   "25.50",
			},
		},
		{
			name: "大金额转换",
			input: searchable_products.Price{
				Currency: "USD",
				Amount:   "9999.99",
			},
			expected: Price{
				Currency: "USD",
				Amount:   "9999.99",
			},
		},
		{
			name: "零价格转换",
			input: searchable_products.Price{
				Currency: "USD",
				Amount:   "0.00",
			},
			expected: Price{
				Currency: "USD",
				Amount:   "0.00",
			},
		},
		{
			name: "小数点后多位数转换",
			input: searchable_products.Price{
				Currency: "JPY",
				Amount:   "1000.000",
			},
			expected: Price{
				Currency: "JPY",
				Amount:   "1000.000",
			},
		},
		{
			name: "特殊货币代码转换",
			input: searchable_products.Price{
				Currency: "CNY",
				Amount:   "88.88",
			},
			expected: Price{
				Currency: "CNY",
				Amount:   "88.88",
			},
		},
		{
			name: "包含特殊字符的金额转换",
			input: searchable_products.Price{
				Currency: "USD",
				Amount:   "10.5",
			},
			expected: Price{
				Currency: "USD",
				Amount:   "10.5",
			},
		},
		{
			name: "长货币代码转换",
			input: searchable_products.Price{
				Currency: "LONG_CURRENCY_CODE",
				Amount:   "123.45",
			},
			expected: Price{
				Currency: "LONG_CURRENCY_CODE",
				Amount:   "123.45",
			},
		},
		{
			name: "整数金额转换",
			input: searchable_products.Price{
				Currency: "USD",
				Amount:   "100",
			},
			expected: Price{
				Currency: "USD",
				Amount:   "100",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := convertToPriceResp(tt.input)

			require.Equal(t, tt.expected.Currency, result.Currency)
			require.Equal(t, tt.expected.Amount, result.Amount)
		})
	}
}

func TestConvertToVariantsResp(t *testing.T) {
	tests := []struct {
		name     string
		input    []searchable_products.Variant
		expected []Variant
	}{
		{
			name:     "nil输入",
			input:    nil,
			expected: []Variant{},
		},
		{
			name:     "空数组",
			input:    []searchable_products.Variant{},
			expected: []Variant{},
		},
		{
			name: "单个变体转换",
			input: []searchable_products.Variant{
				{
					ID:                      "variant1",
					ProductsCenterVariantID: "pc_variant1",
					ConnectorsProductID:     "connector1",
					SourceProductID:         "source_product1",
					SourceVariantID:         "source_variant1",
					SourceInventoryItemId:   "inventory1",
					Sku:                     "SKU001",
					Title:                   "Red Shirt",
					ImageURL:                "https://example.com/image1.jpg",
					FulfillmentService:      "manual",
					InventoryQuantity:       100.0,
					Price: searchable_products.Price{
						Currency: "USD",
						Amount:   "19.99",
					},
					Status: "active",
				},
			},
			expected: []Variant{
				{
					ID:                      "variant1",
					ProductsCenterVariantID: "pc_variant1",
					ConnectorsProductID:     "connector1",
					SourceProductID:         "source_product1",
					SourceVariantID:         "source_variant1",
					SourceInventoryItemId:   "inventory1",
					Sku:                     "SKU001",
					Title:                   "Red Shirt",
					ImageURL:                "https://example.com/image1.jpg",
					FulfillmentService:      "manual",
					InventoryQuantity:       100.0,
					Price: Price{
						Currency: "USD",
						Amount:   "19.99",
					},
					Status: "active",
				},
			},
		},
		{
			name: "多个变体转换",
			input: []searchable_products.Variant{
				{
					ID:                      "variant1",
					ProductsCenterVariantID: "pc_variant1",
					ConnectorsProductID:     "connector1",
					SourceProductID:         "source_product1",
					SourceVariantID:         "source_variant1",
					SourceInventoryItemId:   "inventory1",
					Sku:                     "SKU001",
					Title:                   "Red Shirt",
					ImageURL:                "https://example.com/image1.jpg",
					FulfillmentService:      "manual",
					InventoryQuantity:       100.0,
					Price: searchable_products.Price{
						Currency: "USD",
						Amount:   "19.99",
					},
					Status: "active",
				},
				{
					ID:                      "variant2",
					ProductsCenterVariantID: "pc_variant2",
					ConnectorsProductID:     "connector2",
					SourceProductID:         "source_product2",
					SourceVariantID:         "source_variant2",
					SourceInventoryItemId:   "inventory2",
					Sku:                     "SKU002",
					Title:                   "Blue Shirt",
					ImageURL:                "https://example.com/image2.jpg",
					FulfillmentService:      "automatic",
					InventoryQuantity:       50.0,
					Price: searchable_products.Price{
						Currency: "EUR",
						Amount:   "25.99",
					},
					Status: "inactive",
				},
			},
			expected: []Variant{
				{
					ID:                      "variant1",
					ProductsCenterVariantID: "pc_variant1",
					ConnectorsProductID:     "connector1",
					SourceProductID:         "source_product1",
					SourceVariantID:         "source_variant1",
					SourceInventoryItemId:   "inventory1",
					Sku:                     "SKU001",
					Title:                   "Red Shirt",
					ImageURL:                "https://example.com/image1.jpg",
					FulfillmentService:      "manual",
					InventoryQuantity:       100.0,
					Price: Price{
						Currency: "USD",
						Amount:   "19.99",
					},
					Status: "active",
				},
				{
					ID:                      "variant2",
					ProductsCenterVariantID: "pc_variant2",
					ConnectorsProductID:     "connector2",
					SourceProductID:         "source_product2",
					SourceVariantID:         "source_variant2",
					SourceInventoryItemId:   "inventory2",
					Sku:                     "SKU002",
					Title:                   "Blue Shirt",
					ImageURL:                "https://example.com/image2.jpg",
					FulfillmentService:      "automatic",
					InventoryQuantity:       50.0,
					Price: Price{
						Currency: "EUR",
						Amount:   "25.99",
					},
					Status: "inactive",
				},
			},
		},
		{
			name: "包含空字段的变体转换",
			input: []searchable_products.Variant{
				{
					ID:                      "",
					ProductsCenterVariantID: "",
					ConnectorsProductID:     "",
					SourceProductID:         "",
					SourceVariantID:         "",
					SourceInventoryItemId:   "",
					Sku:                     "",
					Title:                   "",
					ImageURL:                "",
					FulfillmentService:      "",
					InventoryQuantity:       0.0,
					Price: searchable_products.Price{
						Currency: "",
						Amount:   "",
					},
					Status: "",
				},
			},
			expected: []Variant{
				{
					ID:                      "",
					ProductsCenterVariantID: "",
					ConnectorsProductID:     "",
					SourceProductID:         "",
					SourceVariantID:         "",
					SourceInventoryItemId:   "",
					Sku:                     "",
					Title:                   "",
					ImageURL:                "",
					FulfillmentService:      "",
					InventoryQuantity:       0.0,
					Price: Price{
						Currency: "",
						Amount:   "",
					},
					Status: "",
				},
			},
		},
		{
			name: "包含特殊字符的变体转换",
			input: []searchable_products.Variant{
				{
					ID:                      "variant-123_test",
					ProductsCenterVariantID: "pc_variant@456#test",
					ConnectorsProductID:     "connector/123",
					SourceProductID:         "source_product&123",
					SourceVariantID:         "source_variant#456",
					SourceInventoryItemId:   "inventory@789",
					Sku:                     "SKU-001_TEST",
					Title:                   "T-Shirt (Cotton) & Comfortable",
					ImageURL:                "https://example.com/image-test_123.jpg",
					FulfillmentService:      "manual/automatic",
					InventoryQuantity:       99.5,
					Price: searchable_products.Price{
						Currency: "USD",
						Amount:   "29.99",
					},
					Status: "active",
				},
			},
			expected: []Variant{
				{
					ID:                      "variant-123_test",
					ProductsCenterVariantID: "pc_variant@456#test",
					ConnectorsProductID:     "connector/123",
					SourceProductID:         "source_product&123",
					SourceVariantID:         "source_variant#456",
					SourceInventoryItemId:   "inventory@789",
					Sku:                     "SKU-001_TEST",
					Title:                   "T-Shirt (Cotton) & Comfortable",
					ImageURL:                "https://example.com/image-test_123.jpg",
					FulfillmentService:      "manual/automatic",
					InventoryQuantity:       99.5,
					Price: Price{
						Currency: "USD",
						Amount:   "29.99",
					},
					Status: "active",
				},
			},
		},
		{
			name: "零库存变体转换",
			input: []searchable_products.Variant{
				{
					ID:                      "variant_zero",
					ProductsCenterVariantID: "pc_variant_zero",
					ConnectorsProductID:     "connector_zero",
					SourceProductID:         "source_product_zero",
					SourceVariantID:         "source_variant_zero",
					SourceInventoryItemId:   "inventory_zero",
					Sku:                     "SKU_ZERO",
					Title:                   "Out of Stock Item",
					ImageURL:                "https://example.com/out-of-stock.jpg",
					FulfillmentService:      "manual",
					InventoryQuantity:       0.0,
					Price: searchable_products.Price{
						Currency: "USD",
						Amount:   "0.00",
					},
					Status: "inactive",
				},
			},
			expected: []Variant{
				{
					ID:                      "variant_zero",
					ProductsCenterVariantID: "pc_variant_zero",
					ConnectorsProductID:     "connector_zero",
					SourceProductID:         "source_product_zero",
					SourceVariantID:         "source_variant_zero",
					SourceInventoryItemId:   "inventory_zero",
					Sku:                     "SKU_ZERO",
					Title:                   "Out of Stock Item",
					ImageURL:                "https://example.com/out-of-stock.jpg",
					FulfillmentService:      "manual",
					InventoryQuantity:       0.0,
					Price: Price{
						Currency: "USD",
						Amount:   "0.00",
					},
					Status: "inactive",
				},
			},
		},
		{
			name: "大库存量变体转换",
			input: []searchable_products.Variant{
				{
					ID:                      "variant_large",
					ProductsCenterVariantID: "pc_variant_large",
					ConnectorsProductID:     "connector_large",
					SourceProductID:         "source_product_large",
					SourceVariantID:         "source_variant_large",
					SourceInventoryItemId:   "inventory_large",
					Sku:                     "SKU_LARGE",
					Title:                   "High Stock Item",
					ImageURL:                "https://example.com/high-stock.jpg",
					FulfillmentService:      "automatic",
					InventoryQuantity:       9999.99,
					Price: searchable_products.Price{
						Currency: "USD",
						Amount:   "999.99",
					},
					Status: "active",
				},
			},
			expected: []Variant{
				{
					ID:                      "variant_large",
					ProductsCenterVariantID: "pc_variant_large",
					ConnectorsProductID:     "connector_large",
					SourceProductID:         "source_product_large",
					SourceVariantID:         "source_variant_large",
					SourceInventoryItemId:   "inventory_large",
					Sku:                     "SKU_LARGE",
					Title:                   "High Stock Item",
					ImageURL:                "https://example.com/high-stock.jpg",
					FulfillmentService:      "automatic",
					InventoryQuantity:       9999.99,
					Price: Price{
						Currency: "USD",
						Amount:   "999.99",
					},
					Status: "active",
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := convertToVariantsResp(tt.input)

			require.Equal(t, len(tt.expected), len(result))

			for i, expected := range tt.expected {
				require.Equal(t, expected.ID, result[i].ID)
				require.Equal(t, expected.ProductsCenterVariantID, result[i].ProductsCenterVariantID)
				require.Equal(t, expected.ConnectorsProductID, result[i].ConnectorsProductID)
				require.Equal(t, expected.SourceProductID, result[i].SourceProductID)
				require.Equal(t, expected.SourceVariantID, result[i].SourceVariantID)
				require.Equal(t, expected.SourceInventoryItemId, result[i].SourceInventoryItemId)
				require.Equal(t, expected.Sku, result[i].Sku)
				require.Equal(t, expected.Title, result[i].Title)
				require.Equal(t, expected.ImageURL, result[i].ImageURL)
				require.Equal(t, expected.FulfillmentService, result[i].FulfillmentService)
				require.Equal(t, expected.InventoryQuantity, result[i].InventoryQuantity)
				require.Equal(t, expected.Price.Currency, result[i].Price.Currency)
				require.Equal(t, expected.Price.Amount, result[i].Price.Amount)
				require.Equal(t, expected.Status, result[i].Status)
			}
		})
	}
}

func TestConvertToCombinedResp(t *testing.T) {
	tests := []struct {
		name     string
		input    searchable_products.Combined
		expected Combined
	}{
		{
			name: "正常组合模型转换",
			input: searchable_products.Combined{
				ListingID:  "listing123",
				Status:     "active",
				ErrorCodes: []string{"ERROR_001", "ERROR_002"},
			},
			expected: Combined{
				ListingID:  "listing123",
				Status:     "active",
				ErrorCodes: []string{"ERROR_001", "ERROR_002"},
			},
		},
		{
			name: "空字符串字段转换",
			input: searchable_products.Combined{
				ListingID:  "",
				Status:     "",
				ErrorCodes: []string{},
			},
			expected: Combined{
				ListingID:  "",
				Status:     "",
				ErrorCodes: []string{},
			},
		},
		{
			name: "nil ErrorCodes转换",
			input: searchable_products.Combined{
				ListingID:  "listing456",
				Status:     "pending",
				ErrorCodes: nil,
			},
			expected: Combined{
				ListingID:  "listing456",
				Status:     "pending",
				ErrorCodes: nil,
			},
		},
		{
			name: "单个错误码转换",
			input: searchable_products.Combined{
				ListingID:  "listing789",
				Status:     "error",
				ErrorCodes: []string{"SINGLE_ERROR"},
			},
			expected: Combined{
				ListingID:  "listing789",
				Status:     "error",
				ErrorCodes: []string{"SINGLE_ERROR"},
			},
		},
		{
			name: "多个错误码转换",
			input: searchable_products.Combined{
				ListingID:  "listing999",
				Status:     "failed",
				ErrorCodes: []string{"ERROR_001", "ERROR_002", "ERROR_003", "ERROR_004"},
			},
			expected: Combined{
				ListingID:  "listing999",
				Status:     "failed",
				ErrorCodes: []string{"ERROR_001", "ERROR_002", "ERROR_003", "ERROR_004"},
			},
		},
		{
			name: "包含特殊字符的转换",
			input: searchable_products.Combined{
				ListingID:  "listing-123_test",
				Status:     "active-pending",
				ErrorCodes: []string{"ERROR_001@test", "ERROR_002#special"},
			},
			expected: Combined{
				ListingID:  "listing-123_test",
				Status:     "active-pending",
				ErrorCodes: []string{"ERROR_001@test", "ERROR_002#special"},
			},
		},
		{
			name: "长字符串转换",
			input: searchable_products.Combined{
				ListingID:  "very-long-listing-id-12345678901234567890",
				Status:     "very-long-status-name",
				ErrorCodes: []string{"VERY_LONG_ERROR_CODE_NAME_12345678901234567890"},
			},
			expected: Combined{
				ListingID:  "very-long-listing-id-12345678901234567890",
				Status:     "very-long-status-name",
				ErrorCodes: []string{"VERY_LONG_ERROR_CODE_NAME_12345678901234567890"},
			},
		},
		{
			name: "包含空字符串的ErrorCodes转换",
			input: searchable_products.Combined{
				ListingID:  "listing_empty_codes",
				Status:     "active",
				ErrorCodes: []string{"", "ERROR_001", ""},
			},
			expected: Combined{
				ListingID:  "listing_empty_codes",
				Status:     "active",
				ErrorCodes: []string{"", "ERROR_001", ""},
			},
		},
		{
			name: "不同状态类型转换",
			input: searchable_products.Combined{
				ListingID:  "listing_status_test",
				Status:     "draft",
				ErrorCodes: []string{"DRAFT_ERROR"},
			},
			expected: Combined{
				ListingID:  "listing_status_test",
				Status:     "draft",
				ErrorCodes: []string{"DRAFT_ERROR"},
			},
		},
		{
			name: "包含中文的转换",
			input: searchable_products.Combined{
				ListingID:  "listing_中文",
				Status:     "状态",
				ErrorCodes: []string{"错误代码"},
			},
			expected: Combined{
				ListingID:  "listing_中文",
				Status:     "状态",
				ErrorCodes: []string{"错误代码"},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := convertToCombinedResp(tt.input)

			require.Equal(t, tt.expected.ListingID, result.ListingID)
			require.Equal(t, tt.expected.Status, result.Status)

			if tt.expected.ErrorCodes == nil {
				require.Nil(t, result.ErrorCodes)
			} else {
				require.Equal(t, len(tt.expected.ErrorCodes), len(result.ErrorCodes))
				require.Equal(t, tt.expected.ErrorCodes, result.ErrorCodes)
			}
		})
	}
}

func TestConvertToOptionsResp(t *testing.T) {
	tests := []struct {
		name     string
		input    []searchable_products.Option
		expected []Option
	}{
		{
			name:     "nil输入",
			input:    nil,
			expected: []Option{},
		},
		{
			name:     "空数组",
			input:    []searchable_products.Option{},
			expected: []Option{},
		},
		{
			name: "单个选项转换",
			input: []searchable_products.Option{
				{
					Name:   "Color",
					Values: []string{"Red", "Blue", "Green"},
				},
			},
			expected: []Option{
				{
					Name:   "Color",
					Values: []string{"Red", "Blue", "Green"},
				},
			},
		},
		{
			name: "多个选项转换",
			input: []searchable_products.Option{
				{
					Name:   "Color",
					Values: []string{"Red", "Blue", "Green"},
				},
				{
					Name:   "Size",
					Values: []string{"S", "M", "L", "XL"},
				},
				{
					Name:   "Material",
					Values: []string{"Cotton", "Polyester"},
				},
			},
			expected: []Option{
				{
					Name:   "Color",
					Values: []string{"Red", "Blue", "Green"},
				},
				{
					Name:   "Size",
					Values: []string{"S", "M", "L", "XL"},
				},
				{
					Name:   "Material",
					Values: []string{"Cotton", "Polyester"},
				},
			},
		},
		{
			name: "包含空字段的选项转换",
			input: []searchable_products.Option{
				{
					Name:   "",
					Values: []string{},
				},
				{
					Name:   "Color",
					Values: []string{"Red"},
				},
			},
			expected: []Option{
				{
					Name:   "",
					Values: []string{},
				},
				{
					Name:   "Color",
					Values: []string{"Red"},
				},
			},
		},
		{
			name: "包含nil Values的选项转换",
			input: []searchable_products.Option{
				{
					Name:   "Color",
					Values: nil,
				},
			},
			expected: []Option{
				{
					Name:   "Color",
					Values: nil,
				},
			},
		},
		{
			name: "包含空字符串值的选项转换",
			input: []searchable_products.Option{
				{
					Name:   "Color",
					Values: []string{"", "Red", ""},
				},
			},
			expected: []Option{
				{
					Name:   "Color",
					Values: []string{"", "Red", ""},
				},
			},
		},
		{
			name: "包含特殊字符的选项转换",
			input: []searchable_products.Option{
				{
					Name:   "Color & Style",
					Values: []string{"Red/Pink", "Blue-Navy", "Green@Mint"},
				},
				{
					Name:   "Size (US)",
					Values: []string{"S/M", "L/XL", "XXL+"},
				},
			},
			expected: []Option{
				{
					Name:   "Color & Style",
					Values: []string{"Red/Pink", "Blue-Navy", "Green@Mint"},
				},
				{
					Name:   "Size (US)",
					Values: []string{"S/M", "L/XL", "XXL+"},
				},
			},
		},
		{
			name: "单个值的选项转换",
			input: []searchable_products.Option{
				{
					Name:   "Brand",
					Values: []string{"Nike"},
				},
			},
			expected: []Option{
				{
					Name:   "Brand",
					Values: []string{"Nike"},
				},
			},
		},
		{
			name: "长名称和多个值的选项转换",
			input: []searchable_products.Option{
				{
					Name:   "Very Long Option Name That Contains Multiple Words",
					Values: []string{"Value1", "Value2", "Value3", "Value4", "Value5"},
				},
			},
			expected: []Option{
				{
					Name:   "Very Long Option Name That Contains Multiple Words",
					Values: []string{"Value1", "Value2", "Value3", "Value4", "Value5"},
				},
			},
		},
		{
			name: "包含中文的选项转换",
			input: []searchable_products.Option{
				{
					Name:   "颜色",
					Values: []string{"红色", "蓝色", "绿色"},
				},
				{
					Name:   "尺寸",
					Values: []string{"小号", "中号", "大号"},
				},
			},
			expected: []Option{
				{
					Name:   "颜色",
					Values: []string{"红色", "蓝色", "绿色"},
				},
				{
					Name:   "尺寸",
					Values: []string{"小号", "中号", "大号"},
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := convertToOptionsResp(tt.input)

			require.Equal(t, len(tt.expected), len(result))

			for i, expected := range tt.expected {
				require.Equal(t, expected.Name, result[i].Name)

				if expected.Values == nil {
					require.Nil(t, result[i].Values)
				} else {
					require.Equal(t, len(expected.Values), len(result[i].Values))
					require.Equal(t, expected.Values, result[i].Values)
				}
			}
		})
	}
}

func TestConvertToAttributeValuesResp(t *testing.T) {
	tests := []struct {
		name     string
		input    []searchable_products.AttributeValue
		expected []AttributeValue
	}{
		{
			name:     "nil输入",
			input:    nil,
			expected: []AttributeValue{},
		},
		{
			name:     "空数组",
			input:    []searchable_products.AttributeValue{},
			expected: []AttributeValue{},
		},
		{
			name: "单个属性值转换",
			input: []searchable_products.AttributeValue{
				{
					SourceID: "attr_001",
					Type:     "string",
					Value:    "Red",
				},
			},
			expected: []AttributeValue{
				{
					SourceID: "attr_001",
					Type:     "string",
					Value:    "Red",
				},
			},
		},
		{
			name: "多个属性值转换",
			input: []searchable_products.AttributeValue{
				{
					SourceID: "attr_001",
					Type:     "string",
					Value:    "Red",
				},
				{
					SourceID: "attr_002",
					Type:     "number",
					Value:    "42",
				},
				{
					SourceID: "attr_003",
					Type:     "boolean",
					Value:    "true",
				},
			},
			expected: []AttributeValue{
				{
					SourceID: "attr_001",
					Type:     "string",
					Value:    "Red",
				},
				{
					SourceID: "attr_002",
					Type:     "number",
					Value:    "42",
				},
				{
					SourceID: "attr_003",
					Type:     "boolean",
					Value:    "true",
				},
			},
		},
		{
			name: "包含空字段的属性值转换",
			input: []searchable_products.AttributeValue{
				{
					SourceID: "",
					Type:     "",
					Value:    "",
				},
				{
					SourceID: "attr_002",
					Type:     "string",
					Value:    "Valid Value",
				},
			},
			expected: []AttributeValue{
				{
					SourceID: "",
					Type:     "",
					Value:    "",
				},
				{
					SourceID: "attr_002",
					Type:     "string",
					Value:    "Valid Value",
				},
			},
		},
		{
			name: "包含特殊字符的属性值转换",
			input: []searchable_products.AttributeValue{
				{
					SourceID: "attr-001_test",
					Type:     "special@type",
					Value:    "Value with special chars: @#$%^&*",
				},
				{
					SourceID: "attr#002",
					Type:     "url",
					Value:    "https://example.com/path?param=value",
				},
			},
			expected: []AttributeValue{
				{
					SourceID: "attr-001_test",
					Type:     "special@type",
					Value:    "Value with special chars: @#$%^&*",
				},
				{
					SourceID: "attr#002",
					Type:     "url",
					Value:    "https://example.com/path?param=value",
				},
			},
		},
		{
			name: "不同类型的属性值转换",
			input: []searchable_products.AttributeValue{
				{
					SourceID: "attr_string",
					Type:     "string",
					Value:    "String Value",
				},
				{
					SourceID: "attr_number",
					Type:     "number",
					Value:    "123.45",
				},
				{
					SourceID: "attr_boolean",
					Type:     "boolean",
					Value:    "false",
				},
				{
					SourceID: "attr_date",
					Type:     "date",
					Value:    "2023-12-01",
				},
			},
			expected: []AttributeValue{
				{
					SourceID: "attr_string",
					Type:     "string",
					Value:    "String Value",
				},
				{
					SourceID: "attr_number",
					Type:     "number",
					Value:    "123.45",
				},
				{
					SourceID: "attr_boolean",
					Type:     "boolean",
					Value:    "false",
				},
				{
					SourceID: "attr_date",
					Type:     "date",
					Value:    "2023-12-01",
				},
			},
		},
		{
			name: "长字符串属性值转换",
			input: []searchable_products.AttributeValue{
				{
					SourceID: "very-long-source-id-12345678901234567890",
					Type:     "very-long-type-name-12345678901234567890",
					Value:    "Very long value that contains multiple words and special characters @#$%^&*()_+-=[]{}|;:,.<>?",
				},
			},
			expected: []AttributeValue{
				{
					SourceID: "very-long-source-id-12345678901234567890",
					Type:     "very-long-type-name-12345678901234567890",
					Value:    "Very long value that contains multiple words and special characters @#$%^&*()_+-=[]{}|;:,.<>?",
				},
			},
		},
		{
			name: "包含中文的属性值转换",
			input: []searchable_products.AttributeValue{
				{
					SourceID: "中文属性ID",
					Type:     "字符串类型",
					Value:    "中文属性值",
				},
				{
					SourceID: "mixed_中文_id",
					Type:     "mixed类型",
					Value:    "Mixed中文Value",
				},
			},
			expected: []AttributeValue{
				{
					SourceID: "中文属性ID",
					Type:     "字符串类型",
					Value:    "中文属性值",
				},
				{
					SourceID: "mixed_中文_id",
					Type:     "mixed类型",
					Value:    "Mixed中文Value",
				},
			},
		},
		{
			name: "包含JSON格式的属性值转换",
			input: []searchable_products.AttributeValue{
				{
					SourceID: "json_attr",
					Type:     "json",
					Value:    `{"key": "value", "number": 123, "boolean": true}`,
				},
			},
			expected: []AttributeValue{
				{
					SourceID: "json_attr",
					Type:     "json",
					Value:    `{"key": "value", "number": 123, "boolean": true}`,
				},
			},
		},
		{
			name: "包含换行符的属性值转换",
			input: []searchable_products.AttributeValue{
				{
					SourceID: "multiline_attr",
					Type:     "text",
					Value:    "Line 1\nLine 2\nLine 3",
				},
			},
			expected: []AttributeValue{
				{
					SourceID: "multiline_attr",
					Type:     "text",
					Value:    "Line 1\nLine 2\nLine 3",
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := convertToAttributeValuesResp(tt.input)

			require.Equal(t, len(tt.expected), len(result))

			for i, expected := range tt.expected {
				require.Equal(t, expected.SourceID, result[i].SourceID)
				require.Equal(t, expected.Type, result[i].Type)
				require.Equal(t, expected.Value, result[i].Value)
			}
		})
	}
}

func TestConvertToAttributesResp(t *testing.T) {
	tests := []struct {
		name     string
		input    []searchable_products.Attribute
		expected []Attribute
	}{
		{
			name:     "nil输入",
			input:    nil,
			expected: []Attribute{},
		},
		{
			name:     "空数组",
			input:    []searchable_products.Attribute{},
			expected: []Attribute{},
		},
		{
			name: "单个属性转换",
			input: []searchable_products.Attribute{
				{
					SourceID: "attr_001",
					Name:     "Color",
					Values: []searchable_products.AttributeValue{
						{
							SourceID: "value_001",
							Type:     "string",
							Value:    "Red",
						},
					},
				},
			},
			expected: []Attribute{
				{
					SourceID: "attr_001",
					Name:     "Color",
					Values: []AttributeValue{
						{
							SourceID: "value_001",
							Type:     "string",
							Value:    "Red",
						},
					},
				},
			},
		},
		{
			name: "多个属性转换",
			input: []searchable_products.Attribute{
				{
					SourceID: "attr_001",
					Name:     "Color",
					Values: []searchable_products.AttributeValue{
						{
							SourceID: "value_001",
							Type:     "string",
							Value:    "Red",
						},
					},
				},
				{
					SourceID: "attr_002",
					Name:     "Size",
					Values: []searchable_products.AttributeValue{
						{
							SourceID: "value_002",
							Type:     "string",
							Value:    "Large",
						},
					},
				},
			},
			expected: []Attribute{
				{
					SourceID: "attr_001",
					Name:     "Color",
					Values: []AttributeValue{
						{
							SourceID: "value_001",
							Type:     "string",
							Value:    "Red",
						},
					},
				},
				{
					SourceID: "attr_002",
					Name:     "Size",
					Values: []AttributeValue{
						{
							SourceID: "value_002",
							Type:     "string",
							Value:    "Large",
						},
					},
				},
			},
		},
		{
			name: "包含空字段的属性转换",
			input: []searchable_products.Attribute{
				{
					SourceID: "",
					Name:     "",
					Values:   []searchable_products.AttributeValue{},
				},
			},
			expected: []Attribute{
				{
					SourceID: "",
					Name:     "",
					Values:   []AttributeValue{},
				},
			},
		},
		{
			name: "包含nil Values的属性转换",
			input: []searchable_products.Attribute{
				{
					SourceID: "attr_empty",
					Name:     "Empty Attribute",
					Values:   nil,
				},
			},
			expected: []Attribute{
				{
					SourceID: "attr_empty",
					Name:     "Empty Attribute",
					Values:   []AttributeValue{},
				},
			},
		},
		{
			name: "包含多个值的属性转换",
			input: []searchable_products.Attribute{
				{
					SourceID: "attr_multi",
					Name:     "Multi Value Attribute",
					Values: []searchable_products.AttributeValue{
						{
							SourceID: "value_001",
							Type:     "string",
							Value:    "Value 1",
						},
						{
							SourceID: "value_002",
							Type:     "number",
							Value:    "123",
						},
						{
							SourceID: "value_003",
							Type:     "boolean",
							Value:    "true",
						},
					},
				},
			},
			expected: []Attribute{
				{
					SourceID: "attr_multi",
					Name:     "Multi Value Attribute",
					Values: []AttributeValue{
						{
							SourceID: "value_001",
							Type:     "string",
							Value:    "Value 1",
						},
						{
							SourceID: "value_002",
							Type:     "number",
							Value:    "123",
						},
						{
							SourceID: "value_003",
							Type:     "boolean",
							Value:    "true",
						},
					},
				},
			},
		},
		{
			name: "包含特殊字符的属性转换",
			input: []searchable_products.Attribute{
				{
					SourceID: "attr-special_123",
					Name:     "Special@Attribute#Name",
					Values: []searchable_products.AttributeValue{
						{
							SourceID: "value-special_456",
							Type:     "special@type",
							Value:    "Value with special chars: @#$%^&*",
						},
					},
				},
			},
			expected: []Attribute{
				{
					SourceID: "attr-special_123",
					Name:     "Special@Attribute#Name",
					Values: []AttributeValue{
						{
							SourceID: "value-special_456",
							Type:     "special@type",
							Value:    "Value with special chars: @#$%^&*",
						},
					},
				},
			},
		},
		{
			name: "包含中文的属性转换",
			input: []searchable_products.Attribute{
				{
					SourceID: "中文属性ID",
					Name:     "中文属性名称",
					Values: []searchable_products.AttributeValue{
						{
							SourceID: "中文值ID",
							Type:     "字符串",
							Value:    "中文值",
						},
					},
				},
			},
			expected: []Attribute{
				{
					SourceID: "中文属性ID",
					Name:     "中文属性名称",
					Values: []AttributeValue{
						{
							SourceID: "中文值ID",
							Type:     "字符串",
							Value:    "中文值",
						},
					},
				},
			},
		},
		{
			name: "长名称属性转换",
			input: []searchable_products.Attribute{
				{
					SourceID: "very-long-attribute-source-id-12345678901234567890",
					Name:     "Very Long Attribute Name With Many Words And Characters",
					Values: []searchable_products.AttributeValue{
						{
							SourceID: "long-value-id-12345678901234567890",
							Type:     "very-long-type-name",
							Value:    "Very long value with multiple words and special characters",
						},
					},
				},
			},
			expected: []Attribute{
				{
					SourceID: "very-long-attribute-source-id-12345678901234567890",
					Name:     "Very Long Attribute Name With Many Words And Characters",
					Values: []AttributeValue{
						{
							SourceID: "long-value-id-12345678901234567890",
							Type:     "very-long-type-name",
							Value:    "Very long value with multiple words and special characters",
						},
					},
				},
			},
		},
		{
			name: "复杂属性组合转换",
			input: []searchable_products.Attribute{
				{
					SourceID: "complex_attr_1",
					Name:     "Brand",
					Values: []searchable_products.AttributeValue{
						{
							SourceID: "brand_value_1",
							Type:     "string",
							Value:    "Nike",
						},
						{
							SourceID: "brand_value_2",
							Type:     "string",
							Value:    "Adidas",
						},
					},
				},
				{
					SourceID: "complex_attr_2",
					Name:     "Weight",
					Values: []searchable_products.AttributeValue{
						{
							SourceID: "weight_value_1",
							Type:     "number",
							Value:    "1.5",
						},
					},
				},
			},
			expected: []Attribute{
				{
					SourceID: "complex_attr_1",
					Name:     "Brand",
					Values: []AttributeValue{
						{
							SourceID: "brand_value_1",
							Type:     "string",
							Value:    "Nike",
						},
						{
							SourceID: "brand_value_2",
							Type:     "string",
							Value:    "Adidas",
						},
					},
				},
				{
					SourceID: "complex_attr_2",
					Name:     "Weight",
					Values: []AttributeValue{
						{
							SourceID: "weight_value_1",
							Type:     "number",
							Value:    "1.5",
						},
					},
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := convertToAttributesResp(tt.input)

			require.Equal(t, len(tt.expected), len(result))

			for i, expected := range tt.expected {
				require.Equal(t, expected.SourceID, result[i].SourceID)
				require.Equal(t, expected.Name, result[i].Name)
				require.Equal(t, len(expected.Values), len(result[i].Values))

				for j, expectedValue := range expected.Values {
					require.Equal(t, expectedValue.SourceID, result[i].Values[j].SourceID)
					require.Equal(t, expectedValue.Type, result[i].Values[j].Type)
					require.Equal(t, expectedValue.Value, result[i].Values[j].Value)
				}
			}
		})
	}
}

func TestConvertToCategoriesResp(t *testing.T) {
	tests := []struct {
		name     string
		input    []searchable_products.Category
		expected []Category
	}{
		{
			name:     "nil输入",
			input:    nil,
			expected: []Category{},
		},
		{
			name:     "空数组",
			input:    []searchable_products.Category{},
			expected: []Category{},
		},
		{
			name: "单个分类转换",
			input: []searchable_products.Category{
				{
					SourceID: "cat_001",
					Name:     "Electronics",
					Type:     "main",
					ID:       "12345",
				},
			},
			expected: []Category{
				{
					SourceID: "cat_001",
					Name:     "Electronics",
					Type:     "main",
					ID:       "12345",
				},
			},
		},
		{
			name: "多个分类转换",
			input: []searchable_products.Category{
				{
					SourceID: "cat_001",
					Name:     "Electronics",
					Type:     "main",
					ID:       "12345",
				},
				{
					SourceID: "cat_002",
					Name:     "Clothing",
					Type:     "secondary",
					ID:       "67890",
				},
				{
					SourceID: "cat_003",
					Name:     "Books",
					Type:     "tertiary",
					ID:       "11111",
				},
			},
			expected: []Category{
				{
					SourceID: "cat_001",
					Name:     "Electronics",
					Type:     "main",
					ID:       "12345",
				},
				{
					SourceID: "cat_002",
					Name:     "Clothing",
					Type:     "secondary",
					ID:       "67890",
				},
				{
					SourceID: "cat_003",
					Name:     "Books",
					Type:     "tertiary",
					ID:       "11111",
				},
			},
		},
		{
			name: "包含空字段的分类转换",
			input: []searchable_products.Category{
				{
					SourceID: "",
					Name:     "",
					Type:     "",
					ID:       "",
				},
			},
			expected: []Category{
				{
					SourceID: "",
					Name:     "",
					Type:     "",
					ID:       "",
				},
			},
		},
		{
			name: "包含特殊字符的分类转换",
			input: []searchable_products.Category{
				{
					SourceID: "cat-special_123",
					Name:     "Electronics & Gadgets",
					Type:     "main@category",
					ID:       "id#123",
				},
			},
			expected: []Category{
				{
					SourceID: "cat-special_123",
					Name:     "Electronics & Gadgets",
					Type:     "main@category",
					ID:       "id#123",
				},
			},
		},
		{
			name: "包含中文的分类转换",
			input: []searchable_products.Category{
				{
					SourceID: "中文分类ID",
					Name:     "电子产品",
					Type:     "主要分类",
					ID:       "编号12345",
				},
			},
			expected: []Category{
				{
					SourceID: "中文分类ID",
					Name:     "电子产品",
					Type:     "主要分类",
					ID:       "编号12345",
				},
			},
		},
		{
			name: "长名称分类转换",
			input: []searchable_products.Category{
				{
					SourceID: "very-long-source-id-12345678901234567890",
					Name:     "Very Long Category Name With Many Words And Characters",
					Type:     "very-long-category-type-name",
					ID:       "very-long-category-id-12345678901234567890",
				},
			},
			expected: []Category{
				{
					SourceID: "very-long-source-id-12345678901234567890",
					Name:     "Very Long Category Name With Many Words And Characters",
					Type:     "very-long-category-type-name",
					ID:       "very-long-category-id-12345678901234567890",
				},
			},
		},
		{
			name: "数字类型分类转换",
			input: []searchable_products.Category{
				{
					SourceID: "123456789",
					Name:     "Category 123",
					Type:     "type1",
					ID:       "999999999",
				},
			},
			expected: []Category{
				{
					SourceID: "123456789",
					Name:     "Category 123",
					Type:     "type1",
					ID:       "999999999",
				},
			},
		},
		{
			name: "混合分类类型转换",
			input: []searchable_products.Category{
				{
					SourceID: "parent_cat",
					Name:     "Parent Category",
					Type:     "parent",
					ID:       "p001",
				},
				{
					SourceID: "child_cat",
					Name:     "Child Category",
					Type:     "child",
					ID:       "c001",
				},
				{
					SourceID: "leaf_cat",
					Name:     "Leaf Category",
					Type:     "leaf",
					ID:       "l001",
				},
			},
			expected: []Category{
				{
					SourceID: "parent_cat",
					Name:     "Parent Category",
					Type:     "parent",
					ID:       "p001",
				},
				{
					SourceID: "child_cat",
					Name:     "Child Category",
					Type:     "child",
					ID:       "c001",
				},
				{
					SourceID: "leaf_cat",
					Name:     "Leaf Category",
					Type:     "leaf",
					ID:       "l001",
				},
			},
		},
		{
			name: "包含空格的分类转换",
			input: []searchable_products.Category{
				{
					SourceID: "cat with spaces",
					Name:     "Category With Spaces",
					Type:     "type with spaces",
					ID:       "id with spaces",
				},
			},
			expected: []Category{
				{
					SourceID: "cat with spaces",
					Name:     "Category With Spaces",
					Type:     "type with spaces",
					ID:       "id with spaces",
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := convertToCategoriesResp(tt.input)

			require.Equal(t, len(tt.expected), len(result))

			for i, expected := range tt.expected {
				require.Equal(t, expected.SourceID, result[i].SourceID)
				require.Equal(t, expected.Name, result[i].Name)
				require.Equal(t, expected.Type, result[i].Type)
				require.Equal(t, expected.ID, result[i].ID)
			}
		})
	}
}

func TestConvertToMediaResp(t *testing.T) {
	tests := []struct {
		name     string
		input    []searchable_products.Media
		expected []Media
	}{
		{
			name:     "nil输入",
			input:    nil,
			expected: []Media{},
		},
		{
			name:     "空数组",
			input:    []searchable_products.Media{},
			expected: []Media{},
		},
		{
			name: "单个媒体转换",
			input: []searchable_products.Media{
				{
					Type:     "image",
					Position: 1,
					Url:      "https://example.com/image.jpg",
					MimeType: "image/jpeg",
					Thumbnail: struct {
						Url string `json:"url"`
					}{
						Url: "https://example.com/thumb.jpg",
					},
					SourceVideoHost: "",
				},
			},
			expected: []Media{
				{
					Type:     "image",
					Position: 1,
					Url:      "https://example.com/image.jpg",
					MimeType: "image/jpeg",
					Thumbnail: struct {
						Url string `json:"url"`
					}{
						Url: "https://example.com/thumb.jpg",
					},
					SourceVideoHost: "",
				},
			},
		},
		{
			name: "多个媒体转换",
			input: []searchable_products.Media{
				{
					Type:     "image",
					Position: 1,
					Url:      "https://example.com/image1.jpg",
					MimeType: "image/jpeg",
					Thumbnail: struct {
						Url string `json:"url"`
					}{
						Url: "https://example.com/thumb1.jpg",
					},
					SourceVideoHost: "",
				},
				{
					Type:     "video",
					Position: 2,
					Url:      "https://example.com/video.mp4",
					MimeType: "video/mp4",
					Thumbnail: struct {
						Url string `json:"url"`
					}{
						Url: "https://example.com/video_thumb.jpg",
					},
					SourceVideoHost: "youtube",
				},
			},
			expected: []Media{
				{
					Type:     "image",
					Position: 1,
					Url:      "https://example.com/image1.jpg",
					MimeType: "image/jpeg",
					Thumbnail: struct {
						Url string `json:"url"`
					}{
						Url: "https://example.com/thumb1.jpg",
					},
					SourceVideoHost: "",
				},
				{
					Type:     "video",
					Position: 2,
					Url:      "https://example.com/video.mp4",
					MimeType: "video/mp4",
					Thumbnail: struct {
						Url string `json:"url"`
					}{
						Url: "https://example.com/video_thumb.jpg",
					},
					SourceVideoHost: "youtube",
				},
			},
		},
		{
			name: "包含空字段的媒体转换",
			input: []searchable_products.Media{
				{
					Type:     "",
					Position: 0,
					Url:      "",
					MimeType: "",
					Thumbnail: struct {
						Url string `json:"url"`
					}{
						Url: "",
					},
					SourceVideoHost: "",
				},
			},
			expected: []Media{
				{
					Type:     "",
					Position: 0,
					Url:      "",
					MimeType: "",
					Thumbnail: struct {
						Url string `json:"url"`
					}{
						Url: "",
					},
					SourceVideoHost: "",
				},
			},
		},
		{
			name: "不同类型媒体转换",
			input: []searchable_products.Media{
				{
					Type:     "image",
					Position: 1,
					Url:      "https://example.com/image.png",
					MimeType: "image/png",
					Thumbnail: struct {
						Url string `json:"url"`
					}{
						Url: "https://example.com/thumb.png",
					},
					SourceVideoHost: "",
				},
				{
					Type:     "video",
					Position: 2,
					Url:      "https://example.com/video.mov",
					MimeType: "video/quicktime",
					Thumbnail: struct {
						Url string `json:"url"`
					}{
						Url: "https://example.com/video_thumb.png",
					},
					SourceVideoHost: "vimeo",
				},
			},
			expected: []Media{
				{
					Type:     "image",
					Position: 1,
					Url:      "https://example.com/image.png",
					MimeType: "image/png",
					Thumbnail: struct {
						Url string `json:"url"`
					}{
						Url: "https://example.com/thumb.png",
					},
					SourceVideoHost: "",
				},
				{
					Type:     "video",
					Position: 2,
					Url:      "https://example.com/video.mov",
					MimeType: "video/quicktime",
					Thumbnail: struct {
						Url string `json:"url"`
					}{
						Url: "https://example.com/video_thumb.png",
					},
					SourceVideoHost: "vimeo",
				},
			},
		},
		{
			name: "负数位置的媒体转换",
			input: []searchable_products.Media{
				{
					Type:     "image",
					Position: -1,
					Url:      "https://example.com/image.gif",
					MimeType: "image/gif",
					Thumbnail: struct {
						Url string `json:"url"`
					}{
						Url: "https://example.com/thumb.gif",
					},
					SourceVideoHost: "",
				},
			},
			expected: []Media{
				{
					Type:     "image",
					Position: -1,
					Url:      "https://example.com/image.gif",
					MimeType: "image/gif",
					Thumbnail: struct {
						Url string `json:"url"`
					}{
						Url: "https://example.com/thumb.gif",
					},
					SourceVideoHost: "",
				},
			},
		},
		{
			name: "大位置值的媒体转换",
			input: []searchable_products.Media{
				{
					Type:     "image",
					Position: 999999,
					Url:      "https://example.com/large-pos-image.jpg",
					MimeType: "image/jpeg",
					Thumbnail: struct {
						Url string `json:"url"`
					}{
						Url: "https://example.com/large-pos-thumb.jpg",
					},
					SourceVideoHost: "",
				},
			},
			expected: []Media{
				{
					Type:     "image",
					Position: 999999,
					Url:      "https://example.com/large-pos-image.jpg",
					MimeType: "image/jpeg",
					Thumbnail: struct {
						Url string `json:"url"`
					}{
						Url: "https://example.com/large-pos-thumb.jpg",
					},
					SourceVideoHost: "",
				},
			},
		},
		{
			name: "包含特殊字符的媒体转换",
			input: []searchable_products.Media{
				{
					Type:     "image@special",
					Position: 1,
					Url:      "https://example.com/image-with-special_chars.jpg",
					MimeType: "image/jpeg",
					Thumbnail: struct {
						Url string `json:"url"`
					}{
						Url: "https://example.com/thumb-with-special_chars.jpg",
					},
					SourceVideoHost: "youtube#special",
				},
			},
			expected: []Media{
				{
					Type:     "image@special",
					Position: 1,
					Url:      "https://example.com/image-with-special_chars.jpg",
					MimeType: "image/jpeg",
					Thumbnail: struct {
						Url string `json:"url"`
					}{
						Url: "https://example.com/thumb-with-special_chars.jpg",
					},
					SourceVideoHost: "youtube#special",
				},
			},
		},
		{
			name: "不同视频源的媒体转换",
			input: []searchable_products.Media{
				{
					Type:     "video",
					Position: 1,
					Url:      "https://youtube.com/watch?v=123",
					MimeType: "video/mp4",
					Thumbnail: struct {
						Url string `json:"url"`
					}{
						Url: "https://youtube.com/thumb/123.jpg",
					},
					SourceVideoHost: "youtube",
				},
				{
					Type:     "video",
					Position: 2,
					Url:      "https://vimeo.com/456",
					MimeType: "video/mp4",
					Thumbnail: struct {
						Url string `json:"url"`
					}{
						Url: "https://vimeo.com/thumb/456.jpg",
					},
					SourceVideoHost: "vimeo",
				},
			},
			expected: []Media{
				{
					Type:     "video",
					Position: 1,
					Url:      "https://youtube.com/watch?v=123",
					MimeType: "video/mp4",
					Thumbnail: struct {
						Url string `json:"url"`
					}{
						Url: "https://youtube.com/thumb/123.jpg",
					},
					SourceVideoHost: "youtube",
				},
				{
					Type:     "video",
					Position: 2,
					Url:      "https://vimeo.com/456",
					MimeType: "video/mp4",
					Thumbnail: struct {
						Url string `json:"url"`
					}{
						Url: "https://vimeo.com/thumb/456.jpg",
					},
					SourceVideoHost: "vimeo",
				},
			},
		},
		{
			name: "长URL的媒体转换",
			input: []searchable_products.Media{
				{
					Type:     "image",
					Position: 1,
					Url:      "https://very-long-domain-name.example.com/very/long/path/to/image/with/many/segments/image.jpg",
					MimeType: "image/jpeg",
					Thumbnail: struct {
						Url string `json:"url"`
					}{
						Url: "https://very-long-domain-name.example.com/very/long/path/to/thumbnail/thumb.jpg",
					},
					SourceVideoHost: "",
				},
			},
			expected: []Media{
				{
					Type:     "image",
					Position: 1,
					Url:      "https://very-long-domain-name.example.com/very/long/path/to/image/with/many/segments/image.jpg",
					MimeType: "image/jpeg",
					Thumbnail: struct {
						Url string `json:"url"`
					}{
						Url: "https://very-long-domain-name.example.com/very/long/path/to/thumbnail/thumb.jpg",
					},
					SourceVideoHost: "",
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := convertToMediaResp(tt.input)

			require.Equal(t, len(tt.expected), len(result))

			for i, expected := range tt.expected {
				require.Equal(t, expected.Type, result[i].Type)
				require.Equal(t, expected.Position, result[i].Position)
				require.Equal(t, expected.Url, result[i].Url)
				require.Equal(t, expected.MimeType, result[i].MimeType)
				require.Equal(t, expected.Thumbnail.Url, result[i].Thumbnail.Url)
				require.Equal(t, expected.SourceVideoHost, result[i].SourceVideoHost)
			}
		})
	}
}

func TestConvertRequestToMedia(t *testing.T) {
	tests := []struct {
		name     string
		input    []Media
		expected []searchable_products.Media
	}{
		{
			name:     "nil输入",
			input:    nil,
			expected: nil,
		},
		{
			name:     "空数组",
			input:    []Media{},
			expected: []searchable_products.Media{},
		},
		{
			name: "单个媒体转换",
			input: []Media{
				{
					Type:     "image",
					Position: 1,
					Url:      "https://example.com/image.jpg",
					MimeType: "image/jpeg",
					Thumbnail: struct {
						Url string `json:"url"`
					}{
						Url: "https://example.com/thumb.jpg",
					},
					SourceVideoHost: "",
				},
			},
			expected: []searchable_products.Media{
				{
					Type:     "image",
					Position: 1,
					Url:      "https://example.com/image.jpg",
					MimeType: "image/jpeg",
					Thumbnail: struct {
						Url string `json:"url"`
					}{
						Url: "https://example.com/thumb.jpg",
					},
					SourceVideoHost: "",
				},
			},
		},
		{
			name: "多个媒体转换",
			input: []Media{
				{
					Type:     "image",
					Position: 1,
					Url:      "https://example.com/image1.jpg",
					MimeType: "image/jpeg",
					Thumbnail: struct {
						Url string `json:"url"`
					}{
						Url: "https://example.com/thumb1.jpg",
					},
					SourceVideoHost: "",
				},
				{
					Type:     "video",
					Position: 2,
					Url:      "https://example.com/video.mp4",
					MimeType: "video/mp4",
					Thumbnail: struct {
						Url string `json:"url"`
					}{
						Url: "https://example.com/video_thumb.jpg",
					},
					SourceVideoHost: "youtube",
				},
			},
			expected: []searchable_products.Media{
				{
					Type:     "image",
					Position: 1,
					Url:      "https://example.com/image1.jpg",
					MimeType: "image/jpeg",
					Thumbnail: struct {
						Url string `json:"url"`
					}{
						Url: "https://example.com/thumb1.jpg",
					},
					SourceVideoHost: "",
				},
				{
					Type:     "video",
					Position: 2,
					Url:      "https://example.com/video.mp4",
					MimeType: "video/mp4",
					Thumbnail: struct {
						Url string `json:"url"`
					}{
						Url: "https://example.com/video_thumb.jpg",
					},
					SourceVideoHost: "youtube",
				},
			},
		},
		{
			name: "包含空字段的媒体转换",
			input: []Media{
				{
					Type:     "",
					Position: 0,
					Url:      "",
					MimeType: "",
					Thumbnail: struct {
						Url string `json:"url"`
					}{
						Url: "",
					},
					SourceVideoHost: "",
				},
			},
			expected: []searchable_products.Media{
				{
					Type:     "",
					Position: 0,
					Url:      "",
					MimeType: "",
					Thumbnail: struct {
						Url string `json:"url"`
					}{
						Url: "",
					},
					SourceVideoHost: "",
				},
			},
		},
		{
			name: "包含特殊字符的媒体转换",
			input: []Media{
				{
					Type:     "image@special",
					Position: 1,
					Url:      "https://example.com/image-with-special_chars.jpg",
					MimeType: "image/jpeg",
					Thumbnail: struct {
						Url string `json:"url"`
					}{
						Url: "https://example.com/thumb-with-special_chars.jpg",
					},
					SourceVideoHost: "youtube#special",
				},
			},
			expected: []searchable_products.Media{
				{
					Type:     "image@special",
					Position: 1,
					Url:      "https://example.com/image-with-special_chars.jpg",
					MimeType: "image/jpeg",
					Thumbnail: struct {
						Url string `json:"url"`
					}{
						Url: "https://example.com/thumb-with-special_chars.jpg",
					},
					SourceVideoHost: "youtube#special",
				},
			},
		},
		{
			name: "负数位置的媒体转换",
			input: []Media{
				{
					Type:     "image",
					Position: -5,
					Url:      "https://example.com/negative-pos.jpg",
					MimeType: "image/jpeg",
					Thumbnail: struct {
						Url string `json:"url"`
					}{
						Url: "https://example.com/negative-thumb.jpg",
					},
					SourceVideoHost: "",
				},
			},
			expected: []searchable_products.Media{
				{
					Type:     "image",
					Position: -5,
					Url:      "https://example.com/negative-pos.jpg",
					MimeType: "image/jpeg",
					Thumbnail: struct {
						Url string `json:"url"`
					}{
						Url: "https://example.com/negative-thumb.jpg",
					},
					SourceVideoHost: "",
				},
			},
		},
		{
			name: "不同类型媒体转换",
			input: []Media{
				{
					Type:     "image",
					Position: 1,
					Url:      "https://example.com/image.png",
					MimeType: "image/png",
					Thumbnail: struct {
						Url string `json:"url"`
					}{
						Url: "https://example.com/thumb.png",
					},
					SourceVideoHost: "",
				},
				{
					Type:     "video",
					Position: 2,
					Url:      "https://example.com/video.mov",
					MimeType: "video/quicktime",
					Thumbnail: struct {
						Url string `json:"url"`
					}{
						Url: "https://example.com/video_thumb.png",
					},
					SourceVideoHost: "vimeo",
				},
			},
			expected: []searchable_products.Media{
				{
					Type:     "image",
					Position: 1,
					Url:      "https://example.com/image.png",
					MimeType: "image/png",
					Thumbnail: struct {
						Url string `json:"url"`
					}{
						Url: "https://example.com/thumb.png",
					},
					SourceVideoHost: "",
				},
				{
					Type:     "video",
					Position: 2,
					Url:      "https://example.com/video.mov",
					MimeType: "video/quicktime",
					Thumbnail: struct {
						Url string `json:"url"`
					}{
						Url: "https://example.com/video_thumb.png",
					},
					SourceVideoHost: "vimeo",
				},
			},
		},
		{
			name: "长URL的媒体转换",
			input: []Media{
				{
					Type:     "image",
					Position: 1,
					Url:      "https://very-long-domain-name.example.com/very/long/path/to/image/with/many/segments/image.jpg",
					MimeType: "image/jpeg",
					Thumbnail: struct {
						Url string `json:"url"`
					}{
						Url: "https://very-long-domain-name.example.com/very/long/path/to/thumbnail/thumb.jpg",
					},
					SourceVideoHost: "",
				},
			},
			expected: []searchable_products.Media{
				{
					Type:     "image",
					Position: 1,
					Url:      "https://very-long-domain-name.example.com/very/long/path/to/image/with/many/segments/image.jpg",
					MimeType: "image/jpeg",
					Thumbnail: struct {
						Url string `json:"url"`
					}{
						Url: "https://very-long-domain-name.example.com/very/long/path/to/thumbnail/thumb.jpg",
					},
					SourceVideoHost: "",
				},
			},
		},
		{
			name: "大位置值的媒体转换",
			input: []Media{
				{
					Type:     "image",
					Position: 99999,
					Url:      "https://example.com/large-position.jpg",
					MimeType: "image/jpeg",
					Thumbnail: struct {
						Url string `json:"url"`
					}{
						Url: "https://example.com/large-position-thumb.jpg",
					},
					SourceVideoHost: "",
				},
			},
			expected: []searchable_products.Media{
				{
					Type:     "image",
					Position: 99999,
					Url:      "https://example.com/large-position.jpg",
					MimeType: "image/jpeg",
					Thumbnail: struct {
						Url string `json:"url"`
					}{
						Url: "https://example.com/large-position-thumb.jpg",
					},
					SourceVideoHost: "",
				},
			},
		},
		{
			name: "不同视频源的媒体转换",
			input: []Media{
				{
					Type:     "video",
					Position: 1,
					Url:      "https://youtube.com/watch?v=abc123",
					MimeType: "video/mp4",
					Thumbnail: struct {
						Url string `json:"url"`
					}{
						Url: "https://youtube.com/thumb/abc123.jpg",
					},
					SourceVideoHost: "youtube",
				},
				{
					Type:     "video",
					Position: 2,
					Url:      "https://vimeo.com/456789",
					MimeType: "video/mp4",
					Thumbnail: struct {
						Url string `json:"url"`
					}{
						Url: "https://vimeo.com/thumb/456789.jpg",
					},
					SourceVideoHost: "vimeo",
				},
			},
			expected: []searchable_products.Media{
				{
					Type:     "video",
					Position: 1,
					Url:      "https://youtube.com/watch?v=abc123",
					MimeType: "video/mp4",
					Thumbnail: struct {
						Url string `json:"url"`
					}{
						Url: "https://youtube.com/thumb/abc123.jpg",
					},
					SourceVideoHost: "youtube",
				},
				{
					Type:     "video",
					Position: 2,
					Url:      "https://vimeo.com/456789",
					MimeType: "video/mp4",
					Thumbnail: struct {
						Url string `json:"url"`
					}{
						Url: "https://vimeo.com/thumb/456789.jpg",
					},
					SourceVideoHost: "vimeo",
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := convertRequestToMedia(tt.input)

			if tt.expected == nil {
				require.Nil(t, result)
				return
			}

			require.Equal(t, len(tt.expected), len(result))

			for i, expected := range tt.expected {
				require.Equal(t, expected.Type, result[i].Type)
				require.Equal(t, expected.Position, result[i].Position)
				require.Equal(t, expected.Url, result[i].Url)
				require.Equal(t, expected.MimeType, result[i].MimeType)
				require.Equal(t, expected.Thumbnail.Url, result[i].Thumbnail.Url)
				require.Equal(t, expected.SourceVideoHost, result[i].SourceVideoHost)
			}
		})
	}
}

func TestConvertRequestToAttributesValue(t *testing.T) {
	tests := []struct {
		name     string
		input    []AttributeValue
		expected []searchable_products.AttributeValue
	}{
		{
			name:     "nil输入",
			input:    nil,
			expected: nil,
		},
		{
			name:     "空数组",
			input:    []AttributeValue{},
			expected: []searchable_products.AttributeValue{},
		},
		{
			name: "单个属性值转换",
			input: []AttributeValue{
				{
					SourceID: "attr_value_1",
					Type:     "string",
					Value:    "red",
				},
			},
			expected: []searchable_products.AttributeValue{
				{
					SourceID: "attr_value_1",
					Type:     "string",
					Value:    "red",
				},
			},
		},
		{
			name: "多个属性值转换",
			input: []AttributeValue{
				{
					SourceID: "attr_value_1",
					Type:     "string",
					Value:    "red",
				},
				{
					SourceID: "attr_value_2",
					Type:     "number",
					Value:    "42",
				},
			},
			expected: []searchable_products.AttributeValue{
				{
					SourceID: "attr_value_1",
					Type:     "string",
					Value:    "red",
				},
				{
					SourceID: "attr_value_2",
					Type:     "number",
					Value:    "42",
				},
			},
		},
		{
			name: "包含空字段的属性值转换",
			input: []AttributeValue{
				{
					SourceID: "",
					Type:     "",
					Value:    "",
				},
			},
			expected: []searchable_products.AttributeValue{
				{
					SourceID: "",
					Type:     "",
					Value:    "",
				},
			},
		},
		{
			name: "包含特殊字符的属性值转换",
			input: []AttributeValue{
				{
					SourceID: "attr@special-123",
					Type:     "string#type",
					Value:    "value with spaces & symbols!",
				},
			},
			expected: []searchable_products.AttributeValue{
				{
					SourceID: "attr@special-123",
					Type:     "string#type",
					Value:    "value with spaces & symbols!",
				},
			},
		},
		{
			name: "不同类型的属性值转换",
			input: []AttributeValue{
				{
					SourceID: "attr_string",
					Type:     "string",
					Value:    "text_value",
				},
				{
					SourceID: "attr_number",
					Type:     "number",
					Value:    "123.45",
				},
				{
					SourceID: "attr_boolean",
					Type:     "boolean",
					Value:    "true",
				},
			},
			expected: []searchable_products.AttributeValue{
				{
					SourceID: "attr_string",
					Type:     "string",
					Value:    "text_value",
				},
				{
					SourceID: "attr_number",
					Type:     "number",
					Value:    "123.45",
				},
				{
					SourceID: "attr_boolean",
					Type:     "boolean",
					Value:    "true",
				},
			},
		},
		{
			name: "长字符串属性值转换",
			input: []AttributeValue{
				{
					SourceID: "very-long-source-id-with-many-characters-12345678901234567890",
					Type:     "long-type-name-with-many-characters",
					Value:    "very long value string with many characters and words that exceeds normal length",
				},
			},
			expected: []searchable_products.AttributeValue{
				{
					SourceID: "very-long-source-id-with-many-characters-12345678901234567890",
					Type:     "long-type-name-with-many-characters",
					Value:    "very long value string with many characters and words that exceeds normal length",
				},
			},
		},
		{
			name: "包含中文的属性值转换",
			input: []AttributeValue{
				{
					SourceID: "中文属性ID",
					Type:     "字符串类型",
					Value:    "中文属性值",
				},
			},
			expected: []searchable_products.AttributeValue{
				{
					SourceID: "中文属性ID",
					Type:     "字符串类型",
					Value:    "中文属性值",
				},
			},
		},
		{
			name: "包含JSON格式的属性值转换",
			input: []AttributeValue{
				{
					SourceID: "json_attr",
					Type:     "json",
					Value:    `{"key": "value", "number": 123}`,
				},
			},
			expected: []searchable_products.AttributeValue{
				{
					SourceID: "json_attr",
					Type:     "json",
					Value:    `{"key": "value", "number": 123}`,
				},
			},
		},
		{
			name: "包含换行符的属性值转换",
			input: []AttributeValue{
				{
					SourceID: "multiline_attr",
					Type:     "text",
					Value:    "line1\nline2\nline3",
				},
			},
			expected: []searchable_products.AttributeValue{
				{
					SourceID: "multiline_attr",
					Type:     "text",
					Value:    "line1\nline2\nline3",
				},
			},
		},
		{
			name: "包含HTML标签的属性值转换",
			input: []AttributeValue{
				{
					SourceID: "html_attr",
					Type:     "html",
					Value:    "<div>Hello <b>World</b></div>",
				},
			},
			expected: []searchable_products.AttributeValue{
				{
					SourceID: "html_attr",
					Type:     "html",
					Value:    "<div>Hello <b>World</b></div>",
				},
			},
		},
		{
			name: "包含URL的属性值转换",
			input: []AttributeValue{
				{
					SourceID: "url_attr",
					Type:     "url",
					Value:    "https://example.com/path?param=value#anchor",
				},
			},
			expected: []searchable_products.AttributeValue{
				{
					SourceID: "url_attr",
					Type:     "url",
					Value:    "https://example.com/path?param=value#anchor",
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := convertRequestToAttributesValue(tt.input)

			if tt.expected == nil {
				require.Nil(t, result)
				return
			}

			require.Equal(t, len(tt.expected), len(result))

			for i, expected := range tt.expected {
				require.Equal(t, expected.SourceID, result[i].SourceID)
				require.Equal(t, expected.Type, result[i].Type)
				require.Equal(t, expected.Value, result[i].Value)
			}
		})
	}
}

func TestConvertSearchRequestToSaleChannels(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected []models.SalesChannel
	}{
		{
			name:     "空字符串输入",
			input:    "",
			expected: nil,
		},
		{
			name:  "单个销售渠道转换",
			input: "shopify:7495328909601442770",
			expected: []models.SalesChannel{
				{
					Platform: "shopify",
					StoreKey: "7495328909601442770",
				},
			},
		},
		{
			name:  "多个销售渠道转换",
			input: "shopify:7495328909601442770,amazon:store123,ebay:shop456",
			expected: []models.SalesChannel{
				{
					Platform: "shopify",
					StoreKey: "7495328909601442770",
				},
				{
					Platform: "amazon",
					StoreKey: "store123",
				},
				{
					Platform: "ebay",
					StoreKey: "shop456",
				},
			},
		},
		{
			name:  "包含无效格式的销售渠道",
			input: "shopify:7495328909601442770,invalid_format,amazon:store123",
			expected: []models.SalesChannel{
				{
					Platform: "shopify",
					StoreKey: "7495328909601442770",
				},
				{
					Platform: "amazon",
					StoreKey: "store123",
				},
			},
		},
		{
			name:  "包含空字符串的销售渠道",
			input: "shopify:7495328909601442770,,amazon:store123",
			expected: []models.SalesChannel{
				{
					Platform: "shopify",
					StoreKey: "7495328909601442770",
				},
				{
					Platform: "amazon",
					StoreKey: "store123",
				},
			},
		},
		{
			name:  "包含缺少平台的销售渠道",
			input: ":7495328909601442770,amazon:store123",
			expected: []models.SalesChannel{
				{
					Platform: "",
					StoreKey: "7495328909601442770",
				},
				{
					Platform: "amazon",
					StoreKey: "store123",
				},
			},
		},
		{
			name:  "包含缺少店铺键的销售渠道",
			input: "shopify:,amazon:store123",
			expected: []models.SalesChannel{
				{
					Platform: "shopify",
					StoreKey: "",
				},
				{
					Platform: "amazon",
					StoreKey: "store123",
				},
			},
		},
		{
			name:  "包含特殊字符的销售渠道",
			input: "shopify:store-123_test,amazon:store@456",
			expected: []models.SalesChannel{
				{
					Platform: "shopify",
					StoreKey: "store-123_test",
				},
				{
					Platform: "amazon",
					StoreKey: "store@456",
				},
			},
		},
		{
			name:  "包含多个冒号的销售渠道",
			input: "shopify:store:123:test,amazon:store456",
			expected: []models.SalesChannel{
				{
					Platform: "amazon",
					StoreKey: "store456",
				},
			},
		},
		{
			name:     "全部无效格式的销售渠道",
			input:    "invalid_format1,invalid_format2,no_colon",
			expected: []models.SalesChannel{},
		},
		{
			name:  "包含空格的销售渠道",
			input: "shopify:store 123,amazon:store456",
			expected: []models.SalesChannel{
				{
					Platform: "shopify",
					StoreKey: "store 123",
				},
				{
					Platform: "amazon",
					StoreKey: "store456",
				},
			},
		},
		{
			name:  "长平台名和店铺键的销售渠道",
			input: "very-long-platform-name:very-long-store-key-12345678901234567890",
			expected: []models.SalesChannel{
				{
					Platform: "very-long-platform-name",
					StoreKey: "very-long-store-key-12345678901234567890",
				},
			},
		},
		{
			name:  "数字平台名的销售渠道",
			input: "123:store456,amazon:789",
			expected: []models.SalesChannel{
				{
					Platform: "123",
					StoreKey: "store456",
				},
				{
					Platform: "amazon",
					StoreKey: "789",
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := convertSearchRequestToSaleChannels(tt.input)

			if tt.expected == nil {
				require.Nil(t, result)
				return
			}

			require.Equal(t, len(tt.expected), len(result))

			for i, expected := range tt.expected {
				require.Equal(t, expected.Platform, result[i].Platform)
				require.Equal(t, expected.StoreKey, result[i].StoreKey)
			}
		})
	}
}
