package searchable_products

import (
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"

	"github.com/AfterShip/connectors-library/gin/responder"
	"github.com/AfterShip/connectors-library/sdks/products_center"
	"github.com/AfterShip/gopkg/log"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/searchable_products"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
)

type Product struct {
	logger               *log.Logger
	svc                  searchable_products.Service
	productsCenterClient *products_center.Client
	responder            *responder.Responder
}

func NewProduct(logger *log.Logger, productsCenterClient *products_center.Client, svc searchable_products.Service) *Product {
	p := &Product{
		logger:               logger,
		productsCenterClient: productsCenterClient,
		svc:                  svc,
		responder:            responder.NewResponder(false, models.CommonErrorsMapping),
	}

	p.responder.RegisterDomainError(errorResponseMapping)
	return p
}

func (p *Product) RegisterRoutes(router *gin.RouterGroup) {
	p.registerRoutes(router)
	p.registerInternalRoutes(router)
}

func (p *Product) registerRoutes(router *gin.RouterGroup) {
	router.GET("/searchable-products/:id", p.GetSearchableProduct)
	router.GET("/searchable-products", p.ListSearchableProducts)
	router.GET("/searchable-product-ids", p.GetSearchableProductIDs)

	router.POST("/searchable-products/options/aggregation", p.OptionsAggregation)
}

func (p *Product) registerInternalRoutes(router *gin.RouterGroup) {
	internalRouter := router.Group("/internal/searchable-products")
	internalRouter.POST("", p.UpsertSearchableProduct)

	internalRouter.DELETE("/:id", p.DeleteSearchableProduct)

	esProxyGroup := internalRouter.Group("/es-proxy")
	{
		esProxyGroup.GET("/count", p.esProxySearchableProductCount)
		esProxyGroup.GET("/search", p.esProxySearchableProductSearch)
	}
}

func (p *Product) GetSearchableProduct(c *gin.Context) {
	ctx := c.Request.Context()

	id := c.Param("id")
	if id == "" {
		p.responder.ResponseWithError(c, models.ErrMissRequiredPathParam)
	}

	searchableProduct, err := p.svc.GetByID(ctx, id)
	if err != nil {
		p.responder.ResponseWithError(c, err)
		return
	}

	p.responder.ResponseWithOK(c, convertToSearchableProductResp(searchableProduct))
}

func (p *Product) UpsertSearchableProduct(c *gin.Context) {
	ctx := c.Request.Context()

	var req *UpsertSearchableProductRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		p.responder.ResponseWithErrorCode(c, models.CodeMissRequiredBodyParam, err)
		return
	}

	searchableProduct, err := p.svc.Upsert(ctx, req.toServiceSearchableProduct())
	if err != nil {
		p.responder.ResponseWithError(c, err)
		return
	}

	p.responder.ResponseWithOK(c, convertToSearchableProductResp(searchableProduct))
}

func (p *Product) ListSearchableProducts(c *gin.Context) { // nolint
	ctx := c.Request.Context()

	params := &SearchRequestParams{
		Page:  1,
		Limit: 100,
	}
	if err := c.ShouldBindQuery(params); err != nil {
		p.responder.ResponseWithErrorCode(c, models.CodeMissRequiredQueryParam, err)
		return
	}

	searchableProducts, pagination, err := p.svc.Search(ctx, params.toServiceSearchArgs())
	if err != nil {
		p.responder.ResponseWithError(c, err)
		return
	}

	resp := SearchResponse{
		SearchableProducts: convertToSearchableProductsResp(searchableProducts),
		Pagination:         pagination,
		ParameterString:    c.Request.URL.RawQuery,
	}

	p.responder.ResponseWithOK(c, resp)
}

func (p *Product) GetSearchableProductIDs(c *gin.Context) { // nolint
	ctx := c.Request.Context()

	params := &SearchRequestParams{
		Page:  1,
		Limit: 100,
	}
	if err := c.ShouldBindQuery(params); err != nil {
		p.responder.ResponseWithErrorCode(c, models.CodeMissRequiredQueryParam, err)
		return
	}

	ids, pagination, err := p.svc.SearchIDs(ctx, params.toServiceSearchArgs())
	if err != nil {
		p.responder.ResponseWithError(c, err)
		return
	}

	resp := SearchIDsResponse{
		IDs:             ids,
		Pagination:      pagination,
		ParameterString: c.Request.URL.RawQuery,
	}

	p.responder.ResponseWithOK(c, resp)
}

func (p *Product) DeleteSearchableProduct(c *gin.Context) {
	ctx := c.Request.Context()

	id := c.Param("id")
	if id == "" {
		p.responder.ResponseWithError(c, models.ErrMissRequiredPathParam)
	}

	err := p.svc.DeleteByID(ctx, id)
	if err != nil {
		p.responder.ResponseWithError(c, err)
		return
	}

	p.logger.InfoCtx(ctx, "delete searchable product from products center", zap.String("id", id))

	p.responder.ResponseWithOK(c, "deleted")
}

func (p *Product) OptionsAggregation(c *gin.Context) {
	ctx := c.Request.Context()

	var req OptionsAggregationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		p.responder.ResponseWithErrorCode(c, models.CodeMissRequiredBodyParam, err)
		return
	}

	aggregation, err := p.svc.OptionsAggregation(ctx, req.toServiceOptionsAggregationArgs())
	if err != nil {
		p.responder.ResponseWithError(c, err)
		return
	}

	p.responder.ResponseWithOK(c, convertToOptionsAggregationResponse(aggregation))
}
