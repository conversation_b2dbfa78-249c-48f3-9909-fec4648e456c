package task

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"

	"github.com/AfterShip/gopkg/api"
	"github.com/AfterShip/gopkg/log"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/task_schedule"
)

type Task struct {
	logger              *log.Logger
	taskScheduleService task_schedule.Service
	validator           *validator.Validate
}

func NewTask(logger *log.Logger, taskScheduleService task_schedule.Service) *Task {
	return &Task{
		logger:              logger,
		taskScheduleService: taskScheduleService,
		validator:           validator.New(),
	}
}

func (t *Task) validateMiddleware(c *gin.Context) {
	c.Next()
}

func (t *Task) RegisterRoutes(router *gin.RouterGroup) {
	router.Use(t.validateMiddleware)

	router.POST("/tasks", t.create)
	router.GET("/tasks", t.list)
	router.GET("/tasks/:id", t.getByID)
	router.POST("/tasks/:id/cancel", t.cancel)
	router.GET("/tasks/unfinished-task-ids", t.getIDsWithinUnfinishedTask)
}

func (t *Task) list(c *gin.Context) {
	var request listRequest
	if err := c.ShouldBindQuery(&request); err != nil {
		api.ResponseError(c, http.StatusUnprocessableEntity, err)
		return
	}
	args := request.toListArgs()
	result, paginator, err := t.taskScheduleService.List(c, &args)
	if err != nil {
		api.ResponseError(c, http.StatusInternalServerError, err)
		return
	}

	response := listResponse{
		Tasks:           result,
		Paginator:       paginator,
		ParameterString: c.Request.URL.RawQuery,
	}

	api.ResponseWithOK(c, response)
}

func (t *Task) create(c *gin.Context) {
	request := task_schedule.CreateArgs{}
	if err := c.ShouldBindJSON(&request); err != nil {
		api.ResponseError(c, http.StatusUnprocessableEntity, err)
		return
	}

	result, err := t.taskScheduleService.Create(c, &request)
	if err != nil {
		api.ResponseError(c, http.StatusInternalServerError, err)
		return
	}

	api.ResponseWithOK(c, result)
}

func (t *Task) getByID(c *gin.Context) {
	id := c.Param("id")

	result, err := t.taskScheduleService.GetByID(c, id)
	if err != nil {
		api.ResponseError(c, http.StatusInternalServerError, err)
		return
	}

	api.ResponseWithOK(c, result)
}

func (t *Task) cancel(c *gin.Context) {
	id := c.Param("id")
	request := task_schedule.CancelArgs{}
	if err := c.ShouldBindJSON(&request); err != nil {
		api.ResponseError(c, http.StatusUnprocessableEntity, err)
		return
	}

	result, err := t.taskScheduleService.Cancel(c, id, &request)
	if err != nil {
		api.ResponseError(c, http.StatusInternalServerError, err)
		return
	}

	api.ResponseWithOK(c, result)
}

func (t *Task) getIDsWithinUnfinishedTask(c *gin.Context) {
	ctx := c.Request.Context()
	var req getIDsWithinUnfinishedTaskRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		api.ResponseError(c, http.StatusUnprocessableEntity, err)
		return
	}

	if err := t.validator.Struct(req); err != nil {
		api.ResponseError(c, http.StatusUnprocessableEntity, err)
		return
	}

	ids, err := t.taskScheduleService.GetIDsWithinUnfinishedTask(ctx, req.convertToArg())
	if err != nil {
		api.ResponseError(c, http.StatusInternalServerError, err)
		return
	}
	api.ResponseWithOK(c, convertToGetIDsWithinUnfinishedTaskResponse(ids))
}
