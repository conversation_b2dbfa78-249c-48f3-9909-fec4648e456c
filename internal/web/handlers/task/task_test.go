package task

import (
	"bytes"
	"context"
	"errors"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"github.com/tidwall/gjson"

	"github.com/AfterShip/pltf-pd-product-listings/internal/consts"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/task_schedule"
	"github.com/AfterShip/pltf-pd-product-listings/internal/logger"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
)

func TestTask_list(t *testing.T) {
	mockSvc := new(task_schedule.MockTaskScheduleService)
	testcases := []struct {
		name      string
		path      string
		mock      func()
		expCode   int
		respCheck func(*testing.T, string)
	}{
		{
			name:    "Params validate error",
			path:    "/tasks?",
			expCode: http.StatusUnprocessableEntity,
		},
		{
			name: "Error case",
			path: "/tasks?group_names=publish_product_listing&organization_id=123&store_key=123&platform=123",
			mock: func() {
				mockSvc.On("List", mock.Anything, mock.Anything).Return([]task_schedule.TaskSchedule{}, models.Paginator{}, errors.New("some error")).Once()
			},
			expCode: http.StatusInternalServerError,
		},
		{
			name: "Good case",
			path: "/tasks?group_names=publish_product_listing&organization_id=123&store_key=123&platform=123",
			mock: func() {
				mockSvc.On("List", mock.Anything, mock.Anything).Return([]task_schedule.TaskSchedule{
					{
						ID:        "123",
						GroupName: consts.PublishProductListings,
						Outputs:   &task_schedule.TaskScheduleOutputs{},
						Status: models.TaskStatus{
							State:        "succeeded",
							PendingAt:    time.Now(),
							RunningAt:    time.Now(),
							SucceededAt:  time.Now(),
							CancelledAt:  time.Now(),
							LastFailedAt: time.Now(),
							TerminatedAt: time.Now(),
						},
						CreatedAt: time.Now(),
						UpdatedAt: time.Now(),
					},
				}, models.Paginator{}, nil).Once()
			},
			expCode: http.StatusOK,
			respCheck: func(t *testing.T, resp string) {
				require.NotEmpty(t, gjson.Parse(resp).Get("data").String())
			},
		},
	}
	for _, tt := range testcases {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mock != nil {
				tt.mock()
			}
			w := httptest.NewRecorder()
			gin.SetMode(gin.TestMode)
			_, eng := gin.CreateTestContext(w)

			NewTask(logger.Get(), mockSvc).RegisterRoutes(eng.RouterGroup.Group(""))

			req, _ := http.NewRequestWithContext(context.Background(), http.MethodGet, tt.path, nil)
			eng.ServeHTTP(w, req)
			resp := w.Body.String()
			require.Equal(t, tt.expCode, w.Code, resp)
			if tt.respCheck != nil {
				tt.respCheck(t, resp)
			}
		})
	}
}

func TestTask_Create(t *testing.T) {
	mockSvc := new(task_schedule.MockTaskScheduleService)
	testcases := []struct {
		name      string
		path      string
		body      []byte
		mock      func()
		expCode   int
		respCheck func(*testing.T, string)
	}{
		{
			name: "error case",
			path: "/tasks",
			body: []byte(`{"type":"publish_product_listing"}`),
			mock: func() {
				mockSvc.On("Create", mock.Anything, mock.Anything).Return(task_schedule.TaskSchedule{}, errors.New("some error")).Once()
			},
			expCode: http.StatusInternalServerError,
		},
		{
			name: "Good case",
			path: "/tasks",
			body: []byte(`{"type":"publish_product_listing"}`),
			mock: func() {
				mockSvc.On("Create", mock.Anything, mock.Anything).Return(task_schedule.TaskSchedule{
					ID:        "123",
					GroupName: consts.PublishProductListings,
					Outputs:   &task_schedule.TaskScheduleOutputs{},
					Status: models.TaskStatus{
						State:        "succeeded",
						PendingAt:    time.Now(),
						RunningAt:    time.Now(),
						SucceededAt:  time.Now(),
						CancelledAt:  time.Now(),
						LastFailedAt: time.Now(),
						TerminatedAt: time.Now(),
					},
					CreatedAt: time.Now(),
					UpdatedAt: time.Now(),
				}, nil).Once()
			},
			expCode: http.StatusOK,
			respCheck: func(t *testing.T, resp string) {
				require.NotEmpty(t, gjson.Parse(resp).Get("data").String())
			},
		},
	}
	for _, tt := range testcases {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mock != nil {
				tt.mock()
			}
			w := httptest.NewRecorder()
			gin.SetMode(gin.TestMode)
			_, eng := gin.CreateTestContext(w)

			NewTask(logger.Get(), mockSvc).RegisterRoutes(eng.RouterGroup.Group(""))

			req, _ := http.NewRequestWithContext(context.Background(), http.MethodPost, tt.path, bytes.NewBuffer(tt.body))
			eng.ServeHTTP(w, req)
			resp := w.Body.String()
			require.Equal(t, tt.expCode, w.Code, resp)
			if tt.respCheck != nil {
				tt.respCheck(t, resp)
			}
		})
	}
}

func TestTask_getIDsWithinUnfinishedTask(t *testing.T) {
	mockSvc := new(task_schedule.MockTaskScheduleService)
	testcases := []struct {
		name      string
		path      string
		mock      func()
		expCode   int
		respCheck func(*testing.T, string)
	}{
		{
			name:    "缺少必需参数时参数验证失败",
			path:    "/tasks/unfinished-task-ids",
			expCode: http.StatusUnprocessableEntity,
		},
		{
			name:    "只有organization_id参数时参数验证失败",
			path:    "/tasks/unfinished-task-ids?organization_id=org123",
			expCode: http.StatusUnprocessableEntity,
		},
		{
			name:    "只有group_name参数时参数验证失败",
			path:    "/tasks/unfinished-task-ids?group_name=publish_product_listing",
			expCode: http.StatusUnprocessableEntity,
		},
		{
			name: "服务层返回错误",
			path: "/tasks/unfinished-task-ids?organization_id=org123&group_name=publish_product_listing&store_key=store456&platform=shopify",
			mock: func() {
				mockSvc.On("GetIDsWithinUnfinishedTask", mock.Anything, mock.Anything).Return([]string{}, errors.New("service error")).Once()
			},
			expCode: http.StatusInternalServerError,
		},
		{
			name: "成功返回空结果",
			path: "/tasks/unfinished-task-ids?organization_id=org123&group_name=publish_product_listing&store_key=store456&platform=shopify",
			mock: func() {
				mockSvc.On("GetIDsWithinUnfinishedTask", mock.Anything, mock.Anything).Return([]string{}, nil).Once()
			},
			expCode: http.StatusOK,
			respCheck: func(t *testing.T, resp string) {
				data := gjson.Parse(resp).Get("data")
				require.True(t, data.Exists())
				require.Equal(t, 0, len(data.Get("product_listings_ids").Array()))
			},
		},
		{
			name: "成功返回任务ID列表",
			path: "/tasks/unfinished-task-ids?organization_id=org123&group_name=publish_product_listing&store_key=store456&platform=shopify",
			mock: func() {
				mockSvc.On("GetIDsWithinUnfinishedTask", mock.Anything, mock.Anything).Return([]string{"task1", "task2", "task3"}, nil).Once()
			},
			expCode: http.StatusOK,
			respCheck: func(t *testing.T, resp string) {
				data := gjson.Parse(resp).Get("data")
				require.True(t, data.Exists())
				ids := data.Get("product_listings_ids").Array()
				require.Equal(t, 3, len(ids))
				require.Equal(t, "task1", ids[0].String())
				require.Equal(t, "task2", ids[1].String())
				require.Equal(t, "task3", ids[2].String())
			},
		},
		{
			name: "带有额外参数的成功请求",
			path: "/tasks/unfinished-task-ids?organization_id=org123&group_name=publish_product_listing&store_key=store456&platform=shopify&extra_param=value",
			mock: func() {
				mockSvc.On("GetIDsWithinUnfinishedTask", mock.Anything, mock.Anything).Return([]string{"task1"}, nil).Once()
			},
			expCode: http.StatusOK,
			respCheck: func(t *testing.T, resp string) {
				data := gjson.Parse(resp).Get("data")
				require.True(t, data.Exists())
				ids := data.Get("product_listings_ids").Array()
				require.Equal(t, 1, len(ids))
				require.Equal(t, "task1", ids[0].String())
			},
		},
	}

	for _, tt := range testcases {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mock != nil {
				tt.mock()
			}
			w := httptest.NewRecorder()
			gin.SetMode(gin.TestMode)
			_, eng := gin.CreateTestContext(w)

			NewTask(logger.Get(), mockSvc).RegisterRoutes(eng.RouterGroup.Group(""))

			req, _ := http.NewRequestWithContext(context.Background(), http.MethodGet, tt.path, nil)
			eng.ServeHTTP(w, req)
			resp := w.Body.String()
			require.Equal(t, tt.expCode, w.Code, resp)
			if tt.respCheck != nil {
				tt.respCheck(t, resp)
			}
		})
	}
}

func TestTask_cancel(t *testing.T) {
	mockSvc := new(task_schedule.MockTaskScheduleService)
	testcases := []struct {
		name      string
		path      string
		body      []byte
		mock      func()
		expCode   int
		respCheck func(*testing.T, string)
	}{
		{
			name:    "请求体为空时绑定失败",
			path:    "/tasks/task123/cancel",
			body:    []byte(``),
			expCode: http.StatusUnprocessableEntity,
		},
		{
			name:    "请求体格式错误时绑定失败",
			path:    "/tasks/task123/cancel",
			body:    []byte(`{invalid json`),
			expCode: http.StatusUnprocessableEntity,
		},
		{
			name: "服务层返回错误",
			path: "/tasks/task123/cancel",
			body: []byte(`{"reason":"用户取消"}`),
			mock: func() {
				mockSvc.On("Cancel", mock.Anything, "task123", mock.Anything).Return(task_schedule.TaskSchedule{}, errors.New("cancel error")).Once()
			},
			expCode: http.StatusInternalServerError,
		},
		{
			name: "成功取消任务",
			path: "/tasks/task123/cancel",
			body: []byte(`{"reason":"用户取消"}`),
			mock: func() {
				mockSvc.On("Cancel", mock.Anything, "task123", mock.Anything).Return(task_schedule.TaskSchedule{
					ID:        "task123",
					GroupName: consts.PublishProductListings,
					Status: models.TaskStatus{
						State:       "cancelled",
						CancelledAt: time.Now(),
					},
					CreatedAt: time.Now(),
					UpdatedAt: time.Now(),
				}, nil).Once()
			},
			expCode: http.StatusOK,
			respCheck: func(t *testing.T, resp string) {
				data := gjson.Parse(resp).Get("data")
				require.True(t, data.Exists())
				require.Equal(t, "task123", data.Get("id").String())
				require.Equal(t, "cancelled", data.Get("status.state").String())
			},
		},
		{
			name: "带有详细取消原因的成功取消",
			path: "/tasks/task456/cancel",
			body: []byte(`{"reason":"系统维护，需要取消任务"}`),
			mock: func() {
				mockSvc.On("Cancel", mock.Anything, "task456", mock.Anything).Return(task_schedule.TaskSchedule{
					ID:        "task456",
					GroupName: consts.PublishProductListings,
					Status: models.TaskStatus{
						State:       "cancelled",
						CancelledAt: time.Now(),
					},
					CreatedAt: time.Now(),
					UpdatedAt: time.Now(),
				}, nil).Once()
			},
			expCode: http.StatusOK,
			respCheck: func(t *testing.T, resp string) {
				data := gjson.Parse(resp).Get("data")
				require.True(t, data.Exists())
				require.Equal(t, "task456", data.Get("id").String())
				require.Equal(t, "cancelled", data.Get("status.state").String())
			},
		},
		{
			name: "空取消原因的成功取消",
			path: "/tasks/task789/cancel",
			body: []byte(`{"reason":""}`),
			mock: func() {
				mockSvc.On("Cancel", mock.Anything, "task789", mock.Anything).Return(task_schedule.TaskSchedule{
					ID:        "task789",
					GroupName: consts.PublishProductListings,
					Status: models.TaskStatus{
						State:       "cancelled",
						CancelledAt: time.Now(),
					},
					CreatedAt: time.Now(),
					UpdatedAt: time.Now(),
				}, nil).Once()
			},
			expCode: http.StatusOK,
			respCheck: func(t *testing.T, resp string) {
				data := gjson.Parse(resp).Get("data")
				require.True(t, data.Exists())
				require.Equal(t, "task789", data.Get("id").String())
				require.Equal(t, "cancelled", data.Get("status.state").String())
			},
		},
		{
			name: "包含额外字段的请求体",
			path: "/tasks/task999/cancel",
			body: []byte(`{"reason":"用户取消","extra_field":"extra_value"}`),
			mock: func() {
				mockSvc.On("Cancel", mock.Anything, "task999", mock.Anything).Return(task_schedule.TaskSchedule{
					ID:        "task999",
					GroupName: consts.PublishProductListings,
					Status: models.TaskStatus{
						State:       "cancelled",
						CancelledAt: time.Now(),
					},
					CreatedAt: time.Now(),
					UpdatedAt: time.Now(),
				}, nil).Once()
			},
			expCode: http.StatusOK,
			respCheck: func(t *testing.T, resp string) {
				data := gjson.Parse(resp).Get("data")
				require.True(t, data.Exists())
				require.Equal(t, "task999", data.Get("id").String())
				require.Equal(t, "cancelled", data.Get("status.state").String())
			},
		},
	}

	for _, tt := range testcases {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mock != nil {
				tt.mock()
			}
			w := httptest.NewRecorder()
			gin.SetMode(gin.TestMode)
			_, eng := gin.CreateTestContext(w)

			NewTask(logger.Get(), mockSvc).RegisterRoutes(eng.RouterGroup.Group(""))

			req, _ := http.NewRequestWithContext(context.Background(), http.MethodPost, tt.path, bytes.NewBuffer(tt.body))
			req.Header.Set("Content-Type", "application/json")
			eng.ServeHTTP(w, req)
			resp := w.Body.String()
			require.Equal(t, tt.expCode, w.Code, resp)
			if tt.respCheck != nil {
				tt.respCheck(t, resp)
			}
		})
	}
}

func TestTask_getByID(t *testing.T) {
	mockSvc := new(task_schedule.MockTaskScheduleService)
	testcases := []struct {
		name      string
		path      string
		mock      func()
		expCode   int
		respCheck func(*testing.T, string)
	}{
		{
			name: "服务层返回错误",
			path: "/tasks/task123",
			mock: func() {
				mockSvc.On("GetByID", mock.Anything, "task123").Return(task_schedule.TaskSchedule{}, errors.New("service error")).Once()
			},
			expCode: http.StatusInternalServerError,
		},
		{
			name: "成功获取任务信息",
			path: "/tasks/task123",
			mock: func() {
				mockSvc.On("GetByID", mock.Anything, "task123").Return(task_schedule.TaskSchedule{
					ID:        "task123",
					GroupName: consts.PublishProductListings,
					Status: models.TaskStatus{
						State:     "running",
						RunningAt: time.Now(),
					},
					CreatedAt: time.Now(),
					UpdatedAt: time.Now(),
				}, nil).Once()
			},
			expCode: http.StatusOK,
			respCheck: func(t *testing.T, resp string) {
				data := gjson.Parse(resp).Get("data")
				require.True(t, data.Exists())
				require.Equal(t, "task123", data.Get("id").String())
				require.Equal(t, "running", data.Get("status.state").String())
			},
		},
		{
			name: "获取已完成任务信息",
			path: "/tasks/task456",
			mock: func() {
				mockSvc.On("GetByID", mock.Anything, "task456").Return(task_schedule.TaskSchedule{
					ID:        "task456",
					GroupName: consts.PublishProductListings,
					Status: models.TaskStatus{
						State:       "completed",
						RunningAt:   time.Now().Add(-1 * time.Hour),
						SucceededAt: time.Now(),
					},
					CreatedAt: time.Now().Add(-1 * time.Hour),
					UpdatedAt: time.Now(),
				}, nil).Once()
			},
			expCode: http.StatusOK,
			respCheck: func(t *testing.T, resp string) {
				data := gjson.Parse(resp).Get("data")
				require.True(t, data.Exists())
				require.Equal(t, "task456", data.Get("id").String())
				require.Equal(t, "completed", data.Get("status.state").String())
			},
		},
		{
			name: "获取失败任务信息",
			path: "/tasks/task789",
			mock: func() {
				mockSvc.On("GetByID", mock.Anything, "task789").Return(task_schedule.TaskSchedule{
					ID:        "task789",
					GroupName: consts.PublishProductListings,
					Status: models.TaskStatus{
						State:        "failed",
						RunningAt:    time.Now().Add(-30 * time.Minute),
						LastFailedAt: time.Now(),
					},
					CreatedAt: time.Now().Add(-30 * time.Minute),
					UpdatedAt: time.Now(),
				}, nil).Once()
			},
			expCode: http.StatusOK,
			respCheck: func(t *testing.T, resp string) {
				data := gjson.Parse(resp).Get("data")
				require.True(t, data.Exists())
				require.Equal(t, "task789", data.Get("id").String())
				require.Equal(t, "failed", data.Get("status.state").String())
			},
		},
		{
			name: "获取取消任务信息",
			path: "/tasks/task999",
			mock: func() {
				mockSvc.On("GetByID", mock.Anything, "task999").Return(task_schedule.TaskSchedule{
					ID:        "task999",
					GroupName: consts.PublishProductListings,
					Status: models.TaskStatus{
						State:       "cancelled",
						RunningAt:   time.Now().Add(-15 * time.Minute),
						CancelledAt: time.Now(),
					},
					CreatedAt: time.Now().Add(-15 * time.Minute),
					UpdatedAt: time.Now(),
				}, nil).Once()
			},
			expCode: http.StatusOK,
			respCheck: func(t *testing.T, resp string) {
				data := gjson.Parse(resp).Get("data")
				require.True(t, data.Exists())
				require.Equal(t, "task999", data.Get("id").String())
				require.Equal(t, "cancelled", data.Get("status.state").String())
			},
		},
		{
			name: "获取等待中任务信息",
			path: "/tasks/task111",
			mock: func() {
				mockSvc.On("GetByID", mock.Anything, "task111").Return(task_schedule.TaskSchedule{
					ID:        "task111",
					GroupName: consts.PublishProductListings,
					Status: models.TaskStatus{
						State: "pending",
					},
					CreatedAt: time.Now(),
					UpdatedAt: time.Now(),
				}, nil).Once()
			},
			expCode: http.StatusOK,
			respCheck: func(t *testing.T, resp string) {
				data := gjson.Parse(resp).Get("data")
				require.True(t, data.Exists())
				require.Equal(t, "task111", data.Get("id").String())
				require.Equal(t, "pending", data.Get("status.state").String())
			},
		},
		{
			name: "获取包含特殊字符的任务ID",
			path: "/tasks/task-with-special-chars_123",
			mock: func() {
				mockSvc.On("GetByID", mock.Anything, "task-with-special-chars_123").Return(task_schedule.TaskSchedule{
					ID:        "task-with-special-chars_123",
					GroupName: consts.PublishProductListings,
					Status: models.TaskStatus{
						State: "running",
					},
					CreatedAt: time.Now(),
					UpdatedAt: time.Now(),
				}, nil).Once()
			},
			expCode: http.StatusOK,
			respCheck: func(t *testing.T, resp string) {
				data := gjson.Parse(resp).Get("data")
				require.True(t, data.Exists())
				require.Equal(t, "task-with-special-chars_123", data.Get("id").String())
				require.Equal(t, "running", data.Get("status.state").String())
			},
		},
	}

	for _, tt := range testcases {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mock != nil {
				tt.mock()
			}
			w := httptest.NewRecorder()
			gin.SetMode(gin.TestMode)
			_, eng := gin.CreateTestContext(w)

			NewTask(logger.Get(), mockSvc).RegisterRoutes(eng.RouterGroup.Group(""))

			req, _ := http.NewRequestWithContext(context.Background(), http.MethodGet, tt.path, nil)
			eng.ServeHTTP(w, req)
			resp := w.Body.String()
			require.Equal(t, tt.expCode, w.Code, resp)
			if tt.respCheck != nil {
				tt.respCheck(t, resp)
			}
		})
	}
}
