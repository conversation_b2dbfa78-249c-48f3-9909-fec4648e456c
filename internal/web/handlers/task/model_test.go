package task

import (
	"testing"

	"github.com/stretchr/testify/require"

	"github.com/AfterShip/pltf-pd-product-listings/internal/consts"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/task_schedule"
)

func TestGetIDsWithinUnfinishedTaskRequest_convertToArg(t *testing.T) {
	tests := []struct {
		name     string
		request  getIDsWithinUnfinishedTaskRequest
		expected *task_schedule.GetIDsWithinUnfinishedTaskArg
	}{
		{
			name: "正常转换所有字段",
			request: getIDsWithinUnfinishedTaskRequest{
				OrganizationID: "org123",
				StoreKey:       "store456",
				Platform:       "shopify",
				GroupName:      "publish_product_listing",
			},
			expected: &task_schedule.GetIDsWithinUnfinishedTaskArg{
				OrganizationID: "org123",
				StoreKey:       "store456",
				Platform:       "shopify",
				GroupName:      consts.TaskGroupName("publish_product_listing"),
			},
		},
		{
			name: "包含空字符串的转换",
			request: getIDsWithinUnfinishedTaskRequest{
				OrganizationID: "",
				StoreKey:       "",
				Platform:       "",
				GroupName:      "",
			},
			expected: &task_schedule.GetIDsWithinUnfinishedTaskArg{
				OrganizationID: "",
				StoreKey:       "",
				Platform:       "",
				GroupName:      consts.TaskGroupName(""),
			},
		},
		{
			name: "包含特殊字符的转换",
			request: getIDsWithinUnfinishedTaskRequest{
				OrganizationID: "org-123_test",
				StoreKey:       "store-456_test",
				Platform:       "shopify-plus",
				GroupName:      "publish_product_listing",
			},
			expected: &task_schedule.GetIDsWithinUnfinishedTaskArg{
				OrganizationID: "org-123_test",
				StoreKey:       "store-456_test",
				Platform:       "shopify-plus",
				GroupName:      consts.TaskGroupName("publish_product_listing"),
			},
		},
		{
			name: "不同平台的转换",
			request: getIDsWithinUnfinishedTaskRequest{
				OrganizationID: "org789",
				StoreKey:       "store999",
				Platform:       "amazon",
				GroupName:      "publish_product_listing",
			},
			expected: &task_schedule.GetIDsWithinUnfinishedTaskArg{
				OrganizationID: "org789",
				StoreKey:       "store999",
				Platform:       "amazon",
				GroupName:      consts.TaskGroupName("publish_product_listing"),
			},
		},
		{
			name: "长字符串的转换",
			request: getIDsWithinUnfinishedTaskRequest{
				OrganizationID: "very-long-organization-id-12345678901234567890",
				StoreKey:       "very-long-store-key-12345678901234567890",
				Platform:       "very-long-platform-name-12345678901234567890",
				GroupName:      "publish_product_listing",
			},
			expected: &task_schedule.GetIDsWithinUnfinishedTaskArg{
				OrganizationID: "very-long-organization-id-12345678901234567890",
				StoreKey:       "very-long-store-key-12345678901234567890",
				Platform:       "very-long-platform-name-12345678901234567890",
				GroupName:      consts.TaskGroupName("publish_product_listing"),
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tt.request.convertToArg()

			require.NotNil(t, result)
			require.Equal(t, tt.expected.OrganizationID, result.OrganizationID)
			require.Equal(t, tt.expected.StoreKey, result.StoreKey)
			require.Equal(t, tt.expected.Platform, result.Platform)
			require.Equal(t, tt.expected.GroupName, result.GroupName)
		})
	}
}
