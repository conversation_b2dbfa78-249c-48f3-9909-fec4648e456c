package task

import (
	"testing"

	"github.com/stretchr/testify/require"
)

func TestConvertToGetIDsWithinUnfinishedTaskResponse(t *testing.T) {
	tests := []struct {
		name     string
		ids      []string
		expected getIDsWithinUnfinishedTaskResponse
	}{
		{
			name: "空ID列表",
			ids:  []string{},
			expected: getIDsWithinUnfinishedTaskResponse{
				ProductListingIDs: []string{},
			},
		},
		{
			name: "nil ID列表",
			ids:  nil,
			expected: getIDsWithinUnfinishedTaskResponse{
				ProductListingIDs: nil,
			},
		},
		{
			name: "单个ID",
			ids:  []string{"task123"},
			expected: getIDsWithinUnfinishedTaskResponse{
				ProductListingIDs: []string{"task123"},
			},
		},
		{
			name: "多个ID",
			ids:  []string{"task123", "task456", "task789"},
			expected: getIDsWithinUnfinishedTaskResponse{
				ProductListingIDs: []string{"task123", "task456", "task789"},
			},
		},
		{
			name: "包含特殊字符的ID",
			ids:  []string{"task-123_test", "task@456#test", "task.789"},
			expected: getIDsWithinUnfinishedTaskResponse{
				ProductListingIDs: []string{"task-123_test", "task@456#test", "task.789"},
			},
		},
		{
			name: "包含空字符串的ID",
			ids:  []string{"", "task123", ""},
			expected: getIDsWithinUnfinishedTaskResponse{
				ProductListingIDs: []string{"", "task123", ""},
			},
		},
		{
			name: "长ID列表",
			ids:  []string{"id1", "id2", "id3", "id4", "id5", "id6", "id7", "id8", "id9", "id10"},
			expected: getIDsWithinUnfinishedTaskResponse{
				ProductListingIDs: []string{"id1", "id2", "id3", "id4", "id5", "id6", "id7", "id8", "id9", "id10"},
			},
		},
		{
			name: "包含重复ID",
			ids:  []string{"task123", "task456", "task123"},
			expected: getIDsWithinUnfinishedTaskResponse{
				ProductListingIDs: []string{"task123", "task456", "task123"},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := convertToGetIDsWithinUnfinishedTaskResponse(tt.ids)

			require.Equal(t, len(tt.expected.ProductListingIDs), len(result.ProductListingIDs))

			if tt.expected.ProductListingIDs == nil {
				require.Nil(t, result.ProductListingIDs)
			} else {
				require.Equal(t, tt.expected.ProductListingIDs, result.ProductListingIDs)
			}
		})
	}
}
