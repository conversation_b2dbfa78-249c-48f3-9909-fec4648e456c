package task

import (
	"strings"

	"github.com/AfterShip/pltf-pd-product-listings/internal/consts"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/task_schedule"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
)

type listRequest struct {
	GroupNames     string `form:"group_names" binding:"required"`
	OrganizationID string `form:"organization_id" binding:"required"`
	StoreKey       string `form:"store_key" binding:"required"`
	Platform       string `form:"platform" binding:"required"`
	ResourceID     string `form:"resource_id"`
	Status         string `form:"status"`
	Page           int64  `form:"page" binding:"lte=1000"`
	Limit          int64  `form:"limit" binding:"lte=1000"`
}

func (r *listRequest) toListArgs() task_schedule.ListArgs {
	names := strings.Split(r.GroupNames, ",")
	groupNames := make([]consts.TaskGroupName, 0, len(names))
	for _, t := range names {
		groupNames = append(groupNames, consts.TaskGroupName(t))
	}

	return task_schedule.ListArgs{
		GroupNames:     groupNames,
		OrganizationID: r.OrganizationID,
		StoreKey:       r.<PERSON>,
		Platform:       r.Platform,
		ResourceID:     r.ResourceID,
		Status:         r.Status,
		Page:           r.Page,
		Limit:          r.Limit,
	}
}

type listResponse struct {
	Tasks           []task_schedule.TaskSchedule `json:"tasks"`
	Paginator       models.Paginator             `json:"paginator"`
	ParameterString string                       `json:"parameter_string"`
}

type getIDsWithinUnfinishedTaskRequest struct {
	OrganizationID string `validate:"required" form:"organization_id"`
	StoreKey       string `validate:"required" form:"store_key"`
	Platform       string `validate:"required" form:"platform"`
	GroupName      string `validate:"required" form:"group_name"`
}

func (r *getIDsWithinUnfinishedTaskRequest) convertToArg() *task_schedule.GetIDsWithinUnfinishedTaskArg {
	return &task_schedule.GetIDsWithinUnfinishedTaskArg{
		OrganizationID: r.OrganizationID,
		StoreKey:       r.StoreKey,
		Platform:       r.Platform,
		GroupName:      consts.TaskGroupName(r.GroupName),
	}
}

type getIDsWithinUnfinishedTaskResponse struct {
	ProductListingIDs []string `json:"product_listings_ids"`
}
