package relation

import (
	"strings"
	"time"

	"github.com/AfterShip/gopkg/facility/types"

	"github.com/AfterShip/pltf-pd-product-listings/internal/consts"
	product_listing_domain "github.com/AfterShip/pltf-pd-product-listings/internal/domains/product_listing"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
)

type searchRequest struct {
	OrganizationID                    string `form:"organization_id" binding:"required"`
	SalesChannelStoreKey              string `form:"sales_channel_store_key"`
	SalesChannelPlatform              string `form:"sales_channel_platform"`
	SourceStoreKey                    string `form:"source_store_key"`
	SourcePlatform                    string `form:"source_platform"`
	SalesChannelProductID             string `form:"sales_channel_product_id"`
	SalesChannelProductIDs            string `form:"sales_channel_product_ids"`
	SalesChannelVariantID             string `form:"sales_channel_variant_id"`
	SalesChannelVariantIDs            string `form:"sales_channel_variant_ids"`
	ProductsCenterProductID           string `form:"products_center_product_id"`
	ProductsCenterVariantID           string `form:"products_center_variant_id"`
	ProductsCenterConnectorProductIDs string `form:"products_center_connector_product_ids"`
	IncludeDeleted                    bool   `form:"include_delete"`
	LinkStatus                        string `form:"link_status"`
	SyncStatus                        string `form:"sync_status"`
	Page                              int64  `form:"page" binding:"lte=1000"`
	Limit                             int64  `form:"limit" binding:"lte=1000"`
}

func (r *searchRequest) toArgs() product_listing_domain.ListRelationsArgs {
	arg := product_listing_domain.ListRelationsArgs{
		OrganizationID:          r.OrganizationID,
		SalesChannelStoreKey:    r.SalesChannelStoreKey,
		SalesChannelPlatform:    r.SalesChannelPlatform,
		SourcePlatform:          r.SourcePlatform,
		SourceStoreKey:          r.SourceStoreKey,
		SalesChannelProductID:   r.SalesChannelProductID,
		SalesChannelVariantID:   r.SalesChannelVariantID,
		ProductsCenterProductID: r.ProductsCenterProductID,
		ProductsCenterVariantID: r.ProductsCenterVariantID,
		IncludeDeleted:          r.IncludeDeleted,
		Page:                    r.Page,
		Limit:                   r.Limit,
	}

	if r.SalesChannelVariantIDs != "" {
		arg.SalesChannelVariantIDs = strings.Split(r.SalesChannelVariantIDs, ",")
	}

	if r.SalesChannelProductIDs != "" {
		arg.SalesChannelProductIDs = strings.Split(r.SalesChannelProductIDs, ",")
	}

	if r.ProductsCenterConnectorProductIDs != "" {
		arg.ProductsCenterConnectorProductIDs = strings.Split(r.ProductsCenterConnectorProductIDs, ",")
	}

	if r.LinkStatus != "" {
		arg.LinkStatus = strings.Split(r.LinkStatus, ",")
	}

	if r.SyncStatus != "" {
		arg.SyncStatus = strings.Split(r.SyncStatus, ",")
	}

	return arg
}

type relationResponse struct {
	ID                      string                 `json:"id"`
	Organization            models.Organization    `json:"organization"`
	SalesChannel            models.SalesChannel    `json:"sales_channel"`
	ProductListingID        string                 `json:"product_listing_id"`
	VariantPosition         int                    `json:"variant_position"`
	ProductListingVariantID string                 `json:"product_listing_variant_id"`
	SalesChannelVariant     salesChannelVariant    `json:"sales_channel_variant"`
	ProductsCenterVariant   productsCenterVariant  `json:"products_center_variant"`
	SyncStatus              consts.SyncStatus      `json:"sync_status"`
	LinkStatus              consts.LinkStatus      `json:"link_status"`
	AllowSync               consts.AllowSyncStatus `json:"allow_sync"`
	LastSyncedAt            types.Datetime         `json:"last_synced_at"`
	LastLinkedAt            types.Datetime         `json:"last_linked_at"`
	DeletedAt               types.Datetime         `json:"deleted_at"`
	CreatedAt               time.Time              `json:"created_at"`
	UpdatedAt               time.Time              `json:"updated_at"`
}

type salesChannelVariant struct {
	ID                 string `json:"id"`
	ConnectorProductID string `json:"connector_product_id"`
	ProductID          string `json:"product_id"`
	Sku                string `json:"sku"`
}

type productsCenterVariant struct {
	ID                 string                      `json:"id"`
	ProductID          string                      `json:"product_id"`
	ConnectorProductID string                      `json:"connector_product_id"`
	Source             productsCenterVariantSource `json:"source"`
}

type productsCenterVariantSource struct {
	StoreKey  string `json:"store_key"`
	Platform  string `json:"platform"`
	ID        string `json:"id"`
	ProductID string `json:"product_id"`
	Sku       string `json:"sku"`
}

type searchResponse struct {
	Relations       []relationResponse `json:"relations"`
	Paginator       *models.Paginator  `json:"paginator"`
	ParameterString string             `json:"parameter_string"`
}

type salesChannelOrderVariantRelationRequest struct {
	OrganizationID        string `form:"organization_id" binding:"required"`
	SalesChannelStoreKey  string `form:"sales_channel_store_key" binding:"required"`
	SalesChannelPlatform  string `form:"sales_channel_platform" binding:"required"`
	SourceStoreKey        string `form:"source_store_key" binding:"required"`
	SourcePlatform        string `form:"source_platform" binding:"required"`
	SalesChannelProductID string `form:"sales_channel_product_id" binding:"required"`
	SalesChannelVariantID string `form:"sales_channel_variant_id" binding:"required"`
}

func (r *salesChannelOrderVariantRelationRequest) toArg() product_listing_domain.SalesChannelOrderVariantRelationArg {
	return product_listing_domain.SalesChannelOrderVariantRelationArg{
		OrganizationID:        r.OrganizationID,
		SalesChannelStoreKey:  r.SalesChannelStoreKey,
		SalesChannelPlatform:  r.SalesChannelPlatform,
		SourceStoreKey:        r.SourceStoreKey,
		SourcePlatform:        r.SourcePlatform,
		SalesChannelProductID: r.SalesChannelProductID,
		SalesChannelVariantID: r.SalesChannelVariantID,
	}
}
