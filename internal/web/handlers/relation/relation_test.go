package relation

import (
	"context"
	"errors"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/spf13/viper"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"github.com/tidwall/gjson"

	"github.com/AfterShip/gopkg/cfg"
	"github.com/AfterShip/gopkg/facility/types"
	"github.com/AfterShip/gopkg/uuid"

	"github.com/AfterShip/pltf-pd-product-listings/internal/config"
	"github.com/AfterShip/pltf-pd-product-listings/internal/consts"
	"github.com/AfterShip/pltf-pd-product-listings/internal/datastore"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/product_listing"
	"github.com/AfterShip/pltf-pd-product-listings/internal/logger"
)

func TestRelation_search(t *testing.T) {
	configs := new(config.Config)

	_, err := cfg.LoadViperConfig(configs, func(v *viper.Viper) { v.AddConfigPath("../../../../cmd/apiserver/conf") })
	require.NoError(t, err)
	configs.DynamicConfigs.ElasticsearchAuth = &config.ElasticsearchAuthConfig{
		Host: "http://localhost:9200",
	}
	require.NoError(t, datastore.Init(configs))

	mockSvc := new(product_listing.MockProductListingService)

	testcases := []struct {
		name      string
		path      string
		mock      func()
		expCode   int
		respCheck func(*testing.T, string)
	}{
		{
			name:    "Organization_id is required",
			path:    "/relations?page=1",
			expCode: http.StatusUnprocessableEntity,
		},
		{
			name:    "service error",
			path:    "/relations?organization_id=1&page=1",
			expCode: http.StatusInternalServerError,
			mock: func() {
				mockSvc.On("ListRelations", mock.Anything, mock.Anything).Return([]*product_listing.ProductListingRelation{}, errors.New("not found")).Once()
			},
		},
		{
			name: "Good case",
			path: "/relations?organization_id=1&page=1",
			mock: func() {
				mockSvc.On("ListRelations", mock.Anything, mock.Anything).Return([]*product_listing.ProductListingRelation{}, nil).Once()
			},
			expCode: http.StatusOK,
			respCheck: func(t *testing.T, resp string) {
				require.NotEmpty(t, gjson.Parse(resp).Get("data").String())
			},
		},
	}
	for _, tt := range testcases {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mock != nil {
				tt.mock()
			}
			w := httptest.NewRecorder()
			gin.SetMode(gin.TestMode)
			_, eng := gin.CreateTestContext(w)

			NewRelation(logger.Get(), mockSvc).RegisterRoutes(eng.RouterGroup.Group(""))

			req, _ := http.NewRequestWithContext(context.Background(), http.MethodGet, tt.path, nil)
			eng.ServeHTTP(w, req)
			resp := w.Body.String()
			require.Equal(t, tt.expCode, w.Code, resp)
			if tt.respCheck != nil {
				tt.respCheck(t, resp)
			}
		})
	}
}

func TestRelation_ConvertToRelationResponse(t *testing.T) {
	relation := generateProductListingRelation()
	relationResp := convertToRelationResponse([]*product_listing.ProductListingRelation{&relation})
	require.Len(t, relationResp, 1)
	compareRelation(t, &relationResp[0], &relation)
}

func TestRelation_toArgs(t *testing.T) {
	req := searchRequest{
		OrganizationID:          uuid.GenerateUUIDV4(),
		SalesChannelStoreKey:    uuid.GenerateUUIDV4(),
		SalesChannelPlatform:    uuid.GenerateUUIDV4(),
		SalesChannelProductID:   uuid.GenerateUUIDV4(),
		SalesChannelVariantID:   uuid.GenerateUUIDV4(),
		ProductsCenterProductID: uuid.GenerateUUIDV4(),
		ProductsCenterVariantID: uuid.GenerateUUIDV4(),
		Page:                    1,
		Limit:                   10,
	}
	args := req.toArgs()
	require.Equal(t, req.OrganizationID, args.OrganizationID)
	require.Equal(t, req.Page, args.Page)
	require.Equal(t, req.Limit, args.Limit)
	require.Equal(t, req.SalesChannelStoreKey, args.SalesChannelStoreKey)
	require.Equal(t, req.SalesChannelPlatform, args.SalesChannelPlatform)
	require.Equal(t, req.SalesChannelProductID, args.SalesChannelProductID)
	require.Equal(t, req.SalesChannelVariantID, args.SalesChannelVariantID)
	require.Equal(t, req.ProductsCenterProductID, args.ProductsCenterProductID)
	require.Equal(t, req.ProductsCenterVariantID, args.ProductsCenterVariantID)
}

func compareRelation(t *testing.T, expected *relationResponse, actual *product_listing.ProductListingRelation) {
	require.Equal(t, expected.ID, actual.ID)
	require.Equal(t, expected.VariantPosition, actual.VariantPosition)
	require.Equal(t, expected.ProductListingVariantID, actual.ProductListingVariantID)
	require.Equal(t, expected.SyncStatus, actual.SyncStatus)
	require.Equal(t, expected.LinkStatus, actual.LinkStatus)
	require.Equal(t, expected.AllowSync, actual.AllowSync)
	require.Equal(t, expected.LastSyncedAt, actual.LastSyncedAt)
	require.Equal(t, expected.LastLinkedAt, actual.LastLinkedAt)
	require.Equal(t, expected.DeletedAt, actual.DeletedAt)
	require.Equal(t, expected.CreatedAt, actual.CreatedAt)
	require.Equal(t, expected.UpdatedAt, actual.UpdatedAt)
	require.Equal(t, expected.SalesChannelVariant.ID, actual.SalesChannelVariant.ID)
	require.Equal(t, expected.SalesChannelVariant.ConnectorProductID, actual.SalesChannelVariant.ConnectorProductID)
	require.Equal(t, expected.SalesChannelVariant.ProductID, actual.SalesChannelVariant.ProductID)
	require.Equal(t, expected.SalesChannelVariant.Sku, actual.SalesChannelVariant.Sku)
	require.Equal(t, expected.ProductsCenterVariant.ID, actual.ProductsCenterVariant.ID)
	require.Equal(t, expected.ProductsCenterVariant.ProductID, actual.ProductsCenterVariant.ProductID)
	require.Equal(t, expected.ProductsCenterVariant.ConnectorProductID, actual.ProductsCenterVariant.ConnectorProductID)
	require.Equal(t, expected.ProductsCenterVariant.Source.StoreKey, actual.ProductsCenterVariant.Source.StoreKey)
	require.Equal(t, expected.ProductsCenterVariant.Source.Platform, actual.ProductsCenterVariant.Source.Platform)
	require.Equal(t, expected.ProductsCenterVariant.Source.ID, actual.ProductsCenterVariant.Source.ID)
	require.Equal(t, expected.ProductsCenterVariant.Source.ProductID, actual.ProductsCenterVariant.Source.ProductID)
	require.Equal(t, expected.ProductsCenterVariant.Source.Sku, actual.ProductsCenterVariant.Source.Sku)
}

func generateProductListingRelation() product_listing.ProductListingRelation {
	return product_listing.ProductListingRelation{
		ID:                      uuid.GenerateUUIDV4(),
		ProductListingVariantID: uuid.GenerateUUIDV4(),
		SalesChannelVariant: product_listing.SalesChannelVariant{
			ID:                 uuid.GenerateUUIDV4(),
			ConnectorProductID: uuid.GenerateUUIDV4(),
			ProductID:          uuid.GenerateUUIDV4(),
			Sku:                "sku",
		},
		ProductsCenterVariant: product_listing.ProductsCenterVariant{
			ID:                 uuid.GenerateUUIDV4(),
			ProductID:          uuid.GenerateUUIDV4(),
			ConnectorProductID: uuid.GenerateUUIDV4(),
			Source: product_listing.ProductsCenterVariantSource{
				StoreKey: "store_key",
				Platform: "platform",
				ID:       uuid.GenerateUUIDV4(),
			},
		},
		SyncStatus:   consts.SyncStatusSynced,
		LinkStatus:   consts.LinkStatusUnlink,
		AllowSync:    consts.AllowSyncEnabled,
		LastLinkedAt: types.MakeDatetime(time.Now()),
		LastSyncedAt: types.MakeDatetime(time.Now()),
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
		DeletedAt:    types.MakeDatetime(time.Now()),
	}
}

func TestRelation_salesChannelOrderVariantRelation(t *testing.T) {
	configs := new(config.Config)

	_, err := cfg.LoadViperConfig(configs, func(v *viper.Viper) { v.AddConfigPath("../../../../cmd/apiserver/conf") })
	require.NoError(t, err)
	configs.DynamicConfigs.ElasticsearchAuth = &config.ElasticsearchAuthConfig{
		Host: "http://localhost:9200",
	}
	require.NoError(t, datastore.Init(configs))

	mockSvc := new(product_listing.MockProductListingService)

	testcases := []struct {
		name      string
		path      string
		mock      func()
		expCode   int
		respCheck func(*testing.T, string)
	}{
		{
			name:    "缺少必需的organization_id参数",
			path:    "/relations/sales-channel-order-variant?sales_channel_store_key=store123&order_variant_id=variant123",
			expCode: http.StatusUnprocessableEntity,
		},
		{
			name:    "缺少必需的sales_channel_store_key参数",
			path:    "/relations/sales-channel-order-variant?organization_id=org123&order_variant_id=variant123",
			expCode: http.StatusUnprocessableEntity,
		},
		{
			name:    "缺少必需的order_variant_id参数",
			path:    "/relations/sales-channel-order-variant?organization_id=org123&sales_channel_store_key=store123",
			expCode: http.StatusUnprocessableEntity,
		},
		{
			name:    "服务层错误",
			path:    "/relations/sales-channel-order-variant?organization_id=org123&sales_channel_store_key=store123&sales_channel_platform=platform123&source_store_key=sourceStore123&source_platform=sourcePlatform123&sales_channel_product_id=product123&sales_channel_variant_id=variant123",
			expCode: http.StatusInternalServerError,
			mock: func() {
				mockSvc.On("SalesChannelOrderVariantRelation", mock.Anything, mock.Anything).Return(product_listing.ProductListingRelation{}, errors.New("service error")).Once()
			},
		},
		{
			name: "成功获取关系",
			path: "/relations/sales-channel-order-variant?organization_id=org123&sales_channel_store_key=store123&sales_channel_platform=platform123&source_store_key=sourceStore123&source_platform=sourcePlatform123&sales_channel_product_id=product123&sales_channel_variant_id=variant123",
			mock: func() {
				relation := generateProductListingRelation()
				mockSvc.On("SalesChannelOrderVariantRelation", mock.Anything, mock.Anything).Return(relation, nil).Once()
			},
			expCode: http.StatusOK,
			respCheck: func(t *testing.T, resp string) {
				require.NotEmpty(t, gjson.Parse(resp).Get("data").String())
				require.NotEmpty(t, gjson.Parse(resp).Get("data.id").String())
			},
		},
		{
			name:    "空字符串参数",
			path:    "/relations/sales-channel-order-variant?organization_id=&sales_channel_store_key=store123&order_variant_id=variant123",
			expCode: http.StatusUnprocessableEntity,
		},
	}

	for _, tt := range testcases {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mock != nil {
				tt.mock()
			}
			w := httptest.NewRecorder()
			gin.SetMode(gin.TestMode)
			_, eng := gin.CreateTestContext(w)

			NewRelation(logger.Get(), mockSvc).RegisterRoutes(eng.RouterGroup.Group(""))

			req, _ := http.NewRequestWithContext(context.Background(), http.MethodGet, tt.path, nil)
			eng.ServeHTTP(w, req)
			resp := w.Body.String()
			require.Equal(t, tt.expCode, w.Code, resp)
			if tt.respCheck != nil {
				tt.respCheck(t, resp)
			}
		})
	}
}
