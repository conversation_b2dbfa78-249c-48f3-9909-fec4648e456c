package relation

import (
	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"go.uber.org/zap"

	"github.com/AfterShip/connectors-library/gin/responder"
	"github.com/AfterShip/gopkg/log"

	product_listing_domain "github.com/AfterShip/pltf-pd-product-listings/internal/domains/product_listing"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
)

type Relation struct {
	logger    *log.Logger
	service   product_listing_domain.Service
	responder *responder.Responder
	validator *validator.Validate
}

func NewRelation(logger *log.Logger, service product_listing_domain.Service) *Relation {
	return &Relation{
		logger:    logger,
		service:   service,
		responder: responder.NewResponder(false, models.CommonErrorsMapping),
		validator: validator.New(),
	}
}

func (p *Relation) RegisterRoutes(router *gin.RouterGroup) {
	router.GET("/relations", p.search)
	router.GET("/relations/sales-channel-order-variant", p.salesChannelOrderVariantRelation)
}

func (p *Relation) search(c *gin.Context) {
	request := searchRequest{
		Page:  1,
		Limit: 10,
	}
	if err := c.ShouldBindQuery(&request); err != nil {
		p.responder.ResponseWithErrorCode(c, models.CodeMissRequiredQueryParam, err)
		return
	}

	args := request.toArgs()
	relations, err := p.service.ListRelations(c, &args)
	if err != nil {
		p.logger.With().ErrorCtx(c, "failed to list relations", zap.Error(err))
		p.responder.ResponseWithError(c, err)
		return
	}

	resp := searchResponse{
		Relations:       convertToRelationResponse(relations),
		ParameterString: c.Request.URL.RawQuery,
		Paginator: &models.Paginator{
			Page:        request.Page,
			Limit:       request.Limit,
			HasNextPage: len(relations) == int(request.Limit),
		},
	}

	p.responder.ResponseWithOK(c, resp)
}

func (p *Relation) salesChannelOrderVariantRelation(c *gin.Context) {
	request := salesChannelOrderVariantRelationRequest{}

	if err := c.ShouldBindQuery(&request); err != nil {
		p.responder.ResponseWithErrorCode(c, models.CodeMissRequiredQueryParam, err)
		return
	}

	if err := p.validator.Struct(request); err != nil {
		p.responder.ResponseWithErrorCode(c, models.CodeMissRequiredQueryParam, err)
		return
	}

	arg := request.toArg()
	domainRelation, err := p.service.SalesChannelOrderVariantRelation(c, &arg)
	if err != nil {
		p.logger.With().InfoCtx(c, "failed to get order variant relation", zap.Error(err))
		p.responder.ResponseWithError(c, err)
		return
	}

	p.responder.ResponseWithOK(c, convertToSignalRelationResponse(&domainRelation))
}
