package relation

import (
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/product_listing"
)

func convertToSignalRelationResponse(relation *product_listing.ProductListingRelation) relationResponse {
	r := relationResponse{}
	r.ID = relation.ID
	r.Organization = relation.Organization
	r.SalesChannel = relation.SalesChannel
	r.ProductListingID = relation.ProductListingID
	r.VariantPosition = relation.VariantPosition
	r.ProductListingVariantID = relation.ProductListingVariantID
	r.SyncStatus = relation.SyncStatus
	r.LinkStatus = relation.LinkStatus
	r.AllowSync = relation.AllowSync
	r.LastSyncedAt = relation.LastSyncedAt
	r.LastLinkedAt = relation.LastLinkedAt
	r.DeletedAt = relation.DeletedAt
	r.CreatedAt = relation.CreatedAt
	r.UpdatedAt = relation.UpdatedAt
	r.SalesChannelVariant = convertToSalesChannelVariant(&relation.SalesChannelVariant)
	r.ProductsCenterVariant = convertToProductsCenterVariant(&relation.ProductsCenterVariant)
	return r
}

func convertToRelationResponse(relations []*product_listing.ProductListingRelation) []relationResponse {
	response := make([]relationResponse, 0, len(relations))
	for _, relation := range relations {
		response = append(response, convertToSignalRelationResponse(relation))
	}
	return response
}

func convertToSalesChannelVariant(v *product_listing.SalesChannelVariant) salesChannelVariant {
	return salesChannelVariant{
		ID:                 v.ID,
		ConnectorProductID: v.ConnectorProductID,
		ProductID:          v.ProductID,
		Sku:                v.Sku,
	}
}

func convertToProductsCenterVariant(v *product_listing.ProductsCenterVariant) productsCenterVariant {
	return productsCenterVariant{
		ID:                 v.ID,
		ProductID:          v.ProductID,
		ConnectorProductID: v.ConnectorProductID,
		Source: productsCenterVariantSource{
			StoreKey:  v.Source.StoreKey,
			Platform:  v.Source.Platform,
			ID:        v.Source.ID,
			ProductID: v.Source.ProductID,
			Sku:       v.Source.Sku,
		},
	}
}
