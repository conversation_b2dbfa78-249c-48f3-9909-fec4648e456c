package categories

import (
	"context"
	"errors"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"github.com/tidwall/gjson"

	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/category"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/product_category"
)

func Test_getProductAttributes(t *testing.T) {
	mockService := &product_category.MockService{}

	tests := []struct {
		name      string
		path      string
		mock      func()
		expCode   int
		respCheck func(*testing.T, string)
	}{
		// TODO: Add test cases.
		{
			name: "Case 1: Success",
			path: "/categories/product-attributes?organization_id=3ae8df847ca34499b22653ee1581d2a3&sales_channel_platform=tiktok-shop&sales_channel_store_key=8106192175294&is_required=true&product_listings_query=organization_id%3D3ae8df847ca34499b22653ee1581d2a3%26limit%3D100%26sales_channel_platform%3Dtiktok-shop%26sales_channel_store_key%3D8106192175294%26state%3Dpending%26query%3DMens+T-shirt",
			mock: func() {
				mockService.On("GetProductCategoryAttributes", mock.Anything, mock.Anything).Return([]category.Attribute{
					{
						ID:   "1",
						Name: "Material",
						Values: []category.AttributeValue{
							{
								ID:   "1",
								Name: "Cotton",
							},
							{
								ID:   "2",
								Name: "Polyester",
							},
						},
					},
				}, nil).Once()
			},
			expCode: 200,
			respCheck: func(t *testing.T, resp string) {
				require.NotEmpty(t, gjson.Parse(resp).Get("data").String())
			},
		},
		{
			name:      "Case 2: Missing required query param",
			path:      "/categories/product-attributes",
			mock:      nil,
			expCode:   422,
			respCheck: func(t *testing.T, resp string) {},
		},
		{
			name: "Case 3: Error",
			path: "/categories/product-attributes?organization_id=3ae8df847ca34499b22653ee1581d2a3&sales_channel_platform=tiktok-shop&sales_channel_store_key=8106192175294&is_required=true&product_listings_query=organization_id%3D3ae8df847ca34499b22653ee1581d2a3%26limit%3D100%26sales_channel_platform%3Dtiktok-shop%26sales_channel_store_key%3D8106192175294%26state%3Dpending%26query%3DMens+T-shirt",
			mock: func() {
				mockService.On("GetProductCategoryAttributes", mock.Anything, mock.Anything).Return(nil, errors.New("failed")).Once()
			},
			expCode:   500,
			respCheck: func(t *testing.T, resp string) {},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mock != nil {
				tt.mock()
			}
			w := httptest.NewRecorder()
			gin.SetMode(gin.TestMode)
			_, eng := gin.CreateTestContext(w)
			New(nil, nil, mockService).RegisterRoutes(eng.RouterGroup.Group(""))

			req, _ := http.NewRequestWithContext(context.Background(), http.MethodGet, tt.path, nil)
			eng.ServeHTTP(w, req)
			resp := w.Body.String()
			require.Equal(t, tt.expCode, w.Code, resp)
			if tt.respCheck != nil {
				tt.respCheck(t, resp)
			}
		})
	}
}
