package categories

import (
	"github.com/AfterShip/connectors-library/gin/responder"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/category"
	tiktokapi "github.com/AfterShip/pltf-pd-product-listings/internal/third_party/tiktok_api"
)

const (
	categoryVersionNotMatch responder.MetaCode = 42205
	categoryNoPermission    responder.MetaCode = 42212
	categoryNotFound        responder.MetaCode = 40401
	sellerInactive          responder.MetaCode = 42213
)

var errorResponseMapping = map[error]responder.Meta{
	tiktokapi.ErrCategoryVersionNotMatch: {
		Code: categoryVersionNotMatch,
		Description: responder.Description{
			Type:    "CategoryVersionNotMatch",
			Message: "category version not match",
		},
	},
	tiktokapi.ErrCategoryInviteOnly: {
		Code: categoryNoPermission,
		Description: responder.Description{
			Type:    "CategoryNoPermission",
			Message: "no permission to access category",
		},
	},
	category.ErrCategoryNotFound: {
		Code: categoryNotFound,
		Description: responder.Description{
			Type:    "CategoryNotFound",
			Message: "category not found",
		},
	},
	tiktokapi.ErrSellerInactive: {
		Code: sellerInactive,
		Description: responder.Description{
			Type:    "SellerInactive",
			Message: "seller is inactive",
		},
	},
}
