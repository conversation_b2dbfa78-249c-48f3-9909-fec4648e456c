package categories

import (
	"strings"

	"github.com/AfterShip/pltf-pd-product-listings/internal/logger"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"go.uber.org/zap"

	"github.com/AfterShip/connectors-library/gin/responder"
	"github.com/AfterShip/gopkg/log"
	"github.com/AfterShip/pltf-pd-product-listings/internal/consts"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/category"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/product_category"
	"github.com/AfterShip/pltf-pd-product-listings/internal/models"
)

type Category struct {
	logger                 *log.Logger
	categoryService        category.Service
	productCategoryService product_category.Service
	responder              *responder.Responder
	validator              *validator.Validate
}

func New(logger *log.Logger, categoryService category.Service, productCategoryService product_category.Service) *Category {
	h := &Category{
		logger:                 logger,
		categoryService:        categoryService,
		productCategoryService: productCategoryService,
		responder:              responder.NewResponder(true, errorResponseMapping),
		validator:              validator.New(),
	}
	h.responder.RegisterDomainError(errorResponseMapping)
	return h
}

func (h *Category) RegisterRoutes(router *gin.RouterGroup) {
	apiRouter := router.Group("/categories")

	apiRouter.GET("", h.list)
	apiRouter.GET("/versions", h.versions)
	apiRouter.GET("/rules", h.rules)
	apiRouter.GET("/attributes", h.attributes)
	apiRouter.GET("/product-attributes", h.productAttributes)
	apiRouter.GET("/product-rules", h.productRules)

	// mock 数据用
	apiRouter.POST("/test-rules", h.testRules)
	apiRouter.POST("/test-attributes", h.testAttributes)
}

func (h *Category) testRules(c *gin.Context) {
	ctx := c.Request.Context()

	req := category.RulesOutput{}
	if err := c.ShouldBindJSON(&req); err != nil {
		h.responder.ResponseWithErrorCode(c, models.CodeMissRequiredQueryParam, err)
		return
	}

	categoryRules, err := h.categoryService.PostCategoryRules(ctx, &req)
	if err != nil {
		h.responder.ResponseWithError(c, err)
		return
	}

	h.responder.ResponseWithOK(c, categoryRules)
}

func (h *Category) testAttributes(c *gin.Context) {
	ctx := c.Request.Context()

	req := category.AttributesOutput{}
	if err := c.ShouldBindJSON(&req); err != nil {
		h.responder.ResponseWithErrorCode(c, models.CodeMissRequiredQueryParam, err)
		return
	}

	categoryAttributes, err := h.categoryService.PostCategoryAttributes(ctx, &req)
	if err != nil {
		h.responder.ResponseWithError(c, err)
		return
	}

	h.responder.ResponseWithOK(c, categoryAttributes)
}

func (h *Category) list(c *gin.Context) {
	ctx := c.Request.Context()

	req := listRequest{}
	if err := c.ShouldBindQuery(&req); err != nil {
		h.responder.ResponseWithErrorCode(c, models.CodeMissRequiredQueryParam, err)
		return
	}

	categoryList, err := h.categoryService.List(ctx, &category.ListArg{
		OrganizationID:       req.OrganizationID,
		SalesChannelPlatform: req.SalesChannelPlatform,
		SalesChannelStoreKey: req.SalesChannelStoreKey,
		RefreshCache:         req.RefreshCache,
	})
	if err != nil {
		h.responder.ResponseWithError(c, err)
		return
	}

	h.responder.ResponseWithOK(c, categoryList)
}

func (h *Category) versions(c *gin.Context) {
	ctx := c.Request.Context()

	req := getVersionsRequest{}
	if err := c.ShouldBindQuery(&req); err != nil {
		h.responder.ResponseWithErrorCode(c, models.CodeMissRequiredQueryParam, err)
		return
	}

	versionsOutput, err := h.categoryService.Versions(ctx, &category.CategoryVersionsArg{
		OrganizationID:          req.OrganizationID,
		SalesChannelPlatform:    req.SalesChannelPlatform,
		SalesChannelStoreKey:    req.SalesChannelStoreKey,
		SalesChannelCategoryIDs: strings.Split(req.CategoryIDs, ","),
		RefreshCache:            req.RefreshCache,
	})
	if err != nil {
		h.responder.ResponseWithError(c, err)
		return
	}

	h.responder.ResponseWithOK(c, versionsOutput)
}

// nolint:dupl
func (h *Category) rules(c *gin.Context) {
	ctx := c.Request.Context()

	req := getRulesRequest{}
	req.CategoryVersion = consts.CategoryVersionV1 // default v1
	if err := c.ShouldBindQuery(&req); err != nil {
		h.responder.ResponseWithErrorCode(c, models.CodeMissRequiredQueryParam, err)
		return
	}

	categoryRules, err := h.categoryService.GetCategoryRules(ctx, req.convertToGetRulesArg())
	if err != nil {
		h.responder.ResponseWithError(c, err)
		return
	}

	h.responder.ResponseWithOK(c, categoryRules)
}

// nolint:dupl
func (h *Category) attributes(c *gin.Context) {
	ctx := c.Request.Context()

	req := getAttributesRequest{}
	req.CategoryVersion = consts.CategoryVersionV1 // default v1
	if err := c.ShouldBindQuery(&req); err != nil {
		h.responder.ResponseWithErrorCode(c, models.CodeMissRequiredQueryParam, err)
		return
	}

	categoryAttributes, err := h.categoryService.GetCategoryAttributes(ctx, req.convertToGetAttributesArg())

	if err != nil {
		h.responder.ResponseWithError(c, err)
		return
	}

	h.responder.ResponseWithOK(c, categoryAttributes)
}

// nolint:dupl
func (h *Category) productAttributes(c *gin.Context) {
	ctx := c.Request.Context()

	req := getProductAttributesRequest{}
	if err := c.ShouldBindQuery(&req); err != nil {
		h.responder.ResponseWithErrorCode(c, models.CodeMissRequiredQueryParam, err)
		return
	}

	ctx = log.AppendFieldsToContext(ctx, zap.Any("params", req))

	args, err := req.convertToToGetProductCategoryAttributesArgs()
	if err != nil {
		h.responder.ResponseWithErrorCode(c, models.CodeMissRequiredQueryParam, err)
		return
	}

	productAttributes, err := h.productCategoryService.GetProductCategoryAttributes(ctx, args)
	if err != nil {
		logger.Get().WarnCtx(ctx, "GetProductCategoryAttributes error", zap.Error(err))
		h.responder.ResponseWithError(c, err)
		return
	}

	h.responder.ResponseWithOK(c, getProductAttributesResponse{
		Attributes:      productAttributes,
		ParameterString: c.Request.URL.RawQuery,
	})
}

// nolint:dupl
func (h *Category) productRules(c *gin.Context) {
	ctx := c.Request.Context()

	req := getProductRulesRequest{}
	if err := c.ShouldBindQuery(&req); err != nil {
		h.responder.ResponseWithErrorCode(c, models.CodeMissRequiredQueryParam, err)
		return
	}

	args, err := req.convertToToGetProductCategoryRulesArgs()
	if err != nil {
		h.responder.ResponseWithErrorCode(c, models.CodeMissRequiredQueryParam, err)
		return
	}

	rules, err := h.productCategoryService.GetProductCategoryRules(ctx, args)
	if err != nil {
		h.responder.ResponseWithError(c, err)
		return
	}

	h.responder.ResponseWithOK(c, getProductRulesResponse{
		Rules:           rules,
		ParameterString: c.Request.URL.RawQuery,
	})
}
