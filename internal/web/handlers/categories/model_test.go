package categories

import (
	"testing"

	"github.com/stretchr/testify/require"

	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/product_category"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/product_listing"
)

func Test__convertToToGetProductCategoryAttributesArgs(t *testing.T) {
	type fields struct {
		OrganizationID       string
		SalesChannelStoreKey string
		SalesChannelPlatform string
		ProductListingsQuery string
		IsRequired           string
	}
	tests := []struct {
		name    string
		fields  fields
		want    *product_category.GetProductCategoryAttributesArgs
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "Success",
			fields: fields{
				OrganizationID:       "3ae8df847ca34499b22653ee1581d2a3",
				SalesChannelStoreKey: "8106192175294",
				SalesChannelPlatform: "tiktok-shop",
				ProductListingsQuery: "organization_id=xxx&limit=100&sales_channel_platform=tiktok-shop&sales_channel_store_key=xxx&state=pending&query=Mens+T-shirt",
				IsRequired:           "true",
			},
			want: &product_category.GetProductCategoryAttributesArgs{
				OrganizationID:       "3ae8df847ca34499b22653ee1581d2a3",
				SalesChannelStoreKey: "8106192175294",
				SalesChannelPlatform: "tiktok-shop",
				ProductListingsQuery: &product_listing.SearchProductListingArgs{
					OrganizationID:           "3ae8df847ca34499b22653ee1581d2a3",
					SalesChannelStoreKey:     "8106192175294",
					SalesChannelPlatform:     "tiktok-shop",
					States:                   []string{"pending"},
					PredefinedFilters:        nil,
					InventoryFilters:         "",
					Categories:               nil,
					ProductsCenterProductIds: nil,
					FeedCategoryTemplateID:   "",
					SalesChannelProductIds:   nil,
					ProductListingIDs:        nil,
					Query:                    "Mens T-shirt",
					Limit:                    100,
					Page:                     0,
					Cursor:                   "",
				},
				IsRequired: "true",
			},
			wantErr: false,
		},
		{
			name: "Invalid Query",
			fields: fields{
				OrganizationID:       "3ae8df847ca34499b22653ee1581d2a3",
				SalesChannelStoreKey: "8106192175294",
				SalesChannelPlatform: "tiktok-shop",
				ProductListingsQuery: "",
				IsRequired:           "true",
			},
			want: &product_category.GetProductCategoryAttributesArgs{
				OrganizationID:       "3ae8df847ca34499b22653ee1581d2a3",
				SalesChannelStoreKey: "8106192175294",
				SalesChannelPlatform: "tiktok-shop",
				ProductListingsQuery: &product_listing.SearchProductListingArgs{
					OrganizationID:       "3ae8df847ca34499b22653ee1581d2a3",
					SalesChannelStoreKey: "8106192175294",
					SalesChannelPlatform: "tiktok-shop",
					Limit:                10,
				},
				IsRequired: "true",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			r := &getProductAttributesRequest{
				OrganizationID:       tt.fields.OrganizationID,
				SalesChannelStoreKey: tt.fields.SalesChannelStoreKey,
				SalesChannelPlatform: tt.fields.SalesChannelPlatform,
				ProductListingsQuery: tt.fields.ProductListingsQuery,
				IsRequired:           tt.fields.IsRequired,
			}
			got, err := r.convertToToGetProductCategoryAttributesArgs()
			if (err != nil) != tt.wantErr {
				t.Errorf("convertToToGetProductAttributesArgs() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			require.Equal(t, tt.want, got)

		})
	}
}
