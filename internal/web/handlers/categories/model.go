package categories

import (
	"net/http"
	"net/url"

	"github.com/gin-gonic/gin/binding"

	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/category"
	"github.com/AfterShip/pltf-pd-product-listings/internal/domains/product_category"
	product_listing_handler "github.com/AfterShip/pltf-pd-product-listings/internal/web/handlers/product_listing"

	"strings"
)

type listRequest struct {
	OrganizationID       string `form:"organization_id" binding:"required"`
	SalesChannelPlatform string `form:"sales_channel_platform" binding:"required"`
	SalesChannelStoreKey string `form:"sales_channel_store_key" binding:"required"`
	RefreshCache         bool   `form:"refresh_cache"`
}

type getVersionsRequest struct {
	OrganizationID       string `form:"organization_id" binding:"required"`
	SalesChannelPlatform string `form:"sales_channel_platform" binding:"required"`
	SalesChannelStoreKey string `form:"sales_channel_store_key" binding:"required"`
	CategoryIDs          string `form:"category_ids" binding:"required"`
	RefreshCache         bool   `form:"refresh_cache"`
}

type getRulesRequest struct {
	OrganizationID       string `form:"organization_id" binding:"required"`
	SalesChannelPlatform string `form:"sales_channel_platform" binding:"required"`
	SalesChannelStoreKey string `form:"sales_channel_store_key" binding:"required"`
	ExternalCategoryID   string `form:"external_category_id" binding:"required"`
	CategoryVersion      string `form:"category_version" binding:"omitempty,oneof=v1 v2"`
	RefreshCache         bool   `form:"refresh_cache"`
	IsRequired           string `form:"is_required" binding:"omitempty,oneof=true false"`
}

func (req *getRulesRequest) convertToGetRulesArg() *category.GetRulesArg {
	return &category.GetRulesArg{
		OrganizationID:       req.OrganizationID,
		SalesChannelStoreKey: req.SalesChannelStoreKey,
		SalesChannelPlatform: req.SalesChannelPlatform,
		ExternalCategoryID:   req.ExternalCategoryID,
		IsRequired:           req.IsRequired == "true",
		CategoryVersion:      req.CategoryVersion,
		RefreshCache:         req.RefreshCache,
	}
}

type getAttributesRequest struct {
	OrganizationID       string `form:"organization_id" binding:"required"`
	SalesChannelPlatform string `form:"sales_channel_platform" binding:"required"`
	SalesChannelStoreKey string `form:"sales_channel_store_key" binding:"required"`
	ExternalCategoryID   string `form:"external_category_id" binding:"required"`
	CategoryVersion      string `form:"category_version" binding:"omitempty,oneof=v1 v2"`
	RefreshCache         bool   `form:"refresh_cache"`
	IsRequired           string `form:"is_required" binding:"omitempty,oneof=true false"`
	Types                string `form:"types"`
}

func (req *getAttributesRequest) convertToGetAttributesArg() *category.GetAttributesArg {
	arg := &category.GetAttributesArg{
		OrganizationID:       req.OrganizationID,
		SalesChannelStoreKey: req.SalesChannelStoreKey,
		SalesChannelPlatform: req.SalesChannelPlatform,
		ExternalCategoryID:   req.ExternalCategoryID,
		IsRequired:           req.IsRequired == "true",
		CategoryVersion:      req.CategoryVersion,
		RefreshCache:         req.RefreshCache,
	}
	if req.Types != "" {
		arg.Types = strings.Split(req.Types, ",")
	}
	return arg
}

type getProductAttributesRequest struct {
	OrganizationID       string `form:"organization_id" binding:"required"`
	SalesChannelStoreKey string `form:"sales_channel_store_key" binding:"required"`
	SalesChannelPlatform string `form:"sales_channel_platform" binding:"required"`
	ProductListingsQuery string `form:"product_listings_query" binding:"required"`
	IsRequired           string `form:"is_required" binding:"omitempty,oneof=true false"`
	Types                string `form:"types"`
}

func (r *getProductAttributesRequest) convertToToGetProductCategoryAttributesArgs() (*product_category.GetProductCategoryAttributesArgs, error) {
	searchProductListingRequest := new(product_listing_handler.SearchProductListingRequest)
	fakeRequest := &http.Request{URL: &url.URL{RawQuery: r.ProductListingsQuery}}
	if err := binding.Query.Bind(fakeRequest, searchProductListingRequest); err != nil {
		return nil, err
	}

	result := &product_category.GetProductCategoryAttributesArgs{
		OrganizationID:       r.OrganizationID,
		SalesChannelStoreKey: r.SalesChannelStoreKey,
		SalesChannelPlatform: r.SalesChannelPlatform,
		ProductListingsQuery: searchProductListingRequest.ToSearchProductListingArgs(),
		IsRequired:           r.IsRequired,
	}
	if r.Types != "" {
		result.Types = strings.Split(r.Types, ",")
	}

	// OverWrite the organizationID and salesChannelStoreKey from the request
	result.ProductListingsQuery.OrganizationID = r.OrganizationID
	result.ProductListingsQuery.SalesChannelStoreKey = r.SalesChannelStoreKey
	result.ProductListingsQuery.SalesChannelPlatform = r.SalesChannelPlatform

	return result, nil
}

type getProductAttributesResponse struct {
	Attributes      []category.Attribute `json:"attributes"`
	ParameterString string               `json:"parameter_string"`
}

type getProductRulesRequest struct {
	OrganizationID       string `form:"organization_id" binding:"required"`
	SalesChannelStoreKey string `form:"sales_channel_store_key" binding:"required"`
	SalesChannelPlatform string `form:"sales_channel_platform" binding:"required"`
	ProductListingsQuery string `form:"product_listings_query" binding:"required"`
	IsRequired           string `form:"is_required" binding:"omitempty,oneof=true false"`
}

func (r *getProductRulesRequest) convertToToGetProductCategoryRulesArgs() (*product_category.GetProductCategoryRulesArgs, error) {
	searchProductListingRequest := new(product_listing_handler.SearchProductListingRequest)
	fakeRequest := &http.Request{URL: &url.URL{RawQuery: r.ProductListingsQuery}}
	if err := binding.Query.Bind(fakeRequest, searchProductListingRequest); err != nil {
		return nil, err
	}

	result := &product_category.GetProductCategoryRulesArgs{
		OrganizationID:       r.OrganizationID,
		SalesChannelStoreKey: r.SalesChannelStoreKey,
		SalesChannelPlatform: r.SalesChannelPlatform,
		IsRequired:           r.IsRequired,
		ProductListingsQuery: searchProductListingRequest.ToSearchProductListingArgs(),
	}

	// OverWrite the organizationID and salesChannelStoreKey from the request
	result.ProductListingsQuery.OrganizationID = r.OrganizationID
	result.ProductListingsQuery.SalesChannelStoreKey = r.SalesChannelStoreKey
	result.ProductListingsQuery.SalesChannelPlatform = r.SalesChannelPlatform

	return result, nil
}

type getProductRulesResponse struct {
	Rules           []category.Rule `json:"rules"`
	ParameterString string          `json:"parameter_string"`
}
