package consts

import (
	"strconv"
)

// https://www.notion.so/automizely/feed_publications-e63c1161d87944388be605ca83080d74
const (
	BMEEventName = "feed/publication" // 上报到 clickhouse 的 event
)

// 业务事件
type BizEvent string

func (b BizEvent) String() string {
	return string(b)
}

// 业务域
type BizResourceType string

func (b BizResourceType) String() string {
	return string(b)
}

// 业务状态
type BizStatus string

func (b BizStatus) String() string {
	return string(b)
}

// 业务步骤
type BizStageName string

func (b BizStageName) String() string {
	return string(b)
}

// 业务需要进行统计的内容，not error message
type BizMessage string

func (b BizMessage) String() string {
	return string(b)
}

// 错误码
type BizErrorCode int64

func (b BizErrorCode) Int64() int64 {
	return int64(b)
}

func (b BizErrorCode) String() string {
	return strconv.FormatInt(int64(b), 10)
}

const (
	BmeSuccess BizStatus = "succeeded"
	BmeFailed  BizStatus = "failed"
)

// listing 默认都是商品域
const (
	Products BizResourceType = "products"
)

// 同步库存
const (
	PublishInventory                                  BizEvent     = "listing_publish_inventory"
	PublishInventoryAtCreateTaskStageByInventoryLevel BizStageName = "listing_publish_inventory_create_task_by_inventory_level"
	PublishInventoryAtCreateTaskStageByProduct        BizStageName = "listing_publish_inventory_create_task_by_product"
	PublishInventoryAtConsumeTaskStage                BizStageName = "listing_publish_inventory_consume_task"
)

// 同步价格
const (
	PublishPrice                   BizEvent     = "listing_publish_price"
	PublishPriceAtCreateTaskStage  BizStageName = "listing_publish_price_create_task"
	PublishPriceAtConsumeTaskStage BizStageName = "listing_publish_price_consume_task"
)

// sync product listing
const (
	PublishProduct BizEvent = "publish_product_listing"
)

// mapping product listing
const (
	MapProduct BizEvent = "map_product_listing"
)

func StandErrorCode(errorCode string) BizErrorCode {
	// 如果 errorCode 是空字符串，会返回 0 表示任务执行成功
	errorCodeInt, err := strconv.Atoi(errorCode)
	if err != nil {
		errorCodeInt = 0
	}
	return BizErrorCode(int64(errorCodeInt))
}

func InternalErrorCode() BizErrorCode {
	return BizErrorCode(int64(50001))
}

// auto link context
const (
	AutoLinkFromKey = "auto_link_from"

	// auto-link API 触发来源
	AutoLinkFromSettingBatchTask         = "batch_task"
	AutoLinkFromSourceProductEvent       = "source_product_event"
	AutoLinkFromBlockedOrderCompensation = "blocked_order_compensation"

	AutoLinkPassKey = "auto_link_pass"
)

// listing ai record
const (
	FeedEventTypeListingsAI         = "listing_ai_record"
	EventTypeListingAiRecord        = "listing_ai_record"
	AISuggestionProductTitle        = "product_title"
	AISuggestionProductDesc         = "product_desc"
	AISuggestionProductTitleAndDesc = "product_title_and_desc"
)
