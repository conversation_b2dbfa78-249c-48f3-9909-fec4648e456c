package consts

const (
	// 使用 sale_price 同步价格
	PriceSyncSourceFieldSalePrice = "sale_price"

	// 使用 compare_at_price 同步价格
	PriceSyncSourceFieldCompareAtPrice = "compare_at_price"

	// 按百分比同步价格
	PriceSyncUsePercentage = "percentage"

	// 按固定金额同步价格
	PriceSyncRulesUseFixedAmount = "fixed_amount"

	CalculateSuccess = "succeeded"
	CalculateFailed  = "failed"

	CNTInventoryLevelSearchLimit     = 500
	CNTAppsInventoryLevelSearchLimit = 250

	TikTokMaxInventory = 99999

	// 计算价格的数据来源
	PriceFromInputVariant                      = "from_input"                               // 从 payload 读取
	PriceFromConnectorsVariantPrice            = "from_connector_variant_price"             // 读取中台 variant.price
	PriceFromConnectorsVariantPresentmentPrice = "from_connector_variant_presentment_price" // 读取中台 variant.presentment_price
	PriceFromPresentmentPriceService           = "from_presentment_price_service"           // 从多币种服务读取
	PriceFromCurrencyConvertor                 = "from_currency_convertor"                  // 经过价格转换器转换后的价格
	PriceFromCurrencyShopifyMarkets            = "from_shopify_markets"                     // 从 Shopify markets 读取

	// 计算库存的数据来源
	QuantityFromInputVariant     = "from_input"             // 从 payload 读取
	QuantityFromInventoryLevel   = "from_inventory_level"   // 从中台 inventory_level 读取
	QuantityFromConnectorVariant = "from_connector_variant" // 从中台 variant 读取
	QuantityFromAllowBackorder   = "from_allow_backorder"   // 允许超卖同步为 99999
	QuantityFromHitThreshold     = "from_hit_threshold"     // 触发阈值设置为0
	QuantityFromFixNegative      = "from_fix_negative"      // 修正负数库存为0
	QuantityFromInactiveVariant  = "from_inactive_variant"  // 下架SKU清库存

	// Preference
	SettingPreferenceCustomized = "customized"
	SettingPreferenceStore      = "store"
)

type ProductDetailField string

func (f ProductDetailField) String() string {
	return string(f)
}

const (
	ProductDetailFieldTitle       = ProductDetailField("title")
	ProductDetailFieldMedia       = ProductDetailField("media")
	ProductDetailFieldDescription = ProductDetailField("description")
)
