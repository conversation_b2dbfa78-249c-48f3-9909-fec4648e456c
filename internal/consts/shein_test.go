package consts

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func TestOwnershipType_String(t *testing.T) {
	assert.Equal(t, "merchant", WarehouseOwnershipTypeMerchant.String())
	assert.Equal(t, "certified", WarehouseOwnershipTypeCertified.String())
}

func TestExternalImageTypeConsts(t *testing.T) {
	assert.Equal(t, "main", ExternalImageTypeMain)
	assert.Equal(t, "detail", ExternalImageTypeDetail)
	assert.Equal(t, "square", ExternalImageTypeSquare)
	assert.Equal(t, "piece", ExternalImageTypePiece)
	assert.Equal(t, "specific", ExternalImageTypeSpecific)
}

func TestSheinCacheConsts(t *testing.T) {
	assert.Equal(t, 7*24*time.Hour, SHEINLanguageCacheDuration)
	assert.Equal(t, "listings-api:shein_language", SHEINLanguageCacheKeyPrefix)
	assert.Equal(t, 8*time.Hour, SHEINBrandsCacheDuration)
	assert.Equal(t, "listings-api:shein_brands", SHEINBrandsCacheKeyPrefix)
	assert.Equal(t, 8*time.Hour, SHEINCategoryAttributesCacheDuration)
	assert.Equal(t, "listings-api:shein_category_attributes", SHEINCategoryAttributesCacheKeyPrefix)
}
