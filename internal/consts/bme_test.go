package consts

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestBizErrorCode_StringAndInt64(t *testing.T) {
	var code BizErrorCode = 12345
	assert.Equal(t, int64(12345), code.Int64())
	assert.Equal(t, "12345", code.String())
}

func TestStandErrorCode(t *testing.T) {
	assert.Equal(t, BizErrorCode(0), StandErrorCode(""))
	assert.Equal(t, BizErrorCode(0), StandErrorCode("abc"))
	assert.Equal(t, BizErrorCode(100), StandErrorCode("100"))
}

func TestInternalErrorCode(t *testing.T) {
	assert.Equal(t, BizErrorCode(50001), InternalErrorCode())
}

func TestBizEvent_String(t *testing.T) {
	var e BizEvent = "event_x"
	assert.Equal(t, "event_x", e.String())
}

func TestBizResourceType_String(t *testing.T) {
	var r BizResourceType = "resource_x"
	assert.Equal(t, "resource_x", r.String())
}

func TestBizStatus_String(t *testing.T) {
	var s BizStatus = "status_x"
	assert.Equal(t, "status_x", s.String())
}

func TestBizStageName_String(t *testing.T) {
	var s BizStageName = "stage_x"
	assert.Equal(t, "stage_x", s.String())
}

func TestBizMessage_String(t *testing.T) {
	var m BizMessage = "msg_x"
	assert.Equal(t, "msg_x", m.String())
}

func TestConstValues(t *testing.T) {
	assert.Equal(t, BizStatus("succeeded"), BmeSuccess)
	assert.Equal(t, BizStatus("failed"), BmeFailed)
	assert.Equal(t, BizResourceType("products"), Products)
	assert.Equal(t, BizEvent("listing_publish_inventory"), PublishInventory)
	assert.Equal(t, BizStageName("listing_publish_inventory_create_task_by_inventory_level"), PublishInventoryAtCreateTaskStageByInventoryLevel)
	assert.Equal(t, BizStageName("listing_publish_inventory_create_task_by_product"), PublishInventoryAtCreateTaskStageByProduct)
	assert.Equal(t, BizStageName("listing_publish_inventory_consume_task"), PublishInventoryAtConsumeTaskStage)
	assert.Equal(t, BizEvent("listing_publish_price"), PublishPrice)
	assert.Equal(t, BizStageName("listing_publish_price_create_task"), PublishPriceAtCreateTaskStage)
	assert.Equal(t, BizStageName("listing_publish_price_consume_task"), PublishPriceAtConsumeTaskStage)
	assert.Equal(t, BizEvent("publish_product_listing"), PublishProduct)
	assert.Equal(t, BizEvent("map_product_listing"), MapProduct)
	assert.Equal(t, "auto_link_from", AutoLinkFromKey)
	assert.Equal(t, "batch_task", AutoLinkFromSettingBatchTask)
	assert.Equal(t, "source_product_event", AutoLinkFromSourceProductEvent)
	assert.Equal(t, "blocked_order_compensation", AutoLinkFromBlockedOrderCompensation)
	assert.Equal(t, "auto_link_pass", AutoLinkPassKey)
	assert.Equal(t, "listing_ai_record", FeedEventTypeListingsAI)
	assert.Equal(t, "listing_ai_record", EventTypeListingAiRecord)
	assert.Equal(t, "product_title", AISuggestionProductTitle)
	assert.Equal(t, "product_desc", AISuggestionProductDesc)
	assert.Equal(t, "product_title_and_desc", AISuggestionProductTitleAndDesc)
}
