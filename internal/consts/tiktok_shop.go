package consts

// Attributes
// Enum:
// 1.SALES_PROPERTY, eg: Color, Size
// 2.PRODUCT_PROPERTY, eg: Materials, Power
const (
	AttributeTypeSalesProperty           = "SALES_PROPERTY"
	AttributeTypeProductProperty         = "PRODUCT_PROPERTY"
	AttributeTypeSalesPropertyMainOption = "SALES_PROPERTY_MAIN_OPTION"
	AttributeTypeSizeProperty            = "SIZE_PROPERTY"

	AttributeInputTypeSelectManualInput = "select_manual_input"

	AttributeValueDataTypePositiveIntOrDecimal = "POSITIVE_INT_OR_DECIMAL"
)

const (
	CategoryVersionV1 = "v1"
	CategoryVersionV2 = "v2"
)

const (
	RegionUS = "US"
	RegionJP = "JP"
)

const (
	CategoryStatusAvailable = "AVAILABLE"
)

const (
	CategoryStatusActive   = "active"
	CategoryStatusInactive = "inactive"
)
