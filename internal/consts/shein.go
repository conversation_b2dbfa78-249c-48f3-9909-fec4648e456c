package consts

import "time"

type OwnershipType string

const (
	WarehouseOwnershipTypeMerchant  OwnershipType = "merchant"
	WarehouseOwnershipTypeCertified OwnershipType = "certified"
)

func (o OwnershipType) String() string {
	return string(o)
}

const (
	// Shein
	ExternalImageTypeMain     = "main"
	ExternalImageTypeDetail   = "detail"
	ExternalImageTypeSquare   = "square"
	ExternalImageTypePiece    = "piece"
	ExternalImageTypeSpecific = "specific"
)

const (
	SHEINLanguageCacheDuration  = 7 * 24 * time.Hour
	SHEINLanguageCacheKeyPrefix = "listings-api:shein_language"

	SHEINBrandsCacheDuration  = 8 * time.Hour
	SHEINBrandsCacheKeyPrefix = "listings-api:shein_brands"

	SHEINCategoryAttributesCacheDuration  = 8 * time.Hour
	SHEINCategoryAttributesCacheKeyPrefix = "listings-api:shein_category_attributes"
)
