package consts

// MatchState 匹配状态
type MatchState string

func (m MatchState) String() string {
	return string(m)
}

const (
	MatchStateUnmatch MatchState = "unmatch"
	MatchStateMatched MatchState = "matched"
)

// AuditState 审核相关枚举
type AuditState string

func (a AuditState) String() string {
	return string(a)
}

const (
	AuditStateReviewing AuditState = "reviewing"
	AuditStateFailed    AuditState = "failed"
	AuditStateSucceeded AuditState = "succeeded"
)

// LinkStatus 商品与 variant link 状态
type LinkStatus string

func (l LinkStatus) String() string {
	return string(l)
}

const (
	LinkStatusUnlink        LinkStatus = "unlink"         // 商品 unlink
	LinkStatusPartialLinked LinkStatus = "partial_linked" // 商品只有部分 variant link
	LinkStatusLinked        LinkStatus = "linked"         // 商品已经 link
)

// SyncStatus 商品与 variant sync 状态
type SyncStatus string

func (s SyncStatus) String() string {
	return string(s)
}

const (
	SyncStatusSynced        SyncStatus = "synced" // 商品已经 link
	SyncStatusUnsync        SyncStatus = "unsync" // 商品已经 link
	SyncStatusPartialSynced SyncStatus = "partial_synced"
)

// PublishState product listing 任务同步状态
type PublishState string

const (
	PublishStatePending   PublishState = "pending" // 未同步
	PublishStateRunning   PublishState = "running" // 正在同步
	PublishStateBlocked   PublishState = "blocked"
	PublishStateIgnored   PublishState = "ignored"
	PublishStateSucceeded PublishState = "succeeded" // 同步成功
	PublishStateFailed    PublishState = "failed"    // 同步失败
)

// ReadyStatus product listing 同步状态
type ReadyStatus string

const (
	ReadyStatusUnready ReadyStatus = "unready"
	ReadyStatusReady   ReadyStatus = "ready"
)

// SalesChannelProductState sales channel 商品状态
type SalesChannelProductState string

const (
	SalesChannelProductStateDraft               SalesChannelProductState = "draft"
	SalesChannelProductStatePending             SalesChannelProductState = "pending"
	SalesChannelProductStateFailed              SalesChannelProductState = "failed"
	SalesChannelProductStateLive                SalesChannelProductState = "live"
	SalesChannelProductStateSellerDeactivate    SalesChannelProductState = "seller_deactivated"
	SalesChannelProductStatePlatformDeactivated SalesChannelProductState = "platform_deactivated"
	SalesChannelProductStateFreeze              SalesChannelProductState = "freeze"
	SalesChannelProductStateDeleted             SalesChannelProductState = "deleted"
)

// ProductsCenterProductPublishState product center 商品状态
type ProductsCenterProductPublishState string

func (p ProductsCenterProductPublishState) String() string {
	return string(p)
}

const (
	ProductsCenterProductPublishStateActive   ProductsCenterProductPublishState = "active"   // 上架
	ProductsCenterProductPublishStateInactive ProductsCenterProductPublishState = "inactive" // 下架
)

// ProductListingProductState product listings 商品状态
type ProductListingProductState string

func (p ProductListingProductState) String() string {
	return string(p)
}

const (
	ProductListingProductStatePending            ProductListingProductState = "pending"
	ProductListingProductStateActive             ProductListingProductState = "active"
	ProductListingProductStateReviewing          ProductListingProductState = "reviewing"
	ProductListingProductStateInactive           ProductListingProductState = "inactive"
	ProductListingProductStateSuspended          ProductListingProductState = "suspended"
	ProductListingProductStateDeleted            ProductListingProductState = "deleted"
	ProductListingProductStatePartiallyActive    ProductListingProductState = "partially_active"
	ProductListingProductStatePartiallyReviewing ProductListingProductState = "partially_reviewing"
	ProductListingProductStatePartiallySuspend   ProductListingProductState = "partially_suspended"
)

// AllowSyncStatus 允许同步
type AllowSyncStatus string

func (a AllowSyncStatus) String() string {
	return string(a)
}

const (
	AllowSyncEnabled  AllowSyncStatus = "enabled"
	AllowSyncDisabled AllowSyncStatus = "disabled"
)

type ProductsCenterVariantStatus string

func (p ProductsCenterVariantStatus) String() string {
	return string(p)
}

const (
	ProductsCenterVariantStatusActive   = "active"
	ProductsCenterVariantStatusInActive = "inactive"
)

type ProductOptionValueAuditState string

func (p ProductOptionValueAuditState) String() string {
	return string(p)
}

const (
	ProductOptionValueAuditStateReviewing ProductOptionValueAuditState = "reviewing"
	ProductOptionValueAuditStateFailed    ProductOptionValueAuditState = "failed"
	ProductOptionValueAuditStateSucceeded ProductOptionValueAuditState = "succeeded"
)

type ProductOptionValueState string

func (p ProductOptionValueState) String() string {
	return string(p)
}

const (
	ProductOptionValueStateActive    ProductOptionValueState = "active"
	ProductOptionValueStateInactive  ProductOptionValueState = "inactive"
	ProductOptionValueStateReviewing ProductOptionValueState = "reviewing"
	ProductOptionValueStateSuspend   ProductOptionValueState = "suspended"
	ProductOptionValueStatePending   ProductOptionValueState = "pending"
)
