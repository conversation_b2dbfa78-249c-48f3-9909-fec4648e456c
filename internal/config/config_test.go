package config

import (
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestIsInWhiteList(t *testing.T) {
	tests := []struct {
		name           string
		condition      SkuImageRemovalConditions
		organizationID string
		optionNames    []string
		expected       bool
	}{
		{
			name: "组织ID匹配且所有选项都在白名单中",
			condition: SkuImageRemovalConditions{
				OrganizationID:  "org1",
				RequiredOptions: []string{"颜色", "尺寸", "款式"},
			},
			organizationID: "org1",
			optionNames:    []string{"颜色", "尺寸"},
			expected:       true,
		},
		{
			name: "组织ID匹配但有选项不在白名单中",
			condition: SkuImageRemovalConditions{
				OrganizationID:  "org1",
				RequiredOptions: []string{"颜色", "尺寸"},
			},
			organizationID: "org1",
			optionNames:    []string{"颜色", "材质"},
			expected:       false,
		},
		{
			name: "组织ID不匹配",
			condition: SkuImageRemovalConditions{
				OrganizationID:  "org1",
				RequiredOptions: []string{"颜色", "尺寸"},
			},
			organizationID: "org2",
			optionNames:    []string{"颜色", "尺寸"},
			expected:       false,
		},
		{
			name: "组织ID匹配但选项为空",
			condition: SkuImageRemovalConditions{
				OrganizationID:  "org1",
				RequiredOptions: []string{"颜色", "尺寸"},
			},
			organizationID: "org1",
			optionNames:    []string{},
			expected:       true,
		},
		{
			name: "组织ID匹配但白名单为空",
			condition: SkuImageRemovalConditions{
				OrganizationID:  "org1",
				RequiredOptions: []string{},
			},
			organizationID: "org1",
			optionNames:    []string{"颜色", "尺寸"},
			expected:       false,
		},
		{
			name: "组织ID匹配且白名单和选项都为空",
			condition: SkuImageRemovalConditions{
				OrganizationID:  "org1",
				RequiredOptions: []string{},
			},
			organizationID: "org1",
			optionNames:    []string{},
			expected:       true,
		},
		{
			name: "选项顺序不同但都在白名单中",
			condition: SkuImageRemovalConditions{
				OrganizationID:  "org1",
				RequiredOptions: []string{"颜色", "尺寸", "款式"},
			},
			organizationID: "org1",
			optionNames:    []string{"尺寸", "颜色"},
			expected:       true,
		},
		{
			name: "白名单有冗余选项",
			condition: SkuImageRemovalConditions{
				OrganizationID:  "org1",
				RequiredOptions: []string{"颜色", "尺寸", "款式", "材质"},
			},
			organizationID: "org1",
			optionNames:    []string{"颜色", "尺寸"},
			expected:       true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tt.condition.IsInWhiteList(tt.organizationID, tt.optionNames)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestShouldRemoveSkusImage(t *testing.T) {
	tests := []struct {
		name           string
		config         TikTokSyncConfig
		organizationID string
		optionNames    []string
		expected       bool
	}{
		{
			name: "匹配单个条件",
			config: TikTokSyncConfig{
				SkuImageRemovalConditions: []SkuImageRemovalConditions{
					{
						OrganizationID:  "org1",
						RequiredOptions: []string{"颜色", "尺寸"},
					},
				},
			},
			organizationID: "org1",
			optionNames:    []string{"颜色"},
			expected:       true,
		},
		{
			name: "匹配多个条件中的一个",
			config: TikTokSyncConfig{
				SkuImageRemovalConditions: []SkuImageRemovalConditions{
					{
						OrganizationID:  "org1",
						RequiredOptions: []string{"颜色", "尺寸"},
					},
					{
						OrganizationID:  "org2",
						RequiredOptions: []string{"款式", "材质"},
					},
				},
			},
			organizationID: "org2",
			optionNames:    []string{"款式"},
			expected:       true,
		},
		{
			name: "不匹配任何条件",
			config: TikTokSyncConfig{
				SkuImageRemovalConditions: []SkuImageRemovalConditions{
					{
						OrganizationID:  "org1",
						RequiredOptions: []string{"颜色", "尺寸"},
					},
					{
						OrganizationID:  "org2",
						RequiredOptions: []string{"款式", "材质"},
					},
				},
			},
			organizationID: "org3",
			optionNames:    []string{"颜色", "尺寸"},
			expected:       false,
		},
		{
			name: "组织ID匹配但选项不完全匹配",
			config: TikTokSyncConfig{
				SkuImageRemovalConditions: []SkuImageRemovalConditions{
					{
						OrganizationID:  "org1",
						RequiredOptions: []string{"颜色", "尺寸"},
					},
				},
			},
			organizationID: "org1",
			optionNames:    []string{"颜色", "材质"},
			expected:       false,
		},
		{
			name: "条件列表为空",
			config: TikTokSyncConfig{
				SkuImageRemovalConditions: []SkuImageRemovalConditions{},
			},
			organizationID: "org1",
			optionNames:    []string{"颜色", "尺寸"},
			expected:       false,
		},
		{
			name: "选项名称为空",
			config: TikTokSyncConfig{
				SkuImageRemovalConditions: []SkuImageRemovalConditions{
					{
						OrganizationID:  "org1",
						RequiredOptions: []string{"颜色", "尺寸"},
					},
				},
			},
			organizationID: "org1",
			optionNames:    []string{},
			expected:       true,
		},
		{
			name: "必需选项为空",
			config: TikTokSyncConfig{
				SkuImageRemovalConditions: []SkuImageRemovalConditions{
					{
						OrganizationID:  "org1",
						RequiredOptions: []string{},
					},
				},
			},
			organizationID: "org1",
			optionNames:    []string{"颜色", "尺寸"},
			expected:       false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tt.config.ShouldRemoveSkusImage(tt.organizationID, tt.optionNames)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestLoadChannelRegionConfig(t *testing.T) {
	tests := []struct {
		name         string
		config       *Config
		platform     string
		region       string
		wantRegion   string
		wantCurrency string
		wantError    bool
	}{
		{
			name: "正常情况 - 平台和地区都存在",
			config: &Config{
				DynamicConfigs: &DynamicConfigs{
					ChannelRegionConfig: map[string][]ChannelRegionConfig{
						"tiktok": {
							{
								Region:   "US",
								Currency: "USD",
							},
							{
								Region:   "UK",
								Currency: "GBP",
							},
						},
					},
				},
			},
			platform:     "tiktok",
			region:       "US",
			wantRegion:   "US",
			wantCurrency: "USD",
			wantError:    false,
		},
		{
			name: "DynamicConfigs 为 nil",
			config: &Config{
				DynamicConfigs: nil,
			},
			platform:  "tiktok",
			region:    "US",
			wantError: true,
		},
		{
			name: "平台不存在",
			config: &Config{
				DynamicConfigs: &DynamicConfigs{
					ChannelRegionConfig: map[string][]ChannelRegionConfig{
						"tiktok": {
							{
								Region:   "US",
								Currency: "USD",
							},
						},
					},
				},
			},
			platform:  "shopee",
			region:    "US",
			wantError: true,
		},
		{
			name: "地区不存在",
			config: &Config{
				DynamicConfigs: &DynamicConfigs{
					ChannelRegionConfig: map[string][]ChannelRegionConfig{
						"tiktok": {
							{
								Region:   "US",
								Currency: "USD",
							},
						},
					},
				},
			},
			platform:  "tiktok",
			region:    "UK",
			wantError: true,
		},
		{
			name: "货币为空",
			config: &Config{
				DynamicConfigs: &DynamicConfigs{
					ChannelRegionConfig: map[string][]ChannelRegionConfig{
						"tiktok": {
							{
								Region:   "US",
								Currency: "",
							},
						},
					},
				},
			},
			platform:  "tiktok",
			region:    "US",
			wantError: true,
		},
		{
			name: "多个地区配置",
			config: &Config{
				DynamicConfigs: &DynamicConfigs{
					ChannelRegionConfig: map[string][]ChannelRegionConfig{
						"tiktok": {
							{
								Region:          "US",
								Currency:        "USD",
								HasCrossBorder:  true,
								CheckPriceRange: true,
								LocalToLocal: &ChannelPriceRange{
									MinPrice: 0.99,
									MaxPrice: 999.99,
								},
							},
							{
								Region:          "UK",
								Currency:        "GBP",
								HasCrossBorder:  false,
								CheckPriceRange: true,
								LocalToLocal: &ChannelPriceRange{
									MinPrice: 0.99,
									MaxPrice: 500,
								},
							},
							{
								Region:   "JP",
								Currency: "JPY",
							},
						},
					},
				},
			},
			platform:     "tiktok",
			region:       "JP",
			wantRegion:   "JP",
			wantCurrency: "JPY",
			wantError:    false,
		},
		{
			name: "区分大小写",
			config: &Config{
				DynamicConfigs: &DynamicConfigs{
					ChannelRegionConfig: map[string][]ChannelRegionConfig{
						"tiktok": {
							{
								Region:   "US",
								Currency: "USD",
							},
						},
					},
				},
			},
			platform:  "tiktok",
			region:    "us", // 小写
			wantError: true, // 因为配置是 "US"
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			config, err := tt.config.LoadChannelRegionConfig(tt.platform, tt.region)

			if tt.wantError {
				assert.Error(t, err)
				assert.Nil(t, config)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, config)
				assert.Equal(t, tt.wantRegion, config.Region)
				assert.Equal(t, tt.wantCurrency, config.Currency)
			}
		})
	}
}

func TestValidatePriceRange(t *testing.T) {
	tests := []struct {
		name         string
		regionConfig *ChannelRegionConfig
		price        float64
		wantError    bool
	}{
		{
			name: "价格在正常范围内",
			regionConfig: &ChannelRegionConfig{
				CheckPriceRange: true,
				LocalToLocal: &ChannelPriceRange{
					MinPrice: 0.99,
					MaxPrice: 999.99,
				},
			},
			price:     100.0,
			wantError: false,
		},
		{
			name: "价格等于最小值",
			regionConfig: &ChannelRegionConfig{
				CheckPriceRange: true,
				LocalToLocal: &ChannelPriceRange{
					MinPrice: 0.99,
					MaxPrice: 999.99,
				},
			},
			price:     0.99,
			wantError: false,
		},
		{
			name: "价格等于最大值",
			regionConfig: &ChannelRegionConfig{
				CheckPriceRange: true,
				LocalToLocal: &ChannelPriceRange{
					MinPrice: 0.99,
					MaxPrice: 999.99,
				},
			},
			price:     999.99,
			wantError: false,
		},
		{
			name: "价格低于最小值",
			regionConfig: &ChannelRegionConfig{
				CheckPriceRange: true,
				LocalToLocal: &ChannelPriceRange{
					MinPrice: 0.99,
					MaxPrice: 999.99,
				},
			},
			price:     0.5,
			wantError: true,
		},
		{
			name: "价格高于最大值",
			regionConfig: &ChannelRegionConfig{
				CheckPriceRange: true,
				LocalToLocal: &ChannelPriceRange{
					MinPrice: 0.99,
					MaxPrice: 999.99,
				},
			},
			price:     1000.0,
			wantError: true,
		},
		{
			name: "禁用价格范围检查",
			regionConfig: &ChannelRegionConfig{
				CheckPriceRange: false,
				LocalToLocal: &ChannelPriceRange{
					MinPrice: 0.99,
					MaxPrice: 999.99,
				},
			},
			price:     2000.0, // 超出范围但不检查
			wantError: false,
		},
		{
			name:         "配置为 nil",
			regionConfig: nil,
			price:        100.0,
			wantError:    true,
		},
		{
			name: "LocalToLocal 为 nil",
			regionConfig: &ChannelRegionConfig{
				CheckPriceRange: true,
				LocalToLocal:    nil,
			},
			price:     100.0,
			wantError: false,
		},
		{
			name: "价格为零",
			regionConfig: &ChannelRegionConfig{
				CheckPriceRange: true,
				LocalToLocal: &ChannelPriceRange{
					MinPrice: 0.99,
					MaxPrice: 999.99,
				},
			},
			price:     0.0,
			wantError: true,
		},
		{
			name: "价格为负数",
			regionConfig: &ChannelRegionConfig{
				CheckPriceRange: true,
				LocalToLocal: &ChannelPriceRange{
					MinPrice: 0.99,
					MaxPrice: 999.99,
				},
			},
			price:     -10.0,
			wantError: true,
		},
		{
			name: "最小价格为零",
			regionConfig: &ChannelRegionConfig{
				CheckPriceRange: true,
				LocalToLocal: &ChannelPriceRange{
					MinPrice: 0.0,
					MaxPrice: 999.99,
				},
			},
			price:     0.0,
			wantError: false,
		},
		{
			name: "小数点精度测试",
			regionConfig: &ChannelRegionConfig{
				CheckPriceRange: true,
				LocalToLocal: &ChannelPriceRange{
					MinPrice: 0.99,
					MaxPrice: 999.99,
				},
			},
			price:     0.989999, // 接近但小于最小值
			wantError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.regionConfig.ValidatePriceRange(tt.price)

			if tt.wantError {
				assert.Error(t, err, "应该返回错误")
				if tt.regionConfig != nil && tt.regionConfig.CheckPriceRange && tt.regionConfig.LocalToLocal != nil {
					// 验证错误消息中包含价格范围信息
					assert.Contains(t, err.Error(), "price exceeds")
					assert.Contains(t, err.Error(), fmt.Sprintf("%v", tt.regionConfig.LocalToLocal.MinPrice))
					assert.Contains(t, err.Error(), fmt.Sprintf("%v", tt.regionConfig.LocalToLocal.MaxPrice))
				}
			} else {
				assert.NoError(t, err, "不应该返回错误")
			}
		})
	}
}

func TestInitCDNDomainRegExps(t *testing.T) {
	tests := []struct {
		name     string
		regexps  []string
		want     int
		testURLs map[string]bool // 用于测试生成的正则表达式是否能正确匹配URL
	}{
		{
			name:    "正常多个正则表达式",
			regexps: []string{"^https://example\\.com/.*$", "^https://cdn\\.example\\.com/.*$"},
			want:    2,
			testURLs: map[string]bool{
				"https://example.com/image.jpg":     true,
				"https://cdn.example.com/image.jpg": true,
				"https://other.com/image.jpg":       false,
			},
		},
		{
			name:    "空切片",
			regexps: []string{},
			want:    0,
			testURLs: map[string]bool{
				"https://example.com/image.jpg": false,
			},
		},
		{
			name:    "包含空字符串",
			regexps: []string{"^https://example\\.com/.*$", "", "^https://cdn\\.example\\.com/.*$"},
			want:    2, // 应该忽略空字符串
			testURLs: map[string]bool{
				"https://example.com/image.jpg":     true,
				"https://cdn.example.com/image.jpg": true,
			},
		},
		{
			name:    "nil切片",
			regexps: nil,
			want:    0,
			testURLs: map[string]bool{
				"https://example.com/image.jpg": false,
			},
		},
		{
			name:    "特殊正则表达式",
			regexps: []string{"^(http|https)://[^/]+\\.example\\.com/.*$"},
			want:    1,
			testURLs: map[string]bool{
				"http://sub.example.com/image.jpg":  true,
				"https://sub.example.com/path/file": true,
				"https://example.com/image.jpg":     false,
				"ftp://sub.example.com/file":        false,
			},
		},
		{
			name:    "具有复杂模式的正则表达式",
			regexps: []string{"^https://(?:img|cdn)\\d*\\.example\\.com/[a-z0-9]+/.*\\.(jpg|png|gif)$"},
			want:    1,
			testURLs: map[string]bool{
				"https://img.example.com/abcd/test.jpg":  true,
				"https://cdn2.example.com/xyz/logo.png":  true,
				"https://img3.example.com/123/icon.gif":  true,
				"https://img.example.com/test.jpg":       false, // 缺少中间路径
				"https://cdn.example.com/xyz/doc.pdf":    false, // 不支持的扩展名
				"http://img.example.com/abcd/test.jpg":   false, // 不是https
				"https://other.example.com/xyz/logo.png": false, // 不匹配的子域名
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := initCDNDomainRegExps(tt.regexps)

			// 检查返回的正则表达式数量
			assert.Equal(t, tt.want, len(result), "正则表达式数量不匹配")

			// 测试返回的正则表达式是否能正确匹配URLs
			for url, shouldMatch := range tt.testURLs {
				matched := false
				for _, re := range result {
					if re.MatchString(url) {
						matched = true
						break
					}
				}
				assert.Equal(t, shouldMatch, matched, "URL %s 匹配结果不符合预期", url)
			}
		})
	}
}
