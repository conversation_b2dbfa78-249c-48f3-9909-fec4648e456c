package config

import (
	"fmt"
	"os"
	"regexp"

	"github.com/newrelic/go-agent/v3/newrelic"
	"github.com/pkg/errors"
	"github.com/shopspring/decimal"
	"github.com/spf13/viper"
	"go.uber.org/zap"

	cc "github.com/AfterShip/config-center-sdk-go"
	"github.com/AfterShip/config-center-sdk-go/mapper"
	"github.com/AfterShip/gopkg/api/server"
	"github.com/AfterShip/gopkg/cfg"
	"github.com/AfterShip/gopkg/facility/set"
	"github.com/AfterShip/gopkg/log"
)

type Config struct {
	API                        *server.APIConfig
	Admin                      *server.AdminConfig
	NewRelic                   *newrelic.Config
	GCP                        GCPConf
	Log                        Log
	DynamicConfigs             *DynamicConfigs
	Spanner                    *SpannerConfig
	ConnectorsJobAPI           APIConfig
	JobGroups                  map[string]string
	ConnectorsAPI              APIConfig
	ConnectorsCommerceProxyAPI APIConfig
	ProductsCenterAPI          APIConfig
	FeedV1API                  APIConfig
	FeedV2API                  APIConfig
	PubSubTopics               PubSubTopics
	BusinessMonitoringExporter BusinessMonitoringExporter
}

// DynamicConfigs is the config for dynamic config which is loaded from config center
type DynamicConfigs struct {
	GCPServiceAccount string `config:"gcp-auth.service-account"`
	// Elasticsearch cilent configs
	ElasticsearchSSLCA              string                           `config:"db-ssl.es.products-client-ca.crt"`
	ElasticsearchSSLCrt             string                           `config:"db-ssl.es.products-client.crt"`
	ElasticsearchSSLKey             string                           `config:"db-ssl.es.products-client.key"`
	ElasticsearchAuth               *ElasticsearchAuthConfig         `config:"db-auth.es.products"`
	Redis                           RedisConfig                      `config:"db-auth.redis.pltf-pd-product-listings"`
	TikTokSyncConfig                *TikTokSyncConfig                `config:"tiktok_sync_config.json"`
	ChannelRegionConfig             map[string][]ChannelRegionConfig `config:"channel_regions_config.json"`
	TiktokCDNDomains                []string                         `config:"tiktok_cdn_domains.json"`
	SheinCDNDomains                 []string                         `config:"shein_cdn_domains.json"`
	ImageCacheConfig                []ImageCacheConfig               `config:"image_cache_config.json"`
	TTSCDNDomainRegExps             []*regexp.Regexp
	SheinCDNDomainRegExps           []*regexp.Regexp
	ShopifyProductPriceSourceConfig *ShopifyProductPriceSourceConfig `config:"shopify_product_price_source.json"`
	AmazonOptionNameMapping         map[string]string                `config:"amazon_option_name_mapping.json"`
}

type ImageCacheConfig struct {
	OrganizationID string `json:"organization_id" mapstructure:"organization_id"`
	CacheSeconds   int64  `json:"cache_seconds" mapstructure:"cache_seconds"`
}

type TikTokSyncConfig struct {
	UseShortDescriptionOrganizationIDs []string                    `json:"use_short_description_organization_ids" mapstructure:"use_short_description_organization_ids"`
	SquarePngOrganizationIDs           []string                    `json:"square_png_organization_ids" mapstructure:"square_png_organization_ids"`
	EdgeImageOrganizationIDs           []string                    `json:"edge_image_organization_ids" mapstructure:"edge_image_organization_ids"`
	SkuImageRemovalConditions          []SkuImageRemovalConditions `json:"sku_image_removal_conditions" mapstructure:"sku_image_removal_conditions"`
}

func (c TikTokSyncConfig) ShouldRemoveSkusImage(organizationID string, optionNames []string) bool {
	for _, item := range c.SkuImageRemovalConditions {
		if item.IsInWhiteList(organizationID, optionNames) {
			return true
		}
	}
	return false
}

type SkuImageRemovalConditions struct {
	OrganizationID  string   `json:"organization_id" mapstructure:"organization_id"`
	RequiredOptions []string `json:"required_options" mapstructure:"required_options"` // 一个 org id 下，如果只有配置内的几个 option，则移除 sku image
}

func (s *SkuImageRemovalConditions) IsInWhiteList(organizationID string, optionNames []string) bool {
	if s.OrganizationID != organizationID {
		return false
	}

	// 所有 options 都在配置名单内，则可以移除 sku image
	for _, optionName := range optionNames {
		if !set.NewStringSet(s.RequiredOptions...).Contains(optionName) {
			return false
		}
	}

	return true
}

type ChannelRegionConfig struct {
	Region          string             `json:"region" mapstructure:"region"`
	Currency        string             `json:"currency" mapstructure:"currency"`
	HasCrossBorder  bool               `json:"has_cross_border" mapstructure:"has_cross_border"`
	CheckPriceRange bool               `json:"check_price_range" mapstructure:"check_price_range"`
	LocalToLocal    *ChannelPriceRange `json:"CrossBorder" mapstructure:"local_to_local"`
	CrossBorder     *ChannelPriceRange `json:"cross_border" mapstructure:"cross_border"`
}

type ChannelPriceRange struct {
	MinPrice float64 `json:"min_price" mapstructure:"min_price"`
	MaxPrice float64 `json:"max_price" mapstructure:"max_price"`
}

type SpannerConfig struct {
	Project  string ` validate:"required"`
	Instance string ` validate:"required"`
	Database string ` validate:"required"`
}

type ElasticsearchAuthConfig struct {
	Host     string `mapstructure:"host"`
	User     string `mapstructure:"user"`
	Password string `mapstructure:"password"`
}

type RedisConfig struct {
	Host       string `mapstructure:"host" toml:"Host"`
	MasterName string `mapstructure:"master_name" toml:"MasterName"`
	DBNumber   int    `mapstructure:"db_number" toml:"DBNumber"`
	Password   string `mapstructure:"password" toml:"Password"`
}

type APIConfig struct {
	Url string
}

type Log struct {
	Level           string
	EnableAccessLog bool
}

type GCPConf struct {
	ProjectID string
}

type PubSubTopics struct {
	SearchableProductModifySalesChannel string
}

type BusinessMonitoringExporter struct {
	FlushInterval int
	FlushCount    int
}

func Load() (*Config, error) {
	config := new(Config)
	if _, err := cfg.LoadViperConfig(config, func(viper *viper.Viper) {
		viper.SetConfigName("config-" + os.Getenv("APP_ENV"))
	}); err != nil {
		return nil, fmt.Errorf("load the local config: %w", err)
	}

	// Load dynamic configs
	dynamicConfigs, err := LoadDynamicConfigs()
	if err != nil {
		return nil, err
	}
	// Load local dynamic configs for local and development environment
	if os.Getenv("NODE_ENV") == "local" || os.Getenv("NODE_ENV") == "development" {
		dynamicConfigs.Redis = config.DynamicConfigs.Redis
	}

	config.DynamicConfigs = dynamicConfigs

	return config, nil
}

func LoadDynamicConfigs() (*DynamicConfigs, error) {
	dynamicConfigs := new(DynamicConfigs)
	if err := cc.Once(dynamicConfigs); err != nil {
		return nil, fmt.Errorf("load the config center: %w", err)
	}
	dynamicConfigs.TTSCDNDomainRegExps = initCDNDomainRegExps(dynamicConfigs.TiktokCDNDomains)
	dynamicConfigs.SheinCDNDomainRegExps = initCDNDomainRegExps(dynamicConfigs.SheinCDNDomains)
	dynamicConfigs.watch()

	return dynamicConfigs, nil
}

func (c *DynamicConfigs) watch() {
	data := &DynamicConfigs{}
	if _, err := cc.Watch(data, func(config interface{}, meta mapper.Result) {
		newData, ok := config.(DynamicConfigs)
		if !ok {
			log.GlobalLogger().Error("config assert error")
			return
		}

		// 监听部分字段
		updatedSet := set.NewStringSet(meta.Updated...)
		if updatedSet.Contains("TikTokSyncConfig") {
			c.TikTokSyncConfig = newData.TikTokSyncConfig
			log.GlobalLogger().Info("update config center: TikTokSyncConfig",
				zap.Any("tiktok_syc_config.json", newData.TikTokSyncConfig))
		}
		if updatedSet.Contains("TiktokCDNDomains") {
			c.TiktokCDNDomains = newData.TiktokCDNDomains
			c.TTSCDNDomainRegExps = initCDNDomainRegExps(newData.TiktokCDNDomains)
			log.GlobalLogger().Info("update config center: TiktokCDNDomains",
				zap.Any("tiktok_cdn_domains.json", newData.TiktokCDNDomains))
		}
		if updatedSet.Contains("SheinCDNDomains") {
			c.SheinCDNDomains = newData.SheinCDNDomains
			c.SheinCDNDomainRegExps = initCDNDomainRegExps(newData.SheinCDNDomains)
			log.GlobalLogger().Info("update config center: SheinCDNDomains",
				zap.Any("shein_cdn_domains.json", newData.SheinCDNDomains))
		}
		if updatedSet.Contains("ChannelRegionConfig") {
			c.ChannelRegionConfig = newData.ChannelRegionConfig
			log.GlobalLogger().Info("update config center: ChannelRegionConfig",
				zap.Any("channel_regions_config.json", newData.ChannelRegionConfig))
		}
		if updatedSet.Contains("ShopifyProductPriceSourceConfig") {
			c.ShopifyProductPriceSourceConfig = newData.ShopifyProductPriceSourceConfig
			log.GlobalLogger().Info("update config center: ShopifyProductPriceSourceConfig",
				zap.Any("shopify_product_price_source.json", newData.ShopifyProductPriceSourceConfig))
		}
		if updatedSet.Contains("AmazonOptionNameMapping") {
			c.AmazonOptionNameMapping = newData.AmazonOptionNameMapping
			log.GlobalLogger().Info("update config center: AmazonOptionNameMapping",
				zap.Any("amazon_option_name_mapping.json", newData.AmazonOptionNameMapping))
		}
	}); err != nil {
		panic(err)
	}
}

func (c *Config) LoadChannelRegionConfig(platform, region string) (*ChannelRegionConfig, error) {
	if c.DynamicConfigs == nil {
		return nil, errors.New("fatal error,not found dynamic config")
	}

	channelConfig, ok := c.DynamicConfigs.ChannelRegionConfig[platform]
	if !ok {
		return nil, errors.New("fatal error,not found channel config")
	}

	var cf *ChannelRegionConfig
	for i := range channelConfig {
		if channelConfig[i].Region == region {
			cf = &channelConfig[i]
		}
	}

	if cf == nil {
		return nil, errors.New("fatal error,not found region config")
	}

	if cf.Currency == "" {
		return nil, errors.New("fatal error,region not configures currency")
	}

	return cf, nil
}

func (regionConfig *ChannelRegionConfig) ValidatePriceRange(price float64) error {
	if regionConfig == nil {
		return errors.New("nil config")
	}
	if !regionConfig.CheckPriceRange {
		return nil
	}
	// 现在没有跨境店铺，一律以本地店铺判断
	if regionConfig.LocalToLocal != nil {
		if price < regionConfig.LocalToLocal.MinPrice ||
			price > regionConfig.LocalToLocal.MaxPrice {
			return errors.New(fmt.Sprintf("price exceeds the TikTok limit after being calculated by the price rule,"+
				" min price:%s,max price:%s", decimal.NewFromFloat(regionConfig.LocalToLocal.MinPrice),
				decimal.NewFromFloat(regionConfig.LocalToLocal.MaxPrice)))
		}
	}
	return nil
}

func initCDNDomainRegExps(regexps []string) []*regexp.Regexp {
	regExps := make([]*regexp.Regexp, 0)
	for _, endpoint := range regexps {
		if endpoint == "" {
			continue
		}
		regExps = append(regExps, regexp.MustCompile(endpoint))
	}
	return regExps
}

// ShopifyProductPriceSourceConfig 定义 shopify_product_price_source.json 的结构
// 目前只支持 meta_field 字段
type ShopifyProductPriceSourceConfig struct {
	MetaField []ShopifyProductPriceSourceMetaField `json:"meta_filed" mapstructure:"meta_filed"`
}

type ShopifyProductPriceSourceMetaField struct {
	OrganizationID string `json:"organization_id" mapstructure:"organization_id"`
	AppKey         string `json:"app_key" mapstructure:"app_key"`
	Resource       string `json:"resource" mapstructure:"resource"`
	Key            string `json:"key" mapstructure:"key"`
}

// IsHit 查询 shopify_product_price_source.json 的 meta_filed
// 命中返回 level, key, true，否则返回 "", "", false
func (c *ShopifyProductPriceSourceConfig) IsHit(organizationID, appKey string) (string, string, bool) {
	if c == nil {
		return "", "", false
	}
	for _, item := range c.MetaField {
		if item.OrganizationID == organizationID && item.AppKey == appKey {
			return item.Resource, item.Key, true
		}
	}
	return "", "", false
}
